<?php
/**
 * Recurring cart tax itemized totals
 *
 * <AUTHOR>
 * @package WooCommerce Subscriptions/Templates
 * @version 1.0.0 - Migrated from WooCommerce Subscriptions v3.1.0
 */

defined( 'ABSPATH' ) || exit;
$display_heading = true;

foreach ( WC()->cart->get_taxes() as $tax_id => $tax_total ) {
	foreach ( $recurring_carts as $recurring_cart_key => $recurring_cart ) {
		foreach ( $recurring_cart->get_tax_totals() as $recurring_code => $recurring_tax ) {
			if ( ! isset( $recurring_tax->tax_rate_id ) || $recurring_tax->tax_rate_id !== $tax_id ) {
				continue;
			}

			/**
			 * Allow third-parties to filter the tax displayed.
			 *
			 * @since 1.0.0 - Migrated from WooCommerce Subscriptions v3.1.0
			 * @param string The recurring cart's tax total price string for this tax code.
			 * @param WC_Cart $recurring_cart The recurring cart.
			 * @param string  $recurring_code The tax code.
			 * @param object  $recurring_tax  The recurring tax data generated by @see WC_Cart::get_tax_totals()
			 */
			$tax_amount = wp_kses_post( apply_filters( 'wcs_recurring_cart_itemized_tax_totals_html', wcs_cart_price_string( $recurring_tax->formatted_amount, $recurring_cart ), $recurring_cart, $recurring_code, $recurring_tax ) );

			// If the returned amount is empty, skip it.
			if ( empty( $tax_amount ) ) {
				continue;
			} ?>

			<tr class="tax-rate tax-rate-<?php echo esc_attr( sanitize_title( $recurring_code ) ); ?> recurring-total">

			<?php if ( $display_heading ) { ?>
				<?php $display_heading = false; ?>
				<th><?php echo esc_html( $recurring_tax->label ); ?></th>
				<td data-title="<?php echo esc_attr( $recurring_tax->label ); ?>"><?php echo $tax_amount; // XSS ok ?></td>
			<?php } else { ?>
				<th></th>
				<td><?php echo $tax_amount; // XSS ok ?></td>
			<?php }
		}
	}
	$display_heading = true;
}
