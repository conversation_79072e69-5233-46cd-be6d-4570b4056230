*** WooCommerce Subscriptions Core Changelog ***

= 6.7.1 - 2024-01-17 =
* Fix - Resolved an error that would occur with WC 8.5.0 when editing a subscription customer from the admin dashboard.

= 6.7.0 - 2024-01-11 =
* Update - Changed the edit subscription product "Expire after" (Subscription length) so it more clearly describes when a subscription will automatically stop renewing.
* Update - Log all exceptions caught by WooCommerce while processing a subscription scheduled action.
* Fix - Resolved an issue that caused ordering the Admin Subscriptions List Table to not work when HPOS is enabled.

= 6.6.0 - 2023-12-20 =
* Fix - When using the checkout block to pay for renewal orders, ensure the order's cart hash is updated to make sure the existing order can be used.
* Fix - Prevents a PHP fatal error that occurs when the cart contains a renewal order item that no longer exists.
* Fix - When HPOS is enabled and data compatibility mode is turned on, make sure subscription date changes made to postmeta are synced to orders_meta table.
* Fix - Resolved an issue that would cause undefined $current_page, $max_num_pages, and $paginate variable errors when viewing a page with the subscriptions-shortcode.

= 6.5.0 - 2023-11-09 =
* Add - When a customer toggles automatic renewals on or off via their My Account page, add a note to the subscription to record that event.
* Fix - When a subscription is flagged as requiring manual payments, allow admin users to turn on automatic payments for a subscription via the Edit Subscription page by selecting a new payment method.
* Fix - When processing an early renewal order, make sure the suspension count is reset back to 0 on payment complete.
* Fix - Ensure proper backfilling of subscription metadata (i.e. dates and cache) to the postmeta table when HPOS is enabled and compatibility mode (data syncing) is turned on.
* Fix - Fetch and update the `_cancelled_email_sent` meta in a HPOS compatibile way.
* Dev - Introduce a new wcs_get_subscription_grouping_key() function to generate a unique key for a subscription based on its billing schedule. This function uses the existing recurring cart key concept.
* Dev - Deprecate the WC_Subscriptions_Synchroniser::add_to_recurring_cart_key(). Use WC_Subscriptions_Synchroniser::add_to_recurring_product_grouping_key() instead.

= 6.4.0 - 2023-10-18 =
* Add - Use admin theme color and the correct WooCommerce colors.
* Fix - Resolve an issue that would cause 3rd party plugin edit product fields with the show_if_variable-subscription class to be incorrectly hidden.
* Fix - Allow gateways to execute action on payment method deletion before updating the subscription.
* Fix - Ensure subscriptions have a date created that correctly accounts for the site's timezone. Fixes issues with subscriptions having a date created double the site's UTC offset.
* Fix - When HPOS is enabled, fix quick-editing the subscription statuses on the admin list table.
* Dev - Updated the hooks for Checkout Blocks, replacing the deprecated `woocommerce_blocks_checkout_` prefixed hooks with `woocommerce_store_api_checkout`.
* Dev - PHP 8.2: Fix "Creation of dynamic property" warnings.

= 6.3.0 - 2023-10-06 =
* Add - Introduce the "Subscription Relationship" column under the Orders list admin page when HPOS is enabled.
* Fix - Resolved an issue that caused subscriptions with an unpaid early renewal order to be incorrectly considered as needing payment.
* Fix - When HPOS is enabled, make the orders_by_type_query filter box work in the WooCommerce orders screen.
* Fix - Ensure renewal orders paid via the Block Checkout are correctly linked to their subscription.
* Fix - Resolved an issue that caused paying for failed/pending parent orders that include Product Add-ons to not calculate the correct total.
* Fix - Ensure the order needs processing transient is deleted when a subscription order (eg renewal) is created. Fixes issues with renewal orders going straight to a completed status.
* Fix - Store the correct subscription start date in postmeta and ordermeta when HPOS and data syncing is being used.
* Fix - When HPOS is enabled, deleting a customer will now delete their subscriptions.
* Fix - Missing styles on the Edit Subscription page when HPOS is enabled.

= 6.2.0 - 2023-08-10 =
* Add - Introduce an updated empty state screen for the WooCommerce > Subscriptions list table.
* Fix - Ensure subscription checkout and cart block integrations are loaded on store environments where WooPayments is not enabled.
* Fix - Ensure the shipping phone number field is copied to subscriptions and their orders when copying address meta.
* Update - When HPOS is disabled, fetch subscriptions by customer_id using the user's subscription cache to improve performance.
* Dev - Deprecated the 'woocommerce_subscriptions_not_found_label' filter.

= 6.1.0 - 2023-07-26 =
* Fix - Resolved an issue that prevented the "Used for variations" checkbox to not be enabled on the edit product page load causing variations to be deleted erroneously.
* Dev - Fixed wcs_get_subscription_orders() returning an empty list when querying parent orders when HPOS is enabled with data syncing off.

= 6.0.0 - 2023-07-18 =
* Fix - Resolve an issue that prevented the "Used for variations" checkbox from being enabled on the variable subscription product edit screen on WC version v7.9.0.

= 5.9.0 - 2023-07-14 =
* Fix - Ensure when a customer changes the shipping method on cart and checkout that the recurring totals correctly reflect the chosen method.

= 5.8.0 - 2023-07-05 =
* Fix - When HPOS is enabled, permanently deleting a subscription related order wasn't updating the related orders cache properly.
* Fix - Added logic to check if the recurring cart array is present before displaying the recurring totals section in the cart.

= 5.7.2 - 2023-06-05 =
* Fix - Resolved an issue with customers being redirected to an incorrect Pay for Order URL after login.

= 5.7.1 - 2023-05-11 =
* Dev - Resolve errors for third-party code using the URLs returned from WC_Subscriptions_Admin::add_subscription_url() and WCS_Cart_Renewal::get_checkout_payment_url() because they were erroneously escaped. #440
* Dev - Enable third-party code to alter the delete payment token URL returned from flag_subscription_payment_token_deletions. #440

= 5.7.0 - 2023-05-04 =
* Fix - Fatal error from third-party extensions using the `woocommerce_update_order` expecting the second parameter.
* Dev - Pass the subscription object as the second parameter to `woocommerce_update_subscription` hook (and `woocommerce_update_order` for backwards compatibility).
* Dev - Return a response from the WC_Subscription::set_status() function in line with the parent WC_Order::set_status() function.
* Dev - Add the 'wcs_recurring_shipping_package_rates_match_standard_rates' filter to enable third-parties to override whether the subscription packages match during checkout validation.

= 5.6.0 - 2023-04-19 =
* Fix - Correctly determine subscription free shipping eligibility when the initial payment cart isn't eligible. Fixes erroneous "Invalid recurring shipping method" errors on checkout. #409
* Dev - Fixed precision loss notice that occurs when running PHP 8.1. #428
* Dev - Fix phpcs and semgrep warnings to improve code quality. #429
* Dev - Use `wp_safe_redirect()` when processing a payment method change request. #429

= 5.5.0 - 2023-03-10 =
* Fix - When HPOS is enabled, changing your address while paying for a renewal order will update the address on the subscription. #413
* Fix - Prevent admin error notices being shown for the "subscription trial end" event that was caused by no callbacks being attached to this scheduled action. #414
* Fix - Prevent fatal error when copying the `_shipping_address` meta data where the value is not an array. #417

= 5.4.0 - 2023-02-21 =
* Fix - Remove the recurring shipping method cache that caused bugs for third-party plugins like Conditional Shipping and Payments. #407

= 5.3.1 - 2023-02-01 =
* Fix - Fatal error when loading the Edit Subscription page with custom admin billing or shipping fields. #403

= 5.3.0 - 2023-01-26 =
* Add - Highlight subscriptions with overdue payment in list view with red icon & tooltip.
* Fix - Shipping address correctly set when resubscribing or switching subscriptions that contain different billing and shipping addresses.
* Fix - When processing customer requests to update all their subscription payment methods, ensure the updated subscription is used fetch the new payment meta, not and old instance.
* Dev - Remove deprecated `strptime` function in favour of `DateTime::createFromFormat`.
* Fix - Catch exceptions when changing payment method associated with a subscription to avoid fatal errors.

= 5.2.0 - 2023-01-23 =
* Add - New wcs_set_order_address() helper function to set an array of address fields on an order or subscription.
* Fix - Edit, add, and list Subscription admin pages now work when HPOS is enabled.
* Fix - Fixed issues where multiple subscription purchases wouldn't appear on the My Account > Subscriptions screen, on HPOS environments.
* Fix - Refactor `WCS_Meta_Box_Subscription_Data::save` to support HPOS stores, fixing a PHP warning notice when updating an order via the Edit Order screen.
* Fix - Set the `download_permissions_granted` value when purchasing a downloadable subscription product when HPOS is enabled.
* Fix - When a customer changes their address on their account or subscription, make sure the new address is saved when HPOS is enabled.
* Fix - Removed the potential for an infinite loop when getting a subscription's related orders while the subscription is being loaded.
* Fix - Refactor `WC_Subscriptions_Renewal_Order::get_failed_order_replaced_by()` to support HPOS stores.
* Fix - Refactor the `WC_Subscriptions_Tracker` class to support HPOS stores.
* Fix - "Subscriptions by Payment Gateway" in WooCommerce → Status now shows the correct values when HPOS is enabled.
* Fix - Check whether the order actually exists before accessing order properties in wcs_order_contains_subscription()
* Fix - Replace the get_posts() used in get_subscriptions_from_token() to support HPOS stores.
* Fix - On HPOS stores, when a subscription's parent order is trashed or deleted, make sure the related subscription is also trashed or deleted.
* Fix - On HPOS stores, when a subscription is trashed or deleted, make sure it is cancelled first.
* Fix - Merge any custom meta_query args passed to wcs_get_orders_with_meta_query() to avoid overriding WC core args that map onto meta_query.
* Fix - Filtering the Subscriptions List Table by customer, product ID and payment method, now works on stores with HPOS enabled.
* Fix - Prevent erroneously resyncing a subscription every time it is loaded from the database on HPOS environments.
* Fix - Fix "Trying to get property 'ID' of non-object" errors on the edit subscription screen when HPOS is enabled.
* Fix - When HPOS is enabled, clicking the related orders link on the Subscriptions Table now filters the table with the related orders (previously all orders were shown).
* Fix - On HPOS environments, ensure subscription related order caches are updated when relationship order meta (eg `_subscription_renewal` or `_subscription_switch`) is updated.
* Fix - Show subscription status filters/views above the subscriptions list table on stores with HPOS enabled.
* Fix - Reorder the edit subscription meta boxes on HPOS environments so the line items meta box appears after the subscription data.
* Fix - On HPOS environments, prepopulate the subscription start date when creating a new subscription via the admin edit screen.
* Fix - On HPOS environments, handle the admin subscriptions list table bulk actions and row actions in a HPOS compatible way.
* Fix - On HPOS environments, admin notices might show incorrect subscription or order link as part of the admin notice.
* Fix - On HPOS environments, parent order link on subscription edit page, in subscription details box, is incorrect.
* Fix - On HPOS environments, update cache when subscription is trashed, deleted, or restored / untrashed.
* Fix - Replace code using wp_count_posts( 'shop_subscription' ) with data store function to count subscriptions grouped by statuses on stores with HPOS enabled.
* Dev - Replace code using get_post_type( $subscription_id ) with WC Data Store get_order_type().
* Dev - Add subscriptions-core library version to the WooCommerce system status report.
* Dev - Introduced a WCS_Object_Data_Cache_Manager and WCS_Object_Data_Cache_Manager_Many_To_One class as HPOS equivalents of the WCS_Post_Meta_Cache_Manager classes.
* Dev - Introduced a new `untrash_order()` in the `WCS_Orders_Table_Subscription_Data_Store` class to fix untrashing subscriptions on stores that have HPOS enabled.
* Dev - Moved the trash, untrash & delete related `add_actions()` in the `WC_Subscriptions_Manager` class to be added on the `woocommerce_loaded` action.
* Dev - Update `wp_delete_post()` code that was used to delete a subscription to use CRUD methods to support HPOS.
* Dev - Fix phpcs violations in the `WC_Subscriptions_Tracker` class to improve code quality.
* Dev - Fix phpcs violations in the `WCS_Admin_System_Status` class to improve code quality.

= 5.1.0 - 2022-11-24 =
* Fix - Set payment tokens when copying data between orders and subscriptions in a CRUD compatible way. Fixes PHP notices during renewal order process.
* Fix - Infinite loop that can occur with `WCS_Orders_Table_Subscription_Data_Store::read_multiple()` on HPOS-enabled stores.
* Fix - On HPOS stores, when querying for subscriptions with wcs_get_orders_with_meta_query() with status 'any', ensure that wc_get_orders() queries for subscription statuses.
* Fix - On HPOS stores, when saving a subscription make sure subscription properties (ie `_requires_manual_renewal`) are saved to the database.
* Fix - On HPOS stores, when a subscription is loaded from the database, make sure all core subscription properties are read directly from meta.
* Fix - When viewing My Account > Subscriptions, fix an issue where no subscriptions were listed when HPOS is enabled.
* Fix - On HPOS stores, ensure payment tokens are copied from the subscription to the renewal order.
* Fix - Refactor `WCS_Meta_Box_Schedule::save` to support HPOS stores, fixing a PHP warning notice when updating an order via the Edit Order screen.
* Fix - Return a fresh instance of the renewal order after creating it. Fixes caching issues on HPOS sites where the returned order has no line items.
* Fix - Processing a manual renewal order with HPOS and data syncing enabled correctly saves the related order cache metadata on the subscription and prevents the post and order meta data getting out of sync.
* Fix - Use supported CRUD apis to determine if subscriptions are present on store (`wcs_do_subscriptions_exist`)
* Fix - With HPOS and data syncing enabled, updating the status of a pending manual renewal order to a paid status correctly activates the related subscription.
* Update - Refactor the `wcs_is_subscription` helper function to support HPOS.
* Update - Refactor our Related Orders data store classes (WCS_Related_Order_Store_Cached_CPT and WCS_Related_Order_Store_CPT) to use CRUD methods to support subscriptions and orders stored in HPOS.
* Update - Display related orders table when viewing the new "Edit Order" page (HPOS enabled stores).
* Fix - On HPOS stores, make sure the links in the related-orders table redirect to the new Edit Order URL.
* Dev - Removed the deprecated "wcs_subscriptions_for_{$relation_type}_order" dynamic hook used to filter the list of related subscriptions for the given relation type. The following hooks have been removed with no alternative:
        wcs_subscriptions_for_renewal_order
        wcs_subscriptions_for_switch_order
        wcs_subscriptions_for_resubscribe_order
* Dev - Introduce a WC_Subscription::set_status() function to handle subscriptions set with a draft or auto-draft status. Replaces the need for the overriding WC_Subscription::get_status() which has been deleted.
* Dev - Manual renewal orders created with HPOS and data syncing enabled are properly linked to the subscription by its `_subscription_renewal` meta and backfilled to posts table.

= 5.0.0 - 2022-11-14 =
* Dev - The library has been bumped to version to 5.0.0 to reduce confusion with the version of WooCommerce Subscriptions.
* Dev - Usage of \WC_Subscriptions_Core_Plugin::get_plugin_version() is no longer recommended for version detection. \WC_Subscriptions_Core_Plugin::get_library_version() should be used instead.
* Add - New wcs_get_orders_with_meta_query() helper function to query for orders and subscriptions.
* Update - Replace instances of `get_posts()` across codebase with new wcs_get_orders_with_meta_query() function.
* Dev - Code that was tagged with a version and moved from WooCommerce Subscriptions now explicitly mentions this and shows the correct subscriptions-core and WC Subscriptions versions.
* Dev - Refactor the saving of subscription dates in the subscription datastore to separate fetching changes and saving. Enables backfilling subscription dates when HPOS syncing is enabled.

= 2.5.2 - 2022-11-15 =
* Fix - When creating a subscription via the checkout, make sure a new instance of the subscription is attached to the `woocommerce_checkout_subscription_created` action hook.

= 2.5.1 - 2022-11-04 =
* Dev - Replace the use of the deprecated wcs_renewal_order_meta hook with wc_subscription_renewal_order_data in the WCS_Related_Order_Store_Cached_CPT class.
* Dev - Fix typo in deprecation notice for the 'wcs_{type}_meta_query' filter. Incorrect replacement hook.

= 2.5.0 - 2022-11-04 =
* Add - New WCS_Orders_Table_Subscription_Data_Store class to support subscriptions stored in High-Performance Order Storage (HPOS).
* Add - New WCS_Orders_Table_Data_Store_Controller class to load the proper subscriptions data store when the store has HPOS enabled.
* Add - New data copier class to copy data to subscriptions and related orders in place of direct database queries in prepraration for HPOS support.
* Fix - When saving sync meta data on a new subscription, use 'woocommerce_new_subscription' instead of 'save_post'. This is to prevent errors when purchasing a subscription on stores that have HPOS enabled.
* Update - Improve maybe_add_subscription_meta() and subscription_contains_synced_product() inside our WC_Subscriptions_Synchroniser class to use CRUD methods.
* Dev - wcs_get_objects_property and wcs_set_objects_property have been marked as deprecated. Getters/Setters should be used on the objects instead.
* Dev - Deprecated the "wcs_{type}_meta_query" dynamic hook used to alter the database query used to fetch the meta data to copy between subscriptions and renewal orders. There is no direct replacement. Third-parties should use the "wc_subscriptions_{type}_data" or "wc_subscriptions_object_data" hooks instead.
* Dev - Deprecated the "wcs_{type}_meta" dynamic hook used to filter data copied to subscriptions and renewal orders. Third-parties should use wc_subscriptions_{type}_data instead.
        wcs_subscription_meta      -> wc_subscriptions_subscription_data
        wcs_parent_meta            -> wc_subscriptions_parent_data
        wcs_resubscribe_order_meta -> wc_subscriptions_resubscribe_order_data
        wcs_renewal_order_meta     -> wc_subscriptions_renewal_order_data

= 2.4.1 - 2022-11-02 =
* Fix - Undefined method WC_Order::set_shipping_address() on stores running pre-7.1 of WooCommerce which prevented subscriptions from being purchased.

= 2.4.0 - 2022-10-28 =
* Update - The subscription creation function `wcs_create_subscription` has been updated to use WooCommerce CRUD methods in preparation for supporting High Performance Order Storage (HPOS).
* Update - Improve wcs_copy_order_address() to use modern APIs for setting address fields.
* Dev - woocommerce_new_subscription_data hook will only work with CPT datastore and so has been deprecated.
* Dev - i18n usage of strftime has been deprecated for subscription titles. Date is now formatted using woocommerce standard date formatting.

= 2.3.0 - 2022-10-07 =
* Fix - Move One Time Shipping metabox fields to use the woocommerce_product_options_shipping_product_data hook introduced in WC 6.0.
* Dev - Define build tool version requirements for consistent development and build environments.
* Update - Improve handling of bulk action execution.

= 2.2.1 - 2022-08-25 =
* Fix - Fatal Error caused in rare cases where quantity is zero during renewal.

= 2.2.0 - 2022-08-03 =
* Fix - Update subscription address when changed with renewals on block checkout.

= 2.1.0 - 2022-06-06 =
* Fix - Fatal Error caused in rare cases where quantity is zero during renewal.

= 2.0.0 - 2022-05-20 =
* Dev - Retrieving users subscriptions order has been updated to use the WooCommerce specific APIs in WC_Subscriptions_Order.
* Dev - Deprecate the WC_Subscriptions_Order::get_meta() function. Use wcs_get_objects_property( $order, $meta_key, "single", $default ) instead.
* Dev - Update the wcs_get_objects_property() function to prevent calls to get_post_meta() on objects that support calling the get_meta() function.
* Dev - Remove the get_post_meta() call from WCS_Post_Meta_Cache_Manager::maybe_update_for_post_change().
* Dev - Replace code using get_post_type( $order_id ) with WC Data Store get_order_type().
* Dev - Replace all cases of update_post_meta() where an Order ID is passed to use WC_Order::update_meta_data() instead.

= 1.9.0 - 2022-04-27 =
* Fix: Display subscription billing details in the Cart Block when purchasing products with subscription plans created using the All Products extension. PR#149
* Dev: Update phpunit to v9 to allow testing against newer php versions. PR#140

= 1.8.0 - 2022-04-04 =
* Update: Switch to global functions to remove deprecation warnings originating from WooCommerce Blocks. PR#124

= 1.7.0 - 2022-03-18 =
* Fix: Sets up subscriptions integration with the Mini Cart Block and adds new hook to filter compatible blocks. PR#103
* Fix: When using a WooCommerce Blocks powered checkout, fix an issue that led to limited products being removed from the cart when completing a switch or renewal order. PR#119 wcs#4232
* Fix: When there is only one Shipping Method available in the recurring shipping package, make sure that this method is treated as selected in the current session and the `woocommerce_after_shipping_rate` action runs. PR#115
* Fix: Don't anonymize new subscriptions related to old subscriptions via a resubscribe relationship. PR#121 wcs#4304 wcpay#3889
* Fix: Content that appears on the My account > Payment methods page should be translatable. PR#125 wcs#4180 wcpay#3974

= 1.6.4 - 2022-02-10 =
* Fix: When changing the payment method, make sure the subscription total returns $0 when `subscriptions-core` is loaded after the `woocommerce_loaded` action hook. PR#111 wcpay#3768

= 1.6.3 - 2022-02-07 =
* Fix: Replace uses of is_ajax() with wp_doing_ajax(). PR#108 wcpay#3695 wcs#4296
* Improve handling of session data.

= 1.6.2 - 2022-01-19 =
* Fix: Prevent fatal error when too few arguments passed to widget_title filter. PR#100

= 1.6.1 - 2022-01-18 =
* Dev: Update the list of "export-ignore" in `.gitattributes` to include recent developer files. PR#97
* Dev: Set the composer package type to "wordpress-plugin". PR#96

= 1.6.0 - 2022-01-17 =
* Fix: When viewing a WCPay Subscription product page, make sure other gateway's express payment buttons aren't shown. PR#87 wcpay#3401
* Fix: When viewing a WC Product page with a WCPay subscription product in cart, make sure other gateway's express payment buttons are shown. PR#87 wcpay#3401

= 1.5.0 - 2022-01-14 =
* New: Introduce filter to allow third-parties to specify the minimum recurring amount the payment method can support. Displays a warning to the merchant when creating products below that amount. #PR89 wcpay#3542

= 1.4.0 - 2022-01-03 =
* Fix: Simple subscription elements on the product edit page not shown/hidden when necessary. PR#80
* Fix: Prevent fatal errors on the admin subscriptions screen when a subscription fails to load. PR#84 wcpay#3596 wcs#4286
* Fix: Compatibility issue when loading subscriptions templates. PR#86 wcpay#3606 wcs#4291

= 1.3.0 - 2021-12-21 =
* Fix: Remove references to the Subscription extension in the tooltips found on the Payment Methods settings table. PR#55 wcpay#3234
* Fix: Update the Automatic Recurring Payments column on the Payment Methods table to only show which payment methods are supported by Subscriptions Core. PR#55
* Tweak: Update deprecation message when calling WC_Subscriptions_Coupon::cart_contains_limited_recurring_coupon() to mention the correct replacement function. PR#53
* Fix: Prevent deprecation warnings when using WooCommerce Blocks. PR#54
* Tweak: Update recurring payments copy on payment gateways page.
* Fix: Incorrect text when filtering subscriptions to no results.
* Changed: Subscription products must have a recurring amount greater than $0. PR#56 wcpay#3309

= 1.2.0 - 2021-11-23 =
* Fix: Update tooltip wording when deleting product variation. PR#46
* Fix: Don't show an admin error notice when a store downgrades to a previous minor version of Subscriptions. WCS#4271

= 1.1.0 - 2021-11-12 =
* Fix: Add consistent margins to the recurring taxes totals row on the Checkout and Cart block. PR#39
* Fix: Fatal error due to order with no created date in order row template. PR#40
* Fix: Fatal error on the customer payment page for renewal orders with deleted products. PR#42
* Fix: Misleading order note on payment method change. PR#41

= 1.0.3 - 2021-10-29 =
* Fix: Errors when attempting to get the plugin version during PayPal requests. PR#27

= 1.0.2 - 2021-10-29 =
* Fix: PHP 7.2 compatibility - remove trailing commas from function args. PR #23

= 1.0.1 - 2021-10-22 =
* Fix: Don't show a downgrade notice when activating the WC Subscriptions extension after installing WCS Core. PR#7
* Fix: Correctly show the available payment methods when paying for a subscription renewal order. PR#9
* Fix: Don't show the WC Subscriptions extension welcome/installation message after installing WCS Core. PR#11
* Fix: Remove the "Welcome to Subscriptions" notice that is displayed upon upgrading from previous minor versions. PR#14
* Fix: Don't display a "Welcome to Subscriptions 2.1" for stores that have upgraded from really old version of Subscriptions. PR#16
* Fix: Errors during the upgrade process for stores that are upgrading from very old versions of Subscriptions (1.5.0). PR#16
* Fix: Show subscription billing information (recurring cart totals, sign up fees etc) on the WooCommerce Checkout block. PR#18

= 1.0.0 - 2021-09-22 =
* New: Subscriptions Core first release
