{"version": 3, "file": "js/checkout-paylater-block.js", "mappings": ";mBACA,IAAIA,EAAsB,CCA1BA,EAAyBC,IACxB,IAAIC,EAASD,GAAUA,EAAOE,WAC7B,IAAOF,EAAiB,QACxB,IAAM,EAEP,OADAD,EAAoBI,EAAEF,EAAQ,CAAEG,EAAGH,IAC5BA,CAAM,ECLdF,EAAwB,CAACM,EAASC,KACjC,IAAI,IAAIC,KAAOD,EACXP,EAAoBS,EAAEF,EAAYC,KAASR,EAAoBS,EAAEH,EAASE,IAC5EE,OAAOC,eAAeL,EAASE,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,IAE1E,ECNDR,EAAwB,CAACc,EAAKC,IAAUL,OAAOM,UAAUC,eAAeC,KAAKJ,EAAKC,ICAlF,MAAM,EAA+BI,OAAW,GAAU,OCApD,EAA+BA,OAAW,GAAQ,KCAlD,EAA+BA,OAAW,GAAW,QCArD,EAA+BA,OAAW,GAAe,YCAzD,EAA+BA,OAAW,GAAc,WCAxD,EAA+BA,OAAc,UCuB/CC,EAYAC,EAWAC,YAtBJ,SAAWF,GACTA,EAA8B,QAAI,UAClCA,EAA8B,QAAI,UAClCA,EAA+B,SAAI,WACnCA,EAA+B,SAAI,UACpC,CALD,CAKGA,IAAyBA,EAAuB,CAAC,IAOpD,SAAWC,GACTA,EAAgC,eAAI,mBACpCA,EAA+B,cAAI,eACnCA,EAAwC,uBAAI,mBAC7C,CAJD,CAIGA,IAAoBA,EAAkB,CAAC,IAO1C,SAAWC,GACTA,EAAmC,OAAI,SACvCA,EAAgC,IAAI,MACpCA,EAA4C,gBAAI,iBAChDA,EAA6C,iBAAI,kBACjDA,EAA4C,gBAAI,iBAChDA,EAAwC,YAAI,YAC7C,CAPD,CAOGA,IAA+BA,EAA6B,CAAC,IAChE,IAAIC,EAAW,WAQb,OAPAA,EAAWb,OAAOc,QAAU,SAAkBC,GAC5C,IAAK,IAAIC,EAAGC,EAAI,EAAGC,EAAIC,UAAUC,OAAQH,EAAIC,EAAGD,IAE9C,IAAK,IAAII,KADTL,EAAIG,UAAUF,GACOjB,OAAOM,UAAUC,eAAeC,KAAKQ,EAAGK,KAAIN,EAAEM,GAAKL,EAAEK,IAE5E,OAAON,CACT,EACOF,EAASS,MAAMC,KAAMJ,UAC9B,EACA,SAASK,EAAOR,EAAGS,GACjB,IAAIV,EAAI,CAAC,EACT,IAAK,IAAIM,KAAKL,EAAOhB,OAAOM,UAAUC,eAAeC,KAAKQ,EAAGK,IAAMI,EAAEC,QAAQL,GAAK,IAAGN,EAAEM,GAAKL,EAAEK,IAC9F,GAAS,MAALL,GAAqD,mBAAjChB,OAAO2B,sBAA2C,KAAIV,EAAI,EAAb,IAAgBI,EAAIrB,OAAO2B,sBAAsBX,GAAIC,EAAII,EAAED,OAAQH,IAClIQ,EAAEC,QAAQL,EAAEJ,IAAM,GAAKjB,OAAOM,UAAUsB,qBAAqBpB,KAAKQ,EAAGK,EAAEJ,MAAKF,EAAEM,EAAEJ,IAAMD,EAAEK,EAAEJ,IADuB,CAGvH,OAAOF,CACT,CACA,SAASc,EAAcC,EAAIC,EAAMC,GAC/B,GAAIA,GAA6B,IAArBb,UAAUC,OAAc,IAAK,IAA4Ba,EAAxBhB,EAAI,EAAGiB,EAAIH,EAAKX,OAAYH,EAAIiB,EAAGjB,KAC1EgB,GAAQhB,KAAKc,IACVE,IAAIA,EAAKE,MAAM7B,UAAU8B,MAAM5B,KAAKuB,EAAM,EAAGd,IAClDgB,EAAGhB,GAAKc,EAAKd,IAGjB,OAAOa,EAAGO,OAAOJ,GAAME,MAAM7B,UAAU8B,MAAM5B,KAAKuB,GACpD,CAC2B,mBAApBO,iBAAiCA,gBASxC,IAAIC,EAAY,8BACZC,EAGkB,kBAHlBA,EAIc,gBAJdA,EAK2B,2BAQ3BC,EAAmB,SAMnBC,GALmB,uCAAuCL,OAAOI,EAAkB,qBAChD,uCAAuCJ,OAAOI,EAAkB,8BAIxE,UAsB/B,SAASE,EAA2BC,GAKlC,YAJkB,IAAdA,IACFA,EAAYF,GAGPjC,OAAOmC,EAChB,CAiCA,SAASC,EAAqBC,GAC5B,IAAIC,EAAqBD,EAAGC,mBAC1BC,EAAkBF,EAAGE,gBACrBC,EAAKH,EAAGI,uBACRA,OAAgC,IAAPD,EAAgB,GAAKA,EAC9CE,EAAKL,EAAGM,iBACRA,OAA0B,IAAPD,EAAgBT,EAA2BS,EAC5DE,EAA4BL,EAAgBM,OAAO,GAAGC,cAAclB,OAAOW,EAAgBQ,UAAU,IACrGC,EAAe,qBAAqBpB,OAAOU,EAAoB,uBAAuBV,OAAOe,EAAkB,KAAKf,OAAOgB,EAA2B,kBAGtJK,EAAwD,iBAA3BR,EAAsCA,EAAyBA,EAAuBS,KAAK,KAC5H,IAAKD,EAAoBE,SAASZ,GAAkB,CAClD,IAAIa,EAAqB,CAACH,EAAqBV,GAAiBc,OAAOC,SAASJ,OAChFF,GAAgB,4BAA4BpB,OAAOW,EAAiB,0EAA4E,oDAAoDX,OAAOwB,EAAoB,SACjO,CACA,OAAOJ,CACT,CAOA,SAASO,EAAYC,GAEnB,IAAInB,EAAKmB,EACPhB,EAAKV,EACPO,EAAGG,GACH,IAAIiB,EAAsB1C,EAAOsB,EAAI,CAACG,EAAK,KAC3C,MAAO,mBAAmBZ,OAzC5B,SAAiB8B,GAEf,IADA,IAAIC,EAAO,GACFnD,EAAI,EAAGA,EAAIkD,EAAI/C,OAAQH,IAAK,CACnC,IAAIoD,EAAQF,EAAIlD,GAAGqD,WAAW,GAAKrD,EAC/BkD,EAAIlD,EAAI,KACVoD,GAASF,EAAIlD,EAAI,GAAGqD,WAAW,IAAMrD,EAAI,IAE3CmD,GAAQG,OAAOC,aAAa,GAAKC,KAAKC,IAAIL,GAAS,GACrD,CACA,OAAOD,CACT,CA+BmCO,CAAQC,KAAKC,UAAUX,IAC1D,CAmBA,SAASY,EAAcC,EAAOC,GAC5B,IAAIlC,EAAIG,EAdgBgC,EACpBC,EAcJ,OAAQF,EAAOG,MACb,KAAKxE,EAAgByE,eACnB,MAA4B,iBAAjBJ,EAAOK,MACTxE,EAASA,EAAS,CAAC,EAAGkE,GAAQ,CACnCO,cAAeN,EAAOK,MAAMN,MAC5BQ,0BAA2BP,EAAOK,MAAMG,UAGrC3E,EAASA,EAAS,CAAC,EAAGkE,GAAQ,CACnCO,cAAeN,EAAOK,QAE1B,KAAK1E,EAAgB8E,cAGnB,OA7BoBR,EA4BHF,EAAMd,QAAQ1B,IA1B/B2C,OADAA,EAAaQ,KAAKC,SAASC,cAAc,UAAUvD,OAAOE,EAAW,MAAOF,OAAO4C,EAAqB,aACzD,EAASC,EAAWW,aACrEX,EAAWW,WAAWC,YAAYZ,GA0BzBrE,EAASA,EAAS,CAAC,EAAGkE,GAAQ,CACnCO,cAAe5E,EAAqBqF,QACpC9B,QAASpD,EAASA,GAAUiC,EAAK,CAAC,EAAGA,EAAGN,GAA4CA,EAAiCM,GAAKkC,EAAOK,QAASpC,EAAK,CAAC,EAAGA,EAAGV,GAAa,GAAGF,OAAO2B,EAAYgB,EAAOK,QAASpC,MAE7M,KAAKtC,EAAgBqF,uBACnB,OAAOnF,EAASA,EAAS,CAAC,EAAGkE,GAAQ,CACnCkB,gCAAiCjB,EAAOK,QAE5C,QAEI,OAAON,EAGf,CAEA,IAAImB,GAAgB,IAAAC,eAAc,MAuClC,SAASC,IACP,IAAIC,EAhCN,SAAyBA,GACvB,GAAsG,mBAA1FA,aAAqD,EAASA,EAAcC,WAA8D,IAAlCD,EAAcC,SAASlF,OACzI,OAAOiF,EAET,MAAM,IAAIE,MAhJwB,oEAiJpC,CA2BsBC,EAAgB,IAAAC,YAAWP,IAO/C,MAAO,CANoBrF,EAASA,EAAS,CAAC,EAAGwF,GAAgB,CAC/DK,UAAWL,EAAcf,gBAAkB5E,EAAqBiG,QAChEC,UAAWP,EAAcf,gBAAkB5E,EAAqBqF,QAChEc,WAAYR,EAAcf,gBAAkB5E,EAAqBoG,SACjEC,WAAYV,EAAcf,gBAAkB5E,EAAqBsG,WAErCX,EAAcC,SAC9C,EAYgC,IAAAH,eAAc,CAAC,GAiB/C,IAAIc,EAAgB,SAAUnE,GAC5B,IAAIG,EACAE,EAAKL,EAAGoE,UACVA,OAAmB,IAAP/D,EAAgB,GAAKA,EACjCgE,EAAKrE,EAAGsE,SACRA,OAAkB,IAAPD,GAAwBA,EACnCE,EAAWvE,EAAGuE,SACdC,EAAKxE,EAAGyE,cACRA,OAAuB,IAAPD,EAAgB,GAAKA,EACrCE,EAAchG,EAAOsB,EAAI,CAAC,YAAa,WAAY,WAAY,kBAC7D2E,EAAkBL,EAAW,CAC/BM,QAAS,KACP,CAAC,EACDC,EAAa,GAAGtF,OAAO6E,EAAW,KAAK7E,OAAO+E,EAAW,0BAA4B,IAAIQ,OACzFC,GAAsB,IAAAC,QAAO,MAC7BC,GAAU,IAAAD,QAAO,MACjBE,EAAK5B,IAAyB,GAChCS,EAAamB,EAAGnB,WAChB5C,EAAU+D,EAAG/D,QACXgE,GAAK,IAAAC,UAAS,MAChBC,EAAcF,EAAG,GACjBG,EAAiBH,EAAG,GAClBI,GAAK,IAAAH,WAAS,GAChBI,EAAaD,EAAG,GAChBE,EAAgBF,EAAG,GAEnBG,GADO,IAAAN,UAAS,MACG,GACrB,SAASO,IACiB,OAApBV,EAAQW,SACVX,EAAQW,QAAQC,QAAQC,OAAM,WAE9B,GAEJ,CA6EA,OA5E+B,QAA1B3F,EAAK8E,EAAQW,eAA4B,IAAPzF,OAAgB,EAASA,EAAG4F,cACjEd,EAAQW,QAAQG,YAAY,CAC1BrD,QAASgC,EAAYhC,WAIzB,IAAAsD,YAAU,WAER,IAAmB,IAAfjC,EACF,OAAO4B,EAET,IAAIM,EAAwBpG,EAA2BsB,EAAQ+E,eAE/D,QAA8BC,IAA1BF,QAAyEE,IAAlCF,EAAsBG,QAS/D,OARAV,GAAc,WACZ,MAAM,IAAIjC,MAAM1D,EAAqB,CACnCE,mBAAoBkE,EAAckC,YAClCnG,gBAAiB,UACjBE,uBAAwBe,EAAQmF,WAChChG,iBAAkBa,EAAQzB,KAE9B,IACOiG,EAQT,IACEV,EAAQW,QAAUK,EAAsBG,QAAQrI,EAASA,EAAS,CAAC,EAAG2G,GAAc,CAClF6B,OARkB,SAAUC,EAAMC,GACpCnB,EAAemB,GACmB,mBAAvB/B,EAAY6B,QACrB7B,EAAY6B,OAAOC,EAAMC,EAE7B,IAKA,CAAE,MAAOC,GACP,OAAOhB,GAAc,WACnB,MAAM,IAAIjC,MAAM,wEAAwElE,OAAOmH,GACjG,GACF,CAEA,OAAqC,IAAjCzB,EAAQW,QAAQJ,cAClBC,GAAc,GACPE,GAEJZ,EAAoBa,SAGzBX,EAAQW,QAAQe,OAAO5B,EAAoBa,SAASE,OAAM,SAAUY,GAE9B,OAAhC3B,EAAoBa,SAAoE,IAAhDb,EAAoBa,QAAQrB,SAASjG,QAKjFoH,GAAc,WACZ,MAAM,IAAIjC,MAAM,iDAAiDlE,OAAOmH,GAC1E,GACF,IACOf,GAbEA,CAeX,GAAG5G,EAAcA,EAAc,CAACgF,GAAaU,GAAe,GAAO,CAACC,EAAYkC,gBAAgB,KAEhG,IAAAZ,YAAU,WACY,OAAhBX,KAGa,IAAbf,EACFe,EAAYwB,UAAUf,OAAM,WAE5B,IAEAT,EAAYyB,SAAShB,OAAM,WAE3B,IAEJ,GAAG,CAACxB,EAAUe,IACP,kBAAoB,aAAgB,KAAMG,EAAa,kBAAoB,MAAO,CACvFuB,IAAKhC,EACLiC,MAAOrC,EACPP,UAAWS,IACRN,EACP,EA8EA,SAAS0C,EAAoBC,EAAKC,QACb,IAAfA,IACFA,EAAa,CAAC,GAEhB,IAAIC,EAAYvE,SAASwE,cAAc,UAQvC,OAPAD,EAAUE,IAAMJ,EAChBhK,OAAOqK,KAAKJ,GAAYK,SAAQ,SAAUxK,GACxCoK,EAAUK,aAAazK,EAAKmK,EAAWnK,IAC3B,mBAARA,GACFoK,EAAUK,aAAa,QAASN,EAAW,kBAE/C,IACOC,CACT,CACA,SAASM,EAAWvG,EAASwG,GAK3B,QAJwB,IAApBA,IACFA,EAAkBC,SAEpBC,EAAkB1G,EAASwG,GACH,oBAAb9E,SAA0B,OAAO8E,EAAgBG,QAAQ,MACpE,IAAI9H,EArEN,SAAwBmB,GACtB,IACI4G,EAA6B,YADf5G,EAAQ6G,YACmB,wCAA0C,uCAChF7G,EAAQ6G,YACX7G,EAAQ4G,aACVA,EAAa5G,EAAQ4G,kBACd5G,EAAQ4G,YAEjB,IAiC2BE,EACvBC,EAlCAC,EAAyBhH,EACzBnB,EAAK9C,OAAOqK,KAAKY,GAAwBnH,QAAO,SAAUhE,GAC1D,YAA8C,IAAhCmL,EAAuBnL,IAAwD,OAAhCmL,EAAuBnL,IAAiD,KAAhCmL,EAAuBnL,EAC9H,IAAGoL,QAAO,SAAUC,EAAarL,GAC/B,IAwBAsL,EAxBI/F,EAAQ4F,EAAuBnL,GAAKuL,WAOxC,OAiBAD,EAAW,SAAUE,EAAOC,GAC9B,OAAQA,EAAe,IAAM,IAAMD,EAAME,aAC3C,EAxBgC,UAD5B1L,EAA2BA,EA0BpB2L,QAAQ,yBAA0BL,IAzBjC5H,UAAU,EAAG,IAAyB,gBAAR1D,EACpCqL,EAAYlB,WAAWnK,GAAOuF,EAE9B8F,EAAYO,YAAY5L,GAAOuF,EAE1B8F,CACT,GAAG,CACDO,YAAa,CAAC,EACdzB,WAAY,CAAC,IAEfyB,EAAc5I,EAAG4I,YACjBzB,EAAanH,EAAGmH,WAKlB,OAJIyB,EAAY,iBAA+D,IAA7CA,EAAY,eAAehK,QAAQ,OACnEuI,EAAW,oBAAsByB,EAAY,eAC7CA,EAAY,eAAiB,KAExB,CACL1B,IAAK,GAAG3H,OAAOwI,EAAY,KAAKxI,QAUP0I,EAVkCW,EAWzDV,EAAc,GAClBhL,OAAOqK,KAAKU,GAAQT,SAAQ,SAAUxK,GACT,IAAvBkL,EAAY5J,SAAc4J,GAAe,KAC7CA,GAAelL,EAAM,IAAMiL,EAAOjL,EACpC,IACOkL,IAfLf,WAAYA,EAEhB,CAmCW0B,CAAe1H,GACtB+F,EAAMlH,EAAGkH,IACTC,EAAanH,EAAGmH,WACdrH,EAAYqH,EAAW,mBAAqB,SAC5C2B,EAA0BC,EAAyBjJ,GAIvD,OAHKqH,EAAW,yBACdA,EAAW,uBAAyB,aAtGxC,SAAoBD,EAAKC,GACvB,IAAI6B,EAAgBnG,SAASC,cAAc,eAAgBvD,OAAO2H,EAAK,OACvE,GAAsB,OAAlB8B,EAAwB,OAAO,KACnC,IAAIC,EAAahC,EAAoBC,EAAKC,GACtC+B,EAAqBF,EAAcG,YAEvC,UADOD,EAAmBE,QAAQC,QAC9BnM,OAAOqK,KAAK2B,EAAmBE,SAAS9K,SAAWpB,OAAOqK,KAAK0B,EAAWG,SAAS9K,OACrF,OAAO,KAET,IAAIgL,GAAe,EAMnB,OALApM,OAAOqK,KAAK2B,EAAmBE,SAAS5B,SAAQ,SAAUxK,GACpDkM,EAAmBE,QAAQpM,KAASiM,EAAWG,QAAQpM,KACzDsM,GAAe,EAEnB,IACOA,EAAeN,EAAgB,IACxC,CAwFMO,CAAWrC,EAAKC,IAAe2B,EAC1BnB,EAAgBG,QAAQgB,GAanC,SAA0B3H,EAASwG,QACT,IAApBA,IACFA,EAAkBC,SAEpBC,EAAkB1G,EAASwG,GAC3B,IAAIT,EAAM/F,EAAQ+F,IAChBC,EAAahG,EAAQgG,WACvB,GAAmB,iBAARD,GAAmC,IAAfA,EAAI5I,OACjC,MAAM,IAAImF,MAAM,gBAElB,QAA0B,IAAf0D,GAAoD,iBAAfA,EAC9C,MAAM,IAAI1D,MAAM,wCAElB,OAAO,IAAIkE,GAAgB,SAAUG,EAAS0B,GAC5C,GAAwB,oBAAb3G,SAA0B,OAAOiF,KAnHhD,SAA6B9H,GAC3B,IAAIkH,EAAMlH,EAAGkH,IACXC,EAAanH,EAAGmH,WAChBsC,EAAYzJ,EAAGyJ,UACfC,EAAU1J,EAAG0J,QACXtC,EAAYH,EAAoBC,EAAKC,GACzCC,EAAUuC,QAAUD,EACpBtC,EAAUwC,OAASH,EACnB5G,SAASgH,KAAKC,aAAa1C,EAAWvE,SAASgH,KAAKE,kBACtD,CA2GIC,CAAoB,CAClB9C,IAAKA,EACLC,WAAYA,EACZsC,UAAW,WACT,OAAO3B,GACT,EACA4B,QAAS,WACP,IAAIO,EAAe,IAAIxG,MAAM,eAAgBlE,OAAO2H,EAAK,8FACzD,OAAOsC,EAAOS,EAChB,GAEJ,GACF,CAtCSC,CAAiB,CACtBhD,IAAKA,EACLC,WAAYA,GACXQ,GAAiBwC,MAAK,WACvB,IAAIC,EAAqBrB,EAAyBjJ,GAClD,GAAIsK,EACF,OAAOA,EAET,MAAM,IAAI3G,MAAM,cAAclE,OAAOO,EAAW,sCAClD,GACF,CA6BA,SAASiJ,EAAyBjJ,GAChC,OAAOnC,OAAOmC,EAChB,CACA,SAAS+H,EAAkB1G,EAASwG,GAClC,GAAuB,iBAAZxG,GAAoC,OAAZA,EACjC,MAAM,IAAIsC,MAAM,+BAElB,IAAIuE,EAAc7G,EAAQ6G,YAC1B,GAAIA,GAA+B,eAAhBA,GAAgD,YAAhBA,EACjD,MAAM,IAAIvE,MAAM,sEAElB,QAA+B,IAApBkE,GAA8D,mBAApBA,EACnD,MAAM,IAAIlE,MAAM,6CAEpB,CAjKAU,EAAckC,YAAc,gBAyK5B,IAwIIgE,EAAc,SAAUrK,GAC1B,IAAIG,EAAKH,EAAGoE,UACVA,OAAmB,IAAPjE,EAAgB,GAAKA,EACjCoE,EAAWvE,EAAGuE,SACd+F,EAAY5L,EAAOsB,EAAI,CAAC,YAAa,aACnCK,EAAKiD,IAAyB,GAChCS,EAAa1D,EAAG0D,WAChB5C,EAAUd,EAAGc,QACXoJ,GAAmB,IAAAvF,QAAO,MAC1BX,GAAK,IAAAe,WAAS,GAChBI,EAAanB,EAAG,GAChBoB,EAAgBpB,EAAG,GAEnBqB,GADO,IAAAN,UAAS,MACG,GA8CrB,OApBA,IAAAY,YAAU,WAER,IAAmB,IAAfjC,EAAJ,CAGA,IAAIkC,EAAwBpG,EAA2BsB,EAAQzB,IAE/D,QAA8ByG,IAA1BF,QAAuEE,IAAhCF,EAAsBuE,MAC/D,OAAO9E,GAAc,WACnB,MAAM,IAAIjC,MAAM1D,EAAqB,CACnCE,mBAAoBoK,EAAYhE,YAChCnG,gBAAiB,QACjBE,uBAAwBe,EAAQmF,WAChChG,iBAAkBa,EAAQzB,KAE9B,KArCmB,SAAU+K,GAC/B,IAAI7E,EAAU2E,EAAiB3E,QAE/B,IAAKA,IAAY6E,EAAKjF,aACpB,OAAOC,GAAc,GAGnBG,EAAQ8E,YACV9E,EAAQ5C,YAAY4C,EAAQ8E,YAE9BD,EAAK9D,OAAOf,GAASE,OAAM,SAAUY,GAEnB,OAAZd,GAAgD,IAA5BA,EAAQrB,SAASjG,QAKzCoH,GAAc,WACZ,MAAM,IAAIjC,MAAM,+CAA+ClE,OAAOmH,GACxE,GACF,GACF,CAkBEiE,CAAiB1E,EAAsBuE,MAAMzM,EAAS,CAAC,EAAGuM,IAb1D,CAeF,GAAG,CAACvG,EAAYuG,EAAU1D,gBACnB,kBAAoB,aAAgB,KAAMpB,EAAa,kBAAoB,MAAO,CACvFuB,IAAKwD,EACLnG,UAAWA,IACRG,EACP,EACA8F,EAAYhE,YAAc,cAM1B,IAAIuE,EAAiB,SAAU5K,GAC7B,IAAIG,EAAKH,EAAGoE,UACVA,OAAmB,IAAPjE,EAAgB,GAAKA,EACjCE,EAAKL,EAAGyE,cACRA,OAAuB,IAAPpE,EAAgB,GAAKA,EACrCwK,EAAenM,EAAOsB,EAAI,CAAC,YAAa,kBACtCqE,EAAKf,IAAyB,GAChCS,EAAaM,EAAGN,WAChB5C,EAAUkD,EAAGlD,QACX2J,GAAuB,IAAA9F,QAAO,MAC9B+F,GAAW,IAAA/F,QAAO,MAEpBU,GADO,IAAAN,UAAS,MACG,GAgCrB,OA/BA,IAAAY,YAAU,WAER,IAAmB,IAAfjC,EAAJ,CAGA,IAAIkC,EAAwBpG,EAA2BsB,EAAQzB,IAE/D,QAA8ByG,IAA1BF,QAA0EE,IAAnCF,EAAsB+E,SAC/D,OAAOtF,GAAc,WACnB,MAAM,IAAIjC,MAAM1D,EAAqB,CACnCE,mBAAoB2K,EAAevE,YACnCnG,gBAAiB,WACjBE,uBAAwBe,EAAQmF,WAChChG,iBAAkBa,EAAQzB,KAE9B,IAEFqL,EAASnF,QAAUK,EAAsB+E,SAASjN,EAAS,CAAC,EAAG8M,IAC/DE,EAASnF,QAAQe,OAAOmE,EAAqBlF,SAASE,OAAM,SAAUY,GAE/B,OAAjCoE,EAAqBlF,SAAqE,IAAjDkF,EAAqBlF,QAAQrB,SAASjG,QAKnFoH,GAAc,WACZ,MAAM,IAAIjC,MAAM,kDAAkDlE,OAAOmH,GAC3E,GACF,GAxBA,CA0BF,GAAG3H,EAAc,CAACgF,GAAaU,GAAe,IACvC,kBAAoB,MAAO,CAChCsC,IAAK+D,EACL1G,UAAWA,GAEf,EACAwG,EAAevE,YAAc,iBAQ7B,IAAI4E,EAAuB,SAAUjL,GACnC,IAAIG,EACAE,EAAKL,EAAGmB,QACVA,OAAiB,IAAPd,EAAgB,CACxB6K,SAAU,QACR7K,EACJkE,EAAWvE,EAAGuE,SACdF,EAAKrE,EAAGmL,aACRA,OAAsB,IAAP9G,GAAwBA,EACrCG,GAAK,IAAA4G,YAAWpJ,EAAe,CAC/Bb,QAASpD,EAASA,EAAS,CAAC,EAAGoD,IAAWhB,EAAK,CAAC,EAAGA,EAAmC,iBAAIT,EAAiCS,EAAGT,GAA4CA,EAAiCS,EAAGV,GAAa,GAAGF,OAAO2B,EAAYC,IAAWhB,IAC5PqC,cAAe2I,EAAevN,EAAqBiG,QAAUjG,EAAqBqF,UAEpFhB,EAAQuC,EAAG,GACXhB,EAAWgB,EAAG,GAmChB,OAlCA,IAAAwB,YAAU,WACR,IAAqB,IAAjBmF,GAA0BlJ,EAAMO,gBAAkB5E,EAAqBiG,QACzE,OAAOL,EAAS,CACdnB,KAAMxE,EAAgByE,eACtBC,MAAO3E,EAAqBqF,UAGhC,GAAIhB,EAAMO,gBAAkB5E,EAAqBqF,QAAjD,CAGA,IAAIoI,GAAe,EAoBnB,OAnBA3D,EAAWzF,EAAMd,SAASgJ,MAAK,WACzBkB,GACF7H,EAAS,CACPnB,KAAMxE,EAAgByE,eACtBC,MAAO3E,EAAqBoG,UAGlC,IAAG8B,OAAM,SAAUY,GACjB4E,QAAQC,MAAM,GAAGhM,OA5yBC,2CA4yByB,KAAKA,OAAOmH,IACnD2E,GACF7H,EAAS,CACPnB,KAAMxE,EAAgByE,eACtBC,MAAO,CACLN,MAAOrE,EAAqBsG,SAC5BxB,QAASjB,OAAOiF,KAIxB,IACO,WACL2E,GAAe,CACjB,CAvBA,CAwBF,GAAG,CAACpJ,EAAMd,QAASgK,EAAclJ,EAAMO,gBAChC,kBAAoBY,EAAcoI,SAAU,CACjDjJ,MAAOxE,EAASA,EAAS,CAAC,EAAGkE,GAAQ,CACnCuB,SAAUA,KAEXe,EACL,EAgPA,SAASkH,IAET,oPCrpCAC,EAAA,kBAAA/M,CAAA,MAAAV,EAAAU,EAAA,GAAAgN,EAAAzO,OAAAM,UAAAY,EAAAuN,EAAAlO,eAAAR,EAAAC,OAAAC,gBAAA,SAAAc,EAAAU,EAAAgN,GAAA1N,EAAAU,GAAAgN,EAAApJ,KAAA,EAAApE,EAAA,mBAAAyN,OAAAA,OAAA,GAAA/O,EAAAsB,EAAA0N,UAAA,aAAAC,EAAA3N,EAAA4N,eAAA,kBAAAC,EAAA7N,EAAA8N,aAAA,yBAAAC,EAAAjO,EAAAU,EAAAgN,GAAA,OAAAzO,OAAAC,eAAAc,EAAAU,EAAA,CAAA4D,MAAAoJ,EAAAvO,YAAA,EAAA+O,cAAA,EAAAC,UAAA,IAAAnO,EAAAU,EAAA,KAAAuN,EAAA,aAAAjO,GAAAiO,EAAA,SAAAjO,EAAAU,EAAAgN,GAAA,OAAA1N,EAAAU,GAAAgN,CAAA,WAAAU,EAAApO,EAAAU,EAAAgN,EAAAvN,GAAA,IAAAD,EAAAQ,GAAAA,EAAAnB,qBAAA8O,EAAA3N,EAAA2N,EAAAzP,EAAAK,OAAAqP,OAAApO,EAAAX,WAAAsO,EAAA,IAAAU,EAAApO,GAAA,WAAAnB,EAAAJ,EAAA,WAAA0F,MAAAkK,EAAAxO,EAAA0N,EAAAG,KAAAjP,CAAA,UAAA6P,EAAAzO,EAAAU,EAAAgN,GAAA,WAAAtJ,KAAA,SAAAsK,IAAA1O,EAAAP,KAAAiB,EAAAgN,GAAA,OAAA1N,GAAA,OAAAoE,KAAA,QAAAsK,IAAA1O,EAAA,EAAAU,EAAA0N,KAAAA,EAAA,IAAAO,EAAA,iBAAAxN,EAAA,iBAAAyN,EAAA,YAAA3O,EAAA,YAAA4O,EAAA,YAAAR,IAAA,UAAAS,IAAA,UAAAC,IAAA,KAAAzO,EAAA,GAAA2N,EAAA3N,EAAA1B,GAAA,8BAAAD,EAAAM,OAAA+P,eAAAC,EAAAtQ,GAAAA,EAAAA,EAAAuQ,EAAA,MAAAD,GAAAA,IAAAvB,GAAAvN,EAAAV,KAAAwP,EAAArQ,KAAA0B,EAAA2O,GAAA,IAAAE,EAAAJ,EAAAxP,UAAA8O,EAAA9O,UAAAN,OAAAqP,OAAAhO,GAAA,SAAA8O,EAAApP,GAAA,0BAAAuJ,SAAA,SAAA7I,GAAAuN,EAAAjO,EAAAU,GAAA,SAAAV,GAAA,YAAAqP,QAAA3O,EAAAV,EAAA,gBAAAsP,EAAAtP,EAAAU,GAAA,SAAA6O,EAAA7B,EAAA1O,EAAAkB,EAAAtB,GAAA,IAAAiP,EAAAY,EAAAzO,EAAA0N,GAAA1N,EAAAhB,GAAA,aAAA6O,EAAAzJ,KAAA,KAAA2J,EAAAF,EAAAa,IAAAC,EAAAZ,EAAAzJ,MAAA,OAAAqK,GAAA,UAAAa,EAAAb,IAAAxO,EAAAV,KAAAkP,EAAA,WAAAjO,EAAAmJ,QAAA8E,EAAAc,SAAAvD,MAAA,SAAAlM,GAAAuP,EAAA,OAAAvP,EAAAE,EAAAtB,EAAA,aAAAoB,GAAAuP,EAAA,QAAAvP,EAAAE,EAAAtB,EAAA,IAAA8B,EAAAmJ,QAAA8E,GAAAzC,MAAA,SAAAlM,GAAA+N,EAAAzJ,MAAAtE,EAAAE,EAAA6N,EAAA,aAAA/N,GAAA,OAAAuP,EAAA,QAAAvP,EAAAE,EAAAtB,EAAA,IAAAA,EAAAiP,EAAAa,IAAA,KAAAhB,EAAA1O,EAAA,gBAAAsF,MAAA,SAAAtE,EAAAG,GAAA,SAAAuP,IAAA,WAAAhP,GAAA,SAAAA,EAAAgN,GAAA6B,EAAAvP,EAAAG,EAAAO,EAAAgN,EAAA,WAAAA,EAAAA,EAAAA,EAAAxB,KAAAwD,EAAAA,GAAAA,GAAA,aAAAlB,EAAA9N,EAAAgN,EAAAvN,GAAA,IAAAnB,EAAA2P,EAAA,gBAAAzO,EAAAtB,GAAA,GAAAI,IAAA4P,EAAA,MAAApJ,MAAA,mCAAAxG,IAAAiB,EAAA,cAAAC,EAAA,MAAAtB,EAAA,OAAA0F,MAAAtE,EAAA2P,MAAA,OAAAxP,EAAAyP,OAAA1P,EAAAC,EAAAuO,IAAA9P,IAAA,KAAAiP,EAAA1N,EAAA0P,SAAA,GAAAhC,EAAA,KAAAE,EAAA+B,EAAAjC,EAAA1N,GAAA,GAAA4N,EAAA,IAAAA,IAAAc,EAAA,gBAAAd,CAAA,cAAA5N,EAAAyP,OAAAzP,EAAA4P,KAAA5P,EAAA6P,MAAA7P,EAAAuO,SAAA,aAAAvO,EAAAyP,OAAA,IAAA5Q,IAAA2P,EAAA,MAAA3P,EAAAiB,EAAAE,EAAAuO,IAAAvO,EAAA8P,kBAAA9P,EAAAuO,IAAA,gBAAAvO,EAAAyP,QAAAzP,EAAA+P,OAAA,SAAA/P,EAAAuO,KAAA1P,EAAA4P,EAAA,IAAAtO,EAAAmO,EAAA/N,EAAAgN,EAAAvN,GAAA,cAAAG,EAAA8D,KAAA,IAAApF,EAAAmB,EAAAwP,KAAA1P,EAAAkB,EAAAb,EAAAoO,MAAAG,EAAA,gBAAAvK,MAAAhE,EAAAoO,IAAAiB,KAAAxP,EAAAwP,KAAA,WAAArP,EAAA8D,OAAApF,EAAAiB,EAAAE,EAAAyP,OAAA,QAAAzP,EAAAuO,IAAApO,EAAAoO,IAAA,YAAAoB,EAAApP,EAAAgN,GAAA,IAAAvN,EAAAuN,EAAAkC,OAAA5Q,EAAA0B,EAAAkN,SAAAzN,GAAA,GAAAnB,IAAAgB,EAAA,OAAA0N,EAAAmC,SAAA,eAAA1P,GAAAO,EAAAkN,SAAAuC,SAAAzC,EAAAkC,OAAA,SAAAlC,EAAAgB,IAAA1O,EAAA8P,EAAApP,EAAAgN,GAAA,UAAAA,EAAAkC,SAAA,WAAAzP,IAAAuN,EAAAkC,OAAA,QAAAlC,EAAAgB,IAAA,IAAA0B,UAAA,oCAAAjQ,EAAA,aAAA0O,EAAA,IAAA3O,EAAAuO,EAAAzP,EAAA0B,EAAAkN,SAAAF,EAAAgB,KAAA,aAAAxO,EAAAkE,KAAA,OAAAsJ,EAAAkC,OAAA,QAAAlC,EAAAgB,IAAAxO,EAAAwO,IAAAhB,EAAAmC,SAAA,KAAAhB,EAAA,IAAAjQ,EAAAsB,EAAAwO,IAAA,OAAA9P,EAAAA,EAAA+Q,MAAAjC,EAAAhN,EAAA2P,YAAAzR,EAAA0F,MAAAoJ,EAAA4C,KAAA5P,EAAA6P,QAAA,WAAA7C,EAAAkC,SAAAlC,EAAAkC,OAAA,OAAAlC,EAAAgB,IAAA1O,GAAA0N,EAAAmC,SAAA,KAAAhB,GAAAjQ,GAAA8O,EAAAkC,OAAA,QAAAlC,EAAAgB,IAAA,IAAA0B,UAAA,oCAAA1C,EAAAmC,SAAA,KAAAhB,EAAA,UAAA2B,EAAAxQ,GAAA,IAAAU,EAAA,CAAA+P,OAAAzQ,EAAA,SAAAA,IAAAU,EAAAgQ,SAAA1Q,EAAA,SAAAA,IAAAU,EAAAiQ,WAAA3Q,EAAA,GAAAU,EAAAkQ,SAAA5Q,EAAA,SAAA6Q,WAAAC,KAAApQ,EAAA,UAAAqQ,EAAA/Q,GAAA,IAAAU,EAAAV,EAAAgR,YAAA,GAAAtQ,EAAA0D,KAAA,gBAAA1D,EAAAgO,IAAA1O,EAAAgR,WAAAtQ,CAAA,UAAA6N,EAAAvO,GAAA,KAAA6Q,WAAA,EAAAJ,OAAA,SAAAzQ,EAAAuJ,QAAAiH,EAAA,WAAAS,OAAA,YAAA/B,EAAAxO,GAAA,GAAAA,GAAA,KAAAA,EAAA,KAAAgN,EAAAhN,EAAA9B,GAAA,GAAA8O,EAAA,OAAAA,EAAAjO,KAAAiB,GAAA,sBAAAA,EAAA4P,KAAA,OAAA5P,EAAA,IAAAwQ,MAAAxQ,EAAAL,QAAA,KAAArB,GAAA,EAAAkB,EAAA,SAAAoQ,IAAA,OAAAtR,EAAA0B,EAAAL,QAAA,GAAAF,EAAAV,KAAAiB,EAAA1B,GAAA,OAAAsR,EAAAhM,MAAA5D,EAAA1B,GAAAsR,EAAAX,MAAA,EAAAW,EAAA,OAAAA,EAAAhM,MAAAtE,EAAAsQ,EAAAX,MAAA,EAAAW,CAAA,SAAApQ,EAAAoQ,KAAApQ,CAAA,YAAAkQ,UAAAZ,EAAA9O,GAAA,2BAAAoO,EAAAvP,UAAAwP,EAAA/P,EAAAmQ,EAAA,eAAA7K,MAAAyK,EAAAb,cAAA,IAAAlP,EAAA+P,EAAA,eAAAzK,MAAAwK,EAAAZ,cAAA,IAAAY,EAAA1G,YAAA6F,EAAAc,EAAAhB,EAAA,qBAAArN,EAAAyQ,oBAAA,SAAAnR,GAAA,IAAAU,EAAA,mBAAAV,GAAAA,EAAAoR,YAAA,QAAA1Q,IAAAA,IAAAoO,GAAA,uBAAApO,EAAA0H,aAAA1H,EAAA2Q,MAAA,EAAA3Q,EAAA8L,KAAA,SAAAxM,GAAA,OAAAf,OAAAqS,eAAArS,OAAAqS,eAAAtR,EAAA+O,IAAA/O,EAAAuR,UAAAxC,EAAAd,EAAAjO,EAAA+N,EAAA,sBAAA/N,EAAAT,UAAAN,OAAAqP,OAAAa,GAAAnP,CAAA,EAAAU,EAAA8Q,MAAA,SAAAxR,GAAA,OAAAyP,QAAAzP,EAAA,EAAAoP,EAAAE,EAAA/P,WAAA0O,EAAAqB,EAAA/P,UAAAsO,GAAA,0BAAAnN,EAAA4O,cAAAA,EAAA5O,EAAA+Q,MAAA,SAAAzR,EAAA0N,EAAAvN,EAAAnB,EAAAkB,QAAA,IAAAA,IAAAA,EAAAyJ,SAAA,IAAA/K,EAAA,IAAA0Q,EAAAlB,EAAApO,EAAA0N,EAAAvN,EAAAnB,GAAAkB,GAAA,OAAAQ,EAAAyQ,oBAAAzD,GAAA9O,EAAAA,EAAA0R,OAAApE,MAAA,SAAAlM,GAAA,OAAAA,EAAA2P,KAAA3P,EAAAsE,MAAA1F,EAAA0R,MAAA,KAAAlB,EAAAD,GAAAlB,EAAAkB,EAAApB,EAAA,aAAAE,EAAAkB,EAAAvQ,GAAA,0BAAAqP,EAAAkB,EAAA,qDAAAzO,EAAA4I,KAAA,SAAAtJ,GAAA,IAAAU,EAAAzB,OAAAe,GAAA0N,EAAA,WAAAvN,KAAAO,EAAAgN,EAAAoD,KAAA3Q,GAAA,OAAAuN,EAAAgE,UAAA,SAAApB,IAAA,KAAA5C,EAAArN,QAAA,KAAAL,EAAA0N,EAAAiE,MAAA,GAAA3R,KAAAU,EAAA,OAAA4P,EAAAhM,MAAAtE,EAAAsQ,EAAAX,MAAA,EAAAW,CAAA,QAAAA,EAAAX,MAAA,EAAAW,CAAA,GAAA5P,EAAAwO,OAAAA,EAAAX,EAAAhP,UAAA,CAAA6R,YAAA7C,EAAA0C,MAAA,SAAAvQ,GAAA,QAAAkR,KAAA,OAAAtB,KAAA,OAAAP,KAAA,KAAAC,MAAAhQ,EAAA,KAAA2P,MAAA,OAAAE,SAAA,UAAAD,OAAA,YAAAlB,IAAA1O,EAAA,KAAA6Q,WAAAtH,QAAAwH,IAAArQ,EAAA,QAAAgN,KAAA,WAAAA,EAAAnL,OAAA,IAAApC,EAAAV,KAAA,KAAAiO,KAAAwD,OAAAxD,EAAArM,MAAA,WAAAqM,GAAA1N,EAAA,EAAA6R,KAAA,gBAAAlC,MAAA,MAAA3P,EAAA,KAAA6Q,WAAA,GAAAG,WAAA,aAAAhR,EAAAoE,KAAA,MAAApE,EAAA0O,IAAA,YAAAoD,IAAA,EAAA7B,kBAAA,SAAAvP,GAAA,QAAAiP,KAAA,MAAAjP,EAAA,IAAAgN,EAAA,cAAAqE,EAAA5R,EAAAnB,GAAA,OAAAJ,EAAAwF,KAAA,QAAAxF,EAAA8P,IAAAhO,EAAAgN,EAAA4C,KAAAnQ,EAAAnB,IAAA0O,EAAAkC,OAAA,OAAAlC,EAAAgB,IAAA1O,KAAAhB,CAAA,SAAAA,EAAA,KAAA6R,WAAAxQ,OAAA,EAAArB,GAAA,IAAAA,EAAA,KAAAkB,EAAA,KAAA2Q,WAAA7R,GAAAJ,EAAAsB,EAAA8Q,WAAA,YAAA9Q,EAAAuQ,OAAA,OAAAsB,EAAA,UAAA7R,EAAAuQ,QAAA,KAAAmB,KAAA,KAAA/D,EAAA1N,EAAAV,KAAAS,EAAA,YAAA6N,EAAA5N,EAAAV,KAAAS,EAAA,iBAAA2N,GAAAE,EAAA,SAAA6D,KAAA1R,EAAAwQ,SAAA,OAAAqB,EAAA7R,EAAAwQ,UAAA,WAAAkB,KAAA1R,EAAAyQ,WAAA,OAAAoB,EAAA7R,EAAAyQ,WAAA,SAAA9C,GAAA,QAAA+D,KAAA1R,EAAAwQ,SAAA,OAAAqB,EAAA7R,EAAAwQ,UAAA,YAAA3C,EAAA,MAAAvI,MAAA,kDAAAoM,KAAA1R,EAAAyQ,WAAA,OAAAoB,EAAA7R,EAAAyQ,WAAA,KAAAT,OAAA,SAAAlQ,EAAAU,GAAA,QAAAgN,EAAA,KAAAmD,WAAAxQ,OAAA,EAAAqN,GAAA,IAAAA,EAAA,KAAA1O,EAAA,KAAA6R,WAAAnD,GAAA,GAAA1O,EAAAyR,QAAA,KAAAmB,MAAAzR,EAAAV,KAAAT,EAAA,oBAAA4S,KAAA5S,EAAA2R,WAAA,KAAAzQ,EAAAlB,EAAA,OAAAkB,IAAA,UAAAF,GAAA,aAAAA,IAAAE,EAAAuQ,QAAA/P,GAAAA,GAAAR,EAAAyQ,aAAAzQ,EAAA,UAAAtB,EAAAsB,EAAAA,EAAA8Q,WAAA,UAAApS,EAAAwF,KAAApE,EAAApB,EAAA8P,IAAAhO,EAAAR,GAAA,KAAA0P,OAAA,YAAAU,KAAApQ,EAAAyQ,WAAA9B,GAAA,KAAAmD,SAAApT,EAAA,EAAAoT,SAAA,SAAAhS,EAAAU,GAAA,aAAAV,EAAAoE,KAAA,MAAApE,EAAA0O,IAAA,gBAAA1O,EAAAoE,MAAA,aAAApE,EAAAoE,KAAA,KAAAkM,KAAAtQ,EAAA0O,IAAA,WAAA1O,EAAAoE,MAAA,KAAA0N,KAAA,KAAApD,IAAA1O,EAAA0O,IAAA,KAAAkB,OAAA,cAAAU,KAAA,kBAAAtQ,EAAAoE,MAAA1D,IAAA,KAAA4P,KAAA5P,GAAAmO,CAAA,EAAAoD,OAAA,SAAAjS,GAAA,QAAAU,EAAA,KAAAmQ,WAAAxQ,OAAA,EAAAK,GAAA,IAAAA,EAAA,KAAAgN,EAAA,KAAAmD,WAAAnQ,GAAA,GAAAgN,EAAAiD,aAAA3Q,EAAA,YAAAgS,SAAAtE,EAAAsD,WAAAtD,EAAAkD,UAAAG,EAAArD,GAAAmB,CAAA,GAAAhH,MAAA,SAAA7H,GAAA,QAAAU,EAAA,KAAAmQ,WAAAxQ,OAAA,EAAAK,GAAA,IAAAA,EAAA,KAAAgN,EAAA,KAAAmD,WAAAnQ,GAAA,GAAAgN,EAAA+C,SAAAzQ,EAAA,KAAAG,EAAAuN,EAAAsD,WAAA,aAAA7Q,EAAAiE,KAAA,KAAApF,EAAAmB,EAAAuO,IAAAqC,EAAArD,EAAA,QAAA1O,CAAA,QAAAwG,MAAA,0BAAA0M,cAAA,SAAAxR,EAAAgN,EAAAvN,GAAA,YAAA0P,SAAA,CAAAjC,SAAAsB,EAAAxO,GAAA2P,WAAA3C,EAAA6C,QAAApQ,GAAA,cAAAyP,SAAA,KAAAlB,IAAA1O,GAAA6O,CAAA,GAAAnO,CAAA,UAAAyR,EAAAhS,EAAAH,EAAAU,EAAAgN,EAAA1O,EAAAJ,EAAAiP,GAAA,QAAA3N,EAAAC,EAAAvB,GAAAiP,GAAAE,EAAA7N,EAAAoE,KAAA,OAAAnE,GAAA,YAAAO,EAAAP,EAAA,CAAAD,EAAAyP,KAAA3P,EAAA+N,GAAApE,QAAAE,QAAAkE,GAAA7B,KAAAwB,EAAA1O,EAAA,UAAAoT,EAAA1E,EAAA9O,IAAA,MAAAA,GAAAA,EAAA8O,EAAArN,UAAAzB,EAAA8O,EAAArN,QAAA,QAAAK,EAAA,EAAAP,EAAAiB,MAAAxC,GAAA8B,EAAA9B,EAAA8B,IAAAP,EAAAO,GAAAgN,EAAAhN,GAAA,OAAAP,CAAA,ED0pC8B,IAAAiF,eAAc,CAC1CiN,eAAgB,KAChBC,OAAQ,CAAC,EACTC,cAAe/E,EACfgF,gBAAiBhF,IC7pCZ,IAAMiF,EAAkB,SAAEC,GAChC,IAFDhF,EAAAhN,EAE2CiS,GAF3CjF,GAE2BvG,EAAAA,EAAAA,UAAU,MAFrCzG,EAE2C,EAF3C,SAAAgN,GAAA,GAAAtM,MAAAwR,QAAAlF,GAAA,OAAAA,CAAA,CAAAmF,CAAAnF,IAAA,SAAAA,EAAAvM,GAAA,IAAAnB,EAAA,MAAA0N,EAAA,yBAAAC,QAAAD,EAAAC,OAAAC,WAAAF,EAAA,uBAAA1N,EAAA,KAAAU,EAAAP,EAAAD,EAAA6N,EAAAnP,EAAA,GAAAgQ,GAAA,EAAA5P,GAAA,SAAAkB,GAAAF,EAAAA,EAAAP,KAAAiO,IAAA4C,KAAA,IAAAnP,EAAA,IAAAlC,OAAAe,KAAAA,EAAA,OAAA4O,GAAA,cAAAA,GAAAlO,EAAAR,EAAAT,KAAAO,IAAA2P,QAAA/Q,EAAAkS,KAAApQ,EAAA4D,OAAA1F,EAAAyB,SAAAc,GAAAyN,GAAA,UAAAlB,GAAA1O,GAAA,EAAAmB,EAAAuN,CAAA,iBAAAkB,GAAA,MAAA5O,EAAAmQ,SAAApC,EAAA/N,EAAAmQ,SAAAlR,OAAA8O,KAAAA,GAAA,kBAAA/O,EAAA,MAAAmB,CAAA,SAAAvB,CAAA,EAAAkU,CAAApF,EAAAhN,IAAA,SAAAgN,EAAA9O,GAAA,GAAA8O,EAAA,qBAAAA,EAAA,OAAA0E,EAAA1E,EAAA9O,GAAA,IAAAoB,EAAA,GAAAsK,SAAA7K,KAAAiO,GAAArM,MAAA,uBAAArB,GAAA0N,EAAA0D,cAAApR,EAAA0N,EAAA0D,YAAAC,MAAA,QAAArR,GAAA,QAAAA,EAAAoB,MAAAJ,KAAA0M,GAAA,cAAA1N,GAAA,2CAAA+S,KAAA/S,GAAAoS,EAAA1E,EAAA9O,QAAA,GAAAoU,CAAAtF,EAAAhN,IAAA,qBAAA0P,UAAA,6IAAA6C,IAES1K,EAAIoK,EAAA,GAAEO,EAAOP,EAAA,GAmBrB,OAjBA5K,EAAAA,EAAAA,YAAW,WAJZ,IAAA5H,KAKEsN,IAAAjB,MAAE,SAAA2G,IAAA,IAAAC,EAAAC,EAAAC,EAAA,OAAA7F,IAAAW,MAAA,SAAAmF,GAAA,cAAAA,EAAA3B,KAAA2B,EAAAjD,MAAA,cAAAiD,EAAA3B,KAAA,EAAA2B,EAAAjD,KAAA,EAEuBkD,MAAOd,EAAce,UAAU,OAAxC,OAARJ,EAAQE,EAAAxD,KAAAwD,EAAAjD,KAAG,EACE+C,EAASC,OAAM,QAA5BA,EAAIC,EAAAxD,MACA2D,SAALJ,MAAgBA,GAAU,QAANF,EAAJE,EAAM/K,YAAI,IAAA6K,GAAVA,EAAYO,WAChCT,EAASI,EAAK/K,MAEd2K,GAAS,GACTK,EAAAjD,KAAA,iBAAAiD,EAAA3B,KAAA,GAAA2B,EAAAK,GAAAL,EAAA,SAEDlG,QAAQC,MAAKiG,EAAAK,IACbV,GAAS,GAAQ,yBAAAK,EAAA1B,OAAA,GAAAsB,EAAA,kBAhBrB,eAAAnT,EAAA,KAAAU,EAAAN,UAAA,WAAAuJ,SAAA,SAAA+D,EAAA1O,GAAA,IAAAJ,EAAAuB,EAAAI,MAAAP,EAAAU,GAAA,SAAAmT,EAAA1T,GAAAgS,EAAAvT,EAAA8O,EAAA1O,EAAA6U,EAAAC,EAAA,OAAA3T,EAAA,UAAA2T,EAAA3T,GAAAgS,EAAAvT,EAAA8O,EAAA1O,EAAA6U,EAAAC,EAAA,QAAA3T,EAAA,CAAA0T,OAAA,SAmBC,GAAG,CAAEnB,IAEEnK,CACR,+jECZA,IAAMwL,EACLC,MAAA5K,cAAA,OAAK6K,MAAM,UAAUC,OAAO,MAAMC,QAAQ,qBACzCH,MAAA5K,cAAA,KAAGgL,UAAU,8BACZJ,MAAA5K,cAAA,QACCiL,SAAS,OACT1V,EAAE,uQACF2V,KAAK,YAENN,MAAA5K,cAAA,QACCiL,SAAS,OACT1V,EAAE,iWACF2V,KAAK,YAENN,MAAA5K,cAAA,QACCiL,SAAS,OACT1V,EAAE,wOACF2V,KAAK,eAMTC,EAAAA,EAAAA,mBAAmBC,EAAU,CAC5BC,KAAMV,EACNW,KC5Bc,SAAaC,GAA4C,IAAAC,EAKnEC,MAL2B3L,EAAUyL,EAAVzL,WAAY+D,EAAQ0H,EAAR1H,SAAU6H,EAAaH,EAAbG,cAC7CC,EAAW7L,EAAX6L,OAEuCpC,MAAjBxL,EAAAA,EAAAA,WAAU,KAAO,s4BAAvC6N,EAAMrC,EAAA,GAAEsC,EAAStC,EAAA,GAGnBuC,EAAc1R,OACY,QADNoR,EACzBO,GAAG5M,KAAK6M,OAAQ,sBAAe,IAAAR,OAAA,EAA/BA,EAAiCS,yBAGjCH,EAAYrS,SAAU,yBACtBqS,EAAYrS,SAAU,uBAEtBgS,EAAS,IAGV,IAGIS,EAHEC,EAAiBC,yBAAyBC,OAAOC,SAKtDJ,EAD8B,SAA1BC,EAAeI,OACJ,CACdA,OAAQJ,EAAeI,OACvBC,MAAOL,EAAeK,MACtBC,MAAON,EAAeM,OAGR,CACdF,OAAQJ,EAAeI,OACvBG,KAAM,CACLC,SAAUR,EAAgB,iBAC1BnR,KAAMmR,EAAgB,cAEvBS,KAAM,CACLJ,MAAOL,EAAgB,cACvBU,KAAMV,EAAgB,eAKzB,IAAIW,EAAU,CAAE,8BAA+B,wBAE9CV,yBAAyBW,iBACvBX,yBAAyBY,mBAE3BF,EAAU,CACT,8BACA,4BACA,yBAGF,IAAMG,GAAQC,EAAAA,EAAAA,eAAe,CAAEnQ,UAAW+P,IAQ1C,IANAnO,EAAAA,EAAAA,YAAW,WACHgN,GACND,EAAe,CAAEC,OAAQ,QAAU9H,GAErC,GAAG,CAAE8H,EAAQ9H,IAERuI,yBAAyBW,gBAC7B,OACCnC,MAAA5K,cAAA,MAAUiN,EACTrC,MAAA5K,cAAA,OAAKjD,UAAY,kCAChB6N,MAAA5K,cAAA,KAAGjD,UAAY,kCACZoQ,EAAAA,EAAAA,IACD,iKACA,gCAGFvC,MAAA5K,cAAA,OAAKjD,UAAY,iCAChB6N,MAAA5K,cAAA,QAAMjD,UAAY,gCACjB6N,MAAA5K,cAAA,KAAGoN,KAAOhB,yBAAyBiB,aAClCzC,MAAA5K,cAAA,UACChF,KAAO,SACP+B,UAAY,iCAEVoQ,EAAAA,EAAAA,IACD,2BACA,qCAWT,IAAOf,yBAAyBY,iBAC/B,OACCpC,MAAA5K,cAAA,MAAUiN,EACTrC,MAAA5K,cAAA,OAAKjD,UAAY,kCAChB6N,MAAA5K,cAAA,KAAGjD,UAAY,kCACZoQ,EAAAA,EAAAA,IACD,+LACA,gCAGFvC,MAAA5K,cAAA,OAAKjD,UAAY,iCAChB6N,MAAA5K,cAAA,QAAMjD,UAAY,gCACjB6N,MAAA5K,cAAA,KACCoN,KACChB,yBAAyBkB,qBAG1B1C,MAAA5K,cAAA,UACChF,KAAO,SACP+B,UAAY,iCAEVoQ,EAAAA,EAAAA,IACD,2BACA,qCAWT,IAAMI,EAAelE,EACpB+C,yBAAyBoB,KAAKC,oBAE/B,GAAsB,OAAjBF,EACJ,OACC3C,MAAA5K,cAAA,MAAUiN,EACTrC,MAAA5K,cAAC0N,EAAAA,QAAO,OAKX,IAAMC,EAASC,EAAAA,EAAA,GACXL,EAAahD,YAAU,IAC1BtL,WAAY,WACZJ,cAAe,gDAGhB,OACC+L,MAAA5K,cAAA4K,MAAAiD,SAAA,KACCjD,MAAA5K,cAAC8N,EAAAA,kBAAiB,KACjBlD,MAAA5K,cAAC+N,EAAAA,UAAS,CACTC,OAAQb,EAAAA,EAAAA,IACP,2BACA,gCAGDvC,MAAA5K,cAAA,UACGmN,EAAAA,EAAAA,IACD,kIACA,gCAGFvC,MAAA5K,cAAA,KAAGoN,KAAOhB,yBAAyBkB,qBAClC1C,MAAA5K,cAAA,UACChF,KAAO,SACP+B,UAAY,iCAEVoQ,EAAAA,EAAAA,IACD,2BACA,mCAMLvC,MAAA5K,cAAA,MAAUiN,EACTrC,MAAA5K,cAAA,OAAKjD,UAAY,sBAChB6N,MAAA5K,cAAC4D,EAAoB,CAAC9J,QAAU6T,GAC/B/C,MAAA5K,cAACuD,EAAc,CACd5D,MAAQuM,EACR+B,SAAW,WAAH,OAASpC,GAAW,EAAM,EAClCJ,OAASA,MAIZb,MAAA5K,cAAA,OAAKjD,UAAY,8CACd,KAEE6O,GAAUhB,MAAA5K,cAAC0N,EAAAA,QAAO,QAK3B,ED7JCQ,KAAI,WACH,OAAO,IACR", "sources": ["webpack://ppcp-paylater-wc-blocks/webpack/bootstrap", "webpack://ppcp-paylater-wc-blocks/webpack/runtime/compat get default export", "webpack://ppcp-paylater-wc-blocks/webpack/runtime/define property getters", "webpack://ppcp-paylater-wc-blocks/webpack/runtime/hasOwnProperty shorthand", "webpack://ppcp-paylater-wc-blocks/external window [\"wp\",\"blocks\"]", "webpack://ppcp-paylater-wc-blocks/external window [\"wp\",\"i18n\"]", "webpack://ppcp-paylater-wc-blocks/external window [\"wp\",\"element\"]", "webpack://ppcp-paylater-wc-blocks/external window [\"wp\",\"blockEditor\"]", "webpack://ppcp-paylater-wc-blocks/external window [\"wp\",\"components\"]", "webpack://ppcp-paylater-wc-blocks/external window \"React\"", "webpack://ppcp-paylater-wc-blocks/./node_modules/@paypal/react-paypal-js/dist/esm/react-paypal-js.js", "webpack://ppcp-paylater-wc-blocks/../ppcp-paylater-block/resources/js/hooks/script-params.js", "webpack://ppcp-paylater-wc-blocks/./resources/js/CheckoutPayLaterMessagesBlock/checkout-paylater-block.js", "webpack://ppcp-paylater-wc-blocks/./resources/js/CheckoutPayLaterMessagesBlock/edit.js"], "sourcesContent": ["// The require scope\nvar __webpack_require__ = {};\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "const __WEBPACK_NAMESPACE_OBJECT__ = window[\"wp\"][\"blocks\"];", "const __WEBPACK_NAMESPACE_OBJECT__ = window[\"wp\"][\"i18n\"];", "const __WEBPACK_NAMESPACE_OBJECT__ = window[\"wp\"][\"element\"];", "const __WEBPACK_NAMESPACE_OBJECT__ = window[\"wp\"][\"blockEditor\"];", "const __WEBPACK_NAMESPACE_OBJECT__ = window[\"wp\"][\"components\"];", "const __WEBPACK_NAMESPACE_OBJECT__ = window[\"React\"];", "/*!\n * react-paypal-js v8.7.0 (2024-09-16T17:52:54.237Z)\n * Copyright 2020-present, PayPal, Inc. All rights reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport React, { createContext, useContext, useRef, useState, useEffect, useReducer } from 'react';\n\n/**\n * Enum for the SDK script resolve status,\n *\n * @enum {string}\n */\nvar SCRIPT_LOADING_STATE;\n(function (SCRIPT_LOADING_STATE) {\n  SCRIPT_LOADING_STATE[\"INITIAL\"] = \"initial\";\n  SCRIPT_LOADING_STATE[\"PENDING\"] = \"pending\";\n  SCRIPT_LOADING_STATE[\"REJECTED\"] = \"rejected\";\n  SCRIPT_LOADING_STATE[\"RESOLVED\"] = \"resolved\";\n})(SCRIPT_LOADING_STATE || (SCRIPT_LOADING_STATE = {}));\n/**\n * Enum for the PayPalScriptProvider context dispatch actions\n *\n * @enum {string}\n */\nvar DISPATCH_ACTION;\n(function (DISPATCH_ACTION) {\n  DISPATCH_ACTION[\"LOADING_STATUS\"] = \"setLoadingStatus\";\n  DISPATCH_ACTION[\"RESET_OPTIONS\"] = \"resetOptions\";\n  DISPATCH_ACTION[\"SET_BRAINTREE_INSTANCE\"] = \"braintreeInstance\";\n})(DISPATCH_ACTION || (DISPATCH_ACTION = {}));\n/**\n * Enum for all the available hosted fields\n *\n * @enum {string}\n */\nvar PAYPAL_HOSTED_FIELDS_TYPES;\n(function (PAYPAL_HOSTED_FIELDS_TYPES) {\n  PAYPAL_HOSTED_FIELDS_TYPES[\"NUMBER\"] = \"number\";\n  PAYPAL_HOSTED_FIELDS_TYPES[\"CVV\"] = \"cvv\";\n  PAYPAL_HOSTED_FIELDS_TYPES[\"EXPIRATION_DATE\"] = \"expirationDate\";\n  PAYPAL_HOSTED_FIELDS_TYPES[\"EXPIRATION_MONTH\"] = \"expirationMonth\";\n  PAYPAL_HOSTED_FIELDS_TYPES[\"EXPIRATION_YEAR\"] = \"expirationYear\";\n  PAYPAL_HOSTED_FIELDS_TYPES[\"POSTAL_CODE\"] = \"postalCode\";\n})(PAYPAL_HOSTED_FIELDS_TYPES || (PAYPAL_HOSTED_FIELDS_TYPES = {}));\nvar __assign = function () {\n  __assign = Object.assign || function __assign(t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n      for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n    }\n    return t;\n  };\n  return __assign.apply(this, arguments);\n};\nfunction __rest(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n}\nfunction __spreadArray(to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n    if (ar || !(i in from)) {\n      if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n      ar[i] = from[i];\n    }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n}\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\n/*********************************************\n * Common reference to the script identifier *\n *********************************************/\n// keep this script id value in kebab-case format\nvar SCRIPT_ID = \"data-react-paypal-script-id\";\nvar SDK_SETTINGS = {\n  DATA_CLIENT_TOKEN: \"dataClientToken\",\n  DATA_JS_SDK_LIBRARY: \"dataJsSdkLibrary\",\n  DATA_LIBRARY_VALUE: \"react-paypal-js\",\n  DATA_NAMESPACE: \"dataNamespace\",\n  DATA_SDK_INTEGRATION_SOURCE: \"dataSdkIntegrationSource\",\n  DATA_USER_ID_TOKEN: \"dataUserIdToken\"\n};\nvar LOAD_SCRIPT_ERROR = \"Failed to load the PayPal JS SDK script.\";\n/****************************\n * Braintree error messages *\n ****************************/\nvar EMPTY_BRAINTREE_AUTHORIZATION_ERROR_MESSAGE = \"Invalid authorization data. Use dataClientToken or dataUserIdToken to authorize.\";\nvar braintreeVersion = \"3.84.0\";\nvar BRAINTREE_SOURCE = \"https://js.braintreegateway.com/web/\".concat(braintreeVersion, \"/js/client.min.js\");\nvar BRAINTREE_PAYPAL_CHECKOUT_SOURCE = \"https://js.braintreegateway.com/web/\".concat(braintreeVersion, \"/js/paypal-checkout.min.js\");\n/*********************\n * PayPal namespaces *\n *********************/\nvar DEFAULT_PAYPAL_NAMESPACE = \"paypal\";\nvar DEFAULT_BRAINTREE_NAMESPACE = \"braintree\";\n/*****************\n * Hosted Fields *\n *****************/\nvar HOSTED_FIELDS_CHILDREN_ERROR = \"To use HostedFields you must use it with at least 3 children with types: [number, cvv, expirationDate] includes\";\nvar HOSTED_FIELDS_DUPLICATE_CHILDREN_ERROR = \"Cannot use duplicate HostedFields as children\";\n/*******************\n * Script Provider *\n *******************/\nvar SCRIPT_PROVIDER_REDUCER_ERROR = \"usePayPalScriptReducer must be used within a PayPalScriptProvider\";\nvar CARD_FIELDS_DUPLICATE_CHILDREN_ERROR = \"Cannot use duplicate CardFields as children\";\nvar CARD_FIELDS_CONTEXT_ERROR = \"Individual CardFields must be rendered inside the PayPalCardFieldsProvider\";\n\n/**\n * Get the namespace from the window in the browser\n * this is useful to get the paypal object from window\n * after load PayPal SDK script\n *\n * @param namespace the name space to return\n * @returns the namespace if exists or undefined otherwise\n */\nfunction getPayPalWindowNamespace$1(namespace) {\n  if (namespace === void 0) {\n    namespace = DEFAULT_PAYPAL_NAMESPACE;\n  }\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  return window[namespace];\n}\n/**\n * Get a namespace from the window in the browser\n * this is useful to get the braintree from window\n * after load Braintree script\n *\n * @param namespace the name space to return\n * @returns the namespace if exists or undefined otherwise\n */\nfunction getBraintreeWindowNamespace(namespace) {\n  if (namespace === void 0) {\n    namespace = DEFAULT_BRAINTREE_NAMESPACE;\n  }\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  return window[namespace];\n}\n/**\n * Creates a string hash code based on the string argument\n *\n * @param str the source input string to hash\n * @returns string hash code\n */\nfunction hashStr(str) {\n  var hash = \"\";\n  for (var i = 0; i < str.length; i++) {\n    var total = str[i].charCodeAt(0) * i;\n    if (str[i + 1]) {\n      total += str[i + 1].charCodeAt(0) * (i - 1);\n    }\n    hash += String.fromCharCode(97 + Math.abs(total) % 26);\n  }\n  return hash;\n}\nfunction generateErrorMessage(_a) {\n  var reactComponentName = _a.reactComponentName,\n    sdkComponentKey = _a.sdkComponentKey,\n    _b = _a.sdkRequestedComponents,\n    sdkRequestedComponents = _b === void 0 ? \"\" : _b,\n    _c = _a.sdkDataNamespace,\n    sdkDataNamespace = _c === void 0 ? DEFAULT_PAYPAL_NAMESPACE : _c;\n  var requiredOptionCapitalized = sdkComponentKey.charAt(0).toUpperCase().concat(sdkComponentKey.substring(1));\n  var errorMessage = \"Unable to render <\".concat(reactComponentName, \" /> because window.\").concat(sdkDataNamespace, \".\").concat(requiredOptionCapitalized, \" is undefined.\");\n  // The JS SDK only loads the buttons component by default.\n  // All other components like messages and marks must be requested using the \"components\" query parameter\n  var requestedComponents = typeof sdkRequestedComponents === \"string\" ? sdkRequestedComponents : sdkRequestedComponents.join(\",\");\n  if (!requestedComponents.includes(sdkComponentKey)) {\n    var expectedComponents = [requestedComponents, sdkComponentKey].filter(Boolean).join();\n    errorMessage += \"\\nTo fix the issue, add '\".concat(sdkComponentKey, \"' to the list of components passed to the parent PayPalScriptProvider:\") + \"\\n`<PayPalScriptProvider options={{ components: '\".concat(expectedComponents, \"'}}>`.\");\n  }\n  return errorMessage;\n}\n\n/**\n * Generate a new random identifier for react-paypal-js\n *\n * @returns the {@code string} containing the random library name\n */\nfunction getScriptID(options) {\n  // exclude the data-react-paypal-script-id value from the options hash\n  var _a = options,\n    _b = SCRIPT_ID;\n  _a[_b];\n  var paypalScriptOptions = __rest(_a, [_b + \"\"]);\n  return \"react-paypal-js-\".concat(hashStr(JSON.stringify(paypalScriptOptions)));\n}\n/**\n * Destroy the PayPal SDK from the document page\n *\n * @param reactPayPalScriptID the script identifier\n */\nfunction destroySDKScript(reactPayPalScriptID) {\n  var scriptNode = self.document.querySelector(\"script[\".concat(SCRIPT_ID, \"=\\\"\").concat(reactPayPalScriptID, \"\\\"]\"));\n  if (scriptNode === null || scriptNode === void 0 ? void 0 : scriptNode.parentNode) {\n    scriptNode.parentNode.removeChild(scriptNode);\n  }\n}\n/**\n * Reducer function to handle complex state changes on the context\n *\n * @param state  the current state on the context object\n * @param action the action to be executed on the previous state\n * @returns a the same state if the action wasn't found, or a new state otherwise\n */\nfunction scriptReducer(state, action) {\n  var _a, _b;\n  switch (action.type) {\n    case DISPATCH_ACTION.LOADING_STATUS:\n      if (typeof action.value === \"object\") {\n        return __assign(__assign({}, state), {\n          loadingStatus: action.value.state,\n          loadingStatusErrorMessage: action.value.message\n        });\n      }\n      return __assign(__assign({}, state), {\n        loadingStatus: action.value\n      });\n    case DISPATCH_ACTION.RESET_OPTIONS:\n      // destroy existing script to make sure only one script loads at a time\n      destroySDKScript(state.options[SCRIPT_ID]);\n      return __assign(__assign({}, state), {\n        loadingStatus: SCRIPT_LOADING_STATE.PENDING,\n        options: __assign(__assign((_a = {}, _a[SDK_SETTINGS.DATA_SDK_INTEGRATION_SOURCE] = SDK_SETTINGS.DATA_LIBRARY_VALUE, _a), action.value), (_b = {}, _b[SCRIPT_ID] = \"\".concat(getScriptID(action.value)), _b))\n      });\n    case DISPATCH_ACTION.SET_BRAINTREE_INSTANCE:\n      return __assign(__assign({}, state), {\n        braintreePayPalCheckoutInstance: action.value\n      });\n    default:\n      {\n        return state;\n      }\n  }\n}\n// Create the React context to use in the script provider component\nvar ScriptContext = createContext(null);\n\n/**\n * Check if the context is valid and ready to dispatch actions.\n *\n * @param scriptContext the result of connecting to the context provider\n * @returns strict context avoiding null values in the type\n */\nfunction validateReducer(scriptContext) {\n  if (typeof (scriptContext === null || scriptContext === void 0 ? void 0 : scriptContext.dispatch) === \"function\" && scriptContext.dispatch.length !== 0) {\n    return scriptContext;\n  }\n  throw new Error(SCRIPT_PROVIDER_REDUCER_ERROR);\n}\n/**\n * Check if the dataClientToken or the dataUserIdToken are\n * set in the options of the context.\n * @type dataClientToken is use to pass a client token\n * @type dataUserIdToken is use to pass a client tokenization key\n *\n * @param scriptContext the result of connecting to the context provider\n * @throws an {@link Error} if both dataClientToken and the dataUserIdToken keys are null or undefined\n * @returns strict context if one of the keys are defined\n */\nvar validateBraintreeAuthorizationData = function (scriptContext) {\n  var _a, _b;\n  if (!((_a = scriptContext === null || scriptContext === void 0 ? void 0 : scriptContext.options) === null || _a === void 0 ? void 0 : _a[SDK_SETTINGS.DATA_CLIENT_TOKEN]) && !((_b = scriptContext === null || scriptContext === void 0 ? void 0 : scriptContext.options) === null || _b === void 0 ? void 0 : _b[SDK_SETTINGS.DATA_USER_ID_TOKEN])) {\n    throw new Error(EMPTY_BRAINTREE_AUTHORIZATION_ERROR_MESSAGE);\n  }\n  return scriptContext;\n};\n\n/**\n * Custom hook to get access to the Script context and\n * dispatch actions to modify the state on the {@link ScriptProvider} component\n *\n * @returns a tuple containing the state of the context and\n * a dispatch function to modify the state\n */\nfunction usePayPalScriptReducer() {\n  var scriptContext = validateReducer(useContext(ScriptContext));\n  var derivedStatusContext = __assign(__assign({}, scriptContext), {\n    isInitial: scriptContext.loadingStatus === SCRIPT_LOADING_STATE.INITIAL,\n    isPending: scriptContext.loadingStatus === SCRIPT_LOADING_STATE.PENDING,\n    isResolved: scriptContext.loadingStatus === SCRIPT_LOADING_STATE.RESOLVED,\n    isRejected: scriptContext.loadingStatus === SCRIPT_LOADING_STATE.REJECTED\n  });\n  return [derivedStatusContext, scriptContext.dispatch];\n}\n/**\n * Custom hook to get access to the ScriptProvider context\n *\n * @returns the latest state of the context\n */\nfunction useScriptProviderContext() {\n  var scriptContext = validateBraintreeAuthorizationData(validateReducer(useContext(ScriptContext)));\n  return [scriptContext, scriptContext.dispatch];\n}\n\n// Create the React context to use in the PayPal hosted fields provider\nvar PayPalHostedFieldsContext = createContext({});\n\n/**\n * Custom hook to get access to the PayPal Hosted Fields instance.\n * The instance represent the returned object after the render process\n * With this object a user can submit the fields and dynamically modify the cards\n *\n * @returns the hosted fields instance if is available in the component\n */\nfunction usePayPalHostedFields() {\n  return useContext(PayPalHostedFieldsContext);\n}\n\n/**\nThis `<PayPalButtons />` component supports rendering [buttons](https://developer.paypal.com/docs/business/javascript-sdk/javascript-sdk-reference/#buttons) for PayPal, Venmo, and alternative payment methods.\nIt relies on the `<PayPalScriptProvider />` parent component for managing state related to loading the JS SDK script.\n*/\nvar PayPalButtons = function (_a) {\n  var _b;\n  var _c = _a.className,\n    className = _c === void 0 ? \"\" : _c,\n    _d = _a.disabled,\n    disabled = _d === void 0 ? false : _d,\n    children = _a.children,\n    _e = _a.forceReRender,\n    forceReRender = _e === void 0 ? [] : _e,\n    buttonProps = __rest(_a, [\"className\", \"disabled\", \"children\", \"forceReRender\"]);\n  var isDisabledStyle = disabled ? {\n    opacity: 0.38\n  } : {};\n  var classNames = \"\".concat(className, \" \").concat(disabled ? \"paypal-buttons-disabled\" : \"\").trim();\n  var buttonsContainerRef = useRef(null);\n  var buttons = useRef(null);\n  var _f = usePayPalScriptReducer()[0],\n    isResolved = _f.isResolved,\n    options = _f.options;\n  var _g = useState(null),\n    initActions = _g[0],\n    setInitActions = _g[1];\n  var _h = useState(true),\n    isEligible = _h[0],\n    setIsEligible = _h[1];\n  var _j = useState(null),\n    setErrorState = _j[1];\n  function closeButtonsComponent() {\n    if (buttons.current !== null) {\n      buttons.current.close().catch(function () {\n        // ignore errors when closing the component\n      });\n    }\n  }\n  if ((_b = buttons.current) === null || _b === void 0 ? void 0 : _b.updateProps) {\n    buttons.current.updateProps({\n      message: buttonProps.message\n    });\n  }\n  // useEffect hook for rendering the buttons\n  useEffect(function () {\n    // verify the sdk script has successfully loaded\n    if (isResolved === false) {\n      return closeButtonsComponent;\n    }\n    var paypalWindowNamespace = getPayPalWindowNamespace$1(options.dataNamespace);\n    // verify dependency on window object\n    if (paypalWindowNamespace === undefined || paypalWindowNamespace.Buttons === undefined) {\n      setErrorState(function () {\n        throw new Error(generateErrorMessage({\n          reactComponentName: PayPalButtons.displayName,\n          sdkComponentKey: \"buttons\",\n          sdkRequestedComponents: options.components,\n          sdkDataNamespace: options[SDK_SETTINGS.DATA_NAMESPACE]\n        }));\n      });\n      return closeButtonsComponent;\n    }\n    var decoratedOnInit = function (data, actions) {\n      setInitActions(actions);\n      if (typeof buttonProps.onInit === \"function\") {\n        buttonProps.onInit(data, actions);\n      }\n    };\n    try {\n      buttons.current = paypalWindowNamespace.Buttons(__assign(__assign({}, buttonProps), {\n        onInit: decoratedOnInit\n      }));\n    } catch (err) {\n      return setErrorState(function () {\n        throw new Error(\"Failed to render <PayPalButtons /> component. Failed to initialize:  \".concat(err));\n      });\n    }\n    // only render the button when eligible\n    if (buttons.current.isEligible() === false) {\n      setIsEligible(false);\n      return closeButtonsComponent;\n    }\n    if (!buttonsContainerRef.current) {\n      return closeButtonsComponent;\n    }\n    buttons.current.render(buttonsContainerRef.current).catch(function (err) {\n      // component failed to render, possibly because it was closed or destroyed.\n      if (buttonsContainerRef.current === null || buttonsContainerRef.current.children.length === 0) {\n        // paypal buttons container is no longer in the DOM, we can safely ignore the error\n        return;\n      }\n      // paypal buttons container is still in the DOM\n      setErrorState(function () {\n        throw new Error(\"Failed to render <PayPalButtons /> component. \".concat(err));\n      });\n    });\n    return closeButtonsComponent;\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, __spreadArray(__spreadArray([isResolved], forceReRender, true), [buttonProps.fundingSource], false));\n  // useEffect hook for managing disabled state\n  useEffect(function () {\n    if (initActions === null) {\n      return;\n    }\n    if (disabled === true) {\n      initActions.disable().catch(function () {\n        // ignore errors when disabling the component\n      });\n    } else {\n      initActions.enable().catch(function () {\n        // ignore errors when enabling the component\n      });\n    }\n  }, [disabled, initActions]);\n  return React.createElement(React.Fragment, null, isEligible ? React.createElement(\"div\", {\n    ref: buttonsContainerRef,\n    style: isDisabledStyle,\n    className: classNames\n  }) : children);\n};\nPayPalButtons.displayName = \"PayPalButtons\";\nfunction findScript(url, attributes) {\n  var currentScript = document.querySelector(\"script[src=\\\"\".concat(url, \"\\\"]\"));\n  if (currentScript === null) return null;\n  var nextScript = createScriptElement(url, attributes);\n  var currentScriptClone = currentScript.cloneNode();\n  delete currentScriptClone.dataset.uidAuto;\n  if (Object.keys(currentScriptClone.dataset).length !== Object.keys(nextScript.dataset).length) {\n    return null;\n  }\n  var isExactMatch = true;\n  Object.keys(currentScriptClone.dataset).forEach(function (key) {\n    if (currentScriptClone.dataset[key] !== nextScript.dataset[key]) {\n      isExactMatch = false;\n    }\n  });\n  return isExactMatch ? currentScript : null;\n}\nfunction insertScriptElement(_a) {\n  var url = _a.url,\n    attributes = _a.attributes,\n    onSuccess = _a.onSuccess,\n    onError = _a.onError;\n  var newScript = createScriptElement(url, attributes);\n  newScript.onerror = onError;\n  newScript.onload = onSuccess;\n  document.head.insertBefore(newScript, document.head.firstElementChild);\n}\nfunction processOptions(options) {\n  var environment = options.environment;\n  var sdkBaseUrl = environment === \"sandbox\" ? \"https://www.sandbox.paypal.com/sdk/js\" : \"https://www.paypal.com/sdk/js\";\n  delete options.environment;\n  if (options.sdkBaseUrl) {\n    sdkBaseUrl = options.sdkBaseUrl;\n    delete options.sdkBaseUrl;\n  }\n  var optionsWithStringIndex = options;\n  var _a = Object.keys(optionsWithStringIndex).filter(function (key) {\n      return typeof optionsWithStringIndex[key] !== \"undefined\" && optionsWithStringIndex[key] !== null && optionsWithStringIndex[key] !== \"\";\n    }).reduce(function (accumulator, key) {\n      var value = optionsWithStringIndex[key].toString();\n      key = camelCaseToKebabCase(key);\n      if (key.substring(0, 4) === \"data\" || key === \"crossorigin\") {\n        accumulator.attributes[key] = value;\n      } else {\n        accumulator.queryParams[key] = value;\n      }\n      return accumulator;\n    }, {\n      queryParams: {},\n      attributes: {}\n    }),\n    queryParams = _a.queryParams,\n    attributes = _a.attributes;\n  if (queryParams[\"merchant-id\"] && queryParams[\"merchant-id\"].indexOf(\",\") !== -1) {\n    attributes[\"data-merchant-id\"] = queryParams[\"merchant-id\"];\n    queryParams[\"merchant-id\"] = \"*\";\n  }\n  return {\n    url: \"\".concat(sdkBaseUrl, \"?\").concat(objectToQueryString(queryParams)),\n    attributes: attributes\n  };\n}\nfunction camelCaseToKebabCase(str) {\n  var replacer = function (match, indexOfMatch) {\n    return (indexOfMatch ? \"-\" : \"\") + match.toLowerCase();\n  };\n  return str.replace(/[A-Z]+(?![a-z])|[A-Z]/g, replacer);\n}\nfunction objectToQueryString(params) {\n  var queryString = \"\";\n  Object.keys(params).forEach(function (key) {\n    if (queryString.length !== 0) queryString += \"&\";\n    queryString += key + \"=\" + params[key];\n  });\n  return queryString;\n}\nfunction createScriptElement(url, attributes) {\n  if (attributes === void 0) {\n    attributes = {};\n  }\n  var newScript = document.createElement(\"script\");\n  newScript.src = url;\n  Object.keys(attributes).forEach(function (key) {\n    newScript.setAttribute(key, attributes[key]);\n    if (key === \"data-csp-nonce\") {\n      newScript.setAttribute(\"nonce\", attributes[\"data-csp-nonce\"]);\n    }\n  });\n  return newScript;\n}\nfunction loadScript(options, PromisePonyfill) {\n  if (PromisePonyfill === void 0) {\n    PromisePonyfill = Promise;\n  }\n  validateArguments(options, PromisePonyfill);\n  if (typeof document === \"undefined\") return PromisePonyfill.resolve(null);\n  var _a = processOptions(options),\n    url = _a.url,\n    attributes = _a.attributes;\n  var namespace = attributes[\"data-namespace\"] || \"paypal\";\n  var existingWindowNamespace = getPayPalWindowNamespace(namespace);\n  if (!attributes[\"data-js-sdk-library\"]) {\n    attributes[\"data-js-sdk-library\"] = \"paypal-js\";\n  }\n  if (findScript(url, attributes) && existingWindowNamespace) {\n    return PromisePonyfill.resolve(existingWindowNamespace);\n  }\n  return loadCustomScript({\n    url: url,\n    attributes: attributes\n  }, PromisePonyfill).then(function () {\n    var newWindowNamespace = getPayPalWindowNamespace(namespace);\n    if (newWindowNamespace) {\n      return newWindowNamespace;\n    }\n    throw new Error(\"The window.\".concat(namespace, \" global variable is not available.\"));\n  });\n}\nfunction loadCustomScript(options, PromisePonyfill) {\n  if (PromisePonyfill === void 0) {\n    PromisePonyfill = Promise;\n  }\n  validateArguments(options, PromisePonyfill);\n  var url = options.url,\n    attributes = options.attributes;\n  if (typeof url !== \"string\" || url.length === 0) {\n    throw new Error(\"Invalid url.\");\n  }\n  if (typeof attributes !== \"undefined\" && typeof attributes !== \"object\") {\n    throw new Error(\"Expected attributes to be an object.\");\n  }\n  return new PromisePonyfill(function (resolve, reject) {\n    if (typeof document === \"undefined\") return resolve();\n    insertScriptElement({\n      url: url,\n      attributes: attributes,\n      onSuccess: function () {\n        return resolve();\n      },\n      onError: function () {\n        var defaultError = new Error(\"The script \\\"\".concat(url, \"\\\" failed to load. Check the HTTP status code and response body in DevTools to learn more.\"));\n        return reject(defaultError);\n      }\n    });\n  });\n}\nfunction getPayPalWindowNamespace(namespace) {\n  return window[namespace];\n}\nfunction validateArguments(options, PromisePonyfill) {\n  if (typeof options !== \"object\" || options === null) {\n    throw new Error(\"Expected an options object.\");\n  }\n  var environment = options.environment;\n  if (environment && environment !== \"production\" && environment !== \"sandbox\") {\n    throw new Error('The `environment` option must be either \"production\" or \"sandbox\".');\n  }\n  if (typeof PromisePonyfill !== \"undefined\" && typeof PromisePonyfill !== \"function\") {\n    throw new Error(\"Expected PromisePonyfill to be a function.\");\n  }\n}\n\n/**\n * Simple check to determine if the Braintree is a valid namespace.\n *\n * @param braintreeSource the source {@link BraintreeNamespace}\n * @returns a boolean representing if the namespace is valid.\n */\nvar isValidBraintreeNamespace = function (braintreeSource) {\n  var _a, _b;\n  if (typeof ((_a = braintreeSource === null || braintreeSource === void 0 ? void 0 : braintreeSource.client) === null || _a === void 0 ? void 0 : _a.create) !== \"function\" && typeof ((_b = braintreeSource === null || braintreeSource === void 0 ? void 0 : braintreeSource.paypalCheckout) === null || _b === void 0 ? void 0 : _b.create) !== \"function\") {\n    throw new Error(\"The braintreeNamespace property is not a valid BraintreeNamespace type.\");\n  }\n  return true;\n};\n/**\n * Use `actions.braintree` to provide an interface for the paypalCheckoutInstance\n * through the createOrder, createBillingAgreement and onApprove callbacks\n *\n * @param braintreeButtonProps the component button options\n * @returns a new copy of the component button options casted as {@link PayPalButtonsComponentProps}\n */\nvar decorateActions = function (buttonProps, payPalCheckoutInstance) {\n  var createOrderRef = buttonProps.createOrder;\n  var createBillingAgreementRef = buttonProps.createBillingAgreement;\n  var onApproveRef = buttonProps.onApprove;\n  if (typeof createOrderRef === \"function\") {\n    buttonProps.createOrder = function (data, actions) {\n      return createOrderRef(data, __assign(__assign({}, actions), {\n        braintree: payPalCheckoutInstance\n      }));\n    };\n  }\n  if (typeof createBillingAgreementRef === \"function\") {\n    buttonProps.createBillingAgreement = function (data, actions) {\n      return createBillingAgreementRef(data, __assign(__assign({}, actions), {\n        braintree: payPalCheckoutInstance\n      }));\n    };\n  }\n  if (typeof onApproveRef === \"function\") {\n    buttonProps.onApprove = function (data, actions) {\n      return onApproveRef(data, __assign(__assign({}, actions), {\n        braintree: payPalCheckoutInstance\n      }));\n    };\n  }\n  return __assign({}, buttonProps);\n};\n/**\n * Get the Braintree namespace from the component props.\n * If the prop `braintreeNamespace` is undefined will try to load it from the CDN.\n * This function allows users to set the braintree manually on the `BraintreePayPalButtons` component.\n *\n * Use case can be for example legacy sites using AMD/UMD modules,\n * trying to integrate the `BraintreePayPalButtons` component.\n * If we attempt to load the Braintree from the CDN won't define the braintree namespace.\n * This happens because the braintree script is an UMD module.\n * After detecting the AMD on the global scope will create an anonymous module using `define`\n * and the `BraintreePayPalButtons` won't be able to get access to the `window.braintree` namespace\n * from the global context.\n *\n * @param braintreeSource the source {@link BraintreeNamespace}\n * @returns the {@link BraintreeNamespace}\n */\nvar getBraintreeNamespace = function (braintreeSource) {\n  if (braintreeSource && isValidBraintreeNamespace(braintreeSource)) {\n    return Promise.resolve(braintreeSource);\n  }\n  return Promise.all([loadCustomScript({\n    url: BRAINTREE_SOURCE\n  }), loadCustomScript({\n    url: BRAINTREE_PAYPAL_CHECKOUT_SOURCE\n  })]).then(function () {\n    return getBraintreeWindowNamespace();\n  });\n};\n\n/**\nThis `<BraintreePayPalButtons />` component renders the [Braintree PayPal Buttons](https://developer.paypal.com/braintree/docs/guides/paypal/overview) for Braintree Merchants.\nIt relies on the `<PayPalScriptProvider />` parent component for managing state related to loading the JS SDK script.\n\nNote: You are able to make your integration using the client token or using the tokenization key.\n\n- To use the client token integration set the key `dataClientToken` in the `PayPayScriptProvider` component's options.\n- To use the tokenization key integration set the key `dataUserIdToken` in the `PayPayScriptProvider` component's options.\n*/\nvar BraintreePayPalButtons = function (_a) {\n  var _b = _a.className,\n    className = _b === void 0 ? \"\" : _b,\n    _c = _a.disabled,\n    disabled = _c === void 0 ? false : _c,\n    children = _a.children,\n    _d = _a.forceReRender,\n    forceReRender = _d === void 0 ? [] : _d,\n    braintreeNamespace = _a.braintreeNamespace,\n    merchantAccountId = _a.merchantAccountId,\n    buttonProps = __rest(_a, [\"className\", \"disabled\", \"children\", \"forceReRender\", \"braintreeNamespace\", \"merchantAccountId\"]);\n  var _e = useState(null),\n    setErrorState = _e[1];\n  var _f = useScriptProviderContext(),\n    providerContext = _f[0],\n    dispatch = _f[1];\n  useEffect(function () {\n    getBraintreeNamespace(braintreeNamespace).then(function (braintree) {\n      var clientTokenizationKey = providerContext.options[SDK_SETTINGS.DATA_USER_ID_TOKEN];\n      var clientToken = providerContext.options[SDK_SETTINGS.DATA_CLIENT_TOKEN];\n      return braintree.client.create({\n        authorization: clientTokenizationKey || clientToken\n      }).then(function (clientInstance) {\n        var merchantProp = merchantAccountId ? {\n          merchantAccountId: merchantAccountId\n        } : {};\n        return braintree.paypalCheckout.create(__assign(__assign({}, merchantProp), {\n          client: clientInstance\n        }));\n      }).then(function (paypalCheckoutInstance) {\n        dispatch({\n          type: DISPATCH_ACTION.SET_BRAINTREE_INSTANCE,\n          value: paypalCheckoutInstance\n        });\n      });\n    }).catch(function (err) {\n      setErrorState(function () {\n        throw new Error(\"\".concat(LOAD_SCRIPT_ERROR, \" \").concat(err));\n      });\n    });\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [providerContext.options]);\n  return React.createElement(React.Fragment, null, providerContext.braintreePayPalCheckoutInstance && React.createElement(PayPalButtons, __assign({\n    className: className,\n    disabled: disabled,\n    forceReRender: forceReRender\n  }, decorateActions(buttonProps, providerContext.braintreePayPalCheckoutInstance)), children));\n};\n\n/**\nThe `<PayPalMarks />` component is used for conditionally rendering different payment options using radio buttons.\nThe [Display PayPal Buttons with other Payment Methods guide](https://developer.paypal.com/docs/business/checkout/add-capabilities/buyer-experience/#display-paypal-buttons-with-other-payment-methods) describes this style of integration in detail.\nIt relies on the `<PayPalScriptProvider />` parent component for managing state related to loading the JS SDK script.\n\nThis component can also be configured to use a single funding source similar to the [standalone buttons](https://developer.paypal.com/docs/business/checkout/configure-payments/standalone-buttons/) approach.\nA `FUNDING` object is exported by this library which has a key for every available funding source option.\n*/\nvar PayPalMarks = function (_a) {\n  var _b = _a.className,\n    className = _b === void 0 ? \"\" : _b,\n    children = _a.children,\n    markProps = __rest(_a, [\"className\", \"children\"]);\n  var _c = usePayPalScriptReducer()[0],\n    isResolved = _c.isResolved,\n    options = _c.options;\n  var markContainerRef = useRef(null);\n  var _d = useState(true),\n    isEligible = _d[0],\n    setIsEligible = _d[1];\n  var _e = useState(null),\n    setErrorState = _e[1];\n  /**\n   * Render PayPal Mark into the DOM\n   */\n  var renderPayPalMark = function (mark) {\n    var current = markContainerRef.current;\n    // only render the mark when eligible\n    if (!current || !mark.isEligible()) {\n      return setIsEligible(false);\n    }\n    // Remove any children before render it again\n    if (current.firstChild) {\n      current.removeChild(current.firstChild);\n    }\n    mark.render(current).catch(function (err) {\n      // component failed to render, possibly because it was closed or destroyed.\n      if (current === null || current.children.length === 0) {\n        // paypal marks container is no longer in the DOM, we can safely ignore the error\n        return;\n      }\n      // paypal marks container is still in the DOM\n      setErrorState(function () {\n        throw new Error(\"Failed to render <PayPalMarks /> component. \".concat(err));\n      });\n    });\n  };\n  useEffect(function () {\n    // verify the sdk script has successfully loaded\n    if (isResolved === false) {\n      return;\n    }\n    var paypalWindowNamespace = getPayPalWindowNamespace$1(options[SDK_SETTINGS.DATA_NAMESPACE]);\n    // verify dependency on window object\n    if (paypalWindowNamespace === undefined || paypalWindowNamespace.Marks === undefined) {\n      return setErrorState(function () {\n        throw new Error(generateErrorMessage({\n          reactComponentName: PayPalMarks.displayName,\n          sdkComponentKey: \"marks\",\n          sdkRequestedComponents: options.components,\n          sdkDataNamespace: options[SDK_SETTINGS.DATA_NAMESPACE]\n        }));\n      });\n    }\n    renderPayPalMark(paypalWindowNamespace.Marks(__assign({}, markProps)));\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [isResolved, markProps.fundingSource]);\n  return React.createElement(React.Fragment, null, isEligible ? React.createElement(\"div\", {\n    ref: markContainerRef,\n    className: className\n  }) : children);\n};\nPayPalMarks.displayName = \"PayPalMarks\";\n\n/**\nThis `<PayPalMessages />` messages component renders a credit messaging on upstream merchant sites.\nIt relies on the `<PayPalScriptProvider />` parent component for managing state related to loading the JS SDK script.\n*/\nvar PayPalMessages = function (_a) {\n  var _b = _a.className,\n    className = _b === void 0 ? \"\" : _b,\n    _c = _a.forceReRender,\n    forceReRender = _c === void 0 ? [] : _c,\n    messageProps = __rest(_a, [\"className\", \"forceReRender\"]);\n  var _d = usePayPalScriptReducer()[0],\n    isResolved = _d.isResolved,\n    options = _d.options;\n  var messagesContainerRef = useRef(null);\n  var messages = useRef(null);\n  var _e = useState(null),\n    setErrorState = _e[1];\n  useEffect(function () {\n    // verify the sdk script has successfully loaded\n    if (isResolved === false) {\n      return;\n    }\n    var paypalWindowNamespace = getPayPalWindowNamespace$1(options[SDK_SETTINGS.DATA_NAMESPACE]);\n    // verify dependency on window object\n    if (paypalWindowNamespace === undefined || paypalWindowNamespace.Messages === undefined) {\n      return setErrorState(function () {\n        throw new Error(generateErrorMessage({\n          reactComponentName: PayPalMessages.displayName,\n          sdkComponentKey: \"messages\",\n          sdkRequestedComponents: options.components,\n          sdkDataNamespace: options[SDK_SETTINGS.DATA_NAMESPACE]\n        }));\n      });\n    }\n    messages.current = paypalWindowNamespace.Messages(__assign({}, messageProps));\n    messages.current.render(messagesContainerRef.current).catch(function (err) {\n      // component failed to render, possibly because it was closed or destroyed.\n      if (messagesContainerRef.current === null || messagesContainerRef.current.children.length === 0) {\n        // paypal messages container is no longer in the DOM, we can safely ignore the error\n        return;\n      }\n      // paypal messages container is still in the DOM\n      setErrorState(function () {\n        throw new Error(\"Failed to render <PayPalMessages /> component. \".concat(err));\n      });\n    });\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, __spreadArray([isResolved], forceReRender, true));\n  return React.createElement(\"div\", {\n    ref: messagesContainerRef,\n    className: className\n  });\n};\nPayPalMessages.displayName = \"PayPalMessages\";\n\n/**\nThis `<PayPalScriptProvider />` component takes care of loading the JS SDK `<script>`.\nIt manages state for script loading so children components like `<PayPalButtons />` know when it's safe to use the `window.paypal` global namespace.\n\nNote: You always should use this component as a wrapper for  `PayPalButtons`, `PayPalMarks`, `PayPalMessages` and `BraintreePayPalButtons` components.\n */\nvar PayPalScriptProvider = function (_a) {\n  var _b;\n  var _c = _a.options,\n    options = _c === void 0 ? {\n      clientId: \"test\"\n    } : _c,\n    children = _a.children,\n    _d = _a.deferLoading,\n    deferLoading = _d === void 0 ? false : _d;\n  var _e = useReducer(scriptReducer, {\n      options: __assign(__assign({}, options), (_b = {}, _b[SDK_SETTINGS.DATA_JS_SDK_LIBRARY] = SDK_SETTINGS.DATA_LIBRARY_VALUE, _b[SDK_SETTINGS.DATA_SDK_INTEGRATION_SOURCE] = SDK_SETTINGS.DATA_LIBRARY_VALUE, _b[SCRIPT_ID] = \"\".concat(getScriptID(options)), _b)),\n      loadingStatus: deferLoading ? SCRIPT_LOADING_STATE.INITIAL : SCRIPT_LOADING_STATE.PENDING\n    }),\n    state = _e[0],\n    dispatch = _e[1];\n  useEffect(function () {\n    if (deferLoading === false && state.loadingStatus === SCRIPT_LOADING_STATE.INITIAL) {\n      return dispatch({\n        type: DISPATCH_ACTION.LOADING_STATUS,\n        value: SCRIPT_LOADING_STATE.PENDING\n      });\n    }\n    if (state.loadingStatus !== SCRIPT_LOADING_STATE.PENDING) {\n      return;\n    }\n    var isSubscribed = true;\n    loadScript(state.options).then(function () {\n      if (isSubscribed) {\n        dispatch({\n          type: DISPATCH_ACTION.LOADING_STATUS,\n          value: SCRIPT_LOADING_STATE.RESOLVED\n        });\n      }\n    }).catch(function (err) {\n      console.error(\"\".concat(LOAD_SCRIPT_ERROR, \" \").concat(err));\n      if (isSubscribed) {\n        dispatch({\n          type: DISPATCH_ACTION.LOADING_STATUS,\n          value: {\n            state: SCRIPT_LOADING_STATE.REJECTED,\n            message: String(err)\n          }\n        });\n      }\n    });\n    return function () {\n      isSubscribed = false;\n    };\n  }, [state.options, deferLoading, state.loadingStatus]);\n  return React.createElement(ScriptContext.Provider, {\n    value: __assign(__assign({}, state), {\n      dispatch: dispatch\n    })\n  }, children);\n};\n\n/**\n * Custom hook to store registered hosted fields children\n * Each `PayPalHostedField` component should be registered on the parent provider\n *\n * @param initialValue the initially registered components\n * @returns at first, an {@link Object} containing the registered hosted fields,\n * and at the second a function handler to register the hosted fields components\n */\nvar useHostedFieldsRegister = function (initialValue) {\n  if (initialValue === void 0) {\n    initialValue = {};\n  }\n  var registeredFields = useRef(initialValue);\n  var registerHostedField = function (component) {\n    registeredFields.current = __assign(__assign({}, registeredFields.current), component);\n  };\n  return [registeredFields, registerHostedField];\n};\n\n/**\n * Throw an exception if the HostedFields is not found in the paypal namespace\n * Probably cause for this problem is not sending the hosted-fields string\n * as part of the components props in options\n * {@code <PayPalScriptProvider options={{ components: 'hosted-fields'}}>}\n *\n * @param param0 and object containing the components and namespace defined in options\n * @throws {@code Error}\n *\n */\nvar generateMissingHostedFieldsError = function (_a) {\n  var _b = _a.components,\n    components = _b === void 0 ? \"\" : _b,\n    _c = SDK_SETTINGS.DATA_NAMESPACE,\n    _d = _a[_c],\n    dataNamespace = _d === void 0 ? DEFAULT_PAYPAL_NAMESPACE : _d;\n  var expectedComponents = components ? \"\".concat(components, \",hosted-fields\") : \"hosted-fields\";\n  var errorMessage = \"Unable to render <PayPalHostedFieldsProvider /> because window.\".concat(dataNamespace, \".HostedFields is undefined.\");\n  if (!components.includes(\"hosted-fields\")) {\n    errorMessage += \"\\nTo fix the issue, add 'hosted-fields' to the list of components passed to the parent PayPalScriptProvider: <PayPalScriptProvider options={{ components: '\".concat(expectedComponents, \"'}}>\");\n  }\n  return errorMessage;\n};\n/**\n * Validate the expiration date component. Valid combinations are:\n * 1- Only the `expirationDate` field exists.\n * 2- Only the `expirationMonth` and `expirationYear` fields exist. Cannot be used with the `expirationDate` field.\n *\n * @param registerTypes\n * @returns @type {true} when the children are valid\n */\nvar validateExpirationDate = function (registerTypes) {\n  return !registerTypes.includes(PAYPAL_HOSTED_FIELDS_TYPES.EXPIRATION_DATE) && !registerTypes.includes(PAYPAL_HOSTED_FIELDS_TYPES.EXPIRATION_MONTH) && !registerTypes.includes(PAYPAL_HOSTED_FIELDS_TYPES.EXPIRATION_YEAR);\n};\n/**\n * Check if we find the [number, expiration, cvv] in children\n *\n * @param requiredChildren the list with required children [number, expiration, cvv]\n * @param registerTypes    the list of all the children types pass to the parent\n * @throw an @type {Error} when not find the default children\n */\nvar hasDefaultChildren = function (registerTypes) {\n  if (!registerTypes.includes(PAYPAL_HOSTED_FIELDS_TYPES.NUMBER) || !registerTypes.includes(PAYPAL_HOSTED_FIELDS_TYPES.CVV) || validateExpirationDate(registerTypes)) {\n    throw new Error(HOSTED_FIELDS_CHILDREN_ERROR);\n  }\n};\n/**\n * Check if we don't have duplicate children types\n *\n * @param registerTypes the list of all the children types pass to the parent\n * @throw an @type {Error} when duplicate types was found\n */\nvar noDuplicateChildren = function (registerTypes) {\n  if (registerTypes.length !== new Set(registerTypes).size) {\n    throw new Error(HOSTED_FIELDS_DUPLICATE_CHILDREN_ERROR);\n  }\n};\n/**\n * Validate the hosted field children in the PayPalHostedFieldsProvider component.\n * These are the rules:\n * 1- We need to find 3 default children for number, expiration, cvv\n * 2- No duplicate children are allowed\n * 3- No invalid combinations of `expirationDate`, `expirationMonth`, and `expirationYear`\n *\n * @param childrenList     the list of children\n * @param requiredChildren the list with required children [number, expiration, cvv]\n */\nvar validateHostedFieldChildren = function (registeredFields) {\n  hasDefaultChildren(registeredFields);\n  noDuplicateChildren(registeredFields);\n};\n\n/**\nThis `<PayPalHostedFieldsProvider />` provider component wraps the form field elements and accepts props like `createOrder()`.\n\nThis provider component is designed to be used with the `<PayPalHostedField />` component.\n\nWarning: If you don't see anything in the screen probably your client is ineligible.\nTo handle this problem make sure to use the prop `notEligibleError` and pass a component with a custom message.\nTake a look to this link if that is the case: https://developer.paypal.com/docs/checkout/advanced/integrate/\n*/\nvar PayPalHostedFieldsProvider = function (_a) {\n  var styles = _a.styles,\n    createOrder = _a.createOrder,\n    notEligibleError = _a.notEligibleError,\n    children = _a.children,\n    installments = _a.installments;\n  var _b = useScriptProviderContext()[0],\n    options = _b.options,\n    loadingStatus = _b.loadingStatus;\n  var _c = useState(true),\n    isEligible = _c[0],\n    setIsEligible = _c[1];\n  var _d = useState(),\n    cardFields = _d[0],\n    setCardFields = _d[1];\n  var _e = useState(null),\n    setErrorState = _e[1];\n  var hostedFieldsContainerRef = useRef(null);\n  var hostedFields = useRef();\n  var _f = useHostedFieldsRegister(),\n    registeredFields = _f[0],\n    registerHostedField = _f[1];\n  useEffect(function () {\n    var _a;\n    validateHostedFieldChildren(Object.keys(registeredFields.current));\n    // Only render the hosted fields when script is loaded and hostedFields is eligible\n    if (!(loadingStatus === SCRIPT_LOADING_STATE.RESOLVED)) {\n      return;\n    }\n    // Get the hosted fields from the [window.paypal.HostedFields] SDK\n    hostedFields.current = getPayPalWindowNamespace$1(options[SDK_SETTINGS.DATA_NAMESPACE]).HostedFields;\n    if (!hostedFields.current) {\n      throw new Error(generateMissingHostedFieldsError((_a = {\n        components: options.components\n      }, _a[SDK_SETTINGS.DATA_NAMESPACE] = options[SDK_SETTINGS.DATA_NAMESPACE], _a)));\n    }\n    if (!hostedFields.current.isEligible()) {\n      return setIsEligible(false);\n    }\n    // Clean all the fields before the rerender\n    if (cardFields) {\n      cardFields.teardown();\n    }\n    hostedFields.current.render({\n      // Call your server to set up the transaction\n      createOrder: createOrder,\n      fields: registeredFields.current,\n      installments: installments,\n      styles: styles\n    }).then(function (cardFieldsInstance) {\n      if (hostedFieldsContainerRef.current) {\n        setCardFields(cardFieldsInstance);\n      }\n    }).catch(function (err) {\n      setErrorState(function () {\n        throw new Error(\"Failed to render <PayPalHostedFieldsProvider /> component. \".concat(err));\n      });\n    });\n  }, [loadingStatus, styles]); // eslint-disable-line react-hooks/exhaustive-deps\n  return React.createElement(\"div\", {\n    ref: hostedFieldsContainerRef\n  }, isEligible ? React.createElement(PayPalHostedFieldsContext.Provider, {\n    value: {\n      cardFields: cardFields,\n      registerHostedField: registerHostedField\n    }\n  }, children) : notEligibleError);\n};\n\n/**\nThis `<PayPalHostedField />` component renders individual fields for [Hosted Fields](https://developer.paypal.com/docs/business/checkout/advanced-card-payments/integrate#3-add-javascript-sdk-and-card-form) integrations.\nIt relies on the `<PayPalHostedFieldsProvider />` parent component for managing state related to loading the JS SDK script\nand execute some validations before the rendering the fields.\n\nTo use the PayPal hosted fields you need to define at least three fields:\n\n- A card number field\n- The CVV code from the client card\n- The expiration date\n\nYou can define the expiration date as a single field similar to the example below,\nor you are able to define it in [two separate fields](https://paypal.github.io/react-paypal-js//?path=/docs/paypal-paypalhostedfields--expiration-date). One for the month and second for year.\n\nNote: Take care when using multiple instances of the PayPal Hosted Fields on the same page.\nThe component will fail to render when any of the selectors return more than one element.\n*/\nvar PayPalHostedField = function (_a) {\n  var hostedFieldType = _a.hostedFieldType,\n    // eslint-disable-line @typescript-eslint/no-unused-vars\n    options = _a.options,\n    // eslint-disable-line @typescript-eslint/no-unused-vars\n    props = __rest(_a, [\"hostedFieldType\", \"options\"]);\n  var hostedFieldContext = useContext(PayPalHostedFieldsContext);\n  useEffect(function () {\n    var _a;\n    if (!(hostedFieldContext === null || hostedFieldContext === void 0 ? void 0 : hostedFieldContext.registerHostedField)) {\n      throw new Error(\"The HostedField cannot be register in the PayPalHostedFieldsProvider parent component\");\n    }\n    // Register in the parent provider\n    hostedFieldContext.registerHostedField((_a = {}, _a[hostedFieldType] = {\n      selector: options.selector,\n      placeholder: options.placeholder,\n      type: options.type,\n      formatInput: options.formatInput,\n      maskInput: options.maskInput,\n      select: options.select,\n      maxlength: options.maxlength,\n      minlength: options.minlength,\n      prefill: options.prefill,\n      rejectUnsupportedCards: options.rejectUnsupportedCards\n    }, _a));\n  }, []); // eslint-disable-line react-hooks/exhaustive-deps\n  return React.createElement(\"div\", __assign({}, props));\n};\n\n/**\n * Throw an exception if the CardFields is not found in the paypal namespace\n * Probably cause for this problem is not sending the card-fields string\n * as part of the components props in options\n * {@code <PayPalScriptProvider options={{ components: 'card-fields'}}>}\n *\n * @param param0 and object containing the components and namespace defined in options\n * @throws {@code Error}\n *\n */\nvar generateMissingCardFieldsError = function (_a) {\n  var _b = _a.components,\n    components = _b === void 0 ? \"\" : _b,\n    _c = SDK_SETTINGS.DATA_NAMESPACE,\n    _d = _a[_c],\n    dataNamespace = _d === void 0 ? DEFAULT_PAYPAL_NAMESPACE : _d;\n  var expectedComponents = components ? \"\".concat(components, \",card-fields\") : \"card-fields\";\n  var errorMessage = \"Unable to render <PayPalCardFieldsProvider /> because window.\".concat(dataNamespace, \".CardFields is undefined.\");\n  if (!components.includes(\"card-fields\")) {\n    errorMessage += \"\\nTo fix the issue, add 'card-fields' to the list of components passed to the parent PayPalScriptProvider: <PayPalScriptProvider options={{ components: '\".concat(expectedComponents, \"'}}>\");\n  }\n  return errorMessage;\n};\nfunction ignore() {\n  return;\n}\nfunction hasChildren(container) {\n  var _a;\n  return !!((_a = container.current) === null || _a === void 0 ? void 0 : _a.children.length);\n}\nvar PayPalCardFieldsContext = createContext({\n  cardFieldsForm: null,\n  fields: {},\n  registerField: ignore,\n  unregisterField: ignore // implementation is inside hook and passed through the provider\n});\nvar usePayPalCardFields = function () {\n  return useContext(PayPalCardFieldsContext);\n};\nvar usePayPalCardFieldsRegistry = function () {\n  var _a = useState(null),\n    setError = _a[1];\n  var registeredFields = useRef({});\n  var registerField = function () {\n    var props = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      props[_i] = arguments[_i];\n    }\n    var fieldName = props[0],\n      options = props[1],\n      cardFields = props[2];\n    if (registeredFields.current[fieldName]) {\n      setError(function () {\n        throw new Error(CARD_FIELDS_DUPLICATE_CHILDREN_ERROR);\n      });\n    }\n    registeredFields.current[fieldName] = cardFields === null || cardFields === void 0 ? void 0 : cardFields[fieldName](options);\n    return registeredFields.current[fieldName];\n  };\n  var unregisterField = function (fieldName) {\n    var field = registeredFields.current[fieldName];\n    if (field) {\n      field.close().catch(ignore);\n      delete registeredFields.current[fieldName];\n    }\n  };\n  return {\n    fields: registeredFields.current,\n    registerField: registerField,\n    unregisterField: unregisterField\n  };\n};\nvar FullWidthContainer = function (_a) {\n  var children = _a.children;\n  return React.createElement(\"div\", {\n    style: {\n      width: \"100%\"\n    }\n  }, children);\n};\n\n/**\nThe `<PayPalCardFieldsProvider />` is a context provider that is designed to support the rendering and state management of PayPal CardFields in your application.\n\nThe context provider will initialize the `CardFields` instance from the JS SDK and determine eligibility to render the CardField components. Once the `CardFields` are initialized, the context provider will manage the state of the `CardFields` instance as well as the reference to each individual card field.\n\nPassing the `inputEvents` and `style` props to the context provider will apply them to each of the individual field components.\n\nThe state managed by the provider is accessible through our custom hook `usePayPalCardFields`.\n\n*/\nvar PayPalCardFieldsProvider = function (_a) {\n  var children = _a.children,\n    props = __rest(_a, [\"children\"]);\n  var _b = usePayPalScriptReducer()[0],\n    isResolved = _b.isResolved,\n    options = _b.options;\n  var _c = usePayPalCardFieldsRegistry(),\n    fields = _c.fields,\n    registerField = _c.registerField,\n    unregisterField = _c.unregisterField;\n  var _d = useState(null),\n    cardFieldsForm = _d[0],\n    setCardFieldsForm = _d[1];\n  var cardFieldsInstance = useRef(null);\n  var _e = useState(false),\n    isEligible = _e[0],\n    setIsEligible = _e[1];\n  // We set the error inside state so that it can be caught by React's error boundary\n  var _f = useState(null),\n    setError = _f[1];\n  useEffect(function () {\n    var _a, _b, _c;\n    if (!isResolved) {\n      return;\n    }\n    try {\n      cardFieldsInstance.current = (_c = (_b = (_a = getPayPalWindowNamespace$1(options[SDK_SETTINGS.DATA_NAMESPACE])).CardFields) === null || _b === void 0 ? void 0 : _b.call(_a, __assign({}, props))) !== null && _c !== void 0 ? _c : null;\n    } catch (error) {\n      setError(function () {\n        throw new Error(\"Failed to render <PayPalCardFieldsProvider /> component. Failed to initialize:  \".concat(error));\n      });\n      return;\n    }\n    if (!cardFieldsInstance.current) {\n      setError(function () {\n        var _a;\n        throw new Error(generateMissingCardFieldsError((_a = {\n          components: options.components\n        }, _a[SDK_SETTINGS.DATA_NAMESPACE] = options[SDK_SETTINGS.DATA_NAMESPACE], _a)));\n      });\n      return;\n    }\n    setIsEligible(cardFieldsInstance.current.isEligible());\n    setCardFieldsForm(cardFieldsInstance.current);\n    return function () {\n      setCardFieldsForm(null);\n      cardFieldsInstance.current = null;\n    };\n  }, [isResolved]); // eslint-disable-line react-hooks/exhaustive-deps\n  if (!isEligible) {\n    // TODO: What should be returned here?\n    return React.createElement(\"div\", null);\n  }\n  return React.createElement(FullWidthContainer, null, React.createElement(PayPalCardFieldsContext.Provider, {\n    value: {\n      cardFieldsForm: cardFieldsForm,\n      fields: fields,\n      registerField: registerField,\n      unregisterField: unregisterField\n    }\n  }, children));\n};\nvar PayPalCardField = function (_a) {\n  var className = _a.className,\n    fieldName = _a.fieldName,\n    options = __rest(_a, [\"className\", \"fieldName\"]);\n  var _b = usePayPalCardFields(),\n    cardFieldsForm = _b.cardFieldsForm,\n    registerField = _b.registerField,\n    unregisterField = _b.unregisterField;\n  var containerRef = useRef(null);\n  // Set errors is state so that they can be caught by React's error boundary\n  var _c = useState(null),\n    setError = _c[1];\n  function closeComponent() {\n    unregisterField(fieldName);\n  }\n  useEffect(function () {\n    if (!cardFieldsForm) {\n      setError(function () {\n        throw new Error(CARD_FIELDS_CONTEXT_ERROR);\n      });\n      return closeComponent;\n    }\n    if (!containerRef.current) {\n      return closeComponent;\n    }\n    var registeredField = registerField(fieldName, options, cardFieldsForm);\n    registeredField === null || registeredField === void 0 ? void 0 : registeredField.render(containerRef.current).catch(function (err) {\n      if (!hasChildren(containerRef)) {\n        // Component no longer in the DOM, we can safely ignore the error\n        return;\n      }\n      // Component is still in the DOM\n      setError(function () {\n        throw new Error(\"Failed to render <PayPal\".concat(fieldName, \" /> component. \").concat(err));\n      });\n    });\n    return closeComponent;\n  }, []); // eslint-disable-line react-hooks/exhaustive-deps\n  return React.createElement(\"div\", {\n    ref: containerRef,\n    className: className\n  });\n};\nvar PayPalNameField = function (options) {\n  return React.createElement(PayPalCardField, __assign({\n    fieldName: \"NameField\"\n  }, options));\n};\nvar PayPalNumberField = function (options) {\n  return React.createElement(PayPalCardField, __assign({\n    fieldName: \"NumberField\"\n  }, options));\n};\nvar PayPalExpiryField = function (options) {\n  return React.createElement(PayPalCardField, __assign({\n    fieldName: \"ExpiryField\"\n  }, options));\n};\nvar PayPalCVVField = function (options) {\n  return React.createElement(PayPalCardField, __assign({\n    fieldName: \"CVVField\"\n  }, options));\n};\nvar FlexContainer = function (_a) {\n  var children = _a.children;\n  return React.createElement(\"div\", {\n    style: {\n      display: \"flex\",\n      width: \"100%\"\n    }\n  }, children);\n};\n\n/**\nThis `<PayPalCardFieldsForm />` component renders the 4 individual fields for [Card Fields](https://developer.paypal.com/docs/business/checkout/advanced-card-payments/integrate#3-add-javascript-sdk-and-card-form) integrations.\nThis setup relies on the `<PayPalCardFieldsProvider />` parent component, which manages the state related to loading the JS SDK script and performs certain validations before rendering the fields.\n\n\n\nNote: If you want to have more granular control over the layout of how the fields are rendered, you can alternatively use our Individual Fields.\n*/\nvar PayPalCardFieldsForm = function (_a) {\n  var className = _a.className;\n  return React.createElement(\"div\", {\n    className: className\n  }, React.createElement(PayPalCardField, {\n    fieldName: \"NameField\"\n  }), React.createElement(PayPalCardField, {\n    fieldName: \"NumberField\"\n  }), React.createElement(FlexContainer, null, React.createElement(FullWidthContainer, null, React.createElement(PayPalCardField, {\n    fieldName: \"ExpiryField\"\n  })), React.createElement(FullWidthContainer, null, React.createElement(PayPalCardField, {\n    fieldName: \"CVVField\"\n  }))));\n};\nvar FUNDING$1 = {\n  PAYPAL: \"paypal\",\n  VENMO: \"venmo\",\n  APPLEPAY: \"applepay\",\n  ITAU: \"itau\",\n  CREDIT: \"credit\",\n  PAYLATER: \"paylater\",\n  CARD: \"card\",\n  IDEAL: \"ideal\",\n  SEPA: \"sepa\",\n  BANCONTACT: \"bancontact\",\n  GIROPAY: \"giropay\",\n  SOFORT: \"sofort\",\n  EPS: \"eps\",\n  MYBANK: \"mybank\",\n  P24: \"p24\",\n  PAYU: \"payu\",\n  BLIK: \"blik\",\n  TRUSTLY: \"trustly\",\n  OXXO: \"oxxo\",\n  BOLETO: \"boleto\",\n  BOLETOBANCARIO: \"boletobancario\",\n  WECHATPAY: \"wechatpay\",\n  MERCADOPAGO: \"mercadopago\",\n  MULTIBANCO: \"multibanco\",\n  SATISPAY: \"satispay\",\n  PAIDY: \"paidy\",\n  ZIMPLER: \"zimpler\",\n  MAXIMA: \"maxima\"\n};\n[FUNDING$1.IDEAL, FUNDING$1.BANCONTACT, FUNDING$1.GIROPAY, FUNDING$1.SOFORT, FUNDING$1.EPS, FUNDING$1.MYBANK, FUNDING$1.P24, FUNDING$1.PAYU, FUNDING$1.BLIK, FUNDING$1.TRUSTLY, FUNDING$1.OXXO, FUNDING$1.BOLETO, FUNDING$1.BOLETOBANCARIO, FUNDING$1.WECHATPAY, FUNDING$1.MERCADOPAGO, FUNDING$1.MULTIBANCO, FUNDING$1.SATISPAY, FUNDING$1.PAIDY, FUNDING$1.MAXIMA, FUNDING$1.ZIMPLER];\n\n// We do not re-export `FUNDING` from the `sdk-constants` module\n// directly because it has no type definitions.\n//\n// See https://github.com/paypal/react-paypal-js/issues/125\nvar FUNDING = FUNDING$1;\nexport { BraintreePayPalButtons, DISPATCH_ACTION, FUNDING, PAYPAL_HOSTED_FIELDS_TYPES, PayPalButtons, PayPalCVVField, PayPalCardFieldsContext, PayPalCardFieldsForm, PayPalCardFieldsProvider, PayPalExpiryField, PayPalHostedField, PayPalHostedFieldsProvider, PayPalMarks, PayPalMessages, PayPalNameField, PayPalNumberField, PayPalScriptProvider, SCRIPT_LOADING_STATE, ScriptContext, destroySDKScript, getScriptID, scriptReducer, usePayPalCardFields, usePayPalHostedFields, usePayPalScriptReducer, useScriptProviderContext };\n", "import { useState, useEffect } from '@wordpress/element';\n\nexport const useScriptParams = ( requestConfig ) => {\n\tconst [ data, setData ] = useState( null );\n\n\tuseEffect( () => {\n\t\t( async () => {\n\t\t\ttry {\n\t\t\t\tconst response = await fetch( requestConfig.endpoint );\n\t\t\t\tconst json = await response.json();\n\t\t\t\tif ( json.success && json?.data?.url_params ) {\n\t\t\t\t\tsetData( json.data );\n\t\t\t\t} else {\n\t\t\t\t\tsetData( false );\n\t\t\t\t}\n\t\t\t} catch ( e ) {\n\t\t\t\tconsole.error( e );\n\t\t\t\tsetData( false );\n\t\t\t}\n\t\t} )();\n\t}, [ requestConfig ] );\n\n\treturn data;\n};\n", "/**\n * External dependencies\n */\nimport { registerBlockType } from '@wordpress/blocks';\n\n/**\n * Internal dependencies\n */\nimport Edit from './edit';\nimport metadata from './block.json';\n\nconst paypalIcon = (\n\t<svg width=\"584.798\" height=\"720\" viewBox=\"0 0 154.728 190.5\">\n\t\t<g transform=\"translate(898.192 276.071)\">\n\t\t\t<path\n\t\t\t\tclipPath=\"none\"\n\t\t\t\td=\"M-837.663-237.968a5.49 5.49 0 0 0-5.423 4.633l-9.013 57.15-8.281 52.514-.005.044.01-.044 8.281-52.514c.421-2.669 2.719-4.633 5.42-4.633h26.404c26.573 0 49.127-19.387 53.246-45.658.314-1.996.482-3.973.52-5.924v-.003h-.003c-6.753-3.543-14.683-5.565-23.372-5.565z\"\n\t\t\t\tfill=\"#001c64\"\n\t\t\t/>\n\t\t\t<path\n\t\t\t\tclipPath=\"none\"\n\t\t\t\td=\"M-766.506-232.402c-.037 1.951-.207 3.93-.52 5.926-4.119 26.271-26.673 45.658-53.246 45.658h-26.404c-2.701 0-4.999 1.964-5.42 4.633l-8.281 52.514-5.197 32.947a4.46 4.46 0 0 0 4.405 5.153h28.66a5.49 5.49 0 0 0 5.423-4.633l7.55-47.881c.423-2.669 2.722-4.636 5.423-4.636h16.876c26.573 0 49.124-19.386 53.243-45.655 2.924-18.649-6.46-35.614-22.511-44.026z\"\n\t\t\t\tfill=\"#0070e0\"\n\t\t\t/>\n\t\t\t<path\n\t\t\t\tclipPath=\"none\"\n\t\t\t\td=\"M-870.225-276.071a5.49 5.49 0 0 0-5.423 4.636l-22.489 142.608a4.46 4.46 0 0 0 4.405 5.156h33.351l8.281-52.514 9.013-57.15a5.49 5.49 0 0 1 5.423-4.633h47.782c8.691 0 16.621 2.025 23.375 5.563.46-23.917-19.275-43.666-46.412-43.666z\"\n\t\t\t\tfill=\"#003087\"\n\t\t\t/>\n\t\t</g>\n\t</svg>\n);\n\nregisterBlockType( metadata, {\n\ticon: paypalIcon,\n\tedit: Edit,\n\tsave() {\n\t\treturn null;\n\t},\n} );\n", "import { __ } from '@wordpress/i18n';\nimport { useState, useEffect } from '@wordpress/element';\nimport { InspectorControls, useBlockProps } from '@wordpress/block-editor';\nimport { PanelBody, Spinner } from '@wordpress/components';\nimport { PayPalScriptProvider, PayPalMessages } from '@paypal/react-paypal-js';\nimport { useScriptParams } from '../../../../ppcp-paylater-block/resources/js/hooks/script-params';\n\nexport default function Edit( { attributes, clientId, setAttributes } ) {\n\tconst { ppcpId } = attributes;\n\n\tconst [ loaded, setLoaded ] = useState( false );\n\n\tlet amount;\n\tconst postContent = String(\n\t\twp.data.select( 'core/editor' )?.getEditedPostContent()\n\t);\n\tif (\n\t\tpostContent.includes( 'woocommerce/checkout' ) ||\n\t\tpostContent.includes( 'woocommerce/cart' )\n\t) {\n\t\tamount = 50.0;\n\t}\n\n\tconst checkoutConfig = PcpCheckoutPayLaterBlock.config.checkout;\n\n\t// Dynamically setting previewStyle based on the layout attribute\n\tlet previewStyle = {};\n\tif ( checkoutConfig.layout === 'flex' ) {\n\t\tpreviewStyle = {\n\t\t\tlayout: checkoutConfig.layout,\n\t\t\tcolor: checkoutConfig.color,\n\t\t\tratio: checkoutConfig.ratio,\n\t\t};\n\t} else {\n\t\tpreviewStyle = {\n\t\t\tlayout: checkoutConfig.layout,\n\t\t\tlogo: {\n\t\t\t\tposition: checkoutConfig[ 'logo-position' ],\n\t\t\t\ttype: checkoutConfig[ 'logo-type' ],\n\t\t\t},\n\t\t\ttext: {\n\t\t\t\tcolor: checkoutConfig[ 'text-color' ],\n\t\t\t\tsize: checkoutConfig[ 'text-size' ],\n\t\t\t},\n\t\t};\n\t}\n\n\tlet classes = [ 'ppcp-paylater-block-preview', 'ppcp-overlay-parent' ];\n\tif (\n\t\tPcpCheckoutPayLaterBlock.vaultingEnabled ||\n\t\t! PcpCheckoutPayLaterBlock.placementEnabled\n\t) {\n\t\tclasses = [\n\t\t\t'ppcp-paylater-block-preview',\n\t\t\t'ppcp-paylater-unavailable',\n\t\t\t'block-editor-warning',\n\t\t];\n\t}\n\tconst props = useBlockProps( { className: classes } );\n\n\tuseEffect( () => {\n\t\tif ( ! ppcpId ) {\n\t\t\tsetAttributes( { ppcpId: 'ppcp-' + clientId } );\n\t\t}\n\t}, [ ppcpId, clientId ] );\n\n\tif ( PcpCheckoutPayLaterBlock.vaultingEnabled ) {\n\t\treturn (\n\t\t\t<div { ...props }>\n\t\t\t\t<div className={ 'block-editor-warning__contents' }>\n\t\t\t\t\t<p className={ 'block-editor-warning__message' }>\n\t\t\t\t\t\t{ __(\n\t\t\t\t\t\t\t'Checkout - Pay Later Messaging cannot be used while PayPal Vaulting is active. Disable PayPal Vaulting in the PayPal Payment settings to reactivate this block',\n\t\t\t\t\t\t\t'woocommerce-paypal-payments'\n\t\t\t\t\t\t) }\n\t\t\t\t\t</p>\n\t\t\t\t\t<div className={ 'block-editor-warning__actions' }>\n\t\t\t\t\t\t<span className={ 'block-editor-warning__action' }>\n\t\t\t\t\t\t\t<a href={ PcpCheckoutPayLaterBlock.settingsUrl }>\n\t\t\t\t\t\t\t\t<button\n\t\t\t\t\t\t\t\t\ttype={ 'button' }\n\t\t\t\t\t\t\t\t\tclassName={ 'components-button is-primary' }\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t{ __(\n\t\t\t\t\t\t\t\t\t\t'PayPal Payments Settings',\n\t\t\t\t\t\t\t\t\t\t'woocommerce-paypal-payments'\n\t\t\t\t\t\t\t\t\t) }\n\t\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t\t</a>\n\t\t\t\t\t\t</span>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t);\n\t}\n\n\tif ( ! PcpCheckoutPayLaterBlock.placementEnabled ) {\n\t\treturn (\n\t\t\t<div { ...props }>\n\t\t\t\t<div className={ 'block-editor-warning__contents' }>\n\t\t\t\t\t<p className={ 'block-editor-warning__message' }>\n\t\t\t\t\t\t{ __(\n\t\t\t\t\t\t\t'Checkout - Pay Later Messaging cannot be used while the “Checkout” messaging placement is disabled. Enable the placement in the PayPal Payments Pay Later settings to reactivate this block.',\n\t\t\t\t\t\t\t'woocommerce-paypal-payments'\n\t\t\t\t\t\t) }\n\t\t\t\t\t</p>\n\t\t\t\t\t<div className={ 'block-editor-warning__actions' }>\n\t\t\t\t\t\t<span className={ 'block-editor-warning__action' }>\n\t\t\t\t\t\t\t<a\n\t\t\t\t\t\t\t\thref={\n\t\t\t\t\t\t\t\t\tPcpCheckoutPayLaterBlock.payLaterSettingsUrl\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<button\n\t\t\t\t\t\t\t\t\ttype={ 'button' }\n\t\t\t\t\t\t\t\t\tclassName={ 'components-button is-primary' }\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t{ __(\n\t\t\t\t\t\t\t\t\t\t'PayPal Payments Settings',\n\t\t\t\t\t\t\t\t\t\t'woocommerce-paypal-payments'\n\t\t\t\t\t\t\t\t\t) }\n\t\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t\t</a>\n\t\t\t\t\t\t</span>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t);\n\t}\n\n\tconst scriptParams = useScriptParams(\n\t\tPcpCheckoutPayLaterBlock.ajax.cart_script_params\n\t);\n\tif ( scriptParams === null ) {\n\t\treturn (\n\t\t\t<div { ...props }>\n\t\t\t\t<Spinner />\n\t\t\t</div>\n\t\t);\n\t}\n\n\tconst urlParams = {\n\t\t...scriptParams.url_params,\n\t\tcomponents: 'messages',\n\t\tdataNamespace: 'ppcp-block-editor-checkout-paylater-message',\n\t};\n\n\treturn (\n\t\t<>\n\t\t\t<InspectorControls>\n\t\t\t\t<PanelBody\n\t\t\t\t\ttitle={ __(\n\t\t\t\t\t\t'Customize your messaging',\n\t\t\t\t\t\t'woocommerce-paypal-payments'\n\t\t\t\t\t) }\n\t\t\t\t>\n\t\t\t\t\t<p>\n\t\t\t\t\t\t{ __(\n\t\t\t\t\t\t\t'Choose the layout and color of your messaging in the PayPal Payments Pay Later settings for the “Checkout” messaging placement.',\n\t\t\t\t\t\t\t'woocommerce-paypal-payments'\n\t\t\t\t\t\t) }\n\t\t\t\t\t</p>\n\t\t\t\t\t<a href={ PcpCheckoutPayLaterBlock.payLaterSettingsUrl }>\n\t\t\t\t\t\t<button\n\t\t\t\t\t\t\ttype={ 'button' }\n\t\t\t\t\t\t\tclassName={ 'components-button is-primary' }\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t{ __(\n\t\t\t\t\t\t\t\t'PayPal Payments Settings',\n\t\t\t\t\t\t\t\t'woocommerce-paypal-payments'\n\t\t\t\t\t\t\t) }\n\t\t\t\t\t\t</button>\n\t\t\t\t\t</a>\n\t\t\t\t</PanelBody>\n\t\t\t</InspectorControls>\n\t\t\t<div { ...props }>\n\t\t\t\t<div className={ 'ppcp-overlay-child' }>\n\t\t\t\t\t<PayPalScriptProvider options={ urlParams }>\n\t\t\t\t\t\t<PayPalMessages\n\t\t\t\t\t\t\tstyle={ previewStyle }\n\t\t\t\t\t\t\tonRender={ () => setLoaded( true ) }\n\t\t\t\t\t\t\tamount={ amount }\n\t\t\t\t\t\t/>\n\t\t\t\t\t</PayPalScriptProvider>\n\t\t\t\t</div>\n\t\t\t\t<div className={ 'ppcp-overlay-child ppcp-unclicable-overlay' }>\n\t\t\t\t\t{ ' ' }\n\t\t\t\t\t{ /* make the message not clickable */ }\n\t\t\t\t\t{ ! loaded && <Spinner /> }\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t</>\n\t);\n}\n"], "names": ["__webpack_require__", "module", "getter", "__esModule", "d", "a", "exports", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "window", "SCRIPT_LOADING_STATE", "DISPATCH_ACTION", "PAYPAL_HOSTED_FIELDS_TYPES", "__assign", "assign", "t", "s", "i", "n", "arguments", "length", "p", "apply", "this", "__rest", "e", "indexOf", "getOwnPropertySymbols", "propertyIsEnumerable", "__spread<PERSON><PERSON>y", "to", "from", "pack", "ar", "l", "Array", "slice", "concat", "SuppressedError", "SCRIPT_ID", "SDK_SETTINGS", "braintreeVersion", "DEFAULT_PAYPAL_NAMESPACE", "getPayPalWindowNamespace$1", "namespace", "generateErrorMessage", "_a", "reactComponentName", "sdkComponentKey", "_b", "sdkRequestedComponents", "_c", "sdkDataNamespace", "requiredOptionCapitalized", "char<PERSON>t", "toUpperCase", "substring", "errorMessage", "requestedComponents", "join", "includes", "expectedComponents", "filter", "Boolean", "getScriptID", "options", "paypalScriptOptions", "str", "hash", "total", "charCodeAt", "String", "fromCharCode", "Math", "abs", "hashStr", "JSON", "stringify", "scriptReducer", "state", "action", "reactPayPalScriptID", "scriptNode", "type", "LOADING_STATUS", "value", "loadingStatus", "loadingStatusErrorMessage", "message", "RESET_OPTIONS", "self", "document", "querySelector", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "PENDING", "SET_BRAINTREE_INSTANCE", "braintreePayPalCheckoutInstance", "ScriptContext", "createContext", "usePayPalScriptReducer", "scriptContext", "dispatch", "Error", "validateReducer", "useContext", "isInitial", "INITIAL", "isPending", "isResolved", "RESOLVED", "isRejected", "REJECTED", "PayPalButtons", "className", "_d", "disabled", "children", "_e", "forceReRender", "buttonProps", "isDisabledStyle", "opacity", "classNames", "trim", "buttonsContainerRef", "useRef", "buttons", "_f", "_g", "useState", "initActions", "setInitActions", "_h", "isEligible", "setIsEligible", "setErrorState", "closeButtonsComponent", "current", "close", "catch", "updateProps", "useEffect", "paypalWindowNamespace", "dataNamespace", "undefined", "Buttons", "displayName", "components", "onInit", "data", "actions", "err", "render", "fundingSource", "disable", "enable", "ref", "style", "createScriptElement", "url", "attributes", "newScript", "createElement", "src", "keys", "for<PERSON>ach", "setAttribute", "loadScript", "PromisePonyfill", "Promise", "validateArguments", "resolve", "sdkBaseUrl", "environment", "params", "queryString", "optionsWithStringIndex", "reduce", "accumulator", "replacer", "toString", "match", "indexOfMatch", "toLowerCase", "replace", "queryParams", "processOptions", "existingWindowNamespace", "getPayPalWindowNamespace", "currentScript", "nextScript", "currentScriptClone", "cloneNode", "dataset", "uidAuto", "isExactMatch", "findScript", "reject", "onSuccess", "onError", "onerror", "onload", "head", "insertBefore", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "insertScriptElement", "defaultError", "loadCustomScript", "then", "newWindowNamespace", "PayPalMarks", "markProps", "markContainer<PERSON>ef", "Marks", "mark", "<PERSON><PERSON><PERSON><PERSON>", "renderPayPalMark", "PayPalMessages", "messageProps", "messagesContainerRef", "messages", "Messages", "PayPalScriptProvider", "clientId", "deferLoading", "useReducer", "isSubscribed", "console", "error", "Provider", "ignore", "_regeneratorRuntime", "r", "Symbol", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "arg", "h", "f", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "_invoke", "AsyncIterator", "invoke", "_typeof", "__await", "callInvokeWithMethodAndArg", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "isGeneratorFunction", "constructor", "name", "setPrototypeOf", "__proto__", "awrap", "async", "reverse", "pop", "prev", "stop", "rval", "handle", "complete", "finish", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_arrayLikeToArray", "cardFieldsForm", "fields", "registerField", "unregisterField", "useScriptParams", "requestConfig", "_useState2", "isArray", "_arrayWithHoles", "_iterableToArrayLimit", "test", "_unsupportedIterableToArray", "_nonIterableRest", "setData", "_callee", "_json$data", "response", "json", "_context", "fetch", "endpoint", "success", "url_params", "t0", "_next", "_throw", "paypalIcon", "React", "width", "height", "viewBox", "transform", "clipPath", "fill", "registerBlockType", "metadata", "icon", "edit", "_ref", "_wp$data$select", "amount", "setAttributes", "ppcpId", "loaded", "setLoaded", "postContent", "wp", "select", "getEditedPostContent", "previewStyle", "checkoutConfig", "PcpCheckoutPayLaterBlock", "config", "checkout", "layout", "color", "ratio", "logo", "position", "text", "size", "classes", "vaultingEnabled", "placementEnabled", "props", "useBlockProps", "__", "href", "settingsUrl", "payLaterSettingsUrl", "scriptParams", "ajax", "cart_script_params", "Spinner", "urlParams", "_objectSpread", "Fragment", "InspectorCont<PERSON><PERSON>", "PanelBody", "title", "onRender", "save"], "sourceRoot": ""}