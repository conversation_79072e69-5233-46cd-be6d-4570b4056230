/*! For license information please see cart-paylater-block.js.LICENSE.txt */
(()=>{"use strict";var t={n:e=>{var r=e&&e.__esModule?()=>e.default:()=>e;return t.d(r,{a:r}),r},d:(e,r)=>{for(var n in r)t.o(r,n)&&!t.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:r[n]})},o:(t,e)=>Object.prototype.hasOwnProperty.call(t,e)};const e=window.wp.blocks,r=window.wp.i18n,n=window.wp.element,a=window.wp.blockEditor,o=window.wp.components,c=window.React;var i,l,s,u=t.n(c);!function(t){t.INITIAL="initial",t.PENDING="pending",t.REJECTED="rejected",t.RESOLVED="resolved"}(i||(i={})),function(t){t.LOADING_STATUS="setLoadingStatus",t.RESET_OPTIONS="resetOptions",t.SET_BRAINTREE_INSTANCE="braintreeInstance"}(l||(l={})),function(t){t.NUMBER="number",t.CVV="cvv",t.EXPIRATION_DATE="expirationDate",t.EXPIRATION_MONTH="expirationMonth",t.EXPIRATION_YEAR="expirationYear",t.POSTAL_CODE="postalCode"}(s||(s={}));var p=function(){return p=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var a in e=arguments[r])Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a]);return t},p.apply(this,arguments)};function f(t,e){var r={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e.indexOf(n)<0&&(r[n]=t[n]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var a=0;for(n=Object.getOwnPropertySymbols(t);a<n.length;a++)e.indexOf(n[a])<0&&Object.prototype.propertyIsEnumerable.call(t,n[a])&&(r[n[a]]=t[n[a]])}return r}function d(t,e,r){if(r||2===arguments.length)for(var n,a=0,o=e.length;a<o;a++)!n&&a in e||(n||(n=Array.prototype.slice.call(e,0,a)),n[a]=e[a]);return t.concat(n||Array.prototype.slice.call(e))}"function"==typeof SuppressedError&&SuppressedError;var y="data-react-paypal-script-id",m="react-paypal-js",h="dataNamespace",v="dataSdkIntegrationSource",g="3.84.0",b=("https://js.braintreegateway.com/web/".concat(g,"/js/client.min.js"),"https://js.braintreegateway.com/web/".concat(g,"/js/paypal-checkout.min.js"),"paypal");function w(t){return void 0===t&&(t=b),window[t]}function E(t){var e=t.reactComponentName,r=t.sdkComponentKey,n=t.sdkRequestedComponents,a=void 0===n?"":n,o=t.sdkDataNamespace,c=void 0===o?b:o,i=r.charAt(0).toUpperCase().concat(r.substring(1)),l="Unable to render <".concat(e," /> because window.").concat(c,".").concat(i," is undefined."),s="string"==typeof a?a:a.join(",");if(!s.includes(r)){var u=[s,r].filter(Boolean).join();l+="\nTo fix the issue, add '".concat(r,"' to the list of components passed to the parent PayPalScriptProvider:")+"\n`<PayPalScriptProvider options={{ components: '".concat(u,"'}}>`.")}return l}function P(t){var e=t,r=y;e[r];var n=f(e,[r+""]);return"react-paypal-js-".concat(function(t){for(var e="",r=0;r<t.length;r++){var n=t[r].charCodeAt(0)*r;t[r+1]&&(n+=t[r+1].charCodeAt(0)*(r-1)),e+=String.fromCharCode(97+Math.abs(n)%26)}return e}(JSON.stringify(n)))}function S(t,e){var r,n,a,o;switch(e.type){case l.LOADING_STATUS:return"object"==typeof e.value?p(p({},t),{loadingStatus:e.value.state,loadingStatusErrorMessage:e.value.message}):p(p({},t),{loadingStatus:e.value});case l.RESET_OPTIONS:return a=t.options[y],(null==(o=self.document.querySelector("script[".concat(y,'="').concat(a,'"]')))?void 0:o.parentNode)&&o.parentNode.removeChild(o),p(p({},t),{loadingStatus:i.PENDING,options:p(p((r={},r[v]=m,r),e.value),(n={},n[y]="".concat(P(e.value)),n))});case l.SET_BRAINTREE_INSTANCE:return p(p({},t),{braintreePayPalCheckoutInstance:e.value});default:return t}}var N=(0,c.createContext)(null);function O(){var t=function(t){if("function"==typeof(null==t?void 0:t.dispatch)&&0!==t.dispatch.length)return t;throw new Error("usePayPalScriptReducer must be used within a PayPalScriptProvider")}((0,c.useContext)(N));return[p(p({},t),{isInitial:t.loadingStatus===i.INITIAL,isPending:t.loadingStatus===i.PENDING,isResolved:t.loadingStatus===i.RESOLVED,isRejected:t.loadingStatus===i.REJECTED}),t.dispatch]}(0,c.createContext)({});var k=function(t){var e,r=t.className,n=void 0===r?"":r,a=t.disabled,o=void 0!==a&&a,i=t.children,l=t.forceReRender,s=void 0===l?[]:l,y=f(t,["className","disabled","children","forceReRender"]),m=o?{opacity:.38}:{},v="".concat(n," ").concat(o?"paypal-buttons-disabled":"").trim(),g=(0,c.useRef)(null),b=(0,c.useRef)(null),P=O()[0],S=P.isResolved,N=P.options,j=(0,c.useState)(null),R=j[0],_=j[1],L=(0,c.useState)(!0),I=L[0],C=L[1],x=(0,c.useState)(null)[1];function A(){null!==b.current&&b.current.close().catch((function(){}))}return(null===(e=b.current)||void 0===e?void 0:e.updateProps)&&b.current.updateProps({message:y.message}),(0,c.useEffect)((function(){if(!1===S)return A;var t=w(N.dataNamespace);if(void 0===t||void 0===t.Buttons)return x((function(){throw new Error(E({reactComponentName:k.displayName,sdkComponentKey:"buttons",sdkRequestedComponents:N.components,sdkDataNamespace:N[h]}))})),A;try{b.current=t.Buttons(p(p({},y),{onInit:function(t,e){_(e),"function"==typeof y.onInit&&y.onInit(t,e)}}))}catch(t){return x((function(){throw new Error("Failed to render <PayPalButtons /> component. Failed to initialize:  ".concat(t))}))}return!1===b.current.isEligible()?(C(!1),A):g.current?(b.current.render(g.current).catch((function(t){null!==g.current&&0!==g.current.children.length&&x((function(){throw new Error("Failed to render <PayPalButtons /> component. ".concat(t))}))})),A):A}),d(d([S],s,!0),[y.fundingSource],!1)),(0,c.useEffect)((function(){null!==R&&(!0===o?R.disable().catch((function(){})):R.enable().catch((function(){})))}),[o,R]),u().createElement(u().Fragment,null,I?u().createElement("div",{ref:g,style:m,className:v}):i)};function j(t,e){void 0===e&&(e={});var r=document.createElement("script");return r.src=t,Object.keys(e).forEach((function(t){r.setAttribute(t,e[t]),"data-csp-nonce"===t&&r.setAttribute("nonce",e["data-csp-nonce"])})),r}function R(t,e){if(void 0===e&&(e=Promise),L(t,e),"undefined"==typeof document)return e.resolve(null);var r=function(t){var e="sandbox"===t.environment?"https://www.sandbox.paypal.com/sdk/js":"https://www.paypal.com/sdk/js";delete t.environment,t.sdkBaseUrl&&(e=t.sdkBaseUrl,delete t.sdkBaseUrl);var r,n,a=t,o=Object.keys(a).filter((function(t){return void 0!==a[t]&&null!==a[t]&&""!==a[t]})).reduce((function(t,e){var r,n=a[e].toString();return r=function(t,e){return(e?"-":"")+t.toLowerCase()},"data"===(e=e.replace(/[A-Z]+(?![a-z])|[A-Z]/g,r)).substring(0,4)||"crossorigin"===e?t.attributes[e]=n:t.queryParams[e]=n,t}),{queryParams:{},attributes:{}}),c=o.queryParams,i=o.attributes;return c["merchant-id"]&&-1!==c["merchant-id"].indexOf(",")&&(i["data-merchant-id"]=c["merchant-id"],c["merchant-id"]="*"),{url:"".concat(e,"?").concat((r=c,n="",Object.keys(r).forEach((function(t){0!==n.length&&(n+="&"),n+=t+"="+r[t]})),n)),attributes:i}}(t),n=r.url,a=r.attributes,o=a["data-namespace"]||"paypal",c=_(o);return a["data-js-sdk-library"]||(a["data-js-sdk-library"]="paypal-js"),function(t,e){var r=document.querySelector('script[src="'.concat(t,'"]'));if(null===r)return null;var n=j(t,e),a=r.cloneNode();if(delete a.dataset.uidAuto,Object.keys(a.dataset).length!==Object.keys(n.dataset).length)return null;var o=!0;return Object.keys(a.dataset).forEach((function(t){a.dataset[t]!==n.dataset[t]&&(o=!1)})),o?r:null}(n,a)&&c?e.resolve(c):function(t,e){void 0===e&&(e=Promise),L(t,e);var r=t.url,n=t.attributes;if("string"!=typeof r||0===r.length)throw new Error("Invalid url.");if(void 0!==n&&"object"!=typeof n)throw new Error("Expected attributes to be an object.");return new e((function(t,e){if("undefined"==typeof document)return t();!function(t){var e=t.url,r=t.attributes,n=t.onSuccess,a=t.onError,o=j(e,r);o.onerror=a,o.onload=n,document.head.insertBefore(o,document.head.firstElementChild)}({url:r,attributes:n,onSuccess:function(){return t()},onError:function(){var t=new Error('The script "'.concat(r,'" failed to load. Check the HTTP status code and response body in DevTools to learn more.'));return e(t)}})}))}({url:n,attributes:a},e).then((function(){var t=_(o);if(t)return t;throw new Error("The window.".concat(o," global variable is not available."))}))}function _(t){return window[t]}function L(t,e){if("object"!=typeof t||null===t)throw new Error("Expected an options object.");var r=t.environment;if(r&&"production"!==r&&"sandbox"!==r)throw new Error('The `environment` option must be either "production" or "sandbox".');if(void 0!==e&&"function"!=typeof e)throw new Error("Expected PromisePonyfill to be a function.")}k.displayName="PayPalButtons";var I=function(t){var e=t.className,r=void 0===e?"":e,n=t.children,a=f(t,["className","children"]),o=O()[0],i=o.isResolved,l=o.options,s=(0,c.useRef)(null),d=(0,c.useState)(!0),y=d[0],m=d[1],v=(0,c.useState)(null)[1];return(0,c.useEffect)((function(){if(!1!==i){var t=w(l[h]);if(void 0===t||void 0===t.Marks)return v((function(){throw new Error(E({reactComponentName:I.displayName,sdkComponentKey:"marks",sdkRequestedComponents:l.components,sdkDataNamespace:l[h]}))}));!function(t){var e=s.current;if(!e||!t.isEligible())return m(!1);e.firstChild&&e.removeChild(e.firstChild),t.render(e).catch((function(t){null!==e&&0!==e.children.length&&v((function(){throw new Error("Failed to render <PayPalMarks /> component. ".concat(t))}))}))}(t.Marks(p({},a)))}}),[i,a.fundingSource]),u().createElement(u().Fragment,null,y?u().createElement("div",{ref:s,className:r}):n)};I.displayName="PayPalMarks";var C=function(t){var e=t.className,r=void 0===e?"":e,n=t.forceReRender,a=void 0===n?[]:n,o=f(t,["className","forceReRender"]),i=O()[0],l=i.isResolved,s=i.options,y=(0,c.useRef)(null),m=(0,c.useRef)(null),v=(0,c.useState)(null)[1];return(0,c.useEffect)((function(){if(!1!==l){var t=w(s[h]);if(void 0===t||void 0===t.Messages)return v((function(){throw new Error(E({reactComponentName:C.displayName,sdkComponentKey:"messages",sdkRequestedComponents:s.components,sdkDataNamespace:s[h]}))}));m.current=t.Messages(p({},o)),m.current.render(y.current).catch((function(t){null!==y.current&&0!==y.current.children.length&&v((function(){throw new Error("Failed to render <PayPalMessages /> component. ".concat(t))}))}))}}),d([l],a,!0)),u().createElement("div",{ref:y,className:r})};C.displayName="PayPalMessages";var x=function(t){var e,r=t.options,n=void 0===r?{clientId:"test"}:r,a=t.children,o=t.deferLoading,s=void 0!==o&&o,f=(0,c.useReducer)(S,{options:p(p({},n),(e={},e.dataJsSdkLibrary=m,e[v]=m,e[y]="".concat(P(n)),e)),loadingStatus:s?i.INITIAL:i.PENDING}),d=f[0],h=f[1];return(0,c.useEffect)((function(){if(!1===s&&d.loadingStatus===i.INITIAL)return h({type:l.LOADING_STATUS,value:i.PENDING});if(d.loadingStatus===i.PENDING){var t=!0;return R(d.options).then((function(){t&&h({type:l.LOADING_STATUS,value:i.RESOLVED})})).catch((function(e){console.error("".concat("Failed to load the PayPal JS SDK script."," ").concat(e)),t&&h({type:l.LOADING_STATUS,value:{state:i.REJECTED,message:String(e)}})})),function(){t=!1}}}),[d.options,s,d.loadingStatus]),u().createElement(N.Provider,{value:p(p({},d),{dispatch:h})},a)};function A(){}function T(t){return T="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},T(t)}function D(){D=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},c=o.iterator||"@@iterator",i=o.asyncIterator||"@@asyncIterator",l=o.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,r){return t[e]=r}}function u(t,e,r,n){var o=e&&e.prototype instanceof v?e:v,c=Object.create(o.prototype),i=new L(n||[]);return a(c,"_invoke",{value:k(t,r,i)}),c}function p(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=u;var f="suspendedStart",d="suspendedYield",y="executing",m="completed",h={};function v(){}function g(){}function b(){}var w={};s(w,c,(function(){return this}));var E=Object.getPrototypeOf,P=E&&E(E(I([])));P&&P!==r&&n.call(P,c)&&(w=P);var S=b.prototype=v.prototype=Object.create(w);function N(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function O(t,e){function r(a,o,c,i){var l=p(t[a],t,o);if("throw"!==l.type){var s=l.arg,u=s.value;return u&&"object"==T(u)&&n.call(u,"__await")?e.resolve(u.__await).then((function(t){r("next",t,c,i)}),(function(t){r("throw",t,c,i)})):e.resolve(u).then((function(t){s.value=t,c(s)}),(function(t){return r("throw",t,c,i)}))}i(l.arg)}var o;a(this,"_invoke",{value:function(t,n){function a(){return new e((function(e,a){r(t,n,e,a)}))}return o=o?o.then(a,a):a()}})}function k(e,r,n){var a=f;return function(o,c){if(a===y)throw Error("Generator is already running");if(a===m){if("throw"===o)throw c;return{value:t,done:!0}}for(n.method=o,n.arg=c;;){var i=n.delegate;if(i){var l=j(i,n);if(l){if(l===h)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===f)throw a=m,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=y;var s=p(e,r,n);if("normal"===s.type){if(a=n.done?m:d,s.arg===h)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(a=m,n.method="throw",n.arg=s.arg)}}}function j(e,r){var n=r.method,a=e.iterator[n];if(a===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,j(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),h;var o=p(a,e.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,h;var c=o.arg;return c?c.done?(r[e.resultName]=c.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,h):c:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,h)}function R(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function _(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function L(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(R,this),this.reset(!0)}function I(e){if(e||""===e){var r=e[c];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,o=function r(){for(;++a<e.length;)if(n.call(e,a))return r.value=e[a],r.done=!1,r;return r.value=t,r.done=!0,r};return o.next=o}}throw new TypeError(T(e)+" is not iterable")}return g.prototype=b,a(S,"constructor",{value:b,configurable:!0}),a(b,"constructor",{value:g,configurable:!0}),g.displayName=s(b,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===g||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,s(t,l,"GeneratorFunction")),t.prototype=Object.create(S),t},e.awrap=function(t){return{__await:t}},N(O.prototype),s(O.prototype,i,(function(){return this})),e.AsyncIterator=O,e.async=function(t,r,n,a,o){void 0===o&&(o=Promise);var c=new O(u(t,r,n,a),o);return e.isGeneratorFunction(r)?c:c.next().then((function(t){return t.done?t.value:c.next()}))},N(S),s(S,l,"Generator"),s(S,c,(function(){return this})),s(S,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=I,L.prototype={constructor:L,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(_),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function a(n,a){return i.type="throw",i.arg=e,r.next=n,a&&(r.method="next",r.arg=t),!!a}for(var o=this.tryEntries.length-1;o>=0;--o){var c=this.tryEntries[o],i=c.completion;if("root"===c.tryLoc)return a("end");if(c.tryLoc<=this.prev){var l=n.call(c,"catchLoc"),s=n.call(c,"finallyLoc");if(l&&s){if(this.prev<c.catchLoc)return a(c.catchLoc,!0);if(this.prev<c.finallyLoc)return a(c.finallyLoc)}else if(l){if(this.prev<c.catchLoc)return a(c.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<c.finallyLoc)return a(c.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var o=a;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var c=o?o.completion:{};return c.type=t,c.arg=e,o?(this.method="next",this.next=o.finallyLoc,h):this.complete(c)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),h},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),_(r),h}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var a=n.arg;_(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:I(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),h}},e}function B(t,e,r,n,a,o,c){try{var i=t[o](c),l=i.value}catch(t){return void r(t)}i.done?e(l):Promise.resolve(l).then(n,a)}function M(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}(0,c.createContext)({cardFieldsForm:null,fields:{},registerField:A,unregisterField:A});var G=function(t){var e,r,a=(e=(0,n.useState)(null),r=2,function(t){if(Array.isArray(t))return t}(e)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,a,o,c,i=[],l=!0,s=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=o.call(r)).done)&&(i.push(n.value),i.length!==e);l=!0);}catch(t){s=!0,a=t}finally{try{if(!l&&null!=r.return&&(c=r.return(),Object(c)!==c))return}finally{if(s)throw a}}return i}}(e,r)||function(t,e){if(t){if("string"==typeof t)return M(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?M(t,e):void 0}}(e,r)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),o=a[0],c=a[1];return(0,n.useEffect)((function(){var e;(e=D().mark((function e(){var r,n,a;return D().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,fetch(t.endpoint);case 3:return n=e.sent,e.next=6,n.json();case 6:(a=e.sent).success&&null!=a&&null!==(r=a.data)&&void 0!==r&&r.url_params?c(a.data):c(!1),e.next=14;break;case 10:e.prev=10,e.t0=e.catch(0),console.error(e.t0),c(!1);case 14:case"end":return e.stop()}}),e,null,[[0,10]])})),function(){var t=this,r=arguments;return new Promise((function(n,a){var o=e.apply(t,r);function c(t){B(o,n,a,c,i,"next",t)}function i(t){B(o,n,a,c,i,"throw",t)}c(void 0)}))})()}),[t]),o};function F(t){return F="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},F(t)}function U(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function q(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?U(Object(r),!0).forEach((function(e){z(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):U(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function z(t,e,r){return(e=function(t){var e=function(t){if("object"!=F(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,"string");if("object"!=F(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==F(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function V(t,e){if(t){if("string"==typeof t)return J(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?J(t,e):void 0}}function J(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}const K=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"woocommerce-paypal-payments/cart-paylater-messages","title":"Cart - PayPal Pay Later messaging","category":"woocommerce-paypal-payments","description":"PayPal Pay Later messaging will be displayed for eligible customers. Customers automatically see the most relevant Pay Later offering.","example":{},"parent":["woocommerce/cart-totals-block"],"attributes":{"blockId":{"type":"string","default":"woocommerce-paypal-payments/cart-paylater-messages"},"ppcpId":{"type":"string"},"lock":{"type":"object","default":{"remove":true,"move":false}}},"supports":{"html":false,"inserter":false,"multiple":false},"textdomain":"woocommerce-paypal-payments","editorScript":"ppcp-cart-paylater-block"}');var Y=React.createElement("svg",{width:"584.798",height:"720",viewBox:"0 0 154.728 190.5"},React.createElement("g",{transform:"translate(898.192 276.071)"},React.createElement("path",{clipPath:"none",d:"M-837.663-237.968a5.49 5.49 0 0 0-5.423 4.633l-9.013 57.15-8.281 52.514-.005.044.01-.044 8.281-52.514c.421-2.669 2.719-4.633 5.42-4.633h26.404c26.573 0 49.127-19.387 53.246-45.658.314-1.996.482-3.973.52-5.924v-.003h-.003c-6.753-3.543-14.683-5.565-23.372-5.565z",fill:"#001c64"}),React.createElement("path",{clipPath:"none",d:"M-766.506-232.402c-.037 1.951-.207 3.93-.52 5.926-4.119 26.271-26.673 45.658-53.246 45.658h-26.404c-2.701 0-4.999 1.964-5.42 4.633l-8.281 52.514-5.197 32.947a4.46 4.46 0 0 0 4.405 5.153h28.66a5.49 5.49 0 0 0 5.423-4.633l7.55-47.881c.423-2.669 2.722-4.636 5.423-4.636h16.876c26.573 0 49.124-19.386 53.243-45.655 2.924-18.649-6.46-35.614-22.511-44.026z",fill:"#0070e0"}),React.createElement("path",{clipPath:"none",d:"M-870.225-276.071a5.49 5.49 0 0 0-5.423 4.636l-22.489 142.608a4.46 4.46 0 0 0 4.405 5.156h33.351l8.281-52.514 9.013-57.15a5.49 5.49 0 0 1 5.423-4.633h47.782c8.691 0 16.621 2.025 23.375 5.563.46-23.917-19.275-43.666-46.412-43.666z",fill:"#003087"})));(0,e.registerBlockType)(K,{icon:Y,edit:function(t){var e,c,i,l,s=t.attributes,u=t.clientId,p=t.setAttributes,f=s.ppcpId,d=(i=(0,n.useState)(!1),l=2,function(t){if(Array.isArray(t))return t}(i)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,a,o,c,i=[],l=!0,s=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=o.call(r)).done)&&(i.push(n.value),i.length!==e);l=!0);}catch(t){s=!0,a=t}finally{try{if(!l&&null!=r.return&&(c=r.return(),Object(c)!==c))return}finally{if(s)throw a}}return i}}(i,l)||V(i,l)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),y=d[0],m=d[1],h=String(null===(e=wp.data.select("core/editor"))||void 0===e?void 0:e.getEditedPostContent());(h.includes("woocommerce/checkout")||h.includes("woocommerce/cart"))&&(c=50);var v,g=PcpCartPayLaterBlock.config.cart;v="flex"===g.layout?{layout:g.layout,color:g.color,ratio:g.ratio}:{layout:g.layout,logo:{position:g["logo-position"],type:g["logo-type"]},text:{color:g["text-color"],size:g["text-size"]}};var b=["ppcp-paylater-block-preview","ppcp-overlay-parent"];!PcpCartPayLaterBlock.vaultingEnabled&&PcpCartPayLaterBlock.placementEnabled||(b=[].concat(function(t){return function(t){if(Array.isArray(t))return J(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||V(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(b),["ppcp-paylater-unavailable","block-editor-warning"]));var w=(0,a.useBlockProps)({className:b.join(" ")});if((0,n.useEffect)((function(){f||p({ppcpId:"ppcp-".concat(u)})}),[f,u]),PcpCartPayLaterBlock.vaultingEnabled)return React.createElement("div",w,React.createElement("div",{className:"block-editor-warning__contents"},React.createElement("p",{className:"block-editor-warning__message"},(0,r.__)("Cart - Pay Later Messaging cannot be used while PayPal Vaulting is active. Disable PayPal Vaulting in the PayPal Payment settings to reactivate this block","woocommerce-paypal-payments")),React.createElement("div",{className:"block-editor-warning__actions"},React.createElement("span",{className:"block-editor-warning__action"},React.createElement("a",{href:PcpCartPayLaterBlock.settingsUrl},React.createElement("button",{type:"button",className:"components-button is-primary"},(0,r.__)("PayPal Payments Settings","woocommerce-paypal-payments")))))));if(!PcpCartPayLaterBlock.placementEnabled)return React.createElement("div",w,React.createElement("div",{className:"block-editor-warning__contents"},React.createElement("p",{className:"block-editor-warning__message"},(0,r.__)("Cart - Pay Later Messaging cannot be used while the “Cart” messaging placement is disabled. Enable the placement in the PayPal Payments Pay Later settings to reactivate this block.","woocommerce-paypal-payments")),React.createElement("div",{className:"block-editor-warning__actions"},React.createElement("span",{className:"block-editor-warning__action"},React.createElement("a",{href:PcpCartPayLaterBlock.payLaterSettingsUrl},React.createElement("button",{type:"button",className:"components-button is-primary"},(0,r.__)("PayPal Payments Settings","woocommerce-paypal-payments")))))));var E=G(PcpCartPayLaterBlock.ajax.cart_script_params);if(null===E)return React.createElement("div",w,React.createElement(o.Spinner,null));var P=q(q({},E.url_params),{},{components:"messages",dataNamespace:"ppcp-block-editor-cart-paylater-message"});return React.createElement(React.Fragment,null,React.createElement(a.InspectorControls,null,React.createElement(o.PanelBody,{title:(0,r.__)("Customize your messaging","woocommerce-paypal-payments")},React.createElement("p",null,(0,r.__)("Choose the layout and color of your messaging in the PayPal Payments Pay Later settings for the “Cart” messaging placement.","woocommerce-paypal-payments")),React.createElement("a",{href:PcpCartPayLaterBlock.payLaterSettingsUrl},React.createElement("button",{type:"button",className:"components-button is-primary"},(0,r.__)("PayPal Payments Settings","woocommerce-paypal-payments"))))),React.createElement("div",w,React.createElement("div",{className:"ppcp-overlay-child"},React.createElement(x,{options:P},React.createElement(C,{style:v,onRender:function(){return m(!0)},amount:c}))),React.createElement("div",{className:"ppcp-overlay-child ppcp-unclicable-overlay"}," ",!y&&React.createElement(o.Spinner,null))))},save:function(){return null}})})();
//# sourceMappingURL=cart-paylater-block.js.map