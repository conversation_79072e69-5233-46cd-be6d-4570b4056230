{"version": 3, "file": "js/cart-paylater-block-inserter.js", "mappings": ";sQACAA,EAAA,kBAAAC,CAAA,MAAAC,EAAAD,EAAA,GAAAE,EAAAC,OAAAC,UAAAC,EAAAH,EAAAI,eAAAC,EAAAJ,OAAAK,gBAAA,SAAAP,EAAAD,EAAAE,GAAAD,EAAAD,GAAAE,EAAAO,KAAA,EAAAC,EAAA,mBAAAC,OAAAA,OAAA,GAAAC,EAAAF,EAAAG,UAAA,aAAAC,EAAAJ,EAAAK,eAAA,kBAAAC,EAAAN,EAAAO,aAAA,yBAAAC,EAAAjB,EAAAD,EAAAE,GAAA,OAAAC,OAAAK,eAAAP,EAAAD,EAAA,CAAAS,MAAAP,EAAAiB,YAAA,EAAAC,cAAA,EAAAC,UAAA,IAAApB,EAAAD,EAAA,KAAAkB,EAAA,aAAAjB,GAAAiB,EAAA,SAAAjB,EAAAD,EAAAE,GAAA,OAAAD,EAAAD,GAAAE,CAAA,WAAAoB,EAAArB,EAAAD,EAAAE,EAAAG,GAAA,IAAAK,EAAAV,GAAAA,EAAAI,qBAAAmB,EAAAvB,EAAAuB,EAAAX,EAAAT,OAAAqB,OAAAd,EAAAN,WAAAU,EAAA,IAAAW,EAAApB,GAAA,WAAAE,EAAAK,EAAA,WAAAH,MAAAiB,EAAAzB,EAAAC,EAAAY,KAAAF,CAAA,UAAAe,EAAA1B,EAAAD,EAAAE,GAAA,WAAA0B,KAAA,SAAAC,IAAA5B,EAAA6B,KAAA9B,EAAAE,GAAA,OAAAD,GAAA,OAAA2B,KAAA,QAAAC,IAAA5B,EAAA,EAAAD,EAAAsB,KAAAA,EAAA,IAAAS,EAAA,iBAAAC,EAAA,iBAAAC,EAAA,YAAAC,EAAA,YAAAC,EAAA,YAAAZ,IAAA,UAAAa,IAAA,UAAAC,IAAA,KAAAC,EAAA,GAAApB,EAAAoB,EAAA1B,GAAA,8BAAA2B,EAAApC,OAAAqC,eAAAC,EAAAF,GAAAA,EAAAA,EAAAG,EAAA,MAAAD,GAAAA,IAAAvC,GAAAG,EAAAyB,KAAAW,EAAA7B,KAAA0B,EAAAG,GAAA,IAAAE,EAAAN,EAAAjC,UAAAmB,EAAAnB,UAAAD,OAAAqB,OAAAc,GAAA,SAAAM,EAAA3C,GAAA,0BAAA4C,SAAA,SAAA7C,GAAAkB,EAAAjB,EAAAD,GAAA,SAAAC,GAAA,YAAA6C,QAAA9C,EAAAC,EAAA,gBAAA8C,EAAA9C,EAAAD,GAAA,SAAAgD,EAAA9C,EAAAK,EAAAG,EAAAE,GAAA,IAAAE,EAAAa,EAAA1B,EAAAC,GAAAD,EAAAM,GAAA,aAAAO,EAAAc,KAAA,KAAAZ,EAAAF,EAAAe,IAAAE,EAAAf,EAAAP,MAAA,OAAAsB,GAAA,UAAAkB,EAAAlB,IAAA1B,EAAAyB,KAAAC,EAAA,WAAA/B,EAAAkD,QAAAnB,EAAAoB,SAAAC,MAAA,SAAAnD,GAAA+C,EAAA,OAAA/C,EAAAS,EAAAE,EAAA,aAAAX,GAAA+C,EAAA,QAAA/C,EAAAS,EAAAE,EAAA,IAAAZ,EAAAkD,QAAAnB,GAAAqB,MAAA,SAAAnD,GAAAe,EAAAP,MAAAR,EAAAS,EAAAM,EAAA,aAAAf,GAAA,OAAA+C,EAAA,QAAA/C,EAAAS,EAAAE,EAAA,IAAAA,EAAAE,EAAAe,IAAA,KAAA3B,EAAAK,EAAA,gBAAAE,MAAA,SAAAR,EAAAI,GAAA,SAAAgD,IAAA,WAAArD,GAAA,SAAAA,EAAAE,GAAA8C,EAAA/C,EAAAI,EAAAL,EAAAE,EAAA,WAAAA,EAAAA,EAAAA,EAAAkD,KAAAC,EAAAA,GAAAA,GAAA,aAAA3B,EAAA1B,EAAAE,EAAAG,GAAA,IAAAE,EAAAwB,EAAA,gBAAArB,EAAAE,GAAA,GAAAL,IAAA0B,EAAA,MAAAqB,MAAA,mCAAA/C,IAAA2B,EAAA,cAAAxB,EAAA,MAAAE,EAAA,OAAAH,MAAAR,EAAAsD,MAAA,OAAAlD,EAAAmD,OAAA9C,EAAAL,EAAAwB,IAAAjB,IAAA,KAAAE,EAAAT,EAAAoD,SAAA,GAAA3C,EAAA,KAAAE,EAAA0C,EAAA5C,EAAAT,GAAA,GAAAW,EAAA,IAAAA,IAAAmB,EAAA,gBAAAnB,CAAA,cAAAX,EAAAmD,OAAAnD,EAAAsD,KAAAtD,EAAAuD,MAAAvD,EAAAwB,SAAA,aAAAxB,EAAAmD,OAAA,IAAAjD,IAAAwB,EAAA,MAAAxB,EAAA2B,EAAA7B,EAAAwB,IAAAxB,EAAAwD,kBAAAxD,EAAAwB,IAAA,gBAAAxB,EAAAmD,QAAAnD,EAAAyD,OAAA,SAAAzD,EAAAwB,KAAAtB,EAAA0B,EAAA,IAAAK,EAAAX,EAAA3B,EAAAE,EAAAG,GAAA,cAAAiC,EAAAV,KAAA,IAAArB,EAAAF,EAAAkD,KAAArB,EAAAF,EAAAM,EAAAT,MAAAM,EAAA,gBAAA1B,MAAA6B,EAAAT,IAAA0B,KAAAlD,EAAAkD,KAAA,WAAAjB,EAAAV,OAAArB,EAAA2B,EAAA7B,EAAAmD,OAAA,QAAAnD,EAAAwB,IAAAS,EAAAT,IAAA,YAAA6B,EAAA1D,EAAAE,GAAA,IAAAG,EAAAH,EAAAsD,OAAAjD,EAAAP,EAAAa,SAAAR,GAAA,GAAAE,IAAAN,EAAA,OAAAC,EAAAuD,SAAA,eAAApD,GAAAL,EAAAa,SAAAkD,SAAA7D,EAAAsD,OAAA,SAAAtD,EAAA2B,IAAA5B,EAAAyD,EAAA1D,EAAAE,GAAA,UAAAA,EAAAsD,SAAA,WAAAnD,IAAAH,EAAAsD,OAAA,QAAAtD,EAAA2B,IAAA,IAAAmC,UAAA,oCAAA3D,EAAA,aAAA8B,EAAA,IAAAzB,EAAAiB,EAAApB,EAAAP,EAAAa,SAAAX,EAAA2B,KAAA,aAAAnB,EAAAkB,KAAA,OAAA1B,EAAAsD,OAAA,QAAAtD,EAAA2B,IAAAnB,EAAAmB,IAAA3B,EAAAuD,SAAA,KAAAtB,EAAA,IAAAvB,EAAAF,EAAAmB,IAAA,OAAAjB,EAAAA,EAAA2C,MAAArD,EAAAF,EAAAiE,YAAArD,EAAAH,MAAAP,EAAAgE,KAAAlE,EAAAmE,QAAA,WAAAjE,EAAAsD,SAAAtD,EAAAsD,OAAA,OAAAtD,EAAA2B,IAAA5B,GAAAC,EAAAuD,SAAA,KAAAtB,GAAAvB,GAAAV,EAAAsD,OAAA,QAAAtD,EAAA2B,IAAA,IAAAmC,UAAA,oCAAA9D,EAAAuD,SAAA,KAAAtB,EAAA,UAAAiC,EAAAnE,GAAA,IAAAD,EAAA,CAAAqE,OAAApE,EAAA,SAAAA,IAAAD,EAAAsE,SAAArE,EAAA,SAAAA,IAAAD,EAAAuE,WAAAtE,EAAA,GAAAD,EAAAwE,SAAAvE,EAAA,SAAAwE,WAAAC,KAAA1E,EAAA,UAAA2E,EAAA1E,GAAA,IAAAD,EAAAC,EAAA2E,YAAA,GAAA5E,EAAA4B,KAAA,gBAAA5B,EAAA6B,IAAA5B,EAAA2E,WAAA5E,CAAA,UAAAyB,EAAAxB,GAAA,KAAAwE,WAAA,EAAAJ,OAAA,SAAApE,EAAA4C,QAAAuB,EAAA,WAAAS,OAAA,YAAAnC,EAAA1C,GAAA,GAAAA,GAAA,KAAAA,EAAA,KAAAE,EAAAF,EAAAY,GAAA,GAAAV,EAAA,OAAAA,EAAA4B,KAAA9B,GAAA,sBAAAA,EAAAkE,KAAA,OAAAlE,EAAA,IAAA8E,MAAA9E,EAAA+E,QAAA,KAAAxE,GAAA,EAAAG,EAAA,SAAAwD,IAAA,OAAA3D,EAAAP,EAAA+E,QAAA,GAAA1E,EAAAyB,KAAA9B,EAAAO,GAAA,OAAA2D,EAAAzD,MAAAT,EAAAO,GAAA2D,EAAAX,MAAA,EAAAW,EAAA,OAAAA,EAAAzD,MAAAR,EAAAiE,EAAAX,MAAA,EAAAW,CAAA,SAAAxD,EAAAwD,KAAAxD,CAAA,YAAAsD,UAAAf,EAAAjD,GAAA,2BAAAoC,EAAAhC,UAAAiC,EAAA9B,EAAAoC,EAAA,eAAAlC,MAAA4B,EAAAjB,cAAA,IAAAb,EAAA8B,EAAA,eAAA5B,MAAA2B,EAAAhB,cAAA,IAAAgB,EAAA4C,YAAA9D,EAAAmB,EAAArB,EAAA,qBAAAhB,EAAAiF,oBAAA,SAAAhF,GAAA,IAAAD,EAAA,mBAAAC,GAAAA,EAAAiF,YAAA,QAAAlF,IAAAA,IAAAoC,GAAA,uBAAApC,EAAAgF,aAAAhF,EAAAmF,MAAA,EAAAnF,EAAAoF,KAAA,SAAAnF,GAAA,OAAAE,OAAAkF,eAAAlF,OAAAkF,eAAApF,EAAAoC,IAAApC,EAAAqF,UAAAjD,EAAAnB,EAAAjB,EAAAe,EAAA,sBAAAf,EAAAG,UAAAD,OAAAqB,OAAAmB,GAAA1C,CAAA,EAAAD,EAAAuF,MAAA,SAAAtF,GAAA,OAAAkD,QAAAlD,EAAA,EAAA2C,EAAAG,EAAA3C,WAAAc,EAAA6B,EAAA3C,UAAAU,GAAA,0BAAAd,EAAA+C,cAAAA,EAAA/C,EAAAwF,MAAA,SAAAvF,EAAAC,EAAAG,EAAAE,EAAAG,QAAA,IAAAA,IAAAA,EAAA+E,SAAA,IAAA7E,EAAA,IAAAmC,EAAAzB,EAAArB,EAAAC,EAAAG,EAAAE,GAAAG,GAAA,OAAAV,EAAAiF,oBAAA/E,GAAAU,EAAAA,EAAAsD,OAAAd,MAAA,SAAAnD,GAAA,OAAAA,EAAAsD,KAAAtD,EAAAQ,MAAAG,EAAAsD,MAAA,KAAAtB,EAAAD,GAAAzB,EAAAyB,EAAA3B,EAAA,aAAAE,EAAAyB,EAAA/B,GAAA,0BAAAM,EAAAyB,EAAA,qDAAA3C,EAAA0F,KAAA,SAAAzF,GAAA,IAAAD,EAAAG,OAAAF,GAAAC,EAAA,WAAAG,KAAAL,EAAAE,EAAAwE,KAAArE,GAAA,OAAAH,EAAAyF,UAAA,SAAAzB,IAAA,KAAAhE,EAAA6E,QAAA,KAAA9E,EAAAC,EAAA0F,MAAA,GAAA3F,KAAAD,EAAA,OAAAkE,EAAAzD,MAAAR,EAAAiE,EAAAX,MAAA,EAAAW,CAAA,QAAAA,EAAAX,MAAA,EAAAW,CAAA,GAAAlE,EAAA0C,OAAAA,EAAAjB,EAAArB,UAAA,CAAA8E,YAAAzD,EAAAoD,MAAA,SAAA7E,GAAA,QAAA6F,KAAA,OAAA3B,KAAA,OAAAP,KAAA,KAAAC,MAAA3D,EAAA,KAAAsD,MAAA,OAAAE,SAAA,UAAAD,OAAA,YAAA3B,IAAA5B,EAAA,KAAAwE,WAAA5B,QAAA8B,IAAA3E,EAAA,QAAAE,KAAA,WAAAA,EAAA4F,OAAA,IAAAzF,EAAAyB,KAAA,KAAA5B,KAAA4E,OAAA5E,EAAA6F,MAAA,WAAA7F,GAAAD,EAAA,EAAA+F,KAAA,gBAAAzC,MAAA,MAAAtD,EAAA,KAAAwE,WAAA,GAAAG,WAAA,aAAA3E,EAAA2B,KAAA,MAAA3B,EAAA4B,IAAA,YAAAoE,IAAA,EAAApC,kBAAA,SAAA7D,GAAA,QAAAuD,KAAA,MAAAvD,EAAA,IAAAE,EAAA,cAAAgG,EAAA7F,EAAAE,GAAA,OAAAK,EAAAgB,KAAA,QAAAhB,EAAAiB,IAAA7B,EAAAE,EAAAgE,KAAA7D,EAAAE,IAAAL,EAAAsD,OAAA,OAAAtD,EAAA2B,IAAA5B,KAAAM,CAAA,SAAAA,EAAA,KAAAkE,WAAAM,OAAA,EAAAxE,GAAA,IAAAA,EAAA,KAAAG,EAAA,KAAA+D,WAAAlE,GAAAK,EAAAF,EAAAkE,WAAA,YAAAlE,EAAA2D,OAAA,OAAA6B,EAAA,UAAAxF,EAAA2D,QAAA,KAAAwB,KAAA,KAAA/E,EAAAT,EAAAyB,KAAApB,EAAA,YAAAM,EAAAX,EAAAyB,KAAApB,EAAA,iBAAAI,GAAAE,EAAA,SAAA6E,KAAAnF,EAAA4D,SAAA,OAAA4B,EAAAxF,EAAA4D,UAAA,WAAAuB,KAAAnF,EAAA6D,WAAA,OAAA2B,EAAAxF,EAAA6D,WAAA,SAAAzD,GAAA,QAAA+E,KAAAnF,EAAA4D,SAAA,OAAA4B,EAAAxF,EAAA4D,UAAA,YAAAtD,EAAA,MAAAsC,MAAA,kDAAAuC,KAAAnF,EAAA6D,WAAA,OAAA2B,EAAAxF,EAAA6D,WAAA,KAAAT,OAAA,SAAA7D,EAAAD,GAAA,QAAAE,EAAA,KAAAuE,WAAAM,OAAA,EAAA7E,GAAA,IAAAA,EAAA,KAAAK,EAAA,KAAAkE,WAAAvE,GAAA,GAAAK,EAAA8D,QAAA,KAAAwB,MAAAxF,EAAAyB,KAAAvB,EAAA,oBAAAsF,KAAAtF,EAAAgE,WAAA,KAAA7D,EAAAH,EAAA,OAAAG,IAAA,UAAAT,GAAA,aAAAA,IAAAS,EAAA2D,QAAArE,GAAAA,GAAAU,EAAA6D,aAAA7D,EAAA,UAAAE,EAAAF,EAAAA,EAAAkE,WAAA,UAAAhE,EAAAgB,KAAA3B,EAAAW,EAAAiB,IAAA7B,EAAAU,GAAA,KAAA8C,OAAA,YAAAU,KAAAxD,EAAA6D,WAAApC,GAAA,KAAAgE,SAAAvF,EAAA,EAAAuF,SAAA,SAAAlG,EAAAD,GAAA,aAAAC,EAAA2B,KAAA,MAAA3B,EAAA4B,IAAA,gBAAA5B,EAAA2B,MAAA,aAAA3B,EAAA2B,KAAA,KAAAsC,KAAAjE,EAAA4B,IAAA,WAAA5B,EAAA2B,MAAA,KAAAqE,KAAA,KAAApE,IAAA5B,EAAA4B,IAAA,KAAA2B,OAAA,cAAAU,KAAA,kBAAAjE,EAAA2B,MAAA5B,IAAA,KAAAkE,KAAAlE,GAAAmC,CAAA,EAAAiE,OAAA,SAAAnG,GAAA,QAAAD,EAAA,KAAAyE,WAAAM,OAAA,EAAA/E,GAAA,IAAAA,EAAA,KAAAE,EAAA,KAAAuE,WAAAzE,GAAA,GAAAE,EAAAqE,aAAAtE,EAAA,YAAAkG,SAAAjG,EAAA0E,WAAA1E,EAAAsE,UAAAG,EAAAzE,GAAAiC,CAAA,GAAAkE,MAAA,SAAApG,GAAA,QAAAD,EAAA,KAAAyE,WAAAM,OAAA,EAAA/E,GAAA,IAAAA,EAAA,KAAAE,EAAA,KAAAuE,WAAAzE,GAAA,GAAAE,EAAAmE,SAAApE,EAAA,KAAAI,EAAAH,EAAA0E,WAAA,aAAAvE,EAAAuB,KAAA,KAAArB,EAAAF,EAAAwB,IAAA8C,EAAAzE,EAAA,QAAAK,CAAA,QAAA+C,MAAA,0BAAAgD,cAAA,SAAAtG,EAAAE,EAAAG,GAAA,YAAAoD,SAAA,CAAA5C,SAAA6B,EAAA1C,GAAAiE,WAAA/D,EAAAiE,QAAA9D,GAAA,cAAAmD,SAAA,KAAA3B,IAAA5B,GAAAkC,CAAA,GAAAnC,CAAA,UAAAuG,EAAArG,EAAAU,IAAA,MAAAA,GAAAA,EAAAV,EAAA6E,UAAAnE,EAAAV,EAAA6E,QAAA,QAAA/E,EAAA,EAAAK,EAAAmG,MAAA5F,GAAAZ,EAAAY,EAAAZ,IAAAK,EAAAL,GAAAE,EAAAF,GAAA,OAAAK,CAAA,UAAAoG,EAAApG,EAAAJ,EAAAD,EAAAE,EAAAK,EAAAK,EAAAE,GAAA,QAAAJ,EAAAL,EAAAO,GAAAE,GAAAE,EAAAN,EAAAD,KAAA,OAAAJ,GAAA,YAAAL,EAAAK,EAAA,CAAAK,EAAA6C,KAAAtD,EAAAe,GAAAyE,QAAAvC,QAAAlC,GAAAoC,KAAAlD,EAAAK,EAAA,EADA,SAAamG,GACZ,IAAQC,EAAgBD,EAAGE,OAAnBD,YACRE,EAAwCH,EAAGI,KAAnCC,EAAMF,EAANE,OAAQC,EAAQH,EAARG,SAAUC,EAASJ,EAATI,UACpBC,EAAY,WAAH,OAASH,EAAQ,qBAAsBG,aAAe,EAAE,EAE/DC,EAAcT,EAAGU,MAAjBD,UACAE,EAAWC,OAAXD,OAyDR,SAOeE,EAAYC,EAAAC,GAAA,OAAAC,EAAAC,MAAC,KAADC,UAAA,CA0B3B,SAAAF,IA/FD,IAAArH,EA6FE,OA7FFA,EA+FCN,IAAAqF,MA1BA,SAAAyC,EACCC,EACAC,GAAY,IAAAC,EAAAC,EAAAC,EAAAN,UAAA,OAAA7H,IAAAuB,MAAA,SAAA6G,GAAA,cAAAA,EAAAtC,KAAAsC,EAAAjE,MAAA,OAIuD,GAHnE8D,EAAeE,EAAAnD,OAAA,QAAAqD,IAAAF,EAAA,GAAAA,EAAA,GAAG,GAClBD,EAAQC,EAAAnD,OAAA,QAAAqD,IAAAF,EAAA,GAAAA,EAAA,GAAG,GAESG,EAAiBnB,IAAaY,GAClC,CAAAK,EAAAjE,KAAA,eAAAiE,EAAAjE,KAAA,EACToE,EAAO,KAAM,OACnBC,EAAkBT,EAAiBC,EAAcC,GAAkBG,EAAAjE,KAAA,qBACxD+D,EAAW,IAAE,CAAAE,EAAAjE,KAAA,gBAAAiE,EAAAjE,KAAA,GAElBoE,EAAO,KAAM,eAAAH,EAAAjE,KAAA,GACbqD,EACLO,EACAC,EACAC,EACAC,EAAW,GACX,QAAAE,EAAAjE,KAAA,iBAEDsE,QAAQC,IACP,uDACC,yBAAAN,EAAAnC,OAAA,GAAA6B,EAAA,IAIJH,EA/FD,eAAAzH,EAAA,KAAAD,EAAA4H,UAAA,WAAAnC,SAAA,SAAAvF,EAAAK,GAAA,IAAAK,EAAAP,EAAAsH,MAAA1H,EAAAD,GAAA,SAAA0I,EAAArI,GAAAoG,EAAA7F,EAAAV,EAAAK,EAAAmI,EAAAC,EAAA,OAAAtI,EAAA,UAAAsI,EAAAtI,GAAAoG,EAAA7F,EAAAV,EAAAK,EAAAmI,EAAAC,EAAA,QAAAtI,EAAA,CAAAqI,OAAA,OA6FEhB,EAAAC,MAAA,KAAAC,UAAA,CAOD,SAASU,EAAOM,GACf,OAAO,IAAInD,SAAS,SAAEvC,GAAO,OAAM2F,WAAY3F,EAAS0F,EAAI,GAC7D,CAQA,SAASL,EACRT,EACAC,GAEC,IADDC,EAAeJ,UAAA7C,OAAA,QAAAqD,IAAAR,UAAA,GAAAA,UAAA,GAAG,GAEZkB,EAAcT,EAAiBnB,IAAaY,GAClD,GAAOgB,EAAP,CAKA,IAAMC,EAAchC,EAAQ,qBAAsBiC,SACjDF,EAAYG,UAEb,IACCF,EAAYG,YAAYC,MACvB,SAAEC,GAAK,OAAMA,EAAMjE,OAAS4C,CAAY,IAF1C,CASA,IAAIsB,EAAS,EACb,GAAyB,KAApBrB,EAAyB,CAE7B,IAAMsB,EAAcP,EAAYG,YAAYK,WAC3C,SAAEH,GAAK,OAAMA,EAAMjE,OAAS6C,CAAe,IAE5CqB,EAASN,EAAYG,YAAYnE,QAAWuE,EAAc,EAC3D,CAEA,IAAME,EAAW7C,EAAaoB,GAG9Bf,EAAU,qBAAsByC,YAC/BD,EACAT,EAAYG,YAAYnE,OAASsE,EACjCN,EAAYE,UAIbjC,EAAU,qBAAsB0C,sBAC/BF,EAASP,SACT,CACCU,KAAM,CAAEC,QAAQ,IAxBlB,CAZA,CAuCD,CAQA,SAASvB,EAAiBzB,EAAQiD,GAAY,IAClBC,EADkBC,EAtK/C,SAAA7J,EAAAF,GAAA,IAAAC,EAAA,oBAAAU,QAAAT,EAAAS,OAAAE,WAAAX,EAAA,kBAAAD,EAAA,IAAAuG,MAAAwD,QAAA9J,KAAAD,EAAA,SAAAC,EAAAU,GAAA,GAAAV,EAAA,qBAAAA,EAAA,OAAAqG,EAAArG,EAAAU,GAAA,IAAAX,EAAA,GAAAgK,SAAAnI,KAAA5B,GAAA6F,MAAA,uBAAA9F,GAAAC,EAAAgF,cAAAjF,EAAAC,EAAAgF,YAAAC,MAAA,QAAAlF,GAAA,QAAAA,EAAAuG,MAAA0D,KAAAhK,GAAA,cAAAD,GAAA,2CAAAkK,KAAAlK,GAAAsG,EAAArG,EAAAU,QAAA,GAAAwJ,CAAAlK,KAAAF,GAAAE,GAAA,iBAAAA,EAAA6E,OAAA,CAAA9E,IAAAC,EAAAD,GAAA,IAAAoK,EAAA,EAAAC,EAAA,oBAAApI,EAAAoI,EAAAjK,EAAA,kBAAAgK,GAAAnK,EAAA6E,OAAA,CAAAxB,MAAA,IAAAA,MAAA,EAAA9C,MAAAP,EAAAmK,KAAA,EAAArK,EAAA,SAAAE,GAAA,MAAAA,CAAA,EAAA+B,EAAAqI,EAAA,WAAAtG,UAAA,6IAAAzD,EAAAK,GAAA,EAAAI,GAAA,SAAAkB,EAAA,WAAAjC,EAAAA,EAAA6B,KAAA5B,EAAA,EAAAG,EAAA,eAAAH,EAAAD,EAAAiE,OAAA,OAAAtD,EAAAV,EAAAqD,KAAArD,CAAA,EAAAF,EAAA,SAAAE,GAAAc,GAAA,EAAAT,EAAAL,CAAA,EAAA+B,EAAA,eAAArB,GAAA,MAAAX,EAAA8D,QAAA9D,EAAA8D,QAAA,YAAA/C,EAAA,MAAAT,CAAA,IAsK+CgK,CACxB3D,GAAM,IAA3B,IAAAmD,EAAA7H,MAAA4H,EAAAC,EAAA1J,KAAAkD,MAA8B,KAAlB6F,EAAKU,EAAArJ,MAChB,GAAK2I,EAAMjE,OAAS0E,EACnB,OAAOT,EAER,GAAKA,EAAMF,YAAYnE,OAAS,EAAI,CACnC,IAAMyF,EAAanC,EAClBe,EAAMF,YACNW,GAED,GAAKW,EACJ,OAAOA,CAET,CACD,CAAC,OAAAC,GAAAV,EAAA/J,EAAAyK,EAAA,SAAAV,EAAA9H,GAAA,CACD,OAAO,IACR,CA9KAkF,EACC,2BACA,6DACA,SAAEuD,EAAUvF,GACX,GACU,uDAATA,EACC,CACD,IAAMwF,EAAgBtD,EAAQ,CAAC,EAAGqD,EAASE,WAAY,CACtDjB,KAAMtC,EAAQ,CAAC,EAAGqD,EAASE,WAAWjB,KAAM,CAC3CkB,QAASxD,EAAQ,CAAC,EAAGqD,EAASE,WAAWjB,KAAKkB,QAAS,CACtDjB,QAAQ,QAKX,OAAOvC,EAAQ,CAAC,EAAGqD,EAAU,CAC5BE,WAAYD,GAEd,CACA,OAAOD,CACR,IAMDzD,GAAW,WACYC,IAERrE,SAAS,SAAEuG,GACJ,qBAAfA,EAAMjE,OAWUkD,EAVOe,EAWlBF,YACV,uDAGA3B,EACC,gCACA,qDACA,wCAhBF,GACD,GA6IA,CAxLD,CAwLKuD,OAAOpE", "sources": ["webpack://ppcp-paylater-wc-blocks/./resources/js/CartPayLaterMessagesBlock/cart-paylater-block-inserter.js"], "sourcesContent": ["( function ( wp ) {\n\tconst { createBlock } = wp.blocks;\n\tconst { select, dispatch, subscribe } = wp.data;\n\tconst getBlocks = () => select( 'core/block-editor' ).getBlocks() || [];\n\n\tconst { addFilter } = wp.hooks;\n\tconst { assign } = lodash;\n\n\t// We need to make sure the block is unlocked so that it doesn't get automatically inserted as the last block\n\taddFilter(\n\t\t'blocks.registerBlockType',\n\t\t'woocommerce-paypal-payments/modify-cart-paylater-messages',\n\t\t( settings, name ) => {\n\t\t\tif (\n\t\t\t\tname === 'woocommerce-paypal-payments/cart-paylater-messages'\n\t\t\t) {\n\t\t\t\tconst newAttributes = assign( {}, settings.attributes, {\n\t\t\t\t\tlock: assign( {}, settings.attributes.lock, {\n\t\t\t\t\t\tdefault: assign( {}, settings.attributes.lock.default, {\n\t\t\t\t\t\t\tremove: false,\n\t\t\t\t\t\t} ),\n\t\t\t\t\t} ),\n\t\t\t\t} );\n\n\t\t\t\treturn assign( {}, settings, {\n\t\t\t\t\tattributes: newAttributes,\n\t\t\t\t} );\n\t\t\t}\n\t\t\treturn settings;\n\t\t}\n\t);\n\n\t/**\n\t * Subscribes to changes in the block editor, specifically checking for the presence of 'woocommerce/cart'.\n\t */\n\tsubscribe( () => {\n\t\tconst currentBlocks = getBlocks();\n\n\t\tcurrentBlocks.forEach( ( block ) => {\n\t\t\tif ( block.name === 'woocommerce/cart' ) {\n\t\t\t\tensurePayLaterBlockExists( block );\n\t\t\t}\n\t\t} );\n\t} );\n\n\t/**\n\t * Ensures the 'woocommerce-paypal-payments/cart-paylater-messages' block exists inside the 'woocommerce/cart' block.\n\t * @param {Object} cartBlock - The cart block instance.\n\t */\n\tfunction ensurePayLaterBlockExists( cartBlock ) {\n\t\tconst payLaterBlock = findBlockByName(\n\t\t\tcartBlock.innerBlocks,\n\t\t\t'woocommerce-paypal-payments/cart-paylater-messages'\n\t\t);\n\t\tif ( ! payLaterBlock ) {\n\t\t\twaitForBlock(\n\t\t\t\t'woocommerce/cart-totals-block',\n\t\t\t\t'woocommerce-paypal-payments/cart-paylater-messages',\n\t\t\t\t'woocommerce/cart-order-summary-block'\n\t\t\t);\n\t\t}\n\t}\n\n\t/**\n\t * Waits for a specific block to appear using async/await pattern before executing the insertBlockAfter function.\n\t * @param {string} targetBlockName - Name of the block to wait for.\n\t * @param {string} newBlockName    - Name of the new block to insert after the target.\n\t * @param {string} anchorBlockName - Name of the anchor block to determine position.\n\t * @param {number} attempts        - The number of attempts made to find the target block.\n\t */\n\tasync function waitForBlock(\n\t\ttargetBlockName,\n\t\tnewBlockName,\n\t\tanchorBlockName = '',\n\t\tattempts = 0\n\t) {\n\t\tconst targetBlock = findBlockByName( getBlocks(), targetBlockName );\n\t\tif ( targetBlock ) {\n\t\t\tawait delay( 1000 ); // We need this to ensure the block is fully rendered\n\t\t\tinsertBlockAfter( targetBlockName, newBlockName, anchorBlockName );\n\t\t} else if ( attempts < 10 ) {\n\t\t\t// Poll up to 10 times\n\t\t\tawait delay( 1000 ); // Wait 1 second before retrying\n\t\t\tawait waitForBlock(\n\t\t\t\ttargetBlockName,\n\t\t\t\tnewBlockName,\n\t\t\t\tanchorBlockName,\n\t\t\t\tattempts + 1\n\t\t\t);\n\t\t} else {\n\t\t\tconsole.log(\n\t\t\t\t'Failed to find target block after several attempts.'\n\t\t\t);\n\t\t}\n\t}\n\n\t/**\n\t * Delays execution by a given number of milliseconds.\n\t * @param {number} ms - Milliseconds to delay.\n\t * @return {Promise} A promise that resolves after the delay.\n\t */\n\tfunction delay( ms ) {\n\t\treturn new Promise( ( resolve ) => setTimeout( resolve, ms ) );\n\t}\n\n\t/**\n\t * Inserts a block after a specified block if it doesn't already exist.\n\t * @param {string} targetBlockName - Name of the block to find.\n\t * @param {string} newBlockName    - Name of the new block to insert.\n\t * @param {string} anchorBlockName - Name of the anchor block to determine position.\n\t */\n\tfunction insertBlockAfter(\n\t\ttargetBlockName,\n\t\tnewBlockName,\n\t\tanchorBlockName = ''\n\t) {\n\t\tconst targetBlock = findBlockByName( getBlocks(), targetBlockName );\n\t\tif ( ! targetBlock ) {\n\t\t\t// Target block not found\n\t\t\treturn;\n\t\t}\n\n\t\tconst parentBlock = select( 'core/block-editor' ).getBlock(\n\t\t\ttargetBlock.clientId\n\t\t);\n\t\tif (\n\t\t\tparentBlock.innerBlocks.some(\n\t\t\t\t( block ) => block.name === newBlockName\n\t\t\t)\n\t\t) {\n\t\t\t// The block is already inserted next to the target block\n\t\t\treturn;\n\t\t}\n\n\t\tlet offset = 0;\n\t\tif ( anchorBlockName !== '' ) {\n\t\t\t// Find the anchor block and calculate the offset\n\t\t\tconst anchorIndex = parentBlock.innerBlocks.findIndex(\n\t\t\t\t( block ) => block.name === anchorBlockName\n\t\t\t);\n\t\t\toffset = parentBlock.innerBlocks.length - ( anchorIndex + 1 );\n\t\t}\n\n\t\tconst newBlock = createBlock( newBlockName );\n\n\t\t// Insert the block at the correct position\n\t\tdispatch( 'core/block-editor' ).insertBlock(\n\t\t\tnewBlock,\n\t\t\tparentBlock.innerBlocks.length - offset,\n\t\t\tparentBlock.clientId\n\t\t);\n\n\t\t// Lock the block after it has been inserted\n\t\tdispatch( 'core/block-editor' ).updateBlockAttributes(\n\t\t\tnewBlock.clientId,\n\t\t\t{\n\t\t\t\tlock: { remove: true },\n\t\t\t}\n\t\t);\n\t}\n\n\t/**\n\t * Recursively searches for a block by name among all blocks.\n\t * @param {Array}  blocks    - The array of blocks to search.\n\t * @param {string} blockName - The name of the block to find.\n\t * @return {Object|null} The found block, or null if not found.\n\t */\n\tfunction findBlockByName( blocks, blockName ) {\n\t\tfor ( const block of blocks ) {\n\t\t\tif ( block.name === blockName ) {\n\t\t\t\treturn block;\n\t\t\t}\n\t\t\tif ( block.innerBlocks.length > 0 ) {\n\t\t\t\tconst foundBlock = findBlockByName(\n\t\t\t\t\tblock.innerBlocks,\n\t\t\t\t\tblockName\n\t\t\t\t);\n\t\t\t\tif ( foundBlock ) {\n\t\t\t\t\treturn foundBlock;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\treturn null;\n\t}\n} )( window.wp );\n"], "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "_typeof", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "<PERSON><PERSON><PERSON>", "_arrayLikeToArray", "Array", "asyncGeneratorStep", "wp", "createBlock", "blocks", "_wp$data", "data", "select", "dispatch", "subscribe", "getBlocks", "addFilter", "hooks", "assign", "lodash", "waitForBlock", "_x", "_x2", "_waitFor<PERSON>lock", "apply", "arguments", "_callee", "targetBlockName", "newBlockName", "anchorBlockName", "attempts", "_args", "_context", "undefined", "findBlockByName", "delay", "insertBlockAfter", "console", "log", "_next", "_throw", "ms", "setTimeout", "targetBlock", "parentBlock", "getBlock", "clientId", "innerBlocks", "some", "block", "offset", "anchorIndex", "findIndex", "newBlock", "insertBlock", "updateBlockAttributes", "lock", "remove", "blockName", "_step", "_iterator", "isArray", "toString", "from", "test", "_unsupportedIterableToArray", "_n", "F", "_createForOfIteratorHelper", "found<PERSON>lock", "err", "settings", "newAttributes", "attributes", "default", "window"], "sourceRoot": ""}