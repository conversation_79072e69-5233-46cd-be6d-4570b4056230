{"version": 3, "file": "js/edit.js", "mappings": "mBACA,IAAIA,EAAsB,CAAC,ECD3BA,EAAoBC,EAAI,WACvB,GAA0B,iBAAfC,WAAyB,OAAOA,WAC3C,IACC,OAAOC,MAAQ,IAAIC,SAAS,cAAb,EAChB,CAAE,MAAOC,GACR,GAAsB,iBAAXC,OAAqB,OAAOA,MACxC,CACA,CAPuB,G,MCAxB,IAAIC,EACAP,EAAoBC,EAAEO,gBAAeD,EAAYP,EAAoBC,EAAEQ,SAAW,IACtF,IAAIC,EAAWV,EAAoBC,EAAES,SACrC,IAAKH,GAAaG,IACbA,EAASC,eAAkE,WAAjDD,EAASC,cAAcC,QAAQC,gBAC5DN,EAAYG,EAASC,cAAcG,MAC/BP,GAAW,CACf,IAAIQ,EAAUL,EAASM,qBAAqB,UAC5C,GAAGD,EAAQE,OAEV,IADA,IAAIC,EAAIH,EAAQE,OAAS,EAClBC,GAAK,KAAOX,IAAc,aAAaY,KAAKZ,KAAaA,EAAYQ,EAAQG,KAAKJ,GAE3F,CAID,IAAKP,EAAW,MAAM,IAAIa,MAAM,yDAChCb,EAAYA,EAAUc,QAAQ,OAAQ,IAAIA,QAAQ,QAAS,IAAIA,QAAQ,YAAa,KACpFrB,EAAoBsB,EAAIf,EAAY,K,KClBrB,G", "sources": ["webpack://ppcp-paylater-block/webpack/bootstrap", "webpack://ppcp-paylater-block/webpack/runtime/global", "webpack://ppcp-paylater-block/webpack/runtime/publicPath", "webpack://ppcp-paylater-block/./resources/css/edit.scss"], "sourcesContent": ["// The require scope\nvar __webpack_require__ = {};\n\n", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "var scriptUrl;\nif (__webpack_require__.g.importScripts) scriptUrl = __webpack_require__.g.location + \"\";\nvar document = __webpack_require__.g.document;\nif (!scriptUrl && document) {\n\tif (document.currentScript && document.currentScript.tagName.toUpperCase() === 'SCRIPT')\n\t\tscriptUrl = document.currentScript.src;\n\tif (!scriptUrl) {\n\t\tvar scripts = document.getElementsByTagName(\"script\");\n\t\tif(scripts.length) {\n\t\t\tvar i = scripts.length - 1;\n\t\t\twhile (i > -1 && (!scriptUrl || !/^http(s?):/.test(scriptUrl))) scriptUrl = scripts[i--].src;\n\t\t}\n\t}\n}\n// When supporting browsers where an automatic publicPath is not supported you must specify an output.publicPath manually via configuration\n// or pass an empty string (\"\") and set the __webpack_public_path__ variable from your code to use your own logic.\nif (!scriptUrl) throw new Error(\"Automatic publicPath is not supported in this browser\");\nscriptUrl = scriptUrl.replace(/#.*$/, \"\").replace(/\\?.*$/, \"\").replace(/\\/[^\\/]+$/, \"/\");\n__webpack_require__.p = scriptUrl + \"../\";", "export default __webpack_public_path__ + \"css/edit.css\";"], "names": ["__webpack_require__", "g", "globalThis", "this", "Function", "e", "window", "scriptUrl", "importScripts", "location", "document", "currentScript", "tagName", "toUpperCase", "src", "scripts", "getElementsByTagName", "length", "i", "test", "Error", "replace", "p"], "sourceRoot": ""}