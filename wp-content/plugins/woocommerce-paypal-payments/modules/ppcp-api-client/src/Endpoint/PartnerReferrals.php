<?php

/**
 * The partner referrals endpoint.
 *
 * @package WooCommerce\PayPalCommerce\ApiClient\Endpoint
 */
declare (strict_types=1);
namespace WooCommerce\PayPalCommerce\ApiClient\Endpoint;

use WooCommerce\PayPalCommerce\ApiClient\Authentication\Bearer;
use WooCommerce\PayPalCommerce\ApiClient\Exception\PayPalApiException;
use WooCommerce\PayPalCommerce\ApiClient\Exception\RuntimeException;
use WooCommerce\PayPalCommerce\Vendor\Psr\Log\LoggerInterface;
/**
 * Class PartnerReferrals
 *
 * @see https://developer.paypal.com/docs/api/partner-referrals/v2/
 */
class PartnerReferrals
{
    use \WooCommerce\PayPalCommerce\ApiClient\Endpoint\RequestTrait;
    /**
     * The host.
     *
     * @var string
     */
    private $host;
    /**
     * The bearer.
     *
     * @var Bearer
     */
    private $bearer;
    /**
     * The logger.
     *
     * @var LoggerInterface
     */
    private $logger;
    /**
     * PartnerReferrals constructor.
     *
     * @param string          $host The host.
     * @param Bearer          $bearer The bearer.
     * @param LoggerInterface $logger The logger.
     */
    public function __construct(string $host, Bearer $bearer, LoggerInterface $logger)
    {
        $this->host = $host;
        $this->bearer = $bearer;
        $this->logger = $logger;
    }
    /**
     * Fetch the signup link.
     *
     * @param array $data The partner referrals data.
     * @return string
     * @throws RuntimeException If the request fails.
     */
    public function signup_link(array $data): string
    {
        $bearer = $this->bearer->bearer();
        $args = array('method' => 'POST', 'headers' => array('Authorization' => 'Bearer ' . $bearer->token(), 'Content-Type' => 'application/json', 'Prefer' => 'return=representation'), 'body' => wp_json_encode($data));
        $url = trailingslashit($this->host) . 'v2/customer/partner-referrals';
        $response = $this->request($url, $args);
        if (is_wp_error($response)) {
            $error = new RuntimeException(__('Could not create referral.', 'woocommerce-paypal-payments'));
            $this->logger->warning($error->getMessage(), array('args' => $args, 'response' => $response));
            throw $error;
        }
        $json = json_decode($response['body']);
        $status_code = (int) wp_remote_retrieve_response_code($response);
        if (201 !== $status_code) {
            $error = new PayPalApiException($json, $status_code);
            $this->logger->warning($error->getMessage(), array('args' => $args, 'response' => $response));
            throw $error;
        }
        foreach ($json->links as $link) {
            if ('action_url' === $link->rel) {
                return (string) $link->href;
            }
        }
        $error = new RuntimeException(__('Action URL not found.', 'woocommerce-paypal-payments'));
        $this->logger->warning($error->getMessage(), array('args' => $args, 'response' => $response));
        throw $error;
    }
}
