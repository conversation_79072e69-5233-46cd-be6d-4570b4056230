<?php

/**
 * A Collection of all error responses for the order endpoint.
 *
 * @package WooCommerce\PayPalCommerce\ApiClient\Helper
 */
declare (strict_types=1);
namespace WooCommerce\PayPalCommerce\ApiClient\Helper;

/**
 * Class ErrorResponse
 */
class ErrorResponse
{
    const UNKNOWN = 'UNKNOWN';
    /* Order error codes */
    const ACTION_DOES_NOT_MATCH_INTENT = 'ACTION_DOES_NOT_MATCH_INTENT';
    const AGREEMENT_ALREADY_CANCELLED = 'AGREEMENT_ALREADY_CANCELLED';
    const AMOUNT_CANNOT_BE_SPECIFIED = 'AMOUNT_CANNOT_BE_SPECIFIED';
    const AMOUNT_MISMATCH = 'AMOUNT_MISMATCH';
    const AMOUNT_NOT_PATCHABLE = 'AMOUNT_NOT_PATCHABLE';
    const AUTH_CAPTURE_NOT_ENABLED = 'AUTH_CAPTURE_NOT_ENABLED';
    const AUTHENTICATION_FAILURE = 'AUTHENTICATION_FAILURE';
    const AUTHORIZATION_AMOUNT_EXCEEDED = 'AUTHORIZATION_AMOUNT_EXCEEDED';
    const AUTHORIZATION_CURRENCY_MISMATCH = 'AUTHORIZATION_CURRENCY_MISMATCH';
    const BILLING_AGREEMENT_NOT_FOUND = 'BILLING_AGREEMENT_NOT_FOUND';
    const CANNOT_BE_NEGATIVE = 'CANNOT_BE_NEGATIVE';
    const CANNOT_BE_ZERO_OR_NEGATIVE = 'CANNOT_BE_ZERO_OR_NEGATIVE';
    const CARD_TYPE_NOT_SUPPORTED = 'CARD_TYPE_NOT_SUPPORTED';
    const INVALID_SECURITY_CODE_LENGTH = 'INVALID_SECURITY_CODE_LENGTH';
    const CITY_REQUIRED = 'CITY_REQUIRED';
    const COMPLIANCE_VIOLATION = 'COMPLIANCE_VIOLATION';
    const CONSENT_NEEDED = 'CONSENT_NEEDED';
    const CURRENCY_NOT_SUPPORTED_FOR_CARD_TYPE = 'CURRENCY_NOT_SUPPORTED_FOR_CARD_TYPE';
    const CURRENCY_NOT_SUPPORTED_FOR_COUNTRY = 'CURRENCY_NOT_SUPPORTED_FOR_COUNTRY';
    const DECIMAL_PRECISION = 'DECIMAL_PRECISION';
    const DOMESTIC_TRANSACTION_REQUIRED = 'DOMESTIC_TRANSACTION_REQUIRED';
    const DUPLICATE_INVOICE_ID = 'DUPLICATE_INVOICE_ID';
    const DUPLICATE_REQUEST_ID = 'DUPLICATE_REQUEST_ID';
    const FIELD_NOT_PATCHABLE = 'FIELD_NOT_PATCHABLE';
    const INSTRUMENT_DECLINED = 'INSTRUMENT_DECLINED';
    const INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR';
    const INTERNAL_SERVICE_ERROR = 'INTERNAL_SERVICE_ERROR';
    const INVALID_ACCOUNT_STATUS = 'INVALID_ACCOUNT_STATUS';
    const INVALID_ARRAY_MAX_ITEMS = 'INVALID_ARRAY_MAX_ITEMS';
    const INVALID_ARRAY_MIN_ITEMS = 'INVALID_ARRAY_MIN_ITEMS';
    const INVALID_COUNTRY_CODE = 'INVALID_COUNTRY_CODE';
    const INVALID_CURRENCY_CODE = 'INVALID_CURRENCY_CODE';
    const INVALID_JSON_POINTER_FORMAT = 'INVALID_JSON_POINTER_FORMAT';
    const INVALID_PARAMETER_SYNTAX = 'INVALID_PARAMETER_SYNTAX';
    const INVALID_PARAMETER_VALUE = 'INVALID_PARAMETER_VALUE';
    const INVALID_PARAMETER = 'INVALID_PARAMETER';
    const INVALID_PATCH_OPERATION = 'INVALID_PATCH_OPERATION';
    const INVALID_PAYER_ID = 'INVALID_PAYER_ID';
    const INVALID_RESOURCE_ID = 'INVALID_RESOURCE_ID';
    const INVALID_STRING_LENGTH = 'INVALID_STRING_LENGTH';
    const ITEM_TOTAL_MISMATCH = 'ITEM_TOTAL_MISMATCH';
    const ITEM_TOTAL_REQUIRED = 'ITEM_TOTAL_REQUIRED';
    const MAX_AUTHORIZATION_COUNT_EXCEEDED = 'MAX_AUTHORIZATION_COUNT_EXCEEDED';
    const MAX_NUMBER_OF_PAYMENT_ATTEMPTS_EXCEEDED = 'MAX_NUMBER_OF_PAYMENT_ATTEMPTS_EXCEEDED';
    const MAX_VALUE_EXCEEDED = 'MAX_VALUE_EXCEEDED';
    const MISSING_REQUIRED_PARAMETER = 'MISSING_REQUIRED_PARAMETER';
    const MISSING_SHIPPING_ADDRESS = 'MISSING_SHIPPING_ADDRESS';
    const MULTI_CURRENCY_ORDER = 'MULTI_CURRENCY_ORDER';
    const MULTIPLE_SHIPPING_ADDRESS_NOT_SUPPORTED = 'MULTIPLE_SHIPPING_ADDRESS_NOT_SUPPORTED';
    const MULTIPLE_SHIPPING_OPTION_SELECTED = 'MULTIPLE_SHIPPING_OPTION_SELECTED';
    const INVALID_PICKUP_ADDRESS = 'INVALID_PICKUP_ADDRESS';
    const NOT_AUTHORIZED = 'NOT_AUTHORIZED';
    const NOT_ENABLED_FOR_CARD_PROCESSING = 'NOT_ENABLED_FOR_CARD_PROCESSING';
    const NOT_PATCHABLE = 'NOT_PATCHABLE';
    const NOT_SUPPORTED = 'NOT_SUPPORTED';
    const ORDER_ALREADY_AUTHORIZED = 'ORDER_ALREADY_AUTHORIZED';
    const ORDER_ALREADY_CAPTURED = 'ORDER_ALREADY_CAPTURED';
    const ORDER_ALREADY_COMPLETED = 'ORDER_ALREADY_COMPLETED';
    const ORDER_CANNOT_BE_SAVED = 'ORDER_CANNOT_BE_SAVED';
    const ORDER_COMPLETED_OR_VOIDED = 'ORDER_COMPLETED_OR_VOIDED';
    const ORDER_EXPIRED = 'ORDER_EXPIRED';
    const ORDER_NOT_APPROVED = 'ORDER_NOT_APPROVED';
    const ORDER_NOT_SAVED = 'ORDER_NOT_SAVED';
    const ORDER_PREVIOUSLY_VOIDED = 'ORDER_PREVIOUSLY_VOIDED';
    const PARAMETER_VALUE_NOT_SUPPORTED = 'PARAMETER_VALUE_NOT_SUPPORTED';
    const PATCH_PATH_REQUIRED = 'PATCH_PATH_REQUIRED';
    const PATCH_VALUE_REQUIRED = 'PATCH_VALUE_REQUIRED';
    const PAYEE_ACCOUNT_INVALID = 'PAYEE_ACCOUNT_INVALID';
    const PAYEE_ACCOUNT_LOCKED_OR_CLOSED = 'PAYEE_ACCOUNT_LOCKED_OR_CLOSED';
    const PAYEE_ACCOUNT_RESTRICTED = 'PAYEE_ACCOUNT_RESTRICTED';
    const PAYEE_BLOCKED_TRANSACTION = 'PAYEE_BLOCKED_TRANSACTION';
    const PAYER_ACCOUNT_LOCKED_OR_CLOSED = 'PAYER_ACCOUNT_LOCKED_OR_CLOSED';
    const PAYER_ACCOUNT_RESTRICTED = 'PAYER_ACCOUNT_RESTRICTED';
    const PAYER_CANNOT_PAY = 'PAYER_CANNOT_PAY';
    const PAYER_CONSENT_REQUIRED = 'PAYER_CONSENT_REQUIRED';
    const PAYER_COUNTRY_NOT_SUPPORTED = 'PAYER_COUNTRY_NOT_SUPPORTED';
    const PAYEE_NOT_ENABLED_FOR_CARD_PROCESSING = 'PAYEE_NOT_ENABLED_FOR_CARD_PROCESSING';
    const PAYMENT_INSTRUCTION_REQUIRED = 'PAYMENT_INSTRUCTION_REQUIRED';
    const PERMISSION_DENIED = 'PERMISSION_DENIED';
    const POSTAL_CODE_REQUIRED = 'POSTAL_CODE_REQUIRED';
    const PREFERRED_SHIPPING_OPTION_AMOUNT_MISMATCH = 'PREFERRED_SHIPPING_OPTION_AMOUNT_MISMATCH';
    const REDIRECT_PAYER_FOR_ALTERNATE_FUNDING = 'REDIRECT_PAYER_FOR_ALTERNATE_FUNDING';
    const REFERENCE_ID_NOT_FOUND = 'REFERENCE_ID_NOT_FOUND';
    const REFERENCE_ID_REQUIRED = 'REFERENCE_ID_REQUIRED';
    const DUPLICATE_REFERENCE_ID = 'DUPLICATE_REFERENCE_ID';
    const SHIPPING_ADDRESS_INVALID = 'SHIPPING_ADDRESS_INVALID';
    const SHIPPING_OPTION_NOT_SELECTED = 'SHIPPING_OPTION_NOT_SELECTED';
    const SHIPPING_OPTIONS_NOT_SUPPORTED = 'SHIPPING_OPTIONS_NOT_SUPPORTED';
    const TAX_TOTAL_MISMATCH = 'TAX_TOTAL_MISMATCH';
    const TAX_TOTAL_REQUIRED = 'TAX_TOTAL_REQUIRED';
    const TRANSACTION_AMOUNT_EXCEEDS_MONTHLY_MAX_LIMIT = 'TRANSACTION_AMOUNT_EXCEEDS_MONTHLY_MAX_LIMIT';
    const TRANSACTION_BLOCKED_BY_PAYEE = 'TRANSACTION_BLOCKED_BY_PAYEE';
    const TRANSACTION_LIMIT_EXCEEDED = 'TRANSACTION_LIMIT_EXCEEDED';
    const TRANSACTION_RECEIVING_LIMIT_EXCEEDED = 'TRANSACTION_RECEIVING_LIMIT_EXCEEDED';
    const TRANSACTION_REFUSED = 'TRANSACTION_REFUSED';
    const UNSUPPORTED_INTENT = 'UNSUPPORTED_INTENT';
    const UNSUPPORTED_PATCH_PARAMETER_VALUE = 'UNSUPPORTED_PATCH_PARAMETER_VALUE';
    const UNSUPPORTED_PAYMENT_INSTRUCTION = 'UNSUPPORTED_PAYMENT_INSTRUCTION';
    const PAYEE_ACCOUNT_NOT_SUPPORTED = 'PAYEE_ACCOUNT_NOT_SUPPORTED';
    const PAYEE_ACCOUNT_NOT_VERIFIED = 'PAYEE_ACCOUNT_NOT_VERIFIED';
    const PAYEE_NOT_CONSENTED = 'PAYEE_NOT_CONSENTED';
}
