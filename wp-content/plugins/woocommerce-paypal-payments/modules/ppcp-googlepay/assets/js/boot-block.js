/*! For license information please see boot-block.js.LICENSE.txt */
(()=>{"use strict";var t={9457:t=>{var e=function(t){return function(t){return!!t&&"object"==typeof t}(t)&&!function(t){var e=Object.prototype.toString.call(t);return"[object RegExp]"===e||"[object Date]"===e||function(t){return t.$$typeof===r}(t)}(t)},r="function"==typeof Symbol&&Symbol.for?Symbol.for("react.element"):60103;function n(t,e){return!1!==e.clone&&e.isMergeableObject(t)?c((r=t,Array.isArray(r)?[]:{}),t,e):t;var r}function o(t,e,r){return t.concat(e).map((function(t){return n(t,r)}))}function i(t){return Object.keys(t).concat(function(t){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(t).filter((function(e){return Object.propertyIsEnumerable.call(t,e)})):[]}(t))}function a(t,e){try{return e in t}catch(t){return!1}}function c(t,r,u){(u=u||{}).arrayMerge=u.arrayMerge||o,u.isMergeableObject=u.isMergeableObject||e,u.cloneUnlessOtherwiseSpecified=n;var s=Array.isArray(r);return s===Array.isArray(t)?s?u.arrayMerge(t,r,u):function(t,e,r){var o={};return r.isMergeableObject(t)&&i(t).forEach((function(e){o[e]=n(t[e],r)})),i(e).forEach((function(i){(function(t,e){return a(t,e)&&!(Object.hasOwnProperty.call(t,e)&&Object.propertyIsEnumerable.call(t,e))})(t,i)||(a(t,i)&&r.isMergeableObject(e[i])?o[i]=function(t,e){if(!e.customMerge)return c;var r=e.customMerge(t);return"function"==typeof r?r:c}(i,r)(t[i],e[i],r):o[i]=n(e[i],r))})),o}(t,r,u):n(r,u)}c.all=function(t,e){if(!Array.isArray(t))throw new Error("first argument should be an array");return t.reduce((function(t,r){return c(t,r,e)}),{})};var u=c;t.exports=u},9306:(t,e,r)=>{var n=r(4901),o=r(6823),i=TypeError;t.exports=function(t){if(n(t))return t;throw new i(o(t)+" is not a function")}},5548:(t,e,r)=>{var n=r(3517),o=r(6823),i=TypeError;t.exports=function(t){if(n(t))return t;throw new i(o(t)+" is not a constructor")}},3506:(t,e,r)=>{var n=r(3925),o=String,i=TypeError;t.exports=function(t){if(n(t))return t;throw new i("Can't set "+o(t)+" as a prototype")}},6469:(t,e,r)=>{var n=r(8227),o=r(2360),i=r(4913).f,a=n("unscopables"),c=Array.prototype;void 0===c[a]&&i(c,a,{configurable:!0,value:o(null)}),t.exports=function(t){c[a][t]=!0}},7829:(t,e,r)=>{var n=r(8183).charAt;t.exports=function(t,e,r){return e+(r?n(t,e).length:1)}},679:(t,e,r)=>{var n=r(1625),o=TypeError;t.exports=function(t,e){if(n(e,t))return t;throw new o("Incorrect invocation")}},8551:(t,e,r)=>{var n=r(34),o=String,i=TypeError;t.exports=function(t){if(n(t))return t;throw new i(o(t)+" is not an object")}},5652:(t,e,r)=>{var n=r(9039);t.exports=n((function(){if("function"==typeof ArrayBuffer){var t=new ArrayBuffer(8);Object.isExtensible(t)&&Object.defineProperty(t,"a",{value:8})}}))},235:(t,e,r)=>{var n=r(9213).forEach,o=r(4598)("forEach");t.exports=o?[].forEach:function(t){return n(this,t,arguments.length>1?arguments[1]:void 0)}},7916:(t,e,r)=>{var n=r(6080),o=r(9565),i=r(8981),a=r(6319),c=r(4209),u=r(3517),s=r(6198),l=r(2278),f=r(81),p=r(851),h=Array;t.exports=function(t){var e=i(t),r=u(this),d=arguments.length,y=d>1?arguments[1]:void 0,v=void 0!==y;v&&(y=n(y,d>2?arguments[2]:void 0));var g,m,b,w,x,E,S=p(e),O=0;if(!S||this===h&&c(S))for(g=s(e),m=r?new this(g):h(g);g>O;O++)E=v?y(e[O],O):e[O],l(m,O,E);else for(m=r?new this:[],x=(w=f(e,S)).next;!(b=o(x,w)).done;O++)E=v?a(w,y,[b.value,O],!0):b.value,l(m,O,E);return m.length=O,m}},9617:(t,e,r)=>{var n=r(5397),o=r(5610),i=r(6198),a=function(t){return function(e,r,a){var c=n(e),u=i(c);if(0===u)return!t&&-1;var s,l=o(a,u);if(t&&r!=r){for(;u>l;)if((s=c[l++])!=s)return!0}else for(;u>l;l++)if((t||l in c)&&c[l]===r)return t||l||0;return!t&&-1}};t.exports={includes:a(!0),indexOf:a(!1)}},9213:(t,e,r)=>{var n=r(6080),o=r(9504),i=r(7055),a=r(8981),c=r(6198),u=r(1469),s=o([].push),l=function(t){var e=1===t,r=2===t,o=3===t,l=4===t,f=6===t,p=7===t,h=5===t||f;return function(d,y,v,g){for(var m,b,w=a(d),x=i(w),E=c(x),S=n(y,v),O=0,j=g||u,_=e?j(d,E):r||p?j(d,0):void 0;E>O;O++)if((h||O in x)&&(b=S(m=x[O],O,w),t))if(e)_[O]=b;else if(b)switch(t){case 3:return!0;case 5:return m;case 6:return O;case 2:s(_,m)}else switch(t){case 4:return!1;case 7:s(_,m)}return f?-1:o||l?l:_}};t.exports={forEach:l(0),map:l(1),filter:l(2),some:l(3),every:l(4),find:l(5),findIndex:l(6),filterReject:l(7)}},597:(t,e,r)=>{var n=r(9039),o=r(8227),i=r(9519),a=o("species");t.exports=function(t){return i>=51||!n((function(){var e=[];return(e.constructor={})[a]=function(){return{foo:1}},1!==e[t](Boolean).foo}))}},4598:(t,e,r)=>{var n=r(9039);t.exports=function(t,e){var r=[][t];return!!r&&n((function(){r.call(null,e||function(){return 1},1)}))}},4527:(t,e,r)=>{var n=r(3724),o=r(4376),i=TypeError,a=Object.getOwnPropertyDescriptor,c=n&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(t){return t instanceof TypeError}}();t.exports=c?function(t,e){if(o(t)&&!a(t,"length").writable)throw new i("Cannot set read only .length");return t.length=e}:function(t,e){return t.length=e}},7680:(t,e,r)=>{var n=r(9504);t.exports=n([].slice)},7433:(t,e,r)=>{var n=r(4376),o=r(3517),i=r(34),a=r(8227)("species"),c=Array;t.exports=function(t){var e;return n(t)&&(e=t.constructor,(o(e)&&(e===c||n(e.prototype))||i(e)&&null===(e=e[a]))&&(e=void 0)),void 0===e?c:e}},1469:(t,e,r)=>{var n=r(7433);t.exports=function(t,e){return new(n(t))(0===e?0:e)}},6319:(t,e,r)=>{var n=r(8551),o=r(9539);t.exports=function(t,e,r,i){try{return i?e(n(r)[0],r[1]):e(r)}catch(e){o(t,"throw",e)}}},4428:(t,e,r)=>{var n=r(8227)("iterator"),o=!1;try{var i=0,a={next:function(){return{done:!!i++}},return:function(){o=!0}};a[n]=function(){return this},Array.from(a,(function(){throw 2}))}catch(t){}t.exports=function(t,e){try{if(!e&&!o)return!1}catch(t){return!1}var r=!1;try{var i={};i[n]=function(){return{next:function(){return{done:r=!0}}}},t(i)}catch(t){}return r}},2195:(t,e,r)=>{var n=r(9504),o=n({}.toString),i=n("".slice);t.exports=function(t){return i(o(t),8,-1)}},6955:(t,e,r)=>{var n=r(2140),o=r(4901),i=r(2195),a=r(8227)("toStringTag"),c=Object,u="Arguments"===i(function(){return arguments}());t.exports=n?i:function(t){var e,r,n;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,e){try{return t[e]}catch(t){}}(e=c(t),a))?r:u?i(e):"Object"===(n=i(e))&&o(e.callee)?"Arguments":n}},4006:(t,e,r)=>{var n=r(9504),o=r(6279),i=r(3451).getWeakData,a=r(679),c=r(8551),u=r(4117),s=r(34),l=r(2652),f=r(9213),p=r(9297),h=r(1181),d=h.set,y=h.getterFor,v=f.find,g=f.findIndex,m=n([].splice),b=0,w=function(t){return t.frozen||(t.frozen=new x)},x=function(){this.entries=[]},E=function(t,e){return v(t.entries,(function(t){return t[0]===e}))};x.prototype={get:function(t){var e=E(this,t);if(e)return e[1]},has:function(t){return!!E(this,t)},set:function(t,e){var r=E(this,t);r?r[1]=e:this.entries.push([t,e])},delete:function(t){var e=g(this.entries,(function(e){return e[0]===t}));return~e&&m(this.entries,e,1),!!~e}},t.exports={getConstructor:function(t,e,r,n){var f=t((function(t,o){a(t,h),d(t,{type:e,id:b++,frozen:null}),u(o)||l(o,t[n],{that:t,AS_ENTRIES:r})})),h=f.prototype,v=y(e),g=function(t,e,r){var n=v(t),o=i(c(e),!0);return!0===o?w(n).set(e,r):o[n.id]=r,t};return o(h,{delete:function(t){var e=v(this);if(!s(t))return!1;var r=i(t);return!0===r?w(e).delete(t):r&&p(r,e.id)&&delete r[e.id]},has:function(t){var e=v(this);if(!s(t))return!1;var r=i(t);return!0===r?w(e).has(t):r&&p(r,e.id)}}),o(h,r?{get:function(t){var e=v(this);if(s(t)){var r=i(t);if(!0===r)return w(e).get(t);if(r)return r[e.id]}},set:function(t,e){return g(this,t,e)}}:{add:function(t){return g(this,t,!0)}}),f}}},6468:(t,e,r)=>{var n=r(6518),o=r(4576),i=r(9504),a=r(2796),c=r(6840),u=r(3451),s=r(2652),l=r(679),f=r(4901),p=r(4117),h=r(34),d=r(9039),y=r(4428),v=r(687),g=r(3167);t.exports=function(t,e,r){var m=-1!==t.indexOf("Map"),b=-1!==t.indexOf("Weak"),w=m?"set":"add",x=o[t],E=x&&x.prototype,S=x,O={},j=function(t){var e=i(E[t]);c(E,t,"add"===t?function(t){return e(this,0===t?0:t),this}:"delete"===t?function(t){return!(b&&!h(t))&&e(this,0===t?0:t)}:"get"===t?function(t){return b&&!h(t)?void 0:e(this,0===t?0:t)}:"has"===t?function(t){return!(b&&!h(t))&&e(this,0===t?0:t)}:function(t,r){return e(this,0===t?0:t,r),this})};if(a(t,!f(x)||!(b||E.forEach&&!d((function(){(new x).entries().next()})))))S=r.getConstructor(e,t,m,w),u.enable();else if(a(t,!0)){var _=new S,P=_[w](b?{}:-0,1)!==_,k=d((function(){_.has(1)})),C=y((function(t){new x(t)})),I=!b&&d((function(){for(var t=new x,e=5;e--;)t[w](e,e);return!t.has(-0)}));C||((S=e((function(t,e){l(t,E);var r=g(new x,t,S);return p(e)||s(e,r[w],{that:r,AS_ENTRIES:m}),r}))).prototype=E,E.constructor=S),(k||I)&&(j("delete"),j("has"),m&&j("get")),(I||P)&&j(w),b&&E.clear&&delete E.clear}return O[t]=S,n({global:!0,constructor:!0,forced:S!==x},O),v(S,t),b||r.setStrong(S,t,m),S}},7740:(t,e,r)=>{var n=r(9297),o=r(5031),i=r(7347),a=r(4913);t.exports=function(t,e,r){for(var c=o(e),u=a.f,s=i.f,l=0;l<c.length;l++){var f=c[l];n(t,f)||r&&n(r,f)||u(t,f,s(e,f))}}},2211:(t,e,r)=>{var n=r(9039);t.exports=!n((function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype}))},2529:t=>{t.exports=function(t,e){return{value:t,done:e}}},6699:(t,e,r)=>{var n=r(3724),o=r(4913),i=r(6980);t.exports=n?function(t,e,r){return o.f(t,e,i(1,r))}:function(t,e,r){return t[e]=r,t}},6980:t=>{t.exports=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}}},2278:(t,e,r)=>{var n=r(3724),o=r(4913),i=r(6980);t.exports=function(t,e,r){n?o.f(t,e,i(0,r)):t[e]=r}},3640:(t,e,r)=>{var n=r(8551),o=r(4270),i=TypeError;t.exports=function(t){if(n(this),"string"===t||"default"===t)t="string";else if("number"!==t)throw new i("Incorrect hint");return o(this,t)}},2106:(t,e,r)=>{var n=r(283),o=r(4913);t.exports=function(t,e,r){return r.get&&n(r.get,e,{getter:!0}),r.set&&n(r.set,e,{setter:!0}),o.f(t,e,r)}},6840:(t,e,r)=>{var n=r(4901),o=r(4913),i=r(283),a=r(9433);t.exports=function(t,e,r,c){c||(c={});var u=c.enumerable,s=void 0!==c.name?c.name:e;if(n(r)&&i(r,s,c),c.global)u?t[e]=r:a(e,r);else{try{c.unsafe?t[e]&&(u=!0):delete t[e]}catch(t){}u?t[e]=r:o.f(t,e,{value:r,enumerable:!1,configurable:!c.nonConfigurable,writable:!c.nonWritable})}return t}},6279:(t,e,r)=>{var n=r(6840);t.exports=function(t,e,r){for(var o in e)n(t,o,e[o],r);return t}},9433:(t,e,r)=>{var n=r(4576),o=Object.defineProperty;t.exports=function(t,e){try{o(n,t,{value:e,configurable:!0,writable:!0})}catch(r){n[t]=e}return e}},3724:(t,e,r)=>{var n=r(9039);t.exports=!n((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]}))},4055:(t,e,r)=>{var n=r(4576),o=r(34),i=n.document,a=o(i)&&o(i.createElement);t.exports=function(t){return a?i.createElement(t):{}}},6837:t=>{var e=TypeError;t.exports=function(t){if(t>9007199254740991)throw e("Maximum allowed index exceeded");return t}},7400:t=>{t.exports={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}},9296:(t,e,r)=>{var n=r(4055)("span").classList,o=n&&n.constructor&&n.constructor.prototype;t.exports=o===Object.prototype?void 0:o},8727:t=>{t.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},4265:(t,e,r)=>{var n=r(2839);t.exports=/ipad|iphone|ipod/i.test(n)&&"undefined"!=typeof Pebble},9544:(t,e,r)=>{var n=r(2839);t.exports=/(?:ipad|iphone|ipod).*applewebkit/i.test(n)},8574:(t,e,r)=>{var n=r(4215);t.exports="NODE"===n},7860:(t,e,r)=>{var n=r(2839);t.exports=/web0s(?!.*chrome)/i.test(n)},2839:(t,e,r)=>{var n=r(4576).navigator,o=n&&n.userAgent;t.exports=o?String(o):""},9519:(t,e,r)=>{var n,o,i=r(4576),a=r(2839),c=i.process,u=i.Deno,s=c&&c.versions||u&&u.version,l=s&&s.v8;l&&(o=(n=l.split("."))[0]>0&&n[0]<4?1:+(n[0]+n[1])),!o&&a&&(!(n=a.match(/Edge\/(\d+)/))||n[1]>=74)&&(n=a.match(/Chrome\/(\d+)/))&&(o=+n[1]),t.exports=o},4215:(t,e,r)=>{var n=r(4576),o=r(2839),i=r(2195),a=function(t){return o.slice(0,t.length)===t};t.exports=a("Bun/")?"BUN":a("Cloudflare-Workers")?"CLOUDFLARE":a("Deno/")?"DENO":a("Node.js/")?"NODE":n.Bun&&"string"==typeof Bun.version?"BUN":n.Deno&&"object"==typeof Deno.version?"DENO":"process"===i(n.process)?"NODE":n.window&&n.document?"BROWSER":"REST"},6193:(t,e,r)=>{var n=r(9504),o=Error,i=n("".replace),a=String(new o("zxcasd").stack),c=/\n\s*at [^:]*:[^\n]*/,u=c.test(a);t.exports=function(t,e){if(u&&"string"==typeof t&&!o.prepareStackTrace)for(;e--;)t=i(t,c,"");return t}},747:(t,e,r)=>{var n=r(6699),o=r(6193),i=r(4659),a=Error.captureStackTrace;t.exports=function(t,e,r,c){i&&(a?a(t,e):n(t,"stack",o(r,c)))}},4659:(t,e,r)=>{var n=r(9039),o=r(6980);t.exports=!n((function(){var t=new Error("a");return!("stack"in t)||(Object.defineProperty(t,"stack",o(1,7)),7!==t.stack)}))},6518:(t,e,r)=>{var n=r(4576),o=r(7347).f,i=r(6699),a=r(6840),c=r(9433),u=r(7740),s=r(2796);t.exports=function(t,e){var r,l,f,p,h,d=t.target,y=t.global,v=t.stat;if(r=y?n:v?n[d]||c(d,{}):n[d]&&n[d].prototype)for(l in e){if(p=e[l],f=t.dontCallGetSet?(h=o(r,l))&&h.value:r[l],!s(y?l:d+(v?".":"#")+l,t.forced)&&void 0!==f){if(typeof p==typeof f)continue;u(p,f)}(t.sham||f&&f.sham)&&i(p,"sham",!0),a(r,l,p,t)}}},9039:t=>{t.exports=function(t){try{return!!t()}catch(t){return!0}}},9228:(t,e,r)=>{r(7495);var n=r(9565),o=r(6840),i=r(7323),a=r(9039),c=r(8227),u=r(6699),s=c("species"),l=RegExp.prototype;t.exports=function(t,e,r,f){var p=c(t),h=!a((function(){var e={};return e[p]=function(){return 7},7!==""[t](e)})),d=h&&!a((function(){var e=!1,r=/a/;return"split"===t&&((r={}).constructor={},r.constructor[s]=function(){return r},r.flags="",r[p]=/./[p]),r.exec=function(){return e=!0,null},r[p](""),!e}));if(!h||!d||r){var y=/./[p],v=e(p,""[t],(function(t,e,r,o,a){var c=e.exec;return c===i||c===l.exec?h&&!a?{done:!0,value:n(y,e,r,o)}:{done:!0,value:n(t,r,e,o)}:{done:!1}}));o(String.prototype,t,v[0]),o(l,p,v[1])}f&&u(l[p],"sham",!0)}},2744:(t,e,r)=>{var n=r(9039);t.exports=!n((function(){return Object.isExtensible(Object.preventExtensions({}))}))},8745:(t,e,r)=>{var n=r(616),o=Function.prototype,i=o.apply,a=o.call;t.exports="object"==typeof Reflect&&Reflect.apply||(n?a.bind(i):function(){return a.apply(i,arguments)})},6080:(t,e,r)=>{var n=r(7476),o=r(9306),i=r(616),a=n(n.bind);t.exports=function(t,e){return o(t),void 0===e?t:i?a(t,e):function(){return t.apply(e,arguments)}}},616:(t,e,r)=>{var n=r(9039);t.exports=!n((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")}))},566:(t,e,r)=>{var n=r(9504),o=r(9306),i=r(34),a=r(9297),c=r(7680),u=r(616),s=Function,l=n([].concat),f=n([].join),p={};t.exports=u?s.bind:function(t){var e=o(this),r=e.prototype,n=c(arguments,1),u=function(){var r=l(n,c(arguments));return this instanceof u?function(t,e,r){if(!a(p,e)){for(var n=[],o=0;o<e;o++)n[o]="a["+o+"]";p[e]=s("C,a","return new C("+f(n,",")+")")}return p[e](t,r)}(e,r.length,r):e.apply(t,r)};return i(r)&&(u.prototype=r),u}},9565:(t,e,r)=>{var n=r(616),o=Function.prototype.call;t.exports=n?o.bind(o):function(){return o.apply(o,arguments)}},350:(t,e,r)=>{var n=r(3724),o=r(9297),i=Function.prototype,a=n&&Object.getOwnPropertyDescriptor,c=o(i,"name"),u=c&&"something"===function(){}.name,s=c&&(!n||n&&a(i,"name").configurable);t.exports={EXISTS:c,PROPER:u,CONFIGURABLE:s}},6706:(t,e,r)=>{var n=r(9504),o=r(9306);t.exports=function(t,e,r){try{return n(o(Object.getOwnPropertyDescriptor(t,e)[r]))}catch(t){}}},7476:(t,e,r)=>{var n=r(2195),o=r(9504);t.exports=function(t){if("Function"===n(t))return o(t)}},9504:(t,e,r)=>{var n=r(616),o=Function.prototype,i=o.call,a=n&&o.bind.bind(i,i);t.exports=n?a:function(t){return function(){return i.apply(t,arguments)}}},7751:(t,e,r)=>{var n=r(4576),o=r(4901);t.exports=function(t,e){return arguments.length<2?(r=n[t],o(r)?r:void 0):n[t]&&n[t][e];var r}},1767:t=>{t.exports=function(t){return{iterator:t,next:t.next,done:!1}}},851:(t,e,r)=>{var n=r(6955),o=r(5966),i=r(4117),a=r(6269),c=r(8227)("iterator");t.exports=function(t){if(!i(t))return o(t,c)||o(t,"@@iterator")||a[n(t)]}},81:(t,e,r)=>{var n=r(9565),o=r(9306),i=r(8551),a=r(6823),c=r(851),u=TypeError;t.exports=function(t,e){var r=arguments.length<2?c(t):e;if(o(r))return i(n(r,t));throw new u(a(t)+" is not iterable")}},6933:(t,e,r)=>{var n=r(9504),o=r(4376),i=r(4901),a=r(2195),c=r(655),u=n([].push);t.exports=function(t){if(i(t))return t;if(o(t)){for(var e=t.length,r=[],n=0;n<e;n++){var s=t[n];"string"==typeof s?u(r,s):"number"!=typeof s&&"Number"!==a(s)&&"String"!==a(s)||u(r,c(s))}var l=r.length,f=!0;return function(t,e){if(f)return f=!1,e;if(o(this))return e;for(var n=0;n<l;n++)if(r[n]===t)return e}}}},5966:(t,e,r)=>{var n=r(9306),o=r(4117);t.exports=function(t,e){var r=t[e];return o(r)?void 0:n(r)}},2478:(t,e,r)=>{var n=r(9504),o=r(8981),i=Math.floor,a=n("".charAt),c=n("".replace),u=n("".slice),s=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,l=/\$([$&'`]|\d{1,2})/g;t.exports=function(t,e,r,n,f,p){var h=r+t.length,d=n.length,y=l;return void 0!==f&&(f=o(f),y=s),c(p,y,(function(o,c){var s;switch(a(c,0)){case"$":return"$";case"&":return t;case"`":return u(e,0,r);case"'":return u(e,h);case"<":s=f[u(c,1,-1)];break;default:var l=+c;if(0===l)return o;if(l>d){var p=i(l/10);return 0===p?o:p<=d?void 0===n[p-1]?a(c,1):n[p-1]+a(c,1):o}s=n[l-1]}return void 0===s?"":s}))}},4576:function(t,e,r){var n=function(t){return t&&t.Math===Math&&t};t.exports=n("object"==typeof globalThis&&globalThis)||n("object"==typeof window&&window)||n("object"==typeof self&&self)||n("object"==typeof r.g&&r.g)||n("object"==typeof this&&this)||function(){return this}()||Function("return this")()},9297:(t,e,r)=>{var n=r(9504),o=r(8981),i=n({}.hasOwnProperty);t.exports=Object.hasOwn||function(t,e){return i(o(t),e)}},421:t=>{t.exports={}},3138:t=>{t.exports=function(t,e){try{1===arguments.length?console.error(t):console.error(t,e)}catch(t){}}},397:(t,e,r)=>{var n=r(7751);t.exports=n("document","documentElement")},5917:(t,e,r)=>{var n=r(3724),o=r(9039),i=r(4055);t.exports=!n&&!o((function(){return 7!==Object.defineProperty(i("div"),"a",{get:function(){return 7}}).a}))},7055:(t,e,r)=>{var n=r(9504),o=r(9039),i=r(2195),a=Object,c=n("".split);t.exports=o((function(){return!a("z").propertyIsEnumerable(0)}))?function(t){return"String"===i(t)?c(t,""):a(t)}:a},3167:(t,e,r)=>{var n=r(4901),o=r(34),i=r(2967);t.exports=function(t,e,r){var a,c;return i&&n(a=e.constructor)&&a!==r&&o(c=a.prototype)&&c!==r.prototype&&i(t,c),t}},3706:(t,e,r)=>{var n=r(9504),o=r(4901),i=r(7629),a=n(Function.toString);o(i.inspectSource)||(i.inspectSource=function(t){return a(t)}),t.exports=i.inspectSource},7584:(t,e,r)=>{var n=r(34),o=r(6699);t.exports=function(t,e){n(e)&&"cause"in e&&o(t,"cause",e.cause)}},3451:(t,e,r)=>{var n=r(6518),o=r(9504),i=r(421),a=r(34),c=r(9297),u=r(4913).f,s=r(8480),l=r(298),f=r(4124),p=r(3392),h=r(2744),d=!1,y=p("meta"),v=0,g=function(t){u(t,y,{value:{objectID:"O"+v++,weakData:{}}})},m=t.exports={enable:function(){m.enable=function(){},d=!0;var t=s.f,e=o([].splice),r={};r[y]=1,t(r).length&&(s.f=function(r){for(var n=t(r),o=0,i=n.length;o<i;o++)if(n[o]===y){e(n,o,1);break}return n},n({target:"Object",stat:!0,forced:!0},{getOwnPropertyNames:l.f}))},fastKey:function(t,e){if(!a(t))return"symbol"==typeof t?t:("string"==typeof t?"S":"P")+t;if(!c(t,y)){if(!f(t))return"F";if(!e)return"E";g(t)}return t[y].objectID},getWeakData:function(t,e){if(!c(t,y)){if(!f(t))return!0;if(!e)return!1;g(t)}return t[y].weakData},onFreeze:function(t){return h&&d&&f(t)&&!c(t,y)&&g(t),t}};i[y]=!0},1181:(t,e,r)=>{var n,o,i,a=r(8622),c=r(4576),u=r(34),s=r(6699),l=r(9297),f=r(7629),p=r(6119),h=r(421),d="Object already initialized",y=c.TypeError,v=c.WeakMap;if(a||f.state){var g=f.state||(f.state=new v);g.get=g.get,g.has=g.has,g.set=g.set,n=function(t,e){if(g.has(t))throw new y(d);return e.facade=t,g.set(t,e),e},o=function(t){return g.get(t)||{}},i=function(t){return g.has(t)}}else{var m=p("state");h[m]=!0,n=function(t,e){if(l(t,m))throw new y(d);return e.facade=t,s(t,m,e),e},o=function(t){return l(t,m)?t[m]:{}},i=function(t){return l(t,m)}}t.exports={set:n,get:o,has:i,enforce:function(t){return i(t)?o(t):n(t,{})},getterFor:function(t){return function(e){var r;if(!u(e)||(r=o(e)).type!==t)throw new y("Incompatible receiver, "+t+" required");return r}}}},4209:(t,e,r)=>{var n=r(8227),o=r(6269),i=n("iterator"),a=Array.prototype;t.exports=function(t){return void 0!==t&&(o.Array===t||a[i]===t)}},4376:(t,e,r)=>{var n=r(2195);t.exports=Array.isArray||function(t){return"Array"===n(t)}},4901:t=>{var e="object"==typeof document&&document.all;t.exports=void 0===e&&void 0!==e?function(t){return"function"==typeof t||t===e}:function(t){return"function"==typeof t}},3517:(t,e,r)=>{var n=r(9504),o=r(9039),i=r(4901),a=r(6955),c=r(7751),u=r(3706),s=function(){},l=c("Reflect","construct"),f=/^\s*(?:class|function)\b/,p=n(f.exec),h=!f.test(s),d=function(t){if(!i(t))return!1;try{return l(s,[],t),!0}catch(t){return!1}},y=function(t){if(!i(t))return!1;switch(a(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return h||!!p(f,u(t))}catch(t){return!0}};y.sham=!0,t.exports=!l||o((function(){var t;return d(d.call)||!d(Object)||!d((function(){t=!0}))||t}))?y:d},6575:(t,e,r)=>{var n=r(9297);t.exports=function(t){return void 0!==t&&(n(t,"value")||n(t,"writable"))}},2796:(t,e,r)=>{var n=r(9039),o=r(4901),i=/#|\.prototype\./,a=function(t,e){var r=u[c(t)];return r===l||r!==s&&(o(e)?n(e):!!e)},c=a.normalize=function(t){return String(t).replace(i,".").toLowerCase()},u=a.data={},s=a.NATIVE="N",l=a.POLYFILL="P";t.exports=a},4117:t=>{t.exports=function(t){return null==t}},34:(t,e,r)=>{var n=r(4901);t.exports=function(t){return"object"==typeof t?null!==t:n(t)}},3925:(t,e,r)=>{var n=r(34);t.exports=function(t){return n(t)||null===t}},6395:t=>{t.exports=!1},757:(t,e,r)=>{var n=r(7751),o=r(4901),i=r(1625),a=r(7040),c=Object;t.exports=a?function(t){return"symbol"==typeof t}:function(t){var e=n("Symbol");return o(e)&&i(e.prototype,c(t))}},2652:(t,e,r)=>{var n=r(6080),o=r(9565),i=r(8551),a=r(6823),c=r(4209),u=r(6198),s=r(1625),l=r(81),f=r(851),p=r(9539),h=TypeError,d=function(t,e){this.stopped=t,this.result=e},y=d.prototype;t.exports=function(t,e,r){var v,g,m,b,w,x,E,S=r&&r.that,O=!(!r||!r.AS_ENTRIES),j=!(!r||!r.IS_RECORD),_=!(!r||!r.IS_ITERATOR),P=!(!r||!r.INTERRUPTED),k=n(e,S),C=function(t){return v&&p(v,"normal",t),new d(!0,t)},I=function(t){return O?(i(t),P?k(t[0],t[1],C):k(t[0],t[1])):P?k(t,C):k(t)};if(j)v=t.iterator;else if(_)v=t;else{if(!(g=f(t)))throw new h(a(t)+" is not iterable");if(c(g)){for(m=0,b=u(t);b>m;m++)if((w=I(t[m]))&&s(y,w))return w;return new d(!1)}v=l(t,g)}for(x=j?t.next:v.next;!(E=o(x,v)).done;){try{w=I(E.value)}catch(t){p(v,"throw",t)}if("object"==typeof w&&w&&s(y,w))return w}return new d(!1)}},9539:(t,e,r)=>{var n=r(9565),o=r(8551),i=r(5966);t.exports=function(t,e,r){var a,c;o(t);try{if(!(a=i(t,"return"))){if("throw"===e)throw r;return r}a=n(a,t)}catch(t){c=!0,a=t}if("throw"===e)throw r;if(c)throw a;return o(a),r}},3994:(t,e,r)=>{var n=r(7657).IteratorPrototype,o=r(2360),i=r(6980),a=r(687),c=r(6269),u=function(){return this};t.exports=function(t,e,r,s){var l=e+" Iterator";return t.prototype=o(n,{next:i(+!s,r)}),a(t,l,!1,!0),c[l]=u,t}},9462:(t,e,r)=>{var n=r(9565),o=r(2360),i=r(6699),a=r(6279),c=r(8227),u=r(1181),s=r(5966),l=r(7657).IteratorPrototype,f=r(2529),p=r(9539),h=c("toStringTag"),d="IteratorHelper",y="WrapForValidIterator",v=u.set,g=function(t){var e=u.getterFor(t?y:d);return a(o(l),{next:function(){var r=e(this);if(t)return r.nextHandler();try{var n=r.done?void 0:r.nextHandler();return f(n,r.done)}catch(t){throw r.done=!0,t}},return:function(){var r=e(this),o=r.iterator;if(r.done=!0,t){var i=s(o,"return");return i?n(i,o):f(void 0,!0)}if(r.inner)try{p(r.inner.iterator,"normal")}catch(t){return p(o,"throw",t)}return o&&p(o,"normal"),f(void 0,!0)}})},m=g(!0),b=g(!1);i(b,h,"Iterator Helper"),t.exports=function(t,e){var r=function(r,n){n?(n.iterator=r.iterator,n.next=r.next):n=r,n.type=e?y:d,n.nextHandler=t,n.counter=0,n.done=!1,v(this,n)};return r.prototype=e?m:b,r}},1088:(t,e,r)=>{var n=r(6518),o=r(9565),i=r(6395),a=r(350),c=r(4901),u=r(3994),s=r(2787),l=r(2967),f=r(687),p=r(6699),h=r(6840),d=r(8227),y=r(6269),v=r(7657),g=a.PROPER,m=a.CONFIGURABLE,b=v.IteratorPrototype,w=v.BUGGY_SAFARI_ITERATORS,x=d("iterator"),E="keys",S="values",O="entries",j=function(){return this};t.exports=function(t,e,r,a,d,v,_){u(r,e,a);var P,k,C,I=function(t){if(t===d&&N)return N;if(!w&&t&&t in L)return L[t];switch(t){case E:case S:case O:return function(){return new r(this,t)}}return function(){return new r(this)}},T=e+" Iterator",A=!1,L=t.prototype,R=L[x]||L["@@iterator"]||d&&L[d],N=!w&&R||I(d),M="Array"===e&&L.entries||R;if(M&&(P=s(M.call(new t)))!==Object.prototype&&P.next&&(i||s(P)===b||(l?l(P,b):c(P[x])||h(P,x,j)),f(P,T,!0,!0),i&&(y[T]=j)),g&&d===S&&R&&R.name!==S&&(!i&&m?p(L,"name",S):(A=!0,N=function(){return o(R,this)})),d)if(k={values:I(S),keys:v?N:I(E),entries:I(O)},_)for(C in k)(w||A||!(C in L))&&h(L,C,k[C]);else n({target:e,proto:!0,forced:w||A},k);return i&&!_||L[x]===N||h(L,x,N,{name:d}),y[e]=N,k}},713:(t,e,r)=>{var n=r(9565),o=r(9306),i=r(8551),a=r(1767),c=r(9462),u=r(6319),s=c((function(){var t=this.iterator,e=i(n(this.next,t));if(!(this.done=!!e.done))return u(t,this.mapper,[e.value,this.counter++],!0)}));t.exports=function(t){return i(this),o(t),new s(a(this),{mapper:t})}},7657:(t,e,r)=>{var n,o,i,a=r(9039),c=r(4901),u=r(34),s=r(2360),l=r(2787),f=r(6840),p=r(8227),h=r(6395),d=p("iterator"),y=!1;[].keys&&("next"in(i=[].keys())?(o=l(l(i)))!==Object.prototype&&(n=o):y=!0),!u(n)||a((function(){var t={};return n[d].call(t)!==t}))?n={}:h&&(n=s(n)),c(n[d])||f(n,d,(function(){return this})),t.exports={IteratorPrototype:n,BUGGY_SAFARI_ITERATORS:y}},6269:t=>{t.exports={}},6198:(t,e,r)=>{var n=r(8014);t.exports=function(t){return n(t.length)}},283:(t,e,r)=>{var n=r(9504),o=r(9039),i=r(4901),a=r(9297),c=r(3724),u=r(350).CONFIGURABLE,s=r(3706),l=r(1181),f=l.enforce,p=l.get,h=String,d=Object.defineProperty,y=n("".slice),v=n("".replace),g=n([].join),m=c&&!o((function(){return 8!==d((function(){}),"length",{value:8}).length})),b=String(String).split("String"),w=t.exports=function(t,e,r){"Symbol("===y(h(e),0,7)&&(e="["+v(h(e),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),r&&r.getter&&(e="get "+e),r&&r.setter&&(e="set "+e),(!a(t,"name")||u&&t.name!==e)&&(c?d(t,"name",{value:e,configurable:!0}):t.name=e),m&&r&&a(r,"arity")&&t.length!==r.arity&&d(t,"length",{value:r.arity});try{r&&a(r,"constructor")&&r.constructor?c&&d(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var n=f(t);return a(n,"source")||(n.source=g(b,"string"==typeof e?e:"")),t};Function.prototype.toString=w((function(){return i(this)&&p(this).source||s(this)}),"toString")},741:t=>{var e=Math.ceil,r=Math.floor;t.exports=Math.trunc||function(t){var n=+t;return(n>0?r:e)(n)}},1955:(t,e,r)=>{var n,o,i,a,c,u=r(4576),s=r(3389),l=r(6080),f=r(9225).set,p=r(8265),h=r(9544),d=r(4265),y=r(7860),v=r(8574),g=u.MutationObserver||u.WebKitMutationObserver,m=u.document,b=u.process,w=u.Promise,x=s("queueMicrotask");if(!x){var E=new p,S=function(){var t,e;for(v&&(t=b.domain)&&t.exit();e=E.get();)try{e()}catch(t){throw E.head&&n(),t}t&&t.enter()};h||v||y||!g||!m?!d&&w&&w.resolve?((a=w.resolve(void 0)).constructor=w,c=l(a.then,a),n=function(){c(S)}):v?n=function(){b.nextTick(S)}:(f=l(f,u),n=function(){f(S)}):(o=!0,i=m.createTextNode(""),new g(S).observe(i,{characterData:!0}),n=function(){i.data=o=!o}),x=function(t){E.head||n(),E.add(t)}}t.exports=x},6043:(t,e,r)=>{var n=r(9306),o=TypeError,i=function(t){var e,r;this.promise=new t((function(t,n){if(void 0!==e||void 0!==r)throw new o("Bad Promise constructor");e=t,r=n})),this.resolve=n(e),this.reject=n(r)};t.exports.f=function(t){return new i(t)}},2603:(t,e,r)=>{var n=r(655);t.exports=function(t,e){return void 0===t?arguments.length<2?"":e:n(t)}},2703:(t,e,r)=>{var n=r(4576),o=r(9039),i=r(9504),a=r(655),c=r(3802).trim,u=r(7452),s=n.parseInt,l=n.Symbol,f=l&&l.iterator,p=/^[+-]?0x/i,h=i(p.exec),d=8!==s(u+"08")||22!==s(u+"0x16")||f&&!o((function(){s(Object(f))}));t.exports=d?function(t,e){var r=c(a(t));return s(r,e>>>0||(h(p,r)?16:10))}:s},4213:(t,e,r)=>{var n=r(3724),o=r(9504),i=r(9565),a=r(9039),c=r(1072),u=r(3717),s=r(8773),l=r(8981),f=r(7055),p=Object.assign,h=Object.defineProperty,d=o([].concat);t.exports=!p||a((function(){if(n&&1!==p({b:1},p(h({},"a",{enumerable:!0,get:function(){h(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var t={},e={},r=Symbol("assign detection"),o="abcdefghijklmnopqrst";return t[r]=7,o.split("").forEach((function(t){e[t]=t})),7!==p({},t)[r]||c(p({},e)).join("")!==o}))?function(t,e){for(var r=l(t),o=arguments.length,a=1,p=u.f,h=s.f;o>a;)for(var y,v=f(arguments[a++]),g=p?d(c(v),p(v)):c(v),m=g.length,b=0;m>b;)y=g[b++],n&&!i(h,v,y)||(r[y]=v[y]);return r}:p},2360:(t,e,r)=>{var n,o=r(8551),i=r(6801),a=r(8727),c=r(421),u=r(397),s=r(4055),l=r(6119),f="prototype",p="script",h=l("IE_PROTO"),d=function(){},y=function(t){return"<"+p+">"+t+"</"+p+">"},v=function(t){t.write(y("")),t.close();var e=t.parentWindow.Object;return t=null,e},g=function(){try{n=new ActiveXObject("htmlfile")}catch(t){}var t,e,r;g="undefined"!=typeof document?document.domain&&n?v(n):(e=s("iframe"),r="java"+p+":",e.style.display="none",u.appendChild(e),e.src=String(r),(t=e.contentWindow.document).open(),t.write(y("document.F=Object")),t.close(),t.F):v(n);for(var o=a.length;o--;)delete g[f][a[o]];return g()};c[h]=!0,t.exports=Object.create||function(t,e){var r;return null!==t?(d[f]=o(t),r=new d,d[f]=null,r[h]=t):r=g(),void 0===e?r:i.f(r,e)}},6801:(t,e,r)=>{var n=r(3724),o=r(8686),i=r(4913),a=r(8551),c=r(5397),u=r(1072);e.f=n&&!o?Object.defineProperties:function(t,e){a(t);for(var r,n=c(e),o=u(e),s=o.length,l=0;s>l;)i.f(t,r=o[l++],n[r]);return t}},4913:(t,e,r)=>{var n=r(3724),o=r(5917),i=r(8686),a=r(8551),c=r(6969),u=TypeError,s=Object.defineProperty,l=Object.getOwnPropertyDescriptor,f="enumerable",p="configurable",h="writable";e.f=n?i?function(t,e,r){if(a(t),e=c(e),a(r),"function"==typeof t&&"prototype"===e&&"value"in r&&h in r&&!r[h]){var n=l(t,e);n&&n[h]&&(t[e]=r.value,r={configurable:p in r?r[p]:n[p],enumerable:f in r?r[f]:n[f],writable:!1})}return s(t,e,r)}:s:function(t,e,r){if(a(t),e=c(e),a(r),o)try{return s(t,e,r)}catch(t){}if("get"in r||"set"in r)throw new u("Accessors not supported");return"value"in r&&(t[e]=r.value),t}},7347:(t,e,r)=>{var n=r(3724),o=r(9565),i=r(8773),a=r(6980),c=r(5397),u=r(6969),s=r(9297),l=r(5917),f=Object.getOwnPropertyDescriptor;e.f=n?f:function(t,e){if(t=c(t),e=u(e),l)try{return f(t,e)}catch(t){}if(s(t,e))return a(!o(i.f,t,e),t[e])}},298:(t,e,r)=>{var n=r(2195),o=r(5397),i=r(8480).f,a=r(7680),c="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];t.exports.f=function(t){return c&&"Window"===n(t)?function(t){try{return i(t)}catch(t){return a(c)}}(t):i(o(t))}},8480:(t,e,r)=>{var n=r(1828),o=r(8727).concat("length","prototype");e.f=Object.getOwnPropertyNames||function(t){return n(t,o)}},3717:(t,e)=>{e.f=Object.getOwnPropertySymbols},2787:(t,e,r)=>{var n=r(9297),o=r(4901),i=r(8981),a=r(6119),c=r(2211),u=a("IE_PROTO"),s=Object,l=s.prototype;t.exports=c?s.getPrototypeOf:function(t){var e=i(t);if(n(e,u))return e[u];var r=e.constructor;return o(r)&&e instanceof r?r.prototype:e instanceof s?l:null}},4124:(t,e,r)=>{var n=r(9039),o=r(34),i=r(2195),a=r(5652),c=Object.isExtensible,u=n((function(){c(1)}));t.exports=u||a?function(t){return!!o(t)&&(!a||"ArrayBuffer"!==i(t))&&(!c||c(t))}:c},1625:(t,e,r)=>{var n=r(9504);t.exports=n({}.isPrototypeOf)},1828:(t,e,r)=>{var n=r(9504),o=r(9297),i=r(5397),a=r(9617).indexOf,c=r(421),u=n([].push);t.exports=function(t,e){var r,n=i(t),s=0,l=[];for(r in n)!o(c,r)&&o(n,r)&&u(l,r);for(;e.length>s;)o(n,r=e[s++])&&(~a(l,r)||u(l,r));return l}},1072:(t,e,r)=>{var n=r(1828),o=r(8727);t.exports=Object.keys||function(t){return n(t,o)}},8773:(t,e)=>{var r={}.propertyIsEnumerable,n=Object.getOwnPropertyDescriptor,o=n&&!r.call({1:2},1);e.f=o?function(t){var e=n(this,t);return!!e&&e.enumerable}:r},2967:(t,e,r)=>{var n=r(6706),o=r(34),i=r(7750),a=r(3506);t.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var t,e=!1,r={};try{(t=n(Object.prototype,"__proto__","set"))(r,[]),e=r instanceof Array}catch(t){}return function(r,n){return i(r),a(n),o(r)?(e?t(r,n):r.__proto__=n,r):r}}():void 0)},3179:(t,e,r)=>{var n=r(2140),o=r(6955);t.exports=n?{}.toString:function(){return"[object "+o(this)+"]"}},4270:(t,e,r)=>{var n=r(9565),o=r(4901),i=r(34),a=TypeError;t.exports=function(t,e){var r,c;if("string"===e&&o(r=t.toString)&&!i(c=n(r,t)))return c;if(o(r=t.valueOf)&&!i(c=n(r,t)))return c;if("string"!==e&&o(r=t.toString)&&!i(c=n(r,t)))return c;throw new a("Can't convert object to primitive value")}},5031:(t,e,r)=>{var n=r(7751),o=r(9504),i=r(8480),a=r(3717),c=r(8551),u=o([].concat);t.exports=n("Reflect","ownKeys")||function(t){var e=i.f(c(t)),r=a.f;return r?u(e,r(t)):e}},9167:(t,e,r)=>{var n=r(4576);t.exports=n},1103:t=>{t.exports=function(t){try{return{error:!1,value:t()}}catch(t){return{error:!0,value:t}}}},916:(t,e,r)=>{var n=r(4576),o=r(550),i=r(4901),a=r(2796),c=r(3706),u=r(8227),s=r(4215),l=r(6395),f=r(9519),p=o&&o.prototype,h=u("species"),d=!1,y=i(n.PromiseRejectionEvent),v=a("Promise",(function(){var t=c(o),e=t!==String(o);if(!e&&66===f)return!0;if(l&&(!p.catch||!p.finally))return!0;if(!f||f<51||!/native code/.test(t)){var r=new o((function(t){t(1)})),n=function(t){t((function(){}),(function(){}))};if((r.constructor={})[h]=n,!(d=r.then((function(){}))instanceof n))return!0}return!(e||"BROWSER"!==s&&"DENO"!==s||y)}));t.exports={CONSTRUCTOR:v,REJECTION_EVENT:y,SUBCLASSING:d}},550:(t,e,r)=>{var n=r(4576);t.exports=n.Promise},3438:(t,e,r)=>{var n=r(8551),o=r(34),i=r(6043);t.exports=function(t,e){if(n(t),o(e)&&e.constructor===t)return e;var r=i.f(t);return(0,r.resolve)(e),r.promise}},537:(t,e,r)=>{var n=r(550),o=r(4428),i=r(916).CONSTRUCTOR;t.exports=i||!o((function(t){n.all(t).then(void 0,(function(){}))}))},1056:(t,e,r)=>{var n=r(4913).f;t.exports=function(t,e,r){r in t||n(t,r,{configurable:!0,get:function(){return e[r]},set:function(t){e[r]=t}})}},8265:t=>{var e=function(){this.head=null,this.tail=null};e.prototype={add:function(t){var e={item:t,next:null},r=this.tail;r?r.next=e:this.head=e,this.tail=e},get:function(){var t=this.head;if(t)return null===(this.head=t.next)&&(this.tail=null),t.item}},t.exports=e},6682:(t,e,r)=>{var n=r(9565),o=r(8551),i=r(4901),a=r(2195),c=r(7323),u=TypeError;t.exports=function(t,e){var r=t.exec;if(i(r)){var s=n(r,t,e);return null!==s&&o(s),s}if("RegExp"===a(t))return n(c,t,e);throw new u("RegExp#exec called on incompatible receiver")}},7323:(t,e,r)=>{var n,o,i=r(9565),a=r(9504),c=r(655),u=r(7979),s=r(8429),l=r(5745),f=r(2360),p=r(1181).get,h=r(3635),d=r(8814),y=l("native-string-replace",String.prototype.replace),v=RegExp.prototype.exec,g=v,m=a("".charAt),b=a("".indexOf),w=a("".replace),x=a("".slice),E=(o=/b*/g,i(v,n=/a/,"a"),i(v,o,"a"),0!==n.lastIndex||0!==o.lastIndex),S=s.BROKEN_CARET,O=void 0!==/()??/.exec("")[1];(E||O||S||h||d)&&(g=function(t){var e,r,n,o,a,s,l,h=this,d=p(h),j=c(t),_=d.raw;if(_)return _.lastIndex=h.lastIndex,e=i(g,_,j),h.lastIndex=_.lastIndex,e;var P=d.groups,k=S&&h.sticky,C=i(u,h),I=h.source,T=0,A=j;if(k&&(C=w(C,"y",""),-1===b(C,"g")&&(C+="g"),A=x(j,h.lastIndex),h.lastIndex>0&&(!h.multiline||h.multiline&&"\n"!==m(j,h.lastIndex-1))&&(I="(?: "+I+")",A=" "+A,T++),r=new RegExp("^(?:"+I+")",C)),O&&(r=new RegExp("^"+I+"$(?!\\s)",C)),E&&(n=h.lastIndex),o=i(v,k?r:h,A),k?o?(o.input=x(o.input,T),o[0]=x(o[0],T),o.index=h.lastIndex,h.lastIndex+=o[0].length):h.lastIndex=0:E&&o&&(h.lastIndex=h.global?o.index+o[0].length:n),O&&o&&o.length>1&&i(y,o[0],r,(function(){for(a=1;a<arguments.length-2;a++)void 0===arguments[a]&&(o[a]=void 0)})),o&&P)for(o.groups=s=f(null),a=0;a<P.length;a++)s[(l=P[a])[0]]=o[l[1]];return o}),t.exports=g},7979:(t,e,r)=>{var n=r(8551);t.exports=function(){var t=n(this),e="";return t.hasIndices&&(e+="d"),t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.dotAll&&(e+="s"),t.unicode&&(e+="u"),t.unicodeSets&&(e+="v"),t.sticky&&(e+="y"),e}},1034:(t,e,r)=>{var n=r(9565),o=r(9297),i=r(1625),a=r(7979),c=RegExp.prototype;t.exports=function(t){var e=t.flags;return void 0!==e||"flags"in c||o(t,"flags")||!i(c,t)?e:n(a,t)}},8429:(t,e,r)=>{var n=r(9039),o=r(4576).RegExp,i=n((function(){var t=o("a","y");return t.lastIndex=2,null!==t.exec("abcd")})),a=i||n((function(){return!o("a","y").sticky})),c=i||n((function(){var t=o("^r","gy");return t.lastIndex=2,null!==t.exec("str")}));t.exports={BROKEN_CARET:c,MISSED_STICKY:a,UNSUPPORTED_Y:i}},3635:(t,e,r)=>{var n=r(9039),o=r(4576).RegExp;t.exports=n((function(){var t=o(".","s");return!(t.dotAll&&t.test("\n")&&"s"===t.flags)}))},8814:(t,e,r)=>{var n=r(9039),o=r(4576).RegExp;t.exports=n((function(){var t=o("(?<a>b)","g");return"b"!==t.exec("b").groups.a||"bc"!=="b".replace(t,"$<a>c")}))},7750:(t,e,r)=>{var n=r(4117),o=TypeError;t.exports=function(t){if(n(t))throw new o("Can't call method on "+t);return t}},3389:(t,e,r)=>{var n=r(4576),o=r(3724),i=Object.getOwnPropertyDescriptor;t.exports=function(t){if(!o)return n[t];var e=i(n,t);return e&&e.value}},7633:(t,e,r)=>{var n=r(7751),o=r(2106),i=r(8227),a=r(3724),c=i("species");t.exports=function(t){var e=n(t);a&&e&&!e[c]&&o(e,c,{configurable:!0,get:function(){return this}})}},687:(t,e,r)=>{var n=r(4913).f,o=r(9297),i=r(8227)("toStringTag");t.exports=function(t,e,r){t&&!r&&(t=t.prototype),t&&!o(t,i)&&n(t,i,{configurable:!0,value:e})}},6119:(t,e,r)=>{var n=r(5745),o=r(3392),i=n("keys");t.exports=function(t){return i[t]||(i[t]=o(t))}},7629:(t,e,r)=>{var n=r(6395),o=r(4576),i=r(9433),a="__core-js_shared__",c=t.exports=o[a]||i(a,{});(c.versions||(c.versions=[])).push({version:"3.39.0",mode:n?"pure":"global",copyright:"© 2014-2024 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.39.0/LICENSE",source:"https://github.com/zloirock/core-js"})},5745:(t,e,r)=>{var n=r(7629);t.exports=function(t,e){return n[t]||(n[t]=e||{})}},2293:(t,e,r)=>{var n=r(8551),o=r(5548),i=r(4117),a=r(8227)("species");t.exports=function(t,e){var r,c=n(t).constructor;return void 0===c||i(r=n(c)[a])?e:o(r)}},8183:(t,e,r)=>{var n=r(9504),o=r(1291),i=r(655),a=r(7750),c=n("".charAt),u=n("".charCodeAt),s=n("".slice),l=function(t){return function(e,r){var n,l,f=i(a(e)),p=o(r),h=f.length;return p<0||p>=h?t?"":void 0:(n=u(f,p))<55296||n>56319||p+1===h||(l=u(f,p+1))<56320||l>57343?t?c(f,p):n:t?s(f,p,p+2):l-56320+(n-55296<<10)+65536}};t.exports={codeAt:l(!1),charAt:l(!0)}},2333:(t,e,r)=>{var n=r(1291),o=r(655),i=r(7750),a=RangeError;t.exports=function(t){var e=o(i(this)),r="",c=n(t);if(c<0||c===1/0)throw new a("Wrong number of repetitions");for(;c>0;(c>>>=1)&&(e+=e))1&c&&(r+=e);return r}},3802:(t,e,r)=>{var n=r(9504),o=r(7750),i=r(655),a=r(7452),c=n("".replace),u=RegExp("^["+a+"]+"),s=RegExp("(^|[^"+a+"])["+a+"]+$"),l=function(t){return function(e){var r=i(o(e));return 1&t&&(r=c(r,u,"")),2&t&&(r=c(r,s,"$1")),r}};t.exports={start:l(1),end:l(2),trim:l(3)}},4495:(t,e,r)=>{var n=r(9519),o=r(9039),i=r(4576).String;t.exports=!!Object.getOwnPropertySymbols&&!o((function(){var t=Symbol("symbol detection");return!i(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&n&&n<41}))},8242:(t,e,r)=>{var n=r(9565),o=r(7751),i=r(8227),a=r(6840);t.exports=function(){var t=o("Symbol"),e=t&&t.prototype,r=e&&e.valueOf,c=i("toPrimitive");e&&!e[c]&&a(e,c,(function(t){return n(r,this)}),{arity:1})}},1296:(t,e,r)=>{var n=r(4495);t.exports=n&&!!Symbol.for&&!!Symbol.keyFor},9225:(t,e,r)=>{var n,o,i,a,c=r(4576),u=r(8745),s=r(6080),l=r(4901),f=r(9297),p=r(9039),h=r(397),d=r(7680),y=r(4055),v=r(2812),g=r(9544),m=r(8574),b=c.setImmediate,w=c.clearImmediate,x=c.process,E=c.Dispatch,S=c.Function,O=c.MessageChannel,j=c.String,_=0,P={},k="onreadystatechange";p((function(){n=c.location}));var C=function(t){if(f(P,t)){var e=P[t];delete P[t],e()}},I=function(t){return function(){C(t)}},T=function(t){C(t.data)},A=function(t){c.postMessage(j(t),n.protocol+"//"+n.host)};b&&w||(b=function(t){v(arguments.length,1);var e=l(t)?t:S(t),r=d(arguments,1);return P[++_]=function(){u(e,void 0,r)},o(_),_},w=function(t){delete P[t]},m?o=function(t){x.nextTick(I(t))}:E&&E.now?o=function(t){E.now(I(t))}:O&&!g?(a=(i=new O).port2,i.port1.onmessage=T,o=s(a.postMessage,a)):c.addEventListener&&l(c.postMessage)&&!c.importScripts&&n&&"file:"!==n.protocol&&!p(A)?(o=A,c.addEventListener("message",T,!1)):o=k in y("script")?function(t){h.appendChild(y("script"))[k]=function(){h.removeChild(this),C(t)}}:function(t){setTimeout(I(t),0)}),t.exports={set:b,clear:w}},1240:(t,e,r)=>{var n=r(9504);t.exports=n(1..valueOf)},5610:(t,e,r)=>{var n=r(1291),o=Math.max,i=Math.min;t.exports=function(t,e){var r=n(t);return r<0?o(r+e,0):i(r,e)}},5397:(t,e,r)=>{var n=r(7055),o=r(7750);t.exports=function(t){return n(o(t))}},1291:(t,e,r)=>{var n=r(741);t.exports=function(t){var e=+t;return e!=e||0===e?0:n(e)}},8014:(t,e,r)=>{var n=r(1291),o=Math.min;t.exports=function(t){var e=n(t);return e>0?o(e,9007199254740991):0}},8981:(t,e,r)=>{var n=r(7750),o=Object;t.exports=function(t){return o(n(t))}},2777:(t,e,r)=>{var n=r(9565),o=r(34),i=r(757),a=r(5966),c=r(4270),u=r(8227),s=TypeError,l=u("toPrimitive");t.exports=function(t,e){if(!o(t)||i(t))return t;var r,u=a(t,l);if(u){if(void 0===e&&(e="default"),r=n(u,t,e),!o(r)||i(r))return r;throw new s("Can't convert object to primitive value")}return void 0===e&&(e="number"),c(t,e)}},6969:(t,e,r)=>{var n=r(2777),o=r(757);t.exports=function(t){var e=n(t,"string");return o(e)?e:e+""}},2140:(t,e,r)=>{var n={};n[r(8227)("toStringTag")]="z",t.exports="[object z]"===String(n)},655:(t,e,r)=>{var n=r(6955),o=String;t.exports=function(t){if("Symbol"===n(t))throw new TypeError("Cannot convert a Symbol value to a string");return o(t)}},6823:t=>{var e=String;t.exports=function(t){try{return e(t)}catch(t){return"Object"}}},3392:(t,e,r)=>{var n=r(9504),o=0,i=Math.random(),a=n(1..toString);t.exports=function(t){return"Symbol("+(void 0===t?"":t)+")_"+a(++o+i,36)}},7040:(t,e,r)=>{var n=r(4495);t.exports=n&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},8686:(t,e,r)=>{var n=r(3724),o=r(9039);t.exports=n&&o((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))},2812:t=>{var e=TypeError;t.exports=function(t,r){if(t<r)throw new e("Not enough arguments");return t}},8622:(t,e,r)=>{var n=r(4576),o=r(4901),i=n.WeakMap;t.exports=o(i)&&/native code/.test(String(i))},511:(t,e,r)=>{var n=r(9167),o=r(9297),i=r(1951),a=r(4913).f;t.exports=function(t){var e=n.Symbol||(n.Symbol={});o(e,t)||a(e,t,{value:i.f(t)})}},1951:(t,e,r)=>{var n=r(8227);e.f=n},8227:(t,e,r)=>{var n=r(4576),o=r(5745),i=r(9297),a=r(3392),c=r(4495),u=r(7040),s=n.Symbol,l=o("wks"),f=u?s.for||s:s&&s.withoutSetter||a;t.exports=function(t){return i(l,t)||(l[t]=c&&i(s,t)?s[t]:f("Symbol."+t)),l[t]}},7452:t=>{t.exports="\t\n\v\f\r                　\u2028\u2029\ufeff"},4601:(t,e,r)=>{var n=r(7751),o=r(9297),i=r(6699),a=r(1625),c=r(2967),u=r(7740),s=r(1056),l=r(3167),f=r(2603),p=r(7584),h=r(747),d=r(3724),y=r(6395);t.exports=function(t,e,r,v){var g="stackTraceLimit",m=v?2:1,b=t.split("."),w=b[b.length-1],x=n.apply(null,b);if(x){var E=x.prototype;if(!y&&o(E,"cause")&&delete E.cause,!r)return x;var S=n("Error"),O=e((function(t,e){var r=f(v?e:t,void 0),n=v?new x(t):new x;return void 0!==r&&i(n,"message",r),h(n,O,n.stack,2),this&&a(E,this)&&l(n,this,O),arguments.length>m&&p(n,arguments[m]),n}));if(O.prototype=E,"Error"!==w?c?c(O,S):u(O,S,{name:!0}):d&&g in x&&(s(O,x,g),s(O,x,"prepareStackTrace")),u(O,x),!y)try{E.name!==w&&i(E,"name",w),E.constructor=O}catch(t){}return O}}},8706:(t,e,r)=>{var n=r(6518),o=r(9039),i=r(4376),a=r(34),c=r(8981),u=r(6198),s=r(6837),l=r(2278),f=r(1469),p=r(597),h=r(8227),d=r(9519),y=h("isConcatSpreadable"),v=d>=51||!o((function(){var t=[];return t[y]=!1,t.concat()[0]!==t})),g=function(t){if(!a(t))return!1;var e=t[y];return void 0!==e?!!e:i(t)};n({target:"Array",proto:!0,arity:1,forced:!v||!p("concat")},{concat:function(t){var e,r,n,o,i,a=c(this),p=f(a,0),h=0;for(e=-1,n=arguments.length;e<n;e++)if(g(i=-1===e?a:arguments[e]))for(o=u(i),s(h+o),r=0;r<o;r++,h++)r in i&&l(p,h,i[r]);else s(h+1),l(p,h++,i);return p.length=h,p}})},2008:(t,e,r)=>{var n=r(6518),o=r(9213).filter;n({target:"Array",proto:!0,forced:!r(597)("filter")},{filter:function(t){return o(this,t,arguments.length>1?arguments[1]:void 0)}})},113:(t,e,r)=>{var n=r(6518),o=r(9213).find,i=r(6469),a="find",c=!0;a in[]&&Array(1)[a]((function(){c=!1})),n({target:"Array",proto:!0,forced:c},{find:function(t){return o(this,t,arguments.length>1?arguments[1]:void 0)}}),i(a)},3418:(t,e,r)=>{var n=r(6518),o=r(7916);n({target:"Array",stat:!0,forced:!r(4428)((function(t){Array.from(t)}))},{from:o})},4423:(t,e,r)=>{var n=r(6518),o=r(9617).includes,i=r(9039),a=r(6469);n({target:"Array",proto:!0,forced:i((function(){return!Array(1).includes()}))},{includes:function(t){return o(this,t,arguments.length>1?arguments[1]:void 0)}}),a("includes")},3792:(t,e,r)=>{var n=r(5397),o=r(6469),i=r(6269),a=r(1181),c=r(4913).f,u=r(1088),s=r(2529),l=r(6395),f=r(3724),p="Array Iterator",h=a.set,d=a.getterFor(p);t.exports=u(Array,"Array",(function(t,e){h(this,{type:p,target:n(t),index:0,kind:e})}),(function(){var t=d(this),e=t.target,r=t.index++;if(!e||r>=e.length)return t.target=null,s(void 0,!0);switch(t.kind){case"keys":return s(r,!1);case"values":return s(e[r],!1)}return s([r,e[r]],!1)}),"values");var y=i.Arguments=i.Array;if(o("keys"),o("values"),o("entries"),!l&&f&&"values"!==y.name)try{c(y,"name",{value:"values"})}catch(t){}},2062:(t,e,r)=>{var n=r(6518),o=r(9213).map;n({target:"Array",proto:!0,forced:!r(597)("map")},{map:function(t){return o(this,t,arguments.length>1?arguments[1]:void 0)}})},4114:(t,e,r)=>{var n=r(6518),o=r(8981),i=r(6198),a=r(4527),c=r(6837);n({target:"Array",proto:!0,arity:1,forced:r(9039)((function(){return 4294967297!==[].push.call({length:4294967296},1)}))||!function(){try{Object.defineProperty([],"length",{writable:!1}).push()}catch(t){return t instanceof TypeError}}()},{push:function(t){var e=o(this),r=i(e),n=arguments.length;c(r+n);for(var u=0;u<n;u++)e[r]=arguments[u],r++;return a(e,r),r}})},4490:(t,e,r)=>{var n=r(6518),o=r(9504),i=r(4376),a=o([].reverse),c=[1,2];n({target:"Array",proto:!0,forced:String(c)===String(c.reverse())},{reverse:function(){return i(this)&&(this.length=this.length),a(this)}})},4782:(t,e,r)=>{var n=r(6518),o=r(4376),i=r(3517),a=r(34),c=r(5610),u=r(6198),s=r(5397),l=r(2278),f=r(8227),p=r(597),h=r(7680),d=p("slice"),y=f("species"),v=Array,g=Math.max;n({target:"Array",proto:!0,forced:!d},{slice:function(t,e){var r,n,f,p=s(this),d=u(p),m=c(t,d),b=c(void 0===e?d:e,d);if(o(p)&&(r=p.constructor,(i(r)&&(r===v||o(r.prototype))||a(r)&&null===(r=r[y]))&&(r=void 0),r===v||void 0===r))return h(p,m,b);for(n=new(void 0===r?v:r)(g(b-m,0)),f=0;m<b;m++,f++)m in p&&l(n,f,p[m]);return n.length=f,n}})},739:(t,e,r)=>{var n=r(6518),o=r(9039),i=r(8981),a=r(2777);n({target:"Date",proto:!0,arity:1,forced:o((function(){return null!==new Date(NaN).toJSON()||1!==Date.prototype.toJSON.call({toISOString:function(){return 1}})}))},{toJSON:function(t){var e=i(this),r=a(e,"number");return"number"!=typeof r||isFinite(r)?e.toISOString():null}})},9572:(t,e,r)=>{var n=r(9297),o=r(6840),i=r(3640),a=r(8227)("toPrimitive"),c=Date.prototype;n(c,a)||o(c,a,i)},6280:(t,e,r)=>{var n=r(6518),o=r(4576),i=r(8745),a=r(4601),c="WebAssembly",u=o[c],s=7!==new Error("e",{cause:7}).cause,l=function(t,e){var r={};r[t]=a(t,e,s),n({global:!0,constructor:!0,arity:1,forced:s},r)},f=function(t,e){if(u&&u[t]){var r={};r[t]=a(c+"."+t,e,s),n({target:c,stat:!0,constructor:!0,arity:1,forced:s},r)}};l("Error",(function(t){return function(e){return i(t,this,arguments)}})),l("EvalError",(function(t){return function(e){return i(t,this,arguments)}})),l("RangeError",(function(t){return function(e){return i(t,this,arguments)}})),l("ReferenceError",(function(t){return function(e){return i(t,this,arguments)}})),l("SyntaxError",(function(t){return function(e){return i(t,this,arguments)}})),l("TypeError",(function(t){return function(e){return i(t,this,arguments)}})),l("URIError",(function(t){return function(e){return i(t,this,arguments)}})),f("CompileError",(function(t){return function(e){return i(t,this,arguments)}})),f("LinkError",(function(t){return function(e){return i(t,this,arguments)}})),f("RuntimeError",(function(t){return function(e){return i(t,this,arguments)}}))},8111:(t,e,r)=>{var n=r(6518),o=r(4576),i=r(679),a=r(8551),c=r(4901),u=r(2787),s=r(2106),l=r(2278),f=r(9039),p=r(9297),h=r(8227),d=r(7657).IteratorPrototype,y=r(3724),v=r(6395),g="constructor",m="Iterator",b=h("toStringTag"),w=TypeError,x=o[m],E=v||!c(x)||x.prototype!==d||!f((function(){x({})})),S=function(){if(i(this,d),u(this)===d)throw new w("Abstract class Iterator not directly constructable")},O=function(t,e){y?s(d,t,{configurable:!0,get:function(){return e},set:function(e){if(a(this),this===d)throw new w("You can't redefine this property");p(this,t)?this[t]=e:l(this,t,e)}}):d[t]=e};p(d,b)||O(b,m),!E&&p(d,g)&&d[g]!==Object||O(g,S),S.prototype=d,n({global:!0,constructor:!0,forced:E},{Iterator:S})},2489:(t,e,r)=>{var n=r(6518),o=r(9565),i=r(9306),a=r(8551),c=r(1767),u=r(9462),s=r(6319),l=r(6395),f=u((function(){for(var t,e,r=this.iterator,n=this.predicate,i=this.next;;){if(t=a(o(i,r)),this.done=!!t.done)return;if(e=t.value,s(r,n,[e,this.counter++],!0))return e}}));n({target:"Iterator",proto:!0,real:!0,forced:l},{filter:function(t){return a(this),i(t),new f(c(this),{predicate:t})}})},116:(t,e,r)=>{var n=r(6518),o=r(2652),i=r(9306),a=r(8551),c=r(1767);n({target:"Iterator",proto:!0,real:!0},{find:function(t){a(this),i(t);var e=c(this),r=0;return o(e,(function(e,n){if(t(e,r++))return n(e)}),{IS_RECORD:!0,INTERRUPTED:!0}).result}})},7588:(t,e,r)=>{var n=r(6518),o=r(2652),i=r(9306),a=r(8551),c=r(1767);n({target:"Iterator",proto:!0,real:!0},{forEach:function(t){a(this),i(t);var e=c(this),r=0;o(e,(function(e){t(e,r++)}),{IS_RECORD:!0})}})},1701:(t,e,r)=>{var n=r(6518),o=r(713);n({target:"Iterator",proto:!0,real:!0,forced:r(6395)},{map:o})},3579:(t,e,r)=>{var n=r(6518),o=r(2652),i=r(9306),a=r(8551),c=r(1767);n({target:"Iterator",proto:!0,real:!0},{some:function(t){a(this),i(t);var e=c(this),r=0;return o(e,(function(e,n){if(t(e,r++))return n()}),{IS_RECORD:!0,INTERRUPTED:!0}).stopped}})},3110:(t,e,r)=>{var n=r(6518),o=r(7751),i=r(8745),a=r(9565),c=r(9504),u=r(9039),s=r(4901),l=r(757),f=r(7680),p=r(6933),h=r(4495),d=String,y=o("JSON","stringify"),v=c(/./.exec),g=c("".charAt),m=c("".charCodeAt),b=c("".replace),w=c(1..toString),x=/[\uD800-\uDFFF]/g,E=/^[\uD800-\uDBFF]$/,S=/^[\uDC00-\uDFFF]$/,O=!h||u((function(){var t=o("Symbol")("stringify detection");return"[null]"!==y([t])||"{}"!==y({a:t})||"{}"!==y(Object(t))})),j=u((function(){return'"\\udf06\\ud834"'!==y("\udf06\ud834")||'"\\udead"'!==y("\udead")})),_=function(t,e){var r=f(arguments),n=p(e);if(s(n)||void 0!==t&&!l(t))return r[1]=function(t,e){if(s(n)&&(e=a(n,this,d(t),e)),!l(e))return e},i(y,null,r)},P=function(t,e,r){var n=g(r,e-1),o=g(r,e+1);return v(E,t)&&!v(S,o)||v(S,t)&&!v(E,n)?"\\u"+w(m(t,0),16):t};y&&n({target:"JSON",stat:!0,arity:3,forced:O||j},{stringify:function(t,e,r){var n=f(arguments),o=i(O?_:y,null,n);return j&&"string"==typeof o?b(o,x,P):o}})},4731:(t,e,r)=>{var n=r(4576);r(687)(n.JSON,"JSON",!0)},479:(t,e,r)=>{r(687)(Math,"Math",!0)},2892:(t,e,r)=>{var n=r(6518),o=r(6395),i=r(3724),a=r(4576),c=r(9167),u=r(9504),s=r(2796),l=r(9297),f=r(3167),p=r(1625),h=r(757),d=r(2777),y=r(9039),v=r(8480).f,g=r(7347).f,m=r(4913).f,b=r(1240),w=r(3802).trim,x="Number",E=a[x],S=c[x],O=E.prototype,j=a.TypeError,_=u("".slice),P=u("".charCodeAt),k=s(x,!E(" 0o1")||!E("0b1")||E("+0x1")),C=function(t){var e,r=arguments.length<1?0:E(function(t){var e=d(t,"number");return"bigint"==typeof e?e:function(t){var e,r,n,o,i,a,c,u,s=d(t,"number");if(h(s))throw new j("Cannot convert a Symbol value to a number");if("string"==typeof s&&s.length>2)if(s=w(s),43===(e=P(s,0))||45===e){if(88===(r=P(s,2))||120===r)return NaN}else if(48===e){switch(P(s,1)){case 66:case 98:n=2,o=49;break;case 79:case 111:n=8,o=55;break;default:return+s}for(a=(i=_(s,2)).length,c=0;c<a;c++)if((u=P(i,c))<48||u>o)return NaN;return parseInt(i,n)}return+s}(e)}(t));return p(O,e=this)&&y((function(){b(e)}))?f(Object(r),this,C):r};C.prototype=O,k&&!o&&(O.constructor=C),n({global:!0,constructor:!0,wrap:!0,forced:k},{Number:C});var I=function(t,e){for(var r,n=i?v(e):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,isFinite,isInteger,isNaN,isSafeInteger,parseFloat,parseInt,fromString,range".split(","),o=0;n.length>o;o++)l(e,r=n[o])&&!l(t,r)&&m(t,r,g(e,r))};o&&S&&I(c[x],S),(k||o)&&I(c[x],E)},9868:(t,e,r)=>{var n=r(6518),o=r(9504),i=r(1291),a=r(1240),c=r(2333),u=r(9039),s=RangeError,l=String,f=Math.floor,p=o(c),h=o("".slice),d=o(1..toFixed),y=function(t,e,r){return 0===e?r:e%2==1?y(t,e-1,r*t):y(t*t,e/2,r)},v=function(t,e,r){for(var n=-1,o=r;++n<6;)o+=e*t[n],t[n]=o%1e7,o=f(o/1e7)},g=function(t,e){for(var r=6,n=0;--r>=0;)n+=t[r],t[r]=f(n/e),n=n%e*1e7},m=function(t){for(var e=6,r="";--e>=0;)if(""!==r||0===e||0!==t[e]){var n=l(t[e]);r=""===r?n:r+p("0",7-n.length)+n}return r};n({target:"Number",proto:!0,forced:u((function(){return"0.000"!==d(8e-5,3)||"1"!==d(.9,0)||"1.25"!==d(1.255,2)||"1000000000000000128"!==d(0xde0b6b3a7640080,0)}))||!u((function(){d({})}))},{toFixed:function(t){var e,r,n,o,c=a(this),u=i(t),f=[0,0,0,0,0,0],d="",b="0";if(u<0||u>20)throw new s("Incorrect fraction digits");if(c!=c)return"NaN";if(c<=-1e21||c>=1e21)return l(c);if(c<0&&(d="-",c=-c),c>1e-21)if(r=(e=function(t){for(var e=0,r=t;r>=4096;)e+=12,r/=4096;for(;r>=2;)e+=1,r/=2;return e}(c*y(2,69,1))-69)<0?c*y(2,-e,1):c/y(2,e,1),r*=4503599627370496,(e=52-e)>0){for(v(f,0,r),n=u;n>=7;)v(f,1e7,0),n-=7;for(v(f,y(10,n,1),0),n=e-1;n>=23;)g(f,1<<23),n-=23;g(f,1<<n),v(f,1,1),g(f,2),b=m(f)}else v(f,0,r),v(f,1<<-e,0),b=m(f)+p("0",u);return u>0?d+((o=b.length)<=u?"0."+p("0",u-o)+b:h(b,0,o-u)+"."+h(b,o-u)):d+b}})},9085:(t,e,r)=>{var n=r(6518),o=r(4213);n({target:"Object",stat:!0,arity:2,forced:Object.assign!==o},{assign:o})},3851:(t,e,r)=>{var n=r(6518),o=r(9039),i=r(5397),a=r(7347).f,c=r(3724);n({target:"Object",stat:!0,forced:!c||o((function(){a(1)})),sham:!c},{getOwnPropertyDescriptor:function(t,e){return a(i(t),e)}})},1278:(t,e,r)=>{var n=r(6518),o=r(3724),i=r(5031),a=r(5397),c=r(7347),u=r(2278);n({target:"Object",stat:!0,sham:!o},{getOwnPropertyDescriptors:function(t){for(var e,r,n=a(t),o=c.f,s=i(n),l={},f=0;s.length>f;)void 0!==(r=o(n,e=s[f++]))&&u(l,e,r);return l}})},9773:(t,e,r)=>{var n=r(6518),o=r(4495),i=r(9039),a=r(3717),c=r(8981);n({target:"Object",stat:!0,forced:!o||i((function(){a.f(1)}))},{getOwnPropertySymbols:function(t){var e=a.f;return e?e(c(t)):[]}})},875:(t,e,r)=>{var n=r(6518),o=r(9039),i=r(8981),a=r(2787),c=r(2211);n({target:"Object",stat:!0,forced:o((function(){a(1)})),sham:!c},{getPrototypeOf:function(t){return a(i(t))}})},9432:(t,e,r)=>{var n=r(6518),o=r(8981),i=r(1072);n({target:"Object",stat:!0,forced:r(9039)((function(){i(1)}))},{keys:function(t){return i(o(t))}})},287:(t,e,r)=>{r(6518)({target:"Object",stat:!0},{setPrototypeOf:r(2967)})},6099:(t,e,r)=>{var n=r(2140),o=r(6840),i=r(3179);n||o(Object.prototype,"toString",i,{unsafe:!0})},8940:(t,e,r)=>{var n=r(6518),o=r(2703);n({global:!0,forced:parseInt!==o},{parseInt:o})},6499:(t,e,r)=>{var n=r(6518),o=r(9565),i=r(9306),a=r(6043),c=r(1103),u=r(2652);n({target:"Promise",stat:!0,forced:r(537)},{all:function(t){var e=this,r=a.f(e),n=r.resolve,s=r.reject,l=c((function(){var r=i(e.resolve),a=[],c=0,l=1;u(t,(function(t){var i=c++,u=!1;l++,o(r,e,t).then((function(t){u||(u=!0,a[i]=t,--l||n(a))}),s)})),--l||n(a)}));return l.error&&s(l.value),r.promise}})},2003:(t,e,r)=>{var n=r(6518),o=r(6395),i=r(916).CONSTRUCTOR,a=r(550),c=r(7751),u=r(4901),s=r(6840),l=a&&a.prototype;if(n({target:"Promise",proto:!0,forced:i,real:!0},{catch:function(t){return this.then(void 0,t)}}),!o&&u(a)){var f=c("Promise").prototype.catch;l.catch!==f&&s(l,"catch",f,{unsafe:!0})}},436:(t,e,r)=>{var n,o,i,a=r(6518),c=r(6395),u=r(8574),s=r(4576),l=r(9565),f=r(6840),p=r(2967),h=r(687),d=r(7633),y=r(9306),v=r(4901),g=r(34),m=r(679),b=r(2293),w=r(9225).set,x=r(1955),E=r(3138),S=r(1103),O=r(8265),j=r(1181),_=r(550),P=r(916),k=r(6043),C="Promise",I=P.CONSTRUCTOR,T=P.REJECTION_EVENT,A=P.SUBCLASSING,L=j.getterFor(C),R=j.set,N=_&&_.prototype,M=_,D=N,F=s.TypeError,B=s.document,G=s.process,H=k.f,q=H,U=!!(B&&B.createEvent&&s.dispatchEvent),W="unhandledrejection",$=function(t){var e;return!(!g(t)||!v(e=t.then))&&e},z=function(t,e){var r,n,o,i=e.value,a=1===e.state,c=a?t.ok:t.fail,u=t.resolve,s=t.reject,f=t.domain;try{c?(a||(2===e.rejection&&K(e),e.rejection=1),!0===c?r=i:(f&&f.enter(),r=c(i),f&&(f.exit(),o=!0)),r===t.promise?s(new F("Promise-chain cycle")):(n=$(r))?l(n,r,u,s):u(r)):s(i)}catch(t){f&&!o&&f.exit(),s(t)}},V=function(t,e){t.notified||(t.notified=!0,x((function(){for(var r,n=t.reactions;r=n.get();)z(r,t);t.notified=!1,e&&!t.rejection&&Y(t)})))},Q=function(t,e,r){var n,o;U?((n=B.createEvent("Event")).promise=e,n.reason=r,n.initEvent(t,!1,!0),s.dispatchEvent(n)):n={promise:e,reason:r},!T&&(o=s["on"+t])?o(n):t===W&&E("Unhandled promise rejection",r)},Y=function(t){l(w,s,(function(){var e,r=t.facade,n=t.value;if(J(t)&&(e=S((function(){u?G.emit("unhandledRejection",n,r):Q(W,r,n)})),t.rejection=u||J(t)?2:1,e.error))throw e.value}))},J=function(t){return 1!==t.rejection&&!t.parent},K=function(t){l(w,s,(function(){var e=t.facade;u?G.emit("rejectionHandled",e):Q("rejectionhandled",e,t.value)}))},X=function(t,e,r){return function(n){t(e,n,r)}},Z=function(t,e,r){t.done||(t.done=!0,r&&(t=r),t.value=e,t.state=2,V(t,!0))},tt=function(t,e,r){if(!t.done){t.done=!0,r&&(t=r);try{if(t.facade===e)throw new F("Promise can't be resolved itself");var n=$(e);n?x((function(){var r={done:!1};try{l(n,e,X(tt,r,t),X(Z,r,t))}catch(e){Z(r,e,t)}})):(t.value=e,t.state=1,V(t,!1))}catch(e){Z({done:!1},e,t)}}};if(I&&(D=(M=function(t){m(this,D),y(t),l(n,this);var e=L(this);try{t(X(tt,e),X(Z,e))}catch(t){Z(e,t)}}).prototype,(n=function(t){R(this,{type:C,done:!1,notified:!1,parent:!1,reactions:new O,rejection:!1,state:0,value:null})}).prototype=f(D,"then",(function(t,e){var r=L(this),n=H(b(this,M));return r.parent=!0,n.ok=!v(t)||t,n.fail=v(e)&&e,n.domain=u?G.domain:void 0,0===r.state?r.reactions.add(n):x((function(){z(n,r)})),n.promise})),o=function(){var t=new n,e=L(t);this.promise=t,this.resolve=X(tt,e),this.reject=X(Z,e)},k.f=H=function(t){return t===M||void 0===t?new o(t):q(t)},!c&&v(_)&&N!==Object.prototype)){i=N.then,A||f(N,"then",(function(t,e){var r=this;return new M((function(t,e){l(i,r,t,e)})).then(t,e)}),{unsafe:!0});try{delete N.constructor}catch(t){}p&&p(N,D)}a({global:!0,constructor:!0,wrap:!0,forced:I},{Promise:M}),h(M,C,!1,!0),d(C)},3362:(t,e,r)=>{r(436),r(6499),r(2003),r(7743),r(1481),r(280)},7743:(t,e,r)=>{var n=r(6518),o=r(9565),i=r(9306),a=r(6043),c=r(1103),u=r(2652);n({target:"Promise",stat:!0,forced:r(537)},{race:function(t){var e=this,r=a.f(e),n=r.reject,s=c((function(){var a=i(e.resolve);u(t,(function(t){o(a,e,t).then(r.resolve,n)}))}));return s.error&&n(s.value),r.promise}})},1481:(t,e,r)=>{var n=r(6518),o=r(6043);n({target:"Promise",stat:!0,forced:r(916).CONSTRUCTOR},{reject:function(t){var e=o.f(this);return(0,e.reject)(t),e.promise}})},280:(t,e,r)=>{var n=r(6518),o=r(7751),i=r(6395),a=r(550),c=r(916).CONSTRUCTOR,u=r(3438),s=o("Promise"),l=i&&!c;n({target:"Promise",stat:!0,forced:i||c},{resolve:function(t){return u(l&&this===s?a:this,t)}})},825:(t,e,r)=>{var n=r(6518),o=r(7751),i=r(8745),a=r(566),c=r(5548),u=r(8551),s=r(34),l=r(2360),f=r(9039),p=o("Reflect","construct"),h=Object.prototype,d=[].push,y=f((function(){function t(){}return!(p((function(){}),[],t)instanceof t)})),v=!f((function(){p((function(){}))})),g=y||v;n({target:"Reflect",stat:!0,forced:g,sham:g},{construct:function(t,e){c(t),u(e);var r=arguments.length<3?t:c(arguments[2]);if(v&&!y)return p(t,e,r);if(t===r){switch(e.length){case 0:return new t;case 1:return new t(e[0]);case 2:return new t(e[0],e[1]);case 3:return new t(e[0],e[1],e[2]);case 4:return new t(e[0],e[1],e[2],e[3])}var n=[null];return i(d,n,e),new(i(a,t,n))}var o=r.prototype,f=l(s(o)?o:h),g=i(t,f,e);return s(g)?g:f}})},888:(t,e,r)=>{var n=r(6518),o=r(9565),i=r(34),a=r(8551),c=r(6575),u=r(7347),s=r(2787);n({target:"Reflect",stat:!0},{get:function t(e,r){var n,l,f=arguments.length<3?e:arguments[2];return a(e)===f?e[r]:(n=u.f(e,r))?c(n)?n.value:void 0===n.get?void 0:o(n.get,f):i(l=s(e))?t(l,r,f):void 0}})},5472:(t,e,r)=>{var n=r(6518),o=r(4576),i=r(687);n({global:!0},{Reflect:{}}),i(o.Reflect,"Reflect",!0)},7495:(t,e,r)=>{var n=r(6518),o=r(7323);n({target:"RegExp",proto:!0,forced:/./.exec!==o},{exec:o})},906:(t,e,r)=>{r(7495);var n,o,i=r(6518),a=r(9565),c=r(4901),u=r(8551),s=r(655),l=(n=!1,(o=/[ac]/).exec=function(){return n=!0,/./.exec.apply(this,arguments)},!0===o.test("abc")&&n),f=/./.test;i({target:"RegExp",proto:!0,forced:!l},{test:function(t){var e=u(this),r=s(t),n=e.exec;if(!c(n))return a(f,e,r);var o=a(n,e,r);return null!==o&&(u(o),!0)}})},8781:(t,e,r)=>{var n=r(350).PROPER,o=r(6840),i=r(8551),a=r(655),c=r(9039),u=r(1034),s="toString",l=RegExp.prototype,f=l[s],p=c((function(){return"/a/b"!==f.call({source:"a",flags:"b"})})),h=n&&f.name!==s;(p||h)&&o(l,s,(function(){var t=i(this);return"/"+a(t.source)+"/"+a(u(t))}),{unsafe:!0})},7764:(t,e,r)=>{var n=r(8183).charAt,o=r(655),i=r(1181),a=r(1088),c=r(2529),u="String Iterator",s=i.set,l=i.getterFor(u);a(String,"String",(function(t){s(this,{type:u,string:o(t),index:0})}),(function(){var t,e=l(this),r=e.string,o=e.index;return o>=r.length?c(void 0,!0):(t=n(r,o),e.index+=t.length,c(t,!1))}))},5440:(t,e,r)=>{var n=r(8745),o=r(9565),i=r(9504),a=r(9228),c=r(9039),u=r(8551),s=r(4901),l=r(4117),f=r(1291),p=r(8014),h=r(655),d=r(7750),y=r(7829),v=r(5966),g=r(2478),m=r(6682),b=r(8227)("replace"),w=Math.max,x=Math.min,E=i([].concat),S=i([].push),O=i("".indexOf),j=i("".slice),_="$0"==="a".replace(/./,"$0"),P=!!/./[b]&&""===/./[b]("a","$0");a("replace",(function(t,e,r){var i=P?"$":"$0";return[function(t,r){var n=d(this),i=l(t)?void 0:v(t,b);return i?o(i,t,n,r):o(e,h(n),t,r)},function(t,o){var a=u(this),c=h(t);if("string"==typeof o&&-1===O(o,i)&&-1===O(o,"$<")){var l=r(e,a,c,o);if(l.done)return l.value}var d=s(o);d||(o=h(o));var v,b=a.global;b&&(v=a.unicode,a.lastIndex=0);for(var _,P=[];null!==(_=m(a,c))&&(S(P,_),b);)""===h(_[0])&&(a.lastIndex=y(c,p(a.lastIndex),v));for(var k,C="",I=0,T=0;T<P.length;T++){for(var A,L=h((_=P[T])[0]),R=w(x(f(_.index),c.length),0),N=[],M=1;M<_.length;M++)S(N,void 0===(k=_[M])?k:String(k));var D=_.groups;if(d){var F=E([L],N,R,c);void 0!==D&&S(F,D),A=h(n(o,void 0,F))}else A=g(L,c,R,N,D,o);R>=I&&(C+=j(c,I,R)+A,I=R+L.length)}return C+j(c,I)}]}),!!c((function(){var t=/./;return t.exec=function(){var t=[];return t.groups={a:"7"},t},"7"!=="".replace(t,"$<a>")}))||!_||P)},6412:(t,e,r)=>{r(511)("asyncIterator")},6761:(t,e,r)=>{var n=r(6518),o=r(4576),i=r(9565),a=r(9504),c=r(6395),u=r(3724),s=r(4495),l=r(9039),f=r(9297),p=r(1625),h=r(8551),d=r(5397),y=r(6969),v=r(655),g=r(6980),m=r(2360),b=r(1072),w=r(8480),x=r(298),E=r(3717),S=r(7347),O=r(4913),j=r(6801),_=r(8773),P=r(6840),k=r(2106),C=r(5745),I=r(6119),T=r(421),A=r(3392),L=r(8227),R=r(1951),N=r(511),M=r(8242),D=r(687),F=r(1181),B=r(9213).forEach,G=I("hidden"),H="Symbol",q="prototype",U=F.set,W=F.getterFor(H),$=Object[q],z=o.Symbol,V=z&&z[q],Q=o.RangeError,Y=o.TypeError,J=o.QObject,K=S.f,X=O.f,Z=x.f,tt=_.f,et=a([].push),rt=C("symbols"),nt=C("op-symbols"),ot=C("wks"),it=!J||!J[q]||!J[q].findChild,at=function(t,e,r){var n=K($,e);n&&delete $[e],X(t,e,r),n&&t!==$&&X($,e,n)},ct=u&&l((function(){return 7!==m(X({},"a",{get:function(){return X(this,"a",{value:7}).a}})).a}))?at:X,ut=function(t,e){var r=rt[t]=m(V);return U(r,{type:H,tag:t,description:e}),u||(r.description=e),r},st=function(t,e,r){t===$&&st(nt,e,r),h(t);var n=y(e);return h(r),f(rt,n)?(r.enumerable?(f(t,G)&&t[G][n]&&(t[G][n]=!1),r=m(r,{enumerable:g(0,!1)})):(f(t,G)||X(t,G,g(1,m(null))),t[G][n]=!0),ct(t,n,r)):X(t,n,r)},lt=function(t,e){h(t);var r=d(e),n=b(r).concat(dt(r));return B(n,(function(e){u&&!i(ft,r,e)||st(t,e,r[e])})),t},ft=function(t){var e=y(t),r=i(tt,this,e);return!(this===$&&f(rt,e)&&!f(nt,e))&&(!(r||!f(this,e)||!f(rt,e)||f(this,G)&&this[G][e])||r)},pt=function(t,e){var r=d(t),n=y(e);if(r!==$||!f(rt,n)||f(nt,n)){var o=K(r,n);return!o||!f(rt,n)||f(r,G)&&r[G][n]||(o.enumerable=!0),o}},ht=function(t){var e=Z(d(t)),r=[];return B(e,(function(t){f(rt,t)||f(T,t)||et(r,t)})),r},dt=function(t){var e=t===$,r=Z(e?nt:d(t)),n=[];return B(r,(function(t){!f(rt,t)||e&&!f($,t)||et(n,rt[t])})),n};s||(P(V=(z=function(){if(p(V,this))throw new Y("Symbol is not a constructor");var t=arguments.length&&void 0!==arguments[0]?v(arguments[0]):void 0,e=A(t),r=function(t){var n=void 0===this?o:this;n===$&&i(r,nt,t),f(n,G)&&f(n[G],e)&&(n[G][e]=!1);var a=g(1,t);try{ct(n,e,a)}catch(t){if(!(t instanceof Q))throw t;at(n,e,a)}};return u&&it&&ct($,e,{configurable:!0,set:r}),ut(e,t)})[q],"toString",(function(){return W(this).tag})),P(z,"withoutSetter",(function(t){return ut(A(t),t)})),_.f=ft,O.f=st,j.f=lt,S.f=pt,w.f=x.f=ht,E.f=dt,R.f=function(t){return ut(L(t),t)},u&&(k(V,"description",{configurable:!0,get:function(){return W(this).description}}),c||P($,"propertyIsEnumerable",ft,{unsafe:!0}))),n({global:!0,constructor:!0,wrap:!0,forced:!s,sham:!s},{Symbol:z}),B(b(ot),(function(t){N(t)})),n({target:H,stat:!0,forced:!s},{useSetter:function(){it=!0},useSimple:function(){it=!1}}),n({target:"Object",stat:!0,forced:!s,sham:!u},{create:function(t,e){return void 0===e?m(t):lt(m(t),e)},defineProperty:st,defineProperties:lt,getOwnPropertyDescriptor:pt}),n({target:"Object",stat:!0,forced:!s},{getOwnPropertyNames:ht}),M(),D(z,H),T[G]=!0},9463:(t,e,r)=>{var n=r(6518),o=r(3724),i=r(4576),a=r(9504),c=r(9297),u=r(4901),s=r(1625),l=r(655),f=r(2106),p=r(7740),h=i.Symbol,d=h&&h.prototype;if(o&&u(h)&&(!("description"in d)||void 0!==h().description)){var y={},v=function(){var t=arguments.length<1||void 0===arguments[0]?void 0:l(arguments[0]),e=s(d,this)?new h(t):void 0===t?h():h(t);return""===t&&(y[e]=!0),e};p(v,h),v.prototype=d,d.constructor=v;var g="Symbol(description detection)"===String(h("description detection")),m=a(d.valueOf),b=a(d.toString),w=/^Symbol\((.*)\)[^)]+$/,x=a("".replace),E=a("".slice);f(d,"description",{configurable:!0,get:function(){var t=m(this);if(c(y,t))return"";var e=b(t),r=g?E(e,7,-1):x(e,w,"$1");return""===r?void 0:r}}),n({global:!0,constructor:!0,forced:!0},{Symbol:v})}},1510:(t,e,r)=>{var n=r(6518),o=r(7751),i=r(9297),a=r(655),c=r(5745),u=r(1296),s=c("string-to-symbol-registry"),l=c("symbol-to-string-registry");n({target:"Symbol",stat:!0,forced:!u},{for:function(t){var e=a(t);if(i(s,e))return s[e];var r=o("Symbol")(e);return s[e]=r,l[r]=e,r}})},2259:(t,e,r)=>{r(511)("iterator")},2675:(t,e,r)=>{r(6761),r(1510),r(7812),r(3110),r(9773)},7812:(t,e,r)=>{var n=r(6518),o=r(9297),i=r(757),a=r(6823),c=r(5745),u=r(1296),s=c("symbol-to-string-registry");n({target:"Symbol",stat:!0,forced:!u},{keyFor:function(t){if(!i(t))throw new TypeError(a(t)+" is not a symbol");if(o(s,t))return s[t]}})},5700:(t,e,r)=>{var n=r(511),o=r(8242);n("toPrimitive"),o()},8125:(t,e,r)=>{var n=r(7751),o=r(511),i=r(687);o("toStringTag"),i(n("Symbol"),"Symbol")},5746:(t,e,r)=>{var n,o=r(2744),i=r(4576),a=r(9504),c=r(6279),u=r(3451),s=r(6468),l=r(4006),f=r(34),p=r(1181).enforce,h=r(9039),d=r(8622),y=Object,v=Array.isArray,g=y.isExtensible,m=y.isFrozen,b=y.isSealed,w=y.freeze,x=y.seal,E=!i.ActiveXObject&&"ActiveXObject"in i,S=function(t){return function(){return t(this,arguments.length?arguments[0]:void 0)}},O=s("WeakMap",S,l),j=O.prototype,_=a(j.set);if(d)if(E){n=l.getConstructor(S,"WeakMap",!0),u.enable();var P=a(j.delete),k=a(j.has),C=a(j.get);c(j,{delete:function(t){if(f(t)&&!g(t)){var e=p(this);return e.frozen||(e.frozen=new n),P(this,t)||e.frozen.delete(t)}return P(this,t)},has:function(t){if(f(t)&&!g(t)){var e=p(this);return e.frozen||(e.frozen=new n),k(this,t)||e.frozen.has(t)}return k(this,t)},get:function(t){if(f(t)&&!g(t)){var e=p(this);return e.frozen||(e.frozen=new n),k(this,t)?C(this,t):e.frozen.get(t)}return C(this,t)},set:function(t,e){if(f(t)&&!g(t)){var r=p(this);r.frozen||(r.frozen=new n),k(this,t)?_(this,t,e):r.frozen.set(t,e)}else _(this,t,e);return this}})}else o&&h((function(){var t=w([]);return _(new O,t,1),!m(t)}))&&c(j,{set:function(t,e){var r;return v(t)&&(m(t)?r=w:b(t)&&(r=x)),_(this,t,e),r&&r(t),this}})},3772:(t,e,r)=>{r(5746)},8992:(t,e,r)=>{r(8111)},4520:(t,e,r)=>{r(2489)},2577:(t,e,r)=>{r(116)},3949:(t,e,r)=>{r(7588)},1454:(t,e,r)=>{r(1701)},7550:(t,e,r)=>{r(3579)},3500:(t,e,r)=>{var n=r(4576),o=r(7400),i=r(9296),a=r(235),c=r(6699),u=function(t){if(t&&t.forEach!==a)try{c(t,"forEach",a)}catch(e){t.forEach=a}};for(var s in o)o[s]&&u(n[s]&&n[s].prototype);u(i)},2953:(t,e,r)=>{var n=r(4576),o=r(7400),i=r(9296),a=r(3792),c=r(6699),u=r(687),s=r(8227)("iterator"),l=a.values,f=function(t,e){if(t){if(t[s]!==l)try{c(t,s,l)}catch(e){t[s]=l}if(u(t,e,!0),o[e])for(var r in a)if(t[r]!==a[r])try{c(t,r,a[r])}catch(e){t[r]=a[r]}}};for(var p in o)f(n[p]&&n[p].prototype,p);f(i,"DOMTokenList")}},e={};function r(n){var o=e[n];if(void 0!==o)return o.exports;var i=e[n]={exports:{}};return t[n].call(i.exports,i,i.exports,r),i.exports}r.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return r.d(e,{a:e}),e},r.d=(t,e)=>{for(var n in e)r.o(e,n)&&!r.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),r.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),r(2675),r(9463),r(2259),r(6280),r(3418),r(3792),r(4114),r(4782),r(6099),r(7495),r(906),r(8781),r(7764),r(5440),r(2953);const n=window.wp.element;function o(t,e){void 0===e&&(e=Promise),function(t,e){if("object"!=typeof t||null===t)throw new Error("Expected an options object.");if(void 0!==e&&"function"!=typeof e)throw new Error("Expected PromisePonyfill to be a function.")}(t,e);var r=t.url,n=t.attributes;if("string"!=typeof r||0===r.length)throw new Error("Invalid url.");if(void 0!==n&&"object"!=typeof n)throw new Error("Expected attributes to be an object.");return new e((function(t,e){if("undefined"==typeof document)return t();var o,i,a,c;i=(o={url:r,attributes:n,onSuccess:function(){return t()},onError:function(){var t=new Error('The script "'.concat(r,'" failed to load.'));return window.fetch?fetch(r).then((function(r){return 200===r.status&&e(t),r.text()})).then((function(t){var r=function(t){var e=t.split("/* Original Error:")[1];return e?e.replace(/\n/g,"").replace("*/","").trim():t}(t);e(new Error(r))})).catch((function(t){e(t)})):e(t)}}).onSuccess,a=o.onError,c=function(t,e){void 0===e&&(e={});var r=document.createElement("script");return r.src=t,Object.keys(e).forEach((function(t){r.setAttribute(t,e[t]),"data-csp-nonce"===t&&r.setAttribute("nonce",e["data-csp-nonce"])})),r}(o.url,o.attributes),c.onerror=a,c.onload=i,document.head.insertBefore(c,document.head.firstElementChild)}))}const i=window.wc.wcBlocksRegistry,a=window.wp.i18n;function c(t,e){void 0===e&&(e={});var r=document.createElement("script");return r.src=t,Object.keys(e).forEach((function(t){r.setAttribute(t,e[t]),"data-csp-nonce"===t&&r.setAttribute("nonce",e["data-csp-nonce"])})),r}function u(t,e){if(void 0===e&&(e=Promise),l(t,e),"undefined"==typeof document)return e.resolve(null);var r=function(t){var e="https://www.paypal.com/sdk/js";t.sdkBaseUrl&&(e=t.sdkBaseUrl,delete t.sdkBaseUrl);var r,n,o=t,i=Object.keys(o).filter((function(t){return void 0!==o[t]&&null!==o[t]&&""!==o[t]})).reduce((function(t,e){var r,n=o[e].toString();return r=function(t,e){return(e?"-":"")+t.toLowerCase()},"data"===(e=e.replace(/[A-Z]+(?![a-z])|[A-Z]/g,r)).substring(0,4)?t.dataAttributes[e]=n:t.queryParams[e]=n,t}),{queryParams:{},dataAttributes:{}}),a=i.queryParams,c=i.dataAttributes;return a["merchant-id"]&&-1!==a["merchant-id"].indexOf(",")&&(c["data-merchant-id"]=a["merchant-id"],a["merchant-id"]="*"),{url:"".concat(e,"?").concat((r=a,n="",Object.keys(r).forEach((function(t){0!==n.length&&(n+="&"),n+=t+"="+r[t]})),n)),dataAttributes:c}}(t),n=r.url,o=r.dataAttributes,i=o["data-namespace"]||"paypal",a=s(i);return function(t,e){var r=document.querySelector('script[src="'.concat(t,'"]'));if(null===r)return null;var n=c(t,e),o=r.cloneNode();if(delete o.dataset.uidAuto,Object.keys(o.dataset).length!==Object.keys(n.dataset).length)return null;var i=!0;return Object.keys(o.dataset).forEach((function(t){o.dataset[t]!==n.dataset[t]&&(i=!1)})),i?r:null}(n,o)&&a?e.resolve(a):function(t,e){void 0===e&&(e=Promise),l(t,e);var r=t.url,n=t.attributes;if("string"!=typeof r||0===r.length)throw new Error("Invalid url.");if(void 0!==n&&"object"!=typeof n)throw new Error("Expected attributes to be an object.");return new e((function(t,e){if("undefined"==typeof document)return t();!function(t){var e=t.onSuccess,r=t.onError,n=c(t.url,t.attributes);n.onerror=r,n.onload=e,document.head.insertBefore(n,document.head.firstElementChild)}({url:r,attributes:n,onSuccess:function(){return t()},onError:function(){var t=new Error('The script "'.concat(r,'" failed to load.'));return window.fetch?fetch(r).then((function(r){return 200===r.status&&e(t),r.text()})).then((function(t){var r=function(t){var e=t.split("/* Original Error:")[1];return e?e.replace(/\n/g,"").replace("*/","").trim():t}(t);e(new Error(r))})).catch((function(t){e(t)})):e(t)}})}))}({url:n,attributes:o},e).then((function(){var t=s(i);if(t)return t;throw new Error("The window.".concat(i," global variable is not available."))}))}function s(t){return window[t]}function l(t,e){if("object"!=typeof t||null===t)throw new Error("Expected an options object.");if(void 0!==e&&"function"!=typeof e)throw new Error("Expected PromisePonyfill to be a function.")}const f=(t,e,r,n=null)=>{fetch(e.endpoint,{method:"POST",headers:{"Content-Type":"application/json"},credentials:"same-origin",body:JSON.stringify({nonce:e.nonce})}).then((t=>t.json())).then((o=>{var i;((t,e)=>!(!t||t.user!==e||(new Date).getTime()>=1e3*t.expiration))(o,e.user)&&(i=o,sessionStorage.setItem("ppcp-data-client-id",JSON.stringify(i)),t["data-client-token"]=o.token,u(t).then((t=>{"function"==typeof r&&r(t)})).catch((t=>{"function"==typeof n&&n(t)})))}))};window.widgetBuilder=window.widgetBuilder||new class{constructor(){this.paypal=null,this.buttons=new Map,this.messages=new Map,this.renderEventName="ppcp-render",document.ppcpWidgetBuilderStatus=()=>{console.log({buttons:this.buttons,messages:this.messages})},jQuery(document).off(this.renderEventName).on(this.renderEventName,(()=>{this.renderAll()}))}setPaypal(t){this.paypal=t,jQuery(document).trigger("ppcp-paypal-loaded",t)}registerButtons(t,e){t=this.sanitizeWrapper(t),this.buttons.set(this.toKey(t),{wrapper:t,options:e})}renderButtons(t){t=this.sanitizeWrapper(t);const e=this.toKey(t);if(!this.buttons.has(e))return;if(this.hasRendered(t))return;const r=this.buttons.get(e),n=this.paypal.Buttons(r.options);if(!n.isEligible())return void this.buttons.delete(e);const o=this.buildWrapperTarget(t);o&&n.render(o)}renderAllButtons(){for(const[t]of this.buttons)this.renderButtons(t)}registerMessages(t,e){this.messages.set(t,{wrapper:t,options:e})}renderMessages(t){if(!this.messages.has(t))return;const e=this.messages.get(t);if(this.hasRendered(t))return void document.querySelector(t).setAttribute("data-pp-amount",e.options.amount);const r=this.paypal.Messages(e.options);r.render(e.wrapper),setTimeout((()=>{this.hasRendered(t)||r.render(e.wrapper)}),100)}renderAllMessages(){for(const[t,e]of this.messages)this.renderMessages(t)}renderAll(){this.renderAllButtons(),this.renderAllMessages()}hasRendered(t){let e=t;if(Array.isArray(t)){e=t[0];for(const r of t.slice(1))e+=" .item-"+r}const r=document.querySelector(e);return r&&r.hasChildNodes()}sanitizeWrapper(t){return Array.isArray(t)&&1===(t=t.filter((t=>!!t))).length&&(t=t[0]),t}buildWrapperTarget(t){let e=t;if(Array.isArray(t)){const r=jQuery(t[0]);if(!r.length)return;const n="item-"+t[1];let o=r.find("."+n);o.length||(o=jQuery(`<div class="${n}"></div>`),r.append(o)),e=o.get(0)}return jQuery(e).length?e:null}toKey(t){return Array.isArray(t)?JSON.stringify(t):t}};const p=window.widgetBuilder;var h=r(9457),d=r.n(h);const y={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};let v;const g=new Uint8Array(16);function m(){if(!v){if("undefined"==typeof crypto||!crypto.getRandomValues)throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");v=crypto.getRandomValues.bind(crypto)}return v(g)}const b=[];for(let t=0;t<256;++t)b.push((t+256).toString(16).slice(1));const w=function(t,e,r){if(y.randomUUID&&!e&&!t)return y.randomUUID();const n=(t=t||{}).random||(t.rng||m)();if(n[6]=15&n[6]|64,n[8]=63&n[8]|128,e){r=r||0;for(let t=0;t<16;++t)e[r+t]=n[t];return e}return function(t,e=0){return(b[t[e+0]]+b[t[e+1]]+b[t[e+2]]+b[t[e+3]]+"-"+b[t[e+4]]+b[t[e+5]]+"-"+b[t[e+6]]+b[t[e+7]]+"-"+b[t[e+8]]+b[t[e+9]]+"-"+b[t[e+10]]+b[t[e+11]]+b[t[e+12]]+b[t[e+13]]+b[t[e+14]]+b[t[e+15]]).toLowerCase()}(n)},x=t=>{let e=(t=>{const e={};for(const n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[(r=n,r.replace(/([-_]\w)/g,(function(t){return t[1].toUpperCase()})))]=t[n]);var r;return e})(t.url_params);t.script_attributes&&(e=d()(e,t.script_attributes));const r=(t=>{const e={},r=t?.axo?.sdk_client_token,n=w().replace(/-/g,"");return r&&!0!==t?.user?.is_logged&&(e["data-sdk-client-token"]=r,e["data-client-metadata-id"]=n),e})(t),n=(t=>{const e=t?.save_payment_methods?.id_token;return e&&!0===t?.user?.is_logged?{"data-user-id-token":e}:{}})(t);return d().all([e,r,n])},E=new Map,S=new Map,O=async(t,e)=>{if(!t)throw new Error("Namespace is required");if(E.has(t))return console.log(`Script already loaded for namespace: ${t}`),E.get(t);if(S.has(t))return console.log(`Script loading in progress for namespace: ${t}`),S.get(t);const r={...x(e),"data-namespace":t},n=await(async(t,e)=>e.data_client_id?.set_attribute&&!0!==e.vault_v3_enabled?new Promise(((r,n)=>{f(t,e.data_client_id,(t=>{p.setPaypal(t),r(t)}),n)})):null)(r,e);if(n)return n;const o=new Promise(((e,n)=>{u(r).then((r=>{p.setPaypal(r),E.set(t,r),console.log(`Script loaded for namespace: ${t}`),e(r)})).catch((e=>{console.error(`Failed to load script for namespace: ${t}`,e),n(e)})).finally((()=>{S.delete(t)}))}));return S.set(t,o),o};r(6412),r(5700),r(8125),r(4490),r(9572),r(4731),r(479),r(2892),r(875),r(287),r(3362),r(8992),r(3949),r(3500),window.ppcpResources=window.ppcpResources||{};const j=window.ppcpResources.ButtonModuleWatcher=window.ppcpResources.ButtonModuleWatcher||new class{constructor(){this.contextBootstrapRegistry={},this.contextBootstrapWatchers=[]}watchContextBootstrap(t){this.contextBootstrapWatchers.push(t),Object.values(this.contextBootstrapRegistry).forEach(t)}registerContextBootstrap(t,e){this.contextBootstrapRegistry[t]={context:t,handler:e};for(const e of this.contextBootstrapWatchers)e(this.contextBootstrapRegistry[t])}};r(8706),r(2008),r(113),r(4423),r(2062),r(9085),r(3851),r(1278),r(9432),r(8940),r(825),r(888),r(5472),r(3772),r(4520),r(2577),r(1454),r(7550);const _=Object.freeze({INVALIDATE:"ppcp_invalidate_methods",RENDER:"ppcp_render_method",REDRAW:"ppcp_redraw_method"});function P(t,e){return{Default:{...t.style,...e.style},MiniCart:{...t.mini_cart_style,...e.mini_cart_style}}}function k(t){return Object.values(_).includes(t)}function C({event:t,paymentMethod:e="",callback:r}){if(!k(t))throw new Error(`Invalid event: ${t}`);const n=e?`${t}-${e}`:t;document.body.addEventListener(n,r)}class I{#t="";#e=!1;#r=null;constructor(...t){t.length&&(this.#t=`[${t.join(" | ")}]`)}set enabled(t){this.#e=t}log(...t){this.#e&&console.log(this.#t,...t)}error(...t){console.error(this.#t,...t)}group(t=null){this.#e&&(t&&!this.#r||(console.groupEnd(),this.#r=null),t&&(console.group(t),this.#r=t))}}class T{constructor(t,e){this.selector=t,this.selectorInContainer=e,this.containers=[],this.reloadContainers(),jQuery(window).resize((()=>{this.refresh()})).resize(),jQuery(document).on("ppcp-smart-buttons-init",(()=>{this.refresh()})),jQuery(document).on("ppcp-shown ppcp-hidden ppcp-enabled ppcp-disabled",((t,e)=>{this.refresh(),setTimeout(this.refresh.bind(this),200)})),new MutationObserver(this.observeElementsCallback.bind(this)).observe(document.body,{childList:!0,subtree:!0})}observeElementsCallback(t,e){const r=this.selector+", .widget_shopping_cart, .widget_shopping_cart_content";let n=!1;for(const e of t)"childList"===e.type&&e.addedNodes.forEach((t=>{t.matches&&t.matches(r)&&(n=!0)}));n&&(this.reloadContainers(),this.refresh())}reloadContainers(){jQuery(this.selector).each(((t,e)=>{const r=jQuery(e).parent();this.containers.some((t=>t.is(r)))||this.containers.push(r)}))}refresh(){for(const t of this.containers){const e=jQuery(t),r=e.width();e.removeClass("ppcp-width-500 ppcp-width-300 ppcp-width-min"),r>=500?e.addClass("ppcp-width-500"):r>=300?e.addClass("ppcp-width-300"):e.addClass("ppcp-width-min");const n=e.children(":visible").first();e.find(this.selectorInContainer).each(((t,e)=>{const r=jQuery(e);if(r.is(n))return r.css("margin-top","0px"),!0;const o=r.height(),i=Math.max(11,Math.round(.3*o));r.css("margin-top",`${i}px`)}))}}}const A="ppcp-gateway",L={Cart:"cart",Checkout:"checkout",BlockCart:"cart-block",BlockCheckout:"checkout-block",Product:"product",MiniCart:"mini-cart",PayNow:"pay-now",Preview:"preview",Blocks:["cart-block","checkout-block"],Gateways:["checkout","pay-now"]},R=()=>{const t=document.querySelector('input[name="payment_method"]:checked');return t?t.value:null},N=t=>"string"==typeof t?document.querySelector(t):t;class M{static methodId="generic";static cssClass="";#n;#o=!1;#i=!1;#a;#c;#u;#s=[];#l;#f;#p;#h;#d;#y=null;#v=!0;#g=!0;#m=null;#b=[];static createButton(t,e,r,n,o,i){const a=(()=>{const t="__ppcpPBInstances";return document.body[t]||Object.defineProperty(document.body,t,{value:new Map,enumerable:!1,writable:!1,configurable:!1}),document.body[t]})(),c=`${this.methodId}.${t}`;if(!a.has(c)){const u=new this(t,e,r,n,o,i);a.set(c,u)}return a.get(c)}static getWrappers(t,e){throw new Error("Must be implemented in the child class")}static getStyles(t,e){throw new Error("Must be implemented in the child class")}constructor(t,e=null,r={},n={},o=null,i={}){if(this.methodId===M.methodId)throw new Error("Cannot initialize the PaymentButton base class");r||(r={});const a=!!r?.is_debug,c=this.methodId.replace(/^ppcp?-/,"");this.#a=t,this.#l=r,this.#f=n,this.#p=e,this.#h=o,this.#d=i,this.#n=new I(c,t),a&&(this.#n.enabled=!0,((t,e)=>{window.ppcpPaymentButtonList=window.ppcpPaymentButtonList||{};const r=window.ppcpPaymentButtonList;r[t]=r[t]||[],r[t].push(e)})(c,this)),this.#c=this.constructor.getWrappers(this.#l,this.#f),this.applyButtonStyles(this.#l),this.registerValidationRules(this.#w.bind(this),this.#x.bind(this)),((t,e=".ppcp-button-apm")=>{let r=e;if(!window.ppcpApmButtons){if(t&&t.button){const n=t.button.wrapper;jQuery(n).children('div[class^="item-"]').length>0&&(e+=`, ${n} div[class^="item-"]`,r+=', div[class^="item-"]')}window.ppcpApmButtons=new T(e,r)}})(this.#f),this.initEventListeners()}get methodId(){return this.constructor.methodId}get cssClass(){return this.constructor.cssClass}get isInitialized(){return this.#o}get context(){return this.#a}get buttonConfig(){return this.#l}get ppcpConfig(){return this.#f}get externalHandler(){return this.#p||{}}get contextHandler(){return this.#h||{}}get requiresShipping(){return"function"==typeof this.contextHandler.shippingAllowed&&this.contextHandler.shippingAllowed()}get wrappers(){return this.#c}get style(){return L.MiniCart===this.context?this.#u.MiniCart:this.#u.Default}get wrapperId(){return L.MiniCart===this.context?this.wrappers.MiniCart:this.isSeparateGateway?this.wrappers.Gateway:L.Blocks.includes(this.context)?this.wrappers.Block:this.wrappers.Default}get isInsideClassicGateway(){return L.Gateways.includes(this.context)}get isSeparateGateway(){return this.#l.is_wc_gateway_enabled&&this.isInsideClassicGateway}get isCurrentGateway(){if(!this.isInsideClassicGateway)return!0;const t=R();return this.isSeparateGateway?this.methodId===t:A===t}get isPreview(){return L.Preview===this.context}get isEligible(){return this.#y}set isEligible(t){t!==this.#y&&(this.#y=t,this.triggerRedraw())}get isVisible(){return this.#v}set isVisible(t){this.#v!==t&&(this.#v=t,this.triggerRedraw())}get isEnabled(){return this.#g}set isEnabled(t){this.#g!==t&&(this.#g=t,this.triggerRedraw())}get wrapperElement(){return document.getElementById(this.wrapperId)}get ppcpButtonWrapperSelector(){return L.Blocks.includes(this.context)?null:this.context===L.MiniCart?this.ppcpConfig?.button?.mini_cart_wrapper:this.ppcpConfig?.button?.wrapper}get isPresent(){return this.wrapperElement instanceof HTMLElement}get isButtonAttached(){if(!this.#m)return!1;let t=this.#m.parentElement;for(;t?.parentElement;){if("BODY"===t.tagName)return!0;t=t.parentElement}return!1}log(...t){this.#n.log(...t)}error(...t){this.#n.error(...t)}logGroup(t=null){this.#n.group(t)}#w(t,e){this.#b.push({check:t,errorMessage:e,shouldPass:!1})}#x(t){this.#b.push({check:t,shouldPass:!0})}registerValidationRules(t,e){}validateConfiguration(t=!1){for(const e of this.#b){const r=e.check();if(e.shouldPass&&r)return!0;if(!e.shouldPass&&r)return!t&&e.errorMessage&&this.error(e.errorMessage),!1}return!0}applyButtonStyles(t,e=null){e||(e=this.ppcpConfig),this.#u=this.constructor.getStyles(t,e),this.isInitialized&&this.triggerRedraw()}configure(){}init(){this.#o=!0}reinit(){this.#o=!1,this.#y=!1}triggerRedraw(){this.showPaymentGateway(),function({event:t,paymentMethod:e=""}){if(!k(t))throw new Error(`Invalid event: ${t}`);const r=e?`${t}-${e}`:t;document.body.dispatchEvent(new Event(r))}({event:_.REDRAW,paymentMethod:this.methodId})}syncProductButtonsState(){const t=document.querySelector(this.ppcpButtonWrapperSelector);var e;t&&(this.isVisible=!!((e=t).offsetWidth||e.offsetHeight||e.getClientRects().length),this.isEnabled=!(t=>{const e=N(t);return!!e&&jQuery(e).hasClass("ppcp-disabled")})(t))}initEventListeners(){if(C({event:_.REDRAW,paymentMethod:this.methodId,callback:()=>this.refresh()}),this.isInsideClassicGateway){const t=this.isSeparateGateway?this.methodId:A;C({event:_.INVALIDATE,callback:()=>this.isVisible=!1}),C({event:_.RENDER,paymentMethod:t,callback:()=>this.isVisible=!0})}this.context===L.Product&&jQuery(document).on("ppcp-shown ppcp-hidden ppcp-enabled ppcp-disabled",((t,e)=>{jQuery(e.selector).is(this.ppcpButtonWrapperSelector)&&this.syncProductButtonsState()}))}refresh(){this.isPresent&&(this.isEligible?(this.applyWrapperStyles(),this.isEligible&&this.isCurrentGateway&&this.isVisible&&(this.isButtonAttached||(this.log("refresh.addButton"),this.addButton()))):this.wrapperElement.style.display="none")}showPaymentGateway(){if(this.#i||!this.isSeparateGateway||!this.isEligible)return;const t=`style[data-hide-gateway="${this.methodId}"]`,e=`#${this.wrappers.Default}`,r=document.querySelector(`.wc_payment_method.payment_method_${this.methodId}`);document.querySelectorAll(t).forEach((t=>t.remove())),"none"!==r.style.display&&""!==r.style.display||(r.style.display="block"),document.querySelectorAll(e).forEach((t=>t.remove())),this.log("Show gateway"),this.#i=!0,this.isVisible=this.isCurrentGateway}applyWrapperStyles(){const t=this.wrapperElement,{shape:e,height:r}=this.style;for(const e of this.#s)t.classList.remove(e);this.#s=[];const n=[`ppcp-button-${e}`,"ppcp-button-apm",this.cssClass];t.classList.add(...n),this.#s.push(...n),r&&(t.style.height=`${r}px`),t.style.display=this.isVisible?"block":"none";const o=this.context===L.Product?"form.cart":null;((t,e,r=null)=>{const n=N(t);n&&(e?(jQuery(n).removeClass("ppcp-disabled").off("mouseup").find("> *").css("pointer-events",""),((t,e)=>{jQuery(document).trigger("ppcp-enabled",{handler:"ButtonsDisabler.setEnabled",action:"enable",selector:t,element:e})})(t,n)):(jQuery(n).addClass("ppcp-disabled").on("mouseup",(function(t){if(t.stopImmediatePropagation(),r){const t=jQuery(r);t.find(".single_add_to_cart_button").hasClass("disabled")&&t.find(":submit").trigger("click")}})).find("> *").css("pointer-events","none"),((t,e)=>{jQuery(document).trigger("ppcp-disabled",{handler:"ButtonsDisabler.setEnabled",action:"disable",selector:t,element:e})})(t,n)))})(t,this.isEnabled,o)}addButton(){throw new Error("Must be implemented by the child class")}insertButton(t){if(!this.isPresent)return;const e=this.wrapperElement;this.#m&&this.removeButton(),this.log("insertButton",t),this.#m=t,e.appendChild(this.#m)}removeButton(){if(this.isPresent&&this.#m){this.log("removeButton");try{this.wrapperElement.removeChild(this.#m)}catch(t){}this.#m=null}}}function D(t){return D="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},D(t)}function F(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,B(n.key),n)}}function B(t){var e=function(t){if("object"!=D(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,"string");if("object"!=D(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==D(e)?e:e+""}r(739),r(3110);const G=function(){return t=function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.config=e},(e=[{key:"update",value:function(t){var e=this;return new Promise((function(r,n){fetch(e.config.endpoint,{method:"POST",headers:{"Content-Type":"application/json"},credentials:"same-origin",body:JSON.stringify({nonce:e.config.nonce,paymentData:t})}).then((function(t){return t.json()})).then((function(t){t.success&&r(t.data)}))}))}}])&&F(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e}(),H={"#billing_email":["email_address"],"#billing_last_name":["name","surname"],"#billing_first_name":["name","given_name"],"#billing_country":["address","country_code"],"#billing_address_1":["address","address_line_1"],"#billing_address_2":["address","address_line_2"],"#billing_state":["address","admin_area_1"],"#billing_city":["address","admin_area_2"],"#billing_postcode":["address","postal_code"],"#billing_phone":["phone"]};function q(t){return{email_address:t.email_address,phone:t.phone,name:{surname:t.name?.surname,given_name:t.name?.given_name},address:{country_code:t.address?.country_code,address_line_1:t.address?.address_line_1,address_line_2:t.address?.address_line_2,admin_area_1:t.address?.admin_area_1,admin_area_2:t.address?.admin_area_2,postal_code:t.address?.postal_code}}}function U(){const t=window?.PayPalCommerceGateway?.payer??window._PpcpPayerSessionDetails;if(!t)return null;const e=function(){const t={};return Object.entries(H).forEach((([e,r])=>{const n=(t=>document.querySelector(t)?.value)(e);n&&((t,e,r)=>{let n=t;for(let t=0;t<e.length-1;t++)n=n[e[t]]=n[e[t]]||{};n[e[e.length-1]]=r})(t,r,n)})),t.phone&&"string"==typeof t.phone&&(t.phone={phone_type:"HOME",phone_number:{national_number:t.phone}}),t}();return e?function(t,e){const r=(t,e)=>{for(const[n,o]of Object.entries(e))null!=o&&(t[n]="object"==typeof o?r(t[n]||{},o):o);return t};return r(q(t),q(e))}(t,e):q(t)}function W(t,e=!1){var r,n;(r=t)&&"object"==typeof r&&(window._PpcpPayerSessionDetails=q(r)),e&&(n=t,Object.entries(H).forEach((([t,e])=>{const r=((t,e)=>e.reduce(((t,e)=>t?.[e]),t))(n,e);((t,e,r)=>{null!=r&&e&&("phone"===t[0]&&"object"==typeof r&&(r=r.phone_number?.national_number),e.value=r)})(e,document.querySelector(t),r)})))}function $(t){return t.toLowerCase().trim().replace(/[^a-z0-9_-]/g,"_")}function z(t){try{const e=JSON.parse(t);return{data:e.data,expires:e.expires||0}}catch(t){return null}}function V(t){return t?Date.now()+1e3*t:0}class Q{#E="";#S=null;constructor(t){this.#E=$(t)+":",this.#O()}#O(){this.canUseLocalStorage&&Object.keys(localStorage).forEach((t=>{if(!t.startsWith(this.#E))return;const e=z(localStorage.getItem(t));e&&e.expires>0&&e.expires<Date.now()&&localStorage.removeItem(t)}))}#j(t){const e=$(t);if(0===e.length)throw new Error("Name cannot be empty after sanitization");return`${this.#E}${e}`}get canUseLocalStorage(){return null===this.#S&&(this.#S=function(){try{const t="__ppcp_test__";return localStorage.setItem(t,"test"),localStorage.removeItem(t),!0}catch(t){return!1}}()),this.#S}set(t,e,r=0){if(!this.canUseLocalStorage)throw new Error("Local storage is not available");const n=function(t,e){const r={data:t,expires:V(e)};return JSON.stringify(r)}(e,r),o=this.#j(t);localStorage.setItem(o,n)}get(t){if(!this.canUseLocalStorage)throw new Error("Local storage is not available");const e=this.#j(t),r=z(localStorage.getItem(e));return r?r.data:null}clear(t){if(!this.canUseLocalStorage)throw new Error("Local storage is not available");const e=this.#j(t);localStorage.removeItem(e)}}function Y(t){return Y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Y(t)}function J(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,et(n.key),n)}}function K(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(K=function(){return!!t})()}function X(t){return X=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},X(t)}function Z(t,e){return Z=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Z(t,e)}function tt(t,e,r){return(e=et(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function et(t){var e=function(t){if("object"!=Y(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,"string");if("object"!=Y(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==Y(e)?e:e+""}var rt=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),function(t,e,r){return e=X(e),function(t,e){if(e&&("object"==Y(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,K()?Reflect.construct(e,r||[],X(t).constructor):e.apply(t,r))}(this,e,["ppcp-googlepay"])}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Z(t,e)}(e,t),r=e,(n=[{key:"getPayer",value:function(){return this.get(e.PAYER)}},{key:"setPayer",value:function(t){this.set(e.PAYER,t,e.PAYER_TTL)}},{key:"clearPayer",value:function(){this.clear(e.PAYER)}}])&&J(r.prototype,n),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,n}(Q);tt(rt,"PAYER","payer"),tt(rt,"PAYER_TTL",900);const nt=new rt;function ot(t){return ot="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ot(t)}function it(){it=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,r){return t[e]=r}}function l(t,e,r,n){var i=e&&e.prototype instanceof g?e:g,a=Object.create(i.prototype),c=new I(n||[]);return o(a,"_invoke",{value:_(t,r,c)}),a}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=l;var p="suspendedStart",h="suspendedYield",d="executing",y="completed",v={};function g(){}function m(){}function b(){}var w={};s(w,a,(function(){return this}));var x=Object.getPrototypeOf,E=x&&x(x(T([])));E&&E!==r&&n.call(E,a)&&(w=E);var S=b.prototype=g.prototype=Object.create(w);function O(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function j(t,e){function r(o,i,a,c){var u=f(t[o],t,i);if("throw"!==u.type){var s=u.arg,l=s.value;return l&&"object"==ot(l)&&n.call(l,"__await")?e.resolve(l.__await).then((function(t){r("next",t,a,c)}),(function(t){r("throw",t,a,c)})):e.resolve(l).then((function(t){s.value=t,a(s)}),(function(t){return r("throw",t,a,c)}))}c(u.arg)}var i;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return i=i?i.then(o,o):o()}})}function _(e,r,n){var o=p;return function(i,a){if(o===d)throw Error("Generator is already running");if(o===y){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var c=n.delegate;if(c){var u=P(c,n);if(u){if(u===v)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===p)throw o=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=d;var s=f(e,r,n);if("normal"===s.type){if(o=n.done?y:h,s.arg===v)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o=y,n.method="throw",n.arg=s.arg)}}}function P(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,P(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=f(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,v;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function k(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function C(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function I(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(k,this),this.reset(!0)}function T(e){if(e||""===e){var r=e[a];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(ot(e)+" is not iterable")}return m.prototype=b,o(S,"constructor",{value:b,configurable:!0}),o(b,"constructor",{value:m,configurable:!0}),m.displayName=s(b,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===m||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,s(t,u,"GeneratorFunction")),t.prototype=Object.create(S),t},e.awrap=function(t){return{__await:t}},O(j.prototype),s(j.prototype,c,(function(){return this})),e.AsyncIterator=j,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new j(l(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},O(S),s(S,u,"Generator"),s(S,a,(function(){return this})),s(S,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=T,I.prototype={constructor:I,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(C),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return c.type="throw",c.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],c=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var u=n.call(a,"catchLoc"),s=n.call(a,"finallyLoc");if(u&&s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),C(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;C(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:T(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),v}},e}function at(t,e,r,n,o,i,a){try{var c=t[i](a),u=c.value}catch(t){return void r(t)}c.done?e(u):Promise.resolve(u).then(n,o)}function ct(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){at(i,n,o,a,c,"next",t)}function c(t){at(i,n,o,a,c,"throw",t)}a(void 0)}))}}function ut(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function st(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ut(Object(r),!0).forEach((function(e){gt(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ut(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function lt(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,mt(n.key),n)}}function ft(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(ft=function(){return!!t})()}function pt(t,e,r,n){var o=ht(dt(1&n?t.prototype:t),e,r);return 2&n&&"function"==typeof o?function(t){return o.apply(r,t)}:o}function ht(){return ht="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,r){var n=function(t,e){for(;!{}.hasOwnProperty.call(t,e)&&null!==(t=dt(t)););return t}(t,e);if(n){var o=Object.getOwnPropertyDescriptor(n,e);return o.get?o.get.call(arguments.length<3?t:r):o.value}},ht.apply(null,arguments)}function dt(t){return dt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},dt(t)}function yt(t,e){return yt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},yt(t,e)}function vt(t,e,r){(function(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")})(t,e),e.set(t,r)}function gt(t,e,r){return(e=mt(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function mt(t){var e=function(t){if("object"!=ot(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,"string");if("object"!=ot(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==ot(e)?e:e+""}function bt(t,e,r){return t.set(xt(t,e),r),r}function wt(t,e){return t.get(xt(t,e))}function xt(t,e,r){if("function"==typeof t?t===e:t.has(e))return arguments.length<3?e:r;throw new TypeError("Private element is not present on this object")}var Et="failed",St="payerAction";function Ot(t){var e,r=null==t||null===(e=t.paymentMethodData)||void 0===e||null===(e=e.info)||void 0===e?void 0:e.billingAddress;return{email_address:null==t?void 0:t.email,name:{given_name:r.name.split(" ")[0],surname:r.name.split(" ").slice(1).join(" ")},address:{country_code:r.countryCode,address_line_1:r.address1,address_line_2:r.address2,admin_area_1:r.administrativeArea,admin_area_2:r.locality,postal_code:r.postalCode}}}var jt=new WeakMap,_t=new WeakMap,Pt=new WeakMap,kt=new WeakMap,Ct=new WeakMap,It=new WeakMap,Tt=function(t){function e(t,r,n,o,i,a){var c;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),vt(c=function(t,e,r){return e=dt(e),function(t,e){if(e&&("object"==ot(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,ft()?Reflect.construct(e,r||[],dt(t).constructor):e.apply(t,r))}(this,e,[t,r,n,o,i,a]),jt,null),vt(c,_t,null),vt(c,Pt,null),gt(c,"googlePayConfig",null),vt(c,kt,0),vt(c,Ct,1e3),vt(c,It,null),c.init=c.init.bind(c),c.onPaymentDataChanged=c.onPaymentDataChanged.bind(c),c.onButtonClick=c.onButtonClick.bind(c),c.log("Create instance"),c}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&yt(t,e)}(e,t),r=e,n=[{key:"requiresShipping",get:function(){var t;return pt(e,"requiresShipping",this,1)&&(null===(t=this.buttonConfig.shipping)||void 0===t?void 0:t.enabled)}},{key:"googlePayApi",get:function(){var t;return null===(t=window.google)||void 0===t||null===(t=t.payments)||void 0===t?void 0:t.api}},{key:"paymentsClient",get:function(){return wt(jt,this)}},{key:"transactionInfo",get:function(){return wt(_t,this)},set:function(t){bt(_t,this,t),this.refresh()}},{key:"registerValidationRules",value:function(t,e){var r=this;return t((function(){return!["TEST","PRODUCTION"].includes(r.buttonConfig.environment)}),"Invalid environment: ".concat(this.buttonConfig.environment)),e((function(){return r.isPreview})),t((function(){return!r.googlePayConfig}),"No API configuration - missing configure() call?"),t((function(){return!r.transactionInfo}),"No transactionInfo - missing configure() call?"),t((function(){var t;return!(null!==(t=r.contextHandler)&&void 0!==t&&t.validateContext())}),"Invalid context handler."),t((function(){var t;return(null===(t=r.buttonAttributes)||void 0===t?void 0:t.height)&&isNaN(parseInt(r.buttonAttributes.height))}),"Invalid height in buttonAttributes"),t((function(){var t;return(null===(t=r.buttonAttributes)||void 0===t?void 0:t.borderRadius)&&isNaN(parseInt(r.buttonAttributes.borderRadius))}),"Invalid borderRadius in buttonAttributes"),!0}},{key:"configure",value:function(t,e){var r=this,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};wt(kt,this)||bt(kt,this,Date.now()),null!=n&&n.height&&null!=n&&n.borderRadius&&bt(It,this,st({},n));var o=null!=n&&n.height?n:wt(It,this);if(Date.now()-wt(kt,this)>wt(Ct,this))return this.log("GooglePay: Timeout waiting for buttonAttributes - proceeding with initialization"),this.googlePayConfig=t,bt(_t,this,e),this.buttonAttributes=o||n,this.allowedPaymentMethods=this.googlePayConfig.allowedPaymentMethods,this.baseCardPaymentMethod=this.allowedPaymentMethods[0],void this.init();null!=o&&o.height&&null!=o&&o.borderRadius?(bt(kt,this,0),this.googlePayConfig=t,bt(_t,this,e),this.buttonAttributes=o,this.allowedPaymentMethods=this.googlePayConfig.allowedPaymentMethods,this.baseCardPaymentMethod=this.allowedPaymentMethods[0],this.init()):setTimeout((function(){return r.configure(t,e,n)}),100)}},{key:"init",value:function(){var t=this;this.isInitialized||this.validateConfiguration()&&(pt(e,"init",this,3)([]),bt(jt,this,this.createPaymentsClient()),this.paymentsClient.isReadyToPay(this.buildReadyToPayRequest(this.allowedPaymentMethods,this.googlePayConfig)).then((function(e){t.log("PaymentsClient.isReadyToPay response:",e),t.isEligible=!!e.result})).catch((function(e){t.error(e),t.isEligible=!1})))}},{key:"reinit",value:function(){this.validateConfiguration(!0)&&(pt(e,"reinit",this,3)([]),this.init())}},{key:"preparePaymentDataCallbacks",value:function(){var t={};return this.isPreview||this.requiresShipping&&(t.onPaymentDataChanged=this.onPaymentDataChanged),t}},{key:"createPaymentsClient",value:function(){if(!this.googlePayApi)return null;var t=this.preparePaymentDataCallbacks();return new this.googlePayApi.PaymentsClient({environment:this.buttonConfig.environment,paymentDataCallbacks:t})}},{key:"buildReadyToPayRequest",value:function(t,e){return this.log("Ready To Pay request",e,t),Object.assign({},e,{allowedPaymentMethods:t})}},{key:"addButton",value:function(){var t,r,n;if(this.paymentsClient){null!==(t=this.buttonAttributes)&&void 0!==t&&t.height||null===(r=wt(It,this))||void 0===r||!r.height||(this.buttonAttributes=st({},wt(It,this))),this.removeButton();var o=this.baseCardPaymentMethod,i=this.style,a=i.color,c=i.type,u={buttonColor:a||"black",buttonSizeMode:"fill",buttonLocale:i.language||"en",buttonType:c||"pay",buttonRadius:parseInt(null===(n=this.buttonAttributes)||void 0===n?void 0:n.borderRadius,10),onClick:this.onButtonClick,allowedPaymentMethods:[o]},s=this.paymentsClient.createButton(u);bt(Pt,this,s),pt(e,"insertButton",this,3)([s]),this.applyWrapperStyles()}}},{key:"applyWrapperStyles",value:function(){var t;pt(e,"applyWrapperStyles",this,3)([]);var r=this.wrapperElement;if(r){var n=null!==(t=this.buttonAttributes)&&void 0!==t&&t.height?this.buttonAttributes:wt(It,this);if(null!=n&&n.height){var o=parseInt(n.height,10);isNaN(o)||(r.style.height="".concat(o,"px"),r.style.minHeight="".concat(o,"px"))}}}},{key:"removeButton",value:function(){if(this.isPresent&&wt(Pt,this)){this.log("removeButton");try{this.wrapperElement.removeChild(wt(Pt,this))}catch(t){}bt(Pt,this,null)}}},{key:"onButtonClick",value:(a=ct(it().mark((function t(){var e,r,n,o,i=this;return it().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return this.logGroup("onButtonClick"),e=function(){var t=ct(it().mark((function t(){var e;return it().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return window.ppcpFundingSource="googlepay",e=i.paymentDataRequest(),i.log("onButtonClick: paymentDataRequest",e,i.context),t.abrupt("return",i.paymentsClient.loadPaymentData(e).then((function(t){return i.log("loadPaymentData response:",t),t})).catch((function(t){throw i.error("loadPaymentData failed:",t),t})));case 4:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}(),r=function(){var t=ct(it().mark((function t(){return it().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if("function"==typeof i.contextHandler.validateForm){t.next=2;break}return t.abrupt("return",Promise.resolve());case 2:return t.abrupt("return",i.contextHandler.validateForm().catch((function(t){throw i.error("Form validation failed:",t),t})));case 3:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}(),n=function(){var t=ct(it().mark((function t(){return it().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if("function"==typeof i.contextHandler.transactionInfo){t.next=2;break}return t.abrupt("return",Promise.resolve());case 2:return t.abrupt("return",i.contextHandler.transactionInfo().then((function(t){i.transactionInfo=t})).catch((function(t){throw i.error("Failed to get transaction info:",t),t})));case 3:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}(),t.next=6,r().then(n).then(e);case 6:if(o=t.sent,this.logGroup(),o){t.next=10;break}return t.abrupt("return");case 10:return t.abrupt("return",this.processPayment(o));case 11:case"end":return t.stop()}}),t,this)}))),function(){return a.apply(this,arguments)})},{key:"paymentDataRequest",value:function(){var t=this.requiresShipping,e=[];return t&&e.push("SHIPPING_ADDRESS","SHIPPING_OPTION"),st(st({},{apiVersion:2,apiVersionMinor:0}),{},{allowedPaymentMethods:this.googlePayConfig.allowedPaymentMethods,transactionInfo:this.transactionInfo.finalObject,merchantInfo:this.googlePayConfig.merchantInfo,callbackIntents:e,emailRequired:!0,shippingAddressRequired:t,shippingOptionRequired:t,shippingAddressParameters:this.shippingAddressParameters()})}},{key:"shippingAddressParameters",value:function(){return{allowedCountryCodes:this.buttonConfig.shipping.countries,phoneNumberRequired:!0}}},{key:"onPaymentDataChanged",value:function(t){var e=this;return this.log("onPaymentDataChanged",t),new Promise(function(){var r=ct(it().mark((function r(n,o){var i,a,c,u,s,l,f;return it().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,a={},r.next=4,new G(e.buttonConfig.ajax.update_payment_data).update(t);case 4:if(c=r.sent,u=e.transactionInfo,s=["checkout-block","checkout","cart-block","cart","mini-cart","pay-now"].includes(e.context),e.log("onPaymentDataChanged:updatedData",c),e.log("onPaymentDataChanged:transactionInfo",u),c.country_code=u.countryCode,c.currency_code=u.currencyCode,null!==(i=c.shipping_options)&&void 0!==i&&null!==(i=i.shippingOptions)&&void 0!==i&&i.length){r.next=15;break}return a.error=e.unserviceableShippingAddressError(),n(a),r.abrupt("return");case 15:["INITIALIZE","SHIPPING_ADDRESS"].includes(t.callbackTrigger)&&(a.newShippingOptionParameters=e.sanitizeShippingOptions(c.shipping_options)),c.total&&s?(u.setTotal(c.total,c.shipping_fee),e.syncShippingOptionWithForm(null==t||null===(l=t.shippingOptionData)||void 0===l?void 0:l.id)):u.shippingFee=e.getShippingCosts(null==t||null===(f=t.shippingOptionData)||void 0===f?void 0:f.id,c.shipping_options),a.newTransactionInfo=e.calculateNewTransactionInfo(u),n(a),r.next=25;break;case 21:r.prev=21,r.t0=r.catch(0),e.error("Error during onPaymentDataChanged:",r.t0),o(r.t0);case 25:case"end":return r.stop()}}),r,null,[[0,21]])})));return function(t,e){return r.apply(this,arguments)}}())}},{key:"sanitizeShippingOptions",value:function(t){var e=t.shippingOptions.map((function(t){return{id:t.id,label:t.label,description:t.description}})),r=t.defaultSelectedOptionId;return e.some((function(t){return t.id===r}))||(r=e[0].id),{defaultSelectedOptionId:r,shippingOptions:e}}},{key:"getShippingCosts",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.shippingOptions,n=void 0===r?[]:r,o=e.defaultSelectedOptionId,i=void 0===o?"":o;if(null==n||!n.length)return this.log("Cannot calculate shipping cost: No Shipping Options"),0;var a=function(t){return n.find((function(e){return e.id===t}))},c=a("shipping_option_unselected"!==t&&a(t)?t:i);return Number(null==c?void 0:c.cost)||0}},{key:"unserviceableShippingAddressError",value:function(){return{reason:"SHIPPING_ADDRESS_UNSERVICEABLE",message:"Cannot ship to the selected address",intent:"SHIPPING_ADDRESS"}}},{key:"calculateNewTransactionInfo",value:function(t){return t.finalObject}},{key:"processPayment",value:(i=ct(it().mark((function t(e){var r,n,o,i,a,c,u,s,l,f,h,d=this;return it().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return this.logGroup("processPayment"),n=Ot(e),o=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,n={transactionState:t};return(e||r)&&(n.error={intent:e,message:r}),d.log("processPaymentResponse",n),n},i=function(t){return d.error(t),o("ERROR","PAYMENT_AUTHORIZATION",t)},a=function(){var t=ct(it().mark((function t(r){var n,o;return it().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n={orderId:r,paymentMethodData:e.paymentMethodData},t.next=3,p.paypal.Googlepay().confirmOrder(n);case 3:o=t.sent,d.log("confirmOrder",o),t.t0=null==o?void 0:o.status,t.next="APPROVED"===t.t0?8:"PAYER_ACTION_REQUIRED"===t.t0?9:10;break;case 8:return t.abrupt("return","approved");case 9:return t.abrupt("return",St);case 10:return t.abrupt("return",Et);case 11:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),c=function(t){return d.log("initiatePayerAction",t),p.paypal.Googlepay().initiatePayerAction({orderId:t})},u=function(){var t=ct(it().mark((function t(e){var r;return it().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=!0,d.log("approveOrder",e),t.next=4,d.contextHandler.approveOrder({orderID:e,payer:n},{restart:function(){return new Promise((function(t){r=!1,t()}))},order:{get:function(){return new Promise((function(t){t(null)}))}}});case 4:return t.abrupt("return",r);case 5:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),nt.setPayer(n),W(n),t.prev=9,t.next=12,this.contextHandler.createOrder();case 12:return s=t.sent,this.log("createOrder",s),t.next=16,a(s);case 16:if(l=t.sent,Et!==l){t.next=21;break}r=i("TRANSACTION FAILED"),t.next=30;break;case 21:if(St!==l){t.next=26;break}return t.next=24,c(s);case 24:f=t.sent,this.log("3DS verification completed",f);case 26:return t.next=28,u(s);case 28:h=t.sent,r=h?o("SUCCESS"):i("FAILED TO APPROVE");case 30:t.next=35;break;case 32:t.prev=32,t.t0=t.catch(9),r=i(t.t0.message);case 35:return this.logGroup(),t.abrupt("return",r);case 37:case"end":return t.stop()}}),t,this,[[9,32]])}))),function(t){return i.apply(this,arguments)})},{key:"syncShippingOptionWithForm",value:function(t){for(var e=[".woocommerce-shipping-methods",".wc-block-components-shipping-rates-control",".wc-block-components-totals-shipping"],r=t.replace(/"/g,""),n=0,o=e;n<o.length;n++){var i="".concat(o[n],' input[type="radio"][value="').concat(r,'"]'),a=document.querySelector(i);if(a)return a.click(),!0}for(var c=0,u=e;c<u.length;c++){var s="".concat(u[c],' select option[value="').concat(r,'"]'),l=document.querySelector(s);if(l){var f=l.closest("select");if(f)return f.value=r,f.dispatchEvent(new Event("change")),!0}}return!1}}],o=[{key:"getWrappers",value:function(t,e){var r,n,o;return function(t="",e="",r="",n="",o=""){const i=t=>t.replace(/^#/,"");return{Default:i(t),SmartButton:i(r),Block:i(n),Gateway:i(o),MiniCart:i(e)}}((null==t||null===(r=t.button)||void 0===r?void 0:r.wrapper)||"",(null==t||null===(n=t.button)||void 0===n?void 0:n.mini_cart_wrapper)||"",(null==e||null===(o=e.button)||void 0===o?void 0:o.wrapper)||"","ppc-button-googlepay-container","ppc-button-ppcp-googlepay")}},{key:"getStyles",value:function(t,e){var r=P((null==e?void 0:e.button)||{},(null==t?void 0:t.button)||{});return"buy"===r.MiniCart.type&&(r.MiniCart.type="pay"),r}}],n&&lt(r.prototype,n),o&&lt(r,o),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,n,o,i,a}(M);gt(Tt,"methodId","ppcp-googlepay"),gt(Tt,"cssClass","google-pay");const At=Tt,Lt=class{constructor(t,e,r,n){this.id=t,this.quantity=e,this.variations=r,this.extra=n}data(){return{id:this.id,quantity:this.quantity,variations:this.variations,extra:this.extra}}},Rt=class extends Lt{constructor(t,e,r,n){super(t,e,null,n),this.booking=r}data(){return{...super.data(),booking:this.booking}}},Nt=(t,e)=>(r,n)=>{const o=!t.config.vaultingEnabled||"venmo"!==r.paymentSource,i={nonce:t.config.ajax.approve_order.nonce,order_id:r.orderID,funding_source:window.ppcpFundingSource,should_create_wc_order:o};return o&&r.payer&&(i.payer=r.payer),fetch(t.config.ajax.approve_order.endpoint,{method:"POST",headers:{"Content-Type":"application/json"},credentials:"same-origin",body:JSON.stringify(i)}).then((t=>t.json())).then((r=>{if(!r.success)return e.genericError(),n.restart().catch((()=>{e.genericError()}));const o=r.data?.order_received_url;var i;i=o||t.config.redirect,setTimeout((()=>{window.location.href=i}),200)}))},Mt=class{constructor(t=[]){this.cartItemKeys=t}getEndpoint(){let t="/?wc-ajax=%%endpoint%%";return"undefined"!=typeof wc_cart_fragments_params&&wc_cart_fragments_params.wc_ajax_url&&(t=wc_cart_fragments_params.wc_ajax_url),t.toString().replace("%%endpoint%%","remove_from_cart")}addFromPurchaseUnits(t){for(const e of t||[])for(const t of e.items||[])t.cart_item_key&&this.cartItemKeys.push(t.cart_item_key);return this}removeFromCart(){return new Promise(((t,e)=>{if(!this.cartItemKeys||!this.cartItemKeys.length)return void t();const r=this.cartItemKeys.length;let n=0;const o=()=>{n++,n>=r&&t()};for(const t of this.cartItemKeys){const e=new URLSearchParams;e.append("cart_item_key",t),t?fetch(this.getEndpoint(),{method:"POST",credentials:"same-origin",body:e}).then((function(t){return t.json()})).then((()=>{o()})).catch((()=>{o()})):o()}}))}};class Dt{static getPrefixedFields(t,e){const r=new FormData(t),n={};for(const[t,o]of r.entries())e&&!t.startsWith(e)||(n[t]=o);return n}static getFilteredFields(t,e,r){const n=new FormData(t),o={},i={};for(let[t,a]of n.entries()){if(-1!==t.indexOf("[]")){const e=t;i[e]=i[e]||0,t=t.replace("[]",`[${i[e]}]`),i[e]++}t&&(e&&-1!==e.indexOf(t)||r&&r.some((e=>t.startsWith(e)))||(o[t]=a))}return o}}const Ft=class{constructor(t,e,r,n){this.config=t,this.updateCart=e,this.formElement=r,this.errorHandler=n,this.cartHelper=null}subscriptionsConfiguration(t){return{createSubscription:(e,r)=>r.subscription.create({plan_id:t}),onApprove:(t,e)=>{fetch(this.config.ajax.approve_subscription.endpoint,{method:"POST",credentials:"same-origin",body:JSON.stringify({nonce:this.config.ajax.approve_subscription.nonce,order_id:t.orderID,subscription_id:t.subscriptionID})}).then((t=>t.json())).then((()=>{const t=this.getSubscriptionProducts();fetch(this.config.ajax.change_cart.endpoint,{method:"POST",headers:{"Content-Type":"application/json"},credentials:"same-origin",body:JSON.stringify({nonce:this.config.ajax.change_cart.nonce,products:t})}).then((t=>t.json())).then((t=>{if(!t.success)throw console.log(t),Error(t.data.message);location.href=this.config.redirect}))}))},onError:t=>{console.error(t)}}}getSubscriptionProducts(){const t=document.querySelector('[name="add-to-cart"]').value;return[new Lt(t,1,this.variations(),this.extraFields())]}configuration(){return{createOrder:this.createOrder(),onApprove:Nt(this,this.errorHandler),onError:t=>{if(this.refreshMiniCart(),this.isBookingProduct()&&t.message)return this.errorHandler.clear(),void this.errorHandler.message(t.message);this.errorHandler.genericError()},onCancel:()=>{this.isBookingProduct()?this.cleanCart():this.refreshMiniCart()}}}getProducts(){if(this.isBookingProduct()){const t=document.querySelector('[name="add-to-cart"]').value;return[new Rt(t,1,Dt.getPrefixedFields(this.formElement,"wc_bookings_field"),this.extraFields())]}if(this.isGroupedProduct()){const t=[];return this.formElement.querySelectorAll('input[type="number"]').forEach((e=>{if(!e.value)return;const r=e.getAttribute("name").match(/quantity\[([\d]*)\]/);if(2!==r.length)return;const n=parseInt(r[1]),o=parseInt(e.value);t.push(new Lt(n,o,null,this.extraFields()))})),t}const t=document.querySelector('[name="add-to-cart"]').value,e=document.querySelector('[name="quantity"]').value,r=this.variations();return[new Lt(t,e,r,this.extraFields())]}extraFields(){return Dt.getFilteredFields(this.formElement,["add-to-cart","quantity","product_id","variation_id"],["attribute_","wc_bookings_field"])}createOrder(){return this.cartHelper=null,(t,e,r={})=>(this.errorHandler.clear(),this.updateCart.update((t=>{this.cartHelper=(new Mt).addFromPurchaseUnits(t);const e=U(),r=void 0!==this.config.bn_codes[this.config.context]?this.config.bn_codes[this.config.context]:"";return fetch(this.config.ajax.create_order.endpoint,{method:"POST",headers:{"Content-Type":"application/json"},credentials:"same-origin",body:JSON.stringify({nonce:this.config.ajax.create_order.nonce,purchase_units:t,payer:e,bn_code:r,payment_method:A,funding_source:window.ppcpFundingSource,context:this.config.context})}).then((function(t){return t.json()})).then((function(t){if(!t.success)throw console.error(t),Error(t.data.message);return t.data.id}))}),this.getProducts(),r.updateCartOptions||{}))}variations(){return this.hasVariations()?[...this.formElement.querySelectorAll("[name^='attribute_']")].map((t=>({value:t.value,name:t.name}))):null}hasVariations(){return this.formElement.classList.contains("variations_form")}isGroupedProduct(){return this.formElement.classList.contains("grouped_form")}isBookingProduct(){return!!this.formElement.querySelector(".wc-booking-product-id")}cleanCart(){this.cartHelper.removeFromCart().then((()=>{this.refreshMiniCart()})).catch((t=>{this.refreshMiniCart()}))}refreshMiniCart(){jQuery(document.body).trigger("wc_fragment_refresh")}},Bt=class{constructor(t,e){this.endpoint=t,this.nonce=e}simulate(t,e){return new Promise(((r,n)=>{fetch(this.endpoint,{method:"POST",headers:{"Content-Type":"application/json"},credentials:"same-origin",body:JSON.stringify({nonce:this.nonce,products:e})}).then((t=>t.json())).then((e=>{if(!e.success)return void n(e.data);const o=t(e.data);r(o)}))}))}},Gt=class{constructor(t,e){this.genericErrorText=t,this.wrapper=e}genericError(){this.clear(),this.message(this.genericErrorText)}appendPreparedErrorMessageElement(t){this._getMessageContainer().replaceWith(t)}message(t){this._addMessage(t),this._scrollToMessages()}messages(t){t.forEach((t=>this._addMessage(t))),this._scrollToMessages()}currentHtml(){return this._getMessageContainer().outerHTML}_addMessage(t){if(0===t.length)throw new Error("A new message text must be a non-empty string.");const e=this._getMessageContainer(),r=this._prepareMessageElement(t);e.appendChild(r)}_scrollToMessages(){jQuery.scroll_to_notices(jQuery(".woocommerce-error"))}_getMessageContainer(){let t=document.querySelector("ul.woocommerce-error");return null===t&&(t=document.createElement("ul"),t.setAttribute("class","woocommerce-error"),t.setAttribute("role","alert"),jQuery(this.wrapper).prepend(t)),t}_prepareMessageElement(t){const e=document.createElement("li");return e.innerHTML=t,e}clear(){jQuery(".woocommerce-error, .woocommerce-message").remove()}},Ht=class{constructor(t,e){this.endpoint=t,this.nonce=e}update(t,e,r={}){return new Promise(((n,o)=>{fetch(this.endpoint,{method:"POST",headers:{"Content-Type":"application/json"},credentials:"same-origin",body:JSON.stringify({nonce:this.nonce,products:e,...r})}).then((t=>t.json())).then((e=>{if(!e.success)return void o(e.data);const r=t(e.data);n(r)}))}))}},qt=class{constructor(t,e){this.config=t,this.errorHandler=e}subscriptionsConfiguration(t){return{createSubscription:(e,r)=>r.subscription.create({plan_id:t}),onApprove:(t,e)=>{fetch(this.config.ajax.approve_subscription.endpoint,{method:"POST",credentials:"same-origin",body:JSON.stringify({nonce:this.config.ajax.approve_subscription.nonce,order_id:t.orderID,subscription_id:t.subscriptionID,should_create_wc_order:!context.config.vaultingEnabled||"venmo"!==t.paymentSource})}).then((t=>t.json())).then((t=>{if(!t.success)throw console.log(t),Error(t.data.message);const e=t.data?.order_received_url;location.href=e||context.config.redirect}))},onError:t=>{console.error(t)}}}configuration(){return{createOrder:(t,e)=>{const r=U(),n=void 0!==this.config.bn_codes[this.config.context]?this.config.bn_codes[this.config.context]:"";return fetch(this.config.ajax.create_order.endpoint,{method:"POST",headers:{"Content-Type":"application/json"},credentials:"same-origin",body:JSON.stringify({nonce:this.config.ajax.create_order.nonce,purchase_units:[],payment_method:A,funding_source:window.ppcpFundingSource,bn_code:n,payer:r,context:this.config.context})}).then((function(t){return t.json()})).then((function(t){if(!t.success)throw console.error(t),Error(t.data.message);return t.data.id}))},onApprove:Nt(this,this.errorHandler),onError:t=>{this.errorHandler.genericError()}}}};function Ut(t){return Ut="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ut(t)}function Wt(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,$t(n.key),n)}}function $t(t){var e=function(t){if("object"!=Ut(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,"string");if("object"!=Ut(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==Ut(e)?e:e+""}function zt(t,e,r){(function(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")})(t,e),e.set(t,r)}function Vt(t,e){return t.get(Yt(t,e))}function Qt(t,e,r){return t.set(Yt(t,e),r),r}function Yt(t,e,r){if("function"==typeof t?t===e:t.has(e))return arguments.length<3?e:r;throw new TypeError("Private element is not present on this object")}r(9868);var Jt=new WeakMap,Kt=new WeakMap,Xt=new WeakMap,Zt=new WeakMap,te=function(){return t=function t(e,r,n,o){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),zt(this,Jt,""),zt(this,Kt,""),zt(this,Xt,0),zt(this,Zt,0),Qt(Jt,this,o),Qt(Kt,this,n),r=this.toAmount(r),e=this.toAmount(e),this.shippingFee=r,this.amount=e-r},(e=[{key:"amount",get:function(){return Vt(Xt,this)},set:function(t){Qt(Xt,this,this.toAmount(t))}},{key:"shippingFee",get:function(){return Vt(Zt,this)},set:function(t){Qt(Zt,this,this.toAmount(t))}},{key:"currencyCode",get:function(){return Vt(Kt,this)}},{key:"countryCode",get:function(){return Vt(Jt,this)}},{key:"totalPrice",get:function(){return(Vt(Xt,this)+Vt(Zt,this)).toFixed(2)}},{key:"finalObject",get:function(){return{countryCode:this.countryCode,currencyCode:this.currencyCode,totalPriceStatus:"FINAL",totalPrice:this.totalPrice}}},{key:"toAmount",value:function(t){return t=Number(t)||0,Math.round(100*t)/100}},{key:"setTotal",value:function(t,e){(t=this.toAmount(t))&&(this.shippingFee=e,this.amount=t-this.shippingFee)}}])&&Wt(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e}();function ee(t){return ee="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ee(t)}function re(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,ne(n.key),n)}}function ne(t){var e=function(t){if("object"!=ee(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,"string");if("object"!=ee(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==ee(e)?e:e+""}var oe=function(){return t=function t(e,r,n){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.buttonConfig=e,this.ppcpConfig=r,this.externalHandler=n},(e=[{key:"validateContext",value:function(){var t;return null===(t=this.ppcpConfig)||void 0===t||null===(t=t.locations_with_subscription_product)||void 0===t||!t.cart}},{key:"shippingAllowed",value:function(){return this.buttonConfig.shipping.configured}},{key:"transactionInfo",value:function(){var t=this;return new Promise((function(e,r){fetch(t.ppcpConfig.ajax.cart_script_params.endpoint,{method:"GET",credentials:"same-origin"}).then((function(t){return t.json()})).then((function(t){if(t.success){var r=t.data,n=new te(r.total,r.shipping_fee,r.currency_code,r.country_code);e(n)}}))}))}},{key:"createOrder",value:function(){return this.actionHandler().configuration().createOrder(null,null)}},{key:"approveOrder",value:function(t,e){return this.actionHandler().configuration().onApprove(t,e)}},{key:"actionHandler",value:function(){return new qt(this.ppcpConfig,this.errorHandler())}},{key:"errorHandler",value:function(){return new Gt(this.ppcpConfig.labels.error.generic,document.querySelector(".woocommerce-notices-wrapper"))}}])&&re(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e}();const ie=oe;function ae(t){return ae="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ae(t)}function ce(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,ue(n.key),n)}}function ue(t){var e=function(t){if("object"!=ae(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,"string");if("object"!=ae(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==ae(e)?e:e+""}function se(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(se=function(){return!!t})()}function le(t){return le=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},le(t)}function fe(t,e){return fe=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},fe(t,e)}const pe=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),function(t,e,r){return e=le(e),function(t,e){if(e&&("object"==ae(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,se()?Reflect.construct(e,r||[],le(t).constructor):e.apply(t,r))}(this,e,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&fe(t,e)}(e,t),r=e,(n=[{key:"validateContext",value:function(){var t;return null===(t=this.ppcpConfig)||void 0===t||null===(t=t.locations_with_subscription_product)||void 0===t||!t.product}},{key:"transactionInfo",value:function(){var t=this,e=new Gt(this.ppcpConfig.labels.error.generic,document.querySelector(".woocommerce-notices-wrapper")),r=new Ft(null,null,document.querySelector("form.cart"),e),n=PayPalCommerceGateway.data_client_id.has_subscriptions&&PayPalCommerceGateway.data_client_id.paypal_subscriptions_enabled?r.getSubscriptionProducts():r.getProducts();return new Promise((function(e,r){new Bt(t.ppcpConfig.ajax.simulate_cart.endpoint,t.ppcpConfig.ajax.simulate_cart.nonce).simulate((function(t){var r=new te(t.total,t.shipping_fee,t.currency_code,t.country_code);e(r)}),n)}))}},{key:"createOrder",value:function(){return this.actionHandler().configuration().createOrder(null,null,{updateCartOptions:{keepShipping:!0}})}},{key:"actionHandler",value:function(){return new Ft(this.ppcpConfig,new Ht(this.ppcpConfig.ajax.change_cart.endpoint,this.ppcpConfig.ajax.change_cart.nonce),document.querySelector("form.cart"),this.errorHandler())}}])&&ce(r.prototype,n),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,n}(ie);function he(t){return he="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},he(t)}function de(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(de=function(){return!!t})()}function ye(t){return ye=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},ye(t)}function ve(t,e){return ve=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},ve(t,e)}const ge=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),function(t,e,r){return e=ye(e),function(t,e){if(e&&("object"==he(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,de()?Reflect.construct(e,r||[],ye(t).constructor):e.apply(t,r))}(this,e,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&ve(t,e)}(e,t),r=e,Object.defineProperty(r,"prototype",{writable:!1}),r;var r}(ie),me=class{constructor(t="form.woocommerce-checkout"){this.target=t}setTarget(t){this.target=t}block(){jQuery(this.target).block({message:null,overlayCSS:{background:"#fff",opacity:.6}})}unblock(){jQuery(this.target).unblock()}};!function(){var t;function e(t){var e=0;return function(){return e<t.length?{done:!1,value:t[e++]}:{done:!0}}}var r,n="function"==typeof Object.defineProperties?Object.defineProperty:function(t,e,r){return t==Array.prototype||t==Object.prototype||(t[e]=r.value),t},o=function(t){t=["object"==typeof globalThis&&globalThis,t,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var e=0;e<t.length;++e){var r=t[e];if(r&&r.Math==Math)return r}throw Error("Cannot find global object")}(this);function i(t,e){if(e)t:{var r=o;t=t.split(".");for(var i=0;i<t.length-1;i++){var a=t[i];if(!(a in r))break t;r=r[a]}(e=e(i=r[t=t[t.length-1]]))!=i&&null!=e&&n(r,t,{configurable:!0,writable:!0,value:e})}}function a(t){return(t={next:t})[Symbol.iterator]=function(){return this},t}function c(t){var r="undefined"!=typeof Symbol&&Symbol.iterator&&t[Symbol.iterator];return r?r.call(t):{next:e(t)}}if(i("Symbol",(function(t){function e(t,e){this.A=t,n(this,"description",{configurable:!0,writable:!0,value:e})}if(t)return t;e.prototype.toString=function(){return this.A};var r="jscomp_symbol_"+(1e9*Math.random()>>>0)+"_",o=0;return function t(n){if(this instanceof t)throw new TypeError("Symbol is not a constructor");return new e(r+(n||"")+"_"+o++,n)}})),i("Symbol.iterator",(function(t){if(t)return t;t=Symbol("Symbol.iterator");for(var r="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),i=0;i<r.length;i++){var c=o[r[i]];"function"==typeof c&&"function"!=typeof c.prototype[t]&&n(c.prototype,t,{configurable:!0,writable:!0,value:function(){return a(e(this))}})}return t})),"function"==typeof Object.setPrototypeOf)r=Object.setPrototypeOf;else{var u;t:{var s={};try{s.__proto__={a:!0},u=s.a;break t}catch(t){}u=!1}r=u?function(t,e){if(t.__proto__=e,t.__proto__!==e)throw new TypeError(t+" is not extensible");return t}:null}var l=r;function f(){this.m=!1,this.j=null,this.v=void 0,this.h=1,this.u=this.C=0,this.l=null}function p(t){if(t.m)throw new TypeError("Generator is already running");t.m=!0}function h(t,e){return t.h=3,{value:e}}function d(t){this.g=new f,this.G=t}function y(t,e,r,n){try{var o=e.call(t.g.j,r);if(!(o instanceof Object))throw new TypeError("Iterator result "+o+" is not an object");if(!o.done)return t.g.m=!1,o;var i=o.value}catch(e){return t.g.j=null,t.g.s(e),v(t)}return t.g.j=null,n.call(t.g,i),v(t)}function v(t){for(;t.g.h;)try{var e=t.G(t.g);if(e)return t.g.m=!1,{value:e.value,done:!1}}catch(e){t.g.v=void 0,t.g.s(e)}if(t.g.m=!1,t.g.l){if(e=t.g.l,t.g.l=null,e.F)throw e.D;return{value:e.return,done:!0}}return{value:void 0,done:!0}}function g(t){this.next=function(e){return t.o(e)},this.throw=function(e){return t.s(e)},this.return=function(e){return function(t,e){p(t.g);var r=t.g.j;return r?y(t,"return"in r?r.return:function(t){return{value:t,done:!0}},e,t.g.return):(t.g.return(e),v(t))}(t,e)},this[Symbol.iterator]=function(){return this}}function m(t,e){return e=new g(new d(e)),l&&t.prototype&&l(e,t.prototype),e}if(f.prototype.o=function(t){this.v=t},f.prototype.s=function(t){this.l={D:t,F:!0},this.h=this.C||this.u},f.prototype.return=function(t){this.l={return:t},this.h=this.u},d.prototype.o=function(t){return p(this.g),this.g.j?y(this,this.g.j.next,t,this.g.o):(this.g.o(t),v(this))},d.prototype.s=function(t){return p(this.g),this.g.j?y(this,this.g.j.throw,t,this.g.o):(this.g.s(t),v(this))},i("Array.prototype.entries",(function(t){return t||function(){return function(t,e){t instanceof String&&(t+="");var r=0,n=!1,o={next:function(){if(!n&&r<t.length){var o=r++;return{value:e(o,t[o]),done:!1}}return n=!0,{done:!0,value:void 0}}};return o[Symbol.iterator]=function(){return o},o}(this,(function(t,e){return[t,e]}))}})),"undefined"!=typeof Blob&&("undefined"==typeof FormData||!FormData.prototype.keys)){var b=function(t,e){for(var r=0;r<t.length;r++)e(t[r])},w=function(t){return t.replace(/\r?\n|\r/g,"\r\n")},x=function(t,e,r){return e instanceof Blob?(r=void 0!==r?String(r+""):"string"==typeof e.name?e.name:"blob",e.name===r&&"[object Blob]"!==Object.prototype.toString.call(e)||(e=new File([e],r)),[String(t),e]):[String(t),String(e)]},E=function(t,e){if(t.length<e)throw new TypeError(e+" argument required, but only "+t.length+" present.")},S="object"==typeof globalThis?globalThis:"object"==typeof window?window:"object"==typeof self?self:this,O=S.FormData,j=S.XMLHttpRequest&&S.XMLHttpRequest.prototype.send,_=S.Request&&S.fetch,P=S.navigator&&S.navigator.sendBeacon,k=S.Element&&S.Element.prototype,C=S.Symbol&&Symbol.toStringTag;C&&(Blob.prototype[C]||(Blob.prototype[C]="Blob"),"File"in S&&!File.prototype[C]&&(File.prototype[C]="File"));try{new File([],"")}catch(t){S.File=function(t,e,r){return t=new Blob(t,r||{}),Object.defineProperties(t,{name:{value:e},lastModified:{value:+(r&&void 0!==r.lastModified?new Date(r.lastModified):new Date)},toString:{value:function(){return"[object File]"}}}),C&&Object.defineProperty(t,C,{value:"File"}),t}}var I=function(t){return t.replace(/\n/g,"%0A").replace(/\r/g,"%0D").replace(/"/g,"%22")},T=function(t){this.i=[];var e=this;t&&b(t.elements,(function(t){if(t.name&&!t.disabled&&"submit"!==t.type&&"button"!==t.type&&!t.matches("form fieldset[disabled] *"))if("file"===t.type){var r=t.files&&t.files.length?t.files:[new File([],"",{type:"application/octet-stream"})];b(r,(function(r){e.append(t.name,r)}))}else"select-multiple"===t.type||"select-one"===t.type?b(t.options,(function(r){!r.disabled&&r.selected&&e.append(t.name,r.value)})):"checkbox"===t.type||"radio"===t.type?t.checked&&e.append(t.name,t.value):(r="textarea"===t.type?w(t.value):t.value,e.append(t.name,r))}))};if((t=T.prototype).append=function(t,e,r){E(arguments,2),this.i.push(x(t,e,r))},t.delete=function(t){E(arguments,1);var e=[];t=String(t),b(this.i,(function(r){r[0]!==t&&e.push(r)})),this.i=e},t.entries=function t(){var e,r=this;return m(t,(function(t){if(1==t.h&&(e=0),3!=t.h)return e<r.i.length?t=h(t,r.i[e]):(t.h=0,t=void 0),t;e++,t.h=2}))},t.forEach=function(t,e){E(arguments,1);for(var r=c(this),n=r.next();!n.done;n=r.next()){var o=c(n.value);n=o.next().value,o=o.next().value,t.call(e,o,n,this)}},t.get=function(t){E(arguments,1);var e=this.i;t=String(t);for(var r=0;r<e.length;r++)if(e[r][0]===t)return e[r][1];return null},t.getAll=function(t){E(arguments,1);var e=[];return t=String(t),b(this.i,(function(r){r[0]===t&&e.push(r[1])})),e},t.has=function(t){E(arguments,1),t=String(t);for(var e=0;e<this.i.length;e++)if(this.i[e][0]===t)return!0;return!1},t.keys=function t(){var e,r,n,o=this;return m(t,(function(t){if(1==t.h&&(e=c(o),r=e.next()),3!=t.h)return r.done?void(t.h=0):(n=r.value,h(t,c(n).next().value));r=e.next(),t.h=2}))},t.set=function(t,e,r){E(arguments,2),t=String(t);var n=[],o=x(t,e,r),i=!0;b(this.i,(function(e){e[0]===t?i&&(i=!n.push(o)):n.push(e)})),i&&n.push(o),this.i=n},t.values=function t(){var e,r,n,o,i=this;return m(t,(function(t){if(1==t.h&&(e=c(i),r=e.next()),3!=t.h)return r.done?void(t.h=0):(n=r.value,(o=c(n)).next(),h(t,o.next().value));r=e.next(),t.h=2}))},T.prototype._asNative=function(){for(var t=new O,e=c(this),r=e.next();!r.done;r=e.next()){var n=c(r.value);r=n.next().value,n=n.next().value,t.append(r,n)}return t},T.prototype._blob=function(){var t="----formdata-polyfill-"+Math.random(),e=[],r="--"+t+'\r\nContent-Disposition: form-data; name="';return this.forEach((function(t,n){return"string"==typeof t?e.push(r+I(w(n))+'"\r\n\r\n'+w(t)+"\r\n"):e.push(r+I(w(n))+'"; filename="'+I(t.name)+'"\r\nContent-Type: '+(t.type||"application/octet-stream")+"\r\n\r\n",t,"\r\n")})),e.push("--"+t+"--"),new Blob(e,{type:"multipart/form-data; boundary="+t})},T.prototype[Symbol.iterator]=function(){return this.entries()},T.prototype.toString=function(){return"[object FormData]"},k&&!k.matches&&(k.matches=k.matchesSelector||k.mozMatchesSelector||k.msMatchesSelector||k.oMatchesSelector||k.webkitMatchesSelector||function(t){for(var e=(t=(this.document||this.ownerDocument).querySelectorAll(t)).length;0<=--e&&t.item(e)!==this;);return-1<e}),C&&(T.prototype[C]="FormData"),j){var A=S.XMLHttpRequest.prototype.setRequestHeader;S.XMLHttpRequest.prototype.setRequestHeader=function(t,e){A.call(this,t,e),"content-type"===t.toLowerCase()&&(this.B=!0)},S.XMLHttpRequest.prototype.send=function(t){t instanceof T?(t=t._blob(),this.B||this.setRequestHeader("Content-Type",t.type),j.call(this,t)):j.call(this,t)}}_&&(S.fetch=function(t,e){return e&&e.body&&e.body instanceof T&&(e.body=e.body._blob()),_.call(this,t,e)}),P&&(S.navigator.sendBeacon=function(t,e){return e instanceof T&&(e=e._asNative()),P.call(this,t,e)}),S.FormData=T}}();const be=(t,e,r)=>(n,o)=>(r.block(),e.clear(),fetch(t.config.ajax.approve_order.endpoint,{method:"POST",headers:{"Content-Type":"application/json"},credentials:"same-origin",body:JSON.stringify({nonce:t.config.ajax.approve_order.nonce,order_id:n.orderID,funding_source:window.ppcpFundingSource})}).then((t=>t.json())).then((t=>{if(r.unblock(),!t.success){if(100===t.data.code?e.message(t.data.message):e.genericError(),void 0!==o&&void 0!==o.restart)return o.restart();throw new Error(t.data.message)}document.querySelector("#place_order").click()})));class we{constructor(t,e){this.url=t,this.nonce=e}async validate(t){const e=new FormData(t),r=await fetch(this.url,{method:"POST",headers:{"Content-Type":"application/json"},credentials:"same-origin",body:JSON.stringify({nonce:this.nonce,form_encoded:new URLSearchParams(e).toString()})}),n=await r.json();if(!n.success){if(n.data.refresh&&jQuery(document.body).trigger("update_checkout"),n.data.errors)return n.data.errors;throw Error(n.data.message)}return[]}}const xe=class{constructor(t,e,r){this.config=t,this.errorHandler=e,this.spinner=r}subscriptionsConfiguration(t){return{createSubscription:async(e,r)=>{try{await(n=this.config,new Promise((async(t,e)=>{try{const r=new me,o=new Gt(n.labels.error.generic,document.querySelector(".woocommerce-notices-wrapper")),i="checkout"===n.context?"form.checkout":"form#order_review",a=n.early_checkout_validation_enabled?new we(n.ajax.validate_checkout.endpoint,n.ajax.validate_checkout.nonce):null;if(!a)return void t();a.validate(document.querySelector(i)).then((n=>{n.length>0?(r.unblock(),o.clear(),o.messages(n),jQuery(document.body).trigger("checkout_error",[o.currentHtml()]),e()):t()}))}catch(t){console.error(t),e()}})))}catch(t){throw{type:"form-validation-error"}}var n;return r.subscription.create({plan_id:t})},onApprove:(t,e)=>{fetch(this.config.ajax.approve_subscription.endpoint,{method:"POST",credentials:"same-origin",body:JSON.stringify({nonce:this.config.ajax.approve_subscription.nonce,order_id:t.orderID,subscription_id:t.subscriptionID})}).then((t=>t.json())).then((t=>{document.querySelector("#place_order").click()}))},onError:t=>{console.error(t)}}}configuration(){const t=this.spinner;return{createOrder:(e,r)=>{const n=U(),o=void 0!==this.config.bn_codes[this.config.context]?this.config.bn_codes[this.config.context]:"",i=this.errorHandler,a="checkout"===this.config.context?"form.checkout":"form#order_review",c=new FormData(document.querySelector(a)),u=!!jQuery("#createaccount").is(":checked"),s=R(),l=window.ppcpFundingSource,f=!!document.getElementById("wc-ppcp-credit-card-gateway-new-payment-method")?.checked;return fetch(this.config.ajax.create_order.endpoint,{method:"POST",headers:{"Content-Type":"application/json"},credentials:"same-origin",body:JSON.stringify({nonce:this.config.ajax.create_order.nonce,payer:n,bn_code:o,context:this.config.context,order_id:this.config.order_id,payment_method:s,funding_source:l,form_encoded:new URLSearchParams(c).toString(),createaccount:u,save_payment_method:f})}).then((function(t){return t.json()})).then((function(e){if(!e.success){if(t.unblock(),void 0!==e.messages){const t=new DOMParser;i.appendPreparedErrorMessageElement(t.parseFromString(e.messages,"text/html").querySelector("ul"))}else i.clear(),e.data.refresh&&jQuery(document.body).trigger("update_checkout"),e.data.errors?.length>0?i.messages(e.data.errors):e.data.details?.length>0?i.message(e.data.details.map((t=>`${t.issue} ${t.description}`)).join("<br/>")):i.message(e.data.message),jQuery(document.body).trigger("checkout_error",[i.currentHtml()]);throw{type:"create-order-error",data:e.data}}const r=document.createElement("input");return r.setAttribute("type","hidden"),r.setAttribute("name","ppcp-resume-order"),r.setAttribute("value",e.data.custom_id),document.querySelector(a).appendChild(r),e.data.id}))},onApprove:be(this,this.errorHandler,this.spinner),onCancel:()=>{t.unblock()},onError:e=>{console.error(e),t.unblock(),e&&"create-order-error"===e.type||this.errorHandler.genericError()}}}};function Ee(t){return Ee="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ee(t)}function Se(){Se=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,r){return t[e]=r}}function l(t,e,r,n){var i=e&&e.prototype instanceof g?e:g,a=Object.create(i.prototype),c=new I(n||[]);return o(a,"_invoke",{value:_(t,r,c)}),a}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=l;var p="suspendedStart",h="suspendedYield",d="executing",y="completed",v={};function g(){}function m(){}function b(){}var w={};s(w,a,(function(){return this}));var x=Object.getPrototypeOf,E=x&&x(x(T([])));E&&E!==r&&n.call(E,a)&&(w=E);var S=b.prototype=g.prototype=Object.create(w);function O(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function j(t,e){function r(o,i,a,c){var u=f(t[o],t,i);if("throw"!==u.type){var s=u.arg,l=s.value;return l&&"object"==Ee(l)&&n.call(l,"__await")?e.resolve(l.__await).then((function(t){r("next",t,a,c)}),(function(t){r("throw",t,a,c)})):e.resolve(l).then((function(t){s.value=t,a(s)}),(function(t){return r("throw",t,a,c)}))}c(u.arg)}var i;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return i=i?i.then(o,o):o()}})}function _(e,r,n){var o=p;return function(i,a){if(o===d)throw Error("Generator is already running");if(o===y){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var c=n.delegate;if(c){var u=P(c,n);if(u){if(u===v)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===p)throw o=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=d;var s=f(e,r,n);if("normal"===s.type){if(o=n.done?y:h,s.arg===v)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o=y,n.method="throw",n.arg=s.arg)}}}function P(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,P(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=f(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,v;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function k(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function C(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function I(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(k,this),this.reset(!0)}function T(e){if(e||""===e){var r=e[a];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(Ee(e)+" is not iterable")}return m.prototype=b,o(S,"constructor",{value:b,configurable:!0}),o(b,"constructor",{value:m,configurable:!0}),m.displayName=s(b,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===m||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,s(t,u,"GeneratorFunction")),t.prototype=Object.create(S),t},e.awrap=function(t){return{__await:t}},O(j.prototype),s(j.prototype,c,(function(){return this})),e.AsyncIterator=j,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new j(l(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},O(S),s(S,u,"Generator"),s(S,a,(function(){return this})),s(S,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=T,I.prototype={constructor:I,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(C),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return c.type="throw",c.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],c=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var u=n.call(a,"catchLoc"),s=n.call(a,"finallyLoc");if(u&&s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),C(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;C(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:T(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),v}},e}function Oe(t,e,r,n,o,i,a){try{var c=t[i](a),u=c.value}catch(t){return void r(t)}c.done?e(u):Promise.resolve(u).then(n,o)}function je(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,_e(n.key),n)}}function _e(t){var e=function(t){if("object"!=Ee(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,"string");if("object"!=Ee(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==Ee(e)?e:e+""}function Pe(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(Pe=function(){return!!t})()}function ke(t){return ke=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},ke(t)}function Ce(t,e){return Ce=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Ce(t,e)}const Ie=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),function(t,e,r){return e=ke(e),function(t,e){if(e&&("object"==Ee(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,Pe()?Reflect.construct(e,r||[],ke(t).constructor):e.apply(t,r))}(this,e,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Ce(t,e)}(e,t),r=e,n=[{key:"validateForm",value:function(){var t=this;return new Promise(function(){var e,r=(e=Se().mark((function e(r,n){var o,i,a,c;return Se().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,o=new me,i=t.errorHandler(),a="checkout"===t.ppcpConfig.context?"form.checkout":"form#order_review",c=t.ppcpConfig.early_checkout_validation_enabled?new we(t.ppcpConfig.ajax.validate_checkout.endpoint,t.ppcpConfig.ajax.validate_checkout.nonce):null){e.next=8;break}return r(),e.abrupt("return");case 8:c.validate(document.querySelector(a)).then((function(t){t.length>0?(o.unblock(),i.clear(),i.messages(t),jQuery(document.body).trigger("checkout_error",[i.currentHtml()]),n()):r()})),e.next=15;break;case 11:e.prev=11,e.t0=e.catch(0),console.error(e.t0),n();case 15:case"end":return e.stop()}}),e,null,[[0,11]])})),function(){var t=this,r=arguments;return new Promise((function(n,o){var i=e.apply(t,r);function a(t){Oe(i,n,o,a,c,"next",t)}function c(t){Oe(i,n,o,a,c,"throw",t)}a(void 0)}))});return function(t,e){return r.apply(this,arguments)}}())}},{key:"actionHandler",value:function(){return new xe(this.ppcpConfig,this.errorHandler(),new me)}}],n&&je(r.prototype,n),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,n}(ie);function Te(t){return Te="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Te(t)}function Ae(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Le(n.key),n)}}function Le(t){var e=function(t){if("object"!=Te(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,"string");if("object"!=Te(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==Te(e)?e:e+""}function Re(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(Re=function(){return!!t})()}function Ne(t){return Ne=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Ne(t)}function Me(t,e){return Me=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Me(t,e)}const De=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),function(t,e,r){return e=Ne(e),function(t,e){if(e&&("object"==Te(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,Re()?Reflect.construct(e,r||[],Ne(t).constructor):e.apply(t,r))}(this,e,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Me(t,e)}(e,t),r=e,(n=[{key:"createOrder",value:function(){return this.externalHandler.createOrder()}},{key:"approveOrder",value:function(t,e){return this.externalHandler.onApprove(t,e)}}])&&Ae(r.prototype,n),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,n}(ie);function Fe(t){return Fe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Fe(t)}function Be(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Ge(n.key),n)}}function Ge(t){var e=function(t){if("object"!=Fe(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,"string");if("object"!=Fe(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==Fe(e)?e:e+""}function He(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(He=function(){return!!t})()}function qe(t){return qe=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},qe(t)}function Ue(t,e){return Ue=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Ue(t,e)}const We=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),function(t,e,r){return e=qe(e),function(t,e){if(e&&("object"==Fe(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,He()?Reflect.construct(e,r||[],qe(t).constructor):e.apply(t,r))}(this,e,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Ue(t,e)}(e,t),r=e,(n=[{key:"createOrder",value:function(){return this.externalHandler.createOrder()}},{key:"approveOrder",value:function(t,e){return this.externalHandler.onApprove(t,e)}}])&&Be(r.prototype,n),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,n}(ie);function $e(t){return $e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},$e(t)}function ze(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(ze=function(){return!!t})()}function Ve(t){return Ve=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Ve(t)}function Qe(t,e){return Qe=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Qe(t,e)}const Ye=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),function(t,e,r){return e=Ve(e),function(t,e){if(e&&("object"==$e(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,ze()?Reflect.construct(e,r||[],Ve(t).constructor):e.apply(t,r))}(this,e,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Qe(t,e)}(e,t),r=e,Object.defineProperty(r,"prototype",{writable:!1}),r;var r}(ie);function Je(t){return Je="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Je(t)}function Ke(){Ke=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,r){return t[e]=r}}function l(t,e,r,n){var i=e&&e.prototype instanceof g?e:g,a=Object.create(i.prototype),c=new I(n||[]);return o(a,"_invoke",{value:_(t,r,c)}),a}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=l;var p="suspendedStart",h="suspendedYield",d="executing",y="completed",v={};function g(){}function m(){}function b(){}var w={};s(w,a,(function(){return this}));var x=Object.getPrototypeOf,E=x&&x(x(T([])));E&&E!==r&&n.call(E,a)&&(w=E);var S=b.prototype=g.prototype=Object.create(w);function O(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function j(t,e){function r(o,i,a,c){var u=f(t[o],t,i);if("throw"!==u.type){var s=u.arg,l=s.value;return l&&"object"==Je(l)&&n.call(l,"__await")?e.resolve(l.__await).then((function(t){r("next",t,a,c)}),(function(t){r("throw",t,a,c)})):e.resolve(l).then((function(t){s.value=t,a(s)}),(function(t){return r("throw",t,a,c)}))}c(u.arg)}var i;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return i=i?i.then(o,o):o()}})}function _(e,r,n){var o=p;return function(i,a){if(o===d)throw Error("Generator is already running");if(o===y){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var c=n.delegate;if(c){var u=P(c,n);if(u){if(u===v)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===p)throw o=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=d;var s=f(e,r,n);if("normal"===s.type){if(o=n.done?y:h,s.arg===v)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o=y,n.method="throw",n.arg=s.arg)}}}function P(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,P(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=f(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,v;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function k(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function C(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function I(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(k,this),this.reset(!0)}function T(e){if(e||""===e){var r=e[a];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(Je(e)+" is not iterable")}return m.prototype=b,o(S,"constructor",{value:b,configurable:!0}),o(b,"constructor",{value:m,configurable:!0}),m.displayName=s(b,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===m||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,s(t,u,"GeneratorFunction")),t.prototype=Object.create(S),t},e.awrap=function(t){return{__await:t}},O(j.prototype),s(j.prototype,c,(function(){return this})),e.AsyncIterator=j,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new j(l(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},O(S),s(S,u,"Generator"),s(S,a,(function(){return this})),s(S,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=T,I.prototype={constructor:I,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(C),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return c.type="throw",c.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],c=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var u=n.call(a,"catchLoc"),s=n.call(a,"finallyLoc");if(u&&s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),C(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;C(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:T(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),v}},e}function Xe(t,e,r,n,o,i,a){try{var c=t[i](a),u=c.value}catch(t){return void r(t)}c.done?e(u):Promise.resolve(u).then(n,o)}function Ze(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,tr(n.key),n)}}function tr(t){var e=function(t){if("object"!=Je(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,"string");if("object"!=Je(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==Je(e)?e:e+""}function er(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(er=function(){return!!t})()}function rr(t){return rr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},rr(t)}function nr(t,e){return nr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},nr(t,e)}const or=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),function(t,e,r){return e=rr(e),function(t,e){if(e&&("object"==Je(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,er()?Reflect.construct(e,r||[],rr(t).constructor):e.apply(t,r))}(this,e,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&nr(t,e)}(e,t),r=e,n=[{key:"validateContext",value:function(){var t;return null===(t=this.ppcpConfig)||void 0===t||null===(t=t.locations_with_subscription_product)||void 0===t||!t.payorder}},{key:"transactionInfo",value:function(){var t=this;return new Promise(function(){var e,r=(e=Ke().mark((function e(r,n){var o,i;return Ke().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:o=t.ppcpConfig.pay_now,i=new te(o.total,o.shipping_fee,o.currency_code,o.country_code),r(i);case 3:case"end":return e.stop()}}),e)})),function(){var t=this,r=arguments;return new Promise((function(n,o){var i=e.apply(t,r);function a(t){Xe(i,n,o,a,c,"next",t)}function c(t){Xe(i,n,o,a,c,"throw",t)}a(void 0)}))});return function(t,e){return r.apply(this,arguments)}}())}},{key:"actionHandler",value:function(){return new xe(this.ppcpConfig,this.errorHandler(),new me)}}],n&&Ze(r.prototype,n),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,n}(ie);function ir(t){return ir="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ir(t)}function ar(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,cr(n.key),n)}}function cr(t){var e=function(t){if("object"!=ir(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,"string");if("object"!=ir(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==ir(e)?e:e+""}function ur(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(ur=function(){return!!t})()}function sr(t){return sr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},sr(t)}function lr(t,e){return lr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},lr(t,e)}var fr=function(t){function e(t,r,n){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),function(t,e,r){return e=sr(e),function(t,e){if(e&&("object"==ir(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,ur()?Reflect.construct(e,r||[],sr(t).constructor):e.apply(t,r))}(this,e,[t,r,n])}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&lr(t,e)}(e,t),r=e,(n=[{key:"transactionInfo",value:function(){throw new Error("Transaction info fail. This is just a preview.")}},{key:"createOrder",value:function(){throw new Error("Create order fail. This is just a preview.")}},{key:"approveOrder",value:function(t,e){throw new Error("Approve order fail. This is just a preview.")}},{key:"actionHandler",value:function(){throw new Error("Action handler fail. This is just a preview.")}},{key:"errorHandler",value:function(){throw new Error("Error handler fail. This is just a preview.")}}])&&ar(r.prototype,n),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,n}(ie);const pr=fr;function hr(t){return hr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},hr(t)}function dr(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,yr(n.key),n)}}function yr(t){var e=function(t){if("object"!=hr(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,"string");if("object"!=hr(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==hr(e)?e:e+""}var vr=function(){return t=function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t)},e=[{key:"create",value:function(t,e,r,n){switch(t){case"product":return new pe(e,r,n);case"cart":return new ge(e,r,n);case"checkout":return new Ie(e,r,n);case"pay-now":return new or(e,r,n);case"mini-cart":return new Ye(e,r,n);case"cart-block":return new De(e,r,n);case"checkout-block":return new We(e,r,n);case"preview":return new pr(e,r,n)}}}],null&&dr(t.prototype,null),e&&dr(t,e),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e}();const gr=vr;function mr(t){return mr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},mr(t)}function br(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=function(t,e){if(t){if("string"==typeof t)return wr(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?wr(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,c=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return a=t.done,t},e:function(t){c=!0,i=t},f:function(){try{a||null==r.return||r.return()}finally{if(c)throw i}}}}function wr(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function xr(){xr=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,r){return t[e]=r}}function l(t,e,r,n){var i=e&&e.prototype instanceof g?e:g,a=Object.create(i.prototype),c=new I(n||[]);return o(a,"_invoke",{value:_(t,r,c)}),a}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=l;var p="suspendedStart",h="suspendedYield",d="executing",y="completed",v={};function g(){}function m(){}function b(){}var w={};s(w,a,(function(){return this}));var x=Object.getPrototypeOf,E=x&&x(x(T([])));E&&E!==r&&n.call(E,a)&&(w=E);var S=b.prototype=g.prototype=Object.create(w);function O(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function j(t,e){function r(o,i,a,c){var u=f(t[o],t,i);if("throw"!==u.type){var s=u.arg,l=s.value;return l&&"object"==mr(l)&&n.call(l,"__await")?e.resolve(l.__await).then((function(t){r("next",t,a,c)}),(function(t){r("throw",t,a,c)})):e.resolve(l).then((function(t){s.value=t,a(s)}),(function(t){return r("throw",t,a,c)}))}c(u.arg)}var i;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return i=i?i.then(o,o):o()}})}function _(e,r,n){var o=p;return function(i,a){if(o===d)throw Error("Generator is already running");if(o===y){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var c=n.delegate;if(c){var u=P(c,n);if(u){if(u===v)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===p)throw o=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=d;var s=f(e,r,n);if("normal"===s.type){if(o=n.done?y:h,s.arg===v)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o=y,n.method="throw",n.arg=s.arg)}}}function P(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,P(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=f(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,v;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function k(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function C(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function I(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(k,this),this.reset(!0)}function T(e){if(e||""===e){var r=e[a];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(mr(e)+" is not iterable")}return m.prototype=b,o(S,"constructor",{value:b,configurable:!0}),o(b,"constructor",{value:m,configurable:!0}),m.displayName=s(b,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===m||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,s(t,u,"GeneratorFunction")),t.prototype=Object.create(S),t},e.awrap=function(t){return{__await:t}},O(j.prototype),s(j.prototype,c,(function(){return this})),e.AsyncIterator=j,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new j(l(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},O(S),s(S,u,"Generator"),s(S,a,(function(){return this})),s(S,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=T,I.prototype={constructor:I,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(C),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return c.type="throw",c.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],c=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var u=n.call(a,"catchLoc"),s=n.call(a,"finallyLoc");if(u&&s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),C(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;C(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:T(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),v}},e}function Er(t,e,r,n,o,i,a){try{var c=t[i](a),u=c.value}catch(t){return void r(t)}c.done?e(u):Promise.resolve(u).then(n,o)}function Sr(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){Er(i,n,o,a,c,"next",t)}function c(t){Er(i,n,o,a,c,"throw",t)}a(void 0)}))}}function Or(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,jr(n.key),n)}}function jr(t){var e=function(t){if("object"!=mr(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,"string");if("object"!=mr(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==mr(e)?e:e+""}var _r=function(){return t=function t(e,r,n){var o=this,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.namespace=e,this.buttonConfig=r,this.ppcpConfig=n,this.buttonAttributes=i,this.googlePayConfig=null,this.transactionInfo=null,this.contextHandler=null,this.buttons=[],j.watchContextBootstrap(function(){var t=Sr(xr().mark((function t(e){var i,a;return xr().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(o.contextHandler=gr.create(e.context,r,n,e.handler),i=At.createButton(e.context,e.handler,r,n,o.contextHandler,o.buttonAttributes),o.buttons.push(i),a=function(){i.configure(o.googlePayConfig,o.transactionInfo,o.buttonAttributes),i.init()},!o.googlePayConfig||!o.transactionInfo){t.next=8;break}a(),t.next=11;break;case 8:return t.next=10,o.init();case 10:o.googlePayConfig&&o.transactionInfo&&a();case 11:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())},e=[{key:"init",value:(n=Sr(xr().mark((function t(){var e,r,n;return xr().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(t.prev=0,this.googlePayConfig){t.next=5;break}return t.next=4,window[this.namespace].Googlepay().config();case 4:this.googlePayConfig=t.sent;case 5:if(this.transactionInfo){t.next=9;break}return t.next=8,this.fetchTransactionInfo();case 8:this.transactionInfo=t.sent;case 9:if(this.googlePayConfig)if(this.transactionInfo){e=br(this.buttons);try{for(e.s();!(r=e.n()).done;)(n=r.value).configure(this.googlePayConfig,this.transactionInfo,this.buttonAttributes),n.init()}catch(t){e.e(t)}finally{e.f()}}else console.error("No transactionInfo found during init");else console.error("No GooglePayConfig received during init");t.next=15;break;case 12:t.prev=12,t.t0=t.catch(0),console.error("Error during initialization:",t.t0);case 15:case"end":return t.stop()}}),t,this,[[0,12]])}))),function(){return n.apply(this,arguments)})},{key:"fetchTransactionInfo",value:(r=Sr(xr().mark((function t(){return xr().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(t.prev=0,this.contextHandler){t.next=3;break}throw new Error("ContextHandler is not initialized");case 3:return t.next=5,this.contextHandler.transactionInfo();case 5:return t.abrupt("return",t.sent);case 8:throw t.prev=8,t.t0=t.catch(0),console.error("Error fetching transaction info:",t.t0),t.t0;case 12:case"end":return t.stop()}}),t,this,[[0,8]])}))),function(){return r.apply(this,arguments)})},{key:"reinit",value:function(){var t,e=br(this.buttons);try{for(e.s();!(t=e.n()).done;)t.value.reinit()}catch(t){e.e(t)}finally{e.f()}}}],e&&Or(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e,r,n}();const Pr=_r;function kr(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}const Cr=function(t,e,r,o,i,a){var c,u,s,l=(u=(0,n.useState)(null),s=2,function(t){if(Array.isArray(t))return t}(u)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,s=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){s=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return c}}(u,s)||function(t,e){if(t){if("string"==typeof t)return kr(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?kr(t,e):void 0}}(u,s)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),f=l[0],p=l[1],h=function(t,e,r){return(0,n.useMemo)((function(){var n,o=P((null==e?void 0:e.button)||{},(null==t?void 0:t.button)||{});return r&&o.Default&&(o.Default.height=r.height||o.Default.height,o.Default.borderRadius=r.borderRadius||o.Default.borderRadius),"buy"===(null===(n=o.MiniCart)||void 0===n?void 0:n.type)&&(o.MiniCart.type="pay"),o}),[t,e,r])}(r,o,a);return(0,n.useEffect)((function(){var e,n;if(null!=t&&t.defaultView&&r&&i){var o=null===(e=t.defaultView.google)||void 0===e||null===(e=e.payments)||void 0===e?void 0:e.api;if(o){var a=new o.PaymentsClient({environment:"TEST"}),c={allowedPaymentMethods:i.allowedPaymentMethods,buttonColor:r.buttonColor||"black",buttonType:r.buttonType||"pay",buttonLocale:r.buttonLocale||"en",buttonSizeMode:"fill",buttonRadius:parseInt(null==h||null===(n=h.Default)||void 0===n?void 0:n.borderRadius),onClick:function(t){t.preventDefault()}},u=a.createButton(c);return p(u),function(){p(null)}}}}),[e,r,o,i,h]),{button:f,containerStyles:{height:null!=h&&null!==(c=h.Default)&&void 0!==c&&c.height?"".concat(h.Default.height,"px"):""}}};function Ir(t){return Ir="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ir(t)}function Tr(){Tr=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,r){return t[e]=r}}function l(t,e,r,n){var i=e&&e.prototype instanceof g?e:g,a=Object.create(i.prototype),c=new I(n||[]);return o(a,"_invoke",{value:_(t,r,c)}),a}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=l;var p="suspendedStart",h="suspendedYield",d="executing",y="completed",v={};function g(){}function m(){}function b(){}var w={};s(w,a,(function(){return this}));var x=Object.getPrototypeOf,E=x&&x(x(T([])));E&&E!==r&&n.call(E,a)&&(w=E);var S=b.prototype=g.prototype=Object.create(w);function O(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function j(t,e){function r(o,i,a,c){var u=f(t[o],t,i);if("throw"!==u.type){var s=u.arg,l=s.value;return l&&"object"==Ir(l)&&n.call(l,"__await")?e.resolve(l.__await).then((function(t){r("next",t,a,c)}),(function(t){r("throw",t,a,c)})):e.resolve(l).then((function(t){s.value=t,a(s)}),(function(t){return r("throw",t,a,c)}))}c(u.arg)}var i;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return i=i?i.then(o,o):o()}})}function _(e,r,n){var o=p;return function(i,a){if(o===d)throw Error("Generator is already running");if(o===y){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var c=n.delegate;if(c){var u=P(c,n);if(u){if(u===v)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===p)throw o=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=d;var s=f(e,r,n);if("normal"===s.type){if(o=n.done?y:h,s.arg===v)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o=y,n.method="throw",n.arg=s.arg)}}}function P(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,P(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=f(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,v;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function k(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function C(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function I(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(k,this),this.reset(!0)}function T(e){if(e||""===e){var r=e[a];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(Ir(e)+" is not iterable")}return m.prototype=b,o(S,"constructor",{value:b,configurable:!0}),o(b,"constructor",{value:m,configurable:!0}),m.displayName=s(b,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===m||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,s(t,u,"GeneratorFunction")),t.prototype=Object.create(S),t},e.awrap=function(t){return{__await:t}},O(j.prototype),s(j.prototype,c,(function(){return this})),e.AsyncIterator=j,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new j(l(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},O(S),s(S,u,"Generator"),s(S,a,(function(){return this})),s(S,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=T,I.prototype={constructor:I,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(C),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return c.type="throw",c.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],c=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var u=n.call(a,"catchLoc"),s=n.call(a,"finallyLoc");if(u&&s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),C(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;C(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:T(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),v}},e}function Ar(t,e,r,n,o,i,a){try{var c=t[i](a),u=c.value}catch(t){return void r(t)}c.done?e(u):Promise.resolve(u).then(n,o)}function Lr(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}const Rr=function(t,e){var r,o,i=(r=(0,n.useState)(!1),o=2,function(t){if(Array.isArray(t))return t}(r)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,s=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){s=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return c}}(r,o)||function(t,e){if(t){if("string"==typeof t)return Lr(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Lr(t,e):void 0}}(r,o)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),a=i[0],c=i[1];return e.url_params.components+=",googlepay",(0,n.useEffect)((function(){var r=function(){var r,n=(r=Tr().mark((function r(){return Tr().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,O(t,e);case 3:c(!0),r.next=9;break;case 6:r.prev=6,r.t0=r.catch(0),console.error("Error loading PayPal script: ".concat(r.t0));case 9:case"end":return r.stop()}}),r,null,[[0,6]])})),function(){var t=this,e=arguments;return new Promise((function(n,o){var i=r.apply(t,e);function a(t){Ar(i,n,o,a,c,"next",t)}function c(t){Ar(i,n,o,a,c,"throw",t)}a(void 0)}))});return function(){return n.apply(this,arguments)}}();r()}),[t,e]),a};function Nr(t){return Nr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Nr(t)}function Mr(){Mr=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,r){return t[e]=r}}function l(t,e,r,n){var i=e&&e.prototype instanceof g?e:g,a=Object.create(i.prototype),c=new I(n||[]);return o(a,"_invoke",{value:_(t,r,c)}),a}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=l;var p="suspendedStart",h="suspendedYield",d="executing",y="completed",v={};function g(){}function m(){}function b(){}var w={};s(w,a,(function(){return this}));var x=Object.getPrototypeOf,E=x&&x(x(T([])));E&&E!==r&&n.call(E,a)&&(w=E);var S=b.prototype=g.prototype=Object.create(w);function O(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function j(t,e){function r(o,i,a,c){var u=f(t[o],t,i);if("throw"!==u.type){var s=u.arg,l=s.value;return l&&"object"==Nr(l)&&n.call(l,"__await")?e.resolve(l.__await).then((function(t){r("next",t,a,c)}),(function(t){r("throw",t,a,c)})):e.resolve(l).then((function(t){s.value=t,a(s)}),(function(t){return r("throw",t,a,c)}))}c(u.arg)}var i;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return i=i?i.then(o,o):o()}})}function _(e,r,n){var o=p;return function(i,a){if(o===d)throw Error("Generator is already running");if(o===y){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var c=n.delegate;if(c){var u=P(c,n);if(u){if(u===v)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===p)throw o=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=d;var s=f(e,r,n);if("normal"===s.type){if(o=n.done?y:h,s.arg===v)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o=y,n.method="throw",n.arg=s.arg)}}}function P(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,P(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=f(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,v;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function k(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function C(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function I(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(k,this),this.reset(!0)}function T(e){if(e||""===e){var r=e[a];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(Nr(e)+" is not iterable")}return m.prototype=b,o(S,"constructor",{value:b,configurable:!0}),o(b,"constructor",{value:m,configurable:!0}),m.displayName=s(b,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===m||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,s(t,u,"GeneratorFunction")),t.prototype=Object.create(S),t},e.awrap=function(t){return{__await:t}},O(j.prototype),s(j.prototype,c,(function(){return this})),e.AsyncIterator=j,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new j(l(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},O(S),s(S,u,"Generator"),s(S,a,(function(){return this})),s(S,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=T,I.prototype={constructor:I,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(C),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return c.type="throw",c.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],c=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var u=n.call(a,"catchLoc"),s=n.call(a,"finallyLoc");if(u&&s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),C(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;C(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:T(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),v}},e}function Dr(t,e,r,n,o,i,a){try{var c=t[i](a),u=c.value}catch(t){return void r(t)}c.done?e(u):Promise.resolve(u).then(n,o)}function Fr(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}const Br=function(t,e,r){var i,a,c=(i=(0,n.useState)(!1),a=2,function(t){if(Array.isArray(t))return t}(i)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,s=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){s=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return c}}(i,a)||function(t,e){if(t){if("string"==typeof t)return Fr(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Fr(t,e):void 0}}(i,a)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),u=c[0],s=c[1];return(0,n.useEffect)((function(){if(t){var n=function(e){if(document!==t){var r=document.querySelector('script[src^="'.concat(e,'"]'));if(r){var n=t.createElement("script");n.src=r.src,n.async=r.async,n.type=r.type,t.head.appendChild(n)}else console.error("Script not found in the document:",e)}},i=function(){var t,i=(t=Mr().mark((function t(){return Mr().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r){t.next=2;break}return t.abrupt("return");case 2:if(e&&e.sdk_url){t.next=5;break}return console.error("Invalid buttonConfig or missing sdk_url"),t.abrupt("return");case 5:return t.prev=5,t.next=8,o({url:e.sdk_url}).then((function(){n(e.sdk_url)}));case 8:s(!0),t.next=14;break;case 11:t.prev=11,t.t0=t.catch(5),console.error("Failed to load Googlepay script:",t.t0);case 14:case"end":return t.stop()}}),t,null,[[5,11]])})),function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){Dr(i,n,o,a,c,"next",t)}function c(t){Dr(i,n,o,a,c,"throw",t)}a(void 0)}))});return function(){return i.apply(this,arguments)}}();i()}}),[t,e,r]),u};function Gr(t){return Gr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Gr(t)}function Hr(){Hr=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,r){return t[e]=r}}function l(t,e,r,n){var i=e&&e.prototype instanceof g?e:g,a=Object.create(i.prototype),c=new I(n||[]);return o(a,"_invoke",{value:_(t,r,c)}),a}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=l;var p="suspendedStart",h="suspendedYield",d="executing",y="completed",v={};function g(){}function m(){}function b(){}var w={};s(w,a,(function(){return this}));var x=Object.getPrototypeOf,E=x&&x(x(T([])));E&&E!==r&&n.call(E,a)&&(w=E);var S=b.prototype=g.prototype=Object.create(w);function O(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function j(t,e){function r(o,i,a,c){var u=f(t[o],t,i);if("throw"!==u.type){var s=u.arg,l=s.value;return l&&"object"==Gr(l)&&n.call(l,"__await")?e.resolve(l.__await).then((function(t){r("next",t,a,c)}),(function(t){r("throw",t,a,c)})):e.resolve(l).then((function(t){s.value=t,a(s)}),(function(t){return r("throw",t,a,c)}))}c(u.arg)}var i;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return i=i?i.then(o,o):o()}})}function _(e,r,n){var o=p;return function(i,a){if(o===d)throw Error("Generator is already running");if(o===y){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var c=n.delegate;if(c){var u=P(c,n);if(u){if(u===v)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===p)throw o=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=d;var s=f(e,r,n);if("normal"===s.type){if(o=n.done?y:h,s.arg===v)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o=y,n.method="throw",n.arg=s.arg)}}}function P(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,P(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=f(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,v;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function k(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function C(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function I(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(k,this),this.reset(!0)}function T(e){if(e||""===e){var r=e[a];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(Gr(e)+" is not iterable")}return m.prototype=b,o(S,"constructor",{value:b,configurable:!0}),o(b,"constructor",{value:m,configurable:!0}),m.displayName=s(b,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===m||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,s(t,u,"GeneratorFunction")),t.prototype=Object.create(S),t},e.awrap=function(t){return{__await:t}},O(j.prototype),s(j.prototype,c,(function(){return this})),e.AsyncIterator=j,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new j(l(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},O(S),s(S,u,"Generator"),s(S,a,(function(){return this})),s(S,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=T,I.prototype={constructor:I,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(C),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return c.type="throw",c.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],c=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var u=n.call(a,"catchLoc"),s=n.call(a,"finallyLoc");if(u&&s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),C(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;C(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:T(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),v}},e}function qr(t,e,r,n,o,i,a){try{var c=t[i](a),u=c.value}catch(t){return void r(t)}c.done?e(u):Promise.resolve(u).then(n,o)}function Ur(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}const Wr=function(t,e){var r,o,i=(r=(0,n.useState)(null),o=2,function(t){if(Array.isArray(t))return t}(r)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,s=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){s=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return c}}(r,o)||function(t,e){if(t){if("string"==typeof t)return Ur(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Ur(t,e):void 0}}(r,o)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),a=i[0],c=i[1];return(0,n.useEffect)((function(){var r=function(){var r,n=(r=Hr().mark((function r(){var n;return Hr().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:if(e){r.next=2;break}return r.abrupt("return");case 2:return r.prev=2,r.next=5,window[t].Googlepay().config();case 5:n=r.sent,c(n),r.next=12;break;case 9:r.prev=9,r.t0=r.catch(2),console.error("Failed to fetch Google Pay config:",r.t0);case 12:case"end":return r.stop()}}),r,null,[[2,9]])})),function(){var t=this,e=arguments;return new Promise((function(n,o){var i=r.apply(t,e);function a(t){qr(i,n,o,a,c,"next",t)}function c(t){qr(i,n,o,a,c,"throw",t)}a(void 0)}))});return function(){return n.apply(this,arguments)}}();r()}),[t,e]),a};function $r(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}const zr=function(t){var e,r,o=t.namespace,i=t.buttonConfig,a=t.ppcpConfig,c=t.buttonAttributes,u=(e=(0,n.useState)(null),r=2,function(t){if(Array.isArray(t))return t}(e)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,s=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){s=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return c}}(e,r)||function(t,e){if(t){if("string"==typeof t)return $r(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?$r(t,e):void 0}}(e,r)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),s=u[0],l=u[1],f=Rr(o,a),p=Br(s,i,f),h=Wr(o,p),d=Cr(s,o,i,a,h,c),y=d.button,v=d.containerStyles;return React.createElement(React.Fragment,null,React.createElement("div",{id:"express-payment-method-ppcp-googlepay",style:v,ref:function(t){if(t){for(l(t.ownerDocument);t.firstChild;)t.removeChild(t.firstChild);y&&t.appendChild(y)}}}),y&&React.createElement("style",null,".block-editor-iframe__html .gpay-card-info-animated-progress-bar-container {\n                        display: none !important\n                    }"))},Vr=function(t){var e=t.namespace,r=t.buttonConfig,n=t.ppcpConfig,o=t.buttonAttributes;return React.createElement(zr,{namespace:e,buttonConfig:r,ppcpConfig:n,buttonAttributes:o})};function Qr(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,s=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){s=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return c}}(t,e)||function(t,e){if(t){if("string"==typeof t)return Yr(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Yr(t,e):void 0}}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Yr(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var Jr=wc.wcSettings.getSetting("ppcp-gateway_data").scriptData,Kr=wc.wcSettings.getSetting("ppcp-googlepay_data"),Xr=Kr.scriptData,Zr="ppcpBlocksPaypalGooglepay";void 0===window.PayPalCommerceGateway&&(window.PayPalCommerceGateway=Jr);var tn=function(t){var e=t.isEditing,r=t.buttonAttributes,i=Qr((0,n.useState)(!1),2),a=i[0],c=i[1],u=Qr((0,n.useState)(!1),2),s=u[0],l=u[1],f=Qr((0,n.useState)(null),2),p=f[0],h=f[1];return(0,n.useEffect)((function(){e||(o({url:Xr.sdk_url}).then((function(){l(!0)})),Jr.url_params.components+=",googlepay",O(Zr,Jr).then((function(){c(!0)})).catch((function(t){console.error("Failed to load PayPal script: ",t)})))}),[e,Xr,Jr]),(0,n.useEffect)((function(){if(!e&&a&&s&&!p){var t=new Pr(Zr,Xr,Jr,r);h(t)}}),[a,s,e,p,r]),e?React.createElement(Vr,{namespace:Zr,buttonConfig:Xr,ppcpConfig:Jr,buttonAttributes:r}):React.createElement("div",{id:Xr.button.wrapper.replace("#",""),className:"ppcp-button-apm ppcp-button-googlepay"})};null!=Xr&&Xr.is_enabled&&(0,i.registerExpressPaymentMethod)({name:Kr.id,title:"PayPal - ".concat(Kr.title),description:(0,a.__)("Eligible users will see the PayPal button.","woocommerce-paypal-payments"),gatewayId:"ppcp-gateway",label:React.createElement("div",{dangerouslySetInnerHTML:{__html:Kr.title}}),content:React.createElement(tn,{isEditing:!1}),edit:React.createElement(tn,{isEditing:!0}),ariaLabel:Kr.title,canMakePayment:function(){return Kr.enabled},supports:{features:["products"],style:["height","borderRadius"]}})})();
//# sourceMappingURL=boot-block.js.map