{"version": 3, "file": "js/oxxo.js", "mappings": "AAAAA,SAASC,iBAAkB,oBAAoB,WAC9CC,OAAQ,iBAAkBC,GACzB,gCACA,SAAWC,EAAMC,GAChB,GAAKA,EAAKC,cAAsC,KAAtBD,EAAKC,aAAsB,CACpD,IAAMC,EAAQC,OAAOD,MAAQ,EACvBE,EAASD,OAAOC,OAAS,EACzBC,EAAOH,EAAQA,EAAQ,EACvBI,EAAMF,EAASA,EAAS,EAC9BG,OAAOC,KACNR,EAAKC,aACL,SACA,gBACCC,EACA,YACAE,EACA,SACAE,EACA,UACAD,EAEH,CACD,GAEF", "sources": ["webpack://ppcp-wc-gateway/./resources/js/oxxo.js"], "sourcesContent": ["document.addEventListener( 'DOMContentLoaded', function () {\n\tjQuery( 'form.checkout' ).on(\n\t\t'checkout_place_order_success',\n\t\tfunction ( type, data ) {\n\t\t\tif ( data.payer_action && data.payer_action !== '' ) {\n\t\t\t\tconst width = screen.width / 2;\n\t\t\t\tconst height = screen.height / 2;\n\t\t\t\tconst left = width - width / 2;\n\t\t\t\tconst top = height - height / 2;\n\t\t\t\twindow.open(\n\t\t\t\t\tdata.payer_action,\n\t\t\t\t\t'_blank',\n\t\t\t\t\t'popup, width=' +\n\t\t\t\t\t\twidth +\n\t\t\t\t\t\t', height=' +\n\t\t\t\t\t\theight +\n\t\t\t\t\t\t', top=' +\n\t\t\t\t\t\ttop +\n\t\t\t\t\t\t', left=' +\n\t\t\t\t\t\tleft\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\t);\n} );\n"], "names": ["document", "addEventListener", "j<PERSON><PERSON><PERSON>", "on", "type", "data", "payer_action", "width", "screen", "height", "left", "top", "window", "open"], "sourceRoot": ""}