# Authentication Flows

The settings UI offers two distinct authentication methods:

- OAuth
- Direct API

## OAuth

This is the usual authentication UI for most users. It opens a "PayPal popup" with a login mask.
The authentication flow consists of **three steps**:

- Generate a referral URL with a special token
- Translate a one-time OAuth secret into permanent API credentials
- Complete authentication by confirming the token from step 1

**Usage:**

1. Available on the first onboarding page (for sandbox login), or on the last page of the onboarding wizard.
2. Authentication is initiated by clicking a "Connect" button which opens a popup with a PayPal login mask
	- Sometimes the login opens in a new tab, mainly on Mac when using the browser in full-screen mode
3. After completing the login, the final page shows a "Return to your store" button; clicking that button closes the popup/tab and completes the authentication process

**More details on what happens:**

```mermaid
sequenceDiagram
	autonumber
    participant R as React API
	participant S as PHP Server
	
	R->>S: Request partner referral URL
	Note over S: Generate and store a one-time token
	create participant W as WooCommerce API
	S->>W: Request referral URL
	destroy W
	W->>S: Generate the full partner referral URL
	S->>R: Return referral URL
	create participant P as PayPal Popup
	R->>P: Open PayPal popup, which was generated by WooCommerce APi
	Note over P: Complete login inside Popup
	P->>R: Call JS function with OAuth ID and shared secret
	R->>S: Send OAuth data to REST endpoint
	create participant PP as PayPal API
	S->>PP: Request permanent credentials
	PP->>S: Translate one-time secret to permanent credentials
	destroy P
	P->>R: Redirect browser tab with unique token
	Note over R: App unmounts during redirect
	
	Note over S: During page load
	Note over S: Verify token and finalize authentication
	S->>PP: Request merchant details 
	destroy PP
	PP->>S: Return merchant details (e.g. country) 
	Note over S: Render the settings page with React app
	S->>R: Boot react app in "settings mode"
```

1. Authentication starts _before_ the "Connect" button is rendered, as we generate a one-time partner referral URL
	- See `ConnectionUrlGenerator::generate()`
	- This referral URL configures PayPal: Which items render inside the Popup? What is the "return
	  URL" for the final step? Is it a sandbox or live login?
2. _...The merchant completes the login or account creation flow inside the popup..._
3. During page-load of the final confirmation page inside the popup: PayPal directly calls a JS function on the WooCommerce settings page, i.e. the popup communicates with the open WooCommerce tab. This JS function sends an oauth ID and shared secret (OTP) to a REST endpoint
	- See `AuthenticatoinRestEndpoint::connect_oauth()`
	- See `AuthenticationManager::authenticate_via_oauth()` → translates the one-time shared secret
	  into a permanent client secret
	- At this stage, the authentication is _incomplete_, as some details are only provided by the
	  final step
4. When clicking the "Return to store" button, the popup closes and the WooCommerce settings page "reloads"; it's actually a _redirect_ which is initiated by PayPal and receives a unique token (which was generated by the `ConnectionUrlGenerator`) that is required to complete authentication.
	- See `ConnectionListener::process()`
	- See `AuthenticationManager::finish_oauth_authentication()`
	- This listener runs on every wp-admin page load and bails if the required token is not present
5. After the final page reload, the React app directly enters "Settings mode"

## Direct API

This method is only available for business accounts, as it requires the merchant to create a PayPal REST app that's linked to their account.

<details>
<summary><strong>Setup the PayPal REST app</strong></summary>

1. Visit https://developer.paypal.com/
2. In section "Apps & Credentials" click "Create App"
3. After the app is ready, it displays the `Client ID` and `Secret Key` values

</details>

**Usage:**

1. Available on the first onboarding screen, via the "See advanced options" form at the bottom of the page
2. Activate the "Manual Connection" toggle; then enter the `Client ID` and `Secret Key` and hit Enter

**What happens:**

```mermaid
sequenceDiagram
	participant R as React
	participant S as Server
	participant P as PayPal API
	
	R->>S: Send credentials to REST endpoint
	S->>P: Authenticate via Direct API
	P->>S: Return authentication result
	S->>P: Request merchant details
	P->>S: Return merchant details (e.g. country)

	Note over S: Process authentication result
	S->>R: Return authentication status
	Note over R: Update UI to authenticated state<br/>(no page reload)

```

1. Client ID and Secret are sent to a REST endpoint of the plugin. The authentication happens on server-side.
	- See `AuthenticatoinRestEndpoint::connect_direct()`
	- See `AuthenticationManager::authenticate_via_direct_api()`
2. After authentication is completed, the merchant account is prepared on server side and a confirmation is returned to the React app.
	- See `AuthenticationManager::update_connection_details()` → condition `is_merchant_connected()`
3. The React app directly switches to the "Settings mode" without a page reload.
