(()=>{var e={146:(e,t,n)=>{"use strict";var a=n(363),o={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},s={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},r={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},i={};function c(e){return a.isMemo(e)?r:i[e.$$typeof]||o}i[a.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},i[a.Memo]=r;var l=Object.defineProperty,p=Object.getOwnPropertyNames,u=Object.getOwnPropertySymbols,d=Object.getOwnPropertyDescriptor,m=Object.getPrototypeOf,h=Object.prototype;e.exports=function e(t,n,a){if("string"!=typeof n){if(h){var o=m(n);o&&o!==h&&e(t,o,a)}var r=p(n);u&&(r=r.concat(u(n)));for(var i=c(t),f=c(n),y=0;y<r.length;++y){var g=r[y];if(!(s[g]||a&&a[g]||f&&f[g]||i&&i[g])){var v=d(n,g);try{l(t,g,v)}catch(e){}}}}return t}},338:(e,t,n)=>{"use strict";var a=n(795);t.H=a.createRoot,a.hydrateRoot},799:(e,t)=>{"use strict";var n="function"==typeof Symbol&&Symbol.for,a=n?Symbol.for("react.element"):60103,o=n?Symbol.for("react.portal"):60106,s=n?Symbol.for("react.fragment"):60107,r=n?Symbol.for("react.strict_mode"):60108,i=n?Symbol.for("react.profiler"):60114,c=n?Symbol.for("react.provider"):60109,l=n?Symbol.for("react.context"):60110,p=n?Symbol.for("react.async_mode"):60111,u=n?Symbol.for("react.concurrent_mode"):60111,d=n?Symbol.for("react.forward_ref"):60112,m=n?Symbol.for("react.suspense"):60113,h=n?Symbol.for("react.suspense_list"):60120,f=n?Symbol.for("react.memo"):60115,y=n?Symbol.for("react.lazy"):60116,g=n?Symbol.for("react.block"):60121,v=n?Symbol.for("react.fundamental"):60117,b=n?Symbol.for("react.responder"):60118,w=n?Symbol.for("react.scope"):60119;function _(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case a:switch(e=e.type){case p:case u:case s:case i:case r:case m:return e;default:switch(e=e&&e.$$typeof){case l:case d:case y:case f:case c:return e;default:return t}}case o:return t}}}function S(e){return _(e)===u}t.AsyncMode=p,t.ConcurrentMode=u,t.ContextConsumer=l,t.ContextProvider=c,t.Element=a,t.ForwardRef=d,t.Fragment=s,t.Lazy=y,t.Memo=f,t.Portal=o,t.Profiler=i,t.StrictMode=r,t.Suspense=m,t.isAsyncMode=function(e){return S(e)||_(e)===p},t.isConcurrentMode=S,t.isContextConsumer=function(e){return _(e)===l},t.isContextProvider=function(e){return _(e)===c},t.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===a},t.isForwardRef=function(e){return _(e)===d},t.isFragment=function(e){return _(e)===s},t.isLazy=function(e){return _(e)===y},t.isMemo=function(e){return _(e)===f},t.isPortal=function(e){return _(e)===o},t.isProfiler=function(e){return _(e)===i},t.isStrictMode=function(e){return _(e)===r},t.isSuspense=function(e){return _(e)===m},t.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===s||e===u||e===i||e===r||e===m||e===h||"object"==typeof e&&null!==e&&(e.$$typeof===y||e.$$typeof===f||e.$$typeof===c||e.$$typeof===l||e.$$typeof===d||e.$$typeof===v||e.$$typeof===b||e.$$typeof===w||e.$$typeof===g)},t.typeOf=_},363:(e,t,n)=>{"use strict";e.exports=n(799)},795:e=>{"use strict";e.exports=window.ReactDOM},942:(e,t)=>{var n;!function(){"use strict";var a={}.hasOwnProperty;function o(){for(var e="",t=0;t<arguments.length;t++){var n=arguments[t];n&&(e=r(e,s(n)))}return e}function s(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return o.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var n in e)a.call(e,n)&&e[n]&&(t=r(t,n));return t}function r(e,t){return t?e?e+" "+t:e+t:e}e.exports?(o.default=o,e.exports=o):void 0===(n=function(){return o}.apply(t,[]))||(e.exports=n)}()}},t={};function n(a){var o=t[a];if(void 0!==o)return o.exports;var s=t[a]={exports:{}};return e[a](s,s.exports,n),s.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var a in t)n.o(t,a)&&!n.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{"use strict";var e={};n.r(e),n.d(e,{determineProductsAndCaps:()=>Ee,flags:()=>ke,persistentData:()=>Pe,transientData:()=>je});var t={};n.r(t),n.d(t,{hydrate:()=>Ne,persist:()=>Re,refresh:()=>Le,refreshGateways:()=>$e,reset:()=>Me,setIsReady:()=>Ae,setPersistent:()=>Ie,setTransient:()=>De,syncGateways:()=>Ve,updateGatewaysRefreshed:()=>Be,updateGatewaysSynced:()=>Fe});var a={};n.r(a),n.d(a,{OnboardingHooks:()=>et,useBusiness:()=>He,useConnectionButton:()=>Qe,useDetermineProducts:()=>Xe,useFlags:()=>Ke,useGatewayRefresh:()=>Je,useGatewaySync:()=>Ze,useManualConnectionForm:()=>ze,useNavigationState:()=>qe,useOptionalPaymentMethods:()=>Ge,useProducts:()=>We,useSteps:()=>Ye});var o={};n.r(o),n.d(o,{persistentData:()=>nt});var s={};n.r(s),n.d(s,{EVENTS:()=>ht,FUNNEL_ID:()=>mt,STEP_INFO:()=>ft,TRANSLATIONS:()=>yt,config:()=>gt});var r={};n.r(r),n.d(r,{STORE_NAME:()=>Q,hooks:()=>a,initStore:()=>bt,selectors:()=>e});var i={};n.r(i),n.d(i,{features:()=>Kt,getActivityList:()=>qt,merchant:()=>Xt,persistentData:()=>Gt,transientData:()=>Yt,webhooks:()=>Jt,wooSettings:()=>Zt});var c={};n.r(c),n.d(c,{hydrate:()=>en,reset:()=>Qt,resetMerchant:()=>pn,setActiveModal:()=>on,setIsReady:()=>an,setManualConnectionMode:()=>rn,setMerchant:()=>ln,setPersistent:()=>nn,setSandboxMode:()=>sn,setTransient:()=>tn,setWebhooks:()=>cn,startActivity:()=>un,stopActivity:()=>dn});var l={};n.r(l),n.d(l,{authenticateWithCredentials:()=>yn,authenticateWithOAuth:()=>gn,checkWebhookSimulationState:()=>Cn,disconnectMerchant:()=>vn,onboardingUrl:()=>fn,persist:()=>mn,refresh:()=>hn,refreshFeatureStatuses:()=>wn,refreshMerchantData:()=>bn,resubscribeWebhooks:()=>Sn,startWebhookSimulation:()=>xn});var p={};n.r(p),n.d(p,{useActiveModal:()=>Ln,useActivityObserver:()=>Bn,useAuthentication:()=>Mn,useBusyState:()=>Fn,useDisconnectMerchant:()=>Nn,useManualConnection:()=>On,useMerchant:()=>Rn,useMerchantInfo:()=>An,useProduction:()=>Tn,useSandbox:()=>En,useStore:()=>kn,useWebhooks:()=>In,useWooSettings:()=>Dn});var u={};n.r(u),n.d(u,{persistentData:()=>Vn});var d={};n.r(d),n.d(d,{STORE_NAME:()=>wt,hooks:()=>p,initStore:()=>$n,selectors:()=>i});var m={};n.r(m),n.d(m,{persistentData:()=>sa,transientData:()=>ra});var h={};n.r(h),n.d(h,{changePaymentSettings:()=>da,hydrate:()=>ca,persist:()=>ma,refresh:()=>ha,reset:()=>ia,setIsReady:()=>ua,setPersistent:()=>pa,setTransient:()=>la});var f={};n.r(f),n.d(f,{usePaymentMethods:()=>ga,usePaymentMethodsModal:()=>va,useStore:()=>ya});var y={};n.r(y),n.d(y,{persistentData:()=>ba});var g={};n.r(g),n.d(g,{STORE_NAME:()=>Un,hooks:()=>f,initStore:()=>Pa,selectors:()=>m});var v={};n.r(v),n.d(v,{getState:()=>Ba,persistentData:()=>Va,transientData:()=>$a});var b={};n.r(b),n.d(b,{hydrate:()=>za,persist:()=>Ya,refresh:()=>qa,reset:()=>Ua,setIsReady:()=>Ga,setPersistent:()=>Wa,setTransient:()=>Ha});var w={};n.r(w),n.d(w,{useSettings:()=>Za,useStore:()=>Ka});var _={};n.r(_),n.d(_,{persistentData:()=>Ja});var S={};n.r(S),n.d(S,{STORE_NAME:()=>ja,hooks:()=>w,initStore:()=>Qa,selectors:()=>v});var x={};n.r(x),n.d(x,{persistentData:()=>_o,transientData:()=>So});var C={};n.r(C),n.d(C,{hydrate:()=>Co,persist:()=>Eo,refresh:()=>Oo,reset:()=>xo,setIsReady:()=>ko,setPersistent:()=>jo,setTransient:()=>Po});var P={};n.r(P),n.d(P,{useColorProps:()=>Ro,useLabelProps:()=>Fo,useLayoutProps:()=>Bo,useLocationProps:()=>Io,usePaymentMethodProps:()=>Ao,useShapeProps:()=>Lo,useStore:()=>No,useStylingLocation:()=>Do,useTaglineProps:()=>Vo});var j={};n.r(j),n.d(j,{persistentData:()=>$o});var k={};n.r(k),n.d(k,{STORE_NAME:()=>eo,hooks:()=>P,initStore:()=>Uo,selectors:()=>x});var E={};n.r(E),n.d(E,{getCompletedTodos:()=>cs,getDismissedTodos:()=>is,getTodos:()=>rs,persistentData:()=>os,transientData:()=>ss});var O={};n.r(O),n.d(O,{completeOnClick:()=>ws,fetchTodos:()=>ys,persist:()=>gs,refresh:()=>vs,reset:()=>ls,resetDismissedTodos:()=>bs,setCompletedTodos:()=>fs,setDismissedTodos:()=>hs,setIsReady:()=>ds,setPersistent:()=>us,setTodos:()=>ms,setTransient:()=>ps});var T={};n.r(T),n.d(T,{useCompletedTodos:()=>js,useDismissedTodos:()=>Ps,useStore:()=>xs,useTodos:()=>Cs});var M={};n.r(M),n.d(M,{getTodos:()=>ks});var N={};n.r(N),n.d(N,{STORE_NAME:()=>zo,hooks:()=>T,initStore:()=>Es,selectors:()=>E});var D={};n.r(D),n.d(D,{persistentData:()=>zs,transientData:()=>Hs});var I={};n.r(I),n.d(I,{hydrate:()=>Gs,persist:()=>Ks,refresh:()=>Zs,reset:()=>Ws,setIsReady:()=>Xs,setPersistent:()=>qs,setTransient:()=>Ys});var A={};n.r(A),n.d(A,{usePayLaterMessaging:()=>er,useStore:()=>Qs});var R={};n.r(R),n.d(R,{persistentData:()=>tr});var L={};n.r(L),n.d(L,{STORE_NAME:()=>Os,hooks:()=>A,initStore:()=>nr,selectors:()=>D});var F={};n.r(F),n.d(F,{getFeatures:()=>vr,persistentData:()=>yr,transientData:()=>gr});var B={};n.r(B),n.d(B,{fetchFeatures:()=>xr,hydrate:()=>br,setFeatures:()=>Sr,setIsReady:()=>_r,setTransient:()=>wr});var V={};n.r(V),n.d(V,{useFeatures:()=>Cr});var $={};n.r($),n.d($,{getFeatures:()=>Pr});var U={};n.r(U),n.d(U,{STORE_NAME:()=>ar,hooks:()=>V,initStore:()=>jr,selectors:()=>F});var z={};n.r(z),n.d(z,{getAllFieldSources:()=>Ar,getFieldSource:()=>Dr,getStoreFieldSources:()=>Ir,isFieldTracked:()=>Rr});var H={};n.r(H),n.d(H,{clearSources:()=>Fr,reset:()=>Br,updateSources:()=>Lr});var W={};n.r(W),n.d(W,{STORE_NAME:()=>ne,initStore:()=>Vr,selectors:()=>z});const G=window.React;var Y=n.n(G),q=n(338);const X=window.wp.element;var K=n(942),Z=n.n(K);const J=window.wp.data,Q="wc/paypal/onboarding",ee="/wc/v3/wc_paypal/onboarding",te="/wc/v3/wc_paypal/onboarding",ne="wc/paypal/tracking",ae=(e,t,n={})=>({...e,...Object.keys(t).reduce(((e,a)=>(a in n?e[a]=t[a]:console.warn(`Ignoring unknown key "${a}" - to use it, add it to the initial store properties in the reducer.`),e)),{})}),oe=(e,t)=>[(t,n={})=>ae(t,n,e),(e,n={})=>({...e,data:ae(e.data,n,t)})],se=(e,t,n)=>{if(Object.hasOwnProperty.call(e,"data"))throw new Error('The transient state cannot contain a "data" property.');const a={...e,data:t};return function(e=a,t){var o;return Object.hasOwnProperty.call(n,t.type)?n[t.type](e,null!==(o=t.payload)&&void 0!==o?o:{},t):e}},re=e=>{const t=(t,n)=>a=>{const o=(0,J.useSelect)((n=>{const o=n(e);if(!o?.[t])throw new Error(`Please create the selector "${t}" for store "${e}"`);const s=o[t]();return void 0===s?.[a]&&console.error(`Warning: ${t}()[${a}] is undefined in store "${e}". This may indicate a bug.`),s?.[a]}),[a]),s=(0,J.useDispatch)(e),r=(0,J.useDispatch)(ne);return[o,(0,X.useCallback)(((t,o=null)=>{try{if(o&&r?.updateSources&&r.updateSources(e,a,o),!s?.[n])throw new Error(`Please create the action "${n}" for store "${e}"`);s[n](a,t)}catch(t){console.error(`Error updating ${a} in ${e}:`,t)}}),[s,a,r])]};return{useTransient:t("transientData","setTransient"),usePersistent:t("persistentData","setPersistent")}},ie="ppcp/onboarding/SET_TRANSIENT",ce="ppcp/onboarding/SET_PERSISTENT",le="ppcp/onboarding/RESET",pe="ppcp/onboarding/HYDRATE",ue="ppcp/onboarding/SYNC_GATEWAYS",de="ppcp/onboarding/REFRESH_GATEWAYS",me=Object.freeze({isReady:!1,manualClientId:"",manualClientSecret:"",connectionButtonClicked:!1,flags:Object.freeze({canUseCasualSelling:!1,canUseVaulting:!1,canUseCardPayments:!1,canUseSubscriptions:!1,shouldSkipPaymentMethods:!1,canUseFastlane:!1,canUsePayLater:!1})}),he=Object.freeze({completed:!1,step:0,isCasualSeller:null,areOptionalPaymentMethodsEnabled:null,products:[],gatewaysSynced:!1,gatewaysRefreshed:!1}),[fe,ye]=oe(me,he),ge=se(me,he,{[ie]:(e,t)=>fe(e,t),[ce]:(e,t)=>ye(e,t),[le]:e=>{const t=fe(ye(e,he),me);return t.flags={...e.flags},t.isReady=!0,t},[pe]:(e,t)=>{const n=ye(e,t.data);return t.flags&&(n.flags=Object.freeze({...n.flags,...t.flags})),n},[ue]:e=>ye(e,{gatewaysSynced:!0}),[de]:e=>ye(e,{gatewaysRefreshed:!0})}),ve="casual_seller",be="business",we={VIRTUAL:"virtual",PHYSICAL:"physical",SUBSCRIPTIONS:"subscriptions"},_e="EXPRESS_CHECKOUT",Se="ADVANCED_VAULTING",xe=Object.freeze({}),Ce=e=>e||xe,Pe=e=>Ce(e).data||xe,je=e=>{const{data:t,flags:n,...a}=Ce(e);return a||xe},ke=e=>Ce(e).flags||xe,Ee=(e,t,n)=>{const a=[],o={useSubscriptions:!1,useCardPayments:!1},{isCasualSeller:s,areOptionalPaymentMethodsEnabled:r,products:i}=Pe(e),{canUseVaulting:c,canUseCardPayments:l}=ke(e),p=l&&r&&!(s&&t);return p?s||"MX"===n?a.push(_e):(a.push("PPCP"),i?.includes(we.SUBSCRIPTIONS)&&(o.useSubscriptions=!0),c&&a.push(Se)):(a.push(_e),i?.includes(we.SUBSCRIPTIONS)&&(o.useSubscriptions=!0),c&&a.push(Se)),o.useCardPayments=p,{products:a,options:o}},Oe=window.wp.apiFetch;var Te=n.n(Oe);const Me=()=>({type:le}),Ne=e=>({type:pe,payload:e}),De=(e,t)=>({type:ie,payload:{[e]:t}}),Ie=(e,t)=>({type:ce,payload:{[e]:t}}),Ae=e=>De("isReady",e);function Re(){return async({select:e})=>{try{await Te()({path:te,method:"POST",data:e.persistentData()})}catch(e){console.error("Error saving progress.",e)}}}function Le(){return({dispatch:e,select:t})=>{e.invalidateResolutionForStore(),t.persistentData()}}const Fe=(e=!0)=>Ie("gatewaysSynced",e),Be=(e=!0)=>Ie("gatewaysRefreshed",e);function Ve(){return async({dispatch:e})=>(e(Ie("gatewaysSynced",!0)),await e.persist(),{success:!0})}function $e(){return async({dispatch:e})=>(e(Ie("gatewaysRefreshed",!0)),await e.persist(),{success:!0})}const Ue=()=>{const{useTransient:e,usePersistent:t}=re(Q),n=(0,J.useDispatch)(Q),a=(0,J.useSelect)((e=>e(Q).flags()),[]),[o]=e("isReady"),[s,r]=e("manualClientId"),[i,c]=e("manualClientSecret"),[l,p]=e("connectionButtonClicked"),[u,d]=t("step"),[m,h]=t("completed"),[f,y]=t("isCasualSeller"),[g,v]=t("areOptionalPaymentMethodsEnabled"),[b,w]=t("products"),[_,S]=t("gatewaysSynced"),[x,C]=t("gatewaysRefreshed"),P=async(e,t,a)=>{e(t,a),await n.persist()};return{flags:a,isReady:o,step:u,setStep:(e,t)=>P(d,e,t),completed:m,setCompleted:(e,t)=>P(h,e,t),isCasualSeller:f,setIsCasualSeller:(e,t)=>P(y,e,t),manualClientId:s,setManualClientId:e=>P(r,e),manualClientSecret:i,setManualClientSecret:e=>P(c,e),optionalMethods:g,setOptionalMethods:(e,t)=>P(v,e,t),products:b,setProducts:(e,t)=>{const n=e.filter((e=>Object.values(we).includes(e)));return P(w,n,t)},gatewaysSynced:_,setGatewaysSynced:e=>P(S,e,void 0),syncGateways:async()=>await n.syncGateways(void 0),gatewaysRefreshed:x,setGatewaysRefreshed:e=>P(C,e,void 0),refreshGateways:async()=>await n.refreshGateways(void 0),connectionButtonClicked:l,setConnectionButtonClicked:e=>((e,t)=>{e(t,"user")})(p,e)}},ze=()=>{const{manualClientId:e,setManualClientId:t,manualClientSecret:n,setManualClientSecret:a}=Ue();return{manualClientId:e,setManualClientId:t,manualClientSecret:n,setManualClientSecret:a}},He=()=>{const{isCasualSeller:e,setIsCasualSeller:t}=Ue();return{isCasualSeller:e,setIsCasualSeller:t}},We=()=>{const{products:e,setProducts:t}=Ue();return{products:e,setProducts:t}},Ge=()=>{const{optionalMethods:e,setOptionalMethods:t}=Ue();return{optionalMethods:e,setOptionalMethods:t}},Ye=()=>{const{flags:e,isReady:t,step:n,setStep:a,completed:o,setCompleted:s}=Ue();return{flags:e,isReady:t,step:n,setStep:a,completed:o,setCompleted:s}},qe=()=>({products:We(),business:He(),methods:Ge()}),Xe=(e,t)=>(0,J.useSelect)((n=>n(Q).determineProductsAndCaps(e,t)),[e,t]),Ke=()=>{const{flags:e}=Ue();return e},Ze=()=>{const{gatewaysSynced:e,syncGateways:t}=Ue();return{gatewaysSynced:e,syncGateways:t}},Je=()=>{const{gatewaysRefreshed:e,refreshGateways:t}=Ue();return{gatewaysRefreshed:e,refreshGateways:t}},Qe=()=>{const{connectionButtonClicked:e,setConnectionButtonClicked:t}=Ue();return{connectionButtonClicked:e,setConnectionButtonClicked:t}},et={useManualConnectionForm:ze,useBusiness:He,useProducts:We,useOptionalPaymentMethods:Ge,useSteps:Ye,useNavigationState:qe,useDetermineProducts:Xe,useFlags:Ke,useGatewaySync:Ze,useGatewayRefresh:Je,useConnectionButton:Qe},tt=window.wp.i18n;function nt(){return async({dispatch:e,registry:t})=>{try{const t=await Te()({path:ee});await e.hydrate(t,"system"),await e.setIsReady(!0,"system")}catch(e){await t.dispatch("core/notices").createErrorNotice((0,tt.__)("Error retrieving onboarding details.","woocommerce-paypal-payments"))}}}class at{constructor(e="ppcp_onboarding",t={}){this.eventPrefix=e,this.debug=t.debugMode||!1,this.isAvailable=this.checkAvailability(),this.pendingEvents=[],this.setupAvailabilityCheck()}getTrackingFunction(){var e,t;return null!==(e=null!==(t=window.wc?.tracks?.recordEvent)&&void 0!==t?t:window.wcTracks?.recordEvent)&&void 0!==e?e:null}checkAvailability(){const e=this.getTrackingFunction(),t=!("undefined"==typeof window||!e||"function"!=typeof e);return t&&this.debug&&(window.wc?.tracks?.recordEvent?console.log("[WC Tracks] Using wc.tracks.recordEvent (real system)"):window.wcTracks?.recordEvent&&console.log("[WC Tracks] Using wcTracks.recordEvent (fallback)")),t}setupAvailabilityCheck(){if(!this.isAvailable){const e=setInterval((()=>{this.checkAvailability()&&(this.isAvailable=!0,this.processPendingEvents(),clearInterval(e))}),1e3);setTimeout((()=>clearInterval(e)),5e3)}}debugLog(...e){this.debug&&console.log(...e)}buildEventName(e){if(e.startsWith(this.eventPrefix+"_"))return this.debugLog("[WC Tracks] Event already prefixed:",e),e;const t=`${this.eventPrefix}_${e}`;return this.debugLog("[WC Tracks] Adding prefix:",e,"→",t),t}track(e,t={}){if(!this.isAvailable)return this.pendingEvents.push({eventName:e,properties:t,timestamp:Date.now()}),this.debugLog("[WC Tracks] Not available, queuing:",e),!1;const n=this.buildEventName(e);if(!this.isValidEventName(n))return console.error("[WC Tracks] Invalid event name:",n),!1;try{const e=this.getTrackingFunction();if(!e)return console.error("[WC Tracks] No tracking function available"),!1;const a=this.sanitizeProperties(t);return e(n,a),this.debugLog("[WC Tracks] Event sent:",n,a),!0}catch(e){return console.error("[WC Tracks] Error sending event:",e),!1}}processPendingEvents(){0!==this.pendingEvents.length&&(this.debugLog(`[WC Tracks] Processing ${this.pendingEvents.length} queued events`),this.pendingEvents.forEach((({eventName:e,properties:t})=>{this.track(e,t)})),this.pendingEvents=[])}isValidEventName(e){return/^[a-z_][a-z0-9_]*$/.test(e)}sanitizeProperties(e){const t={};return Object.entries(e).forEach((([e,n])=>{const a=e.toLowerCase().replace(/[^a-z0-9_]/g,"_");if(!a.startsWith("_")||e.startsWith("_"))if(null==n)t[a]="null";else if("boolean"==typeof n)t[a]=n;else if("number"==typeof n)t[a]=n;else if(Array.isArray(n))t[a]=n.join(",");else if("object"==typeof n){const e=JSON.stringify(n);t[a]=e.length>200?e.substring(0,200)+"...":e}else{const e=String(n);t[a]=e.length>255?e.substring(0,255)+"...":e}})),t}getInfo(){const e=this.getTrackingFunction(),t=!!window.wc?.tracks?.recordEvent;return{name:"WooCommerce Tracks",available:this.isAvailable,eventPrefix:this.eventPrefix,pendingEvents:this.pendingEvents.length,debug:this.debug,usingRealSystem:t,trackingFunction:e?"available":"not available"}}setDebugMode(e){"boolean"==typeof e?this.debug=e:e&&"object"==typeof e&&(this.debug=!!e.debugMode)}}class ot{constructor(e={}){this.enabled=!1!==e.enabled,this.prefix=e.prefix||"[Track]"}track(e,t={}){return!!this.enabled&&(Object.keys(t).length>0?console.log(`${this.prefix} ${e}`,t):console.log(`${this.prefix} ${e}`),!0)}setEnabled(e){this.enabled=e}}class st{constructor(e,t={}){this.funnelConfig=e,this.adapters=[],this.sessionStartTime=Date.now(),this.sessionId=this.generateSessionId(),this.eventCount=0,this.debugMode=t.debugMode||!1,this.events=e.events||{},this.translations=e.translations||{},this.stepInfo=e.stepInfo||{},this.ignoredSources=new Set(["subscription","unknown",void 0,""]),(e.funnelId?.includes("ppcp")||e.funnelId?.includes("paypal"))&&(this.debugMode=!0),"undefined"!=typeof window&&(window.funnelTrackingService=this)}generateSessionId(){return`${this.funnelConfig.eventPrefix||"tracking"}_${Date.now()}_${Math.random().toString(36).slice(2,11)}`}addAdapter(e){this.adapters.push(e)}clearAdapters(){this.adapters=[]}getAdapters(){return this.adapters.map((e=>e.getInfo?.()||e))}processStateChange(e){const{field:t,oldValue:n,newValue:a,metadata:o,action:s}=e;if(n===a)return;let r,i;if("string"==typeof s)r=o?.source||"",i=s;else{if(!s||"object"!=typeof s||!s.type)return;r=s.source||"unknown",i=s.type}const c=t||(s?.payload?Object.keys(s.payload)[0]:"unknown");this.shouldTrackFieldSource(c,r)&&this.processTrackedChange(c,n,a,{...o,source:r,actionType:i})}shouldTrackFieldSource(e,t){if(this.ignoredSources.has(t))return!1;const n=this.findFieldRules(e);return!n||n.allowedSources.includes(t)}findFieldRules(e){for(const t in this.funnelConfig.fieldConfigs){const n=this.funnelConfig.fieldConfigs[t].find((t=>t.fieldName===e));if(n&&n.rules)return n.rules}return null}processTrackedChange(e,t,n,a){const o=this.translations[e];if(o&&"function"==typeof o)try{o(t,n,a,this)}catch(t){console.error(`[Funnel Tracking] Error in translation for ${e}:`,t)}else this.genericFieldTracking(e,t,n,a)}genericFieldTracking(e,t,n,a){const o=`${e}_change`,s={field_name:e,old_value:t,new_value:n,source:a.source,...this.getCommonProperties(a)};this.sendToAdapters(o,s)}getCommonProperties(e={}){return{}}sendToAdapters(e,t){this.eventCount++,this.adapters.forEach(((n,a)=>{try{n.track(e,t)}catch(e){console.error(`[Funnel Tracking] Adapter ${a} error:`,e,n.getInfo?.()||"unknown adapter")}}))}getStats(){return{sessionId:this.sessionId,sessionStartTime:this.sessionStartTime,sessionDuration:Date.now()-this.sessionStartTime,eventCount:this.eventCount,adaptersCount:this.adapters.length,debugMode:this.debugMode,funnelId:this.funnelConfig.funnelId,eventsAvailable:Object.keys(this.events).length,translationsAvailable:Object.keys(this.translations).length,fieldConfigStores:Object.keys(this.funnelConfig.fieldConfigs||{}),totalFieldConfigs:Object.values(this.funnelConfig.fieldConfigs||{}).reduce(((e,t)=>e+t.length),0)}}testFieldSourceTracking(e,t){return{field:e,source:t,shouldTrack:this.shouldTrackFieldSource(e,t),fieldRules:this.findFieldRules(e),ignoredSources:Array.from(this.ignoredSources)}}}function rt(e,t,n){try{if("function"==typeof n.selector)return n.selector(e,t);const a=e(t);if(!a)return;const o=n.type||"persistent";let s;if("persistent"===o)s=a.persistentData?.();else{if("transient"!==o)return void console.warn(`[FIELD VALUE] Unknown data type: ${o}`);s=a.transientData?.()}if(!s||"object"!=typeof s)return;const r=n.fieldName.split(".");let i=s;for(const e of r){if(null==i)return;i=i[e]}return i}catch(e){return void console.error(`[FIELD VALUE] Error getting value for ${n.fieldName}:`,e)}}const it=new class{constructor(){this.storeSubscriptions={},this.storeRegistrations={},this.debugMode=!1}registerFunnelForStore(e,t,n,a,o,s,r=null,i={}){this.storeRegistrations[e]||(this.storeRegistrations[e]=[]);const c=this.storeRegistrations[e].findIndex((e=>e.funnelId===t)),l={funnelId:t,trackingService:n,fieldRules:a,fieldConfigs:o,debugMode:s,trackingCondition:r,stepInfo:i,isActive:!1,previousValues:{},hasTrackedPageLoad:!1,initializationAttempts:0,lastConditionResult:null,conditionCheckCount:0};return c>=0?this.storeRegistrations[e][c]=l:this.storeRegistrations[e].push(l),s&&(this.debugMode=!0),this.ensureStoreSubscription(e),this.debugMode&&console.log(`[SubscriptionManager] Registered funnel ${t} for store ${e}. Total funnels for this store: ${this.storeRegistrations[e].length}`),l}ensureStoreSubscription(e){if(this.storeSubscriptions[e])return;const t=wp.data.subscribe((()=>{this.handleStoreChange(e)}));this.storeSubscriptions[e]={unsubscribe:t,isActive:!0},this.debugMode&&console.log(`[SubscriptionManager] Created unified subscription for store ${e}`)}handleStoreChange(e){try{const t=wp.data.select,n=t(e);if(!n)return;(this.storeRegistrations[e]||[]).forEach((a=>{try{this.processFunnelForStore(e,a,t,n)}catch(t){console.error(`[SubscriptionManager] Error processing funnel ${a.funnelId} for store ${e}:`,t)}}))}catch(t){console.error(`[SubscriptionManager] Error handling store change for ${e}:`,t)}}processFunnelForStore(e,t,n,a){const{trackingService:o,fieldRules:s,fieldConfigs:r,trackingCondition:i}=t,c=this.evaluateTrackingCondition(n,i,t);if(this.handleConditionChange(t,c)){if(!t.isActive){if(t.initializationAttempts++,!this.isStoreReadyForTracking(a,t))return;t.isActive=!0,this.initializePreviousValues(n,e,r,t.previousValues),!t.hasTrackedPageLoad&&this.shouldTrackPageLoad(e)&&c&&(this.trackInitialPageLoad(n,e,o,t),t.hasTrackedPageLoad=!0)}this.processFieldChangesForFunnel(n,a,e,t,r,s,o)}}processFieldChangesForFunnel(e,t,n,a,o,s,r){o.forEach((t=>{try{const o=rt(e,n,t),s=a.previousValues[t.fieldName];if(o===s)return;const i=e("wc/paypal/tracking"),c=i?.getFieldSource?.(n,t.fieldName)?.source||"";if(!r.shouldTrackFieldSource(t.fieldName,c))return void(a.previousValues[t.fieldName]=o);this.processTrackedChangeForFunnel(t,s,o,c,r,e,n,a),a.previousValues[t.fieldName]=o}catch(e){console.error(`[SubscriptionManager] Error processing field ${t.fieldName} for funnel ${a.funnelId}:`,e)}}))}evaluateTrackingCondition(e,t,n){if(!t)return!0;n.conditionCheckCount++;try{const a=e(t.store);if(!a)return!1;const o=a.transientData?.()?.isReady;if(!o)return!1;const s=a[t.selector];if("function"!=typeof s)return!1;const r=s();if(!r||"object"!=typeof r)return!1;let i;return i=t.field?r[t.field]===t.expectedValue:!!r==!!t.expectedValue,n.lastConditionResult=i,i}catch(e){return!1}}handleConditionChange(e,t){return!t&&e.isActive?(this.resetFunnelState(e),!1):(t&&!e.isActive&&this.resetFunnelState(e),t)}resetFunnelState(e){e.isActive=!1,e.hasTrackedPageLoad=!1,e.initializationAttempts=0,e.previousValues={}}isStoreReadyForTracking(e,t){const n=e.transientData?.()?.isReady;return!!n||t.initializationAttempts>50}initializePreviousValues(e,t,n,a){n.forEach((n=>{try{const o=rt(e,t,n);a[n.fieldName]=o}catch(e){console.error(`[SubscriptionManager] Error initializing ${n.fieldName}:`,e)}}))}trackInitialPageLoad(e,t,n,a){try{const o=e(t).persistentData?.(),s=o?.step;if("number"==typeof s){const t=this.createFunnelMetadata(e,a);n.processStateChange({field:"step",oldValue:null,newValue:s,action:{type:"PAGE_LOAD",payload:{step:s},source:"system"},metadata:t})}}catch(e){console.error(`[SubscriptionManager] Error tracking page load for ${t}:`,e)}}shouldTrackPageLoad(e){return e.includes("onboarding")||e.includes("wizard")}processTrackedChangeForFunnel(e,t,n,a,o,s,r,i){const c=this.createFunnelMetadata(s,i),l={type:"transient"===e.type?"SET_TRANSIENT":"SET_PERSISTENT",payload:{[e.fieldName]:n},source:a};o.processStateChange({field:e.fieldName,oldValue:t,newValue:n,action:l,metadata:{...c,detectedSource:a}})}createFunnelMetadata(e,t){try{const n={action:"SUBSCRIBER_CHANGE",timestamp:Date.now(),funnelId:t.funnelId};return this.getFunnelStores(t.funnelId).forEach((t=>{try{const a=e(t);if(!a)return;const o=this.safeStoreCall(a,"flags",{}),s=this.safeStoreCall(a,"persistentData",{}),r=this.safeStoreCall(a,"transientData",{}),i=t.replace("wc/paypal/","");n[`${i}_flags`]=o,n[`${i}_isReady`]=r.isReady,Object.assign(n,s,r),n.contributingStores||(n.contributingStores=[]),n.contributingStores.push(t)}catch(e){console.warn(`[SubscriptionManager] Error getting metadata from store ${t}:`,e)}})),this.enhanceMetadataWithStepInfo(n,t),n}catch(e){return console.error(`[SubscriptionManager] Error creating funnel metadata for ${t.funnelId}:`,e),{error:"funnel_metadata_creation_failed",errorMessage:e.message,timestamp:Date.now(),funnelId:t.funnelId}}}enhanceMetadataWithStepInfo(e,t){try{const n=e.step,a=t.stepInfo||{};if("number"==typeof n&&a[n]){const t=a[n];e.stepName="string"==typeof t?t:t.name}e.currentStep=n,null==n&&(e.step=null,e.currentStep=null)}catch(e){console.warn("[SubscriptionManager] Error enhancing metadata with step info:",e)}}getFunnelStores(e){const t=[];return Object.entries(this.storeRegistrations).forEach((([n,a])=>{a.some((t=>t.funnelId===e))&&t.push(n)})),t}safeStoreCall(e,t,n=null){try{if("function"==typeof e[t]){const a=e[t]();return void 0!==a?a:n}return n}catch(e){return n}}unregisterFunnelForStore(e,t){const n=this.storeRegistrations[e];if(!n)return;const a=n.findIndex((e=>e.funnelId===t));a>=0&&(n.splice(a,1),this.debugMode&&console.log(`[SubscriptionManager] Unregistered funnel ${t} from store ${e}. Remaining funnels: ${n.length}`),0===n.length&&this.cleanupStoreSubscription(e))}cleanupStoreSubscription(e){const t=this.storeSubscriptions[e];t&&(t.unsubscribe(),delete this.storeSubscriptions[e],delete this.storeRegistrations[e],this.debugMode&&console.log(`[SubscriptionManager] Cleaned up subscription for store ${e}`))}getStatus(){const e={storesTracked:Object.keys(this.storeSubscriptions).length,activeSubscriptions:Object.keys(this.storeSubscriptions).filter((e=>this.storeSubscriptions[e].isActive)).length,totalFunnelRegistrations:0,storeDetails:{}};return Object.entries(this.storeRegistrations).forEach((([t,n])=>{e.totalFunnelRegistrations+=n.length,e.storeDetails[t]={funnelCount:n.length,funnels:n.map((e=>({funnelId:e.funnelId,isActive:e.isActive,conditionMet:e.lastConditionResult,conditionChecks:e.conditionCheckCount})))}})),e}},ct={funnels:{},storeToFunnel:{},instances:{}};function lt(e,t){return ct.funnels[t]?(ct.funnels[t].stores.includes(e)||ct.funnels[t].stores.push(e),ct.storeToFunnel[e]||(ct.storeToFunnel[e]=[]),ct.storeToFunnel[e].includes(t)||ct.storeToFunnel[e].push(t),!0):(console.error(`[REGISTRY] Funnel ${t} does not exist`),!1)}const pt=(e,t="persistent",n={})=>({fieldName:e,type:t,selector:n.selector||((n,a)=>{const o="persistent"===t?n(a).persistentData():n(a).transientData();return o?.[e]}),...n.rules&&{rules:n.rules},...n}),ut=(e,t="persistent",n="enabled",a="disabled")=>pt(e,t,{transform:e=>{let t;return t=!0===e?n:!1===e?a:"not_selected",{selected_value:t}}});class dt{constructor(e){this.funnelId=e,this.config={debug:!1,adapters:["console"],eventPrefix:e,events:{},translations:{},stepInfo:{},fieldConfigs:{}}}setDebug(e=!0){return this.config.debug=e,this}setAdapters(e){return this.config.adapters=e,this}setEventPrefix(e){return this.config.eventPrefix=e,this}addEvents(e){return this.config.events={...this.config.events,...e},this}addTranslations(e){return this.config.translations={...this.config.translations,...e},this}addStepInfo(e){return this.config.stepInfo={...this.config.stepInfo,...e},this}setTrackingCondition(e){return this.config.trackingCondition=e,this}addStore(e,t){return this.config.fieldConfigs[e]=t,this}mergeConfig(e){return this.config={...this.config,...e},this}build(){return this.config}static createBasicFunnel(e,t={}){const n=new dt(e).setDebug(t.debug||!1).setAdapters(t.adapters||["console"]);return t.eventPrefix&&n.setEventPrefix(t.eventPrefix),t.trackingCondition&&n.setTrackingCondition(t.trackingCondition),n}}const mt="ppcp_onboarding",ht={welcome_view:"ppcp_onboarding_welcome_view",account_type_view:"ppcp_onboarding_account_type_view",products_view:"ppcp_onboarding_products_view",payment_options_view:"ppcp_onboarding_payment_options_view",complete_view:"ppcp_onboarding_complete_view",account_type_select:"ppcp_onboarding_account_type_business_type_select",products_select:"ppcp_onboarding_products_products_select",payment_options_select:"ppcp_onboarding_payment_options_payment_method_select",sandbox_mode_select:"ppcp_onboarding_sandbox_mode_select",manual_connection_select:"ppcp_onboarding_manual_connection_select",complete_connect_click:"ppcp_onboarding_complete_connect_click"},ft={0:{name:"welcome",viewEvent:ht.welcome_view},1:{name:"account_type",viewEvent:ht.account_type_view},2:{name:"products",viewEvent:ht.products_view},3:{name:"payment_options",viewEvent:ht.payment_options_view},4:{name:"complete",viewEvent:ht.complete_view}},yt={step:(e,t,n,a)=>{const o=ft[t];if(!o)return;const s={step_number:t,step_name:o.name,...a.getCommonProperties(n)};a.sendToAdapters(o.viewEvent,s)},isCasualSeller:(e,t,n,a)=>{if(null===t)return;const o={selected_value:!0===t?"personal":"business",step_number:n.currentStep,step_name:n.stepName,...a.getCommonProperties(n)};a.sendToAdapters(ht.account_type_select,o)},products:(e,t,n,a)=>{if(!Array.isArray(t))return;const o={selected_products:t.join(","),products_count:t.length,previous_products:Array.isArray(e)?e.join(","):"none",step_number:n.currentStep,step_name:n.stepName,...a.getCommonProperties(n)};a.sendToAdapters(ht.products_select,o)},areOptionalPaymentMethodsEnabled:(e,t,n,a)=>{if(null===t)return;const o={selected_value:t?"expanded":"no_cards",step_number:n.currentStep,step_name:n.stepName,...a.getCommonProperties(n)};a.sendToAdapters(ht.payment_options_select,o)},completed:(e,t,n,a)=>{if(!0===t){const e={step_number:n.currentStep,step_name:n.stepName,total_duration_ms:Date.now()-a.sessionStartTime,final_account_type:n?.isCasualSeller?"personal":"business",final_products:Array.isArray(n?.products)?n.products.join(","):"",final_payment_options:n?.areOptionalPaymentMethodsEnabled?"expanded":"no_cards",final_sandbox_mode:n?.useSandbox?"enabled":"disabled",...a.getCommonProperties(n)};a.sendToAdapters(ht.complete_connect_click,e)}},connectionButtonClicked:(e,t,n,a)=>{if(!0===t&&!1===e){const e={step_number:n.currentStep,step_name:n.stepName,...a.getCommonProperties(n)};a.sendToAdapters(ht.complete_connect_click,e)}},useSandbox:(e,t,n,a)=>{if(null===t)return;const o={selected_value:!0===t?"enabled":"disabled",...a.getCommonProperties(n)};a.sendToAdapters(ht.sandbox_mode_select,o)},useManualConnection:(e,t,n,a)=>{if(null===t)return;const o={selected_value:!0===t?"enabled":"disabled",...a.getCommonProperties(n)};a.sendToAdapters(ht.manual_connection_select,o)}},gt=dt.createBasicFunnel(mt,{debug:!1,adapters:["woocommerce-tracks"],eventPrefix:"ppcp_onboarding",trackingCondition:{store:"wc/paypal/common",selector:"merchant",field:"isConnected",expectedValue:!1}}).addEvents(ht).addTranslations(yt).addStepInfo(ft).addStore("wc/paypal/onboarding",[((e,t="persistent",n={})=>pt("step",t,{...n,rules:{allowedSources:["user","system"],...n.rules}}))(0,"persistent",{transform:e=>({step_number:e,step_name:ft[e]?.name||`step_${e}`})}),pt("isCasualSeller","persistent",{transform:e=>({selected_value:!0===e?"personal":"business"}),rules:{allowedSources:["user"]}}),((e,t="persistent",n={})=>pt("products",t,{...n,transform:e=>({selected_items:Array.isArray(e)?e.join(","):"none",items_count:Array.isArray(e)?e.length:0,...n.transform?n.transform(e):{}})}))(0,"persistent",{rules:{allowedSources:["user"]}}),pt("areOptionalPaymentMethodsEnabled","persistent",{transform:e=>({selected_value:!0===e?"expanded":"no_cards"}),rules:{allowedSources:["user"]}}),pt("completed","persistent",{transform:e=>({completed:!0===e}),rules:{allowedSources:["system"]}}),((e,t={})=>pt("connectionButtonClicked","transient",t))()]).addStore("wc/paypal/common",[ut("useSandbox","persistent","enabled","disabled"),ut("useManualConnection","persistent","enabled","disabled")]).build();let vt=!1;vt||(function(e,t){const n={debug:!1,adapters:["console"],eventPrefix:"ppcp_general",fieldConfigs:{},events:{},translations:{},stepInfo:{},trackingCondition:null,...t,funnelId:e};if(n.trackingCondition){const t=function(e){if(!e)return{valid:!0,message:"No condition specified"};const t=[];return e.store||t.push("Missing required field: store"),e.selector||t.push("Missing required field: selector"),{valid:0===t.length,errors:t,condition:e}}(n.trackingCondition);t.valid||console.error(`[REGISTRY] Invalid tracking condition for funnel ${e}:`,t.errors)}ct.funnels[e]={funnelId:e,config:n,stores:[],isInitialized:!1}}(mt,gt),vt=!0);const bt=()=>{const n=(0,J.createReduxStore)(Q,{reducer:ge,actions:t,selectors:e,resolvers:o});return(0,J.register)(n),lt(Q,mt),Boolean(wp.data.select(Q))},wt="wc/paypal/common",_t="/wc/v3/wc_paypal/common",St="/wc/v3/wc_paypal/common/merchant",xt="/wc/v3/wc_paypal/common",Ct="/wc/v3/wc_paypal/authenticate/direct",Pt="/wc/v3/wc_paypal/authenticate/oauth",jt="/wc/v3/wc_paypal/authenticate/disconnect",kt="/wc/v3/wc_paypal/login_link",Et="/wc/v3/wc_paypal/webhooks",Ot="/wc/v3/wc_paypal/webhooks/simulate",Tt="/wc/v3/wc_paypal/refresh-features",Mt="ppcp/common/SET_TRANSIENT",Nt="ppcp/common/SET_PERSISTENT",Dt="ppcp/common/RESET",It="ppcp/common/HYDRATE",At="ppcp/common/SET_MERCHANT",Rt="ppcp/common/RESET_MERCHANT",Lt="ppcp/common/START_ACTIVITY",Ft="ppcp/common/STOP_ACTIVITY",Bt=Object.freeze({isReady:!1,activities:new Map,activeModal:"",activeHighlight:"",merchant:Object.freeze({isConnected:!1,isSandbox:!1,id:"",email:"",clientId:"",clientSecret:"",sellerType:"unknown"}),wooSettings:Object.freeze({storeCountry:"",storeCurrency:"",ownBrandOnly:!1}),features:Object.freeze({save_paypal_and_venmo:{enabled:!1},advanced_credit_and_debit_cards:{enabled:!1},apple_pay:{enabled:!1},google_pay:{enabled:!1},alternative_payment_methods:{enabled:!1},pay_later_messaging:{enabled:!1}}),webhooks:Object.freeze([])}),Vt=Object.freeze({useSandbox:!1,useManualConnection:!1}),[$t,Ut]=oe(Bt,Vt),zt=se(Bt,Vt,{[Mt]:(e,t)=>$t(e,t),[Nt]:(e,t)=>Ut(e,t),[Dt]:e=>{const t=$t(Ut(e,Vt),Bt);return t.wooSettings={...e.wooSettings},t.merchant={...e.merchant},t.features={...e.features},t.isReady=!0,t},[Lt]:(e,t)=>$t(e,{activities:new Map(e.activities).set(t.id,t.description)}),[Ft]:(e,t)=>{const n=new Map(e.activities);return n.delete(t.id),$t(e,{activities:n})},[Rt]:e=>({...e,merchant:Object.freeze({...Bt.merchant}),features:Object.freeze({...Bt.features})}),[At]:(e,t)=>Ut(e,{merchant:t.merchant}),[It]:(e,t)=>{const n=Ut(e,t.data);return["wooSettings","merchant","features","webhooks"].forEach((e=>{t[e]&&(n[e]=Object.freeze({...n[e],...t[e]}))})),n}}),Ht=Object.freeze({}),Wt=e=>e||Ht,Gt=e=>Wt(e).data||Ht,Yt=e=>{const{data:t,merchant:n,features:a,wooSettings:o,webhooks:s,...r}=Wt(e);return r||Ht},qt=e=>{const{activities:t=new Map}=e;return Object.fromEntries(t)},Xt=e=>Wt(e).merchant||Ht,Kt=e=>Wt(e).features||Ht,Zt=e=>{const t=Wt(e).wooSettings||Ht,n=document.cookie.split("; ").find((e=>e.startsWith("simulate-branded-only=")))?.split("=")[1],a="true"===n||t.ownBrandOnly;return{...t,ownBrandOnly:a}},Jt=e=>Wt(e).webhooks||Ht,Qt=()=>({type:Dt}),en=e=>({type:It,payload:e}),tn=(e,t)=>({type:Mt,payload:{[e]:t}}),nn=(e,t)=>({type:Nt,payload:{[e]:t}}),an=e=>tn("isReady",e),on=e=>tn("activeModal",e),sn=e=>nn("useSandbox",e),rn=e=>nn("useManualConnection",e),cn=e=>nn("webhooks",e),ln=e=>({type:At,payload:{merchant:e}}),pn=()=>({type:Rt}),un=(e,t=null)=>e&&"string"==typeof e?{type:Lt,payload:{id:e,description:t}}:(console.warn("Activity ID must be a non-empty string"),null),dn=e=>({type:Ft,payload:{id:e}});function mn(){return async({select:e})=>{await Te()({path:xt,method:"POST",data:e.persistentData()})}}function hn(){return({dispatch:e,select:t})=>{e.invalidateResolutionForStore(),t.persistentData()}}function fn(e=[],t={},n=!1){return async()=>{try{return Te()({path:kt,method:"POST",data:{useSandbox:n,products:e,options:t}})}catch(e){return{success:!1,error:e}}}}function yn(e,t,n){return async()=>{try{return await Te()({path:Ct,method:"POST",data:{clientId:e,clientSecret:t,useSandbox:n}})}catch(e){return{success:!1,error:e}}}}function gn(e,t,n){return async()=>{try{return await Te()({path:Pt,method:"POST",data:{sharedId:e,authCode:t,useSandbox:n}})}catch(e){return{success:!1,error:e}}}}function vn(e=!1){return async()=>await Te()({path:jt,method:"POST",data:{reset:e}})}function bn(){return async({dispatch:e})=>{try{await e.resetMerchant();const t=await Te()({path:St});return t.success&&t.merchant&&e.hydrate(t),t}catch(e){return{success:!1,error:e}}}}function wn(){return async({dispatch:e})=>{try{const t=await Te()({path:Tt,method:"POST"});return t&&t.success&&await e.refreshMerchantData(),t}catch(e){return{success:!1,error:e,message:e.message}}}}function Sn(){return async({dispatch:e})=>{try{const t=await Te()({method:"POST",path:Et});return t.success&&t.merchant&&e.hydrate(t),t}catch(e){return{success:!1,error:e}}}}function xn(){return async()=>await Te()({method:"POST",path:Ot})}function Cn(){return async()=>await Te()({path:Ot})}const Pn=()=>{const e=(0,J.useSelect)((e=>e(wt)),[]),t=(0,J.useDispatch)(wt),{useTransient:n,usePersistent:a}=re(wt);return(0,X.useMemo)((()=>({select:e,dispatch:t,useTransient:n,usePersistent:a})),[e,t,n,a])},jn=()=>{const{useTransient:e,usePersistent:t,dispatch:n,select:a}=Pn(),{persist:o,authenticateWithCredentials:s,authenticateWithOAuth:r,startWebhookSimulation:i,checkWebhookSimulationState:c}=n,[l,p]=e("activeModal");return{activeModal:l,setActiveModal:p,authenticateWithCredentials:s,authenticateWithOAuth:r,wooSettings:a.wooSettings(),features:a.features(),webhooks:a.webhooks(),startWebhookSimulation:i,checkWebhookSimulationState:c}},kn=()=>{const{select:e,dispatch:t,useTransient:n}=Pn(),{persist:a,refresh:o}=t,[s]=n("isReady");return s||e.persistentData(),{persist:a,refresh:o,isReady:s}},En=()=>{const{dispatch:e,usePersistent:t}=Pn(),[n,a]=t("useSandbox"),{onboardingUrl:o}=e;return{isSandboxMode:n,setSandboxMode:(t,n)=>(a(t,n),e.persist()),onboardingUrl:o}},On=()=>{const{dispatch:e,usePersistent:t}=Pn(),[n,a]=t("useManualConnection");return{isManualConnectionMode:n,setManualConnectionMode:(t,n)=>(a(t,n),e.persist())}},Tn=()=>{const{dispatch:e}=Pn(),{onboardingUrl:t}=e;return{onboardingUrl:t}},Mn=()=>{const{authenticateWithCredentials:e,authenticateWithOAuth:t}=jn(),{isManualConnectionMode:n,setManualConnectionMode:a}=On();return{isManualConnectionMode:n,setManualConnectionMode:a,authenticateWithCredentials:e,authenticateWithOAuth:t}},Nn=()=>{const{disconnectMerchant:e}=(0,J.useDispatch)(wt);return{disconnectMerchant:e}},Dn=()=>{const{wooSettings:e}=jn();return e},In=()=>{const{webhooks:e,setWebhooks:t,registerWebhooks:n,startWebhookSimulation:a,checkWebhookSimulationState:o}=jn();return{webhooks:e,setWebhooks:t,registerWebhooks:n,startWebhookSimulation:a,checkWebhookSimulationState:o}},An=()=>{const{features:e}=jn(),t=Rn(),{refreshMerchantData:n,setMerchant:a}=(0,J.useDispatch)(wt),{isReady:o}=kn();return{merchant:t,features:e,verifyLoginStatus:(0,X.useCallback)((async()=>{const e=await n();if(!e.success||!e.merchant)throw new Error(e?.message||e?.error?.message);const t=e.merchant;return!(!t?.isConnected||!t?.id||(a(t),0))}),[n,a]),isReady:o}},Rn=()=>{const e=(0,J.useSelect)((e=>e(wt).merchant()),[]);return(0,X.useMemo)((()=>{var t,n,a,o,s,r,i;return{isConnected:null!==(t=e.isConnected)&&void 0!==t&&t,isSandbox:null===(n=e.isSandbox)||void 0===n||n,id:null!==(a=e.id)&&void 0!==a?a:"",email:null!==(o=e.email)&&void 0!==o?o:"",clientId:null!==(s=e.clientId)&&void 0!==s?s:"",clientSecret:null!==(r=e.clientSecret)&&void 0!==r?r:"",isBusinessSeller:"business"===e.sellerType,isCasualSeller:"personal"===e.sellerType,isSendOnlyCountry:null!==(i=e.isSendOnlyCountry)&&void 0!==i&&i}}),[e])},Ln=()=>{const{activeModal:e,setActiveModal:t}=jn();return{activeModal:e,setActiveModal:t}},Fn=()=>{const{startActivity:e,stopActivity:t}=(0,J.useDispatch)(wt),n=(0,J.useSelect)((e=>e(wt).getActivityList()),[]),a=Object.keys(n).length>0,o=(0,X.useCallback)((async(n,a,o)=>{e(n,a);try{return await o()}finally{t(n)}}),[e,t]);return{startActivity:e,stopActivity:t,withActivity:o,isBusy:a}},Bn=()=>{const e=(0,J.useSelect)((e=>e(wt).getActivityList()),[]),[t,n]=(0,X.useState)(e);(0,X.useEffect)((()=>{n(e)}),[e]);const a=(0,X.useCallback)((n=>{const a=Object.keys(e).filter((e=>!t[e]));a.length&&a.forEach((t=>n(t,Object.keys(e))))}),[e,t]),o=(0,X.useCallback)((n=>{const a=Object.keys(t).filter((t=>!e[t]));a.length&&a.forEach((t=>n(t,Object.keys(e))))}),[e,t]);return{activities:e,onStarted:a,onFinished:o}};function Vn(){return async({dispatch:e,registry:t})=>{try{const[t,n]=await Promise.all([Te()({path:_t}),Te()({path:Et})]);t?.success&&n?.success&&n.data&&(t.webhooks=n.data),await e.hydrate(t),await e.setIsReady(!0)}catch(e){await t.dispatch("core/notices").createErrorNotice((0,tt.__)("Error retrieving plugin details.","woocommerce-paypal-payments"))}}}const $n=()=>{const e=(0,J.createReduxStore)(wt,{reducer:zt,actions:{...c,...l},selectors:i,resolvers:u});return(0,J.register)(e),lt(wt,mt),Boolean(wp.data.select(wt))},Un="wc/paypal/payment",zn="/wc/v3/wc_paypal/payment",Hn="/wc/v3/wc_paypal/payment",Wn="PAYMENT:SET_TRANSIENT",Gn="PAYMENT:SET_DISABLED_BY_DEPENDENCY",Yn="PAYMENT:RESTORE_DEPENDENCY_STATE",qn="PAYMENT:SET_PERSISTENT",Xn="PAYMENT:RESET",Kn="PAYMENT:HYDRATE",Zn="PAYMENT:CHANGE_PAYMENT_SETTING",Jn=Object.freeze({isReady:!1}),Qn=Object.freeze({"ppcp-gateway":{},venmo:{},"pay-later":{},"ppcp-card-button-gateway":{},"ppcp-credit-card-gateway":{},"ppcp-axo-gateway":{},"ppcp-applepay":{},"ppcp-googlepay":{},"ppcp-bancontact":{},"ppcp-blik":{},"ppcp-eps":{},"ppcp-ideal":{},"ppcp-mybank":{},"ppcp-p24":{},"ppcp-trustly":{},"ppcp-multibanco":{},"ppcp-pay-upon-invoice-gateway":{},"ppcp-oxxo-gateway":{},paypalShowLogo:!1,threeDSecure:"no-3d-secure",fastlaneCardholderName:!1,fastlaneDisplayWatermark:!1,__meta:!1}),[ea,ta]=oe(Jn,Qn),na=se(Jn,Qn,{[Wn]:(e,t)=>ea(e,t),[qn]:(e,t)=>ta(e,t),[Zn]:(e,t)=>{const n=t.id,a=e.data[n];return a&&a.id===n?ta(e,{[n]:{...a,...t.props}}):e},[Xn]:e=>{const t=ea(ta(e,Qn),Jn);return t.isReady=!0,t},[Kn]:(e,t)=>ta(e,t.data),[Gn]:(e,t)=>{const{methodId:n}=t,a=e.data[n];if(!a)return e;const o={...e.data,[n]:{...a,enabled:!1,_disabledByDependency:!0,_originalState:a.enabled}};return{...e,data:o}},[Yn]:(e,t)=>{const{methodId:n}=t,a=e.data[n];if(!a||!a._disabledByDependency)return e;const o={...e.data,[n]:{...a,enabled:!0===a._originalState,_disabledByDependency:!1,_originalState:void 0}};return{...e,data:o}}}),aa=Object.freeze({}),oa=e=>e||aa,sa=e=>oa(e).data||aa,ra=e=>{const{data:t,...n}=oa(e);return n||aa},ia=()=>({type:Xn}),ca=e=>({type:Kn,payload:e}),la=(e,t)=>({type:Wn,payload:{[e]:t}}),pa=(e,t)=>({type:qn,payload:{[e]:t}}),ua=e=>la("isReady",e),da=(e,t)=>({type:Zn,payload:{id:e,props:t}});function ma(){return async({select:e})=>{await Te()({path:Hn,method:"POST",data:e.persistentData()})}}function ha(){return({dispatch:e,select:t})=>{e.invalidateResolutionForStore(),t.persistentData()}}const fa=()=>{const e=(0,J.useSelect)((e=>e(Un)),[]),t=(0,J.useDispatch)(Un),{useTransient:n,usePersistent:a}=re(Un);return(0,X.useMemo)((()=>({select:e,dispatch:t,useTransient:n,usePersistent:a})),[e,t,n,a])},ya=()=>{const{select:e,useTransient:t,dispatch:n}=fa(),{persist:a,refresh:o,setPersistent:s,changePaymentSettings:r}=n,[i]=t("isReady");return i||e.persistentData(),{persist:a,refresh:o,setPersistent:s,changePaymentSettings:r,isReady:i}},ga=()=>{const{usePersistent:e}=fa(),[t]=e("ppcp-gateway"),[n]=e("venmo"),[a]=e("pay-later"),[o]=e("ppcp-card-button-gateway"),[s]=e("ppcp-credit-card-gateway"),[r]=e("ppcp-axo-gateway"),[i]=e("ppcp-applepay"),[c]=e("ppcp-googlepay"),[l]=e("ppcp-bancontact"),[p]=e("ppcp-blik"),[u]=e("ppcp-eps"),[d]=e("ppcp-ideal"),[m]=e("ppcp-mybank"),[h]=e("ppcp-p24"),[f]=e("ppcp-trustly"),[y]=e("ppcp-multibanco"),[g]=e("ppcp-pay-upon-invoice-gateway"),[v]=e("ppcp-oxxo-gateway"),b=e=>e.filter((e=>e&&e.id?.length)),w=b([t,n,a,o]),_=b([s,r,i,c]),S=b([l,p,u,d,m,h,f,y,g,v]);return{all:[...w,..._,...S],paypal:w,cardPayment:_,apm:S}},va=()=>{const{usePersistent:e}=fa(),[t]=e("paypalShowLogo"),[n]=e("fastlaneCardholderName"),[a]=e("fastlaneDisplayWatermark");return{paypalShowLogo:t,fastlaneCardholderName:n,fastlaneDisplayWatermark:a}};function ba(){return async({dispatch:e,registry:t})=>{try{const t=await Te()({path:zn});await e.hydrate(t),await e.setIsReady(!0)}catch(e){await t.dispatch("core/notices").createErrorNotice((0,tt.__)("Error retrieving payment details.","woocommerce-paypal-payments"))}}}const wa={"ppcp-applepay":"enable_apple_pay","ppcp-googlepay":"enable_google_pay","ppcp-axo-gateway":"enable_fastlane","ppcp-card-button-gateway":"enable_credit_debit_cards"},_a={},Sa=(e,t)=>{const n=t[e];return!n||!n.depends_on_payment_methods||!n.depends_on_payment_methods.some((e=>{const n=t[e];return!n||!1===n.enabled}))},xa={},Ca=(e,t)=>{const n=t[e];return!n||!n.depends_on_payment_methods||!n.depends_on_payment_methods.some((e=>{const n=t[e];return!n||!1===n.enabled}))},Pa=()=>{const e=(0,J.createReduxStore)(Un,{reducer:na,actions:h,selectors:m,resolvers:y});return(0,J.register)(e),(()=>{let e=null,t=!1;(0,J.subscribe)((()=>{if(!t){t=!0;try{const n=(0,J.select)("wc/paypal/payment").persistentData(),a=(0,J.select)("wc/paypal/todos").getCompletedTodos();if(!n||!e)return e=n,void(t=!1);let o=[...a||[]];Object.entries(wa).forEach((([t,a])=>{const s=e[t]?.enabled,r=n[t]?.enabled;s!==r&&(r?o.includes(a)||o.push(a):o=o.filter((e=>e!==a)))})),(o.length!==a.length||o.some((e=>!a.includes(e))))&&(0,J.dispatch)("wc/paypal/todos").setCompletedTodos(o),e={...n}}catch(e){console.error("Error in todo sync:",e)}finally{t=!1}}}))})(),(()=>{let e=null,t=!1;(0,J.subscribe)((()=>{if(!t){t=!0;try{const n=(0,J.select)("wc/paypal/payment");if(!n)return void(t=!1);const a=n.persistentData();if(!a)return void(t=!1);if(!e)return e={...a},void(t=!1);const o=Object.keys(a).filter((t=>"__meta"!==t&&a[t]&&e[t])).filter((t=>a[t].enabled!==e[t].enabled));o.length>0&&o.forEach((e=>{const t=a[e].enabled,n=Object.entries(a).filter((([t,n])=>"__meta"!==t&&n&&n.depends_on_payment_methods&&n.depends_on_payment_methods.includes(e))).map((([e])=>e));n.length>0&&(t?((e,t)=>{e.forEach((e=>{t[e]&&e in _a&&Sa(e,t)&&(t[e].enabled=_a[e],t[e].isDisabled=!1,delete _a[e])}))})(n,a):((e,t)=>{e.forEach((e=>{t[e]&&(e in _a||(_a[e]=t[e].enabled),t[e].enabled=!1,t[e].isDisabled=!0)}))})(n,a))})),e={...a}}catch(e){}finally{t=!1}}}))})(),(()=>{let e=null,t=!1;(0,J.subscribe)((()=>{if(!t){t=!0;try{const n=(0,J.select)("wc/paypal/settings"),a=(0,J.select)("wc/paypal/payment");if(!n||!a)return void(t=!1);const o=n.persistentData(),s=a.persistentData();if(!o||!s)return void(t=!1);if(!e)return e={...o},void(t=!1);const r=Object.keys(o).filter((t=>void 0!==e[t]&&o[t]!==e[t]));if(r.length>0)for(const e in s){if("__meta"===e||!s[e])continue;const t=s[e];if(!t.depends_on_settings?.settings)continue;const{settings:n}=t.depends_on_settings,a=Object.values(n).filter((e=>r.includes(e.id)));if(a.length>0)if(a.some((e=>o[e.id]!==e.value)))e in xa||(xa[e]=t.enabled),s[e].enabled=!1,s[e].isDisabled=!0;else{const t=Object.values(n).every((e=>o[e.id]===e.value)),a=Ca(e,s);t&&a&&e in xa&&(s[e].enabled=xa[e],s[e].isDisabled=!1,delete xa[e])}}e={...o}}catch(e){}finally{t=!1}}}))})(),Boolean(wp.data.select(Un))},ja="wc/paypal/settings",ka="/wc/v3/wc_paypal/settings",Ea="/wc/v3/wc_paypal/settings",Oa="ppcp/settings/SET_TRANSIENT",Ta="ppcp/settings/SET_PERSISTENT",Ma="ppcp/settings/RESET",Na="ppcp/settings/HYDRATE",Da=Object.freeze({isReady:!1}),Ia=Object.freeze({invoicePrefix:"",brandName:"",softDescriptor:"",subtotalAdjustment:"no_details",landingPage:"any",buttonLanguage:"",threeDSecure:"only-required-3d-secure",authorizeOnly:!1,captureVirtualOrders:!1,savePaypalAndVenmo:!1,enableContactModule:!1,saveCardDetails:!1,enablePayNow:!1,enableLogging:!1,stayUpdated:!1,disabledCards:[]}),[Aa,Ra]=oe(Da,Ia),La=se(Da,Ia,{[Oa]:(e,t)=>Aa(e,t),[Ta]:(e,t)=>Ra(e,t),[Ma]:e=>{const t=Aa(Ra(e,Ia),Da);return t.isReady=!0,t},[Na]:(e,t)=>Ra(e,t.data)}),Fa=Object.freeze({}),Ba=e=>e||Fa,Va=e=>Ba(e).data||Fa,$a=e=>{const{data:t,...n}=Ba(e);return n||Fa},Ua=()=>({type:Ma}),za=e=>({type:Na,payload:e}),Ha=(e,t)=>({type:Oa,payload:{[e]:t}}),Wa=(e,t)=>({type:Ta,payload:{[e]:t}}),Ga=e=>Ha("isReady",e);function Ya(){return async({select:e})=>{await Te()({path:Ea,method:"POST",data:e.persistentData()})}}function qa(){return({dispatch:e,select:t})=>{e.invalidateResolutionForStore(),t.persistentData()}}const Xa=()=>{const e=(0,J.useSelect)((e=>e(ja)),[]),t=(0,J.useDispatch)(ja),{useTransient:n,usePersistent:a}=re(ja);return(0,X.useMemo)((()=>({select:e,dispatch:t,useTransient:n,usePersistent:a})),[e,t,n,a])},Ka=()=>{const{select:e,dispatch:t,useTransient:n}=Xa(),{persist:a,refresh:o}=t,[s]=n("isReady");return s||e.persistentData(),{persist:a,refresh:o,isReady:s}},Za=()=>{const{usePersistent:e}=Xa(),[t,n]=e("invoicePrefix"),[a,o]=e("brandName"),[s,r]=e("softDescriptor"),[i,c]=e("subtotalAdjustment"),[l,p]=e("landingPage"),[u,d]=e("buttonLanguage"),[m,h]=e("authorizeOnly"),[f,y]=e("captureVirtualOrders"),[g,v]=e("savePaypalAndVenmo"),[b,w]=e("enableContactModule"),[_,S]=e("saveCardDetails"),[x,C]=e("enablePayNow"),[P,j]=e("enableLogging"),[k,E]=e("stayUpdated"),[O,T]=e("disabledCards"),[M,N]=e("threeDSecure");return{invoicePrefix:t,setInvoicePrefix:n,authorizeOnly:m,setAuthorizeOnly:h,captureVirtualOnlyOrders:f,setCaptureVirtualOnlyOrders:y,savePaypalAndVenmo:g,setSavePaypalAndVenmo:v,contactModule:b,setContactModule:w,saveCardDetails:_,setSaveCardDetails:S,payNowExperience:x,setPayNowExperience:C,logging:P,setLogging:j,stayUpdated:k,setStayUpdated:E,subtotalAdjustment:i,setSubtotalAdjustment:c,brandName:a,setBrandName:o,softDescriptor:s,setSoftDescriptor:r,landingPage:l,setLandingPage:p,buttonLanguage:u,setButtonLanguage:d,disabledCards:O,setDisabledCards:T,threeDSecure:M,setThreeDSecure:N}};function Ja(){return async({dispatch:e,registry:t})=>{try{const t=await Te()({path:ka});await e.hydrate(t),await e.setIsReady(!0)}catch(e){await t.dispatch("core/notices").createErrorNotice((0,tt.__)("Error retrieving PayPal Settings details.","woocommerce-paypal-payments"))}}}const Qa=()=>{const e=(0,J.createReduxStore)(ja,{reducer:La,actions:b,selectors:v,resolvers:_});return(0,J.register)(e),Boolean(wp.data.select(ja))},eo="wc/paypal/style",to="/wc/v3/wc_paypal/styling",no="/wc/v3/wc_paypal/styling",ao="ppcp/style/SET_TRANSIENT",oo="ppcp/style/SET_PERSISTENT",so="ppcp/style/RESET",ro="ppcp/style/HYDRATE",io={cart:{value:"cart",label:(0,tt.__)("Cart","woocommerce-paypal-payments"),link:"https://woocommerce.com/document/woocommerce-paypal-payments/#button-on-cart",props:{layout:!1,tagline:!1}},classicCheckout:{value:"classicCheckout",label:(0,tt.__)("Classic Checkout","woocommerce-paypal-payments"),link:"https://woocommerce.com/document/woocommerce-paypal-payments/#button-on-checkout",props:{layout:!0,tagline:!0}},expressCheckout:{value:"expressCheckout",label:(0,tt.__)("Express Checkout","woocommerce-paypal-payments"),link:"https://woocommerce.com/document/woocommerce-paypal-payments/#button-on-block-express-checkout",props:{layout:!1,tagline:!1}},miniCart:{value:"miniCart",label:(0,tt.__)("Mini Cart","woocommerce-paypel-payements"),link:"https://woocommerce.com/document/woocommerce-paypal-payments/#button-on-mini-cart",props:{layout:!0,tagline:!0}},product:{value:"product",label:(0,tt.__)("Product Page","woocommerce-paypal-payments"),link:"https://woocommerce.com/document/woocommerce-paypal-payments/#button-on-single-product",props:{layout:!0,tagline:!0}}},co={paypal:{value:"paypal",label:(0,tt.__)("PayPal","woocommerce-paypal-payments")},checkout:{value:"checkout",label:(0,tt.__)("Checkout","woocommerce-paypal-payments")},buynow:{value:"buynow",label:(0,tt.__)("PayPal Buy Now","woocommerce-paypal-payments")},pay:{value:"pay",label:(0,tt.__)("Pay with PayPal","woocommerce-paypal-payments")}},lo={gold:{value:"gold",label:(0,tt.__)("Gold (Recommended)","woocommerce-paypal-payments")},blue:{value:"blue",label:(0,tt.__)("Blue","woocommerce-paypal-payments")},silver:{value:"silver",label:(0,tt.__)("Silver","woocommerce-paypal-payments")},black:{value:"black",label:(0,tt.__)("Black","woocommerce-paypal-payments")},white:{value:"white",label:(0,tt.__)("White","woocommerce-paypal-payments")}},po={vertical:{value:"vertical",label:(0,tt.__)("Vertical","woocommerce-paypal-payments")},horizontal:{value:"horizontal",label:(0,tt.__)("Horizontal","woocommerce-paypal-payments")}},uo={rect:{value:"rect",label:(0,tt.__)("Rectangle","woocommerce-paypal-payments")},pill:{value:"pill",label:(0,tt.__)("Pill","woocommerce-paypal-payments")}},mo={"ppcp-gateway":{value:"ppcp-gateway",label:(0,tt.__)("PayPal","woocommerce-paypal-payments"),checked:!0,disabled:!0},venmo:{value:"venmo",label:(0,tt.__)("Venmo","woocommerce-paypal-payments"),isFunding:!0},"pay-later":{value:"pay-later",label:(0,tt.__)("Pay Later","woocommerce-paypal-payments"),isFunding:!0},"ppcp-googlepay":{value:"ppcp-googlepay",label:(0,tt.__)("Google Pay","woocommerce-paypal-payments")},"ppcp-applepay":{value:"ppcp-applepay",label:(0,tt.__)("Apple Pay","woocommerce-paypal-payments")}},ho=Object.freeze({isReady:!1,location:io.cart.value}),fo=Object.freeze({[io.cart.value]:Object.freeze({enabled:!0,methods:[],label:co.pay.value,shape:uo.rect.value,color:lo.gold.value}),[io.classicCheckout.value]:Object.freeze({enabled:!0,methods:[],label:co.checkout.value,shape:uo.rect.value,color:lo.gold.value,layout:po.vertical.value,tagline:!1}),[io.expressCheckout.value]:Object.freeze({enabled:!0,methods:[],label:co.checkout.value,shape:uo.rect.value,color:lo.gold.value}),[io.miniCart.value]:Object.freeze({enabled:!0,methods:[],label:co.pay.value,shape:uo.rect.value,color:lo.gold.value,layout:po.vertical.value,tagline:!1}),[io.product.value]:Object.freeze({enabled:!0,methods:[],label:co.buynow.value,shape:uo.rect.value,color:lo.gold.value,layout:po.vertical.value,tagline:!1})}),[yo,go]=oe(ho,fo),vo=se(ho,fo,{[ao]:(e,t)=>yo(e,t),[oo]:(e,t)=>go(e,t),[so]:e=>{const t=yo(go(e,fo),ho);return t.isReady=!0,t},[ro]:(e,t)=>{const n=Object.keys(fo).reduce(((n,a)=>{var o,s;return n[a]=(o=e.data[a],!(s=t.data[a])||"object"!=typeof s||Array.isArray(s)?o:{...o,...s}),n}),{});return go(e,n)}}),bo=Object.freeze({}),wo=e=>e||bo,_o=e=>wo(e).data||bo,So=e=>{const{data:t,...n}=wo(e);return n||bo},xo=()=>({type:so}),Co=e=>({type:ro,payload:e}),Po=(e,t)=>({type:ao,payload:{[e]:t}}),jo=(e,t)=>({type:oo,payload:{[e]:t}}),ko=e=>Po("isReady",e);function Eo(){return async({select:e})=>{await Te()({path:no,method:"POST",data:e.persistentData()})}}function Oo(){return({dispatch:e,select:t})=>{e.invalidateResolutionForStore(),t.persistentData()}}const To=()=>{const e=(0,J.useSelect)((e=>e(eo)),[]),t=(0,J.useDispatch)(eo),{useTransient:n,usePersistent:a}=re(eo);return(0,X.useMemo)((()=>({select:e,dispatch:t,useTransient:n,usePersistent:a})),[e,t,n,a])},Mo=()=>{const{useTransient:e,dispatch:t}=To(),{setPersistent:n}=t,[a,o]=e("location"),s=(0,J.useSelect)((e=>e(eo).persistentData()),[]);return{location:a,setLocation:o,getLocationProp:(0,X.useCallback)(((e,t)=>void 0===s[e]?.[t]?(console.error(`Trying to access non-existent style property: ${e}.${t}. Possibly wrong style name - review the reducer.`),null):s[e][t]),[s]),setLocationProp:(0,X.useCallback)(((e,t,a)=>{const o={...s[e],[t]:a};n(e,o)}),[s,n])}},No=()=>{const{select:e,dispatch:t,useTransient:n}=To(),{persist:a,refresh:o}=t,[s]=n("isReady");return s||e.persistentData(),{persist:a,refresh:o,isReady:s}},Do=()=>{const{location:e,setLocation:t}=Mo();return{location:e,setLocation:t}},Io=e=>{var t;const{getLocationProp:n,setLocationProp:a}=Mo(),o=null!==(t=io[e])&&void 0!==t?t:{},s=e=>void 0===e||!!e;return{choices:Object.values(io),details:o,isActive:s(n(e,"enabled")),setActive:t=>a(e,"enabled",s(t))}},Ao=e=>{const{getLocationProp:t,setLocationProp:n}=Mo(),a=e=>Array.isArray(e)?e:e?[e]:[];return{choices:Object.values(mo),paymentMethods:a(t(e,"methods")),setPaymentMethods:t=>n(e,"methods",a(t))}},Ro=e=>{const{getLocationProp:t,setLocationProp:n}=Mo(),a=e=>Object.values(lo).some((t=>t.value===e))?e:lo.gold.value;return{choices:Object.values(lo),color:a(t(e,"color")),setColor:t=>n(e,"color",a(t))}},Lo=e=>{const{getLocationProp:t,setLocationProp:n}=Mo(),a=e=>Object.values(uo).some((t=>t.value===e))?e:uo.rect.value;return{choices:Object.values(uo),shape:a(t(e,"shape")),setShape:t=>n(e,"shape",a(t))}},Fo=e=>{const{getLocationProp:t,setLocationProp:n}=Mo(),a=e=>Object.values(co).some((t=>t.value===e))?e:co.paypal.value;return{choices:Object.values(co),label:a(t(e,"label")),setLabel:t=>n(e,"label",a(t))}},Bo=e=>{const{getLocationProp:t,setLocationProp:n}=Mo(),{details:a}=Io(e),o=!1!==a.props.layout,s=e=>Object.values(po).some((t=>t.value===e))?e:po.vertical.value;return{choices:Object.values(po),isAvailable:o,layout:s(t(e,"layout")),setLayout:t=>n(e,"layout",s(t))}},Vo=e=>{const{getLocationProp:t,setLocationProp:n}=Mo(),{details:a}=Io(e),o=!1!==a.props.tagline&&po.horizontal.value===t(e,"layout"),s=e=>!!e;return{isAvailable:o,tagline:!!o&&s(t(e,"tagline")),setTagline:t=>n(e,"tagline",s(t))}};function $o(){return async({dispatch:e,registry:t})=>{try{const t=await Te()({path:to});await e.hydrate(t),await e.setIsReady(!0)}catch(e){await t.dispatch("core/notices").createErrorNotice((0,tt.__)("Error retrieving Styling details.","woocommerce-paypal-payments"))}}}const Uo=()=>{const e=(0,J.createReduxStore)(eo,{reducer:vo,actions:C,selectors:x,resolvers:j});return(0,J.register)(e),Boolean(wp.data.select(eo))},zo="wc/paypal/todos",Ho="/wc/v3/wc_paypal/todos",Wo="/wc/v3/wc_paypal/todos",Go="/wc/v3/wc_paypal/todos/reset",Yo="/wc/v3/wc_paypal/todos/complete",qo={RESET:"ppcp/todos/RESET",SET_TRANSIENT:"ppcp/todos/SET_TRANSIENT",SET_COMPLETED_TODOS:"ppcp/todos/SET_COMPLETED_TODOS",SET_TODOS:"ppcp/todos/SET_TODOS",SET_DISMISSED_TODOS:"ppcp/todos/SET_DISMISSED_TODOS",RESET_DISMISSED_TODOS:"ppcp/todos/RESET_DISMISSED_TODOS"},Xo=Object.freeze({isReady:!1,completedTodos:[]}),Ko=Object.freeze({todos:[],dismissedTodos:[],completedOnClickTodos:[]}),[Zo,Jo]=oe(Xo,Ko),Qo=se(Xo,Ko,{[qo.SET_TRANSIENT]:(e,t)=>Zo(e,t),[qo.RESET]:e=>{const t=Zo(Jo(e,Ko),Xo);return t.isReady=!0,t},[qo.SET_TODOS]:(e,t)=>Jo(e,{todos:t}),[qo.SET_DISMISSED_TODOS]:(e,t)=>Jo(e,{dismissedTodos:Array.isArray(t)?t:[]}),[qo.SET_COMPLETED_TODOS]:(e,t)=>Zo(e,{completedTodos:Array.isArray(t)?t:[]}),[qo.RESET_DISMISSED_TODOS]:e=>Jo(e,{dismissedTodos:[]}),[qo.HYDRATE]:(e,t)=>Jo(e,t.data)}),es=Object.freeze({}),ts=Object.freeze([]),ns=e=>e||es,as=e=>Array.isArray(e)?e:e?Object.values(e):ts,os=e=>ns(e).data||es,ss=e=>{const{data:t,...n}=ns(e);return n||es},rs=e=>{const t=e?.todos||os(e).todos;return as(t)},is=e=>{const t=e?.dismissedTodos||os(e).dismissedTodos;return as(t)},cs=e=>as(e?.completedTodos),ls=()=>({type:qo.RESET}),ps=(e,t)=>({type:qo.SET_TRANSIENT,payload:{[e]:t}}),us=(e,t)=>({type:qo.SET_PERSISTENT,payload:{[e]:t}}),ds=e=>ps("isReady",e),ms=e=>({type:qo.SET_TODOS,payload:e}),hs=e=>({type:qo.SET_DISMISSED_TODOS,payload:e}),fs=e=>({type:qo.SET_COMPLETED_TODOS,payload:e});function ys(){return async()=>{const e=await Te()({path:Ho});return e?.data||[]}}function gs(){return async({select:e})=>{await Te()({path:Wo,method:"POST",data:e.persistentData()})}}function vs(){return({dispatch:e,select:t})=>{e.invalidateResolutionForStore(),t.persistentData()}}function bs(){return async({dispatch:e})=>{try{const t=await Te()({path:Go,method:"POST"});return t&&t.success&&await e.setDismissedTodos([]),t}catch(e){return{success:!1,error:e,message:e.message}}}}function ws(e){return async({select:t,dispatch:n})=>{try{const a=await Te()({path:Yo,method:"POST",data:{todoId:e}});if(a?.success){const a=await t.getCompletedTodos();await n.setCompletedTodos([...a,e])}return a}catch(e){return{success:!1,error:e,message:e.message}}}}const _s=()=>{const e=(0,J.useSelect)((e=>e(zo)),[]),t=(0,J.useDispatch)(zo),{useTransient:n,usePersistent:a}=re(zo);return(0,X.useMemo)((()=>({select:e,dispatch:t,useTransient:n,usePersistent:a})),[e,t,n,a])},Ss=()=>{const{dispatch:e,select:t}=_s(),{fetchTodos:n,setDismissedTodos:a,setCompletedTodos:o}=e,s=t.getTodos(),r=t.getDismissedTodos(),i=t.getCompletedTodos(),c=new Set(r);return{todos:s.filter((e=>!c.has(e.id))),dismissedTodos:r,completedTodos:i,fetchTodos:n,dismissTodo:async e=>{if(!c.has(e)){const t=[...r,e];await a(t)}},setTodoCompleted:async(e,t)=>{let n;n=t?[...i,e]:i.filter((t=>t!==e)),await o(n)}}},xs=()=>{const{select:e,dispatch:t,useTransient:n}=_s(),{persist:a,refresh:o}=t,[s]=n("isReady");return s||e.getTodos(),{persist:a,refresh:o,isReady:s}},Cs=()=>{const{todos:e,fetchTodos:t,dismissTodo:n,setTodoCompleted:a}=Ss(),{isReady:o}=xs();return{todos:e,fetchTodos:t,dismissTodo:n,setTodoCompleted:a,isReady:o}},Ps=()=>{const{dismissedTodos:e}=Ss();return{dismissedTodos:e}},js=()=>{const{completedTodos:e}=Ss();return{completedTodos:e}};function ks(){return async({dispatch:e,registry:t})=>{try{const t=await Te()({path:Ho}),{todos:n=[],dismissedTodos:a=[]}=t?.data||{};await e.setTodos(n),await e.setDismissedTodos(a),await e.setIsReady(!0)}catch(e){await t.dispatch("core/notices").createErrorNotice((0,tt.__)("Error retrieving todos.","woocommerce-paypal-payments"))}}}const Es=()=>{const e=(0,J.createReduxStore)(zo,{reducer:Qo,actions:O,selectors:E,resolvers:M});return(0,J.register)(e),Boolean(wp.data.select(zo))},Os="wc/paypal/pay_later_messaging",Ts="/wc/v3/wc_paypal/pay_later_messaging",Ms="/wc/v3/wc_paypal/pay_later_messaging",Ns="ppcp/paylater/SET_TRANSIENT",Ds="ppcp/paylater/SET_PERSISTENT",Is="ppcp/paylater/RESET",As="ppcp/paylater/HYDRATE",Rs=Object.freeze({isReady:!1}),Ls=Object.freeze({cart:{},checkout:{},product:{},shop:{},home:{},custom_placement:[]}),[Fs,Bs]=oe(Rs,Ls),Vs=se(Rs,Ls,{[Ns]:(e,t)=>Fs(e,t),[Ds]:(e,t)=>Bs(e,t),[Is]:e=>{const t=Fs(Bs(e,Ls),Rs);return t.isReady=!0,t},[As]:(e,t)=>Bs(e,t.data)}),$s=Object.freeze({}),Us=e=>e||$s,zs=e=>Us(e).data||$s,Hs=e=>{const{data:t,...n}=Us(e);return n||$s},Ws=()=>({type:Is}),Gs=e=>({type:As,payload:e}),Ys=(e,t)=>({type:Ns,payload:{[e]:t}}),qs=(e,t)=>({type:Ds,payload:{[e]:t}}),Xs=e=>Ys("isReady",e);function Ks(){return async({select:e})=>{await Te()({path:Ms,method:"POST",data:e.persistentData()})}}function Zs(){return({dispatch:e,select:t})=>{e.invalidateResolutionForStore(),t.persistentData()}}const Js=()=>{const{useTransient:e,usePersistent:t}=re(Os),{persist:n}=(0,J.useDispatch)(Os),[a]=e("isReady"),[o,s]=t("cart"),[r,i]=t("checkout"),[c,l]=t("product"),[p,u]=t("shop"),[d,m]=t("home"),[h,f]=t("custom_placement");return{persist:n,isReady:a,cart:o,setCart:s,checkout:r,setCheckout:i,product:c,setProduct:l,shop:p,setShop:u,home:d,setHome:m,custom_placement:h,setCustom_placement:f}},Qs=()=>{const{persist:e,isReady:t}=Js();return{persist:e,isReady:t}},er=()=>{const{cart:e,setCart:t,checkout:n,setCheckout:a,product:o,setProduct:s,shop:r,setShop:i,home:c,setHome:l,custom_placement:p,setCustom_placement:u}=Js();return{config:{cart:e,checkout:n,product:o,shop:r,home:c,custom_placement:p},setCart:t,setCheckout:a,setProduct:s,setShop:i,setHome:l,setCustom_placement:u}};function tr(){return async({dispatch:e,registry:t})=>{try{const t=await Te()({path:Ts});await e.hydrate(t),await e.setIsReady(!0)}catch(e){await t.dispatch("core/notices").createErrorNotice((0,tt.__)("Error retrieving Pay Later Messaging details.","woocommerce-paypal-payments"))}}}const nr=()=>{const e=(0,J.createReduxStore)(Os,{reducer:Vs,actions:I,selectors:D,resolvers:R});return(0,J.register)(e),Boolean(wp.data.select(Os))},ar="wc/paypal/features",or="/wc/v3/wc_paypal/features",sr="ppcp/features/SET_TRANSIENT",rr="ppcp/features/SET_FEATURES",ir="ppcp/features/HYDRATE",cr=Object.freeze({isReady:!1}),lr=Object.freeze({features:[]}),[pr,ur]=oe(cr,lr),dr=se(cr,lr,{[sr]:(e,t)=>pr(e,t),[rr]:(e,t)=>ur(e,{features:t}),[ir]:(e,t)=>ur(e,t.data)}),mr=Object.freeze({}),hr=Object.freeze([]),fr=e=>e||mr,yr=e=>fr(e).data||mr,gr=e=>{const{data:t,...n}=fr(e);return n||mr},vr=e=>e?.features||yr(e).features||hr,br=e=>({type:ir,payload:e}),wr=(e,t)=>({type:sr,payload:{[e]:t}}),_r=e=>wr("isReady",e),Sr=e=>({type:rr,payload:e}),xr=async()=>{try{const e=await Te()({path:or});return e?.data?{success:!0,features:e.data.features}:{success:!1,features:[]}}catch(e){return{success:!1,error:e,message:e.message}}},Cr=()=>{const{features:e,isReady:t}=(0,J.useSelect)((e=>({features:e(ar).getFeatures()||[],isReady:e(ar).transientData()?.isReady||!1})),[]),{setFeatures:n,setIsReady:a}=(0,J.useDispatch)(ar);return(0,X.useEffect)((()=>{t||(async()=>{try{const e=await Te()({path:or});if(e?.data?.features){const t=e.data.features;t.length>0&&(await n(t),await a(!0))}}catch(e){}})()}),[t,n,a]),{features:e,isReady:t,fetchFeatures:async()=>{try{const e=await Te()({path:or}),t=e.data?.features||[];return t.length>0?(await n(t),await a(!0),{success:!0,features:t}):{success:!1,features:[]}}catch(e){return{success:!1,error:e,message:e.message}}}}};function Pr(){return async({dispatch:e})=>{try{const t=await Te()({path:or});t?.features&&(e.setFeatures(t.features),e.setIsReady(!0))}catch(e){console.error("Error fetching features:",e)}}}const jr=()=>{const e=(0,J.createReduxStore)(ar,{reducer:dr,actions:B,selectors:F,resolvers:$});return(0,J.register)(e),Boolean(wp.data.select(ar))},kr="ppcp/tracking/UPDATE_SOURCES",Er="ppcp/tracking/CLEAR_SOURCES",Or="ppcp/tracking/CLEAR_FIELD_SOURCE",Tr="ppcp/tracking/RESET",Mr={},Nr=(e=Mr,t)=>{switch(t.type){case kr:{const{storeName:n,fieldName:a,source:o,timestamp:s}=t.payload;return{...e,[n]:{...e[n]||{},[a]:{source:o,timestamp:s}}}}case Or:{const{storeName:n,fieldName:a}=t.payload,o=e[n];if(!o)return e;const s={...o};return delete s[a],{...e,[n]:s}}case Er:{const{storeName:n}=t.payload;if(n){const t={...e};return delete t[n],t}return Mr}case Tr:return Mr;default:return e}},Dr=(e,t,n)=>e?.[t]?.[n]||null,Ir=(e,t)=>e?.[t]||{},Ar=e=>e||{},Rr=(e,t,n)=>!!Dr(e,t,n),Lr=(e,t,n)=>({type:kr,payload:{storeName:e,fieldName:t,source:n,timestamp:Date.now()}}),Fr=(e=null,t=null)=>t?{type:Or,payload:{storeName:e,fieldName:t}}:{type:Er,payload:{storeName:e}},Br=()=>({type:Tr}),Vr=()=>{const e=(0,J.createReduxStore)(ne,{reducer:Nr,actions:H,selectors:z});return(0,J.register)(e),Boolean(wp.data.select(ne))},$r=[r,d,g,S,k,N,L,U,W];$r.forEach((e=>{try{!1===e.initStore()&&console.error(`Store initialization failed for ${e.STORE_NAME}`)}catch(t){console.error("Error during store initialization:",e.STORE_NAME,t)}}));const Ur=a,zr=p,Hr=f,Wr=w,Gr=P,Yr=T,qr=A,Xr=V,Kr=Q,Zr=wt,Jr=Un,Qr=ja,ei=eo,ti=zo,ni=Os,ai=ar,oi=ne;((e,t)=>{if(!e)return;const n=(e,t)=>{console.log(`\n%c${e}:`,"font-weight:bold",t,"\n\n")},a=window.ppcpDebugger=window.ppcpDebugger||{};a.dumpStore=async(e=null)=>{console?.groupCollapsed?t.forEach((t=>{const n=t.STORE_NAME,a=`wp.data.select( '${n}' )`;console.group(`[STORE] ${a}`),Object.keys(t.selectors).forEach((t=>{let a=wp.data.select(n)[t]();e?(a=e(a,t,n),null!=a&&console.log(`.${t}() [filtered]`,a)):(console.groupCollapsed(`.${t}()`),console.table(a),console.groupEnd())})),console.groupEnd()})):console.error("console.groupCollapsed is not supported.")},a.resetStore=()=>{const e=[];n("resetStore","Reset all Redux stores to their DEFAULT state, without changing any server-side data. The default state is defined in the JS code.");const{completed:t}=wp.data.select(Kr).persistentData();e.push(Zr),e.push(Jr),e.push(Qr),e.push(ei),e.push(ti),e.push(ni),e.push(ai),e.push(oi),t||e.push(Kr),e.forEach((e=>{const t=wp.data.dispatch(e);try{t.reset(),console.log(`Done: Store '${e}' reset`)}catch(t){console.error(`Failed: Could not reset store '${e}'`)}})),console.log("---- Complete ----\n\n")},a.refreshStore=()=>{const e=[];n("refreshStore","Refreshes all Redux details with details provided by the server. This has a similar effect as reloading the page without saving"),e.push(Zr),e.push(Jr),e.push(Qr),e.push(ei),e.push(ti),e.push(Kr),e.push(ni),e.push(ai),e.push(oi),e.forEach((e=>{const t=wp.data.dispatch(e);try{t.refresh(),console.log(`Done: Store '${e}' refreshed from REST`)}catch(t){console.error(`Failed: Could not refresh store '${e}' from REST`)}})),console.log("---- Complete ----\n\n")},a.disconnect=()=>{const e=wp.data.dispatch(Zr);n(),e.disconnectMerchant(),console.log("Disconnected from PayPal. Reloading the page..."),window.location.reload()},a.onboardingMode=e=>{const t=wp.data.dispatch(Kr);n("onboardingMode","Toggle between onboarding wizard and the settings screen."),t.setPersistent("completed",!e),t.persist()},a.simulateBrandedOnly=e=>{const t=new Date(Date.now()+36e5).toUTCString();document.cookie=`simulate-branded-only=${e}; expires=${t}; path=/`,window.location.reload()},Object.assign(e,a)})(window.ppcpSettings,$r);const si=window.wp.components,ri=window.ReactJSXRuntime,ii=({asModal:e=!1,ariaLabel:t=(0,tt.__)("Loading…","woocommerce-paypal-payments")})=>{const n=Z()("ppcp-r-spinner-overlay",{"ppcp--is-modal":e});return(0,ri.jsx)("div",{className:n,role:"status","aria-label":t,children:(0,ri.jsx)(si.Spinner,{})})},ci=({isCard:e=!0,page:t,children:n})=>{let a="ppcp-r-container";return e&&(a+=" ppcp-r-container--card"),t&&(a+=` ppcp-r-container--${t}`),(0,ri.jsx)("div",{className:a,children:n})},li=({id:e,children:t})=>(0,ri.jsx)("div",{className:"ppcp--action",...e?{id:e}:{},children:t}),pi=({isDimmed:e=!1,children:t})=>{const n=Z()("ppcp--card-actions",{"ppcp--dimmed":e});return(0,ri.jsx)("div",{className:n,children:t})},ui=({children:e,asCard:t=!0,className:n="",id:a=""})=>{const o=Z()("ppcp--content",n,{"ppcp--is-card":t});return(0,ri.jsx)("div",{id:a,className:o,children:e})},di=({children:e})=>(0,ri.jsx)("div",{className:"ppcp-r-settings-card__content-wrapper",children:e}),mi=({children:e,className:t=""})=>{if(!e)return null;const n=Z()("ppcp--description",t);return"string"!=typeof e?(0,ri.jsx)("span",{className:n,children:e}):(0,ri.jsx)("span",{className:n,dangerouslySetInnerHTML:{__html:e}})},hi=({children:e,className:t=""})=>{if(!e)return null;const n=Z()("ppcp--header",t);return(0,ri.jsx)("div",{className:n,children:e})},fi=({url:e})=>e&&"#"!==e?(0,ri.jsx)(si.Button,{href:e,variant:"tertiary",target:"_blank",children:(0,tt.__)("Learn more","woocommerce-paypal-payments")}):null,yi=({children:e,type:t="info",className:n=""})=>{if(!e)return null;const a=Z()("ppcp--notice",`type--${t}`,n);return(0,ri.jsx)("span",{className:a,children:e})},gi=({className:e="",text:t="",withLine:n=!0})=>{const a=["ppcp-r-separator"],o=n?"ppcp-r-separator__line":"ppcp-r-separator__space";e&&a.push(e);const s=e=>`${o} ${o}--${e}`;return(0,ri.jsx)("div",{className:a.join(" "),children:t?(0,ri.jsxs)(ri.Fragment,{children:[(0,ri.jsx)("span",{className:s("before")}),(0,ri.jsx)("span",{className:"ppcp-r-separator__text",children:t}),(0,ri.jsx)("span",{className:s("after")})]}):(0,ri.jsx)("span",{className:s("full")})})},vi=({children:e,noCaps:t=!1,big:n=!1,className:a=""})=>{if(!e)return null;const o=Z()("ppcp--title",a,{"ppcp--no-caps":t,"ppcp--big":n});return(0,ri.jsx)("span",{className:o,children:e})},bi=({children:e})=>e?(0,ri.jsx)("span",{className:"ppcp--title-extra",children:e}):null,wi=({children:e})=>(0,ri.jsx)("span",{className:"ppcp--title-wrapper",children:e}),_i=({id:e,className:t,title:n,description:a,children:o,contentContainer:s=!0})=>{const r={className:Z()("ppcp-r-settings-card",t),id:e},i=e?`${e}-title`:void 0,c=e?`${e}-description`:void 0;return(0,ri.jsxs)("div",{...r,role:"region","aria-labelledby":i,children:[(0,ri.jsx)("div",{className:"ppcp-r-settings-card__header",children:(0,ri.jsxs)("div",{className:"ppcp-r-settings-card__content-inner",children:[(0,ri.jsx)("h2",{id:i,className:"ppcp-r-settings-card__title",children:n}),(0,ri.jsx)("div",{id:c,className:"ppcp-r-settings-card__description",children:a})]})}),(0,ri.jsx)(Si,{showCards:s,children:o})]})},Si=({showCards:e,children:t})=>e?(0,ri.jsx)(ui,{children:t}):t,xi=window.wp.a11y,Ci=window.wp.primitives,Pi=(0,ri.jsx)(Ci.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,ri.jsx)(Ci.Path,{d:"M14.6 7l-1.2-1L8 12l5.4 6 1.2-1-4.6-5z"})}),ji=()=>window.innerHeight+window.scrollY>=document.body.scrollHeight,ki=(e,t=!0)=>new Promise((n=>{const a=document.getElementById(e);if(a){const e=document.querySelector(".ppcp-r-navigation-container"),o=e?e.offsetHeight:0,s=a.getBoundingClientRect().top+window.scrollY-(o+55);window.scrollTo({top:s,behavior:"smooth"}),t&&(a.classList.add("ppcp-highlight"),setTimeout((()=>{a.classList.remove("ppcp-highlight")}),2e3)),setTimeout(n,300)}else console.error(`Failed to scroll: Element with ID "${e}" not found`),n()})),Ei=()=>{window.location.href=window.ppcpSettings.wcPaymentsTabUrl},Oi=(e=null)=>{let t=window.ppcpSettings.pluginSettingsUrl;e&&(t+="&panel="+e),window.location.href=t},Ti=()=>{const e=new URLSearchParams(window.location.search),t=e.get("highlight");return!!t&&(setTimeout((()=>{ki(t),e.delete("highlight");const n=window.location.pathname+(e.toString()?"?"+e.toString():"")+window.location.hash;window.history.replaceState({},document.title,n)}),100),!0)},Mi=()=>({goToWooCommercePaymentsTab:Ei,goToPluginSettings:Oi,handleHighlightFromUrl:Ti}),Ni=(0,X.createContext)(!1),Di=({children:e,enabled:t=!0,busySpinner:n=!0,className:a="",onBusy:o=()=>({disabled:!0}),isBusy:s=!1})=>{const{isBusy:r}=zr.useBusyState(),i=(0,X.useContext)(Ni),c=(s||r)&&t,l=n&&c&&!i,p=Z()("ppcp-r-busy-wrapper",a,{"ppcp--is-loading":c}),u=(0,X.useMemo)((()=>X.Children.map(e,(e=>(0,X.isValidElement)(e)?(0,X.cloneElement)(e,c?o(e.props):{}):e))),[e,c,o]);return(0,ri.jsx)(Ni.Provider,{value:c,children:(0,ri.jsxs)("div",{className:p,children:[l&&(0,ri.jsx)(ii,{asModal:!1,message:""}),u]})})},Ii=({percent:e})=>(e=Math.min(Math.max(e,0),100),(0,ri.jsx)("div",{className:"ppcp-r-navigation--progress-bar",style:{width:`${e}%`}})),Ai=({title:e,children:t,isMainTitle:n=!0,exitOnTitleClick:a=!1,onTitleClick:o=null,showProgressBar:s=!1,progressBarPercent:r=0,subNavigation:i=null})=>{const{goToWooCommercePaymentsTab:c}=Mi(),{isScrolled:l}=(()=>{const[e,t]=(0,X.useState)(!1),[n,a]=(0,X.useState)(ji()),o=(0,X.useRef)(null);return(0,X.useEffect)((()=>{const e=()=>{t(window.pageYOffset>20),a(ji())},n=()=>{o.current=window.requestAnimationFrame(e)};return window.addEventListener("scroll",n),window.addEventListener("resize",n),()=>{window.removeEventListener("scroll",n),window.removeEventListener("resize",n),window.cancelAnimationFrame(o.current)}}),[]),{isScrolled:e,atBottom:n,atTop:!e}})(),p=Z()("ppcp-r-navigation-container",{"ppcp--is-scrolled":l}),u=Z()("ppcp--nav-title",{"ppcp--big":n}),d=(0,X.useCallback)((()=>{a?c():"function"==typeof o&&o()}),[a,c,o]);return(0,X.useLayoutEffect)((()=>{window.dispatchEvent(new Event("resize"))}),[]),(0,ri.jsx)(ri.Fragment,{children:(0,ri.jsxs)("nav",{className:p,children:[(0,ri.jsxs)("div",{className:"ppcp-r-navigation",children:[(0,ri.jsx)(Di,{className:"ppcp-r-navigation--left",busySpinner:!1,enabled:!a,children:(0,ri.jsxs)(si.Button,{variant:"link",onClick:d,className:"is-title",children:[(0,ri.jsx)(si.Icon,{icon:Pi}),(0,ri.jsx)("span",{className:u,children:e})]})}),(0,ri.jsx)(Di,{className:"ppcp-r-navigation--right",busySpinner:!1,children:t})]}),i&&(0,ri.jsx)("section",{className:"ppcp--top-sub-navigation",children:i}),s&&(0,ri.jsx)(Ii,{percent:r})]})})},Ri=()=>{const{withActivity:e}=zr.useBusyState(),t=Hr.useStore(),n=Wr.useStore(),a=Gr.useStore(),o=Yr.useStore(),s=qr.useStore(),r=(0,X.useMemo)((()=>[{key:"methods",message:"Process payment methods",store:t},{key:"settings",message:"Process the settings",store:n},{key:"styling",message:"Process styling details",store:a},{key:"todos",message:"Process todos state",store:o},{key:"pay-later-messaging",message:"Process pay later messaging details",store:s}]),[s,t,n,a,o]);return{persistAll:(0,X.useCallback)((()=>{document.getElementById("configurator-publishButton")?.click(),r.forEach((({key:t,message:n,store:a})=>{e(`persist-${t}`,n,a.persist)}))}),[r,e]),refreshAll:(0,X.useCallback)((()=>{r.forEach((({key:t,message:n,store:a})=>{e(`refresh-${t}`,n,a.refresh)}))}),[r,e])}},Li=window.wp.url,Fi=()=>window.location,Bi=()=>Object.fromEntries(new URLSearchParams(Fi().search)),Vi=(e,t=!1)=>{const n=t?e:{...Bi(),...e};return a=$i(n),window.history.pushState({path:a},"",a);var a},$i=(e,t=(()=>Fi().pathname)())=>(0,Li.addQueryArgs)(t,e),Ui=({tabs:e,activePanel:t,setActivePanel:n})=>{const a=(0,X.useCallback)((t=>{var a;a=t,e.some((e=>e.name===a))?n(t):console.warn(`Invalid tab name: ${t}`)}),[e,n]);return(0,X.useEffect)((()=>{Vi({panel:t})}),[t]),(0,ri.jsx)(si.TabPanel,{className:`ppcp-r-tabs ${t}`,initialTabName:t,onSelect:a,tabs:e,orientation:"horizontal",selectOnMove:!1,children:e=>(0,ri.jsx)("div",{className:`ppcp-r-tabpanel-content ppcp-r-tabpanel-${e.name}`,children:e.render?e.render():""})})},zi=({canSave:e=!0,tabs:t=[],activePanel:n="",setActivePanel:a=()=>{}})=>{const{persistAll:o}=Ri(),s=(0,tt.__)("PayPal Payments","woocommerce-paypal-payments"),[r,i]=(0,X.useState)(!1);return(0,ri.jsx)(Ai,{title:s,exitOnTitleClick:!0,subNavigation:(0,ri.jsx)(Ui,{tabs:t,activePanel:n,setActivePanel:a}),children:e&&(0,ri.jsxs)(ri.Fragment,{children:[(0,ri.jsx)(si.Button,{variant:"primary",onClick:()=>{i(!0),(0,xi.speak)((0,tt.__)("Saving settings…","woocommerce-paypal-payments"),"assertive"),o()},"aria-busy":r,children:r?(0,tt.__)("Saving…","woocommerce-paypal-payments"):(0,tt.__)("Save","woocommerce-paypal-payments")}),(0,ri.jsx)(Hi,{setIsSaving:i,isSaving:r})]})})},Hi=({setIsSaving:e,isSaving:t})=>{const[n,a]=(0,X.useState)(!1),[o,s]=(0,X.useState)(!1),{onStarted:r,onFinished:i}=zr.useActivityObserver(),c=(0,X.useRef)(null),l=(0,X.useCallback)((t=>{t.startsWith("persist")&&(e(!0),a(!1),s(!1),c.current&&clearTimeout(c.current))}),[e]),p=(0,X.useCallback)(((n,o)=>{t&&0===o.length&&(e(!1),a(!0),setTimeout((()=>s(!0)),50),(0,xi.speak)((0,tt.__)("Settings saved successfully.","woocommerce-paypal-payments"),"assertive"),c.current=setTimeout((()=>{s(!1),setTimeout((()=>a(!1)),300)}),2500))}),[t,e]);if((0,X.useEffect)((()=>{r(l),i(p)}),[r,i,l,p]),!n)return null;const u=Z()("ppcp-r-navbar-notice","ppcp--success",{"ppcp--animating":o});return(0,ri.jsx)("span",{className:u,role:"status","aria-live":"polite",children:(0,ri.jsx)("span",{className:"ppcp--inner-text",children:(0,tt.__)("Completed","woocommerce-paypal-payments")})})},Wi=()=>{const e="/wp-admin/admin.php?page=wc-settings";return(0,ri.jsxs)(ri.Fragment,{children:[(0,ri.jsx)(zi,{canSave:!1}),(0,ri.jsx)(ci,{page:"settings",children:(0,ri.jsxs)(_i,{title:(0,tt.__)('"Send-only" Country',"woocommerce-paypal-payments"),description:(0,tt.__)("Sellers in your country are unable to receive payments via PayPal","woocommerce-paypal-payments"),children:[(0,ri.jsx)("p",{children:(0,tt.__)('Your current WooCommerce store location is in a "send-only" country, according to PayPal\'s policies',"woocommerce-paypal-payments")}),(0,ri.jsx)("p",{children:(0,tt.__)('Since receiving payments is essential for using the PayPal Payments extension, you are unable to connect your PayPal account while operating from a "send-only" country.',"woocommerce-paypal-payments")}),(0,ri.jsx)("p",{dangerouslySetInnerHTML:{__html:(0,tt.sprintf)(/* translators: 1: URL to the WooCommerce store location settings */ /* translators: 1: URL to the WooCommerce store location settings */
(0,tt.__)('To activate PayPal, please <a href="%1$s">update your WooCommerce store location</a> to a supported region and connect a PayPal account eligible for receiving payments.',"woocommerce-paypal-payments"),e)}}),(0,ri.jsx)("div",{children:(0,ri.jsx)(si.Button,{href:e,variant:"primary",className:"small-button",children:(0,tt.__)("Go to WooCommerce settings","woocommerce-paypal-payments")})})]})})]})},Gi=({type:e})=>(0,ri.jsx)(si.Icon,{icon:{...n.g.ppcpSettings,getImage(e,t=""){const a=n.g.ppcpSettings.assets.imagesUrl;return(0,ri.jsx)("img",{className:t,alt:"",src:a+e})}}.getImage(`icon-button-${e}.svg`),className:"ppcp--method-icon"}),Yi=({icons:e=[]})=>(0,ri.jsx)("div",{className:"ppcp-r-payment-method-icons",children:e.map((e=>(0,ri.jsx)(Gi,{type:e},e)))}),qi=(0,ri.jsx)(Ci.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,ri.jsx)(Ci.Path,{d:"M6.5 12.4L12 8l5.5 4.4-.9 1.2L12 10l-4.5 3.6-1-1.2z"})}),Xi=(0,ri.jsx)(Ci.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,ri.jsx)(Ci.Path,{d:"M17.5 11.6L12 16l-5.5-4.4.9-1.2L12 14l4.5-3.6 1 1.2z"})}),Ki=e=>e&&window.location.hash===`#${e}`;function Zi(e="",t=null){const[n,a]=(0,X.useState)(((e,t)=>null!==t?t:Ki(e))(e,t));(0,X.useEffect)((()=>{const t=()=>{Ki(e)&&a(!0)};return window.addEventListener("hashchange",t),()=>{window.removeEventListener("hashchange",t)}}),[e]);const o=(0,X.useCallback)((e=>(a(!n),e?.preventDefault(),!1)),[n]);return{isOpen:n,setIsOpen:a,toggleOpen:o}}const Ji=({title:e,id:t="",noCaps:n=!1,initiallyOpen:a=null,description:o="",children:s=null,className:r=""})=>{const{isOpen:i,toggleOpen:c}=Zi(t,a),l=t?`${t}-content`:`accordion-${e.replace(/\s+/g,"-").toLowerCase()}-content`;return(0,ri.jsxs)("div",{className:Z()("ppcp-r-accordion",r,{"ppcp--is-open":i}),id:t||void 0,children:[(0,ri.jsx)("button",{type:"button",className:"ppcp--toggler",onClick:c,"aria-expanded":i,"aria-controls":l,children:(0,ri.jsxs)(hi,{children:[(0,ri.jsxs)(wi,{children:[(0,ri.jsx)(vi,{noCaps:n,children:e}),(0,ri.jsx)(li,{children:(0,ri.jsx)(si.Icon,{icon:i?qi:Xi})})]}),o&&(0,ri.jsx)(mi,{children:o})]})}),(0,ri.jsx)("div",{className:Z()("ppcp--accordion-content",{"ppcp--is-open":i}),id:l,"aria-hidden":!i,inert:i?void 0:"",children:(0,ri.jsx)(ui,{asCard:!1,children:s})})]})},Qi=({imageName:e,className:t="",alt:a=""})=>{const o=n.g.ppcpSettings.assets.imagesUrl;return(0,ri.jsx)("img",{className:t,alt:a,src:`${o}${e}`})},ec=(0,ri.jsx)(Ci.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 25 24",children:(0,ri.jsx)(Ci.Path,{d:"M12.4999 12.75V18.75C12.4999 18.9489 12.4209 19.1397 12.2803 19.2803C12.1396 19.421 11.9488 19.5 11.7499 19.5C11.551 19.5 11.3603 19.421 11.2196 19.2803C11.0789 19.1397 10.9999 18.9489 10.9999 18.75V14.5613L4.78055 20.7806C4.71087 20.8503 4.62815 20.9056 4.5371 20.9433C4.44606 20.981 4.34847 21.0004 4.24993 21.0004C4.15138 21.0004 4.0538 20.981 3.96276 20.9433C3.87171 20.9056 3.78899 20.8503 3.7193 20.7806C3.64962 20.7109 3.59435 20.6282 3.55663 20.5372C3.51892 20.4461 3.49951 20.3485 3.49951 20.25C3.49951 20.1515 3.51892 20.0539 3.55663 19.9628C3.59435 19.8718 3.64962 19.7891 3.7193 19.7194L9.93868 13.5H5.74993C5.55102 13.5 5.36025 13.421 5.2196 13.2803C5.07895 13.1397 4.99993 12.9489 4.99993 12.75C4.99993 12.5511 5.07895 12.3603 5.2196 12.2197C5.36025 12.079 5.55102 12 5.74993 12H11.7499C11.9488 12 12.1396 12.079 12.2803 12.2197C12.4209 12.3603 12.4999 12.5511 12.4999 12.75ZM19.9999 3H7.99993C7.6021 3 7.22057 3.15804 6.93927 3.43934C6.65796 3.72064 6.49993 4.10218 6.49993 4.5V9C6.49993 9.19891 6.57895 9.38968 6.7196 9.53033C6.86025 9.67098 7.05102 9.75 7.24993 9.75C7.44884 9.75 7.63961 9.67098 7.78026 9.53033C7.92091 9.38968 7.99993 9.19891 7.99993 9V4.5H19.9999V16.5H15.4999C15.301 16.5 15.1103 16.579 14.9696 16.7197C14.8289 16.8603 14.7499 17.0511 14.7499 17.25C14.7499 17.4489 14.8289 17.6397 14.9696 17.7803C15.1103 17.921 15.301 18 15.4999 18H19.9999C20.3978 18 20.7793 17.842 21.0606 17.5607C21.3419 17.2794 21.4999 16.8978 21.4999 16.5V4.5C21.4999 4.10218 21.3419 3.72064 21.0606 3.43934C20.7793 3.15804 20.3978 3 19.9999 3Z"})}),tc=(0,ri.jsx)(Ci.SVG,{fill:"none",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 110 38",children:(0,ri.jsx)(Ci.Path,{d:"M109.583.683v27.359h-6.225V.683h6.225Zm-8.516 9.234v18.175h-5.534v-1.567c-.7.683-1.5 1.2-2.383 1.567a7.259 7.259 0 0 1-2.892.583c-1.3 0-2.508-.242-3.616-.725a9.216 9.216 0 0 1-2.892-2.067 10.021 10.021 0 0 1-1.958-3.05c-.459-1.183-.684-2.458-.684-3.816 0-1.359.225-2.617.684-3.775.483-1.184 1.133-2.217 1.958-3.092a8.708 8.708 0 0 1 2.892-2.033c1.108-.509 2.316-.767 3.616-.767 1.034 0 2 .192 2.892.583a7.312 7.312 0 0 1 2.383 1.567V9.933h5.534v-.016Zm-9.809 13.225c1.134 0 2.059-.384 2.784-1.167.75-.775 1.125-1.767 1.125-2.975 0-1.208-.375-2.208-1.125-2.975-.725-.775-1.659-1.167-2.784-1.167-1.125 0-2.075.384-2.825 1.167-.725.775-1.083 1.767-1.083 2.975 0 1.208.367 2.208 1.083 2.975.75.775 1.692 1.167 2.825 1.167ZM72.225.683c1.642 0 3.042.234 4.2.692 1.158.458 2.133 1.1 2.933 1.925a9.439 9.439 0 0 1 1.917 2.908c.458 1.092.683 2.267.683 3.525 0 1.259-.225 2.434-.683 3.525a9.293 9.293 0 0 1-1.917 2.909c-.791.825-1.775 1.466-2.933 1.925-1.158.458-2.558.691-4.2.691h-3v9.3h-6.333V.683h9.333Zm-.908 12.467c.85 0 1.491-.083 1.958-.258a3.853 3.853 0 0 0 1.192-.725c.65-.609.975-1.417.975-2.434 0-1.016-.325-1.825-.975-2.433a3.329 3.329 0 0 0-1.192-.692c-.458-.191-1.108-.291-1.958-.291h-2.1v6.833h2.1ZM39.558 9.917h6.875l4.667 8.716h.075l4.158-8.716H61.7l-13.642 27.4h-6.333l6.225-12.534-8.392-14.866Zm-1.225 0v18.175H32.8v-1.567c-.7.683-1.5 1.2-2.383 1.567a7.258 7.258 0 0 1-2.892.583c-1.3 0-2.508-.242-3.617-.725a9.218 9.218 0 0 1-2.891-2.067 10.18 10.18 0 0 1-1.959-3.05c-.458-1.183-.683-2.458-.683-3.816 0-1.359.225-2.617.683-3.775.484-1.184 1.134-2.217 1.959-3.092a8.626 8.626 0 0 1 2.891-2.033c1.109-.509 2.317-.767 3.617-.767 1.033 0 2 .192 2.892.583A7.312 7.312 0 0 1 32.8 11.5V9.933h5.533v-.016Zm-9.808 13.225c1.133 0 2.058-.384 2.792-1.167.75-.775 1.125-1.767 1.125-2.975 0-1.208-.375-2.208-1.125-2.975-.725-.775-1.659-1.167-2.792-1.167-1.133 0-2.075.384-2.825 1.167-.725.775-1.083 1.767-1.083 2.975 0 1.208.366 2.208 1.083 2.975.75.775 1.692 1.167 2.825 1.167ZM9.75.683c1.642 0 3.042.234 4.2.692 1.158.458 2.133 1.1 2.933 1.925A9.439 9.439 0 0 1 18.8 6.208c.458 1.092.683 2.267.683 3.525 0 1.259-.225 2.434-.683 3.525a9.293 9.293 0 0 1-1.917 2.909c-.791.825-1.775 1.466-2.933 1.925-1.158.458-2.558.691-4.2.691h-3v9.3H.417V.683H9.75Zm-.9 12.467c.85 0 1.492-.083 1.958-.258A3.855 3.855 0 0 0 12 12.167c.65-.609.975-1.417.975-2.434 0-1.016-.325-1.825-.975-2.433a3.33 3.33 0 0 0-1.192-.692c-.458-.191-1.108-.291-1.958-.291h-2.1v6.833h2.1Z",fill:"#000"})}),nc="✔️",ac="❌",oc=e=>(0,ri.jsxs)("section",{className:"ppcp-r-onboarding-header",children:[(0,ri.jsx)("div",{className:"ppcp-r-onboarding-header__logo",children:(0,ri.jsx)("div",{className:"ppcp-r-onboarding-header__logo-wrapper",children:(0,ri.jsx)(si.Icon,{icon:tc,width:110,height:38})})}),(0,ri.jsxs)("div",{className:"ppcp-r-onboarding-header__content",children:[(0,ri.jsx)("h1",{className:"ppcp-r-onboarding-header__title",children:e.title}),e.description&&(0,ri.jsx)("p",{className:"ppcp-r-onboarding-header__description",dangerouslySetInnerHTML:{__html:e.description}})]})]}),sc={US:{fixedFee:{USD:.49,GBP:.39,CAD:.59,AUD:.59,EUR:.39},checkout:3.49,plater:4.99,ccf:{percentage:2.89,fixedFee:.29},dw:{percentage:2.89,fixedFee:.29},apm:{percentage:2.89,fixedFee:.29},fast:{percentage:2.89,fixedFee:.29},standardCardFields:2.99},GB:{fixedFee:{GBP:.3,USD:.3,CAD:.3,AUD:.3,EUR:.35},checkout:2.9,plater:2.9,ccf:1.2,dw:1.2,fast:1.2,apm:1.2,standardCardFields:1.2},CA:{fixedFee:{CAD:.3,USD:.3,GBP:.2,AUD:.3,EUR:.35},checkout:2.9,ccf:2.7,dw:2.7,fast:2.7,apm:2.9,standardCardFields:2.9},AU:{fixedFee:{AUD:.3,USD:.3,GBP:.2,CAD:.3,EUR:.35},checkout:2.6,plater:2.6,ccf:1.75,dw:1.75,fast:1.75,apm:2.6,standardCardFields:2.6},FR:{fixedFee:{EUR:.35,USD:.3,GBP:.3,CAD:.3,AUD:.3},checkout:2.9,plater:2.9,ccf:1.2,dw:1.2,fast:1.2,apm:1.2,standardCardFields:1.2},IT:{fixedFee:{EUR:.35,USD:.3,GBP:.3,CAD:.3,AUD:.3},checkout:3.4,plater:3.4,ccf:1.2,dw:1.2,fast:1.2,apm:1.2,standardCardFields:1.2},DE:{fixedFee:{EUR:.39,USD:.49,GBP:.29,CAD:.59,AUD:.59},checkout:2.99,plater:2.99,ccf:2.99,dw:2.99,fast:2.99,apm:2.99,standardCardFields:2.99},ES:{fixedFee:{EUR:.35,USD:.3,GBP:.3,CAD:.3,AUD:.3},checkout:2.9,plater:2.9,ccf:1.2,dw:1.2,fast:1.2,apm:1.2,standardCardFields:1.2}},rc={US:{PaymentDetails:"https://www.paypal.com/us/business/paypal-business-fees",PayPalCheckout:"https://www.paypal.com/us/business/accept-payments/checkout",PayLater:"https://www.paypal.com/us/business/accept-payments/checkout/installments",Venmo:"https://www.paypal.com/us/enterprise/payment-processing/accept-venmo",Crypto:"https://www.paypal.com/us/digital-wallet/manage-money/crypto",OptionalMethods:"https://www.paypal.com/us/business/accept-payments/checkout/integration#expanded-checkout",Fastlane:"https://www.paypal.com/us/enterprise/payment-processing/guest-checkout"},CA:{PaymentDetails:"https://www.paypal.com/ca/business/paypal-business-fees",PayPalCheckout:"https://www.paypal.com/ca/business/accept-payments/checkout"},GB:{PaymentDetails:"https://www.paypal.com/uk/business/paypal-business-fees",PayPalCheckout:"https://www.paypal.com/uk/business/accept-payments/checkout",PayInThree:"https://www.paypal.com/uk/business/accept-payments/checkout/installments"},FR:{PaymentDetails:"https://www.paypal.com/fr/business/paypal-business-fees",PayPalCheckout:"https://www.paypal.com/fr/business/accept-payments/checkout",PayLater:"https://www.paypal.com/fr/business/accept-payments/checkout/installments"},ES:{PaymentDetails:"https://www.paypal.com/es/business/paypal-business-fees",PayPalCheckout:"https://www.paypal.com/es/business/accept-payments/checkout",PayLater:"https://www.paypal.com/es/business/accept-payments/checkout/installments"},IT:{PaymentDetails:"https://www.paypal.com/it/business/paypal-business-fees",PayPalCheckout:"https://www.paypal.com/it/business/accept-payments/checkout",PayLater:"https://www.paypal.com/it/business/accept-payments/checkout/installments"},DE:{PaymentDetails:"https://www.paypal.com/de/business/paypal-business-fees",PayPalCheckout:"https://www.paypal.com/de/business/accept-payments/checkout",PayLater:"https://www.paypal.com/de/business/accept-payments/checkout/installments"},AU:{PaymentDetails:"https://www.paypal.com/au/business/paypal-business-fees",PayPalCheckout:"https://www.paypal.com/au/business/accept-payments/checkout",PayLater:"https://www.paypal.com/au/business/accept-payments/checkout/installments"}},ic=()=>{const{storeCountry:e}=zr.useWooSettings();if(!sc[e])return null;const t=rc[e]||rc.US,n=(0,tt.sprintf)(
// translators: %1$s: Pricing date, %2$s Link to PayPal price-details page.
// translators: %1$s: Pricing date, %2$s Link to PayPal price-details page.
(0,tt.__)('Prices based on domestic transactions as of %1$s. <a target="_blank" href="%2$s">Click here</a> for full pricing details.',"woocommerce-paypal-payments"),"February 1st, 2025",t.PaymentDetails);return(0,ri.jsxs)("p",{className:"ppcp-r-optional-payment-methods__description","data-country":e,children:[(0,ri.jsx)("sup",{children:"1"}),(0,ri.jsx)("span",{dangerouslySetInnerHTML:{__html:n}})]})},cc=({images:e})=>e&&e.length?(0,ri.jsx)(lc,{children:(0,ri.jsx)("span",{className:"ppcp-r-badge-box__title-image-badge",children:e.map(((e,t)=>(0,ri.jsx)(Qi,{imageName:e,className:"ppcp-r-badge-box__image"},`badge-${t}`)))})}):null,lc=({children:e})=>e?(0,ri.jsxs)(ri.Fragment,{children:[" ",e," "]}):null,pc=({description:e,learnMoreLink:t})=>e||t?(0,ri.jsx)("div",{className:"ppcp-r-badge-box__description",children:(0,ri.jsxs)("p",{className:"ppcp-r-badge-box__description",children:[e,(0,ri.jsx)(fi,{url:t})]})}):null,uc=({title:e,textBadge:t,imageBadge:n=[],description:a="",learnMoreLink:o=""})=>{const s="ppcp-r-badge-box__title",r=n.length?`${s} ppcp-r-badge-box__title--has-image-badge`:s;return(0,ri.jsxs)("div",{className:"ppcp-r-badge-box",children:[(0,ri.jsxs)("span",{className:r,children:[(0,ri.jsx)("span",{className:"ppcp-r-badge-box__title-text",children:e}),(0,ri.jsx)(cc,{images:n}),(0,ri.jsx)(lc,{children:t})]}),(0,ri.jsx)(pc,{description:a,learnMoreLink:o})]})},dc=({methods:e,learnMoreConfig:t})=>(0,ri.jsx)(ri.Fragment,{children:e.map(((n,a)=>(0,ri.jsx)(mc,{...n,learnMore:t[n.name],showSeparator:a<e.length-1},n.name)))}),mc=({Component:e,learnMore:t,showSeparator:n})=>(0,ri.jsxs)(ri.Fragment,{children:[(0,ri.jsx)(e,{learnMore:t}),n&&(0,ri.jsx)(gi,{className:"ppcp-r-payment-method--separator"})]}),hc={USD:{prefix:"$",suffix:"USD"},CAD:{prefix:"$",suffix:"CAD"},AUD:{prefix:"$",suffix:"AUD"},EUR:{prefix:"€",suffix:""},GBP:{prefix:"£",suffix:""}},fc=(e,t)=>{const n=hc[t],a=e.toFixed(2);return n?`${n.prefix}${a} ${n.suffix}`:(console.error(`Unsupported currency: ${t}`),a)},yc="positive",gc=({text:e,type:t})=>{const n=`ppcp-r-title-badge ppcp-r-title-badge--${t}`;return(0,ri.jsx)("span",{className:n,dangerouslySetInnerHTML:{__html:e}})},vc=({item:e})=>{var t,n;const{storeCountry:a,storeCurrency:o}=zr.useWooSettings(),s=sc[a],r=e.split(" ")[0];if(!s||!s[r])return null;const i="number"==typeof s[r]?s[r].toFixed(2):s[r].percentage.toFixed(2),c=null!==(t=null!==(n=s[r].fixedFee)&&void 0!==n?n:s.fixedFee[o])&&void 0!==t?t:0,l=((e,t,n)=>{if(t[e])return fc(n,e);const[a,o]=Object.entries(t)[0];return fc(o+n,a)})(o,s.fixedFee,c),p=(0,tt.sprintf)((0,tt.__)("from %1$s%% + %2$s","woocommerce-paypal-payments"),i,l);return(0,ri.jsx)(gc,{type:"info",text:`${p}<sup>1</sup>`})},bc=({learnMore:e=""})=>{const{storeCountry:t}=Dn(),n="MX"===t?["icon-button-oxxo.svg"]:["icon-button-ideal.svg","icon-button-blik.svg","icon-button-bancontact.svg"];return(0,ri.jsx)(uc,{title:(0,tt.__)("Alternative Payment Methods","woocommerce-paypal-payments"),imageBadge:n,textBadge:(0,ri.jsx)(vc,{item:"apm"}),description:(0,tt.__)("Seamless payments for customers across the globe using their preferred payment methods.","woocommerce-paypal-payments"),learnMoreLink:e})},wc=({learnMore:e=""})=>(0,ri.jsx)(uc,{title:(0,tt.__)("Custom Card Fields","woocommerce-paypal-payments"),imageBadge:["icon-button-visa.svg","icon-button-mastercard.svg","icon-button-amex.svg","icon-button-discover.svg"],textBadge:(0,ri.jsx)(vc,{item:"ccf"}),description:(0,tt.__)("Style the credit card fields to match your own style. Includes advanced processing with risk management, 3D Secure, fraud protection options, and chargeback protection.","woocommerce-paypal-payments"),learnMoreLink:e}),_c=({learnMore:e=""})=>(0,ri.jsx)(uc,{title:(0,tt.__)("Credit and Debit Cards","woocommerce-paypal-payments"),imageBadge:["icon-button-visa.svg","icon-button-mastercard.svg","icon-button-amex.svg","icon-button-discover.svg"],textBadge:(0,ri.jsx)(vc,{item:"standardCardFields"}),description:(0,tt.__)("Process major credit and debit cards through PayPal’s card fields.","woocommerce-paypal-payments"),learnMoreLink:e}),Sc=({learnMore:e=""})=>(0,ri.jsx)(uc,{title:(0,tt.__)("Digital Wallets","woocommerce-paypal-payments"),imageBadge:["icon-button-apple-pay.svg","icon-button-google-pay.svg"],textBadge:(0,ri.jsx)(vc,{item:"dw"}),description:(0,tt.__)("Accept Apple Pay on eligible devices and Google Pay through mobile and web.","woocommerce-paypal-payments"),learnMoreLink:e}),xc=({learnMore:e=""})=>(0,ri.jsx)(uc,{title:(0,tt.__)("Pay Later","woocommerce-paypal-payments"),imageBadge:["icon-payment-method-paypal-small.svg"],textBadge:(0,ri.jsx)(vc,{item:"plater"}),description:(0,tt.__)("Offer installment payment options and get paid upfront.","woocommerce-paypal-payments"),learnMoreLink:e}),Cc=({learnMore:e="https://www.paypal.com/us/business/accept-payments/checkout",description:t})=>{const n=(0,tt.__)("PayPal Checkout","woocommerce-paypal-payments");return(0,ri.jsx)(uc,{title:n,textBadge:(0,ri.jsx)(vc,{item:"checkout"}),description:t,learnMoreLink:e})},Pc=({learnMore:e=""})=>(0,ri.jsx)(uc,{title:(0,tt.__)("Pay with PayPal","woocommerce-paypal-payments"),imageBadge:["icon-button-paypal.svg"],description:(0,tt.__)("Our brand recognition helps give customers the confidence to buy.","woocommerce-paypal-payments"),learnMoreLink:e}),jc=[{name:"paypal",always:!0},{name:"venmo",isOwnBrand:!0,onlyAcdc:!1,countries:["US"]},{name:"visa",isOwnBrand:!1,onlyAcdc:!1},{name:"mastercard",isOwnBrand:!1,onlyAcdc:!1},{name:"amex",isOwnBrand:!1,onlyAcdc:!1},{name:"discover",isOwnBrand:!1,onlyAcdc:!1},{name:"apple-pay",isOwnBrand:!1,onlyAcdc:!0},{name:"google-pay",isOwnBrand:!1,onlyAcdc:!0},{name:"blik",isOwnBrand:!0,onlyAcdc:!0},{name:"ideal",isOwnBrand:!0,onlyAcdc:!0},{name:"bancontact",isOwnBrand:!0,onlyAcdc:!0},{name:"oxxo",isOwnBrand:!0,onlyAcdc:!1,countries:["MX"]}],kc={includedMethods:[{name:"PayWithPayPal",Component:Pc},{name:"PayLater",Component:xc}],extendedMethods:[{name:"CreditDebitCards",Component:_c,isOwnBrand:!1,isAcdc:!1},{name:"CardFields",Component:wc,isOwnBrand:!1,isAcdc:!0},{name:"DigitalWallets",Component:Sc,isOwnBrand:!1,isAcdc:!0},{name:"APMs",Component:bc,isOwnBrand:!0,isAcdc:!0}]},Ec={US:{includedMethods:[{name:"PayWithPayPal",Component:Pc},{name:"PayLater",Component:xc},{name:"Venmo",Component:({learnMore:e=""})=>(0,ri.jsx)(uc,{title:(0,tt.__)("Venmo","woocommerce-paypal-payments"),imageBadge:["icon-button-venmo.svg"],description:(0,tt.__)("Automatically offer Venmo checkout to millions of active users.","woocommerce-paypal-payments"),learnMoreLink:e})},{name:"Crypto",Component:({learnMore:e=""})=>(0,ri.jsx)(uc,{title:(0,tt.__)("Crypto","woocommerce-paypal-payments"),imageBadge:["icon-payment-method-crypto.svg"],description:(0,tt.__)("Let customers checkout with Crypto while you get paid in cash.","woocommerce-paypal-payments"),learnMoreLink:e})}],extendedMethods:[{name:"CreditDebitCards",Component:_c,isOwnBrand:!1,isAcdc:!1},{name:"CardFields",Component:wc,isOwnBrand:!1,isAcdc:!0},{name:"DigitalWallets",Component:Sc,isOwnBrand:!1,isAcdc:!0},{name:"APMs",Component:bc,isOwnBrand:!0,isAcdc:!0},{name:"Fastlane",Component:({learnMore:e=""})=>(0,ri.jsx)(uc,{title:(0,tt.__)("","woocommerce-paypal-payments"),imageBadge:["icon-payment-method-fastlane-small.svg"],textBadge:(0,ri.jsx)(vc,{item:"fast country currency=storeCurrency=storeCountrylane"}),description:(0,tt.__)("Speed up guest checkout with Fastlane. Link a customer's email address to their payment details.","woocommerce-paypal-payments"),learnMoreLink:e}),isOwnBrand:!1,isAcdc:!0,isFastlane:!0}]},GB:{includedMethods:[{name:"PayWithPayPal",Component:Pc},{name:"PayInThree",Component:({learnMore:e=""})=>(0,ri.jsx)(uc,{title:(0,tt.__)("Pay in 3","woocommerce-paypal-payments"),imageBadge:["icon-payment-method-paypal-small.svg"],description:(0,tt.__)("Offer installment payment options and get paid upfront - at no extra cost to you.","woocommerce-paypal-payments"),learnMoreLink:e})}]},MX:{extendedMethods:[{name:"CreditDebitCards",Component:_c,isOwnBrand:!1,isAcdc:!1},{name:"APMs",Component:bc,isOwnBrand:!0,isAcdc:!1}]}},Oc=(e,t,n,a)=>(0,X.useMemo)((()=>{console.log("[Payment Config]",{country:e,canUseCardPayments:t,hasFastlane:n,ownBrandOnly:a});const o={...kc,...Ec[e]||{}};let s=rc[e]||{};if(a&&s.OptionalMethods){const{OptionalMethods:e,...t}=s;s={...t,APMs:e}}const r=(i=o.extendedMethods,c=[n=>"MX"===e?!n.isAcdc||t:n.isAcdc===t,e=>!a||!0===e.isOwnBrand,e=>"Fastlane"!==e.name||n],i.filter((e=>c.every((t=>t(e))))));var i,c;const l=((e,t)=>{const n={EXPANDED:(0,tt.__)("Expanded Checkout","woocommerce-paypal-payments"),OPTIONAL:(0,tt.__)("Optional payment methods","woocommerce-paypal-payments")},a={LOCAL_METHODS:(0,tt.__)("Accept local payment methods. Note: Additional application required for some methods","woocommerce-paypal-payments"),WITH_APPLICATION:(0,tt.__)("with additional application","woocommerce-paypal-payments"),US_EXPANDED:(0,tt.__)("Accept debit/credit cards, PayPal, Apple Pay, Google Pay, and more. Note: Additional application required for some methods","woocommerce-paypal-payments")},o={DEFAULT_CHECKOUT:(0,tt.__)("Our all-in-one checkout solution lets you offer PayPal, Pay Later options, and more to help maximise conversion","woocommerce-paypal-payments"),US_CHECKOUT:(0,tt.__)("Our all-in-one checkout solution lets you offer PayPal, Venmo, Pay Later options, and more to help maximise conversion","woocommerce-paypal-payments")},s={paypalCheckoutDescription:o.DEFAULT_CHECKOUT,optionalTitle:n.OPTIONAL,optionalDescription:a.WITH_APPLICATION};return"US"===e&&(s.paypalCheckoutDescription=o.US_CHECKOUT,s.optionalTitle=n.EXPANDED,s.optionalDescription=a.US_EXPANDED),t&&(s.optionalTitle=n.EXPANDED,s.optionalDescription=a.LOCAL_METHODS),s})(e,a),p=((e,t,n)=>jc.filter((({always:a,isOwnBrand:o,onlyAcdc:s,countries:r=[]})=>!!a||("MX"!==e||!s)&&!(n&&!o)&&!(!t&&s)&&(!r.length||r.includes(e)))).map((e=>e.name)))(e,t,a);return{includedMethods:o.includedMethods,basicMethods:o.basicMethods,optionalMethods:r,paypalCheckoutDescription:l.paypalCheckoutDescription,optionalTitle:l.optionalTitle,optionalDescription:l.optionalDescription,learnMoreConfig:s,icons:p}}),[e,t,n,a]),Tc=({useAcdc:e,isFastlane:t,storeCountry:n,ownBrandOnly:a,onlyOptional:o=!1})=>{const{includedMethods:s,optionalMethods:r,optionalTitle:i,optionalDescription:c,learnMoreConfig:l,paypalCheckoutDescription:p}=Oc(n,e,t,a);if(o)return(0,ri.jsx)(Nc,{methods:r,learnMoreConfig:l});const u=e&&"MX"!==n?c:"";return(0,ri.jsxs)("div",{className:"ppcp-r-welcome-docs__wrapper",children:[(0,ri.jsx)(Mc,{methods:s,learnMoreConfig:l,paypalCheckoutDescription:p}),(0,ri.jsx)(Nc,{title:i,description:u,methods:r,learnMoreConfig:l})]})},Mc=({methods:e,learnMoreConfig:t,paypalCheckoutDescription:n})=>(0,ri.jsxs)("div",{className:"ppcp-r-welcome-docs__col",children:[(0,ri.jsx)(Cc,{learnMore:t.PayPalCheckout,description:n}),(0,ri.jsx)(uc,{title:(0,tt.__)("Included in PayPal Checkout","woocommerce-paypal-payments")}),(0,ri.jsx)(dc,{methods:e,learnMoreConfig:t})]}),Nc=({title:e="",description:t="",methods:n,learnMoreConfig:a})=>n.length?(0,ri.jsxs)("div",{className:"ppcp-r-welcome-docs__col",children:[e&&(0,ri.jsx)(uc,{title:e,description:t,learnMoreLink:a.OptionalMethods}),(0,ri.jsx)(dc,{methods:n,learnMoreConfig:a})]}):null,Dc=({useAcdc:e,isFastlane:t,storeCountry:n,ownBrandOnly:a})=>(0,ri.jsxs)("div",{className:"ppcp-r-welcome-docs",children:[(0,ri.jsx)("h2",{className:"ppcp-r-welcome-docs__title",children:(0,tt.__)("Want to know more about PayPal Payments?","woocommerce-paypal-payments")}),(0,ri.jsx)(Tc,{useAcdc:e,isFastlane:t,storeCountry:n,ownBrandOnly:a}),(0,ri.jsx)(ic,{})]}),Ic=({isToggled:e,setToggled:t,disabled:n=!1,...a})=>{const o=(0,X.useRef)(null);return(0,ri.jsxs)("div",{className:["ppcp-r-toggle-block"].join(" "),children:[(0,ri.jsxs)("div",{className:"ppcp-r-toggle-block__wrapper",children:[(0,ri.jsxs)("div",{className:"ppcp-r-toggle-block__content",children:[a?.label&&(0,ri.jsx)("div",{className:"ppcp-r-toggle-block__content-label",onClick:()=>{o.current&&!n&&(o.current.click(),o.current.focus())},children:a.label}),a?.description&&(0,ri.jsx)("p",{className:"ppcp-r-toggle-block__content-description",dangerouslySetInnerHTML:{__html:a.description}})]}),(0,ri.jsx)("div",{className:"ppcp-r-toggle-block__switch",children:(0,ri.jsx)(si.ToggleControl,{__nextHasNoMarginBottom:!0,ref:o,checked:e,onChange:e=>t(e),disabled:n})})]}),a.children&&e&&(0,ri.jsx)("div",{className:"ppcp-r-toggle-block__toggled-content",children:a.children})]})},Ac=window.wp.notices,Rc={CONNECTED:(0,tt.__)("Connected to PayPal","woocommerce-paypal-payments"),API_ERROR:(0,tt.__)("Could not connect to PayPal. Please make sure your Client ID and Secret Key are correct.","woocommerce-paypal-payments"),LOGIN_FAILED:(0,tt.__)("Login was not successful. Please try again.","woocommerce-paypal-payments")},Lc=()=>{const{isSandboxMode:e,setSandboxMode:t}=zr.useSandbox();return{isSandboxMode:e,setSandboxMode:t}},Fc=({className:e,variant:t,showIcon:n,href:a,children:o,onClick:s})=>{const r="undefined"!=typeof window&&window.navigator.userAgent.toLowerCase().indexOf("firefox")>-1,i={className:e,variant:t,icon:n?ec:null,onClick:s};return a&&(i.href=a,i["data-paypal-button"]="true",i["data-paypal-onboard-button"]="true"),r?(0,ri.jsxs)(ri.Fragment,{children:[(0,ri.jsx)(si.Button,{...i,children:o}),(0,ri.jsx)(yi,{type:"error",children:(0,tt.__)("This button may not work in Firefox. Please use another browser, like Chrome, to complete this step.","woocommerce-paypal-payments")})]}):(0,ri.jsx)(si.Button,{...i,children:o})},Bc=({title:e,isSandbox:t=!1,variant:n="primary",showIcon:a=!0,className:o=""})=>{const{onboardingUrl:s,scriptLoaded:r,setCompleteHandler:i,removeCompleteHandler:c}=(e=>{const{onboardingUrl:t}=e?zr.useSandbox():zr.useProduction(),{ownBrandOnly:n,storeCountry:a}=zr.useWooSettings(),{products:o,options:s}=Ur.useDetermineProducts(n,a),{startActivity:r}=zr.useBusyState(),{authenticateWithOAuth:i}=zr.useAuthentication(),[c,l]=(0,X.useState)(""),[p,u]=(0,X.useState)(!1),d=(0,X.useRef)(null);(0,X.useEffect)((()=>{(async()=>{const n=await t(o,s,e);n.success&&n.data?l(n.data):console.error("Failed to fetch onboarding URL")})()}),[e,o,s,t]),(0,X.useEffect)((()=>{if(!c)return;const e=document.createElement("script");return e.id="partner-js",e.src="https://www.paypal.com/webapps/merchantboarding/js/lib/lightbox/partner.js",e.onload=()=>{u(!0)},document.body.appendChild(e),()=>{["partner-js","signup-js","rampConfig-js"].forEach((e=>{const t=document.querySelector(`script[id="${e}"]`);t?.parentNode&&t.parentNode.removeChild(t)}))}}),[c]);const m=(0,X.useCallback)((e=>{const t=async(t,n)=>{r("oauth/login","Validating the connection details"),await i(n,t,"sandbox"===e)};d.current=setInterval((()=>{const e=window.PAYPAL?.apps?.Signup?.MiniBrowser;e&&!e.onOnboardComplete&&(e.onOnboardComplete=t)}),250)}),[i,r]),h=(0,X.useCallback)((()=>{d.current&&(clearInterval(d.current),d.current=null),delete window.PAYPAL?.apps?.Signup?.MiniBrowser?.onOnboardComplete}),[]);return{onboardingUrl:c,scriptLoaded:p,setCompleteHandler:m,removeCompleteHandler:h}})(t),{connectionButtonClicked:l,setConnectionButtonClicked:p}=et.useConnectionButton(),u=Z()("ppcp-r-connection-button",o,{"ppcp--mode-sandbox":t,"ppcp--mode-live":!t,"ppcp--button-clicked":l}),d=t?"sandbox":"production",m=(0,X.useCallback)((()=>{p(!0)}),[p]);return(0,X.useEffect)((()=>{s&&l&&p(!1)}),[s,l,p]),(0,X.useEffect)((()=>(r&&s&&(window.PAYPAL.apps.Signup.render(),i(d)),()=>{c()})),[r,s,d,i,c]),(0,ri.jsx)(Di,{isBusy:!s,children:(0,ri.jsx)(Fc,{className:u,variant:n,showIcon:a,href:s,onClick:m,children:(0,ri.jsx)("span",{className:"button-title",children:e})})})},Vc=()=>{const{isSandboxMode:e,setSandboxMode:t}=Lc();return(0,ri.jsx)(Di,{children:(0,ri.jsx)(Ic,{label:(0,tt.__)("Enable Sandbox Mode","woocommerce-paypal-payments"),description:(0,tt.__)("Activate Sandbox mode to safely test PayPal with sample data. Once your store is ready to go live, you can easily switch to your production account.","woocommerce-paypal-payments"),isToggled:!!e,setToggled:e=>{t(e,"user")},children:(0,ri.jsx)(Bc,{title:(0,tt.__)("Connect Account","woocommerce-paypal-payments"),showIcon:!1,variant:"secondary",className:"small-button",isSandbox:!0})})})},$c=React.forwardRef((({control:e,value:t,onChange:n,onConfirm:a=null,delay:o=300,...s},r)=>{const[i,c]=(0,X.useState)(t),l=(0,X.useRef)(n),p=(0,X.useRef)(a);l.current=n,p.current=a;const u=(0,X.useRef)(((e,t)=>{const n={timeoutId:null,args:null},a=()=>{n.timeoutId&&window.clearTimeout(n.timeoutId),n.timeoutId=null,n.args=null},o=()=>{n.timeoutId&&(e.apply(null,n.args||[]),a())},s=(...e)=>{a(),n.args=e,n.timeoutId=window.setTimeout(o,t)};return s.cancel=a,s.flush=o,s})((e=>{l.current(e)}),o)).current;(0,X.useEffect)((()=>{c(t),u?.cancel()}),[u,t]),(0,X.useEffect)((()=>()=>u?.cancel()),[u]);const d=(0,X.useCallback)((e=>{c(e),u(e)}),[u]),m=(0,X.useCallback)((e=>{if(p.current&&"Enter"===e.key)return e.preventDefault(),u.flush(),p.current(),!1}),[u]);return(0,ri.jsx)(e,{ref:r,...s,value:i,onChange:d,onKeyDown:m})})),Uc={noClientId:(0,tt.__)("Please enter your Client ID","woocommerce-paypal-payments"),noClientSecret:(0,tt.__)("Please enter your Secret Key","woocommerce-paypal-payments"),invalidClientId:(0,tt.__)("Please enter a valid Client ID","woocommerce-paypal-payments")},zc=()=>{const[e,t]=(0,X.useState)(!1),[n,a]=(0,X.useState)(!1),{isSandboxMode:o}=Lc(),{manualClientId:s,setManualClientId:r,manualClientSecret:i,setManualClientSecret:c}=Ur.useManualConnectionForm(),{handleDirectAuthentication:l}=(()=>{const{handleFailed:e,handleCompleted:t,createErrorNotice:n}=(()=>{const{setCompleted:e}=Ur.useSteps(),{createSuccessNotice:t,createErrorNotice:n}=(0,J.useDispatch)(Ac.store),{verifyLoginStatus:a}=zr.useMerchantInfo(),{withActivity:o}=zr.useBusyState(),{refreshAll:s}=Ri();return{handleFailed:(e,t)=>{var a;console.error("Connection error",e),n(null!==(a=e?.message)&&void 0!==a?a:t)},handleCompleted:async()=>{await o("auth/verify-login","Verifying Authentication",(async()=>{try{await a()?(t(Rc.CONNECTED),await e(!0),s()):n(Rc.LOGIN_FAILED)}catch(e){var o;n(null!==(o=e.message)&&void 0!==o?o:Rc.LOGIN_FAILED)}}))},createErrorNotice:n}})(),{withActivity:a}=zr.useBusyState(),{authenticateWithCredentials:o,isManualConnectionMode:s,setManualConnectionMode:r}=zr.useAuthentication();return{handleDirectAuthentication:async s=>a("auth/api-login","Connecting manually via Client ID and Secret",(async()=>{let a;if("function"==typeof s)try{a=s()}catch(e){return void n(e.message)}else"object"==typeof s&&(a=s);if(!a||!a.clientId||!a.clientSecret)return void n("Invalid connection details (clientID or clientSecret missing)");const r=await o(a.clientId,a.clientSecret,!!a.isSandbox);return r.success?await t():e(r,Rc.API_ERROR),r.success})),isManualConnectionMode:s,setManualConnectionMode:r}})(),{isManualConnectionMode:p,setManualConnectionMode:u}=On(),d=(0,X.useRef)(null),m=(0,X.useRef)(null),h=(0,X.useCallback)((()=>{const t=[{ref:d,valid:()=>s,errorMessage:Uc.noClientId},{ref:d,valid:()=>e,errorMessage:Uc.invalidClientId},{ref:m,valid:()=>i&&n,errorMessage:Uc.noClientSecret}];for(const{ref:e,valid:n,errorMessage:a}of t)if(!n())throw e?.current?.focus(),new Error(a);return{clientId:s,clientSecret:i,isSandbox:o}}),[s,i,o,e,n]);(0,X.useEffect)((()=>{t(!s||/^A[\w-]{79}$/.test(s)),a(i&&i.length>0)}),[s,i]);const f=(0,X.useMemo)((()=>o?(0,tt.__)("Sandbox Client ID","woocommerce-paypal-payments"):(0,tt.__)("Live Client ID","woocommerce-paypal-payments")),[o]),y=(0,X.useMemo)((()=>o?(0,tt.__)("Sandbox Secret Key","woocommerce-paypal-payments"):(0,tt.__)("Live Secret Key","woocommerce-paypal-payments")),[o]),g=(0,tt.sprintf)(
// translators: %s: Link to PayPal REST application guide
// translators: %s: Link to PayPal REST application guide
(0,tt.__)('For advanced users: Connect a custom PayPal REST app for full control over your integration. For more information on creating a PayPal REST application, <a target="_blank" href="%s">click here</a>.',"woocommerce-paypal-payments"),"https://woocommerce.com/document/woocommerce-paypal-payments/#manual-credential-input"),v=(0,X.useCallback)((()=>l(h)),[l,h]);return(0,ri.jsx)(Di,{onBusy:e=>({disabled:!0,label:e.label+" ..."}),children:(0,ri.jsxs)(Ic,{label:(0,tt.__)("Manually Connect","woocommerce-paypal-payments"),description:g,isToggled:!!p,setToggled:e=>{u(e,"user")},children:[(0,ri.jsx)($c,{__nextHasNoMarginBottom:!0,control:si.TextControl,ref:d,label:f,value:s,onChange:r,onConfirm:v,className:Z()({"ppcp--has-error":!e})}),e||(0,ri.jsx)("p",{className:"client-id-error",children:Uc.invalidClientId}),(0,ri.jsx)($c,{__nextHasNoMarginBottom:!0,control:si.TextControl,ref:m,label:y,value:i,onChange:c,onConfirm:v,type:"password"}),(0,ri.jsx)(si.Button,{variant:"secondary",className:"small-button",onClick:v,children:(0,tt.__)("Connect Account","woocommerce-paypal-payments")})]})})},Hc=()=>(0,ri.jsxs)(ri.Fragment,{children:[(0,ri.jsx)(Vc,{}),(0,ri.jsx)(gi,{withLine:!1}),(0,ri.jsx)(zc,{})]}),Wc=()=>(0,ri.jsxs)("div",{className:"ppcp-r-welcome-features",children:[(0,ri.jsxs)("div",{className:"ppcp-r-welcome-features__col",children:[(0,ri.jsx)("span",{children:(0,tt.__)("Deposits","woocommerce-paypal-payments")}),(0,ri.jsx)("p",{children:(0,tt.__)("Instant","woocommerce-paypal-payments")})]}),(0,ri.jsxs)("div",{className:"ppcp-r-welcome-features__col",children:[(0,ri.jsx)("span",{children:(0,tt.__)("Payment Capture","woocommerce-paypal-payments")}),(0,ri.jsx)("p",{children:(0,tt.__)("Authorize only or Capture","woocommerce-paypal-payments")})]}),(0,ri.jsxs)("div",{className:"ppcp-r-welcome-features__col",children:[(0,ri.jsx)("span",{children:(0,tt.__)("Recurring payments","woocommerce-paypal-payments")}),(0,ri.jsx)("p",{children:(0,tt.__)("Supported","woocommerce-paypal-payments")})]})]}),Gc=({label:e,value:t,checked:n=null,disabled:a=null,onChange:o,changeCallback:s})=>{const r=Z()({"ppcp--is-disabled":a});return(0,ri.jsx)(si.CheckboxControl,{__nextHasNoMarginBottom:!0,label:e,value:t,checked:n,disabled:a,onChange:e=>{o?o(t,e):s&&(console.warn('Deprecated prop, use "onChange" instead of "changeCallback"'),s(t,e))},className:r})},Yc=({name:e,options:t,value:n,onChange:a})=>{const o=(0,X.useCallback)(((e,t)=>{a("boolean"==typeof n?t:t?[...n,e]:n.filter((t=>t!==e)))}),[a,n]),s=(e,t)=>"boolean"==typeof e?e:Array.isArray(n)?n.includes(t):"boolean"==typeof n?n:n===t;return(0,ri.jsx)(ri.Fragment,{children:t.map((({value:t,label:n,checked:a,disabled:r,description:i})=>(0,ri.jsx)(Gc,{value:t,label:n,checked:s(a,t),disabled:r,description:i,changeCallback:o},e+t)))})},qc=({options:e,selected:t,onChange:n})=>(0,ri.jsx)(si.RadioControl,{options:e,onChange:n,selected:t}),Xc=({id:e,name:t,value:n,currentValue:a,checked:o=null,onChange:s,handleRdbState:r})=>{const i={className:"ppcp-r__radio-value",type:"radio",onChange:(0,X.useCallback)((()=>{s?s(n):r&&(console.warn('Deprecated prop, use "onChange" instead of "handleRdbState"'),r(n))}),[r,s,n]),checked:null===o?n===a:o,id:e,name:t,value:n};return(0,ri.jsxs)("div",{className:"ppcp-r__radio",children:[(0,ri.jsx)("input",{...i}),(0,ri.jsx)("span",{className:"ppcp-r__radio-presentation"})]})},Kc=({multiSelect:e=!1,options:t,value:n,onChange:a})=>(0,ri.jsx)("div",{className:"ppcp-r-select-box-wrapper",children:t.map((({value:t,title:o,description:s,contents:r,isDisabled:i=!1})=>{let c;return c=Array.isArray(n)?n.includes(t):n===t,(0,ri.jsx)(Zc,{itemTitle:o,itemDescription:s,itemValue:t,onChange:a,isMulti:e,isSelected:c,isDisabled:i,children:r},t)}))}),Zc=({itemTitle:e,itemDescription:t,itemValue:n,onChange:a,isMulti:o,isSelected:s,children:r,isDisabled:i=!1})=>{const c=Z()("ppcp-r-select-box",{"ppcp--selected":s,"ppcp--multiselect":o,"ppcp--no-title":!e});return(0,ri.jsxs)("label",{className:c,children:[(0,ri.jsx)(Jc,{value:n,isRadio:!o,onChange:a,isSelected:s,isDisabled:i}),(0,ri.jsx)("div",{className:"ppcp--box-content",children:(0,ri.jsxs)("div",{className:"ppcp--box-content-inner",children:[e&&(0,ri.jsx)("span",{className:"ppcp--box-title",children:e}),(0,ri.jsx)("div",{className:"ppcp--box-description",children:t}),r&&(0,ri.jsx)("div",{className:"ppcp--box-details",children:r})]})})]})},Jc=({value:e,onChange:t,isRadio:n,isSelected:a,isDisabled:o})=>n?(0,ri.jsx)(Xc,{value:e,onChange:t,checked:a}):(0,ri.jsx)(Gc,{value:e,onChange:t,checked:a,disabled:o});function Qc(e){return Qc="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Qc(e)}function el(e){var t=function(e){if("object"!=Qc(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=Qc(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==Qc(t)?t:t+""}function tl(e,t,n){return(t=el(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function nl(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function al(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?nl(Object(n),!0).forEach((function(t){tl(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):nl(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function ol(){return ol=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)({}).hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},ol.apply(null,arguments)}var sl=function(){function e(e){var t=this;this._insertTag=function(e){var n;n=0===t.tags.length?t.insertionPoint?t.insertionPoint.nextSibling:t.prepend?t.container.firstChild:t.before:t.tags[t.tags.length-1].nextSibling,t.container.insertBefore(e,n),t.tags.push(e)},this.isSpeedy=void 0===e.speedy||e.speedy,this.tags=[],this.ctr=0,this.nonce=e.nonce,this.key=e.key,this.container=e.container,this.prepend=e.prepend,this.insertionPoint=e.insertionPoint,this.before=null}var t=e.prototype;return t.hydrate=function(e){e.forEach(this._insertTag)},t.insert=function(e){this.ctr%(this.isSpeedy?65e3:1)==0&&this._insertTag(function(e){var t=document.createElement("style");return t.setAttribute("data-emotion",e.key),void 0!==e.nonce&&t.setAttribute("nonce",e.nonce),t.appendChild(document.createTextNode("")),t.setAttribute("data-s",""),t}(this));var t=this.tags[this.tags.length-1];if(this.isSpeedy){var n=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]}(t);try{n.insertRule(e,n.cssRules.length)}catch(e){}}else t.appendChild(document.createTextNode(e));this.ctr++},t.flush=function(){this.tags.forEach((function(e){var t;return null==(t=e.parentNode)?void 0:t.removeChild(e)})),this.tags=[],this.ctr=0},e}(),rl=Math.abs,il=String.fromCharCode,cl=Object.assign;function ll(e){return e.trim()}function pl(e,t,n){return e.replace(t,n)}function ul(e,t){return e.indexOf(t)}function dl(e,t){return 0|e.charCodeAt(t)}function ml(e,t,n){return e.slice(t,n)}function hl(e){return e.length}function fl(e){return e.length}function yl(e,t){return t.push(e),e}var gl=1,vl=1,bl=0,wl=0,_l=0,Sl="";function xl(e,t,n,a,o,s,r){return{value:e,root:t,parent:n,type:a,props:o,children:s,line:gl,column:vl,length:r,return:""}}function Cl(e,t){return cl(xl("",null,null,"",null,null,0),e,{length:-e.length},t)}function Pl(){return _l=wl>0?dl(Sl,--wl):0,vl--,10===_l&&(vl=1,gl--),_l}function jl(){return _l=wl<bl?dl(Sl,wl++):0,vl++,10===_l&&(vl=1,gl++),_l}function kl(){return dl(Sl,wl)}function El(){return wl}function Ol(e,t){return ml(Sl,e,t)}function Tl(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function Ml(e){return gl=vl=1,bl=hl(Sl=e),wl=0,[]}function Nl(e){return Sl="",e}function Dl(e){return ll(Ol(wl-1,Rl(91===e?e+2:40===e?e+1:e)))}function Il(e){for(;(_l=kl())&&_l<33;)jl();return Tl(e)>2||Tl(_l)>3?"":" "}function Al(e,t){for(;--t&&jl()&&!(_l<48||_l>102||_l>57&&_l<65||_l>70&&_l<97););return Ol(e,El()+(t<6&&32==kl()&&32==jl()))}function Rl(e){for(;jl();)switch(_l){case e:return wl;case 34:case 39:34!==e&&39!==e&&Rl(_l);break;case 40:41===e&&Rl(e);break;case 92:jl()}return wl}function Ll(e,t){for(;jl()&&e+_l!==57&&(e+_l!==84||47!==kl()););return"/*"+Ol(t,wl-1)+"*"+il(47===e?e:jl())}function Fl(e){for(;!Tl(kl());)jl();return Ol(e,wl)}var Bl="-ms-",Vl="-moz-",$l="-webkit-",Ul="comm",zl="rule",Hl="decl",Wl="@keyframes";function Gl(e,t){for(var n="",a=fl(e),o=0;o<a;o++)n+=t(e[o],o,e,t)||"";return n}function Yl(e,t,n,a){switch(e.type){case"@layer":if(e.children.length)break;case"@import":case Hl:return e.return=e.return||e.value;case Ul:return"";case Wl:return e.return=e.value+"{"+Gl(e.children,a)+"}";case zl:e.value=e.props.join(",")}return hl(n=Gl(e.children,a))?e.return=e.value+"{"+n+"}":""}function ql(e){return Nl(Xl("",null,null,null,[""],e=Ml(e),0,[0],e))}function Xl(e,t,n,a,o,s,r,i,c){for(var l=0,p=0,u=r,d=0,m=0,h=0,f=1,y=1,g=1,v=0,b="",w=o,_=s,S=a,x=b;y;)switch(h=v,v=jl()){case 40:if(108!=h&&58==dl(x,u-1)){-1!=ul(x+=pl(Dl(v),"&","&\f"),"&\f")&&(g=-1);break}case 34:case 39:case 91:x+=Dl(v);break;case 9:case 10:case 13:case 32:x+=Il(h);break;case 92:x+=Al(El()-1,7);continue;case 47:switch(kl()){case 42:case 47:yl(Zl(Ll(jl(),El()),t,n),c);break;default:x+="/"}break;case 123*f:i[l++]=hl(x)*g;case 125*f:case 59:case 0:switch(v){case 0:case 125:y=0;case 59+p:-1==g&&(x=pl(x,/\f/g,"")),m>0&&hl(x)-u&&yl(m>32?Jl(x+";",a,n,u-1):Jl(pl(x," ","")+";",a,n,u-2),c);break;case 59:x+=";";default:if(yl(S=Kl(x,t,n,l,p,o,i,b,w=[],_=[],u),s),123===v)if(0===p)Xl(x,t,S,S,w,s,u,i,_);else switch(99===d&&110===dl(x,3)?100:d){case 100:case 108:case 109:case 115:Xl(e,S,S,a&&yl(Kl(e,S,S,0,0,o,i,b,o,w=[],u),_),o,_,u,i,a?w:_);break;default:Xl(x,S,S,S,[""],_,0,i,_)}}l=p=m=0,f=g=1,b=x="",u=r;break;case 58:u=1+hl(x),m=h;default:if(f<1)if(123==v)--f;else if(125==v&&0==f++&&125==Pl())continue;switch(x+=il(v),v*f){case 38:g=p>0?1:(x+="\f",-1);break;case 44:i[l++]=(hl(x)-1)*g,g=1;break;case 64:45===kl()&&(x+=Dl(jl())),d=kl(),p=u=hl(b=x+=Fl(El())),v++;break;case 45:45===h&&2==hl(x)&&(f=0)}}return s}function Kl(e,t,n,a,o,s,r,i,c,l,p){for(var u=o-1,d=0===o?s:[""],m=fl(d),h=0,f=0,y=0;h<a;++h)for(var g=0,v=ml(e,u+1,u=rl(f=r[h])),b=e;g<m;++g)(b=ll(f>0?d[g]+" "+v:pl(v,/&\f/g,d[g])))&&(c[y++]=b);return xl(e,t,n,0===o?zl:i,c,l,p)}function Zl(e,t,n){return xl(e,t,n,Ul,il(_l),ml(e,2,-2),0)}function Jl(e,t,n,a){return xl(e,t,n,Hl,ml(e,0,a),ml(e,a+1,-1),a)}var Ql=function(e,t,n){for(var a=0,o=0;a=o,o=kl(),38===a&&12===o&&(t[n]=1),!Tl(o);)jl();return Ol(e,wl)},ep=new WeakMap,tp=function(e){if("rule"===e.type&&e.parent&&!(e.length<1)){for(var t=e.value,n=e.parent,a=e.column===n.column&&e.line===n.line;"rule"!==n.type;)if(!(n=n.parent))return;if((1!==e.props.length||58===t.charCodeAt(0)||ep.get(n))&&!a){ep.set(e,!0);for(var o=[],s=function(e,t){return Nl(function(e,t){var n=-1,a=44;do{switch(Tl(a)){case 0:38===a&&12===kl()&&(t[n]=1),e[n]+=Ql(wl-1,t,n);break;case 2:e[n]+=Dl(a);break;case 4:if(44===a){e[++n]=58===kl()?"&\f":"",t[n]=e[n].length;break}default:e[n]+=il(a)}}while(a=jl());return e}(Ml(e),t))}(t,o),r=n.props,i=0,c=0;i<s.length;i++)for(var l=0;l<r.length;l++,c++)e.props[c]=o[i]?s[i].replace(/&\f/g,r[l]):r[l]+" "+s[i]}}},np=function(e){if("decl"===e.type){var t=e.value;108===t.charCodeAt(0)&&98===t.charCodeAt(2)&&(e.return="",e.value="")}};function ap(e,t){switch(function(e,t){return 45^dl(e,0)?(((t<<2^dl(e,0))<<2^dl(e,1))<<2^dl(e,2))<<2^dl(e,3):0}(e,t)){case 5103:return $l+"print-"+e+e;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return $l+e+e;case 5349:case 4246:case 4810:case 6968:case 2756:return $l+e+Vl+e+Bl+e+e;case 6828:case 4268:return $l+e+Bl+e+e;case 6165:return $l+e+Bl+"flex-"+e+e;case 5187:return $l+e+pl(e,/(\w+).+(:[^]+)/,$l+"box-$1$2"+Bl+"flex-$1$2")+e;case 5443:return $l+e+Bl+"flex-item-"+pl(e,/flex-|-self/,"")+e;case 4675:return $l+e+Bl+"flex-line-pack"+pl(e,/align-content|flex-|-self/,"")+e;case 5548:return $l+e+Bl+pl(e,"shrink","negative")+e;case 5292:return $l+e+Bl+pl(e,"basis","preferred-size")+e;case 6060:return $l+"box-"+pl(e,"-grow","")+$l+e+Bl+pl(e,"grow","positive")+e;case 4554:return $l+pl(e,/([^-])(transform)/g,"$1"+$l+"$2")+e;case 6187:return pl(pl(pl(e,/(zoom-|grab)/,$l+"$1"),/(image-set)/,$l+"$1"),e,"")+e;case 5495:case 3959:return pl(e,/(image-set\([^]*)/,$l+"$1$`$1");case 4968:return pl(pl(e,/(.+:)(flex-)?(.*)/,$l+"box-pack:$3"+Bl+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+$l+e+e;case 4095:case 3583:case 4068:case 2532:return pl(e,/(.+)-inline(.+)/,$l+"$1$2")+e;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(hl(e)-1-t>6)switch(dl(e,t+1)){case 109:if(45!==dl(e,t+4))break;case 102:return pl(e,/(.+:)(.+)-([^]+)/,"$1"+$l+"$2-$3$1"+Vl+(108==dl(e,t+3)?"$3":"$2-$3"))+e;case 115:return~ul(e,"stretch")?ap(pl(e,"stretch","fill-available"),t)+e:e}break;case 4949:if(115!==dl(e,t+1))break;case 6444:switch(dl(e,hl(e)-3-(~ul(e,"!important")&&10))){case 107:return pl(e,":",":"+$l)+e;case 101:return pl(e,/(.+:)([^;!]+)(;|!.+)?/,"$1"+$l+(45===dl(e,14)?"inline-":"")+"box$3$1"+$l+"$2$3$1"+Bl+"$2box$3")+e}break;case 5936:switch(dl(e,t+11)){case 114:return $l+e+Bl+pl(e,/[svh]\w+-[tblr]{2}/,"tb")+e;case 108:return $l+e+Bl+pl(e,/[svh]\w+-[tblr]{2}/,"tb-rl")+e;case 45:return $l+e+Bl+pl(e,/[svh]\w+-[tblr]{2}/,"lr")+e}return $l+e+Bl+e+e}return e}var op=[function(e,t,n,a){if(e.length>-1&&!e.return)switch(e.type){case Hl:e.return=ap(e.value,e.length);break;case Wl:return Gl([Cl(e,{value:pl(e.value,"@","@"+$l)})],a);case zl:if(e.length)return function(e,t){return e.map(t).join("")}(e.props,(function(t){switch(function(e){return(e=/(::plac\w+|:read-\w+)/.exec(e))?e[0]:e}(t)){case":read-only":case":read-write":return Gl([Cl(e,{props:[pl(t,/:(read-\w+)/,":-moz-$1")]})],a);case"::placeholder":return Gl([Cl(e,{props:[pl(t,/:(plac\w+)/,":"+$l+"input-$1")]}),Cl(e,{props:[pl(t,/:(plac\w+)/,":-moz-$1")]}),Cl(e,{props:[pl(t,/:(plac\w+)/,Bl+"input-$1")]})],a)}return""}))}}],sp=function(e){var t=e.key;if("css"===t){var n=document.querySelectorAll("style[data-emotion]:not([data-s])");Array.prototype.forEach.call(n,(function(e){-1!==e.getAttribute("data-emotion").indexOf(" ")&&(document.head.appendChild(e),e.setAttribute("data-s",""))}))}var a,o,s=e.stylisPlugins||op,r={},i=[];a=e.container||document.head,Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="'+t+' "]'),(function(e){for(var t=e.getAttribute("data-emotion").split(" "),n=1;n<t.length;n++)r[t[n]]=!0;i.push(e)}));var c,l,p,u,d=[Yl,(u=function(e){c.insert(e)},function(e){e.root||(e=e.return)&&u(e)})],m=(l=[tp,np].concat(s,d),p=fl(l),function(e,t,n,a){for(var o="",s=0;s<p;s++)o+=l[s](e,t,n,a)||"";return o});o=function(e,t,n,a){c=n,Gl(ql(e?e+"{"+t.styles+"}":t.styles),m),a&&(h.inserted[t.name]=!0)};var h={key:t,sheet:new sl({key:t,container:a,nonce:e.nonce,speedy:e.speedy,prepend:e.prepend,insertionPoint:e.insertionPoint}),nonce:e.nonce,inserted:r,registered:{},insert:o};return h.sheet.hydrate(i),h},rp=function(e,t,n){var a=e.key+"-"+t.name;!1===n&&void 0===e.registered[a]&&(e.registered[a]=t.styles)},ip={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,scale:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1};function cp(e){var t=Object.create(null);return function(n){return void 0===t[n]&&(t[n]=e(n)),t[n]}}var lp=/[A-Z]|^ms/g,pp=/_EMO_([^_]+?)_([^]*?)_EMO_/g,up=function(e){return 45===e.charCodeAt(1)},dp=function(e){return null!=e&&"boolean"!=typeof e},mp=cp((function(e){return up(e)?e:e.replace(lp,"-$&").toLowerCase()})),hp=function(e,t){switch(e){case"animation":case"animationName":if("string"==typeof t)return t.replace(pp,(function(e,t,n){return yp={name:t,styles:n,next:yp},t}))}return 1===ip[e]||up(e)||"number"!=typeof t||0===t?t:t+"px"};function fp(e,t,n){if(null==n)return"";var a=n;if(void 0!==a.__emotion_styles)return a;switch(typeof n){case"boolean":return"";case"object":var o=n;if(1===o.anim)return yp={name:o.name,styles:o.styles,next:yp},o.name;var s=n;if(void 0!==s.styles){var r=s.next;if(void 0!==r)for(;void 0!==r;)yp={name:r.name,styles:r.styles,next:yp},r=r.next;return s.styles+";"}return function(e,t,n){var a="";if(Array.isArray(n))for(var o=0;o<n.length;o++)a+=fp(e,t,n[o])+";";else for(var s in n){var r=n[s];if("object"!=typeof r){var i=r;null!=t&&void 0!==t[i]?a+=s+"{"+t[i]+"}":dp(i)&&(a+=mp(s)+":"+hp(s,i)+";")}else if(!Array.isArray(r)||"string"!=typeof r[0]||null!=t&&void 0!==t[r[0]]){var c=fp(e,t,r);switch(s){case"animation":case"animationName":a+=mp(s)+":"+c+";";break;default:a+=s+"{"+c+"}"}}else for(var l=0;l<r.length;l++)dp(r[l])&&(a+=mp(s)+":"+hp(s,r[l])+";")}return a}(e,t,n);case"function":if(void 0!==e){var i=yp,c=n(e);return yp=i,fp(e,t,c)}}var l=n;if(null==t)return l;var p=t[l];return void 0!==p?p:l}var yp,gp=/label:\s*([^\s;{]+)\s*(;|$)/g;function vp(e,t,n){if(1===e.length&&"object"==typeof e[0]&&null!==e[0]&&void 0!==e[0].styles)return e[0];var a=!0,o="";yp=void 0;var s=e[0];null==s||void 0===s.raw?(a=!1,o+=fp(n,t,s)):o+=s[0];for(var r=1;r<e.length;r++)o+=fp(n,t,e[r]),a&&(o+=s[r]);gp.lastIndex=0;for(var i,c="";null!==(i=gp.exec(o));)c+="-"+i[1];var l=function(e){for(var t,n=0,a=0,o=e.length;o>=4;++a,o-=4)t=***********(65535&(t=255&e.charCodeAt(a)|(255&e.charCodeAt(++a))<<8|(255&e.charCodeAt(++a))<<16|(255&e.charCodeAt(++a))<<24))+(59797*(t>>>16)<<16),n=***********(65535&(t^=t>>>24))+(59797*(t>>>16)<<16)^***********(65535&n)+(59797*(n>>>16)<<16);switch(o){case 3:n^=(255&e.charCodeAt(a+2))<<16;case 2:n^=(255&e.charCodeAt(a+1))<<8;case 1:n=***********(65535&(n^=255&e.charCodeAt(a)))+(59797*(n>>>16)<<16)}return(((n=***********(65535&(n^=n>>>13))+(59797*(n>>>16)<<16))^n>>>15)>>>0).toString(36)}(o)+c;return{name:l,styles:o,next:yp}}var bp=!!G.useInsertionEffect&&G.useInsertionEffect,_p=bp||function(e){return e()},Sp=(bp||G.useLayoutEffect,G.createContext("undefined"!=typeof HTMLElement?sp({key:"css"}):null)),xp=(Sp.Provider,function(e){return(0,G.forwardRef)((function(t,n){var a=(0,G.useContext)(Sp);return e(t,a,n)}))}),Cp=G.createContext({}),Pp={}.hasOwnProperty,jp="__EMOTION_TYPE_PLEASE_DO_NOT_USE__",kp=function(e){var t=e.cache,n=e.serialized,a=e.isStringTag;return rp(t,n,a),_p((function(){return function(e,t,n){rp(e,t,n);var a=e.key+"-"+t.name;if(void 0===e.inserted[t.name]){var o=t;do{e.insert(t===o?"."+a:"",o,e.sheet,!0),o=o.next}while(void 0!==o)}}(t,n,a)})),null},Ep=xp((function(e,t,n){var a=e.css;"string"==typeof a&&void 0!==t.registered[a]&&(a=t.registered[a]);var o=e[jp],s=[a],r="";"string"==typeof e.className?r=function(e,t,n){var a="";return n.split(" ").forEach((function(n){void 0!==e[n]?t.push(e[n]+";"):n&&(a+=n+" ")})),a}(t.registered,s,e.className):null!=e.className&&(r=e.className+" ");var i=vp(s,void 0,G.useContext(Cp));r+=t.key+"-"+i.name;var c={};for(var l in e)Pp.call(e,l)&&"css"!==l&&l!==jp&&(c[l]=e[l]);return c.className=r,n&&(c.ref=n),G.createElement(G.Fragment,null,G.createElement(kp,{cache:t,serialized:i,isStringTag:"string"==typeof o}),G.createElement(o,c))})),Op=(n(146),function(e,t){var n=arguments;if(null==t||!Pp.call(t,"css"))return G.createElement.apply(void 0,n);var a=n.length,o=new Array(a);o[0]=Ep,o[1]=function(e,t){var n={};for(var a in t)Pp.call(t,a)&&(n[a]=t[a]);return n[jp]=e,n}(e,t);for(var s=2;s<a;s++)o[s]=n[s];return G.createElement.apply(null,o)});function Tp(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return vp(t)}function Mp(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=Array(t);n<t;n++)a[n]=e[n];return a}function Np(e,t){if(e){if("string"==typeof e)return Mp(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Mp(e,t):void 0}}function Dp(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var a,o,s,r,i=[],c=!0,l=!1;try{if(s=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(a=s.call(n)).done)&&(i.push(a.value),i.length!==t);c=!0);}catch(e){l=!0,o=e}finally{try{if(!c&&null!=n.return&&(r=n.return(),Object(r)!==r))return}finally{if(l)throw o}}return i}}(e,t)||Np(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ip(e,t){if(null==e)return{};var n,a,o=function(e,t){if(null==e)return{};var n={};for(var a in e)if({}.hasOwnProperty.call(e,a)){if(t.includes(a))continue;n[a]=e[a]}return n}(e,t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(a=0;a<s.length;a++)n=s[a],t.includes(n)||{}.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}var Ap=n(795);const Rp=Math.min,Lp=Math.max,Fp=Math.round,Bp=Math.floor,Vp=e=>({x:e,y:e});function $p(){return"undefined"!=typeof window}function Up(e){return Wp(e)?(e.nodeName||"").toLowerCase():"#document"}function zp(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function Hp(e){var t;return null==(t=(Wp(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function Wp(e){return!!$p()&&(e instanceof Node||e instanceof zp(e).Node)}function Gp(e){return!!$p()&&(e instanceof Element||e instanceof zp(e).Element)}function Yp(e){return!!$p()&&(e instanceof HTMLElement||e instanceof zp(e).HTMLElement)}function qp(e){return!(!$p()||"undefined"==typeof ShadowRoot)&&(e instanceof ShadowRoot||e instanceof zp(e).ShadowRoot)}function Xp(e){const{overflow:t,overflowX:n,overflowY:a,display:o}=Kp(e);return/auto|scroll|overlay|hidden|clip/.test(t+a+n)&&!["inline","contents"].includes(o)}function Kp(e){return zp(e).getComputedStyle(e)}function Zp(e){const t=function(e){if("html"===Up(e))return e;const t=e.assignedSlot||e.parentNode||qp(e)&&e.host||Hp(e);return qp(t)?t.host:t}(e);return function(e){return["html","body","#document"].includes(Up(e))}(t)?e.ownerDocument?e.ownerDocument.body:e.body:Yp(t)&&Xp(t)?t:Zp(t)}function Jp(e,t,n){var a;void 0===t&&(t=[]),void 0===n&&(n=!0);const o=Zp(e),s=o===(null==(a=e.ownerDocument)?void 0:a.body),r=zp(o);if(s){const e=Qp(r);return t.concat(r,r.visualViewport||[],Xp(o)?o:[],e&&n?Jp(e):[])}return t.concat(o,Jp(o,[],n))}function Qp(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function eu(e){return Gp(e)?e:e.contextElement}function tu(e){const t=eu(e);if(!Yp(t))return Vp(1);const n=t.getBoundingClientRect(),{width:a,height:o,$:s}=function(e){const t=Kp(e);let n=parseFloat(t.width)||0,a=parseFloat(t.height)||0;const o=Yp(e),s=o?e.offsetWidth:n,r=o?e.offsetHeight:a,i=Fp(n)!==s||Fp(a)!==r;return i&&(n=s,a=r),{width:n,height:a,$:i}}(t);let r=(s?Fp(n.width):n.width)/a,i=(s?Fp(n.height):n.height)/o;return r&&Number.isFinite(r)||(r=1),i&&Number.isFinite(i)||(i=1),{x:r,y:i}}const nu=Vp(0);function au(e){const t=zp(e);return"undefined"!=typeof CSS&&CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:nu}function ou(e,t,n,a){void 0===t&&(t=!1),void 0===n&&(n=!1);const o=e.getBoundingClientRect(),s=eu(e);let r=Vp(1);t&&(a?Gp(a)&&(r=tu(a)):r=tu(e));const i=function(e,t,n){return void 0===t&&(t=!1),!(!n||t&&n!==zp(e))&&t}(s,n,a)?au(s):Vp(0);let c=(o.left+i.x)/r.x,l=(o.top+i.y)/r.y,p=o.width/r.x,u=o.height/r.y;if(s){const e=zp(s),t=a&&Gp(a)?zp(a):a;let n=e,o=Qp(n);for(;o&&a&&t!==n;){const e=tu(o),t=o.getBoundingClientRect(),a=Kp(o),s=t.left+(o.clientLeft+parseFloat(a.paddingLeft))*e.x,r=t.top+(o.clientTop+parseFloat(a.paddingTop))*e.y;c*=e.x,l*=e.y,p*=e.x,u*=e.y,c+=s,l+=r,n=zp(o),o=Qp(n)}}return function(e){const{x:t,y:n,width:a,height:o}=e;return{width:a,height:o,top:n,left:t,right:t+a,bottom:n+o,x:t,y:n}}({width:p,height:u,x:c,y:l})}const su=G.useLayoutEffect;var ru=["className","clearValue","cx","getStyles","getClassNames","getValue","hasValue","isMulti","isRtl","options","selectOption","selectProps","setValue","theme"],iu=function(){};function cu(e,t){return t?"-"===t[0]?e+t:e+"__"+t:e}function lu(e,t){for(var n=arguments.length,a=new Array(n>2?n-2:0),o=2;o<n;o++)a[o-2]=arguments[o];var s=[].concat(a);if(t&&e)for(var r in t)t.hasOwnProperty(r)&&t[r]&&s.push("".concat(cu(e,r)));return s.filter((function(e){return e})).map((function(e){return String(e).trim()})).join(" ")}var pu=function(e){return t=e,Array.isArray(t)?e.filter(Boolean):"object"===Qc(e)&&null!==e?[e]:[];var t},uu=function(e){return e.className,e.clearValue,e.cx,e.getStyles,e.getClassNames,e.getValue,e.hasValue,e.isMulti,e.isRtl,e.options,e.selectOption,e.selectProps,e.setValue,e.theme,al({},Ip(e,ru))},du=function(e,t,n){var a=e.cx,o=e.getStyles,s=e.getClassNames,r=e.className;return{css:o(t,e),className:a(null!=n?n:{},s(t,e),r)}};function mu(e){return[document.documentElement,document.body,window].indexOf(e)>-1}function hu(e){return mu(e)?window.pageYOffset:e.scrollTop}function fu(e,t){mu(e)?window.scrollTo(0,t):e.scrollTop=t}function yu(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:200,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:iu,o=hu(e),s=t-o,r=0;!function t(){var i,c=s*((i=(i=r+=10)/n-1)*i*i+1)+o;fu(e,c),r<n?window.requestAnimationFrame(t):a(e)}()}function gu(e,t){var n=e.getBoundingClientRect(),a=t.getBoundingClientRect(),o=t.offsetHeight/3;a.bottom+o>n.bottom?fu(e,Math.min(t.offsetTop+t.clientHeight-e.offsetHeight+o,e.scrollHeight)):a.top-o<n.top&&fu(e,Math.max(t.offsetTop-o,0))}function vu(){try{return document.createEvent("TouchEvent"),!0}catch(e){return!1}}var bu=!1,wu={get passive(){return bu=!0}},_u="undefined"!=typeof window?window:{};_u.addEventListener&&_u.removeEventListener&&(_u.addEventListener("p",iu,wu),_u.removeEventListener("p",iu,!1));var Su=bu;function xu(e){return null!=e}function Cu(e,t,n){return e?t:n}var Pu=["children","innerProps"],ju=["children","innerProps"];var ku,Eu,Ou,Tu=function(e){return"auto"===e?"bottom":e},Mu=(0,G.createContext)(null),Nu=function(e){var t=e.children,n=e.minMenuHeight,a=e.maxMenuHeight,o=e.menuPlacement,s=e.menuPosition,r=e.menuShouldScrollIntoView,i=e.theme,c=((0,G.useContext)(Mu)||{}).setPortalPlacement,l=(0,G.useRef)(null),p=Dp((0,G.useState)(a),2),u=p[0],d=p[1],m=Dp((0,G.useState)(null),2),h=m[0],f=m[1],y=i.spacing.controlHeight;return su((function(){var e=l.current;if(e){var t="fixed"===s,i=function(e){var t=e.maxHeight,n=e.menuEl,a=e.minHeight,o=e.placement,s=e.shouldScroll,r=e.isFixedPosition,i=e.controlHeight,c=function(e){var t=getComputedStyle(e),n="absolute"===t.position,a=/(auto|scroll)/;if("fixed"===t.position)return document.documentElement;for(var o=e;o=o.parentElement;)if(t=getComputedStyle(o),(!n||"static"!==t.position)&&a.test(t.overflow+t.overflowY+t.overflowX))return o;return document.documentElement}(n),l={placement:"bottom",maxHeight:t};if(!n||!n.offsetParent)return l;var p,u=c.getBoundingClientRect().height,d=n.getBoundingClientRect(),m=d.bottom,h=d.height,f=d.top,y=n.offsetParent.getBoundingClientRect().top,g=r||mu(p=c)?window.innerHeight:p.clientHeight,v=hu(c),b=parseInt(getComputedStyle(n).marginBottom,10),w=parseInt(getComputedStyle(n).marginTop,10),_=y-w,S=g-f,x=_+v,C=u-v-f,P=m-g+v+b,j=v+f-w,k=160;switch(o){case"auto":case"bottom":if(S>=h)return{placement:"bottom",maxHeight:t};if(C>=h&&!r)return s&&yu(c,P,k),{placement:"bottom",maxHeight:t};if(!r&&C>=a||r&&S>=a)return s&&yu(c,P,k),{placement:"bottom",maxHeight:r?S-b:C-b};if("auto"===o||r){var E=t,O=r?_:x;return O>=a&&(E=Math.min(O-b-i,t)),{placement:"top",maxHeight:E}}if("bottom"===o)return s&&fu(c,P),{placement:"bottom",maxHeight:t};break;case"top":if(_>=h)return{placement:"top",maxHeight:t};if(x>=h&&!r)return s&&yu(c,j,k),{placement:"top",maxHeight:t};if(!r&&x>=a||r&&_>=a){var T=t;return(!r&&x>=a||r&&_>=a)&&(T=r?_-w:x-w),s&&yu(c,j,k),{placement:"top",maxHeight:T}}return{placement:"bottom",maxHeight:t};default:throw new Error('Invalid placement provided "'.concat(o,'".'))}return l}({maxHeight:a,menuEl:e,minHeight:n,placement:o,shouldScroll:r&&!t,isFixedPosition:t,controlHeight:y});d(i.maxHeight),f(i.placement),null==c||c(i.placement)}}),[a,o,s,r,n,c,y]),t({ref:l,placerProps:al(al({},e),{},{placement:h||Tu(o),maxHeight:u})})},Du=function(e,t){var n=e.theme,a=n.spacing.baseUnit,o=n.colors;return al({textAlign:"center"},t?{}:{color:o.neutral40,padding:"".concat(2*a,"px ").concat(3*a,"px")})},Iu=Du,Au=Du,Ru=["size"],Lu=["innerProps","isRtl","size"],Fu={name:"8mmkcg",styles:"display:inline-block;fill:currentColor;line-height:1;stroke:currentColor;stroke-width:0"},Bu=function(e){var t=e.size,n=Ip(e,Ru);return Op("svg",ol({height:t,width:t,viewBox:"0 0 20 20","aria-hidden":"true",focusable:"false",css:Fu},n))},Vu=function(e){return Op(Bu,ol({size:20},e),Op("path",{d:"M14.348 14.849c-0.469 0.469-1.229 0.469-1.697 0l-2.651-3.030-2.651 3.029c-0.469 0.469-1.229 0.469-1.697 0-0.469-0.469-0.469-1.229 0-1.697l2.758-3.15-2.759-3.152c-0.469-0.469-0.469-1.228 0-1.697s1.228-0.469 1.697 0l2.652 3.031 2.651-3.031c0.469-0.469 1.228-0.469 1.697 0s0.469 1.229 0 1.697l-2.758 3.152 2.758 3.15c0.469 0.469 0.469 1.229 0 1.698z"}))},$u=function(e){return Op(Bu,ol({size:20},e),Op("path",{d:"M4.516 7.548c0.436-0.446 1.043-0.481 1.576 0l3.908 3.747 3.908-3.747c0.533-0.481 1.141-0.446 1.574 0 0.436 0.445 0.408 1.197 0 1.615-0.406 0.418-4.695 4.502-4.695 4.502-0.217 0.223-0.502 0.335-0.787 0.335s-0.57-0.112-0.789-0.335c0 0-4.287-4.084-4.695-4.502s-0.436-1.17 0-1.615z"}))},Uu=function(e,t){var n=e.isFocused,a=e.theme,o=a.spacing.baseUnit,s=a.colors;return al({label:"indicatorContainer",display:"flex",transition:"color 150ms"},t?{}:{color:n?s.neutral60:s.neutral20,padding:2*o,":hover":{color:n?s.neutral80:s.neutral40}})},zu=Uu,Hu=Uu,Wu=function(){var e=Tp.apply(void 0,arguments),t="animation-"+e.name;return{name:t,styles:"@keyframes "+t+"{"+e.styles+"}",anim:1,toString:function(){return"_EMO_"+this.name+"_"+this.styles+"_EMO_"}}}(ku||(Eu=["\n  0%, 80%, 100% { opacity: 0; }\n  40% { opacity: 1; }\n"],Ou||(Ou=Eu.slice(0)),ku=Object.freeze(Object.defineProperties(Eu,{raw:{value:Object.freeze(Ou)}})))),Gu=function(e){var t=e.delay,n=e.offset;return Op("span",{css:Tp({animation:"".concat(Wu," 1s ease-in-out ").concat(t,"ms infinite;"),backgroundColor:"currentColor",borderRadius:"1em",display:"inline-block",marginLeft:n?"1em":void 0,height:"1em",verticalAlign:"top",width:"1em"},"","")})},Yu=["data"],qu=["innerRef","isDisabled","isHidden","inputClassName"],Xu={gridArea:"1 / 2",font:"inherit",minWidth:"2px",border:0,margin:0,outline:0,padding:0},Ku={flex:"1 1 auto",display:"inline-grid",gridArea:"1 / 1 / 2 / 3",gridTemplateColumns:"0 min-content","&:after":al({content:'attr(data-value) " "',visibility:"hidden",whiteSpace:"pre"},Xu)},Zu=function(e){return al({label:"input",color:"inherit",background:0,opacity:e?0:1,width:"100%"},Xu)},Ju=function(e){var t=e.children,n=e.innerProps;return Op("div",n,t)},Qu={ClearIndicator:function(e){var t=e.children,n=e.innerProps;return Op("div",ol({},du(e,"clearIndicator",{indicator:!0,"clear-indicator":!0}),n),t||Op(Vu,null))},Control:function(e){var t=e.children,n=e.isDisabled,a=e.isFocused,o=e.innerRef,s=e.innerProps,r=e.menuIsOpen;return Op("div",ol({ref:o},du(e,"control",{control:!0,"control--is-disabled":n,"control--is-focused":a,"control--menu-is-open":r}),s,{"aria-disabled":n||void 0}),t)},DropdownIndicator:function(e){var t=e.children,n=e.innerProps;return Op("div",ol({},du(e,"dropdownIndicator",{indicator:!0,"dropdown-indicator":!0}),n),t||Op($u,null))},DownChevron:$u,CrossIcon:Vu,Group:function(e){var t=e.children,n=e.cx,a=e.getStyles,o=e.getClassNames,s=e.Heading,r=e.headingProps,i=e.innerProps,c=e.label,l=e.theme,p=e.selectProps;return Op("div",ol({},du(e,"group",{group:!0}),i),Op(s,ol({},r,{selectProps:p,theme:l,getStyles:a,getClassNames:o,cx:n}),c),Op("div",null,t))},GroupHeading:function(e){var t=uu(e);t.data;var n=Ip(t,Yu);return Op("div",ol({},du(e,"groupHeading",{"group-heading":!0}),n))},IndicatorsContainer:function(e){var t=e.children,n=e.innerProps;return Op("div",ol({},du(e,"indicatorsContainer",{indicators:!0}),n),t)},IndicatorSeparator:function(e){var t=e.innerProps;return Op("span",ol({},t,du(e,"indicatorSeparator",{"indicator-separator":!0})))},Input:function(e){var t=e.cx,n=e.value,a=uu(e),o=a.innerRef,s=a.isDisabled,r=a.isHidden,i=a.inputClassName,c=Ip(a,qu);return Op("div",ol({},du(e,"input",{"input-container":!0}),{"data-value":n||""}),Op("input",ol({className:t({input:!0},i),ref:o,style:Zu(r),disabled:s},c)))},LoadingIndicator:function(e){var t=e.innerProps,n=e.isRtl,a=e.size,o=void 0===a?4:a,s=Ip(e,Lu);return Op("div",ol({},du(al(al({},s),{},{innerProps:t,isRtl:n,size:o}),"loadingIndicator",{indicator:!0,"loading-indicator":!0}),t),Op(Gu,{delay:0,offset:n}),Op(Gu,{delay:160,offset:!0}),Op(Gu,{delay:320,offset:!n}))},Menu:function(e){var t=e.children,n=e.innerRef,a=e.innerProps;return Op("div",ol({},du(e,"menu",{menu:!0}),{ref:n},a),t)},MenuList:function(e){var t=e.children,n=e.innerProps,a=e.innerRef,o=e.isMulti;return Op("div",ol({},du(e,"menuList",{"menu-list":!0,"menu-list--is-multi":o}),{ref:a},n),t)},MenuPortal:function(e){var t=e.appendTo,n=e.children,a=e.controlElement,o=e.innerProps,s=e.menuPlacement,r=e.menuPosition,i=(0,G.useRef)(null),c=(0,G.useRef)(null),l=Dp((0,G.useState)(Tu(s)),2),p=l[0],u=l[1],d=(0,G.useMemo)((function(){return{setPortalPlacement:u}}),[]),m=Dp((0,G.useState)(null),2),h=m[0],f=m[1],y=(0,G.useCallback)((function(){if(a){var e=function(e){var t=e.getBoundingClientRect();return{bottom:t.bottom,height:t.height,left:t.left,right:t.right,top:t.top,width:t.width}}(a),t="fixed"===r?0:window.pageYOffset,n=e[p]+t;n===(null==h?void 0:h.offset)&&e.left===(null==h?void 0:h.rect.left)&&e.width===(null==h?void 0:h.rect.width)||f({offset:n,rect:e})}}),[a,r,p,null==h?void 0:h.offset,null==h?void 0:h.rect.left,null==h?void 0:h.rect.width]);su((function(){y()}),[y]);var g=(0,G.useCallback)((function(){"function"==typeof c.current&&(c.current(),c.current=null),a&&i.current&&(c.current=function(e,t,n,a){void 0===a&&(a={});const{ancestorScroll:o=!0,ancestorResize:s=!0,elementResize:r="function"==typeof ResizeObserver,layoutShift:i="function"==typeof IntersectionObserver,animationFrame:c=!1}=a,l=eu(e),p=o||s?[...l?Jp(l):[],...Jp(t)]:[];p.forEach((e=>{o&&e.addEventListener("scroll",n,{passive:!0}),s&&e.addEventListener("resize",n)}));const u=l&&i?function(e,t){let n,a=null;const o=Hp(e);function s(){var e;clearTimeout(n),null==(e=a)||e.disconnect(),a=null}return function r(i,c){void 0===i&&(i=!1),void 0===c&&(c=1),s();const{left:l,top:p,width:u,height:d}=e.getBoundingClientRect();if(i||t(),!u||!d)return;const m={rootMargin:-Bp(p)+"px "+-Bp(o.clientWidth-(l+u))+"px "+-Bp(o.clientHeight-(p+d))+"px "+-Bp(l)+"px",threshold:Lp(0,Rp(1,c))||1};let h=!0;function f(e){const t=e[0].intersectionRatio;if(t!==c){if(!h)return r();t?r(!1,t):n=setTimeout((()=>{r(!1,1e-7)}),1e3)}h=!1}try{a=new IntersectionObserver(f,{...m,root:o.ownerDocument})}catch(e){a=new IntersectionObserver(f,m)}a.observe(e)}(!0),s}(l,n):null;let d,m=-1,h=null;r&&(h=new ResizeObserver((e=>{let[a]=e;a&&a.target===l&&h&&(h.unobserve(t),cancelAnimationFrame(m),m=requestAnimationFrame((()=>{var e;null==(e=h)||e.observe(t)}))),n()})),l&&!c&&h.observe(l),h.observe(t));let f=c?ou(e):null;return c&&function t(){const a=ou(e);!f||a.x===f.x&&a.y===f.y&&a.width===f.width&&a.height===f.height||n(),f=a,d=requestAnimationFrame(t)}(),n(),()=>{var e;p.forEach((e=>{o&&e.removeEventListener("scroll",n),s&&e.removeEventListener("resize",n)})),null==u||u(),null==(e=h)||e.disconnect(),h=null,c&&cancelAnimationFrame(d)}}(a,i.current,y,{elementResize:"ResizeObserver"in window}))}),[a,y]);su((function(){g()}),[g]);var v=(0,G.useCallback)((function(e){i.current=e,g()}),[g]);if(!t&&"fixed"!==r||!h)return null;var b=Op("div",ol({ref:v},du(al(al({},e),{},{offset:h.offset,position:r,rect:h.rect}),"menuPortal",{"menu-portal":!0}),o),n);return Op(Mu.Provider,{value:d},t?(0,Ap.createPortal)(b,t):b)},LoadingMessage:function(e){var t=e.children,n=void 0===t?"Loading...":t,a=e.innerProps,o=Ip(e,ju);return Op("div",ol({},du(al(al({},o),{},{children:n,innerProps:a}),"loadingMessage",{"menu-notice":!0,"menu-notice--loading":!0}),a),n)},NoOptionsMessage:function(e){var t=e.children,n=void 0===t?"No options":t,a=e.innerProps,o=Ip(e,Pu);return Op("div",ol({},du(al(al({},o),{},{children:n,innerProps:a}),"noOptionsMessage",{"menu-notice":!0,"menu-notice--no-options":!0}),a),n)},MultiValue:function(e){var t=e.children,n=e.components,a=e.data,o=e.innerProps,s=e.isDisabled,r=e.removeProps,i=e.selectProps,c=n.Container,l=n.Label,p=n.Remove;return Op(c,{data:a,innerProps:al(al({},du(e,"multiValue",{"multi-value":!0,"multi-value--is-disabled":s})),o),selectProps:i},Op(l,{data:a,innerProps:al({},du(e,"multiValueLabel",{"multi-value__label":!0})),selectProps:i},t),Op(p,{data:a,innerProps:al(al({},du(e,"multiValueRemove",{"multi-value__remove":!0})),{},{"aria-label":"Remove ".concat(t||"option")},r),selectProps:i}))},MultiValueContainer:Ju,MultiValueLabel:Ju,MultiValueRemove:function(e){var t=e.children,n=e.innerProps;return Op("div",ol({role:"button"},n),t||Op(Vu,{size:14}))},Option:function(e){var t=e.children,n=e.isDisabled,a=e.isFocused,o=e.isSelected,s=e.innerRef,r=e.innerProps;return Op("div",ol({},du(e,"option",{option:!0,"option--is-disabled":n,"option--is-focused":a,"option--is-selected":o}),{ref:s,"aria-disabled":n},r),t)},Placeholder:function(e){var t=e.children,n=e.innerProps;return Op("div",ol({},du(e,"placeholder",{placeholder:!0}),n),t)},SelectContainer:function(e){var t=e.children,n=e.innerProps,a=e.isDisabled,o=e.isRtl;return Op("div",ol({},du(e,"container",{"--is-disabled":a,"--is-rtl":o}),n),t)},SingleValue:function(e){var t=e.children,n=e.isDisabled,a=e.innerProps;return Op("div",ol({},du(e,"singleValue",{"single-value":!0,"single-value--is-disabled":n}),a),t)},ValueContainer:function(e){var t=e.children,n=e.innerProps,a=e.isMulti,o=e.hasValue;return Op("div",ol({},du(e,"valueContainer",{"value-container":!0,"value-container--is-multi":a,"value-container--has-value":o}),n),t)}},ed=["defaultInputValue","defaultMenuIsOpen","defaultValue","inputValue","menuIsOpen","onChange","onInputChange","onMenuClose","onMenuOpen","value"];function td(e,t){for(var n=0;n<t.length;n++){var a=t[n];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,el(a.key),a)}}function nd(e,t){return nd=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},nd(e,t)}function ad(e){return ad=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},ad(e)}function od(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(od=function(){return!!e})()}function sd(e){return function(e){if(Array.isArray(e))return Mp(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||Np(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var rd=Number.isNaN||function(e){return"number"==typeof e&&e!=e};function id(e,t){if(e.length!==t.length)return!1;for(var n=0;n<e.length;n++)if(!((a=e[n])===(o=t[n])||rd(a)&&rd(o)))return!1;var a,o;return!0}for(var cd={name:"7pg0cj-a11yText",styles:"label:a11yText;z-index:9999;border:0;clip:rect(1px, 1px, 1px, 1px);height:1px;width:1px;position:absolute;overflow:hidden;padding:0;white-space:nowrap"},ld=function(e){return Op("span",ol({css:cd},e))},pd={guidance:function(e){var t=e.isSearchable,n=e.isMulti,a=e.tabSelectsValue,o=e.context,s=e.isInitialFocus;switch(o){case"menu":return"Use Up and Down to choose options, press Enter to select the currently focused option, press Escape to exit the menu".concat(a?", press Tab to select the option and exit the menu":"",".");case"input":return s?"".concat(e["aria-label"]||"Select"," is focused ").concat(t?",type to refine list":"",", press Down to open the menu, ").concat(n?" press left to focus selected values":""):"";case"value":return"Use left and right to toggle between focused values, press Backspace to remove the currently focused value";default:return""}},onChange:function(e){var t=e.action,n=e.label,a=void 0===n?"":n,o=e.labels,s=e.isDisabled;switch(t){case"deselect-option":case"pop-value":case"remove-value":return"option ".concat(a,", deselected.");case"clear":return"All selected options have been cleared.";case"initial-input-focus":return"option".concat(o.length>1?"s":""," ").concat(o.join(","),", selected.");case"select-option":return"option ".concat(a,s?" is disabled. Select another option.":", selected.");default:return""}},onFocus:function(e){var t=e.context,n=e.focused,a=e.options,o=e.label,s=void 0===o?"":o,r=e.selectValue,i=e.isDisabled,c=e.isSelected,l=e.isAppleDevice,p=function(e,t){return e&&e.length?"".concat(e.indexOf(t)+1," of ").concat(e.length):""};if("value"===t&&r)return"value ".concat(s," focused, ").concat(p(r,n),".");if("menu"===t&&l){var u=i?" disabled":"",d="".concat(c?" selected":"").concat(u);return"".concat(s).concat(d,", ").concat(p(a,n),".")}return""},onFilter:function(e){var t=e.inputValue,n=e.resultsMessage;return"".concat(n).concat(t?" for search term "+t:"",".")}},ud=function(e){var t=e.ariaSelection,n=e.focusedOption,a=e.focusedValue,o=e.focusableOptions,s=e.isFocused,r=e.selectValue,i=e.selectProps,c=e.id,l=e.isAppleDevice,p=i.ariaLiveMessages,u=i.getOptionLabel,d=i.inputValue,m=i.isMulti,h=i.isOptionDisabled,f=i.isSearchable,y=i.menuIsOpen,g=i.options,v=i.screenReaderStatus,b=i.tabSelectsValue,w=i.isLoading,_=i["aria-label"],S=i["aria-live"],x=(0,G.useMemo)((function(){return al(al({},pd),p||{})}),[p]),C=(0,G.useMemo)((function(){var e,n="";if(t&&x.onChange){var a=t.option,o=t.options,s=t.removedValue,i=t.removedValues,c=t.value,l=s||a||(e=c,Array.isArray(e)?null:e),p=l?u(l):"",d=o||i||void 0,m=d?d.map(u):[],f=al({isDisabled:l&&h(l,r),label:p,labels:m},t);n=x.onChange(f)}return n}),[t,x,h,r,u]),P=(0,G.useMemo)((function(){var e="",t=n||a,s=!!(n&&r&&r.includes(n));if(t&&x.onFocus){var i={focused:t,label:u(t),isDisabled:h(t,r),isSelected:s,options:o,context:t===n?"menu":"value",selectValue:r,isAppleDevice:l};e=x.onFocus(i)}return e}),[n,a,u,h,x,o,r,l]),j=(0,G.useMemo)((function(){var e="";if(y&&g.length&&!w&&x.onFilter){var t=v({count:o.length});e=x.onFilter({inputValue:d,resultsMessage:t})}return e}),[o,d,y,x,g,v,w]),k="initial-input-focus"===(null==t?void 0:t.action),E=(0,G.useMemo)((function(){var e="";if(x.guidance){var t=a?"value":y?"menu":"input";e=x.guidance({"aria-label":_,context:t,isDisabled:n&&h(n,r),isMulti:m,isSearchable:f,tabSelectsValue:b,isInitialFocus:k})}return e}),[_,n,a,m,h,f,y,x,r,b,k]),O=Op(G.Fragment,null,Op("span",{id:"aria-selection"},C),Op("span",{id:"aria-focused"},P),Op("span",{id:"aria-results"},j),Op("span",{id:"aria-guidance"},E));return Op(G.Fragment,null,Op(ld,{id:c},k&&O),Op(ld,{"aria-live":S,"aria-atomic":"false","aria-relevant":"additions text",role:"log"},s&&!k&&O))},dd=[{base:"A",letters:"AⒶＡÀÁÂẦẤẪẨÃĀĂẰẮẴẲȦǠÄǞẢÅǺǍȀȂẠẬẶḀĄȺⱯ"},{base:"AA",letters:"Ꜳ"},{base:"AE",letters:"ÆǼǢ"},{base:"AO",letters:"Ꜵ"},{base:"AU",letters:"Ꜷ"},{base:"AV",letters:"ꜸꜺ"},{base:"AY",letters:"Ꜽ"},{base:"B",letters:"BⒷＢḂḄḆɃƂƁ"},{base:"C",letters:"CⒸＣĆĈĊČÇḈƇȻꜾ"},{base:"D",letters:"DⒹＤḊĎḌḐḒḎĐƋƊƉꝹ"},{base:"DZ",letters:"ǱǄ"},{base:"Dz",letters:"ǲǅ"},{base:"E",letters:"EⒺＥÈÉÊỀẾỄỂẼĒḔḖĔĖËẺĚȄȆẸỆȨḜĘḘḚƐƎ"},{base:"F",letters:"FⒻＦḞƑꝻ"},{base:"G",letters:"GⒼＧǴĜḠĞĠǦĢǤƓꞠꝽꝾ"},{base:"H",letters:"HⒽＨĤḢḦȞḤḨḪĦⱧⱵꞍ"},{base:"I",letters:"IⒾＩÌÍÎĨĪĬİÏḮỈǏȈȊỊĮḬƗ"},{base:"J",letters:"JⒿＪĴɈ"},{base:"K",letters:"KⓀＫḰǨḲĶḴƘⱩꝀꝂꝄꞢ"},{base:"L",letters:"LⓁＬĿĹĽḶḸĻḼḺŁȽⱢⱠꝈꝆꞀ"},{base:"LJ",letters:"Ǉ"},{base:"Lj",letters:"ǈ"},{base:"M",letters:"MⓂＭḾṀṂⱮƜ"},{base:"N",letters:"NⓃＮǸŃÑṄŇṆŅṊṈȠƝꞐꞤ"},{base:"NJ",letters:"Ǌ"},{base:"Nj",letters:"ǋ"},{base:"O",letters:"OⓄＯÒÓÔỒỐỖỔÕṌȬṎŌṐṒŎȮȰÖȪỎŐǑȌȎƠỜỚỠỞỢỌỘǪǬØǾƆƟꝊꝌ"},{base:"OI",letters:"Ƣ"},{base:"OO",letters:"Ꝏ"},{base:"OU",letters:"Ȣ"},{base:"P",letters:"PⓅＰṔṖƤⱣꝐꝒꝔ"},{base:"Q",letters:"QⓆＱꝖꝘɊ"},{base:"R",letters:"RⓇＲŔṘŘȐȒṚṜŖṞɌⱤꝚꞦꞂ"},{base:"S",letters:"SⓈＳẞŚṤŜṠŠṦṢṨȘŞⱾꞨꞄ"},{base:"T",letters:"TⓉＴṪŤṬȚŢṰṮŦƬƮȾꞆ"},{base:"TZ",letters:"Ꜩ"},{base:"U",letters:"UⓊＵÙÚÛŨṸŪṺŬÜǛǗǕǙỦŮŰǓȔȖƯỪỨỮỬỰỤṲŲṶṴɄ"},{base:"V",letters:"VⓋＶṼṾƲꝞɅ"},{base:"VY",letters:"Ꝡ"},{base:"W",letters:"WⓌＷẀẂŴẆẄẈⱲ"},{base:"X",letters:"XⓍＸẊẌ"},{base:"Y",letters:"YⓎＹỲÝŶỸȲẎŸỶỴƳɎỾ"},{base:"Z",letters:"ZⓏＺŹẐŻŽẒẔƵȤⱿⱫꝢ"},{base:"a",letters:"aⓐａẚàáâầấẫẩãāăằắẵẳȧǡäǟảåǻǎȁȃạậặḁąⱥɐ"},{base:"aa",letters:"ꜳ"},{base:"ae",letters:"æǽǣ"},{base:"ao",letters:"ꜵ"},{base:"au",letters:"ꜷ"},{base:"av",letters:"ꜹꜻ"},{base:"ay",letters:"ꜽ"},{base:"b",letters:"bⓑｂḃḅḇƀƃɓ"},{base:"c",letters:"cⓒｃćĉċčçḉƈȼꜿↄ"},{base:"d",letters:"dⓓｄḋďḍḑḓḏđƌɖɗꝺ"},{base:"dz",letters:"ǳǆ"},{base:"e",letters:"eⓔｅèéêềếễểẽēḕḗĕėëẻěȅȇẹệȩḝęḙḛɇɛǝ"},{base:"f",letters:"fⓕｆḟƒꝼ"},{base:"g",letters:"gⓖｇǵĝḡğġǧģǥɠꞡᵹꝿ"},{base:"h",letters:"hⓗｈĥḣḧȟḥḩḫẖħⱨⱶɥ"},{base:"hv",letters:"ƕ"},{base:"i",letters:"iⓘｉìíîĩīĭïḯỉǐȉȋịįḭɨı"},{base:"j",letters:"jⓙｊĵǰɉ"},{base:"k",letters:"kⓚｋḱǩḳķḵƙⱪꝁꝃꝅꞣ"},{base:"l",letters:"lⓛｌŀĺľḷḹļḽḻſłƚɫⱡꝉꞁꝇ"},{base:"lj",letters:"ǉ"},{base:"m",letters:"mⓜｍḿṁṃɱɯ"},{base:"n",letters:"nⓝｎǹńñṅňṇņṋṉƞɲŉꞑꞥ"},{base:"nj",letters:"ǌ"},{base:"o",letters:"oⓞｏòóôồốỗổõṍȭṏōṑṓŏȯȱöȫỏőǒȍȏơờớỡởợọộǫǭøǿɔꝋꝍɵ"},{base:"oi",letters:"ƣ"},{base:"ou",letters:"ȣ"},{base:"oo",letters:"ꝏ"},{base:"p",letters:"pⓟｐṕṗƥᵽꝑꝓꝕ"},{base:"q",letters:"qⓠｑɋꝗꝙ"},{base:"r",letters:"rⓡｒŕṙřȑȓṛṝŗṟɍɽꝛꞧꞃ"},{base:"s",letters:"sⓢｓßśṥŝṡšṧṣṩșşȿꞩꞅẛ"},{base:"t",letters:"tⓣｔṫẗťṭțţṱṯŧƭʈⱦꞇ"},{base:"tz",letters:"ꜩ"},{base:"u",letters:"uⓤｕùúûũṹūṻŭüǜǘǖǚủůűǔȕȗưừứữửựụṳųṷṵʉ"},{base:"v",letters:"vⓥｖṽṿʋꝟʌ"},{base:"vy",letters:"ꝡ"},{base:"w",letters:"wⓦｗẁẃŵẇẅẘẉⱳ"},{base:"x",letters:"xⓧｘẋẍ"},{base:"y",letters:"yⓨｙỳýŷỹȳẏÿỷẙỵƴɏỿ"},{base:"z",letters:"zⓩｚźẑżžẓẕƶȥɀⱬꝣ"}],md=new RegExp("["+dd.map((function(e){return e.letters})).join("")+"]","g"),hd={},fd=0;fd<dd.length;fd++)for(var yd=dd[fd],gd=0;gd<yd.letters.length;gd++)hd[yd.letters[gd]]=yd.base;var vd=function(e){return e.replace(md,(function(e){return hd[e]}))},bd=function(e,t){void 0===t&&(t=id);var n=null;function a(){for(var a=[],o=0;o<arguments.length;o++)a[o]=arguments[o];if(n&&n.lastThis===this&&t(a,n.lastArgs))return n.lastResult;var s=e.apply(this,a);return n={lastResult:s,lastArgs:a,lastThis:this},s}return a.clear=function(){n=null},a}(vd),wd=function(e){return e.replace(/^\s+|\s+$/g,"")},_d=function(e){return"".concat(e.label," ").concat(e.value)},Sd=["innerRef"];function xd(e){var t=e.innerRef,n=function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),a=1;a<t;a++)n[a-1]=arguments[a];var o=Object.entries(e).filter((function(e){var t=Dp(e,1)[0];return!n.includes(t)}));return o.reduce((function(e,t){var n=Dp(t,2),a=n[0],o=n[1];return e[a]=o,e}),{})}(Ip(e,Sd),"onExited","in","enter","exit","appear");return Op("input",ol({ref:t},n,{css:Tp({label:"dummyInput",background:0,border:0,caretColor:"transparent",fontSize:"inherit",gridArea:"1 / 1 / 2 / 3",outline:0,padding:0,width:1,color:"transparent",left:-100,opacity:0,position:"relative",transform:"scale(.01)"},"","")}))}var Cd=["boxSizing","height","overflow","paddingRight","position"],Pd={boxSizing:"border-box",overflow:"hidden",position:"relative",height:"100%"};function jd(e){e.cancelable&&e.preventDefault()}function kd(e){e.stopPropagation()}function Ed(){var e=this.scrollTop,t=this.scrollHeight,n=e+this.offsetHeight;0===e?this.scrollTop=1:n===t&&(this.scrollTop=e-1)}function Od(){return"ontouchstart"in window||navigator.maxTouchPoints}var Td=!("undefined"==typeof window||!window.document||!window.document.createElement),Md=0,Nd={capture:!1,passive:!1},Dd=function(e){var t=e.target;return t.ownerDocument.activeElement&&t.ownerDocument.activeElement.blur()},Id={name:"1kfdb0e",styles:"position:fixed;left:0;bottom:0;right:0;top:0"};function Ad(e){var t=e.children,n=e.lockEnabled,a=e.captureEnabled,o=function(e){var t=e.isEnabled,n=e.onBottomArrive,a=e.onBottomLeave,o=e.onTopArrive,s=e.onTopLeave,r=(0,G.useRef)(!1),i=(0,G.useRef)(!1),c=(0,G.useRef)(0),l=(0,G.useRef)(null),p=(0,G.useCallback)((function(e,t){if(null!==l.current){var c=l.current,p=c.scrollTop,u=c.scrollHeight,d=c.clientHeight,m=l.current,h=t>0,f=u-d-p,y=!1;f>t&&r.current&&(a&&a(e),r.current=!1),h&&i.current&&(s&&s(e),i.current=!1),h&&t>f?(n&&!r.current&&n(e),m.scrollTop=u,y=!0,r.current=!0):!h&&-t>p&&(o&&!i.current&&o(e),m.scrollTop=0,y=!0,i.current=!0),y&&function(e){e.cancelable&&e.preventDefault(),e.stopPropagation()}(e)}}),[n,a,o,s]),u=(0,G.useCallback)((function(e){p(e,e.deltaY)}),[p]),d=(0,G.useCallback)((function(e){c.current=e.changedTouches[0].clientY}),[]),m=(0,G.useCallback)((function(e){var t=c.current-e.changedTouches[0].clientY;p(e,t)}),[p]),h=(0,G.useCallback)((function(e){if(e){var t=!!Su&&{passive:!1};e.addEventListener("wheel",u,t),e.addEventListener("touchstart",d,t),e.addEventListener("touchmove",m,t)}}),[m,d,u]),f=(0,G.useCallback)((function(e){e&&(e.removeEventListener("wheel",u,!1),e.removeEventListener("touchstart",d,!1),e.removeEventListener("touchmove",m,!1))}),[m,d,u]);return(0,G.useEffect)((function(){if(t){var e=l.current;return h(e),function(){f(e)}}}),[t,h,f]),function(e){l.current=e}}({isEnabled:void 0===a||a,onBottomArrive:e.onBottomArrive,onBottomLeave:e.onBottomLeave,onTopArrive:e.onTopArrive,onTopLeave:e.onTopLeave}),s=function(e){var t=e.isEnabled,n=e.accountForScrollbars,a=void 0===n||n,o=(0,G.useRef)({}),s=(0,G.useRef)(null),r=(0,G.useCallback)((function(e){if(Td){var t=document.body,n=t&&t.style;if(a&&Cd.forEach((function(e){var t=n&&n[e];o.current[e]=t})),a&&Md<1){var s=parseInt(o.current.paddingRight,10)||0,r=document.body?document.body.clientWidth:0,i=window.innerWidth-r+s||0;Object.keys(Pd).forEach((function(e){var t=Pd[e];n&&(n[e]=t)})),n&&(n.paddingRight="".concat(i,"px"))}t&&Od()&&(t.addEventListener("touchmove",jd,Nd),e&&(e.addEventListener("touchstart",Ed,Nd),e.addEventListener("touchmove",kd,Nd))),Md+=1}}),[a]),i=(0,G.useCallback)((function(e){if(Td){var t=document.body,n=t&&t.style;Md=Math.max(Md-1,0),a&&Md<1&&Cd.forEach((function(e){var t=o.current[e];n&&(n[e]=t)})),t&&Od()&&(t.removeEventListener("touchmove",jd,Nd),e&&(e.removeEventListener("touchstart",Ed,Nd),e.removeEventListener("touchmove",kd,Nd)))}}),[a]);return(0,G.useEffect)((function(){if(t){var e=s.current;return r(e),function(){i(e)}}}),[t,r,i]),function(e){s.current=e}}({isEnabled:n});return Op(G.Fragment,null,n&&Op("div",{onClick:Dd,css:Id}),t((function(e){o(e),s(e)})))}var Rd={name:"1a0ro4n-requiredInput",styles:"label:requiredInput;opacity:0;pointer-events:none;position:absolute;bottom:0;left:0;right:0;width:100%"},Ld=function(e){var t=e.name,n=e.onFocus;return Op("input",{required:!0,name:t,tabIndex:-1,"aria-hidden":"true",onFocus:n,css:Rd,value:"",onChange:function(){}})};function Fd(e){var t;return"undefined"!=typeof window&&null!=window.navigator&&e.test((null===(t=window.navigator.userAgentData)||void 0===t?void 0:t.platform)||window.navigator.platform)}function Bd(){return Fd(/^Mac/i)}var Vd={clearIndicator:Hu,container:function(e){var t=e.isDisabled;return{label:"container",direction:e.isRtl?"rtl":void 0,pointerEvents:t?"none":void 0,position:"relative"}},control:function(e,t){var n=e.isDisabled,a=e.isFocused,o=e.theme,s=o.colors,r=o.borderRadius;return al({label:"control",alignItems:"center",cursor:"default",display:"flex",flexWrap:"wrap",justifyContent:"space-between",minHeight:o.spacing.controlHeight,outline:"0 !important",position:"relative",transition:"all 100ms"},t?{}:{backgroundColor:n?s.neutral5:s.neutral0,borderColor:n?s.neutral10:a?s.primary:s.neutral20,borderRadius:r,borderStyle:"solid",borderWidth:1,boxShadow:a?"0 0 0 1px ".concat(s.primary):void 0,"&:hover":{borderColor:a?s.primary:s.neutral30}})},dropdownIndicator:zu,group:function(e,t){var n=e.theme.spacing;return t?{}:{paddingBottom:2*n.baseUnit,paddingTop:2*n.baseUnit}},groupHeading:function(e,t){var n=e.theme,a=n.colors,o=n.spacing;return al({label:"group",cursor:"default",display:"block"},t?{}:{color:a.neutral40,fontSize:"75%",fontWeight:500,marginBottom:"0.25em",paddingLeft:3*o.baseUnit,paddingRight:3*o.baseUnit,textTransform:"uppercase"})},indicatorsContainer:function(){return{alignItems:"center",alignSelf:"stretch",display:"flex",flexShrink:0}},indicatorSeparator:function(e,t){var n=e.isDisabled,a=e.theme,o=a.spacing.baseUnit,s=a.colors;return al({label:"indicatorSeparator",alignSelf:"stretch",width:1},t?{}:{backgroundColor:n?s.neutral10:s.neutral20,marginBottom:2*o,marginTop:2*o})},input:function(e,t){var n=e.isDisabled,a=e.value,o=e.theme,s=o.spacing,r=o.colors;return al(al({visibility:n?"hidden":"visible",transform:a?"translateZ(0)":""},Ku),t?{}:{margin:s.baseUnit/2,paddingBottom:s.baseUnit/2,paddingTop:s.baseUnit/2,color:r.neutral80})},loadingIndicator:function(e,t){var n=e.isFocused,a=e.size,o=e.theme,s=o.colors,r=o.spacing.baseUnit;return al({label:"loadingIndicator",display:"flex",transition:"color 150ms",alignSelf:"center",fontSize:a,lineHeight:1,marginRight:a,textAlign:"center",verticalAlign:"middle"},t?{}:{color:n?s.neutral60:s.neutral20,padding:2*r})},loadingMessage:Au,menu:function(e,t){var n,a=e.placement,o=e.theme,s=o.borderRadius,r=o.spacing,i=o.colors;return al((tl(n={label:"menu"},function(e){return e?{bottom:"top",top:"bottom"}[e]:"bottom"}(a),"100%"),tl(n,"position","absolute"),tl(n,"width","100%"),tl(n,"zIndex",1),n),t?{}:{backgroundColor:i.neutral0,borderRadius:s,boxShadow:"0 0 0 1px hsla(0, 0%, 0%, 0.1), 0 4px 11px hsla(0, 0%, 0%, 0.1)",marginBottom:r.menuGutter,marginTop:r.menuGutter})},menuList:function(e,t){var n=e.maxHeight,a=e.theme.spacing.baseUnit;return al({maxHeight:n,overflowY:"auto",position:"relative",WebkitOverflowScrolling:"touch"},t?{}:{paddingBottom:a,paddingTop:a})},menuPortal:function(e){var t=e.rect,n=e.offset,a=e.position;return{left:t.left,position:a,top:n,width:t.width,zIndex:1}},multiValue:function(e,t){var n=e.theme,a=n.spacing,o=n.borderRadius,s=n.colors;return al({label:"multiValue",display:"flex",minWidth:0},t?{}:{backgroundColor:s.neutral10,borderRadius:o/2,margin:a.baseUnit/2})},multiValueLabel:function(e,t){var n=e.theme,a=n.borderRadius,o=n.colors,s=e.cropWithEllipsis;return al({overflow:"hidden",textOverflow:s||void 0===s?"ellipsis":void 0,whiteSpace:"nowrap"},t?{}:{borderRadius:a/2,color:o.neutral80,fontSize:"85%",padding:3,paddingLeft:6})},multiValueRemove:function(e,t){var n=e.theme,a=n.spacing,o=n.borderRadius,s=n.colors,r=e.isFocused;return al({alignItems:"center",display:"flex"},t?{}:{borderRadius:o/2,backgroundColor:r?s.dangerLight:void 0,paddingLeft:a.baseUnit,paddingRight:a.baseUnit,":hover":{backgroundColor:s.dangerLight,color:s.danger}})},noOptionsMessage:Iu,option:function(e,t){var n=e.isDisabled,a=e.isFocused,o=e.isSelected,s=e.theme,r=s.spacing,i=s.colors;return al({label:"option",cursor:"default",display:"block",fontSize:"inherit",width:"100%",userSelect:"none",WebkitTapHighlightColor:"rgba(0, 0, 0, 0)"},t?{}:{backgroundColor:o?i.primary:a?i.primary25:"transparent",color:n?i.neutral20:o?i.neutral0:"inherit",padding:"".concat(2*r.baseUnit,"px ").concat(3*r.baseUnit,"px"),":active":{backgroundColor:n?void 0:o?i.primary:i.primary50}})},placeholder:function(e,t){var n=e.theme,a=n.spacing,o=n.colors;return al({label:"placeholder",gridArea:"1 / 1 / 2 / 3"},t?{}:{color:o.neutral50,marginLeft:a.baseUnit/2,marginRight:a.baseUnit/2})},singleValue:function(e,t){var n=e.isDisabled,a=e.theme,o=a.spacing,s=a.colors;return al({label:"singleValue",gridArea:"1 / 1 / 2 / 3",maxWidth:"100%",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},t?{}:{color:n?s.neutral40:s.neutral80,marginLeft:o.baseUnit/2,marginRight:o.baseUnit/2})},valueContainer:function(e,t){var n=e.theme.spacing,a=e.isMulti,o=e.hasValue,s=e.selectProps.controlShouldRenderValue;return al({alignItems:"center",display:a&&o&&s?"flex":"grid",flex:1,flexWrap:"wrap",WebkitOverflowScrolling:"touch",position:"relative",overflow:"hidden"},t?{}:{padding:"".concat(n.baseUnit/2,"px ").concat(2*n.baseUnit,"px")})}},$d={borderRadius:4,colors:{primary:"#2684FF",primary75:"#4C9AFF",primary50:"#B2D4FF",primary25:"#DEEBFF",danger:"#DE350B",dangerLight:"#FFBDAD",neutral0:"hsl(0, 0%, 100%)",neutral5:"hsl(0, 0%, 95%)",neutral10:"hsl(0, 0%, 90%)",neutral20:"hsl(0, 0%, 80%)",neutral30:"hsl(0, 0%, 70%)",neutral40:"hsl(0, 0%, 60%)",neutral50:"hsl(0, 0%, 50%)",neutral60:"hsl(0, 0%, 40%)",neutral70:"hsl(0, 0%, 30%)",neutral80:"hsl(0, 0%, 20%)",neutral90:"hsl(0, 0%, 10%)"},spacing:{baseUnit:4,controlHeight:38,menuGutter:8}},Ud={"aria-live":"polite",backspaceRemovesValue:!0,blurInputOnSelect:vu(),captureMenuScroll:!vu(),classNames:{},closeMenuOnSelect:!0,closeMenuOnScroll:!1,components:{},controlShouldRenderValue:!0,escapeClearsValue:!1,filterOption:function(e,t){if(e.data.__isNew__)return!0;var n=al({ignoreCase:!0,ignoreAccents:!0,stringify:_d,trim:!0,matchFrom:"any"},void 0),a=n.ignoreCase,o=n.ignoreAccents,s=n.stringify,r=n.trim,i=n.matchFrom,c=r?wd(t):t,l=r?wd(s(e)):s(e);return a&&(c=c.toLowerCase(),l=l.toLowerCase()),o&&(c=bd(c),l=vd(l)),"start"===i?l.substr(0,c.length)===c:l.indexOf(c)>-1},formatGroupLabel:function(e){return e.label},getOptionLabel:function(e){return e.label},getOptionValue:function(e){return e.value},isDisabled:!1,isLoading:!1,isMulti:!1,isRtl:!1,isSearchable:!0,isOptionDisabled:function(e){return!!e.isDisabled},loadingMessage:function(){return"Loading..."},maxMenuHeight:300,minMenuHeight:140,menuIsOpen:!1,menuPlacement:"bottom",menuPosition:"absolute",menuShouldBlockScroll:!1,menuShouldScrollIntoView:!function(){try{return/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)}catch(e){return!1}}(),noOptionsMessage:function(){return"No options"},openMenuOnFocus:!1,openMenuOnClick:!0,options:[],pageSize:5,placeholder:"Select...",screenReaderStatus:function(e){var t=e.count;return"".concat(t," result").concat(1!==t?"s":""," available")},styles:{},tabIndex:0,tabSelectsValue:!0,unstyled:!1};function zd(e,t,n,a){return{type:"option",data:t,isDisabled:Zd(e,t,n),isSelected:Jd(e,t,n),label:Xd(e,t),value:Kd(e,t),index:a}}function Hd(e,t){return e.options.map((function(n,a){if("options"in n){var o=n.options.map((function(n,a){return zd(e,n,t,a)})).filter((function(t){return Yd(e,t)}));return o.length>0?{type:"group",data:n,options:o,index:a}:void 0}var s=zd(e,n,t,a);return Yd(e,s)?s:void 0})).filter(xu)}function Wd(e){return e.reduce((function(e,t){return"group"===t.type?e.push.apply(e,sd(t.options.map((function(e){return e.data})))):e.push(t.data),e}),[])}function Gd(e,t){return e.reduce((function(e,n){return"group"===n.type?e.push.apply(e,sd(n.options.map((function(e){return{data:e.data,id:"".concat(t,"-").concat(n.index,"-").concat(e.index)}})))):e.push({data:n.data,id:"".concat(t,"-").concat(n.index)}),e}),[])}function Yd(e,t){var n=e.inputValue,a=void 0===n?"":n,o=t.data,s=t.isSelected,r=t.label,i=t.value;return(!em(e)||!s)&&Qd(e,{label:r,value:i,data:o},a)}var qd=function(e,t){var n;return(null===(n=e.find((function(e){return e.data===t})))||void 0===n?void 0:n.id)||null},Xd=function(e,t){return e.getOptionLabel(t)},Kd=function(e,t){return e.getOptionValue(t)};function Zd(e,t,n){return"function"==typeof e.isOptionDisabled&&e.isOptionDisabled(t,n)}function Jd(e,t,n){if(n.indexOf(t)>-1)return!0;if("function"==typeof e.isOptionSelected)return e.isOptionSelected(t,n);var a=Kd(e,t);return n.some((function(t){return Kd(e,t)===a}))}function Qd(e,t,n){return!e.filterOption||e.filterOption(t,n)}var em=function(e){var t=e.hideSelectedOptions,n=e.isMulti;return void 0===t?n:t},tm=1,nm=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&nd(e,t)}(n,e);var t=function(e){var t=od();return function(){var n,a=ad(e);if(t){var o=ad(this).constructor;n=Reflect.construct(a,arguments,o)}else n=a.apply(this,arguments);return function(e,t){if(t&&("object"==Qc(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,n)}}(n);function n(e){var a;if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,n),(a=t.call(this,e)).state={ariaSelection:null,focusedOption:null,focusedOptionId:null,focusableOptionsWithIds:[],focusedValue:null,inputIsHidden:!1,isFocused:!1,selectValue:[],clearFocusValueOnUpdate:!1,prevWasFocused:!1,inputIsHiddenAfterUpdate:void 0,prevProps:void 0,instancePrefix:""},a.blockOptionHover=!1,a.isComposing=!1,a.commonProps=void 0,a.initialTouchX=0,a.initialTouchY=0,a.openAfterFocus=!1,a.scrollToFocusedOptionOnUpdate=!1,a.userIsDragging=void 0,a.isAppleDevice=Bd()||Fd(/^iPhone/i)||Fd(/^iPad/i)||Bd()&&navigator.maxTouchPoints>1,a.controlRef=null,a.getControlRef=function(e){a.controlRef=e},a.focusedOptionRef=null,a.getFocusedOptionRef=function(e){a.focusedOptionRef=e},a.menuListRef=null,a.getMenuListRef=function(e){a.menuListRef=e},a.inputRef=null,a.getInputRef=function(e){a.inputRef=e},a.focus=a.focusInput,a.blur=a.blurInput,a.onChange=function(e,t){var n=a.props,o=n.onChange,s=n.name;t.name=s,a.ariaOnChange(e,t),o(e,t)},a.setValue=function(e,t,n){var o=a.props,s=o.closeMenuOnSelect,r=o.isMulti,i=o.inputValue;a.onInputChange("",{action:"set-value",prevInputValue:i}),s&&(a.setState({inputIsHiddenAfterUpdate:!r}),a.onMenuClose()),a.setState({clearFocusValueOnUpdate:!0}),a.onChange(e,{action:t,option:n})},a.selectOption=function(e){var t=a.props,n=t.blurInputOnSelect,o=t.isMulti,s=t.name,r=a.state.selectValue,i=o&&a.isOptionSelected(e,r),c=a.isOptionDisabled(e,r);if(i){var l=a.getOptionValue(e);a.setValue(r.filter((function(e){return a.getOptionValue(e)!==l})),"deselect-option",e)}else{if(c)return void a.ariaOnChange(e,{action:"select-option",option:e,name:s});o?a.setValue([].concat(sd(r),[e]),"select-option",e):a.setValue(e,"select-option")}n&&a.blurInput()},a.removeValue=function(e){var t=a.props.isMulti,n=a.state.selectValue,o=a.getOptionValue(e),s=n.filter((function(e){return a.getOptionValue(e)!==o})),r=Cu(t,s,s[0]||null);a.onChange(r,{action:"remove-value",removedValue:e}),a.focusInput()},a.clearValue=function(){var e=a.state.selectValue;a.onChange(Cu(a.props.isMulti,[],null),{action:"clear",removedValues:e})},a.popValue=function(){var e=a.props.isMulti,t=a.state.selectValue,n=t[t.length-1],o=t.slice(0,t.length-1),s=Cu(e,o,o[0]||null);n&&a.onChange(s,{action:"pop-value",removedValue:n})},a.getFocusedOptionId=function(e){return qd(a.state.focusableOptionsWithIds,e)},a.getFocusableOptionsWithIds=function(){return Gd(Hd(a.props,a.state.selectValue),a.getElementId("option"))},a.getValue=function(){return a.state.selectValue},a.cx=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return lu.apply(void 0,[a.props.classNamePrefix].concat(t))},a.getOptionLabel=function(e){return Xd(a.props,e)},a.getOptionValue=function(e){return Kd(a.props,e)},a.getStyles=function(e,t){var n=a.props.unstyled,o=Vd[e](t,n);o.boxSizing="border-box";var s=a.props.styles[e];return s?s(o,t):o},a.getClassNames=function(e,t){var n,o;return null===(n=(o=a.props.classNames)[e])||void 0===n?void 0:n.call(o,t)},a.getElementId=function(e){return"".concat(a.state.instancePrefix,"-").concat(e)},a.getComponents=function(){return e=a.props,al(al({},Qu),e.components);var e},a.buildCategorizedOptions=function(){return Hd(a.props,a.state.selectValue)},a.getCategorizedOptions=function(){return a.props.menuIsOpen?a.buildCategorizedOptions():[]},a.buildFocusableOptions=function(){return Wd(a.buildCategorizedOptions())},a.getFocusableOptions=function(){return a.props.menuIsOpen?a.buildFocusableOptions():[]},a.ariaOnChange=function(e,t){a.setState({ariaSelection:al({value:e},t)})},a.onMenuMouseDown=function(e){0===e.button&&(e.stopPropagation(),e.preventDefault(),a.focusInput())},a.onMenuMouseMove=function(e){a.blockOptionHover=!1},a.onControlMouseDown=function(e){if(!e.defaultPrevented){var t=a.props.openMenuOnClick;a.state.isFocused?a.props.menuIsOpen?"INPUT"!==e.target.tagName&&"TEXTAREA"!==e.target.tagName&&a.onMenuClose():t&&a.openMenu("first"):(t&&(a.openAfterFocus=!0),a.focusInput()),"INPUT"!==e.target.tagName&&"TEXTAREA"!==e.target.tagName&&e.preventDefault()}},a.onDropdownIndicatorMouseDown=function(e){if(!(e&&"mousedown"===e.type&&0!==e.button||a.props.isDisabled)){var t=a.props,n=t.isMulti,o=t.menuIsOpen;a.focusInput(),o?(a.setState({inputIsHiddenAfterUpdate:!n}),a.onMenuClose()):a.openMenu("first"),e.preventDefault()}},a.onClearIndicatorMouseDown=function(e){e&&"mousedown"===e.type&&0!==e.button||(a.clearValue(),e.preventDefault(),a.openAfterFocus=!1,"touchend"===e.type?a.focusInput():setTimeout((function(){return a.focusInput()})))},a.onScroll=function(e){"boolean"==typeof a.props.closeMenuOnScroll?e.target instanceof HTMLElement&&mu(e.target)&&a.props.onMenuClose():"function"==typeof a.props.closeMenuOnScroll&&a.props.closeMenuOnScroll(e)&&a.props.onMenuClose()},a.onCompositionStart=function(){a.isComposing=!0},a.onCompositionEnd=function(){a.isComposing=!1},a.onTouchStart=function(e){var t=e.touches,n=t&&t.item(0);n&&(a.initialTouchX=n.clientX,a.initialTouchY=n.clientY,a.userIsDragging=!1)},a.onTouchMove=function(e){var t=e.touches,n=t&&t.item(0);if(n){var o=Math.abs(n.clientX-a.initialTouchX),s=Math.abs(n.clientY-a.initialTouchY);a.userIsDragging=o>5||s>5}},a.onTouchEnd=function(e){a.userIsDragging||(a.controlRef&&!a.controlRef.contains(e.target)&&a.menuListRef&&!a.menuListRef.contains(e.target)&&a.blurInput(),a.initialTouchX=0,a.initialTouchY=0)},a.onControlTouchEnd=function(e){a.userIsDragging||a.onControlMouseDown(e)},a.onClearIndicatorTouchEnd=function(e){a.userIsDragging||a.onClearIndicatorMouseDown(e)},a.onDropdownIndicatorTouchEnd=function(e){a.userIsDragging||a.onDropdownIndicatorMouseDown(e)},a.handleInputChange=function(e){var t=a.props.inputValue,n=e.currentTarget.value;a.setState({inputIsHiddenAfterUpdate:!1}),a.onInputChange(n,{action:"input-change",prevInputValue:t}),a.props.menuIsOpen||a.onMenuOpen()},a.onInputFocus=function(e){a.props.onFocus&&a.props.onFocus(e),a.setState({inputIsHiddenAfterUpdate:!1,isFocused:!0}),(a.openAfterFocus||a.props.openMenuOnFocus)&&a.openMenu("first"),a.openAfterFocus=!1},a.onInputBlur=function(e){var t=a.props.inputValue;a.menuListRef&&a.menuListRef.contains(document.activeElement)?a.inputRef.focus():(a.props.onBlur&&a.props.onBlur(e),a.onInputChange("",{action:"input-blur",prevInputValue:t}),a.onMenuClose(),a.setState({focusedValue:null,isFocused:!1}))},a.onOptionHover=function(e){if(!a.blockOptionHover&&a.state.focusedOption!==e){var t=a.getFocusableOptions().indexOf(e);a.setState({focusedOption:e,focusedOptionId:t>-1?a.getFocusedOptionId(e):null})}},a.shouldHideSelectedOptions=function(){return em(a.props)},a.onValueInputFocus=function(e){e.preventDefault(),e.stopPropagation(),a.focus()},a.onKeyDown=function(e){var t=a.props,n=t.isMulti,o=t.backspaceRemovesValue,s=t.escapeClearsValue,r=t.inputValue,i=t.isClearable,c=t.isDisabled,l=t.menuIsOpen,p=t.onKeyDown,u=t.tabSelectsValue,d=t.openMenuOnFocus,m=a.state,h=m.focusedOption,f=m.focusedValue,y=m.selectValue;if(!(c||"function"==typeof p&&(p(e),e.defaultPrevented))){switch(a.blockOptionHover=!0,e.key){case"ArrowLeft":if(!n||r)return;a.focusValue("previous");break;case"ArrowRight":if(!n||r)return;a.focusValue("next");break;case"Delete":case"Backspace":if(r)return;if(f)a.removeValue(f);else{if(!o)return;n?a.popValue():i&&a.clearValue()}break;case"Tab":if(a.isComposing)return;if(e.shiftKey||!l||!u||!h||d&&a.isOptionSelected(h,y))return;a.selectOption(h);break;case"Enter":if(229===e.keyCode)break;if(l){if(!h)return;if(a.isComposing)return;a.selectOption(h);break}return;case"Escape":l?(a.setState({inputIsHiddenAfterUpdate:!1}),a.onInputChange("",{action:"menu-close",prevInputValue:r}),a.onMenuClose()):i&&s&&a.clearValue();break;case" ":if(r)return;if(!l){a.openMenu("first");break}if(!h)return;a.selectOption(h);break;case"ArrowUp":l?a.focusOption("up"):a.openMenu("last");break;case"ArrowDown":l?a.focusOption("down"):a.openMenu("first");break;case"PageUp":if(!l)return;a.focusOption("pageup");break;case"PageDown":if(!l)return;a.focusOption("pagedown");break;case"Home":if(!l)return;a.focusOption("first");break;case"End":if(!l)return;a.focusOption("last");break;default:return}e.preventDefault()}},a.state.instancePrefix="react-select-"+(a.props.instanceId||++tm),a.state.selectValue=pu(e.value),e.menuIsOpen&&a.state.selectValue.length){var o=a.getFocusableOptionsWithIds(),s=a.buildFocusableOptions(),r=s.indexOf(a.state.selectValue[0]);a.state.focusableOptionsWithIds=o,a.state.focusedOption=s[r],a.state.focusedOptionId=qd(o,s[r])}return a}return function(e,t,n){t&&td(e.prototype,t),n&&td(e,n),Object.defineProperty(e,"prototype",{writable:!1})}(n,[{key:"componentDidMount",value:function(){this.startListeningComposition(),this.startListeningToTouch(),this.props.closeMenuOnScroll&&document&&document.addEventListener&&document.addEventListener("scroll",this.onScroll,!0),this.props.autoFocus&&this.focusInput(),this.props.menuIsOpen&&this.state.focusedOption&&this.menuListRef&&this.focusedOptionRef&&gu(this.menuListRef,this.focusedOptionRef)}},{key:"componentDidUpdate",value:function(e){var t=this.props,n=t.isDisabled,a=t.menuIsOpen,o=this.state.isFocused;(o&&!n&&e.isDisabled||o&&a&&!e.menuIsOpen)&&this.focusInput(),o&&n&&!e.isDisabled?this.setState({isFocused:!1},this.onMenuClose):o||n||!e.isDisabled||this.inputRef!==document.activeElement||this.setState({isFocused:!0}),this.menuListRef&&this.focusedOptionRef&&this.scrollToFocusedOptionOnUpdate&&(gu(this.menuListRef,this.focusedOptionRef),this.scrollToFocusedOptionOnUpdate=!1)}},{key:"componentWillUnmount",value:function(){this.stopListeningComposition(),this.stopListeningToTouch(),document.removeEventListener("scroll",this.onScroll,!0)}},{key:"onMenuOpen",value:function(){this.props.onMenuOpen()}},{key:"onMenuClose",value:function(){this.onInputChange("",{action:"menu-close",prevInputValue:this.props.inputValue}),this.props.onMenuClose()}},{key:"onInputChange",value:function(e,t){this.props.onInputChange(e,t)}},{key:"focusInput",value:function(){this.inputRef&&this.inputRef.focus()}},{key:"blurInput",value:function(){this.inputRef&&this.inputRef.blur()}},{key:"openMenu",value:function(e){var t=this,n=this.state,a=n.selectValue,o=n.isFocused,s=this.buildFocusableOptions(),r="first"===e?0:s.length-1;if(!this.props.isMulti){var i=s.indexOf(a[0]);i>-1&&(r=i)}this.scrollToFocusedOptionOnUpdate=!(o&&this.menuListRef),this.setState({inputIsHiddenAfterUpdate:!1,focusedValue:null,focusedOption:s[r],focusedOptionId:this.getFocusedOptionId(s[r])},(function(){return t.onMenuOpen()}))}},{key:"focusValue",value:function(e){var t=this.state,n=t.selectValue,a=t.focusedValue;if(this.props.isMulti){this.setState({focusedOption:null});var o=n.indexOf(a);a||(o=-1);var s=n.length-1,r=-1;if(n.length){switch(e){case"previous":r=0===o?0:-1===o?s:o-1;break;case"next":o>-1&&o<s&&(r=o+1)}this.setState({inputIsHidden:-1!==r,focusedValue:n[r]})}}}},{key:"focusOption",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"first",t=this.props.pageSize,n=this.state.focusedOption,a=this.getFocusableOptions();if(a.length){var o=0,s=a.indexOf(n);n||(s=-1),"up"===e?o=s>0?s-1:a.length-1:"down"===e?o=(s+1)%a.length:"pageup"===e?(o=s-t)<0&&(o=0):"pagedown"===e?(o=s+t)>a.length-1&&(o=a.length-1):"last"===e&&(o=a.length-1),this.scrollToFocusedOptionOnUpdate=!0,this.setState({focusedOption:a[o],focusedValue:null,focusedOptionId:this.getFocusedOptionId(a[o])})}}},{key:"getTheme",value:function(){return this.props.theme?"function"==typeof this.props.theme?this.props.theme($d):al(al({},$d),this.props.theme):$d}},{key:"getCommonProps",value:function(){var e=this.clearValue,t=this.cx,n=this.getStyles,a=this.getClassNames,o=this.getValue,s=this.selectOption,r=this.setValue,i=this.props,c=i.isMulti,l=i.isRtl,p=i.options;return{clearValue:e,cx:t,getStyles:n,getClassNames:a,getValue:o,hasValue:this.hasValue(),isMulti:c,isRtl:l,options:p,selectOption:s,selectProps:i,setValue:r,theme:this.getTheme()}}},{key:"hasValue",value:function(){return this.state.selectValue.length>0}},{key:"hasOptions",value:function(){return!!this.getFocusableOptions().length}},{key:"isClearable",value:function(){var e=this.props,t=e.isClearable,n=e.isMulti;return void 0===t?n:t}},{key:"isOptionDisabled",value:function(e,t){return Zd(this.props,e,t)}},{key:"isOptionSelected",value:function(e,t){return Jd(this.props,e,t)}},{key:"filterOption",value:function(e,t){return Qd(this.props,e,t)}},{key:"formatOptionLabel",value:function(e,t){if("function"==typeof this.props.formatOptionLabel){var n=this.props.inputValue,a=this.state.selectValue;return this.props.formatOptionLabel(e,{context:t,inputValue:n,selectValue:a})}return this.getOptionLabel(e)}},{key:"formatGroupLabel",value:function(e){return this.props.formatGroupLabel(e)}},{key:"startListeningComposition",value:function(){document&&document.addEventListener&&(document.addEventListener("compositionstart",this.onCompositionStart,!1),document.addEventListener("compositionend",this.onCompositionEnd,!1))}},{key:"stopListeningComposition",value:function(){document&&document.removeEventListener&&(document.removeEventListener("compositionstart",this.onCompositionStart),document.removeEventListener("compositionend",this.onCompositionEnd))}},{key:"startListeningToTouch",value:function(){document&&document.addEventListener&&(document.addEventListener("touchstart",this.onTouchStart,!1),document.addEventListener("touchmove",this.onTouchMove,!1),document.addEventListener("touchend",this.onTouchEnd,!1))}},{key:"stopListeningToTouch",value:function(){document&&document.removeEventListener&&(document.removeEventListener("touchstart",this.onTouchStart),document.removeEventListener("touchmove",this.onTouchMove),document.removeEventListener("touchend",this.onTouchEnd))}},{key:"renderInput",value:function(){var e=this.props,t=e.isDisabled,n=e.isSearchable,a=e.inputId,o=e.inputValue,s=e.tabIndex,r=e.form,i=e.menuIsOpen,c=e.required,l=this.getComponents().Input,p=this.state,u=p.inputIsHidden,d=p.ariaSelection,m=this.commonProps,h=a||this.getElementId("input"),f=al(al(al({"aria-autocomplete":"list","aria-expanded":i,"aria-haspopup":!0,"aria-errormessage":this.props["aria-errormessage"],"aria-invalid":this.props["aria-invalid"],"aria-label":this.props["aria-label"],"aria-labelledby":this.props["aria-labelledby"],"aria-required":c,role:"combobox","aria-activedescendant":this.isAppleDevice?void 0:this.state.focusedOptionId||""},i&&{"aria-controls":this.getElementId("listbox")}),!n&&{"aria-readonly":!0}),this.hasValue()?"initial-input-focus"===(null==d?void 0:d.action)&&{"aria-describedby":this.getElementId("live-region")}:{"aria-describedby":this.getElementId("placeholder")});return n?G.createElement(l,ol({},m,{autoCapitalize:"none",autoComplete:"off",autoCorrect:"off",id:h,innerRef:this.getInputRef,isDisabled:t,isHidden:u,onBlur:this.onInputBlur,onChange:this.handleInputChange,onFocus:this.onInputFocus,spellCheck:"false",tabIndex:s,form:r,type:"text",value:o},f)):G.createElement(xd,ol({id:h,innerRef:this.getInputRef,onBlur:this.onInputBlur,onChange:iu,onFocus:this.onInputFocus,disabled:t,tabIndex:s,inputMode:"none",form:r,value:""},f))}},{key:"renderPlaceholderOrValue",value:function(){var e=this,t=this.getComponents(),n=t.MultiValue,a=t.MultiValueContainer,o=t.MultiValueLabel,s=t.MultiValueRemove,r=t.SingleValue,i=t.Placeholder,c=this.commonProps,l=this.props,p=l.controlShouldRenderValue,u=l.isDisabled,d=l.isMulti,m=l.inputValue,h=l.placeholder,f=this.state,y=f.selectValue,g=f.focusedValue,v=f.isFocused;if(!this.hasValue()||!p)return m?null:G.createElement(i,ol({},c,{key:"placeholder",isDisabled:u,isFocused:v,innerProps:{id:this.getElementId("placeholder")}}),h);if(d)return y.map((function(t,r){var i=t===g,l="".concat(e.getOptionLabel(t),"-").concat(e.getOptionValue(t));return G.createElement(n,ol({},c,{components:{Container:a,Label:o,Remove:s},isFocused:i,isDisabled:u,key:l,index:r,removeProps:{onClick:function(){return e.removeValue(t)},onTouchEnd:function(){return e.removeValue(t)},onMouseDown:function(e){e.preventDefault()}},data:t}),e.formatOptionLabel(t,"value"))}));if(m)return null;var b=y[0];return G.createElement(r,ol({},c,{data:b,isDisabled:u}),this.formatOptionLabel(b,"value"))}},{key:"renderClearIndicator",value:function(){var e=this.getComponents().ClearIndicator,t=this.commonProps,n=this.props,a=n.isDisabled,o=n.isLoading,s=this.state.isFocused;if(!this.isClearable()||!e||a||!this.hasValue()||o)return null;var r={onMouseDown:this.onClearIndicatorMouseDown,onTouchEnd:this.onClearIndicatorTouchEnd,"aria-hidden":"true"};return G.createElement(e,ol({},t,{innerProps:r,isFocused:s}))}},{key:"renderLoadingIndicator",value:function(){var e=this.getComponents().LoadingIndicator,t=this.commonProps,n=this.props,a=n.isDisabled,o=n.isLoading,s=this.state.isFocused;return e&&o?G.createElement(e,ol({},t,{innerProps:{"aria-hidden":"true"},isDisabled:a,isFocused:s})):null}},{key:"renderIndicatorSeparator",value:function(){var e=this.getComponents(),t=e.DropdownIndicator,n=e.IndicatorSeparator;if(!t||!n)return null;var a=this.commonProps,o=this.props.isDisabled,s=this.state.isFocused;return G.createElement(n,ol({},a,{isDisabled:o,isFocused:s}))}},{key:"renderDropdownIndicator",value:function(){var e=this.getComponents().DropdownIndicator;if(!e)return null;var t=this.commonProps,n=this.props.isDisabled,a=this.state.isFocused,o={onMouseDown:this.onDropdownIndicatorMouseDown,onTouchEnd:this.onDropdownIndicatorTouchEnd,"aria-hidden":"true"};return G.createElement(e,ol({},t,{innerProps:o,isDisabled:n,isFocused:a}))}},{key:"renderMenu",value:function(){var e=this,t=this.getComponents(),n=t.Group,a=t.GroupHeading,o=t.Menu,s=t.MenuList,r=t.MenuPortal,i=t.LoadingMessage,c=t.NoOptionsMessage,l=t.Option,p=this.commonProps,u=this.state.focusedOption,d=this.props,m=d.captureMenuScroll,h=d.inputValue,f=d.isLoading,y=d.loadingMessage,g=d.minMenuHeight,v=d.maxMenuHeight,b=d.menuIsOpen,w=d.menuPlacement,_=d.menuPosition,S=d.menuPortalTarget,x=d.menuShouldBlockScroll,C=d.menuShouldScrollIntoView,P=d.noOptionsMessage,j=d.onMenuScrollToTop,k=d.onMenuScrollToBottom;if(!b)return null;var E,O=function(t,n){var a=t.type,o=t.data,s=t.isDisabled,r=t.isSelected,i=t.label,c=t.value,d=u===o,m=s?void 0:function(){return e.onOptionHover(o)},h=s?void 0:function(){return e.selectOption(o)},f="".concat(e.getElementId("option"),"-").concat(n),y={id:f,onClick:h,onMouseMove:m,onMouseOver:m,tabIndex:-1,role:"option","aria-selected":e.isAppleDevice?void 0:r};return G.createElement(l,ol({},p,{innerProps:y,data:o,isDisabled:s,isSelected:r,key:f,label:i,type:a,value:c,isFocused:d,innerRef:d?e.getFocusedOptionRef:void 0}),e.formatOptionLabel(t.data,"menu"))};if(this.hasOptions())E=this.getCategorizedOptions().map((function(t){if("group"===t.type){var o=t.data,s=t.options,r=t.index,i="".concat(e.getElementId("group"),"-").concat(r),c="".concat(i,"-heading");return G.createElement(n,ol({},p,{key:i,data:o,options:s,Heading:a,headingProps:{id:c,data:t.data},label:e.formatGroupLabel(t.data)}),t.options.map((function(e){return O(e,"".concat(r,"-").concat(e.index))})))}if("option"===t.type)return O(t,"".concat(t.index))}));else if(f){var T=y({inputValue:h});if(null===T)return null;E=G.createElement(i,p,T)}else{var M=P({inputValue:h});if(null===M)return null;E=G.createElement(c,p,M)}var N={minMenuHeight:g,maxMenuHeight:v,menuPlacement:w,menuPosition:_,menuShouldScrollIntoView:C},D=G.createElement(Nu,ol({},p,N),(function(t){var n=t.ref,a=t.placerProps,r=a.placement,i=a.maxHeight;return G.createElement(o,ol({},p,N,{innerRef:n,innerProps:{onMouseDown:e.onMenuMouseDown,onMouseMove:e.onMenuMouseMove},isLoading:f,placement:r}),G.createElement(Ad,{captureEnabled:m,onTopArrive:j,onBottomArrive:k,lockEnabled:x},(function(t){return G.createElement(s,ol({},p,{innerRef:function(n){e.getMenuListRef(n),t(n)},innerProps:{role:"listbox","aria-multiselectable":p.isMulti,id:e.getElementId("listbox")},isLoading:f,maxHeight:i,focusedOption:u}),E)})))}));return S||"fixed"===_?G.createElement(r,ol({},p,{appendTo:S,controlElement:this.controlRef,menuPlacement:w,menuPosition:_}),D):D}},{key:"renderFormField",value:function(){var e=this,t=this.props,n=t.delimiter,a=t.isDisabled,o=t.isMulti,s=t.name,r=t.required,i=this.state.selectValue;if(r&&!this.hasValue()&&!a)return G.createElement(Ld,{name:s,onFocus:this.onValueInputFocus});if(s&&!a){if(o){if(n){var c=i.map((function(t){return e.getOptionValue(t)})).join(n);return G.createElement("input",{name:s,type:"hidden",value:c})}var l=i.length>0?i.map((function(t,n){return G.createElement("input",{key:"i-".concat(n),name:s,type:"hidden",value:e.getOptionValue(t)})})):G.createElement("input",{name:s,type:"hidden",value:""});return G.createElement("div",null,l)}var p=i[0]?this.getOptionValue(i[0]):"";return G.createElement("input",{name:s,type:"hidden",value:p})}}},{key:"renderLiveRegion",value:function(){var e=this.commonProps,t=this.state,n=t.ariaSelection,a=t.focusedOption,o=t.focusedValue,s=t.isFocused,r=t.selectValue,i=this.getFocusableOptions();return G.createElement(ud,ol({},e,{id:this.getElementId("live-region"),ariaSelection:n,focusedOption:a,focusedValue:o,isFocused:s,selectValue:r,focusableOptions:i,isAppleDevice:this.isAppleDevice}))}},{key:"render",value:function(){var e=this.getComponents(),t=e.Control,n=e.IndicatorsContainer,a=e.SelectContainer,o=e.ValueContainer,s=this.props,r=s.className,i=s.id,c=s.isDisabled,l=s.menuIsOpen,p=this.state.isFocused,u=this.commonProps=this.getCommonProps();return G.createElement(a,ol({},u,{className:r,innerProps:{id:i,onKeyDown:this.onKeyDown},isDisabled:c,isFocused:p}),this.renderLiveRegion(),G.createElement(t,ol({},u,{innerRef:this.getControlRef,innerProps:{onMouseDown:this.onControlMouseDown,onTouchEnd:this.onControlTouchEnd},isDisabled:c,isFocused:p,menuIsOpen:l}),G.createElement(o,ol({},u,{isDisabled:c}),this.renderPlaceholderOrValue(),this.renderInput()),G.createElement(n,ol({},u,{isDisabled:c}),this.renderClearIndicator(),this.renderLoadingIndicator(),this.renderIndicatorSeparator(),this.renderDropdownIndicator())),this.renderMenu(),this.renderFormField())}}],[{key:"getDerivedStateFromProps",value:function(e,t){var n=t.prevProps,a=t.clearFocusValueOnUpdate,o=t.inputIsHiddenAfterUpdate,s=t.ariaSelection,r=t.isFocused,i=t.prevWasFocused,c=t.instancePrefix,l=e.options,p=e.value,u=e.menuIsOpen,d=e.inputValue,m=e.isMulti,h=pu(p),f={};if(n&&(p!==n.value||l!==n.options||u!==n.menuIsOpen||d!==n.inputValue)){var y=u?function(e,t){return Wd(Hd(e,t))}(e,h):[],g=u?Gd(Hd(e,h),"".concat(c,"-option")):[],v=a?function(e,t){var n=e.focusedValue,a=e.selectValue.indexOf(n);if(a>-1){if(t.indexOf(n)>-1)return n;if(a<t.length)return t[a]}return null}(t,h):null,b=function(e,t){var n=e.focusedOption;return n&&t.indexOf(n)>-1?n:t[0]}(t,y);f={selectValue:h,focusedOption:b,focusedOptionId:qd(g,b),focusableOptionsWithIds:g,focusedValue:v,clearFocusValueOnUpdate:!1}}var w=null!=o&&e!==n?{inputIsHidden:o,inputIsHiddenAfterUpdate:void 0}:{},_=s,S=r&&i;return r&&!S&&(_={value:Cu(m,h,h[0]||null),options:h,action:"initial-input-focus"},S=!i),"initial-input-focus"===(null==s?void 0:s.action)&&(_=null),al(al(al({},f),w),{},{prevProps:e,ariaSelection:_,prevWasFocused:S})}}]),n}(G.Component);nm.defaultProps=Ud;var am=(0,G.forwardRef)((function(e,t){var n=function(e){var t=e.defaultInputValue,n=void 0===t?"":t,a=e.defaultMenuIsOpen,o=void 0!==a&&a,s=e.defaultValue,r=void 0===s?null:s,i=e.inputValue,c=e.menuIsOpen,l=e.onChange,p=e.onInputChange,u=e.onMenuClose,d=e.onMenuOpen,m=e.value,h=Ip(e,ed),f=Dp((0,G.useState)(void 0!==i?i:n),2),y=f[0],g=f[1],v=Dp((0,G.useState)(void 0!==c?c:o),2),b=v[0],w=v[1],_=Dp((0,G.useState)(void 0!==m?m:r),2),S=_[0],x=_[1],C=(0,G.useCallback)((function(e,t){"function"==typeof l&&l(e,t),x(e)}),[l]),P=(0,G.useCallback)((function(e,t){var n;"function"==typeof p&&(n=p(e,t)),g(void 0!==n?n:e)}),[p]),j=(0,G.useCallback)((function(){"function"==typeof d&&d(),w(!0)}),[d]),k=(0,G.useCallback)((function(){"function"==typeof u&&u(),w(!1)}),[u]),E=void 0!==i?i:y,O=void 0!==c?c:b,T=void 0!==m?m:S;return al(al({},h),{},{inputValue:E,menuIsOpen:O,onChange:C,onInputChange:P,onMenuClose:k,onMenuOpen:j,value:T})}(e);return G.createElement(nm,ol({ref:t},n))})),om=am;const sm=e=>(0,ri.jsx)(Qu.DropdownIndicator,{...e,children:(0,ri.jsx)(si.Icon,{icon:e.selectProps.menuIsOpen?qi:Xi})}),rm=()=>null,im=({options:e,value:t,onChange:n,isMulti:a,placeholder:o})=>{const[s,r]=(0,X.useState)(((e,t)=>Array.isArray(e)?e.map((e=>t.find((t=>t.value===e)))):t.find((t=>t.value===e)))(t,e)),i=(0,X.useCallback)((e=>(r(e),Array.isArray(e)?n(e.map((e=>e.id))):n(e.id))),[n]);return(0,X.useEffect)((()=>{var e;n((e=s)?Array.isArray(e)?e.map((e=>e.value)):e.value:null)}),[s,n]),(0,ri.jsx)(om,{className:"ppcp-r-select",classNamePrefix:"ppcp",isMulti:a,options:e,value:s,onChange:i,placeholder:o,components:{DropdownIndicator:sm,IndicatorSeparator:rm}})},cm=()=>(0,ri.jsx)("p",{children:(0,tt.__)("* Business account is required for subscriptions.","woocommerce-paypal-payments")}),lm=()=>(0,ri.jsxs)("ul",{className:"ppcp-r-services",children:[(0,ri.jsx)("li",{children:(0,tt.__)("Services","woocommerce-paypal-payments")}),(0,ri.jsx)("li",{children:(0,tt.__)("Downloadable","woocommerce-paypal-payments")}),(0,ri.jsx)("li",{children:(0,tt.__)("Bookings","woocommerce-paypal-payments")}),(0,ri.jsx)("li",{children:(0,tt.__)("Deposits","woocommerce-paypal-payments")})]}),pm=()=>(0,ri.jsxs)("ul",{className:"ppcp-r-services",children:[(0,ri.jsx)("li",{children:(0,tt.__)("Goods","woocommerce-paypal-payments")}),(0,ri.jsx)("li",{children:(0,tt.__)("Deliveries","woocommerce-paypal-payments")})]}),um=({showLink:e,showNotice:t})=>(0,ri.jsxs)(ri.Fragment,{children:[e&&(0,ri.jsx)("p",{dangerouslySetInnerHTML:{__html:(0,tt.sprintf)(/* translators: %s is the URL to the WooCommerce Subscriptions product page */ /* translators: %s is the URL to the WooCommerce Subscriptions product page */
(0,tt.__)('* To use subscriptions, you must have <a target="_blank" href="%s">WooCommerce Subscriptions</a> enabled.',"woocommerce-paypal-payments"),"https://woocommerce.com/products/woocommerce-subscriptions/")}}),t&&(0,ri.jsx)("p",{children:(0,tt.__)("* Business account is required for subscriptions.","woocommerce-paypal-payments")})]}),dm=e=>e.isBrandedOnly?(0,tt.__)("Add Expanded Checkout for more ways to pay","woocommerce-paypal-payments"):(0,tt.__)("Add Credit and Debit Cards","woocommerce-paypal-payments"),mm=()=>{const{isCasualSeller:e}=Ur.useBusiness(),{storeCountry:t,storeCurrency:n,ownBrandOnly:a}=zr.useWooSettings(),{canUseCardPayments:o}=Ur.useFlags();return(0,ri.jsx)(Tc,{onlyOptional:!0,useAcdc:!e&&o&&"MX"!==t,isFastlane:!0,isPayLater:!0,ownBrandOnly:a,storeCountry:t,storeCurrency:n})},hm=[{id:"welcome",title:(0,tt.__)("PayPal Payments","woocommerce-paypal-payments"),StepComponent:({setStep:e,currentStep:t})=>{const{storeCountry:n,ownBrandOnly:a}=zr.useWooSettings(),{canUseCardPayments:o,canUseFastlane:s}=Ur.useFlags(),{icons:r}=Oc(n,o,s,a),i=o&&!a&&"MX"!==n?(0,tt.__)("Your all-in-one integration for PayPal checkout solutions that enable buyers to pay via PayPal, Pay Later, all major credit/debit cards, Apple Pay, Google Pay, and more.","woocommerce-paypal-payments"):(0,tt.__)("Your all-in-one integration for PayPal checkout solutions that enable buyers to pay via PayPal, Pay Later, and more.","woocommerce-paypal-payments");return(0,ri.jsxs)("div",{className:"ppcp-r-page-welcome",children:[(0,ri.jsx)(oc,{title:(0,tt.__)("Welcome to PayPal Payments","woocommerce-paypal-payments"),description:i}),(0,ri.jsxs)("div",{className:"ppcp-r-inner-container",children:[(0,ri.jsx)(Wc,{}),(0,ri.jsx)(Yi,{icons:r}),(0,ri.jsx)("p",{className:"ppcp-r-button__description",children:(0,tt.__)("Click the button below to be guided through connecting your existing PayPal account or creating a new one. You will be able to choose the payment options that are right for your store.","woocommerce-paypal-payments")}),(0,ri.jsx)(Di,{children:(0,ri.jsx)(si.Button,{className:"ppcp-r-button-activate-paypal",variant:"primary",onClick:()=>{e(t+1,"user")},children:(0,tt.__)("Activate PayPal Payments","woocommerce-paypal-payments")})})]}),(0,ri.jsx)(gi,{className:"ppcp-r-page-welcome-mode-separator"}),(0,ri.jsx)(Dc,{useAcdc:o,isFastlane:s,storeCountry:n,ownBrandOnly:a}),(0,ri.jsx)(gi,{text:(0,tt.__)("or","woocommerce-paypal-payments")}),(0,ri.jsx)(Ji,{title:(0,tt.__)("See advanced options","woocommerce-paypal-payments"),className:"onboarding-advanced-options",noCaps:!0,id:"advanced-options",children:(0,ri.jsx)(Hc,{})})]})},canProceed:()=>!0},{id:"business",title:(0,tt.__)("Set up store type","woocommerce-paypal-payments"),StepComponent:({})=>{const{isCasualSeller:e,setIsCasualSeller:t}=Ur.useBusiness(),[n,a]=(0,X.useState)((e=>null===e?"":e?ve:be)(e));(0,X.useEffect)((()=>{n&&t(ve===n,"user")}),[n,t]);const{canUseSubscriptions:o}=Ur.useFlags(),s=[{value:be,title:(0,tt.__)("Business","woocommerce-paypal-payments"),description:(0,tt.__)("Recommended for individuals and organizations that primarily use PayPal to sell goods or services or receive donations, even if your business is not incorporated.","woocommerce-paypal-payments")},{value:ve,title:(0,tt.__)("Personal Account","woocommerce-paypal-payments"),description:(0,tt.__)("Ideal for those who primarily make purchases or send personal transactions to family and friends.","woocommerce-paypal-payments"),contents:o?(0,ri.jsx)(cm,{}):null}];return(0,ri.jsxs)("div",{className:"ppcp-r-page-business",children:[(0,ri.jsx)(oc,{title:(0,tt.__)("Choose your account type","woocommerce-paypal-payments")}),(0,ri.jsx)("div",{className:"ppcp-r-inner-container",children:(0,ri.jsx)(Kc,{multiSelect:!1,options:s,onChange:a,value:n})})]})},canProceed:({business:e})=>null!==e.isCasualSeller},{id:"products",title:(0,tt.__)("Select product types","woocommerce-paypal-payments"),StepComponent:()=>{const{products:e,setProducts:t}=Ur.useProducts(),{canUseSubscriptions:n}=Ur.useFlags(),[a,o]=(0,X.useState)(null),[s,r]=(0,X.useState)([]),{isCasualSeller:i}=Ur.useBusiness();return(0,X.useEffect)((()=>{const e=[{value:we.VIRTUAL,title:(0,tt.__)("Virtual","woocommerce-paypal-payments"),description:(0,tt.__)("Items do not require shipping.","woocommerce-paypal-payments"),contents:(0,ri.jsx)(lm,{})},{value:we.PHYSICAL,title:(0,tt.__)("Physical Goods","woocommerce-paypal-payments"),description:(0,tt.__)("Items require shipping.","woocommerce-paypal-payments"),contents:(0,ri.jsx)(pm,{})},{value:we.SUBSCRIPTIONS,title:(0,tt.__)("Subscriptions","woocommerce-paypal-payments"),description:(0,tt.__)("Recurring payments for either physical goods or services.","woocommerce-paypal-payments"),isDisabled:i,contents:(0,ri.jsx)(um,{showLink:!1,showNotice:i})}];(()=>{const t=e.map((e=>e.value!==we.SUBSCRIPTIONS||n?e:{...e,isDisabled:!0,contents:(0,ri.jsx)(um,{showLink:!0,showNotice:i})}));r(t),o(n)})()}),[n,i]),(0,ri.jsxs)("div",{className:"ppcp-r-page-products",children:[(0,ri.jsx)(oc,{title:(0,tt.__)("Tell us about the products you sell","woocommerce-paypal-payments")}),(0,ri.jsx)("div",{className:"ppcp-r-inner-container",children:(0,ri.jsx)(Kc,{multiSelect:!0,options:s,onChange:(n,a)=>{t(a?[...e,n]:e.filter((e=>e!==n)),"user")},value:e})})]})},canProceed:({products:e})=>e.products.length>0},{id:"methods",title:(0,tt.__)("Choose checkout options","woocommerce-paypal-payments"),StepComponent:()=>{const{optionalMethods:e,setOptionalMethods:t}=Ur.useOptionalPaymentMethods(),{ownBrandOnly:n,storeCountry:a}=zr.useWooSettings(),{isCasualSeller:o}=Ur.useBusiness(),{canUseCardPayments:s}=Ur.useFlags(),r=[{value:!0,title:(0,X.useMemo)((()=>o||!s||"MX"===a?null:(0,tt.__)("Available with additional application","woocommerce-paypal-payments")),[o,s,a]),description:(0,ri.jsx)(mm,{})},{title:n||!s||"MX"===a?(0,tt.__)("No thanks, I prefer to use a different provider for local payment methods","woocommerce-paypal-payments"):(0,tt.__)("No thanks, I prefer to use a different provider for processing credit cards, digital wallets, and local payment methods","woocommerce-paypal-payments"),value:!1}];return(0,ri.jsxs)("div",{className:"ppcp-r-page-optional-payment-methods",children:[(0,ri.jsx)(oc,{title:(0,ri.jsx)(dm,{isBrandedOnly:n})}),(0,ri.jsxs)("div",{className:"ppcp-r-inner-container",children:[(0,ri.jsx)(Kc,{multiSelect:!1,options:r,onChange:e=>{t(e,"user")},value:e}),(0,ri.jsx)(ic,{})]})]})},canProceed:({methods:e})=>null!==e.optionalMethods},{id:"complete",title:(0,tt.__)("Connect your PayPal account","woocommerce-paypal-payments"),StepComponent:()=>(0,ri.jsxs)("div",{className:"ppcp-r-page-products",children:[(0,ri.jsx)(oc,{title:(0,tt.__)("Complete Your Payment Setup","woocommerce-paypal-payments"),description:(0,tt.__)("To finalize your payment setup, please log in to PayPal. If you don’t have an account yet, don’t worry - we’ll guide you through the easy process of creating one.","woocommerce-paypal-payments")}),(0,ri.jsx)("div",{className:"ppcp-r-inner-container ppcp--wide",children:(0,ri.jsx)("div",{className:"ppcp-r-onboarding-header__description",children:(0,ri.jsx)(Bc,{title:(0,tt.__)("Connect to PayPal","woocommerce-paypal-payments")})})})]}),canProceed:()=>!0}],fm=({stepDetails:e,onNext:t,onPrev:n})=>{const{goToWooCommercePaymentsTab:a}=Mi(),{title:o,isFirst:s,percentage:r,showNext:i,canProceed:c}=e,l=!c(Ur.useNavigationState());return(0,ri.jsx)(Ai,{title:o,isMainTitle:s,exitOnTitleClick:s,onTitleClick:n,showProgressBar:!0,progressBarPercent:.9*r,children:(0,ri.jsx)(ym,{onExit:a,isFirst:s,isDisabled:l,showNext:i,onNext:t})})},ym=({isFirst:e,showNext:t,isDisabled:n,onExit:a,onNext:o})=>e?null:(0,ri.jsxs)(ri.Fragment,{children:[(0,ri.jsx)(si.Button,{variant:"link",onClick:a,children:(0,tt.__)("Save and exit","woocommerce-paypal-payments")}),t&&(0,ri.jsx)(si.Button,{variant:"primary",disabled:n,onClick:o,children:(0,tt.__)("Continue","woocommerce-paypal-payments")})]}),gm=()=>{const{step:e,setStep:t,flags:n}=Ur.useSteps(),a=(e=>{const{ownBrandOnly:t}=zr.useWooSettings(),{isCasualSeller:n}=Ur.useBusiness(),a=((e,t)=>e.filter((e=>t.every((t=>t(e))))))(hm,[t=>e.canUseCasualSelling||"business"!==t.id,a=>"methods"!==a.id||!e.shouldSkipPaymentMethods&&!(t&&n)]),o=a.length;return a.map(((e,t)=>({...e,isFirst:0===t,isLast:t===o-1,showNext:t<o-1,percentage:t/(o-1)*100,nextStep:t<o-1?t+1:t,prevStep:t>0?t-1:0})))})(n),o=((e,t)=>{var n;return t["number"==typeof(n=e)&&Number.isInteger(n)&&n>=0&&n<t.length?e:0]})(e,a);return o?.StepComponent||console.error("Invalid Onboarding State",{step:e,flags:n,Steps:a,currentStep:o}),(0,ri.jsxs)(ri.Fragment,{children:[(0,ri.jsx)(fm,{stepDetails:o,onNext:()=>t(o.nextStep,"user"),onPrev:()=>t(o.prevStep,"user")}),(0,ri.jsx)(ci,{page:"onboarding",children:(0,ri.jsx)("div",{className:"ppcp-r-card",children:(0,ri.jsx)(o.StepComponent,{setStep:t,currentStep:e,stepperOrder:a})})})]})},vm=(0,ri.jsx)(Ci.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,ri.jsx)(Ci.Path,{d:"M7 7.2h8.2L13.5 9l1.1 1.1 3.6-3.6-3.5-4-1.1 1 1.9 2.3H7c-.9 0-1.7.3-2.3.9-1.4 1.5-1.4 4.2-1.4 5.6v.2h1.5v-.3c0-1.1 0-3.5 1-4.5.3-.3.7-.5 1.2-.5zm13.8 4V11h-1.5v.3c0 1.1 0 3.5-1 4.5-.3.3-.7.5-1.3.5H8.8l1.7-1.7-1.1-1.1L5.9 17l3.5 4 1.1-1-1.9-2.3H17c.9 0 1.7-.3 2.3-.9 1.5-1.4 1.5-4.2 1.5-5.6z"})}),bm=({id:e,className:t,children:n,title:a,titleSuffix:o,description:s,horizontalLayout:r=!1,separatorAndGap:i=!0,visible:c=!0})=>{if(!c)return null;const l={className:Z()("ppcp-r-settings-block",t,{"ppcp--no-gap":!i,"ppcp--horizontal":r}),...e&&{id:e}};return(0,ri.jsxs)("div",{...l,children:[(0,ri.jsx)(wm,{blockTitle:a,blockSuffix:o,blockDescription:s}),(0,ri.jsx)(ui,{asCard:!1,children:n})]})},wm=({blockTitle:e,blockSuffix:t,blockDescription:n})=>e||n?(0,ri.jsxs)(hi,{children:[(0,ri.jsxs)(vi,{children:[e,(0,ri.jsx)(bi,{children:t})]}),(0,ri.jsx)(mi,{children:n})]}):null,_m=(0,ri.jsx)(Ci.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,ri.jsx)(Ci.Path,{fillRule:"evenodd",d:"M10.289 4.836A1 1 0 0111.275 4h1.306a1 1 0 01.987.836l.244 1.466c.787.26 1.503.679 2.108 1.218l1.393-.522a1 1 0 011.216.437l.653 1.13a1 1 0 01-.23 1.273l-1.148.944a6.025 6.025 0 010 2.435l1.149.946a1 1 0 01.23 1.272l-.653 1.13a1 1 0 01-1.216.437l-1.394-.522c-.605.54-1.32.958-2.108 1.218l-.244 1.466a1 1 0 01-.987.836h-1.306a1 1 0 01-.986-.836l-.244-1.466a5.995 5.995 0 01-2.108-1.218l-1.394.522a1 1 0 01-1.217-.436l-.653-1.131a1 1 0 01.23-1.272l1.149-.946a6.026 6.026 0 010-2.435l-1.148-.944a1 1 0 01-.23-1.272l.653-1.131a1 1 0 011.217-.437l1.393.522a5.994 5.994 0 012.108-1.218l.244-1.466zM14.929 12a3 3 0 11-6 0 3 3 0 016 0z",clipRule:"evenodd"})}),Sm=(0,ri.jsx)(Ci.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"-2 -2 24 24",children:(0,ri.jsx)(Ci.Path,{d:"M10 2c4.42 0 8 3.58 8 8s-3.58 8-8 8-8-3.58-8-8 3.58-8 8-8zm1.13 9.38l.35-6.46H8.52l.35 6.46h2.26zm-.09 3.36c.24-.23.37-.55.37-.96 0-.42-.12-.74-.36-.97s-.59-.35-1.06-.35-.82.12-1.07.35-.37.55-.37.97c0 .***********.***********.34 1.06.34s.8-.11 1.05-.34z"})}),xm=({warningMessages:e})=>{const t=Object.values(e||{});return 0===t.length?null:(0,ri.jsxs)("span",{className:"ppcp--method-warning",children:[(0,ri.jsx)(si.Icon,{icon:Sm}),(0,ri.jsx)("div",{className:"ppcp--method-warning-message",children:t.map(((e,t)=>(0,ri.jsx)("div",{className:"ppcp--method-warning__item",dangerouslySetInnerHTML:{__html:e}},t)))})]})},Cm=({paymentMethod:e,onTriggerModal:t,onSelect:n,isSelected:a,isDisabled:o,disabledMessage:s,warningMessages:r})=>{const i=r&&Object.keys(r).length>0,c=["ppcp--method-item",o?"ppcp--method-item--disabled":"",i&&!o?"ppcp--method-item--warning":""].filter(Boolean).join(" ");return(0,ri.jsxs)(bm,{id:e.id,className:c,separatorAndGap:!1,"aria-disabled":o?"true":"false",children:[o&&(0,ri.jsx)("div",{className:"ppcp--method-disabled-overlay",role:"alert","aria-live":"polite",children:(0,ri.jsx)("p",{className:"ppcp--method-disabled-message",tabIndex:"0",children:s})}),(0,ri.jsxs)("div",{className:"ppcp--method-inner",children:[(0,ri.jsxs)("div",{className:"ppcp--method-title-wrapper",children:[e?.icon&&(0,ri.jsx)(Gi,{icons:[e.icon],type:e.icon}),(0,ri.jsx)("span",{className:"ppcp--method-title",children:e.itemTitle})]}),(0,ri.jsx)("p",{className:"ppcp--method-description",children:e.itemDescription}),(0,ri.jsxs)("div",{className:"ppcp--method-footer",children:[(0,ri.jsxs)("div",{className:"ppcp--method-toggle-wrapper",children:[(0,ri.jsx)(si.ToggleControl,{__nextHasNoMarginBottom:!0,checked:a,onChange:n,disabled:o,"aria-label":`Enable ${e.itemTitle}`}),i&&!o&&a&&(0,ri.jsx)(xm,{warningMessages:r})]}),e?.fields&&t&&(0,ri.jsx)(si.Button,{className:"ppcp--method-settings",disabled:o,onClick:t,"aria-label":`Configure ${e.itemTitle} settings`,children:(0,ri.jsx)(si.Icon,{icon:_m})})]})]})]})},Pm=({paymentMethods:e=[],onTriggerModal:t})=>{const{changePaymentSettings:n}=Hr.useStore();return e.length?(0,ri.jsx)(bm,{className:"ppcp--grid ppcp-r-settings-block__payment-methods",children:e.filter((e=>e&&e.id)).map((e=>(0,ri.jsx)(Cm,{paymentMethod:e,isSelected:e.enabled,isDisabled:e.isDisabled,disabledMessage:e.disabledMessage,onSelect:t=>{return a=e.id,n(a,{enabled:t});var a},onTriggerModal:()=>t?.(e.id),warningMessages:e.warningMessages},e.id)))}):null},jm={OVERVIEW:"tab-panel-0-overview",PAYMENT_METHODS:"tab-panel-0-payment-methods",SETTINGS:"tab-panel-0-settings",STYLING:"tab-panel-0-styling",PAY_LATER_MESSAGING:"tab-panel-0-pay-later-messaging"},km=(e,t,n=!1)=>new Promise((a=>{const o=document.getElementById(e);o?(o.click(),setTimeout((()=>{ki(t||"ppcp-settings-container",n).then(a)}),100)):(console.error(`Failed to select tab: Tab with ID "${e}" not found`),a())})),Em=({title:e,description:t,isCompleted:n,isDismissing:a,onClick:o,onDismiss:s})=>(0,ri.jsx)("div",{className:`ppcp-r-todo-item ${n?"is-completed":""} ${a?"is-dismissing":""}`,onClick:o,children:(0,ri.jsxs)("div",{className:"ppcp-r-todo-item__inner",children:[(0,ri.jsx)("div",{className:"ppcp-r-todo-item__icon",children:n&&(0,ri.jsx)("span",{className:"dashicons dashicons-yes"})}),(0,ri.jsxs)("div",{className:"ppcp-r-todo-item__content",children:[(0,ri.jsx)("div",{className:"ppcp-r-todo-item__description",children:e}),t&&(0,ri.jsx)("div",{className:"ppcp-r-todo-item__secondary-description",children:t})]}),(0,ri.jsx)("button",{className:"ppcp-r-todo-item__dismiss",onClick:s,"aria-label":"Dismiss todo item",children:(0,ri.jsx)("span",{className:"dashicons dashicons-no-alt"})})]})}),Om=({todosData:e,className:t="",setActiveModal:n,onDismissTodo:a})=>{const[o,s]=(0,X.useState)(new Set),{completedTodos:r,dismissedTodos:i}=(0,J.useSelect)((e=>({completedTodos:e(zo).getCompletedTodos()||[],dismissedTodos:e(zo).getDismissedTodos()||[]})),[]),{completeOnClick:c}=(0,J.useDispatch)(zo);if((0,X.useEffect)((()=>{0===i.length&&s(new Set)}),[i]),0===e.length)return null;const l=e.filter((e=>!i.includes(e.id))).slice(0,5);return(0,ri.jsx)("div",{className:`ppcp-r-settings-block__todo ppcp-r-todo-items ${t}`,children:l.map((e=>(0,ri.jsx)(Em,{id:e.id,title:e.title,description:e.description,isCompleted:r.includes(e.id),isDismissing:o.has(e.id),onDismiss:t=>((e,t)=>{t.preventDefault(),t.stopPropagation(),s((t=>new Set([...t,e]))),setTimeout((()=>{a(e)}),300)})(e.id,t),onClick:()=>(async e=>{const{action:t}=e,a=Boolean(t.highlight);if("tab"===t.type){const e=jm[t.tab.toUpperCase()];await km(e,t.section,a)}else"external"===t.type&&window.open(t.url,"_blank");t.completeOnClick&&await c(e.id),t.modal&&n(t.modal)})(e)},e.id)))})},Tm=({title:e,description:t,...n})=>{const{actionProps:a}=n,{isSandbox:o}=zr.useMerchant();return(0,ri.jsxs)(bm,{...n,className:"ppcp-r-settings-block__feature",children:[(0,ri.jsxs)(hi,{children:[(0,ri.jsxs)(vi,{children:[e,a?.enabled&&(0,ri.jsx)(gc,{...a?.badge})]}),(0,ri.jsxs)(mi,{className:"ppcp-r-settings-block__feature__description",children:[(0,ri.jsx)("span",{className:"ppcp-r-feature-item__description",dangerouslySetInnerHTML:{__html:t}}),a?.notes?.length>0&&(0,ri.jsx)("span",{className:"ppcp--item-notes",children:a.notes.map(((e,t)=>(0,ri.jsx)("span",{children:e},t)))})]})]}),(0,ri.jsx)(li,{children:(0,ri.jsx)("div",{className:"ppcp--action-buttons",children:a?.buttons.map((e=>{const{class:t,type:n,text:s,onClick:r}=e,i=(e=>{const{url:t,urls:n}=e;return n?o?n.sandbox:n.live:t})(e);return(0,ri.jsx)(si.Button,{className:t,variant:n,isBusy:a.isBusy,href:i,target:i?"_blank":void 0,onClick:i?void 0:r,children:s},s)}))})})]})},Mm=()=>{const[e,t]=(0,X.useState)(!1),{todos:n,isReady:a,dismissTodo:o}=Cs(),{setActiveModal:s}=(0,J.useDispatch)(wt),{resetDismissedTodos:r,setDismissedTodos:i}=(0,J.useDispatch)(zo),{createSuccessNotice:c}=(0,J.useDispatch)(Ac.store);return a&&n.length>0?(0,ri.jsx)(_i,{className:"ppcp-r-tab-overview-todo",title:(0,tt.__)("Things to do next","woocommerce-paypal-payments"),description:(0,ri.jsxs)(ri.Fragment,{children:[(0,ri.jsx)("p",{children:(0,tt.__)("Complete these tasks to keep your store updated with the latest products and services.","woocommerce-paypal-payments")}),(0,ri.jsxs)(si.Button,{variant:"tertiary",onClick:async()=>{t(!0);try{await i([]),await r(),c((0,tt.__)("Dismissed items restored successfully.","woocommerce-paypal-payments"),{icon:nc,speak:!0})}finally{t(!1)}},disabled:e,children:[(0,ri.jsx)(si.Icon,{icon:vm,size:18}),e?(0,tt.__)("Restoring…","woocommerce-paypal-payments"):(0,tt.__)("Restore dismissed Things To Do","woocommerce-paypal-payments")]})]}),children:(0,ri.jsx)(Om,{todosData:n,setActiveModal:s,onDismissTodo:o})}):null},Nm=({isBusy:e,isSandbox:t,title:n,description:a,buttons:o,enabled:s,notes:r})=>{const{setActiveModal:i}=(0,J.useDispatch)(wt),c=e=>e.urls?t?e.urls.sandbox:e.urls.live:e.url,l=o.filter((e=>!e.showWhen||s&&"enabled"===e.showWhen||!s&&"disabled"===e.showWhen)),p={isBusy:e,enabled:s,notes:r,buttons:l.map((e=>({...e,url:c(e),onClick:()=>(async e=>{if("tab"===e.action?.type){const t=void 0===e.action?.highlight||Boolean(e.action.highlight),n=jm[e.action.tab.toUpperCase()];await km(n,e.action.section,t)}e.action?.modal&&i(e.action.modal)})(e)})))};return s&&(p.badge={text:(0,tt.__)("Active","woocommerce-paypal-payments"),type:yc}),(0,ri.jsx)(ui,{children:(0,ri.jsx)(Tm,{title:n,description:a,actionProps:p})})},Dm=({refreshHandler:e,isRefreshing:t})=>{const n=t?(0,tt.__)("Refreshing…","woocommerce-paypal-payments"):(0,tt.__)("Refresh","woocommerce-paypal-payments");return(0,ri.jsxs)(ri.Fragment,{children:[(0,ri.jsx)("p",{children:(0,tt.__)("Enable additional features and capabilities on your WooCommerce store.","woocommerce-paypal-payments")}),(0,ri.jsx)("p",{children:(0,tt.__)("Click Refresh to update your current features after making changes.","woocommerce-paypal-payments")}),(0,ri.jsxs)(si.Button,{variant:"tertiary",onClick:e,disabled:t,children:[(0,ri.jsx)(si.Icon,{icon:vm,size:18}),n]})]})},Im=()=>{const[e,t]=(0,X.useState)(!1),{merchant:n}=An(),{storeCountry:a}=Dn(),{features:o,fetchFeatures:s}=Cr(),{refreshFeatureStatuses:r}=(0,J.useDispatch)(wt),{createSuccessNotice:i,createErrorNotice:c}=(0,J.useDispatch)(Ac.store);if(!o||0===o.length)return null;const l=o.filter((e=>"advanced_credit_and_debit_cards"!==e.id||"MX"!==a));return(0,ri.jsx)(_i,{className:"ppcp-r-tab-overview-features",title:(0,tt.__)("Features","woocommerce-paypal-payments"),description:(0,ri.jsx)(Dm,{refreshHandler:async()=>{t(!0);try{const e=await r();if(!e?.success)throw new Error(e?.message||"Failed to refresh status");const t=await s();if(!t.success)throw new Error(t?.message||"Failed to fetch features");i((0,tt.__)("Features refreshed successfully.","woocommerce-paypal-payments"),{icon:nc,speak:!0})}catch(e){c((0,tt.sprintf)(/* translators: %s: error message */ /* translators: %s: error message */
(0,tt.__)("Operation failed: %s","woocommerce-paypal-payments"),e.message||(0,tt.__)("Unknown error","woocommerce-paypal-payments")),{icon:ac,speak:!0})}finally{t(!1)}},isRefreshing:e}),contentContainer:!1,"aria-live":"polite","aria-busy":e,children:(0,ri.jsx)(di,{children:l.map((({id:t,enabled:a,...o})=>(0,ri.jsx)(Nm,{isBusy:e,isSandbox:n.isSandbox,enabled:a,...o},t)))})})},Am=()=>(0,ri.jsx)(_i,{className:"ppcp-r-tab-overview-help",title:(0,tt.__)("Help Center","woocommerce-paypal-payments"),description:(0,tt.__)("Access detailed guides and responsive support to streamline setup and enhance your experience.","woocommerce-paypal-payments"),contentContainer:!1,children:(0,ri.jsxs)(di,{children:[(0,ri.jsx)(ui,{children:(0,ri.jsx)(Tm,{title:(0,tt.__)("Documentation","woocommerce-paypal-payments"),description:(0,tt.__)("Find detailed guides and resources to help you set up, manage, and optimize your PayPal integration.","woocommerce-paypal-payments"),actionProps:{buttons:[{type:"tertiary",text:(0,tt.__)("View full documentation","woocommerce-paypal-payments"),url:"https://woocommerce.com/document/woocommerce-paypal-payments/"}]}})}),(0,ri.jsx)(ui,{children:(0,ri.jsx)(Tm,{title:(0,tt.__)("Support","woocommerce-paypal-payments"),description:(0,tt.__)("Need help? Access troubleshooting tips or contact our support team for personalized assistance.","woocommerce-paypal-payments"),actionProps:{buttons:[{type:"tertiary",text:(0,tt.__)("View support options","woocommerce-paypal-payments"),url:"https://woocommerce.com/document/woocommerce-paypal-payments/#get-help "}]}})})]})}),Rm=()=>{const{isReady:e}=Yr.useTodos(),{isReady:t}=zr.useMerchantInfo(),{isReady:n}=Xr.useFeatures();return(()=>{const{gatewaysSynced:e}=Ur.useGatewaySync(),t=(0,J.useDispatch)(Q),{syncGateways:n}=t,{isReady:a,completed:o}=Ur.useSteps(),{isReady:s}=zr.useStore(),[r,i]=(0,X.useState)(!1),[c,l]=(0,X.useState)(!1),[p,u]=(0,X.useState)(null),d=(0,X.useRef)(!1),m=(0,X.useCallback)((async()=>{if(r)return{success:!1,skipped:!0};i(!0),u(null);try{const e=await n();if(e.success)return await new Promise((e=>setTimeout(e,1e3))),l(!0),{success:!0};throw new Error(e.message||"Failed to sync gateways")}catch(e){return u(e),setTimeout((()=>{d.current=!1}),5e3),{success:!1,error:e}}finally{i(!1)}}),[r,n]);(0,X.useEffect)((()=>{a&&s&&!e&&(r||d.current||(d.current=!0,m()))}),[a,s,o,e,r,m])})(),e&&t&&n?(0,ri.jsxs)("div",{className:"ppcp-r-tab-overview",role:"region","aria-label":(0,tt.__)("PayPal Overview","woocommerce-paypal-payments"),children:[(0,ri.jsx)(Mm,{}),(0,ri.jsx)(Im,{}),(0,ri.jsx)(Am,{})]}):(0,ri.jsx)(ii,{asModal:!0,ariaLabel:(0,tt.__)("Loading PayPal settings","woocommerce-paypal-payments")})},Lm=e=>{let t="ppcp-r-modal";return e?.className&&(t+=" "+e.className),(0,ri.jsx)(si.Modal,{className:t,onRequestClose:()=>e.setModalIsVisible(!1),size:e?.size,children:(0,ri.jsxs)("div",{className:"ppcp-r-modal__container",children:[(0,ri.jsxs)("div",{className:"ppcp-r-modal__header",children:[(0,ri.jsx)(Gi,{icons:[e.icon],type:e.icon}),(0,ri.jsx)("span",{className:"ppcp-r-modal__title",children:e.title})]}),(0,ri.jsx)("div",{className:"ppcp-r-modal__content",children:e.children})]})})},Fm=({method:e,setModalIsVisible:t,onSave:n})=>{const{all:a}=Hr.usePaymentMethods(),{paypalShowLogo:o,fastlaneCardholderName:s,fastlaneDisplayWatermark:r}=Hr.usePaymentMethodsModal(),[i,c]=(0,X.useState)((()=>{if(!e?.id)return{};const t=a.find((t=>t.id===e.id));if(!t?.fields)return{};const n={};return Object.entries(t.fields).forEach((([e,a])=>{switch(e){case"checkoutPageTitle":n[e]=t.title;break;case"checkoutPageDescription":n[e]=t.description;break;default:n[e]=a.default}})),n.paypalShowLogo=o,n.fastlaneCardholderName=s,n.fastlaneDisplayWatermark=r,n}));if(!e?.id)return null;const l=a.find((t=>t.id===e.id));return l?.fields?(0,ri.jsx)(Lm,{setModalIsVisible:t,icon:l.icon,title:e.title,children:(0,ri.jsxs)("div",{className:"ppcp-r-modal__field-rows",children:[Object.entries(l.fields).map((([e,t])=>((e,t)=>{switch(t.type){case"text":return(0,ri.jsx)("div",{className:"ppcp-r-modal__field-row",children:(0,ri.jsx)(si.TextControl,{__nextHasNoMarginBottom:!0,className:"ppcp-r-vertical-text-control",label:t.label,value:i[e],onChange:t=>c((n=>({...n,[e]:t})))})},e);case"toggle":return(0,ri.jsx)("div",{className:"ppcp-r-modal__field-row",children:(0,ri.jsx)(si.ToggleControl,{__nextHasNoMarginBottom:!0,label:t.label,checked:i[e],onChange:t=>c((n=>({...n,[e]:t})))})},e);case"radio":return(0,ri.jsxs)(ri.Fragment,{children:[(0,ri.jsxs)("div",{className:"ppcp-r-modal__field-row",children:[(0,ri.jsx)("strong",{className:"ppcp-r-modal__content-title",children:t.label}),t.description&&(0,ri.jsx)("span",{className:"ppcp-r-modal__field-description",children:t.description})]}),(0,ri.jsx)("div",{className:"ppcp-r-modal__field-row",children:(0,ri.jsx)(si.RadioControl,{selected:i[e],options:t.options,onChange:t=>c((n=>({...n,[e]:t})))})})]});default:return null}})(e,t))),(0,ri.jsx)("div",{className:"ppcp-r-modal__field-row ppcp-r-modal__field-row--save",children:(0,ri.jsx)(si.Button,{variant:"primary",onClick:()=>{n?.(e.id,i),t(!1)},children:(0,tt.__)("Save changes","woocommerce-paypal-payments")})})]})}):null},Bm=(e,t)=>{const n=t[e];return n&&(n.itemTitle||n.title)||""},Vm=({parentId:e,parentName:t})=>{const n=t||e;return(0,X.createInterpolateElement)(/* translators: %s: payment method name */ /* translators: %s: payment method name */
(0,tt.__)("This payment method requires <methodLink /> to be enabled.","woocommerce-paypal-payments"),{methodLink:(0,ri.jsx)("strong",{children:(0,ri.jsx)("a",{href:"#",onClick:t=>{t.preventDefault(),ki(e)},children:n})})})},$m=({dependentMethodId:e,dependentMethodName:t,requiredValue:n})=>{const a=t||e,o=n?(0,tt.__)("Enable <methodLink /> to use this method.","woocommerce-paypal-payments"):(0,tt.__)("Disable <methodLink /> to use this method.","woocommerce-paypal-payments");return(0,X.createInterpolateElement)(o,{methodLink:(0,ri.jsx)("strong",{children:(0,ri.jsx)("a",{href:"#",onClick:t=>{t.preventDefault(),ki(e)},children:a})})})},Um=({settingName:e,sectionId:t})=>(0,ri.jsx)("strong",{children:(0,ri.jsx)("a",{href:"#",onClick:e=>{if(e.preventDefault(),t){const e=jm.SETTINGS,n=(e=>{if(!e)return e;const t=e.replace(/([A-Z])/g,"-$1").toLowerCase();return t.startsWith("ppcp-")?t:`ppcp-${t}`})(t);km(e),setTimeout((()=>{ki(n)}),100)}},children:e})}),zm=({settingId:e,requiredValue:t})=>{const n={savePaypalAndVenmo:"Save PayPal and Venmo"}[e]||e,a=(0,ri.jsx)(Um,{settingName:n,sectionId:e}),o={true:(0,tt.__)("This payment method requires <settingLink /> to be enabled.","woocommerce-paypal-payments"),false:(0,tt.__)("This payment method requires <settingLink /> to be disabled.","woocommerce-paypal-payments")};return"boolean"==typeof t?(0,X.createInterpolateElement)(o[t],{settingLink:a}):(0,X.createInterpolateElement)((0,tt.sprintf)(/* translators: %s: required setting value */ /* translators: %s: required setting value */
(0,tt.__)('This payment method requires <settingLink /> to be set to "%s".',"woocommerce-paypal-payments"),t),{settingLink:a})},Hm=({isEnabled:e=!1,onToggle:t,label:n="",isDisabled:a=!1,groupName:o=""})=>{let s;if(n)s=n;else{const t=e?(0,tt.__)("Disable","woocommerce-paypal-payments"):(0,tt.__)("Enable","woocommerce-paypal-payments"),n=(0,tt.__)("all %s Methods","woocommerce-paypal-payments");
/* translators: %s: payment method group name */s=(0,tt.sprintf)(/* translators: %1$s: action (Enable/Disable), %2$s: formatted string with payment method group name */ /* translators: %1$s: action (Enable/Disable), %2$s: formatted string with payment method group name */
(0,tt.__)("%1$s %2$s","woocommerce-paypal-payments"),t,(0,tt.sprintf)(n,o))}return(0,ri.jsx)("div",{className:"ppcp-bulk-toggle-payment-gateways",children:(0,ri.jsx)(si.Button,{variant:"tertiary",onClick:t,disabled:a,children:s})})},Wm=({id:e,title:t,description:n,icon:a,methods:o,methodsMap:s={},onTriggerModal:r,isDisabled:i=!1,showBulkToggle:c=!1,groupName:l=""})=>{const{isReady:p,changePaymentSettings:u}=Hr.useStore(),{isReady:d}=Wr.useStore(),{handleHighlightFromUrl:m}=Mi(),{gatewaysRefreshed:h}=Ur.useGatewayRefresh();(()=>{const e=(0,J.useDispatch)(Un),t=(0,J.useDispatch)(Q),{gatewaysRefreshed:n}=Ur.useGatewayRefresh(),{gatewaysSynced:a}=Ur.useGatewaySync(),{refreshGateways:o}=t,{hydrate:s,refresh:r,reset:i}=e,[c,l]=(0,X.useState)(!1),[p,u]=(0,X.useState)(!1),[d,m]=(0,X.useState)(null),h=a,f=(0,X.useCallback)((async()=>{if(p)return{success:!1,skipped:!0,reason:"already-refreshing"};if(!h)return{success:!1,skipped:!0,reason:"not-ready"};u(!0),m(null);try{"function"==typeof i&&await i();const e=await Te()({path:"/wc/v3/wc_paypal/payment",method:"GET"});return s(e),"function"==typeof r&&await r(),await o(),l(!0),{success:!0}}catch(e){return m(e),{success:!1,error:e}}finally{u(!1)}}),[p,h,i,s,r,o]);(0,X.useEffect)((()=>{!h||n||p||c||f().catch((()=>{}))}),[h,n,p,c,f])})();const f=((e,t)=>(0,J.useSelect)((()=>{const n={};return e&&t&&Object.keys(t).length>0&&e.forEach((e=>{if(e&&e.id){const a=((e,t)=>{const n=e.depends_on_payment_methods;return n&&Array.isArray(n)?n.filter((e=>{const n=t[e];return n&&!n.enabled})):[]})(e,t);if(a.length>0){const o=a[0];return void(n[e.id]={type:"parent",isDisabled:!0,parentId:o,parentName:Bm(o,t)})}const o=((e,t)=>{const n=e.depends_on_payment_methods_values;if(!n)return null;for(const[e,a]of Object.entries(n)){const n=t[e];if(n&&"boolean"==typeof a&&n.enabled!==a)return{dependentId:e,dependentName:Bm(e,t),requiredValue:a}}return null})(e,t);o&&(n[e.id]={type:"value",isDisabled:!0,dependentId:o.dependentId,dependentName:o.dependentName,requiredValue:o.requiredValue})}})),n}),[e,t]))(o,s),y=(e=>{const t=(0,J.useSelect)((t=>{const n=t("wc/paypal/settings");if(!n||!e?.length)return null;const a=n.persistentData(),o={};return e.forEach((e=>{if(e?.id&&e.depends_on_settings&&e.depends_on_settings.settings){const t=e.depends_on_settings.settings;for(const[n,s]of Object.entries(t)){const t=s.id,n=s.value;if(a[t]!==n){o[e.id]={isDisabled:!0,settingId:t,requiredValue:n};break}}}})),o}),[e]);return t})(o),g=((e,t,n,a=!1)=>(0,X.useMemo)((()=>{const o={};return e&&e.length?(e.forEach((e=>{if(!e||!e.id)return;let s=null,r=e.isDisabled||a;const i=t?.[e.id];if(i)"parent"===i.type?s=(0,ri.jsx)(Vm,{parentId:i.parentId,parentName:i.parentName}):"value"===i.type&&(s=(0,ri.jsx)($m,{dependentMethodId:i.dependentId,dependentMethodName:i.dependentName,requiredValue:i.requiredValue})),r=!0;else if(n?.[e.id]?.isDisabled){const t=n[e.id];s=(0,ri.jsx)(zm,{settingId:t.settingId,requiredValue:t.requiredValue,methodId:e.id}),r=!0}o[e.id]={dependencyMessage:s,isMethodDisabled:r}})),o):o}),[e,t,n,a]))(o,f,y,i),{allEnabled:v,toggleAllMethods:b,methodCount:w}=(({methods:e=[],methodsMap:t={},changePaymentSettings:n,paymentDependencies:a={},settingDependencies:o={},additionalDeps:s=[],groupName:r=""})=>{const[i,c]=(0,X.useState)(!1),[l,p]=(0,X.useState)([]);(0,X.useEffect)((()=>{if(!e||0===e.length)return c(!1),void p([]);const t=e.filter((e=>{if(!e||!e.id)return!1;const t=a&&a[e.id],n=o&&o[e.id];return!(t||n||e.isDisabled)}));p(t);const n=t.length>0&&t.every((e=>!0===e.enabled));c(n)}),[e,t,a,o,...s]);const u=(0,X.useCallback)((()=>{if(!l.length||!n)return;const e=!i;l.forEach((t=>{n(t.id,{enabled:e})}));const t=e?(0,tt.__)("enabled","woocommerce-paypal-payments"):(0,tt.__)("disabled","woocommerce-paypal-payments"),a=r||(0,tt.__)("payment","woocommerce-paypal-payments"),o=(0,tt.sprintf)(/* translators: %1$s: group name, %2$s: "enabled" or "disabled" */ /* translators: %1$s: group name, %2$s: "enabled" or "disabled" */
(0,tt.__)("All %1$s payment gateways have been %2$s.","woocommerce-paypal-payments"),a,t);(0,xi.speak)(o,"assertive")}),[l,n,i,r]);return{allEnabled:i,toggleAllMethods:u,availableMethods:l,methodCount:l.length}})({methods:o,methodsMap:s,changePaymentSettings:u,paymentDependencies:f,settingDependencies:y,additionalDeps:[i,h],groupName:l});if((0,X.useEffect)((()=>{p&&d&&m()}),[m,p,d]),!p||!d||!h)return(0,ri.jsx)(ii,{asModal:!0});const _=o.map((e=>{const t=g[e.id]||{};return{...e,isDisabled:t.isMethodDisabled||e.isDisabled||i,disabledMessage:t.dependencyMessage}})),S=c?(0,ri.jsxs)("div",{children:[(0,ri.jsx)("p",{children:n}),(0,ri.jsx)(Hm,{isEnabled:v,onToggle:b,isDisabled:i||0===w,groupName:l,methodCount:w})]}):n;return(0,ri.jsx)(_i,{id:e,title:t,description:S,icon:a,contentContainer:!1,children:(0,ri.jsx)(Pm,{paymentMethods:_,onTriggerModal:r})})},Gm=()=>{const e=Hr.usePaymentMethods(),t=Hr.useStore(),{setPersistent:n,changePaymentSettings:a}=t,{activeModal:o,setActiveModal:s}=Ln(),{features:r}=Cr(),i={};e.all.forEach((e=>{i[e.id]=e}));const c=(0,X.useCallback)(((e,t)=>{a(e,{title:t.checkoutPageTitle,description:t.checkoutPageDescription}),["paypalShowLogo","threeDSecure","fastlaneCardholderName","fastlaneDisplayWatermark"].forEach((e=>{e in t&&n(e,t[e])})),s(null)}),[a,s,n]),l=zr.useMerchant(),{storeCountry:p}=Dn(),{canUseCardPayments:u}=Ur.useFlags(),d=e.cardPayment.length>0&&l.isBusinessSeller&&u&&r.some((e=>"advanced_credit_and_debit_cards"===e.id&&e.enabled)),m=e.paypal.filter((e=>"ppcp-card-button-gateway"!==e.id||"MX"===p||!r.some((e=>"advanced_credit_and_debit_cards"===e.id&&!0===e.enabled)))),h=e.apm.length>0&&l.isBusinessSeller;return(0,ri.jsxs)("div",{className:"ppcp-r-payment-methods",children:[(0,ri.jsx)(Wm,{id:"ppcp-paypal-checkout-card",title:(0,tt.__)("PayPal Checkout","woocommerce-paypal-payments"),description:(0,tt.__)("Select your preferred checkout option with PayPal for easy payment processing.","woocommerce-paypal-payments"),icon:"icon-checkout-standard.svg",methods:m,onTriggerModal:s,methodsMap:i}),d&&(0,ri.jsx)(Wm,{id:"ppcp-card-payments-card",title:(0,tt.__)("Online Card Payments","woocommerce-paypal-payments"),description:(0,tt.__)("Select your preferred card payment options for efficient payment processing.","woocommerce-paypal-payments"),icon:"icon-checkout-online-methods.svg",methods:e.cardPayment,onTriggerModal:s,methodsMap:i}),h&&(0,ri.jsx)(Wm,{id:"ppcp-alternative-payments-card",title:(0,tt.__)("Alternative Payment Methods","woocommerce-paypal-payments"),description:(0,tt.__)("With alternative payment methods, customers across the globe can pay with their bank accounts and other local payment methods.","woocommerce-paypal-payments"),icon:"icon-checkout-alternative-methods.svg",methods:e.apm,onTriggerModal:s,methodsMap:i,showBulkToggle:e.apm.length>1,groupName:"Alternative Payment"}),o&&(0,ri.jsx)(Fm,{method:o?e.all.find((e=>e.id===o)):null,setModalIsVisible:()=>s(null),onSave:c})]})},Ym=({isActive:e,isSandbox:t,isBusinessSeller:n})=>{if(e){let e;return e=n?t?(0,tt.__)("Business | Sandbox","woocommerce-paypal-payments"):(0,tt.__)("Business | Live","woocommerce-paypal-payments"):t?(0,tt.__)("Sandbox","woocommerce-paypal-payments"):(0,tt.__)("Active","woocommerce-paypal-payments"),(0,ri.jsx)(gc,{type:yc,text:e})}return(0,ri.jsx)(gc,{type:"negative",text:(0,tt.__)("Not Connected","woocommerce-paypal-payments")})},qm=({type:e,className:t,spacing:n,children:a})=>{const o=Z()("components-flex",`components-${e}-stack`,t),s={gap:`calc(${4*n}px)`};return(0,ri.jsx)("div",{className:o,style:s,children:a})},Xm=({className:e,spacing:t=3,children:n})=>(0,ri.jsx)(qm,{type:"h",className:e,spacing:t,children:n}),Km=({className:e,spacing:t=3,children:n})=>(0,ri.jsx)(qm,{type:"v",className:e,spacing:t,children:n}),Zm=()=>{const{isOpen:e,setIsOpen:t}=Zi("disconnect-merchant"),[n,a]=(0,X.useState)(!1),{disconnectMerchant:o}=zr.useDisconnectMerchant(),{goToPluginSettings:s}=Mi(),r=(0,X.useCallback)((()=>{t(!0)}),[t]),i=(0,X.useCallback)((()=>{t(!1)}),[t]),c=(0,X.useCallback)((async()=>{await o(n),s()}),[o,n]),l=(0,tt.__)("Disconnect from PayPal?","woocommerce-paypal-payments");return(0,ri.jsxs)(ri.Fragment,{children:[(0,ri.jsx)(si.Button,{variant:"tertiary",isDestructive:!0,onClick:r,children:(0,tt.__)("Disconnect","woocommerce-paypal-payments")}),e&&(0,ri.jsxs)(si.Modal,{className:"ppcp--modal-disconnect",size:"small",title:l,onRequestClose:i,children:[(0,ri.jsx)("p",{children:(0,tt.__)("Disconnecting your account will restart the connection wizard. Are you sure you want to disconnect from your PayPal account?","woocommerce-paypal-payments")}),(0,ri.jsx)(si.ToggleControl,{__nextHasNoMarginBottom:!0,className:"ppcp--toggle-danger",checked:n,onChange:a,label:(0,tt.__)("Start over","woocommerce-paypal-payments"),help:n?(0,tt.__)("Attention: The plugin is reset to its initial state!","woocommerce-paypal-payments"):(0,tt.__)("Disconnect, but preserve all settings","woocommerce-paypal-payments")}),(0,ri.jsxs)(Xm,{className:"ppcp--action-buttons",children:[(0,ri.jsx)(si.Button,{variant:"tertiary",onClick:i,children:(0,tt.__)("Cancel","woocommerce-paypal-payments")}),(0,ri.jsx)(si.Button,{variant:"primary",isDestructive:n,onClick:c,children:(0,tt.__)("Disconnect","woocommerce-paypal-payments")})]})]})]})},Jm=({value:e})=>(0,ri.jsx)(li,{children:(0,ri.jsx)("div",{className:"ppcp--static-value",children:e})}),Qm=({value:e,description:t,onChange:n,placeholder:a=""})=>(0,ri.jsxs)(li,{children:[(0,ri.jsx)(si.TextControl,{__nextHasNoMarginBottom:!0,className:"ppcp-r-vertical-text-control",placeholder:a,value:e,onChange:n}),(0,ri.jsx)(mi,{children:t})]}),eh=({id:e="",label:t,description:n,value:a,onChange:o,disabled:s=!1})=>(0,ri.jsx)(li,{id:e,children:(0,ri.jsx)(si.ToggleControl,{className:"ppcp--control-toggle",__nextHasNoMarginBottom:!0,checked:a,onChange:o,label:t,help:n?(0,ri.jsx)(mi,{children:n}):null,disabled:s})}),th=({type:e="secondary",isBusy:t,onClick:n,buttonLabel:a})=>(0,ri.jsx)(li,{children:(0,ri.jsx)(si.Button,{className:"small-button",isBusy:t,variant:e,onClick:n,children:a})}),nh=({options:e,value:t,onChange:n})=>(0,ri.jsx)(li,{children:(0,ri.jsx)(qc,{options:e,selected:t,onChange:n})}),ah=({options:e,value:t,onChange:n,placeholder:a,isMulti:o=!1})=>(0,ri.jsx)(li,{children:(0,ri.jsx)(im,{isMulti:o,options:e,value:t,placeholder:a,onChange:n})}),oh=()=>{const e=zr.useMerchant(),t=Z()("ppcp-connection-details ppcp--value-list",{"ppcp--type-business":e.isBusinessSeller,"ppcp--type-casual":e.isCasualSeller});return(0,ri.jsxs)(_i,{className:t,title:(0,tt.__)("Connection status","woocommerce-paypal-payments"),description:(0,ri.jsx)(sh,{}),children:[(0,ri.jsx)(bm,{className:"ppcp--pull-right",children:(0,ri.jsx)(Jm,{value:(0,ri.jsx)(Ym,{isActive:e.isConnected,isSandbox:e.isSandbox,isBusinessSeller:e.isBusinessSeller})})}),(0,ri.jsx)(bm,{title:(0,tt.__)("Merchant ID","woocommerce-paypal-payments"),className:"ppcp--no-gap",children:(0,ri.jsx)(Jm,{value:e.id})}),(0,ri.jsx)(bm,{title:(0,tt.__)("Email address","woocommerce-paypal-payments"),children:(0,ri.jsx)(Jm,{value:e.email})}),(0,ri.jsx)(bm,{title:(0,tt.__)("Client ID","woocommerce-paypal-payments"),children:(0,ri.jsx)(Jm,{value:e.clientId})})]})},sh=()=>(0,ri.jsxs)(ri.Fragment,{children:[(0,tt.__)("Your PayPal account connection details.","woocommerce-paypal-payments"),(0,ri.jsx)(pi,{isDimmed:!0,children:(0,ri.jsx)(Zm,{})})]}),rh=()=>{const{authorizeOnly:e,setAuthorizeOnly:t,captureVirtualOnlyOrders:n,setCaptureVirtualOnlyOrders:a}=Wr.useSettings();return(0,G.useEffect)((()=>{!e&&n&&a(!1)}),[e]),(0,ri.jsxs)(bm,{title:(0,tt.__)("Order Intent","woocommerce-paypal-payments"),description:(0,tt.__)("Choose between immediate capture or authorization-only, with manual capture in the Order section.","woocommerce-paypal-payments"),className:"ppcp--order-intent",children:[(0,ri.jsx)(eh,{label:(0,tt.__)("Authorize Only","woocommerce-paypal-payments"),onChange:t,value:e}),(0,ri.jsx)(eh,{label:(0,tt.__)("Capture Virtual-Only Orders","woocommerce-paypal-payments"),onChange:a,value:n,disabled:!e})]})},ih=({ownBradOnly:e})=>{const{savePaypalAndVenmo:t,setSavePaypalAndVenmo:n,saveCardDetails:a,setSaveCardDetails:o}=Wr.useSettings(),{features:s}=An();return(0,ri.jsxs)(bm,{title:(0,tt.__)("Save payment methods","woocommerce-paypal-payments"),description:(0,tt.__)("Securely store customers' payment methods for future payments and subscriptions, simplifying checkout and enabling recurring transactions.","woocommerce-paypal-payments"),className:"ppcp--save-payment-methods",children:[(0,ri.jsx)(eh,{id:"ppcp-save-paypal-and-venmo",label:(0,tt.__)("Save PayPal and Venmo","woocommerce-paypal-payments"),description:(0,tt.sprintf)(/* translators: 1: URL to Pay Later documentation */ /* translators: 1: URL to Pay Later documentation */
(0,tt.__)('Securely store your customers\' PayPal accounts for a seamless checkout experience. <br />This will disable the <a target="_blank" rel="noreferrer" href="%1$s">Pay Later</a> payment method on your site.',"woocommerce-paypal-payments"),"https://woocommerce.com/document/woocommerce-paypal-payments/#pay-later"),value:!!s.save_paypal_and_venmo.enabled&&t,onChange:n,disabled:!s.save_paypal_and_venmo.enabled}),e||(0,ri.jsx)(eh,{label:(0,tt.__)("Save Credit and Debit Cards","woocommerce-paypal-payments"),description:(0,tt.__)("Securely store your customer's credit card.","woocommerce-paypal-payments"),onChange:o,value:a})]})},ch=()=>{const{invoicePrefix:e,setInvoicePrefix:t}=Wr.useSettings();return(0,ri.jsx)(bm,{title:"Invoice Prefix",titleSuffix:(0,tt.__)("(Recommended)","woocommerce-paypal-payments"),className:"ppcp--invoice-prefix",children:(0,ri.jsx)(Qm,{placeholder:(0,tt.__)("Input prefix","woocommerce-paypal-payments"),onChange:t,value:e,description:"Add a unique prefix to invoice numbers for site-specific tracking (recommended)."})})},lh=()=>{const{payNowExperience:e,setPayNowExperience:t}=Wr.useSettings();return(0,ri.jsx)(bm,{className:"ppcp--pay-now-experience",children:(0,ri.jsx)(eh,{label:(0,tt.__)("Pay Now Experience","woocommerce-paypal-payments"),description:(0,tt.__)("Let PayPal customers skip the Order Review page by selecting shipping options directly within PayPal.","woocommerce-paypal-payments"),onChange:t,value:e})})},ph=()=>{const{stayUpdated:e,setStayUpdated:t}=Wr.useSettings();return(0,ri.jsx)(bm,{className:"ppcp--pay-now-experience",children:(0,ri.jsx)(eh,{label:(0,tt.__)("Stay Updated","woocommerce-paypal-payments"),description:(0,tt.__)("Get the latest PayPal features and capabilities as they are released. When the extension is updated, new features, payment methods, styling options, and more will automatically update.","woocommerce-paypal-payments"),onChange:t,value:e})})},uh=({ownBradOnly:e})=>(0,ri.jsxs)(_i,{icon:"icon-settings-common.svg",title:(0,tt.__)("Common settings","woocommerce-paypal-payments"),className:"ppcp-r-settings-card ppcp-r-settings-card--common-settings",description:(0,tt.__)("Customize key features to tailor your PayPal experience.","woocommerce-paypal-payments"),children:[(0,ri.jsx)(ch,{}),(0,ri.jsx)(rh,{}),(0,ri.jsx)(ih,{ownBradOnly:e}),(0,ri.jsx)(lh,{}),(0,ri.jsx)(ph,{})]}),dh=()=>{const{createSuccessNotice:e,createInfoNotice:t,createErrorNotice:n,removeNotice:a}=(0,J.useDispatch)(Ac.store),{startWebhookSimulation:o,checkWebhookSimulationState:s}=zr.useWebhooks(),[r,i]=(0,X.useState)(!1),c=e=>new Promise((t=>setTimeout(t,e)));return(0,ri.jsx)(bm,{title:(0,tt.__)("Test webhooks","woocommerce-paypal-payments"),description:(0,tt.__)("Send a test-webhook from PayPal to confirm that webhooks are being received and processed correctly.","woocommerce-paypal-payments"),horizontalLayout:!0,className:"ppcp--webhook-simulation",children:(0,ri.jsx)(th,{type:"secondary",isBusy:r,onClick:()=>(async()=>{const r="paypal-webhook-simulation-info-notice",l=()=>{t((0,tt.__)("Waiting for the webhook to arrive…","woocommerce-paypal-payments"),{id:r})},p=()=>{a(r),i(!1)};i(!0),l();try{await o()}catch(e){return console.error(e),i(!1),void n((0,tt.__)("Operation failed. Check WooCommerce logs for more details.","woocommerce-paypal-payments"),{icon:ac})}for(let t=0;t<30;t++){await c(2e3);const t=await s();try{if(!t.success){console.error("Simulation state query failed: "+t?.data);continue}if("received"===t?.data?.state)return e((0,tt.__)("The webhook was received successfully.","woocommerce-paypal-payments"),{icon:nc}),void p();a(r),l()}catch(e){console.error(e)}}p(),n((0,tt.__)("Looks like the webhook cannot be received. Check that your website is accessible from the internet.","woocommerce-paypal-payments"),{icon:ac})})(),buttonLabel:(0,tt.__)("Simulate webhooks","woocommerce-paypal-payments")})})},mh=()=>{const{createSuccessNotice:e,createErrorNotice:t}=(0,J.useDispatch)(Ac.store),[n,a]=(0,X.useState)(!1),{resubscribeWebhooks:o}=(0,J.useDispatch)(wt);return(0,ri.jsx)(bm,{title:(0,tt.__)("Resubscribe webhooks","woocommerce-paypal-payments"),description:(0,tt.__)("Click to remove the current webhook subscription and subscribe again, for example, if the website domain or URL structure changed.","woocommerce-paypal-payments"),horizontalLayout:!0,className:"ppcp--webhook-resubscribe",children:(0,ri.jsx)(th,{type:"secondary",isBusy:n,onClick:()=>(async()=>{a(!0);try{await o()}catch(e){return a(!1),void t((0,tt.__)("Operation failed. Check WooCommerce logs for more details.","woocommerce-paypal-payments"),{icon:ac})}a(!1),e((0,tt.__)("Webhooks were successfully re-subscribed.","woocommerce-paypal-payments"),{icon:nc})})(),buttonLabel:(0,tt.__)("Resubscribe webhooks","woocommerce-paypal-payments")})})},hh=({url:e})=>(0,ri.jsxs)("div",{children:[(0,ri.jsx)(vi,{children:(0,tt.__)("Notification URL","woocommerce-paypal-payments")}),(0,ri.jsx)("p",{children:e})]}),fh=({events:e})=>(0,ri.jsxs)("div",{children:[(0,ri.jsx)(vi,{children:(0,tt.__)("Subscribed Events","woocommerce-paypal-payments")}),(0,ri.jsx)("ul",{className:"ppcp--webhook-list",children:e.map(((e,t)=>(0,ri.jsx)("li",{children:e},t)))})]}),yh=()=>{const{webhooks:e}=zr.useWebhooks(),{url:t,events:n}=e;return t&&n?.length?(0,ri.jsxs)(bm,{separatorAndGap:!1,className:"ppcp--webhooks",children:[(0,ri.jsx)(hh,{url:t}),(0,ri.jsx)(fh,{events:n})]}):(0,ri.jsx)("div",{children:"..."})},gh=()=>{const{logging:e,setLogging:t}=Wr.useSettings();return(0,ri.jsxs)(Ji,{className:"ppcp--troubleshooting",title:(0,tt.__)("Troubleshooting","woocommerce-paypal-payments"),description:(0,tt.__)("Access tools to help debug and resolve issues.","woocommerce-paypal-payments"),children:[(0,ri.jsx)(bm,{children:(0,ri.jsx)(eh,{label:(0,tt.__)("Logging","woocommerce-paypal-payments"),description:(0,tt.__)("Log additional debugging information in the WooCommerce logs that can assist technical staff to determine issues.","woocommerce-paypal-payments"),value:e,onChange:t})}),(0,ri.jsxs)(bm,{title:(0,tt.__)("Webhooks","woocommerce-paypal-payments"),description:(0,tt.sprintf)((0,tt.__)('The following PayPal webhooks are subscribed. More information about the webhooks is available in the <a href="%s">Webhook Status documentation</a>.',"woocommerce-paypal-payments"),"https://woocommerce.com/document/woocommerce-paypal-payments/#webhook-status"),children:[(0,ri.jsx)(yh,{}),(0,ri.jsx)(mh,{}),(0,ri.jsx)(dh,{})]})]})},vh=({value:e,onChange:t,placeholder:n})=>(0,ri.jsx)(Qm,{value:e,onChange:e=>{e.length<=22&&t(e)},placeholder:n}),bh=[{value:"correction",label:(0,tt.__)("Add a correction","woocommerce-paypal-payments"),description:(0,tt.__)("Adds an additional line item with the missing amount.","woocommerce-paypal-payments")},{value:"no_details",label:(0,tt.__)("Do not send line items","woocommerce-paypal-payments"),description:(0,tt.__)("Resubmit the transaction without line item details.","woocommerce-paypal-payments")}],wh=[{value:"any",label:(0,tt.__)("No preference","woocommerce-paypal-payments"),description:(0,tt.__)("Shows the buyer the PayPal login for a recognized PayPal buyer.","woocommerce-paypal-payments")},{value:"login",label:(0,tt.__)("Login page","woocommerce-paypal-payments"),description:(0,tt.__)("Always show the buyer the PayPal login screen.","woocommerce-paypal-payments")},{value:"guest_checkout",label:(0,tt.__)("Guest checkout page","woocommerce-paypal-payments"),description:(0,tt.__)("Always show the buyer the guest checkout fields first.","woocommerce-paypal-payments")}],_h=({hasContactModule:e})=>{const{savePaypalAndVenmo:t,setSavePaypalAndVenmo:n,contactModule:a,setContactModule:o,subtotalAdjustment:s,setSubtotalAdjustment:r,brandName:i,setBrandName:c,softDescriptor:l,setSoftDescriptor:p,landingPage:u,setLandingPage:d,buttonLanguage:m,setButtonLanguage:h}=Wr.useSettings(),f=(0,J.useSelect)((e=>e("core").getSite()),[]),y=f?.title,g=window.ppcpSettings.buttonLanguageChoices;return(0,ri.jsxs)(Ji,{className:"ppcp--paypal-settings",title:(0,tt.__)("PayPal Settings","woocommerce-paypal-payments"),description:(0,tt.__)("Modify the PayPal checkout experience.","woocommerce-paypal-payments"),children:[(0,ri.jsx)(bm,{title:(0,tt.__)("Subtotal mismatch fallback","woocommerce-paypal-payments"),description:(0,tt.__)("Due to differences in how WooCommerce and PayPal calculates taxes, some transactions may fail due to a rounding error. This settings determines the fallback behavior.","woocommerce-paypal-payments"),children:(0,ri.jsx)(nh,{options:bh,value:s,onChange:r})}),(0,ri.jsx)(bm,{children:(0,ri.jsx)(eh,{label:(0,tt.__)("Instant payments only","woocommerce-paypal-payments"),description:(0,tt.__)("If enabled, PayPal will not allow buyers to use funding sources that take additional time to complete, such as eChecks.","woocommerce-paypal-payments"),value:t,onChange:n})}),(0,ri.jsx)(bm,{visible:e,children:(0,ri.jsx)(eh,{label:(0,tt.__)("Contact selection on payment","woocommerce-paypal-payments"),description:(0,tt.__)("Allow customers to choose an alternative email and phone number from their PayPal contacts during payment. Order confirmations and tracking updates are sent to the selected contacts instead of checkout details. Perfect for gift orders.","woocommerce-paypal-payments"),value:a,onChange:o})}),(0,ri.jsx)(bm,{title:(0,tt.__)("Brand name","woocommerce-paypal-payments"),description:(0,tt.__)("What business name to show to your buyers during checkout and on receipts.","woocommerce-paypal-payments"),children:(0,ri.jsx)(Qm,{value:i,onChange:c,placeholder:y||(0,tt.__)("Brand name","woocommerce-paypal-payments")})}),(0,ri.jsx)(bm,{title:(0,tt.__)("Soft Descriptor","woocommerce-paypal-payments"),description:(0,tt.__)("The dynamic text used to construct the statement descriptor that appears on a payer's card statement. Applies to PayPal and Credit Card transactions. Max value of 22 characters.","woocommerce-paypal-payments"),children:(0,ri.jsx)(vh,{value:l,onChange:p,placeholder:(0,tt.__)("Soft Descriptor","woocommerce-paypal-payments")})}),(0,ri.jsx)(bm,{title:(0,tt.__)("PayPal landing page","woocommerce-paypal-payments"),description:(0,tt.__)("Determine which experience a buyer sees when they click the PayPal button.","woocommerce-paypal-payments"),children:(0,ri.jsx)(nh,{options:wh,value:u,onChange:d})}),(0,ri.jsx)(bm,{title:(0,tt.__)("Button Language","woocommerce-paypal-payments"),description:(0,tt.__)("If left blank, PayPal and other buttons will present in the user's detected language. Enter a language here to force all buttons to display in that language.","woocommerce-paypal-payments"),children:(0,ri.jsx)(ah,{options:g,value:m,onChange:h,placeholder:(0,tt.__)("Browser language","woocommerce-paypal-payments")})})]})},Sh=()=>{const{disabledCards:e,setDisabledCards:t,threeDSecure:n,setThreeDSecure:a}=Wr.useSettings(),o=window.ppcpSettings.disabledCardsChoices,s=window.ppcpSettings.threeDSecureOptions;return(0,ri.jsxs)(Ji,{title:(0,tt.__)("Other payment method settings","woocommerce-paypal-payments"),description:(0,tt.__)("Modify the checkout experience for alternative payment methods, credit cards, and digital wallets.","woocommerce-paypal-payments"),children:[(0,ri.jsx)(bm,{title:(0,tt.__)("Disable specific credit cards","woocommerce-paypal-payments"),description:(0,tt.__)("By default, all possible credit cards will be accepted. Card types added here will be rejected at checkout.","woocommerce-paypal-payments"),children:(0,ri.jsx)(ah,{options:o,value:e,onChange:t,isMulti:!0,placeholder:(0,tt.__)("Show all cards","woocommerce-paypal-payments")})}),(0,ri.jsx)(bm,{title:(0,tt.__)("3D Secure","woocommerce-paypal-payments"),description:(0,tt.__)("Authenticate cardholders through their card issuers to reduce fraud and improve transaction security. Successful 3D Secure authentication can shift liability for fraudulent chargebacks to the card issuer.","woocommerce-paypal-payments"),children:(0,ri.jsx)(nh,{options:s,value:n,onChange:a})})]})},xh=({ownBradOnly:e,hasContactModule:t})=>(0,ri.jsx)(_i,{icon:"icon-settings-expert.svg",className:"ppcp-r-settings-card ppcp-r-settings-card--expert-settings",title:(0,tt.__)("Expert Settings","woocommerce-paypal-payments"),description:(0,tt.__)("Fine-tune your PayPal experience with advanced options.","woocommerce-paypal-payments"),actionProps:{key:"payNowExperience"},contentContainer:!1,children:(0,ri.jsxs)(di,{children:[(0,ri.jsx)(ui,{children:(0,ri.jsx)(gh,{})}),(0,ri.jsx)(ui,{children:(0,ri.jsx)(_h,{hasContactModule:t})}),e||(0,ri.jsx)(ui,{children:(0,ri.jsx)(Sh,{})})]})}),Ch=()=>{const{ownBrandOnly:e}=zr.useWooSettings(),{isReady:t}=Wr.useStore(),{features:n}=zr.useMerchantInfo();return t?(0,ri.jsxs)("div",{className:"ppcp-r-settings",children:[(0,ri.jsx)(oh,{}),(0,ri.jsx)(uh,{ownBradOnly:e}),(0,ri.jsx)(xh,{ownBradOnly:e,hasContactModule:n?.contact_module?.enabled})]}):(0,ri.jsx)(ii,{asModal:!0})};var Ph,jh,kh;!function(e){e.INITIAL="initial",e.PENDING="pending",e.REJECTED="rejected",e.RESOLVED="resolved"}(Ph||(Ph={})),function(e){e.LOADING_STATUS="setLoadingStatus",e.RESET_OPTIONS="resetOptions",e.SET_BRAINTREE_INSTANCE="braintreeInstance"}(jh||(jh={})),function(e){e.NUMBER="number",e.CVV="cvv",e.EXPIRATION_DATE="expirationDate",e.EXPIRATION_MONTH="expirationMonth",e.EXPIRATION_YEAR="expirationYear",e.POSTAL_CODE="postalCode"}(kh||(kh={}));var Eh=function(){return Eh=Object.assign||function(e){for(var t,n=1,a=arguments.length;n<a;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},Eh.apply(this,arguments)};function Oh(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(a=Object.getOwnPropertySymbols(e);o<a.length;o++)t.indexOf(a[o])<0&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(n[a[o]]=e[a[o]])}return n}function Th(e,t,n){if(n||2===arguments.length)for(var a,o=0,s=t.length;o<s;o++)!a&&o in t||(a||(a=Array.prototype.slice.call(t,0,o)),a[o]=t[o]);return e.concat(a||Array.prototype.slice.call(t))}"function"==typeof SuppressedError&&SuppressedError;var Mh="data-react-paypal-script-id",Nh="react-paypal-js",Dh="dataNamespace",Ih="dataSdkIntegrationSource",Ah="3.84.0",Rh=("https://js.braintreegateway.com/web/".concat(Ah,"/js/client.min.js"),"https://js.braintreegateway.com/web/".concat(Ah,"/js/paypal-checkout.min.js"),"paypal");function Lh(e){return void 0===e&&(e=Rh),window[e]}function Fh(e){var t=e.reactComponentName,n=e.sdkComponentKey,a=e.sdkRequestedComponents,o=void 0===a?"":a,s=e.sdkDataNamespace,r=void 0===s?Rh:s,i=n.charAt(0).toUpperCase().concat(n.substring(1)),c="Unable to render <".concat(t," /> because window.").concat(r,".").concat(i," is undefined."),l="string"==typeof o?o:o.join(",");if(!l.includes(n)){var p=[l,n].filter(Boolean).join();c+="\nTo fix the issue, add '".concat(n,"' to the list of components passed to the parent PayPalScriptProvider:")+"\n`<PayPalScriptProvider options={{ components: '".concat(p,"'}}>`.")}return c}function Bh(e){var t=e,n=Mh;t[n];var a=Oh(t,[n+""]);return"react-paypal-js-".concat(function(e){for(var t="",n=0;n<e.length;n++){var a=e[n].charCodeAt(0)*n;e[n+1]&&(a+=e[n+1].charCodeAt(0)*(n-1)),t+=String.fromCharCode(97+Math.abs(a)%26)}return t}(JSON.stringify(a)))}function Vh(e,t){var n,a,o,s;switch(t.type){case jh.LOADING_STATUS:return"object"==typeof t.value?Eh(Eh({},e),{loadingStatus:t.value.state,loadingStatusErrorMessage:t.value.message}):Eh(Eh({},e),{loadingStatus:t.value});case jh.RESET_OPTIONS:return o=e.options[Mh],(null==(s=self.document.querySelector("script[".concat(Mh,'="').concat(o,'"]')))?void 0:s.parentNode)&&s.parentNode.removeChild(s),Eh(Eh({},e),{loadingStatus:Ph.PENDING,options:Eh(Eh((n={},n[Ih]=Nh,n),t.value),(a={},a[Mh]="".concat(Bh(t.value)),a))});case jh.SET_BRAINTREE_INSTANCE:return Eh(Eh({},e),{braintreePayPalCheckoutInstance:t.value});default:return e}}var $h=(0,G.createContext)(null);function Uh(){var e=function(e){if("function"==typeof(null==e?void 0:e.dispatch)&&0!==e.dispatch.length)return e;throw new Error("usePayPalScriptReducer must be used within a PayPalScriptProvider")}((0,G.useContext)($h));return[Eh(Eh({},e),{isInitial:e.loadingStatus===Ph.INITIAL,isPending:e.loadingStatus===Ph.PENDING,isResolved:e.loadingStatus===Ph.RESOLVED,isRejected:e.loadingStatus===Ph.REJECTED}),e.dispatch]}(0,G.createContext)({});var zh=function(e){var t,n=e.className,a=void 0===n?"":n,o=e.disabled,s=void 0!==o&&o,r=e.children,i=e.forceReRender,c=void 0===i?[]:i,l=Oh(e,["className","disabled","children","forceReRender"]),p=s?{opacity:.38}:{},u="".concat(a," ").concat(s?"paypal-buttons-disabled":"").trim(),d=(0,G.useRef)(null),m=(0,G.useRef)(null),h=Uh()[0],f=h.isResolved,y=h.options,g=(0,G.useState)(null),v=g[0],b=g[1],w=(0,G.useState)(!0),_=w[0],S=w[1],x=(0,G.useState)(null)[1];function C(){null!==m.current&&m.current.close().catch((function(){}))}return(null===(t=m.current)||void 0===t?void 0:t.updateProps)&&m.current.updateProps({message:l.message}),(0,G.useEffect)((function(){if(!1===f)return C;var e=Lh(y.dataNamespace);if(void 0===e||void 0===e.Buttons)return x((function(){throw new Error(Fh({reactComponentName:zh.displayName,sdkComponentKey:"buttons",sdkRequestedComponents:y.components,sdkDataNamespace:y[Dh]}))})),C;try{m.current=e.Buttons(Eh(Eh({},l),{onInit:function(e,t){b(t),"function"==typeof l.onInit&&l.onInit(e,t)}}))}catch(e){return x((function(){throw new Error("Failed to render <PayPalButtons /> component. Failed to initialize:  ".concat(e))}))}return!1===m.current.isEligible()?(S(!1),C):d.current?(m.current.render(d.current).catch((function(e){null!==d.current&&0!==d.current.children.length&&x((function(){throw new Error("Failed to render <PayPalButtons /> component. ".concat(e))}))})),C):C}),Th(Th([f],c,!0),[l.fundingSource],!1)),(0,G.useEffect)((function(){null!==v&&(!0===s?v.disable().catch((function(){})):v.enable().catch((function(){})))}),[s,v]),Y().createElement(Y().Fragment,null,_?Y().createElement("div",{ref:d,style:p,className:u}):r)};function Hh(e,t){void 0===t&&(t={});var n=document.createElement("script");return n.src=e,Object.keys(t).forEach((function(e){n.setAttribute(e,t[e]),"data-csp-nonce"===e&&n.setAttribute("nonce",t["data-csp-nonce"])})),n}function Wh(e,t){if(void 0===t&&(t=Promise),Yh(e,t),"undefined"==typeof document)return t.resolve(null);var n=function(e){var t="sandbox"===e.environment?"https://www.sandbox.paypal.com/sdk/js":"https://www.paypal.com/sdk/js";delete e.environment,e.sdkBaseUrl&&(t=e.sdkBaseUrl,delete e.sdkBaseUrl);var n,a,o=e,s=Object.keys(o).filter((function(e){return void 0!==o[e]&&null!==o[e]&&""!==o[e]})).reduce((function(e,t){var n,a=o[t].toString();return n=function(e,t){return(t?"-":"")+e.toLowerCase()},"data"===(t=t.replace(/[A-Z]+(?![a-z])|[A-Z]/g,n)).substring(0,4)||"crossorigin"===t?e.attributes[t]=a:e.queryParams[t]=a,e}),{queryParams:{},attributes:{}}),r=s.queryParams,i=s.attributes;return r["merchant-id"]&&-1!==r["merchant-id"].indexOf(",")&&(i["data-merchant-id"]=r["merchant-id"],r["merchant-id"]="*"),{url:"".concat(t,"?").concat((n=r,a="",Object.keys(n).forEach((function(e){0!==a.length&&(a+="&"),a+=e+"="+n[e]})),a)),attributes:i}}(e),a=n.url,o=n.attributes,s=o["data-namespace"]||"paypal",r=Gh(s);return o["data-js-sdk-library"]||(o["data-js-sdk-library"]="paypal-js"),function(e,t){var n=document.querySelector('script[src="'.concat(e,'"]'));if(null===n)return null;var a=Hh(e,t),o=n.cloneNode();if(delete o.dataset.uidAuto,Object.keys(o.dataset).length!==Object.keys(a.dataset).length)return null;var s=!0;return Object.keys(o.dataset).forEach((function(e){o.dataset[e]!==a.dataset[e]&&(s=!1)})),s?n:null}(a,o)&&r?t.resolve(r):function(e,t){void 0===t&&(t=Promise),Yh(e,t);var n=e.url,a=e.attributes;if("string"!=typeof n||0===n.length)throw new Error("Invalid url.");if(void 0!==a&&"object"!=typeof a)throw new Error("Expected attributes to be an object.");return new t((function(e,t){if("undefined"==typeof document)return e();!function(e){var t=e.url,n=e.attributes,a=e.onSuccess,o=e.onError,s=Hh(t,n);s.onerror=o,s.onload=a,document.head.insertBefore(s,document.head.firstElementChild)}({url:n,attributes:a,onSuccess:function(){return e()},onError:function(){var e=new Error('The script "'.concat(n,'" failed to load. Check the HTTP status code and response body in DevTools to learn more.'));return t(e)}})}))}({url:a,attributes:o},t).then((function(){var e=Gh(s);if(e)return e;throw new Error("The window.".concat(s," global variable is not available."))}))}function Gh(e){return window[e]}function Yh(e,t){if("object"!=typeof e||null===e)throw new Error("Expected an options object.");var n=e.environment;if(n&&"production"!==n&&"sandbox"!==n)throw new Error('The `environment` option must be either "production" or "sandbox".');if(void 0!==t&&"function"!=typeof t)throw new Error("Expected PromisePonyfill to be a function.")}zh.displayName="PayPalButtons";var qh=function(e){var t=e.className,n=void 0===t?"":t,a=e.children,o=Oh(e,["className","children"]),s=Uh()[0],r=s.isResolved,i=s.options,c=(0,G.useRef)(null),l=(0,G.useState)(!0),p=l[0],u=l[1],d=(0,G.useState)(null)[1];return(0,G.useEffect)((function(){if(!1!==r){var e=Lh(i[Dh]);if(void 0===e||void 0===e.Marks)return d((function(){throw new Error(Fh({reactComponentName:qh.displayName,sdkComponentKey:"marks",sdkRequestedComponents:i.components,sdkDataNamespace:i[Dh]}))}));!function(e){var t=c.current;if(!t||!e.isEligible())return u(!1);t.firstChild&&t.removeChild(t.firstChild),e.render(t).catch((function(e){null!==t&&0!==t.children.length&&d((function(){throw new Error("Failed to render <PayPalMarks /> component. ".concat(e))}))}))}(e.Marks(Eh({},o)))}}),[r,o.fundingSource]),Y().createElement(Y().Fragment,null,p?Y().createElement("div",{ref:c,className:n}):a)};qh.displayName="PayPalMarks";var Xh=function(e){var t=e.className,n=void 0===t?"":t,a=e.forceReRender,o=void 0===a?[]:a,s=Oh(e,["className","forceReRender"]),r=Uh()[0],i=r.isResolved,c=r.options,l=(0,G.useRef)(null),p=(0,G.useRef)(null),u=(0,G.useState)(null)[1];return(0,G.useEffect)((function(){if(!1!==i){var e=Lh(c[Dh]);if(void 0===e||void 0===e.Messages)return u((function(){throw new Error(Fh({reactComponentName:Xh.displayName,sdkComponentKey:"messages",sdkRequestedComponents:c.components,sdkDataNamespace:c[Dh]}))}));p.current=e.Messages(Eh({},s)),p.current.render(l.current).catch((function(e){null!==l.current&&0!==l.current.children.length&&u((function(){throw new Error("Failed to render <PayPalMessages /> component. ".concat(e))}))}))}}),Th([i],o,!0)),Y().createElement("div",{ref:l,className:n})};Xh.displayName="PayPalMessages";var Kh=function(e){var t,n=e.options,a=void 0===n?{clientId:"test"}:n,o=e.children,s=e.deferLoading,r=void 0!==s&&s,i=(0,G.useReducer)(Vh,{options:Eh(Eh({},a),(t={},t.dataJsSdkLibrary=Nh,t[Ih]=Nh,t[Mh]="".concat(Bh(a)),t)),loadingStatus:r?Ph.INITIAL:Ph.PENDING}),c=i[0],l=i[1];return(0,G.useEffect)((function(){if(!1===r&&c.loadingStatus===Ph.INITIAL)return l({type:jh.LOADING_STATUS,value:Ph.PENDING});if(c.loadingStatus===Ph.PENDING){var e=!0;return Wh(c.options).then((function(){e&&l({type:jh.LOADING_STATUS,value:Ph.RESOLVED})})).catch((function(t){console.error("".concat("Failed to load the PayPal JS SDK script."," ").concat(t)),e&&l({type:jh.LOADING_STATUS,value:{state:Ph.REJECTED,message:String(t)}})})),function(){e=!1}}}),[c.options,r,c.loadingStatus]),Y().createElement($h.Provider,{value:Eh(Eh({},c),{dispatch:l})},o)};function Zh(){}(0,G.createContext)({cardFieldsForm:null,fields:{},registerField:Zh,unregisterField:Zh});const Jh=({location:e})=>{const{paymentMethods:t}=Gr.usePaymentMethodProps(e),{layout:n}=Gr.useLayoutProps(e),{shape:a}=Gr.useShapeProps(e),{label:o}=Gr.useLabelProps(e),{color:s}=Gr.useColorProps(e),{tagline:r}=Gr.useTaglineProps(e),i=(0,X.useMemo)((()=>({layout:n,shape:a,label:o,color:s,tagline:r})),[s,o,n,a,r]),c=(0,X.useMemo)((()=>{const e=["card"];return Object.values(mo).filter((e=>e.isFunding)).filter((e=>!t.includes(e.value))).forEach((t=>{e.push(t.value)})),e}),[t]),l=(0,X.useMemo)((()=>({clientId:"test",merchantId:"QTQX5NP6N9WZU",components:"buttons","disable-funding":c.join(","),"buyer-country":"US",currency:"USD"})),[c]);return(0,ri.jsx)("div",{className:"preview-panel",children:(0,ri.jsx)("div",{className:"preview-panel-inner",children:(0,ri.jsx)(Kh,{options:l,children:(0,ri.jsx)(zh,{style:i,forceReRender:[i],children:"Error"})})})})},Qh=(0,ri.jsx)(Ci.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,ri.jsx)(Ci.Path,{d:"M12 4.75a7.25 7.25 0 100 14.5 7.25 7.25 0 000-14.5zM3.25 12a8.75 8.75 0 1117.5 0 8.75 8.75 0 01-17.5 0zM12 8.75a1.5 1.5 0 01.167 2.99c-.465.052-.917.44-.917 1.01V14h1.5v-.845A3 3 0 109 10.25h1.5a1.5 1.5 0 011.5-1.5zM11.25 15v1.5h1.5V15h-1.5z"})}),ef=({title:e,bigTitle:t=!1,className:n="",description:a="",separatorAndGap:o=!0,children:s})=>(0,ri.jsxs)(bm,{className:n,separatorAndGap:o,children:[(0,ri.jsxs)(hi,{children:[(0,ri.jsx)(vi,{noCaps:!0,big:t,children:e}),(0,ri.jsx)(mi,{children:a})]}),(0,ri.jsx)(ui,{asCard:!1,className:"section-content",children:s})]}),tf=({title:e,name:t,className:n="",description:a="",separatorAndGap:o=!0,options:s,value:r,onChange:i,children:c})=>(n=Z()("ppcp--has-checkboxes",t,n),t||console.error("Checkbox sections need a unique name! No name given to:",e),(0,ri.jsxs)(ef,{title:e,className:n,description:a,separatorAndGap:o,children:[(0,ri.jsx)(Km,{spacing:6,children:(0,ri.jsx)(Yc,{name:t,options:s,value:r,onChange:i})}),c]})),nf=({title:e,className:t="",description:n="",separatorAndGap:a=!0,options:o,selected:s,onChange:r,children:i})=>(t=Z()("ppcp--has-radio-buttons",t),(0,ri.jsxs)(ef,{title:e,className:t,description:n,separatorAndGap:a,children:[(0,ri.jsx)(Xm,{children:(0,ri.jsx)(si.RadioControl,{options:o,selected:s,onChange:r})}),i]})),af=({title:e,className:t="",description:n="",separatorAndGap:a=!0,options:o,value:s,onChange:r,children:i})=>(t=Z()("ppcp--has-select",t),(0,ri.jsxs)(ef,{title:e,className:t,description:n,separatorAndGap:a,children:[(0,ri.jsx)(si.SelectControl,{__nextHasNoMarginBottom:!0,options:o,value:s,onChange:r}),i]})),of=({location:e,setLocation:t})=>{const{choices:n,details:a,isActive:o,setActive:s}=Gr.useLocationProps(e),r={value:"active",label:(0,tt.__)("Enable payment methods in this location","woocommerce-paypal-payments")};return(0,ri.jsxs)(ri.Fragment,{children:[(0,ri.jsx)(ef,{className:"header-section",bigTitle:!0,title:(0,tt.__)("Button Styling","wooocommerce-paypal-payments"),description:(0,tt.__)("Customize the appearance of the PayPal smart buttons on your website and choose which payment buttons to display.","woocommerce-paypal-payments")}),(0,ri.jsx)(af,{className:"location-selector",title:(0,tt.__)("Location","woocommerce-paypal-payments"),separatorAndGap:!1,options:n,value:e,onChange:t,children:a.link&&(0,ri.jsx)(si.Button,{icon:Qh,href:a.link,target:"_blank"})}),(0,ri.jsx)(tf,{name:"location-activation",separatorAndGap:!1,options:[r],value:o,onChange:s})]})},sf=({location:e})=>{const{color:t,setColor:n,choices:a}=Gr.useColorProps(e);return(0,ri.jsx)(af,{title:(0,tt.__)("Button Color","woocommerce-paypal-payments"),className:"button-color",options:a,value:t,onChange:n})},rf=({location:e})=>{const{label:t,setLabel:n,choices:a}=Gr.useLabelProps(e);return(0,ri.jsx)(af,{title:(0,tt.__)("Button Label","woocommerce-paypal-payments"),className:"button-label",options:a,value:t,onChange:n})},cf=({location:e})=>{const{isAvailable:t,layout:n,setLayout:a,choices:o}=Gr.useLayoutProps(e);return t?(0,ri.jsxs)(ri.Fragment,{children:[(0,ri.jsx)(nf,{className:"button-layout",title:(0,tt.__)("Button Layout","woocommerce-paypal-payments"),options:o,selected:n,onChange:a}),(0,ri.jsx)(uf,{location:e})]}):null},lf=({location:e})=>{const{shape:t,setShape:n,choices:a}=Gr.useShapeProps(e);return(0,ri.jsx)(nf,{title:(0,tt.__)("Shape","woocommerce-paypal-payments"),className:"button-shape",options:a,selected:t,onChange:n})},pf=({location:e})=>{const{paymentMethods:t,setPaymentMethods:n,choices:a}=Gr.usePaymentMethodProps(e),{all:o}=Hr.usePaymentMethods(),s=(0,X.useMemo)((()=>a.filter((e=>{const t=o.find((t=>t.id===e.value));return t?.enabled}))),[a,o]);return(0,ri.jsx)(tf,{name:"payment-methods",title:(0,tt.__)("Payment Methods","woocommerce-paypal-payments"),options:s,value:t,onChange:n})},uf=({location:e})=>{const{isAvailable:t,tagline:n,setTagline:a}=Gr.useTaglineProps(e);if(!t)return null;const o={value:"active",label:(0,tt.__)("Show tagline below buttons","woocommerce-paypal-payments")};return(0,ri.jsx)(tf,{name:"tagline",separatorAndGap:!1,options:[o],value:n,onChange:a})},df=({location:e,setLocation:t})=>{const{isActive:n}=Gr.useLocationProps(e),a=()=>n?(0,ri.jsxs)(ri.Fragment,{children:[(0,ri.jsx)(pf,{location:e}),(0,ri.jsx)(cf,{location:e}),(0,ri.jsx)(lf,{location:e}),(0,ri.jsx)(rf,{location:e}),(0,ri.jsx)(sf,{location:e})]}):null;return(0,ri.jsxs)("div",{className:"settings-panel",children:[(0,ri.jsx)(of,{location:e,setLocation:t}),(0,ri.jsx)(a,{})]})},mf=()=>{const{isReady:e}=Gr.useStore(),{location:t,setLocation:n}=Gr.useStylingLocation();return e?(0,ri.jsxs)("div",{className:"ppcp-r-styling",children:[(0,ri.jsx)(df,{location:t,setLocation:n}),(0,ri.jsx)(Jh,{location:t})]}):(0,ri.jsx)(ii,{asModal:!0})},hf=()=>{const{config:e,setCart:t,setCheckout:n,setProduct:a,setShop:o,setHome:s,setCustom_placement:r}=qr.usePayLaterMessaging(),i=window.ppcpSettings?.PcpPayLaterConfigurator;return(0,X.useEffect)((()=>{window.merchantConfigurators&&i&&window.merchantConfigurators.Messaging({config:e,merchantClientId:i.merchantClientId,partnerClientId:i.partnerClientId,partnerName:"WooCommerce",bnCode:i.bnCode,placements:["cart","checkout","product","shop","home","custom_placement"],styleOverrides:{button:"ppcp-r-paylater-configurator__publish-button",header:"ppcp-r-paylater-configurator__header",subheader:"ppcp-r-paylater-configurator__subheader"},onSave:e=>{t(e.config.cart),n(e.config.checkout),a(e.config.product),o(e.config.shop),s(e.config.home),r(e.config.custom_placement)}})}),[i,e]),(0,ri.jsx)("div",{id:"messaging-configurator",className:"ppcp-r-paylater-configurator"})},ff=[{name:"overview",title:(0,tt.__)("Overview","woocommerce-paypal-payments"),Component:(0,ri.jsx)(Rm,{})},{name:"payment-methods",title:(0,tt.__)("Payment Methods","woocommerce-paypal-payments"),Component:(0,ri.jsx)(Gm,{})},{name:"settings",title:(0,tt.__)("Settings","woocommerce-paypal-payments"),Component:(0,ri.jsx)(Ch,{})},{name:"styling",title:(0,tt.__)("Styling","woocommerce-paypal-payments"),Component:(0,ri.jsx)(mf,{})},{name:"pay-later-messaging",title:(0,tt.__)("Pay Later Messaging","woocommerce-paypal-payments"),Component:(0,ri.jsx)(hf,{}),showIf:()=>!!window.ppcpSettings?.isPayLaterConfiguratorAvailable}],yf=({activePanel:e,setActivePanel:t})=>{const n=ff.filter((e=>!e.showIf||e.showIf())),{Component:a}=n.find((t=>t.name===e));return(0,ri.jsxs)(ri.Fragment,{children:[(0,ri.jsx)(zi,{tabs:n,activePanel:e,setActivePanel:t}),(0,ri.jsx)(ci,{page:"settings",children:a})]})},gf=()=>{const{isReady:e,completed:t}=Ur.useSteps(),{isReady:n}=zr.useStore(),{merchant:{isSendOnlyCountry:a}}=zr.useMerchantInfo();(0,X.useEffect)((()=>{!function(){const e={};Object.values(ct.funnels).forEach((t=>{if(!t.isInitialized){const n=function(e){const t=ct.funnels[e];if(!t)return console.error(`[REGISTRY] Funnel ${e} not found`),null;const{config:n,stores:a}=t;if(0===a.length)return console.warn(`[REGISTRY] No stores registered for funnel ${e}`),null;const o=new st(n,{debugMode:n.debug});n.adapters.includes("woocommerce-tracks")&&o.addAdapter(new at(n.eventPrefix,{debugMode:n.debug})),(n.adapters.includes("console")||n.debug)&&o.addAdapter(new ot({enabled:!0,logLevel:n.debug?"debug":"info",prefix:`[${e}]`,colorize:!0,showTimestamp:!0}));const s=[];a.forEach((t=>{if(!wp.data||!wp.data.select(t))return void console.warn(`[REGISTRY] Store ${t} not available for funnel ${e}`);const a=n.fieldConfigs[t]||[],r={};a.forEach((e=>{e.rules&&(r[e.fieldName]=e.rules)}));const i=it.registerFunnelForStore(t,e,o,r,a,n.debug,n.trackingCondition,n.stepInfo);s.push({storeName:t,registration:i})}));const r={funnelId:e,trackingService:o,stores:a,config:n,trackingCondition:n.trackingCondition,registrations:s,unsubscribe:()=>{s.forEach((({storeName:t})=>{it.unregisterFunnelForStore(t,e)})),delete ct.instances[e]},getConditionStatus:()=>{const e={};return s.forEach((({storeName:t,registration:n})=>{e[t]={isActive:n.isActive,conditionMet:n.lastConditionResult,conditionChecks:n.conditionCheckCount,initAttempts:n.initializationAttempts}})),e},testCondition:()=>{const e={};return s.forEach((({storeName:t,registration:n})=>{const a=it.evaluateTrackingCondition(wp.data.select,n.trackingCondition,n);e[t]={conditionMet:a,registration:{funnelId:n.funnelId,isActive:n.isActive,lastResult:n.lastConditionResult}}})),e},getDetailedStatus:()=>({funnelId:e,stores:a,trackingCondition:n.trackingCondition,storeStatuses:r.getConditionStatus(),subscriptionManagerStatus:it.getStatus(),adapterCount:o.adapters.length,eventCount:o.eventCount})};return ct.instances[e]=r,r}(t.funnelId);n&&(e[t.funnelId]=n,ct.funnels[t.funnelId].isInitialized=!0)}}))}()}),[]),(0,X.useEffect)((()=>{const e=e=>{e.stopImmediatePropagation()};return window.addEventListener("beforeunload",e),()=>{window.removeEventListener("beforeunload",e)}}),[]);const o=Z()("ppcp-r-app",{loading:!e}),[s,r]=(0,X.useState)(Bi().panel),i=()=>{(e=>{const t=Bi(),n=(a=t,o=e,Object.keys(a).reduce(((e,t)=>(o.includes(t)&&(e[t]=a[t]),e)),{}));var a,o;return Object.keys(n).length!==Object.keys(t).length&&(Vi(n,!0),!0)})(["page","tab","section"])&&r("")},c=(0,X.useMemo)((()=>e&&n?a?(i(),(0,ri.jsx)(Wi,{})):t?(0,ri.jsx)(yf,{activePanel:s||"overview",setActivePanel:r}):(i(),(0,ri.jsx)(gm,{})):(0,ri.jsx)(ii,{asModal:!0})),[a,n,t,e,s]);return(0,ri.jsx)("div",{className:o,children:c})};(0,q.H)(document.getElementById("ppcp-settings-container")).render((0,ri.jsx)(gf,{}))})()})();