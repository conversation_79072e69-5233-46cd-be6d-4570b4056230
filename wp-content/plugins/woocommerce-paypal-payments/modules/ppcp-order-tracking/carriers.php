<?php

/**
 * The order tracking carriers.
 *
 * @package WooCommerce\PayPalCommerce\OrderTracking
 */
declare (strict_types=1);
namespace WooCommerce\PayPalCommerce\OrderTracking;

if (!function_exists('WooCommerce\PayPalCommerce\OrderTracking\tr')) {
    /**
     * Prevent strings of being added to the .pot file but still be translatable via "gettext_with_context" filter if necessary.
     *
     * @param string $text    Text to translate.
     * @param string $context Context information for the translators.
     * @param string $domain  Text domain. Unique identifier for retrieving translated strings.
     * @return string Translated context string without pipe.
     */
    function tr(string $text, string $context, string $domain): string
    {
        // We want to call "_x" with variable arguments so the text isn't added to generated .pot translation files.
        return _x(
            // phpcs:ignore WordPress.WP.I18n.NonSingularStringLiteralText
            $text,
            // phpcs:ignore WordPress.WP.I18n.NonSingularStringLiteralContext
            $context,
            // phpcs:ignore WordPress.WP.I18n.NonSingularStringLiteralDomain
            $domain
        );
    }
}
return (array) apply_filters('woocommerce_paypal_payments_tracking_carriers', array('global' => array('name' => 'Global', 'items' => array('99MINUTOS' => tr('99minutos', 'Name of carrier', 'woocommerce-paypal-payments'), 'A2B_BA' => tr('A2B Express Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'ABCUSTOM_SFTP' => tr('AB Custom Group', 'Name of carrier', 'woocommerce-paypal-payments'), 'ACILOGISTIX' => tr('ACI Logistix', 'Name of carrier', 'woocommerce-paypal-payments'), 'ACOMMERCE' => tr('ACOMMERCE', 'Name of carrier', 'woocommerce-paypal-payments'), 'ACTIVOS24_API' => tr('Activos24', 'Name of carrier', 'woocommerce-paypal-payments'), 'ADS' => tr('ADS Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'AEROFLASH' => tr('AEROFLASH', 'Name of carrier', 'woocommerce-paypal-payments'), 'AGEDISS_SFTP' => tr('Agediss', 'Name of carrier', 'woocommerce-paypal-payments'), 'AIR_21' => tr('AIR 21', 'Name of carrier', 'woocommerce-paypal-payments'), 'AIRSPEED' => tr('AIRSPEED', 'Name of carrier', 'woocommerce-paypal-payments'), 'AIRTERRA' => tr('Airterra', 'Name of carrier', 'woocommerce-paypal-payments'), 'AITWORLDWIDE_API' => tr('AIT', 'Name of carrier', 'woocommerce-paypal-payments'), 'AITWORLDWIDE_SFTP' => tr('AIT', 'Name of carrier', 'woocommerce-paypal-payments'), 'ALLIED_EXPRESS_FTP' => tr('Allied Express (FTP)', 'Name of carrier', 'woocommerce-paypal-payments'), 'ALLJOY' => tr('ALLJOY SUPPLY CHAIN', 'Name of carrier', 'woocommerce-paypal-payments'), 'AMAZON_EMAIL_PUSH' => tr('Amazon', 'Name of carrier', 'woocommerce-paypal-payments'), 'AMAZON_ORDER' => tr('Amazon order', 'Name of carrier', 'woocommerce-paypal-payments'), 'AMAZON_UK_API' => tr('amazon_uk_api', 'Name of carrier', 'woocommerce-paypal-payments'), 'AMS_GRP' => tr('AMS Group', 'Name of carrier', 'woocommerce-paypal-payments'), 'ANDREANI_API' => tr('Andreani', 'Name of carrier', 'woocommerce-paypal-payments'), 'ANTERAJA' => tr('Anteraja', 'Name of carrier', 'woocommerce-paypal-payments'), 'ARAMEX' => tr('Aramex', 'Name of carrier', 'woocommerce-paypal-payments'), 'ARAMEX_API' => tr('Aramex', 'Name of carrier', 'woocommerce-paypal-payments'), 'ARASKARGO' => tr('Aras Cargo', 'Name of carrier', 'woocommerce-paypal-payments'), 'ARGENTS_WEBHOOK' => tr('Argents Express Group', 'Name of carrier', 'woocommerce-paypal-payments'), 'ASENDIA_DE' => tr('asendia_de', 'Name of carrier', 'woocommerce-paypal-payments'), 'ATSHEALTHCARE_REFERENCE' => tr('ATS Healthcare', 'Name of carrier', 'woocommerce-paypal-payments'), 'ATSHEALTHCARE' => tr('ATS Healthcare', 'Name of carrier', 'woocommerce-paypal-payments'), 'AUEXPRESS' => tr('Au Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'AUSTRALIA_POST_API' => tr('Australia Post', 'Name of carrier', 'woocommerce-paypal-payments'), 'AVERITT' => tr('Averitt Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'AXLEHIRE_FTP' => tr('Axlehire', 'Name of carrier', 'woocommerce-paypal-payments'), 'AXLEHIRE' => tr('AxleHire', 'Name of carrier', 'woocommerce-paypal-payments'), 'BARQEXP' => tr('Barq', 'Name of carrier', 'woocommerce-paypal-payments'), 'BDMNET' => tr('BDMnet', 'Name of carrier', 'woocommerce-paypal-payments'), 'BEL_BELGIUM_POST' => tr('bel_belgium_post', 'Name of carrier', 'woocommerce-paypal-payments'), 'BLR_BELPOST' => tr('Belpost', 'Name of carrier', 'woocommerce-paypal-payments'), 'BERT' => tr('BERT', 'Name of carrier', 'woocommerce-paypal-payments'), 'BESTTRANSPORT_SFTP' => tr('Best Transport', 'Name of carrier', 'woocommerce-paypal-payments'), 'BETTERTRUCKS' => tr('Better Trucks', 'Name of carrier', 'woocommerce-paypal-payments'), 'BIGSMART' => tr('Big Smart', 'Name of carrier', 'woocommerce-paypal-payments'), 'BIOCAIR_FTP' => tr('BioCair', 'Name of carrier', 'woocommerce-paypal-payments'), 'BJSHOMEDELIVERY' => tr('BJS Distribution courier', 'Name of carrier', 'woocommerce-paypal-payments'), 'BJSHOMEDELIVERY_FTP' => tr('BJS Distribution, Storage & Couriers - FTP', 'Name of carrier', 'woocommerce-paypal-payments'), 'BLUEDART' => tr('BLUEDART', 'Name of carrier', 'woocommerce-paypal-payments'), 'BLUEDART_API' => tr('Bluedart', 'Name of carrier', 'woocommerce-paypal-payments'), 'BOLLORE_LOGISTICS' => tr('Bollore Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'BOMI' => tr('Bomi Group', 'Name of carrier', 'woocommerce-paypal-payments'), 'BE_BPOST' => tr('Bpost (www.bpost.be)', 'Name of carrier', 'woocommerce-paypal-payments'), 'BPOST_API' => tr('Bpost API', 'Name of carrier', 'woocommerce-paypal-payments'), 'BPOST_INT' => tr('Bpost international', 'Name of carrier', 'woocommerce-paypal-payments'), 'BRT_IT_API' => tr('BRT Bartolini API', 'Name of carrier', 'woocommerce-paypal-payments'), 'BUFFALO' => tr('BUFFALO', 'Name of carrier', 'woocommerce-paypal-payments'), 'BURD' => tr('Burd Delivery', 'Name of carrier', 'woocommerce-paypal-payments'), 'CHROBINSON' => tr('C.H. Robinson Worldwide', 'Name of carrier', 'woocommerce-paypal-payments'), 'CAGO' => tr('Cago', 'Name of carrier', 'woocommerce-paypal-payments'), 'CANPAR' => tr('CANPAR', 'Name of carrier', 'woocommerce-paypal-payments'), 'CAPITAL' => tr('Capital Transport', 'Name of carrier', 'woocommerce-paypal-payments'), 'CARRY_FLAP' => tr('Carry-Flap Co.', 'Name of carrier', 'woocommerce-paypal-payments'), 'CBL_LOGISTICA_API' => tr('CBL Logistica (API)', 'Name of carrier', 'woocommerce-paypal-payments'), 'CDLDELIVERS' => tr('CDL Last Mile', 'Name of carrier', 'woocommerce-paypal-payments'), 'CELERITAS' => tr('Celeritas Transporte', 'Name of carrier', 'woocommerce-paypal-payments'), 'CEVA' => tr('CEVA LOGISTICS', 'Name of carrier', 'woocommerce-paypal-payments'), 'CEVA_TRACKING' => tr('CEVA Package', 'Name of carrier', 'woocommerce-paypal-payments'), 'CHAZKI' => tr('Chazki', 'Name of carrier', 'woocommerce-paypal-payments'), 'CHIENVENTURE_WEBHOOK' => tr('Chienventure', 'Name of carrier', 'woocommerce-paypal-payments'), 'CHILEXPRESS' => tr('Chile Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'CITY56_WEBHOOK' => tr('City Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'CJ_GLS' => tr('CJ GLS', 'Name of carrier', 'woocommerce-paypal-payments'), 'CJ_LOGISTICS' => tr('CJ Logistics International', 'Name of carrier', 'woocommerce-paypal-payments'), 'CJ_PHILIPPINES' => tr('cj_philippines', 'Name of carrier', 'woocommerce-paypal-payments'), 'CLICKLINK_SFTP' => tr('ClickLink', 'Name of carrier', 'woocommerce-paypal-payments'), 'CN_LOGISTICS' => tr('CN Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'COLLECTPLUS' => tr('COLLECTPLUS', 'Name of carrier', 'woocommerce-paypal-payments'), 'COM1EXPRESS' => tr('ComOne Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'CONCISE' => tr('Concise', 'Name of carrier', 'woocommerce-paypal-payments'), 'CONCISE_WEBHOOK' => tr('Concise', 'Name of carrier', 'woocommerce-paypal-payments'), 'CONCISE_API' => tr('Concise', 'Name of carrier', 'woocommerce-paypal-payments'), 'COORDINADORA_API' => tr('Coordinadora', 'Name of carrier', 'woocommerce-paypal-payments'), 'COPA_COURIER' => tr('Copa Airlines Courier', 'Name of carrier', 'woocommerce-paypal-payments'), 'CORREOS_DE_ESPANA' => tr('CORREOS DE ESPANA', 'Name of carrier', 'woocommerce-paypal-payments'), 'CORREOSEXPRESS_API' => tr('Correos Express (API)', 'Name of carrier', 'woocommerce-paypal-payments'), 'CORREOS_ES' => tr('correos Express (www.correos.es)', 'Name of carrier', 'woocommerce-paypal-payments'), 'COURANT_PLUS_API' => tr('Courant Plus', 'Name of carrier', 'woocommerce-paypal-payments'), 'COURIER_POST' => tr('COURIER POST', 'Name of carrier', 'woocommerce-paypal-payments'), 'COURIERPLUS' => tr('COURIERPLUS', 'Name of carrier', 'woocommerce-paypal-payments'), 'CRLEXPRESS' => tr('CRL Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'CROSSFLIGHT' => tr('Crossflight Limited', 'Name of carrier', 'woocommerce-paypal-payments'), 'CRYOPDP_FTP' => tr('CryoPDP', 'Name of carrier', 'woocommerce-paypal-payments'), 'CESKAPOSTA_API' => tr('Czech Post', 'Name of carrier', 'woocommerce-paypal-payments'), 'DEXPRESS_WEBHOOK' => tr('D Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'DACHSER' => tr('DACHSER', 'Name of carrier', 'woocommerce-paypal-payments'), 'DACHSER_WEB' => tr('DACHSER', 'Name of carrier', 'woocommerce-paypal-payments'), 'DAESHIN' => tr('Daeshin', 'Name of carrier', 'woocommerce-paypal-payments'), 'DAIICHI' => tr('Daiichi Freight System Inc', 'Name of carrier', 'woocommerce-paypal-payments'), 'DANNIAO' => tr('Danniao', 'Name of carrier', 'woocommerce-paypal-payments'), 'DAO365' => tr('DAO365', 'Name of carrier', 'woocommerce-paypal-payments'), 'DAYROSS' => tr('Day & Ross', 'Name of carrier', 'woocommerce-paypal-payments'), 'DYLT' => tr('Daylight Transport', 'Name of carrier', 'woocommerce-paypal-payments'), 'DBSCHENKER_API' => tr('DB Schenker', 'Name of carrier', 'woocommerce-paypal-payments'), 'DBSCHENKER_B2B' => tr('DB Schenker B2B', 'Name of carrier', 'woocommerce-paypal-payments'), 'DBSCHENKER_ICELAND' => tr('DB Schenker Iceland', 'Name of carrier', 'woocommerce-paypal-payments'), 'DDEXPRESS' => tr('DD Express Courier', 'Name of carrier', 'woocommerce-paypal-payments'), 'DE_DHL' => tr('DE DHL', 'Name of carrier', 'woocommerce-paypal-payments'), 'DELCART_IN' => tr('delcart_in', 'Name of carrier', 'woocommerce-paypal-payments'), 'DELIVERYOURPARCEL_ZA' => tr('Deliver Your Parcel', 'Name of carrier', 'woocommerce-paypal-payments'), 'DELIVER_IT' => tr('Deliver-iT', 'Name of carrier', 'woocommerce-paypal-payments'), 'DELIVERE' => tr('delivere', 'Name of carrier', 'woocommerce-paypal-payments'), 'DELIVERR_SFTP' => tr('Deliverr', 'Name of carrier', 'woocommerce-paypal-payments'), 'DELTEC_DE' => tr('DELTEC DE', 'Name of carrier', 'woocommerce-paypal-payments'), 'DEMANDSHIP' => tr('DemandShip', 'Name of carrier', 'woocommerce-paypal-payments'), 'DEUTSCHE_DE' => tr('deutsche_de', 'Name of carrier', 'woocommerce-paypal-payments'), 'DHL_API' => tr('DHL', 'Name of carrier', 'woocommerce-paypal-payments'), 'DHL_REFERENCE_API' => tr('DHL (Reference number)', 'Name of carrier', 'woocommerce-paypal-payments'), 'DHL_ACTIVE_TRACING' => tr('DHL Active Tracing', 'Name of carrier', 'woocommerce-paypal-payments'), 'DHL_ECOMMERCE_GC' => tr('DHL eCommerce Greater China', 'Name of carrier', 'woocommerce-paypal-payments'), 'DHL_GLOBAL_MAIL_API' => tr('DHL eCommerce Solutions', 'Name of carrier', 'woocommerce-paypal-payments'), 'DE_DHL_EXPRESS' => tr('DHL Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'DHL_SFTP' => tr('DHL Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'DHL_FR' => tr('DHL France (www.dhl.com)', 'Name of carrier', 'woocommerce-paypal-payments'), 'DHL_FREIGHT' => tr('DHL Freight', 'Name of carrier', 'woocommerce-paypal-payments'), 'DHL' => tr('dhl Global', 'Name of carrier', 'woocommerce-paypal-payments'), 'DHL_GLOBAL_FORWARDING_API' => tr('DHL Global Forwarding API', 'Name of carrier', 'woocommerce-paypal-payments'), 'DHL_GT_API' => tr('DHL Global Forwarding Guatemala', 'Name of carrier', 'woocommerce-paypal-payments'), 'DHL_PA_API' => tr('DHL GLOBAL FORWARDING PANAMÁ', 'Name of carrier', 'woocommerce-paypal-payments'), 'IT_DHL_ECOMMERCE' => tr('DHL International', 'Name of carrier', 'woocommerce-paypal-payments'), 'DHL_JP' => tr('DHL Japan', 'Name of carrier', 'woocommerce-paypal-payments'), 'DHL_PARCEL_NL' => tr('DHL Parcel NL', 'Name of carrier', 'woocommerce-paypal-payments'), 'DHL_SG' => tr('dhl Singapore', 'Name of carrier', 'woocommerce-paypal-payments'), 'DHL_ES_SFTP' => tr('DHL Spain Domestic', 'Name of carrier', 'woocommerce-paypal-payments'), 'DHL_SUPPLYCHAIN_IN' => tr('DHL supply chain India', 'Name of carrier', 'woocommerce-paypal-payments'), 'DHL_SUPPLYCHAIN_ID' => tr('DHL Supply Chain Indonesia', 'Name of carrier', 'woocommerce-paypal-payments'), 'DHL_AT' => tr('dhl_at', 'Name of carrier', 'woocommerce-paypal-payments'), 'DHL_GLOBAL_MAIL' => tr('dhl_global_mail', 'Name of carrier', 'woocommerce-paypal-payments'), 'DHL_IT' => tr('dhl_it', 'Name of carrier', 'woocommerce-paypal-payments'), 'DHL_PIECEID' => tr('dhl_pieceid', 'Name of carrier', 'woocommerce-paypal-payments'), 'DHL_SUPPLY_CHAIN_AU' => tr('dhl_supply_chain_au', 'Name of carrier', 'woocommerce-paypal-payments'), 'DHLPARCEL_UK' => tr('dhlparcel_uk', 'Name of carrier', 'woocommerce-paypal-payments'), 'DIALOGO_LOGISTICA_API' => tr('Dialogo Logistica', 'Name of carrier', 'woocommerce-paypal-payments'), 'DIALOGO_LOGISTICA' => tr('Dialogo Logistica', 'Name of carrier', 'woocommerce-paypal-payments'), 'DIRECTFREIGHT_AU_REF' => tr('Direct Freight Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'DIREX' => tr('Direx', 'Name of carrier', 'woocommerce-paypal-payments'), 'DKSH' => tr('DKSH', 'Name of carrier', 'woocommerce-paypal-payments'), 'DMFGROUP' => tr('DMF', 'Name of carrier', 'woocommerce-paypal-payments'), 'DNJ_EXPRESS' => tr('DNJ Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'DOTZOT' => tr('DOTZOT', 'Name of carrier', 'woocommerce-paypal-payments'), 'DPD' => tr('DPD', 'Name of carrier', 'woocommerce-paypal-payments'), 'DPD_AT_SFTP' => tr('DPD Austria', 'Name of carrier', 'woocommerce-paypal-payments'), 'DPD_DELISTRACK' => tr('DPD delistrack', 'Name of carrier', 'woocommerce-paypal-payments'), 'DPD_NL' => tr('DPD Netherlands', 'Name of carrier', 'woocommerce-paypal-payments'), 'DPD_RU_API' => tr('DPD Russia', 'Name of carrier', 'woocommerce-paypal-payments'), 'DPD_SK_SFTP' => tr('DPD Slovakia', 'Name of carrier', 'woocommerce-paypal-payments'), 'DPD_CH_SFTP' => tr('DPD Switzerland', 'Name of carrier', 'woocommerce-paypal-payments'), 'DPD_UK_SFTP' => tr('DPD UK', 'Name of carrier', 'woocommerce-paypal-payments'), 'DPD_DE' => tr('dpd_de', 'Name of carrier', 'woocommerce-paypal-payments'), 'DPD_FR_REFERENCE' => tr('dpd_fr_reference', 'Name of carrier', 'woocommerce-paypal-payments'), 'DPD_UK' => tr('dpd_uk', 'Name of carrier', 'woocommerce-paypal-payments'), 'CN_DPEX' => tr('DPEX', 'Name of carrier', 'woocommerce-paypal-payments'), 'DPEX' => tr('DPEX (www.dpex.com)', 'Name of carrier', 'woocommerce-paypal-payments'), 'DSV' => tr('DSV courier', 'Name of carrier', 'woocommerce-paypal-payments'), 'DSV_REFERENCE' => tr('DSV Futurewave', 'Name of carrier', 'woocommerce-paypal-payments'), 'DX' => tr('DX', 'Name of carrier', 'woocommerce-paypal-payments'), 'DX_B2B_CONNUM' => tr('DX (B2B)', 'Name of carrier', 'woocommerce-paypal-payments'), 'DX_FREIGHT' => tr('DX Freight', 'Name of carrier', 'woocommerce-paypal-payments'), 'DYNALOGIC' => tr('Dynamic Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'EASTWESTCOURIER_FTP' => tr('East West Courier Pte Ltd', 'Name of carrier', 'woocommerce-paypal-payments'), 'EC_CN' => tr('EC_CN', 'Name of carrier', 'woocommerce-paypal-payments'), 'ECARGO' => tr('ECARGO', 'Name of carrier', 'woocommerce-paypal-payments'), 'ECEXPRESS' => tr('ECexpress', 'Name of carrier', 'woocommerce-paypal-payments'), 'ECMS' => tr('ECMS International Logistics Co.', 'Name of carrier', 'woocommerce-paypal-payments'), 'ECOFREIGHT' => tr('Eco Freight', 'Name of carrier', 'woocommerce-paypal-payments'), 'ECOURIER' => tr('ecourier', 'Name of carrier', 'woocommerce-paypal-payments'), 'ECOUTIER' => tr('eCoutier', 'Name of carrier', 'woocommerce-paypal-payments'), 'EFS' => tr('EFS (E-commerce Fulfillment Service)', 'Name of carrier', 'woocommerce-paypal-payments'), 'ELITE_CO' => tr('Elite Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'ELOGISTICA' => tr('elogistica', 'Name of carrier', 'woocommerce-paypal-payments'), 'ELTA_GR' => tr('elta_gr', 'Name of carrier', 'woocommerce-paypal-payments'), 'ARE_EMIRATES_POST' => tr('Emirates Post', 'Name of carrier', 'woocommerce-paypal-payments'), 'EMS' => tr('EMS', 'Name of carrier', 'woocommerce-paypal-payments'), 'EMS_CN' => tr('ems_cn', 'Name of carrier', 'woocommerce-paypal-payments'), 'ENSENDA' => tr('ENSENDA', 'Name of carrier', 'woocommerce-paypal-payments'), 'EFWNOW_API' => tr('Estes Forwarding Worldwide', 'Name of carrier', 'woocommerce-paypal-payments'), 'ETOMARS' => tr('Etomars', 'Name of carrier', 'woocommerce-paypal-payments'), 'ETOTAL' => tr('eTotal Solution Limited', 'Name of carrier', 'woocommerce-paypal-payments'), 'EDF_FTP' => tr('Eurodifarm', 'Name of carrier', 'woocommerce-paypal-payments'), 'EURODIS' => tr('eurodis', 'Name of carrier', 'woocommerce-paypal-payments'), 'EUROPAKET_API' => tr('Europacket+', 'Name of carrier', 'woocommerce-paypal-payments'), 'MYHERMES_UK_API' => tr('EVRi', 'Name of carrier', 'woocommerce-paypal-payments'), 'EWE' => tr('EWE Global Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'EXELOT_FTP' => tr('Exelot Ltd.', 'Name of carrier', 'woocommerce-paypal-payments'), 'EXPEDITORS' => tr('Expeditors', 'Name of carrier', 'woocommerce-paypal-payments'), 'EXPEDITORS_API_REF' => tr('Expeditors API Reference', 'Name of carrier', 'woocommerce-paypal-payments'), 'EZSHIP' => tr('EZship', 'Name of carrier', 'woocommerce-paypal-payments'), 'FAIRSENDEN_API' => tr('fairsenden', 'Name of carrier', 'woocommerce-paypal-payments'), 'FXTRAN' => tr('Falcon Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'FAN' => tr('FAN COURIER EXPRESS', 'Name of carrier', 'woocommerce-paypal-payments'), 'FNF_ZA' => tr('Fast & Furious', 'Name of carrier', 'woocommerce-paypal-payments'), 'FASTDESPATCH' => tr('Fast Despatch Logistics Limited', 'Name of carrier', 'woocommerce-paypal-payments'), 'FASTBOX' => tr('Fastbox', 'Name of carrier', 'woocommerce-paypal-payments'), 'FASTSHIP' => tr('Fastship Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'FASTTRACK' => tr('fasttrack', 'Name of carrier', 'woocommerce-paypal-payments'), 'FASTWAY_AU' => tr('fastway_au', 'Name of carrier', 'woocommerce-paypal-payments'), 'FASTWAY_UK' => tr('FASTWAY_UK', 'Name of carrier', 'woocommerce-paypal-payments'), 'FASTWAY_US' => tr('FASTWAY_US', 'Name of carrier', 'woocommerce-paypal-payments'), 'FASTWAY_ZA' => tr('fastway_za', 'Name of carrier', 'woocommerce-paypal-payments'), 'FAXECARGO' => tr('Faxe Cargo', 'Name of carrier', 'woocommerce-paypal-payments'), 'FEDEX_FR' => tr('FedEx® Freight', 'Name of carrier', 'woocommerce-paypal-payments'), 'FEDEX_API' => tr('FedEx®', 'Name of carrier', 'woocommerce-paypal-payments'), 'FERCAM_IT' => tr('fercam_it', 'Name of carrier', 'woocommerce-paypal-payments'), 'FETCHR' => tr('Fetchr', 'Name of carrier', 'woocommerce-paypal-payments'), 'FIRST_LOGISTICS_API' => tr('First Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'FIRST_LOGISITCS' => tr('first_logisitcs', 'Name of carrier', 'woocommerce-paypal-payments'), 'FITZMARK_API' => tr('FitzMark', 'Name of carrier', 'woocommerce-paypal-payments'), 'FLASHEXPRESS_WEBHOOK' => tr('Flash Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'FLIGHTLG' => tr('Flight Logistics Group', 'Name of carrier', 'woocommerce-paypal-payments'), 'FLIPXP' => tr('FlipXpress', 'Name of carrier', 'woocommerce-paypal-payments'), 'FLYTEXPRESS' => tr('FLYTEXPRESS', 'Name of carrier', 'woocommerce-paypal-payments'), 'FORWARDAIR' => tr('Forward Air', 'Name of carrier', 'woocommerce-paypal-payments'), 'FOUR_PX_EXPRESS' => tr('FOUR PX EXPRESS', 'Name of carrier', 'woocommerce-paypal-payments'), 'FR_COLISSIMO' => tr('fr_colissimo', 'Name of carrier', 'woocommerce-paypal-payments'), 'FR_MONDIAL' => tr('fr_mondial', 'Name of carrier', 'woocommerce-paypal-payments'), 'FRAGILEPAK_SFTP' => tr('FragilePAK', 'Name of carrier', 'woocommerce-paypal-payments'), 'FRONTDOORCORP' => tr('FRONTdoor Collective', 'Name of carrier', 'woocommerce-paypal-payments'), 'FUJEXP' => tr('FUJIE EXPRESS', 'Name of carrier', 'woocommerce-paypal-payments'), 'GWLOGIS_API' => tr('G.I.G', 'Name of carrier', 'woocommerce-paypal-payments'), 'GAC' => tr('GAC', 'Name of carrier', 'woocommerce-paypal-payments'), 'GATI_KWE_API' => tr('Gati-KWE', 'Name of carrier', 'woocommerce-paypal-payments'), 'GDPHARM' => tr('GDPharm Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'GW_WORLD' => tr('Gebrüder Weiss', 'Name of carrier', 'woocommerce-paypal-payments'), 'GEODIS' => tr('GEODIS', 'Name of carrier', 'woocommerce-paypal-payments'), 'GEODIS_API' => tr('GEODIS - Distribution & Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'GPOST' => tr('Georgian Post', 'Name of carrier', 'woocommerce-paypal-payments'), 'GIAO_HANG' => tr('Giao hàng nhanh', 'Name of carrier', 'woocommerce-paypal-payments'), 'GIO_ECOURIER_API' => tr('GIO Express Ecourier', 'Name of carrier', 'woocommerce-paypal-payments'), 'GIO_ECOURIER' => tr('GIO Express Inc', 'Name of carrier', 'woocommerce-paypal-payments'), 'GOGLOBALPOST' => tr('Global Post', 'Name of carrier', 'woocommerce-paypal-payments'), 'GLOBEGISTICS' => tr('GLOBEGISTICS', 'Name of carrier', 'woocommerce-paypal-payments'), 'GLOVO' => tr('Glovo', 'Name of carrier', 'woocommerce-paypal-payments'), 'GLS' => tr('GLS', 'Name of carrier', 'woocommerce-paypal-payments'), 'GLS_SPAIN_API' => tr('GLS Spain', 'Name of carrier', 'woocommerce-paypal-payments'), 'GLS_DE' => tr('GLS_DE', 'Name of carrier', 'woocommerce-paypal-payments'), 'GLS_ES' => tr('GLS_ES', 'Name of carrier', 'woocommerce-paypal-payments'), 'GLS_FR' => tr('GLS_FR', 'Name of carrier', 'woocommerce-paypal-payments'), 'GLS_ITALY_FTP' => tr('gls_italy_ftp', 'Name of carrier', 'woocommerce-paypal-payments'), 'GLS_SPAIN' => tr('gls_spain', 'Name of carrier', 'woocommerce-paypal-payments'), 'GOLS' => tr('GO Logistics & Storage', 'Name of carrier', 'woocommerce-paypal-payments'), 'GOPEOPLE' => tr('Go People', 'Name of carrier', 'woocommerce-paypal-payments'), 'GORUSH' => tr('Go Rush', 'Name of carrier', 'woocommerce-paypal-payments'), 'GOJEK' => tr('Gojek', 'Name of carrier', 'woocommerce-paypal-payments'), 'GREYHOUND' => tr('GREYHOUND', 'Name of carrier', 'woocommerce-paypal-payments'), 'MAZET' => tr('Groupe Mazet', 'Name of carrier', 'woocommerce-paypal-payments'), 'HANJIN' => tr('HanJin', 'Name of carrier', 'woocommerce-paypal-payments'), 'HELLENIC_POST' => tr('Hellenic (Greece) Post', 'Name of carrier', 'woocommerce-paypal-payments'), 'HELLMANN' => tr('Hellmann Worldwide Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'HELTHJEM_API' => tr('Helthjem', 'Name of carrier', 'woocommerce-paypal-payments'), 'HERMES_DE_FTP' => tr('Hermes Germany', 'Name of carrier', 'woocommerce-paypal-payments'), 'HERMES_UK_SFTP' => tr('Hermes UK', 'Name of carrier', 'woocommerce-paypal-payments'), 'HERMESWORLD_UK' => tr('hermesworld_uk', 'Name of carrier', 'woocommerce-paypal-payments'), 'HEROEXPRESS' => tr('Hero Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'HFD' => tr('HFD', 'Name of carrier', 'woocommerce-paypal-payments'), 'HK_RPX' => tr('hk_rpx', 'Name of carrier', 'woocommerce-paypal-payments'), 'HOMELOGISTICS' => tr('Home Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'HOMERUNNER' => tr('HomeRunner', 'Name of carrier', 'woocommerce-paypal-payments'), 'HERMES_IT' => tr('HR Parcel', 'Name of carrier', 'woocommerce-paypal-payments'), 'HSDEXPRESS' => tr('HSDEXPRESS', 'Name of carrier', 'woocommerce-paypal-payments'), 'HSM_GLOBAL' => tr('HSM Global', 'Name of carrier', 'woocommerce-paypal-payments'), 'HUANTONG' => tr('HuanTong', 'Name of carrier', 'woocommerce-paypal-payments'), 'HUBBED' => tr('HUBBED', 'Name of carrier', 'woocommerce-paypal-payments'), 'HUNTER_EXPRESS_SFTP' => tr('Hunter Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'IBVENTURE_WEBHOOK' => tr('IB Venture', 'Name of carrier', 'woocommerce-paypal-payments'), 'POSTUR_IS' => tr('Iceland Post', 'Name of carrier', 'woocommerce-paypal-payments'), 'ICSCOURIER' => tr('ICS COURIER', 'Name of carrier', 'woocommerce-paypal-payments'), 'IDEXPRESS_ID' => tr('iDexpress Indonesia', 'Name of carrier', 'woocommerce-paypal-payments'), 'IDN_POS' => tr('idn_pos', 'Name of carrier', 'woocommerce-paypal-payments'), 'IDS_LOGISTICS' => tr('ids_logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'ILYANGLOGIS' => tr('Ilyang logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'IMEXGLOBALSOLUTIONS' => tr('imexglobalsolutions', 'Name of carrier', 'woocommerce-paypal-payments'), 'IMILE_API' => tr('iMile', 'Name of carrier', 'woocommerce-paypal-payments'), 'IML' => tr('IML courier', 'Name of carrier', 'woocommerce-paypal-payments'), 'IMX' => tr('IMX', 'Name of carrier', 'woocommerce-paypal-payments'), 'INDIA_POST' => tr('India Post Domestic', 'Name of carrier', 'woocommerce-paypal-payments'), 'INDIA_POST_INT' => tr('India Post International', 'Name of carrier', 'woocommerce-paypal-payments'), 'INEXPOST' => tr('Inexpost', 'Name of carrier', 'woocommerce-paypal-payments'), 'INNTRALOG_SFTP' => tr('Inntralog GmbH', 'Name of carrier', 'woocommerce-paypal-payments'), 'INPOST_UK' => tr('InPost', 'Name of carrier', 'woocommerce-paypal-payments'), 'INSTABOX_WEBHOOK' => tr('Instabox', 'Name of carrier', 'woocommerce-paypal-payments'), 'INTERNATIONAL_SEUR_API' => tr('International Seur API', 'Name of carrier', 'woocommerce-paypal-payments'), 'INTERSMARTTRANS' => tr('INTERSMARTTRANS & SOLUTIONS SL', 'Name of carrier', 'woocommerce-paypal-payments'), 'INTEX_DE' => tr('INTEX Paketdienst GmbH', 'Name of carrier', 'woocommerce-paypal-payments'), 'INTIME_FTP' => tr('InTime', 'Name of carrier', 'woocommerce-paypal-payments'), 'ITHINKLOGISTICS' => tr('iThink Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'JTCARGO' => tr('J&T CARGO', 'Name of carrier', 'woocommerce-paypal-payments'), 'JTEXPRESS_PH' => tr('J&T Express Philippines', 'Name of carrier', 'woocommerce-paypal-payments'), 'JTEXPRESS_SG_API' => tr('J&T Express Singapore', 'Name of carrier', 'woocommerce-paypal-payments'), 'JT_LOGISTICS' => tr('J&T International logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'JAVIT' => tr('Javit', 'Name of carrier', 'woocommerce-paypal-payments'), 'CN_JCEX' => tr('JCEX courier', 'Name of carrier', 'woocommerce-paypal-payments'), 'JD_EXPRESS' => tr('JD Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'JD_WORLDWIDE' => tr('JD Worldwide', 'Name of carrier', 'woocommerce-paypal-payments'), 'JETSHIP_MY' => tr('jetship_my', 'Name of carrier', 'woocommerce-paypal-payments'), 'JNE_API' => tr('JNE (API)', 'Name of carrier', 'woocommerce-paypal-payments'), 'IDN_JNE' => tr('JNE Express (Jalur Nugraha Ekakurir)', 'Name of carrier', 'woocommerce-paypal-payments'), 'JOYINGBOX' => tr('joyingbox', 'Name of carrier', 'woocommerce-paypal-payments'), 'KARGOMKOLAY' => tr('KargomKolay (CargoMini)', 'Name of carrier', 'woocommerce-paypal-payments'), 'KEDAEX' => tr('KedaEX', 'Name of carrier', 'woocommerce-paypal-payments'), 'HK_TGX' => tr('Kerry Express Hong Kong', 'Name of carrier', 'woocommerce-paypal-payments'), 'KERRY_EXPRESS_TW_API' => tr('Kerry Express TaiWan', 'Name of carrier', 'woocommerce-paypal-payments'), 'THA_KERRY' => tr('Kerry Express Thailand', 'Name of carrier', 'woocommerce-paypal-payments'), 'KERRY_EXPRESS_TH_WEBHOOK' => tr('Kerry Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'KNG' => tr('Keuhne + Nagel Global', 'Name of carrier', 'woocommerce-paypal-payments'), 'BE_KIALA' => tr('Kiala', 'Name of carrier', 'woocommerce-paypal-payments'), 'LOGISYSTEMS_SFTP' => tr('Kiitääjät', 'Name of carrier', 'woocommerce-paypal-payments'), 'KOMON_EXPRESS' => tr('Komon Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'KRONOS_WEBHOOK' => tr('Kronos Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'KRONOS' => tr('Kronos Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'KUEHNE' => tr('Kuehne + Nagel', 'Name of carrier', 'woocommerce-paypal-payments'), 'LALAMOVE_API' => tr('Lalamove', 'Name of carrier', 'woocommerce-paypal-payments'), 'LBCEXPRESS_FTP' => tr('LBC EXPRESS INC.', 'Name of carrier', 'woocommerce-paypal-payments'), 'LBCEXPRESS_API' => tr('LBC EXPRESS INC.', 'Name of carrier', 'woocommerce-paypal-payments'), 'LCTBR_API' => tr('LCT do Brasil', 'Name of carrier', 'woocommerce-paypal-payments'), 'LTU_LIETUVOS' => tr('Lietuvos pastas', 'Name of carrier', 'woocommerce-paypal-payments'), 'LINKBRIDGE' => tr('Link Bridge(BeiJing)international logistics co.', 'Name of carrier', 'woocommerce-paypal-payments'), 'LION_PARCEL' => tr('LION PARCEL', 'Name of carrier', 'woocommerce-paypal-payments'), 'LIVRAPIDE' => tr('Livrapide', 'Name of carrier', 'woocommerce-paypal-payments'), 'LOGGI' => tr('Loggi', 'Name of carrier', 'woocommerce-paypal-payments'), 'LOGISTICSWORLDWIDE_KR' => tr('LOGISTICSWORLDWIDE KR', 'Name of carrier', 'woocommerce-paypal-payments'), 'LOGISTICSWORLDWIDE_MY' => tr('LOGISTICSWORLDWIDE MY', 'Name of carrier', 'woocommerce-paypal-payments'), 'LOGWIN_LOGISTICS' => tr('Logwin Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'LOGYSTO' => tr('Logysto', 'Name of carrier', 'woocommerce-paypal-payments'), 'LUWJISTIK' => tr('Luwjistik', 'Name of carrier', 'woocommerce-paypal-payments'), 'MX_CARGO' => tr('M&X cargo', 'Name of carrier', 'woocommerce-paypal-payments'), 'M3LOGISTICS' => tr('M3 Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'REIMAGINEDELIVERY' => tr('maergo', 'Name of carrier', 'woocommerce-paypal-payments'), 'MAGYAR_POSTA_API' => tr('Magyar Posta', 'Name of carrier', 'woocommerce-paypal-payments'), 'MAIL_BOX_ETC' => tr('Mail Boxes Etc.', 'Name of carrier', 'woocommerce-paypal-payments'), 'MYS_EMS' => tr('Malaysia Post EMS / Pos Laju', 'Name of carrier', 'woocommerce-paypal-payments'), 'MALCA_AMIT_API' => tr('Malca Amit', 'Name of carrier', 'woocommerce-paypal-payments'), 'MALCA_AMIT' => tr('Malca-Amit', 'Name of carrier', 'woocommerce-paypal-payments'), 'MARKEN' => tr('Marken', 'Name of carrier', 'woocommerce-paypal-payments'), 'MEDAFRICA' => tr('Med Africa Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'MEEST' => tr('Meest', 'Name of carrier', 'woocommerce-paypal-payments'), 'MEGASAVE' => tr('megasave', 'Name of carrier', 'woocommerce-paypal-payments'), 'MENSAJEROSURBANOS_API' => tr('Mensajeros Urbanos', 'Name of carrier', 'woocommerce-paypal-payments'), 'MWD' => tr('Metropolitan Warehouse & Delivery', 'Name of carrier', 'woocommerce-paypal-payments'), 'MWD_API' => tr('Metropolitan Warehouse & Delivery', 'Name of carrier', 'woocommerce-paypal-payments'), 'MHI' => tr('Mhi', 'Name of carrier', 'woocommerce-paypal-payments'), 'MIKROPAKKET' => tr('Mikropakket', 'Name of carrier', 'woocommerce-paypal-payments'), 'MISUMI_CN' => tr('MISUMI Group Inc.', 'Name of carrier', 'woocommerce-paypal-payments'), 'MNX' => tr('MNX', 'Name of carrier', 'woocommerce-paypal-payments'), 'MOBI_BR' => tr('Mobi Logistica', 'Name of carrier', 'woocommerce-paypal-payments'), 'MONDIALRELAY_FR' => tr('Mondial Relay France', 'Name of carrier', 'woocommerce-paypal-payments'), 'MONDIALRELAY_ES' => tr('Mondial Relay Spain(Punto Pack)', 'Name of carrier', 'woocommerce-paypal-payments'), 'MONDIAL_BE' => tr('MONDIAL_BE', 'Name of carrier', 'woocommerce-paypal-payments'), 'MOOVA' => tr('Moova', 'Name of carrier', 'woocommerce-paypal-payments'), 'MORNINGLOBAL' => tr('Morning Global', 'Name of carrier', 'woocommerce-paypal-payments'), 'MOTHERSHIP_API' => tr('Mothership', 'Name of carrier', 'woocommerce-paypal-payments'), 'MOVIANTO' => tr('Movianto', 'Name of carrier', 'woocommerce-paypal-payments'), 'MUDITA' => tr('MUDITA', 'Name of carrier', 'woocommerce-paypal-payments'), 'MYDYNALOGIC' => tr('My DynaLogic', 'Name of carrier', 'woocommerce-paypal-payments'), 'MYSENDLE_API' => tr('mySendle', 'Name of carrier', 'woocommerce-paypal-payments'), 'NMTRANSFER' => tr('N&M Transfer Co., Inc.', 'Name of carrier', 'woocommerce-paypal-payments'), 'NACEX_SPAIN_REFERENCE' => tr('nacex_spain_reference', 'Name of carrier', 'woocommerce-paypal-payments'), 'NAEKO_FTP' => tr('Naeko Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'NAQEL_EXPRESS' => tr('Naqel Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'NEWZEALAND_COURIERS' => tr('NEW ZEALAND COURIERS', 'Name of carrier', 'woocommerce-paypal-payments'), 'NEWGISTICS' => tr('Newgistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'NEWGISTICSAPI' => tr('Newgistics API', 'Name of carrier', 'woocommerce-paypal-payments'), 'NIGHTLINE_UK' => tr('nightline_uk', 'Name of carrier', 'woocommerce-paypal-payments'), 'NIMBUSPOST' => tr('NimbusPost', 'Name of carrier', 'woocommerce-paypal-payments'), 'NIPPON_EXPRESS_FTP' => tr('Nippon Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'NIPPON_EXPRESS' => tr('Nippon Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'NORTHLINE' => tr('Northline', 'Name of carrier', 'woocommerce-paypal-payments'), 'NOVA_POSHTA_API' => tr('Nova Poshta API', 'Name of carrier', 'woocommerce-paypal-payments'), 'NOVOFARMA_WEBHOOK' => tr('Novofarma', 'Name of carrier', 'woocommerce-paypal-payments'), 'NTL' => tr('NTL logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'NYTLOGISTICS' => tr('NYT SUPPLY CHAIN LOGISTICS Co., LTD', 'Name of carrier', 'woocommerce-paypal-payments'), 'OHI_WEBHOOK' => tr('Ohi', 'Name of carrier', 'woocommerce-paypal-payments'), 'SHOPOLIVE' => tr('Olive', 'Name of carrier', 'woocommerce-paypal-payments'), 'OMLOGISTICS_API' => tr('OM LOGISTICS LTD', 'Name of carrier', 'woocommerce-paypal-payments'), 'OMNIRPS_WEBHOOK' => tr('Omni Returns', 'Name of carrier', 'woocommerce-paypal-payments'), 'ONTRAC' => tr('ONTRAC', 'Name of carrier', 'woocommerce-paypal-payments'), 'ORANGECONNEX' => tr('orangeconnex', 'Name of carrier', 'woocommerce-paypal-payments'), 'ORANGE_DS' => tr('OrangeDS (Orange Distribution Solutions Inc)', 'Name of carrier', 'woocommerce-paypal-payments'), 'OSM_WORLDWIDE_SFTP' => tr('OSM Worldwide', 'Name of carrier', 'woocommerce-paypal-payments'), 'OZEPARTS_SHIPPING' => tr('Ozeparts Shipping', 'Name of carrier', 'woocommerce-paypal-payments'), 'P2P_TRC' => tr('P2P TrakPak', 'Name of carrier', 'woocommerce-paypal-payments'), 'PACKETA' => tr('Packeta', 'Name of carrier', 'woocommerce-paypal-payments'), 'PACKFLEET' => tr('PACKFLEET', 'Name of carrier', 'woocommerce-paypal-payments'), 'PACKS' => tr('Packs', 'Name of carrier', 'woocommerce-paypal-payments'), 'PAKAJO' => tr('Pakajo World', 'Name of carrier', 'woocommerce-paypal-payments'), 'PANDAGO_API' => tr('Pandago', 'Name of carrier', 'woocommerce-paypal-payments'), 'PANDION' => tr('Pandion', 'Name of carrier', 'woocommerce-paypal-payments'), 'PANDU' => tr('PANDU', 'Name of carrier', 'woocommerce-paypal-payments'), 'PANTHER_REFERENCE_API' => tr('Panther Reference', 'Name of carrier', 'woocommerce-paypal-payments'), 'PANTHER_ORDER_NUMBER' => tr('panther_order_number', 'Name of carrier', 'woocommerce-paypal-payments'), 'PAPA_WEBHOOK' => tr('Papa', 'Name of carrier', 'woocommerce-paypal-payments'), 'PARCELRIGHT' => tr('Parcel Right', 'Name of carrier', 'woocommerce-paypal-payments'), 'PARCEL_2_POST' => tr('Parcel To Post', 'Name of carrier', 'woocommerce-paypal-payments'), 'PARCELFORCE' => tr('PARCELFORCE', 'Name of carrier', 'woocommerce-paypal-payments'), 'PARCELSTARS_WEBHOOK' => tr('Parcelstars', 'Name of carrier', 'woocommerce-paypal-payments'), 'PARCLL' => tr('PARCLL', 'Name of carrier', 'woocommerce-paypal-payments'), 'PASSPORTSHIPPING' => tr('Passport Shipping', 'Name of carrier', 'woocommerce-paypal-payments'), 'PATHEON' => tr('Patheon Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'PAYO' => tr('Payo', 'Name of carrier', 'woocommerce-paypal-payments'), 'PCHOME_API' => tr('Pchome Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'PGEON_API' => tr('Pgeon', 'Name of carrier', 'woocommerce-paypal-payments'), 'PHSE_API' => tr('PHSE', 'Name of carrier', 'woocommerce-paypal-payments'), 'PICKUPP_VNM' => tr('pickupp_vnm', 'Name of carrier', 'woocommerce-paypal-payments'), 'PIDGE' => tr('Pidge', 'Name of carrier', 'woocommerce-paypal-payments'), 'PIL_LOGISTICS' => tr('PIL Logistics (China) Co.', 'Name of carrier', 'woocommerce-paypal-payments'), 'PLYCONGROUP' => tr('Plycon Transportation Group', 'Name of carrier', 'woocommerce-paypal-payments'), 'POLARSPEED' => tr('PolarSpeed Inc', 'Name of carrier', 'woocommerce-paypal-payments'), 'POSTONE' => tr('Post ONE', 'Name of carrier', 'woocommerce-paypal-payments'), 'POSTAPLUS' => tr('Posta Plus', 'Name of carrier', 'woocommerce-paypal-payments'), 'POSTE_ITALIANE_PACCOCELERE' => tr('Poste Italiane Paccocelere', 'Name of carrier', 'woocommerce-paypal-payments'), 'POSTEN_NORGE' => tr('Posten Norge (www.posten.no)', 'Name of carrier', 'woocommerce-paypal-payments'), 'POSTI_API' => tr('Posti API', 'Name of carrier', 'woocommerce-paypal-payments'), 'POSTNL_INT_3_S' => tr('PostNL International', 'Name of carrier', 'woocommerce-paypal-payments'), 'NLD_POSTNL' => tr('PostNL International', 'Name of carrier', 'woocommerce-paypal-payments'), 'POSTNL_INTERNATIONAL' => tr('PostNL International', 'Name of carrier', 'woocommerce-paypal-payments'), 'SWE_POSTNORD' => tr('Postnord sweden', 'Name of carrier', 'woocommerce-paypal-payments'), 'POSTPLUS' => tr('PostPlus', 'Name of carrier', 'woocommerce-paypal-payments'), 'PROCARRIER' => tr('Pro Carrier', 'Name of carrier', 'woocommerce-paypal-payments'), 'PRODUCTCAREGROUP_SFTP' => tr('Product Care Services Limited', 'Name of carrier', 'woocommerce-paypal-payments'), 'PROFESSIONAL_COURIERS' => tr('PROFESSIONAL COURIERS', 'Name of carrier', 'woocommerce-paypal-payments'), 'PPL' => tr('Professional Parcel Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'PROMEDDELIVERY' => tr('ProMed Delivery', 'Name of carrier', 'woocommerce-paypal-payments'), 'PUROLATOR' => tr('purolator', 'Name of carrier', 'woocommerce-paypal-payments'), 'PUROLATOR_INTERNATIONAL' => tr('Purolator International', 'Name of carrier', 'woocommerce-paypal-payments'), 'QTRACK' => tr('QTrack', 'Name of carrier', 'woocommerce-paypal-payments'), 'QUALITYPOST' => tr('qualitypost', 'Name of carrier', 'woocommerce-paypal-payments'), 'QINTL_API' => tr('Quickstat Courier LLC', 'Name of carrier', 'woocommerce-paypal-payments'), 'QUIQUP' => tr('Quiqup', 'Name of carrier', 'woocommerce-paypal-payments'), 'RANSA_WEBHOOK' => tr('Ransa', 'Name of carrier', 'woocommerce-paypal-payments'), 'REDJEPAKKETJE' => tr('Red je Pakketje', 'Name of carrier', 'woocommerce-paypal-payments'), 'RELAISCOLIS' => tr('Relais Colis', 'Name of carrier', 'woocommerce-paypal-payments'), 'RHENUS_GROUP' => tr('Rhenus Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'RHENUS_UK_API' => tr('Rhenus Logistics UK', 'Name of carrier', 'woocommerce-paypal-payments'), 'AIR_CANADA' => tr('Rivo', 'Name of carrier', 'woocommerce-paypal-payments'), 'RIXONHK_API' => tr('Rixon Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'ROCHE_INTERNAL_SFTP' => tr('Roche Internal Courier', 'Name of carrier', 'woocommerce-paypal-payments'), 'ROYAL_MAIL_FTP' => tr('Royal Mail', 'Name of carrier', 'woocommerce-paypal-payments'), 'ROYALSHIPMENTS' => tr('royalshipments', 'Name of carrier', 'woocommerce-paypal-payments'), 'RRDONNELLEY' => tr('rrdonnelley', 'Name of carrier', 'woocommerce-paypal-payments'), 'RUSSIAN_POST' => tr('Russian post', 'Name of carrier', 'woocommerce-paypal-payments'), 'SAEE' => tr('saee', 'Name of carrier', 'woocommerce-paypal-payments'), 'SAGAWA' => tr('SAGAWA', 'Name of carrier', 'woocommerce-paypal-payments'), 'SAGAWA_API' => tr('Sagawa', 'Name of carrier', 'woocommerce-paypal-payments'), 'SBERLOGISTICS_RU' => tr('Sber Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'SECRETLAB_WEBHOOK' => tr('Secretlab', 'Name of carrier', 'woocommerce-paypal-payments'), 'SEINO_API' => tr('Seino', 'Name of carrier', 'woocommerce-paypal-payments'), 'SEKO_SFTP' => tr('SEKO Worldwide', 'Name of carrier', 'woocommerce-paypal-payments'), 'SENDING' => tr('Sending Transporte Urgente y Comunicacion', 'Name of carrier', 'woocommerce-paypal-payments'), 'SHOWL' => tr('SENHONG INTERNATIONAL LOGISTICS', 'Name of carrier', 'woocommerce-paypal-payments'), 'NOWLOG_API' => tr('Sequoialog', 'Name of carrier', 'woocommerce-paypal-payments'), 'SERVIENTREGA' => tr('Servientrega', 'Name of carrier', 'woocommerce-paypal-payments'), 'SERVIP_WEBHOOK' => tr('SerVIP', 'Name of carrier', 'woocommerce-paypal-payments'), 'SETEL' => tr('Setel Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'SF_EX' => tr('SF Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'SF_EXPRESS_CN' => tr('SF Express China', 'Name of carrier', 'woocommerce-paypal-payments'), 'SGT_IT' => tr('SGT_IT', 'Name of carrier', 'woocommerce-paypal-payments'), 'SHADOWFAX' => tr('Shadowfax', 'Name of carrier', 'woocommerce-paypal-payments'), 'SHENZHEN' => tr('shenzhen 1st International Logistics(Group)Co', 'Name of carrier', 'woocommerce-paypal-payments'), 'HOTSIN_CARGO' => tr('SHENZHEN HOTSIN CARGO INTL FORWARDING CO., LTD', 'Name of carrier', 'woocommerce-paypal-payments'), 'KWT' => tr('Shenzhen Jinghuada Logistics Co.', 'Name of carrier', 'woocommerce-paypal-payments'), 'SHERPA' => tr('Sherpa', 'Name of carrier', 'woocommerce-paypal-payments'), 'SHIPA' => tr('SHIPA', 'Name of carrier', 'woocommerce-paypal-payments'), 'SHIPPIE' => tr('Shippie', 'Name of carrier', 'woocommerce-paypal-payments'), 'SHIPPIFY' => tr('Shippify, Inc', 'Name of carrier', 'woocommerce-paypal-payments'), 'SHIPROCKET' => tr('Shiprocket X', 'Name of carrier', 'woocommerce-paypal-payments'), 'SHIPX' => tr('ShipX', 'Name of carrier', 'woocommerce-paypal-payments'), 'SHIPXPRES' => tr('SHIPXPRESS', 'Name of carrier', 'woocommerce-paypal-payments'), 'SPX' => tr('Shopee Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'SPX_TH' => tr('Shopee Xpress', 'Name of carrier', 'woocommerce-paypal-payments'), 'SHUNBANG_EXPRESS' => tr('ShunBang Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'SHYPLITE' => tr('Shypmax', 'Name of carrier', 'woocommerce-paypal-payments'), 'SIMPLETIRE_WEBHOOK' => tr('SimpleTire', 'Name of carrier', 'woocommerce-paypal-payments'), 'SIMSGLOBAL' => tr('Sims Global', 'Name of carrier', 'woocommerce-paypal-payments'), 'SIODEMKA' => tr('SIODEMKA', 'Name of carrier', 'woocommerce-paypal-payments'), 'SKynet_WORLDWIDE' => tr('SkyNet Worldwide Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'SKY_POSTAL' => tr('SkyPostal', 'Name of carrier', 'woocommerce-paypal-payments'), 'SK_POSTA' => tr('Slovenska pošta', 'Name of carrier', 'woocommerce-paypal-payments'), 'SMARTCAT' => tr('SMARTCAT', 'Name of carrier', 'woocommerce-paypal-payments'), 'SMARTKARGO' => tr('SmartKargo', 'Name of carrier', 'woocommerce-paypal-payments'), 'SMG_EXPRESS' => tr('SMG Direct', 'Name of carrier', 'woocommerce-paypal-payments'), 'SMSA_EXPRESS_WEBHOOK' => tr('SMSA Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'SNTGLOBAL_API' => tr('Snt Global Etrax', 'Name of carrier', 'woocommerce-paypal-payments'), 'SOLISTICA_API' => tr('solistica', 'Name of carrier', 'woocommerce-paypal-payments'), 'SPANISH_SEUR_FTP' => tr('Spanish Seur', 'Name of carrier', 'woocommerce-paypal-payments'), 'SPECTRAN' => tr('Spectran', 'Name of carrier', 'woocommerce-paypal-payments'), 'SPEEDEX' => tr('speedex', 'Name of carrier', 'woocommerce-paypal-payments'), 'SPEEDY' => tr('Speedy', 'Name of carrier', 'woocommerce-paypal-payments'), 'SPREETAIL_API' => tr('Spreetail', 'Name of carrier', 'woocommerce-paypal-payments'), 'SPRINT_PACK' => tr('SPRINT PACK', 'Name of carrier', 'woocommerce-paypal-payments'), 'SRT_TRANSPORT' => tr('SRT Transport', 'Name of carrier', 'woocommerce-paypal-payments'), 'STAR_TRACK_NEXT_FLIGHT' => tr('Star Track Next Flight', 'Name of carrier', 'woocommerce-paypal-payments'), 'STARLINKS_API' => tr('Starlinks Global', 'Name of carrier', 'woocommerce-paypal-payments'), 'STARTRACK' => tr('startrack', 'Name of carrier', 'woocommerce-paypal-payments'), 'STAR_TRACK_WEBHOOK' => tr('StarTrack', 'Name of carrier', 'woocommerce-paypal-payments'), 'STARTRACK_EXPRESS' => tr('startrack_express', 'Name of carrier', 'woocommerce-paypal-payments'), 'STATOVERNIGHT' => tr('Stat Overnight', 'Name of carrier', 'woocommerce-paypal-payments'), 'CN_STO' => tr('STO Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'SWISHIP' => tr('Swiship', 'Name of carrier', 'woocommerce-paypal-payments'), 'SWISS_POST' => tr('SWISS POST', 'Name of carrier', 'woocommerce-paypal-payments'), 'T_CAT' => tr('T-cat', 'Name of carrier', 'woocommerce-paypal-payments'), 'T_CAT_API' => tr('T-cat', 'Name of carrier', 'woocommerce-paypal-payments'), 'LOGINEXT_WEBHOOK' => tr('T&W Delivery', 'Name of carrier', 'woocommerce-paypal-payments'), 'TW_TAIWAN_POST' => tr('Taiwan Post', 'Name of carrier', 'woocommerce-paypal-payments'), 'TAMERGROUP_WEBHOOK' => tr('Tamer Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'TAQBIN_HK' => tr('TAQBIN Hong Kong', 'Name of carrier', 'woocommerce-paypal-payments'), 'TAQBIN_SG' => tr('taqbin_sg', 'Name of carrier', 'woocommerce-paypal-payments'), 'TCS_API' => tr('TCS', 'Name of carrier', 'woocommerce-paypal-payments'), 'TECOR' => tr('tecor', 'Name of carrier', 'woocommerce-paypal-payments'), 'TELEPORT_WEBHOOK' => tr('Teleport', 'Name of carrier', 'woocommerce-paypal-payments'), 'SIC_TELIWAY' => tr('Teliway SIC Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'TESTING_COURIER_WEBHOOK' => tr('Testing Courier', 'Name of carrier', 'woocommerce-paypal-payments'), 'TESTING_COURIER' => tr('Testing Courier', 'Name of carrier', 'woocommerce-paypal-payments'), 'TH_CJ' => tr('TH_CJ', 'Name of carrier', 'woocommerce-paypal-payments'), 'THIJS_NL' => tr('Thijs Logistiek', 'Name of carrier', 'woocommerce-paypal-payments'), 'THUNDEREXPRESS' => tr('Thunder Express Australia', 'Name of carrier', 'woocommerce-paypal-payments'), 'TIPSA_API' => tr('Tipsa API', 'Name of carrier', 'woocommerce-paypal-payments'), 'TIPSA_REF' => tr('Tipsa Reference', 'Name of carrier', 'woocommerce-paypal-payments'), 'TNT_FR_REFERENCE' => tr('TNT France Reference', 'Name of carrier', 'woocommerce-paypal-payments'), 'TNT_REFR' => tr('TNT Reference', 'Name of carrier', 'woocommerce-paypal-payments'), 'TNT_AU' => tr('tnt_au', 'Name of carrier', 'woocommerce-paypal-payments'), 'TNT_CN' => tr('TNT_CN', 'Name of carrier', 'woocommerce-paypal-payments'), 'TNT_DE' => tr('TNT_DE', 'Name of carrier', 'woocommerce-paypal-payments'), 'TNT_ES' => tr('TNT_ES', 'Name of carrier', 'woocommerce-paypal-payments'), 'TNT_IT' => tr('tnt_it', 'Name of carrier', 'woocommerce-paypal-payments'), 'TNT_JP' => tr('TNT_JP', 'Name of carrier', 'woocommerce-paypal-payments'), 'TNT_PL' => tr('TNT_PL', 'Name of carrier', 'woocommerce-paypal-payments'), 'TOLL_WEBHOOK' => tr('Toll Group', 'Name of carrier', 'woocommerce-paypal-payments'), 'TOLL_IPEC' => tr('TOLL IPEC', 'Name of carrier', 'woocommerce-paypal-payments'), 'TOLL_PRIORITY' => tr('Toll Priority', 'Name of carrier', 'woocommerce-paypal-payments'), 'TOMYDOOR' => tr('Tomydoor', 'Name of carrier', 'woocommerce-paypal-payments'), 'TONAMI_FTP' => tr('Tonami', 'Name of carrier', 'woocommerce-paypal-payments'), 'ESDEX' => tr('Top Ideal Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'TOTAL_EXPRESS_API' => tr('Total Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'TOURLINE_REFERENCE' => tr('Tourline Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'THAIPARCELS' => tr('TP Logistic', 'Name of carrier', 'woocommerce-paypal-payments'), 'TRANS2U' => tr('Trans2u', 'Name of carrier', 'woocommerce-paypal-payments'), 'TRANSMISSION' => tr('TRANSMISSION', 'Name of carrier', 'woocommerce-paypal-payments'), 'TANET' => tr('Transport Ambientales', 'Name of carrier', 'woocommerce-paypal-payments'), 'TRANSVIRTUAL' => tr('TransVirtual', 'Name of carrier', 'woocommerce-paypal-payments'), 'TRUNKRS' => tr('Trunkrs', 'Name of carrier', 'woocommerce-paypal-payments'), 'TRUSK' => tr('Trusk France', 'Name of carrier', 'woocommerce-paypal-payments'), 'TUSKLOGISTICS' => tr('Tusk Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'TYP' => tr('TYP', 'Name of carrier', 'woocommerce-paypal-payments'), 'U_ENVIOS' => tr('U-ENVIOS', 'Name of carrier', 'woocommerce-paypal-payments'), 'UBER_WEBHOOK' => tr('Uber', 'Name of carrier', 'woocommerce-paypal-payments'), 'UCS' => tr('UCS', 'Name of carrier', 'woocommerce-paypal-payments'), 'UDS' => tr('United Delivery Service', 'Name of carrier', 'woocommerce-paypal-payments'), 'UPS' => tr('United Parcel Service', 'Name of carrier', 'woocommerce-paypal-payments'), 'UP_EXPRESS' => tr('up_express', 'Name of carrier', 'woocommerce-paypal-payments'), 'UPARCEL' => tr('uParcel', 'Name of carrier', 'woocommerce-paypal-payments'), 'UPS_API' => tr('UPS', 'Name of carrier', 'woocommerce-paypal-payments'), 'UPS_FREIGHT' => tr('UPS Freight', 'Name of carrier', 'woocommerce-paypal-payments'), 'UPS_REFERENCE' => tr('UPS Reference', 'Name of carrier', 'woocommerce-paypal-payments'), 'URGENT_CARGUS' => tr('Urgent Cargus', 'Name of carrier', 'woocommerce-paypal-payments'), 'US_APC' => tr('us_apc', 'Name of carrier', 'woocommerce-paypal-payments'), 'USPS_API' => tr('USPS API', 'Name of carrier', 'woocommerce-paypal-payments'), 'PB_USPSFLATS_FTP' => tr('USPS Flats (Pitney Bowes)', 'Name of carrier', 'woocommerce-paypal-payments'), 'USPS_WEBHOOK' => tr('USPS Informed Visibility - Webhook', 'Name of carrier', 'woocommerce-paypal-payments'), 'VALUE_WEBHOOK' => tr('Value Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'VIAXPRESS' => tr('ViaXpress', 'Name of carrier', 'woocommerce-paypal-payments'), 'VNPOST_API' => tr('Vietnam Post', 'Name of carrier', 'woocommerce-paypal-payments'), 'VIRTRANSPORT_SFTP' => tr('Vir Transport', 'Name of carrier', 'woocommerce-paypal-payments'), 'VNPOST_EMS' => tr('vnpost_ems', 'Name of carrier', 'woocommerce-paypal-payments'), 'VOX' => tr('VOX SOLUCION EMPRESARIAL SRL', 'Name of carrier', 'woocommerce-paypal-payments'), 'WATKINS_SHEPARD' => tr('watkins_shepard', 'Name of carrier', 'woocommerce-paypal-payments'), 'WEWORLDEXPRESS' => tr('We World Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'WESHIP_API' => tr('WeShip', 'Name of carrier', 'woocommerce-paypal-payments'), 'WESHIP' => tr('WeShip', 'Name of carrier', 'woocommerce-paypal-payments'), 'WHISTL_SFTP' => tr('Whistl', 'Name of carrier', 'woocommerce-paypal-payments'), 'WINESHIPPING_WEBHOOK' => tr('Wineshipping', 'Name of carrier', 'woocommerce-paypal-payments'), 'WISH_EMAIL_PUSH' => tr('Wish', 'Name of carrier', 'woocommerce-paypal-payments'), 'WOOYOUNG_LOGISTICS_SFTP' => tr('WOO YOUNG LOGISTICS CO., LTD.', 'Name of carrier', 'woocommerce-paypal-payments'), 'WORLDCOURIER' => tr('World Courier', 'Name of carrier', 'woocommerce-paypal-payments'), 'WORLDNET' => tr('Worldnet Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'WSPEXPRESS' => tr('WSP Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'XYY' => tr('Xingyunyi Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'XPEDIGO' => tr('Xpedigo', 'Name of carrier', 'woocommerce-paypal-payments'), 'XPRESSBEES' => tr('XPRESSBEES', 'Name of carrier', 'woocommerce-paypal-payments'), 'YAMATO' => tr('YAMATO', 'Name of carrier', 'woocommerce-paypal-payments'), 'TAQBIN_SG_API' => tr('Yamato Singapore', 'Name of carrier', 'woocommerce-paypal-payments'), 'YIFAN' => tr('YiFan Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'YODEL' => tr('yodel', 'Name of carrier', 'woocommerce-paypal-payments'), 'YODEL_API' => tr('Yodel API', 'Name of carrier', 'woocommerce-paypal-payments'), 'YODEL_DIR' => tr('Yodel Direct', 'Name of carrier', 'woocommerce-paypal-payments'), 'YODEL_INTNL' => tr('Yodel International', 'Name of carrier', 'woocommerce-paypal-payments'), 'YUSEN' => tr('Yusen Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'YUSEN_SFTP' => tr('Yusen Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'YYCOM' => tr('yycom', 'Name of carrier', 'woocommerce-paypal-payments'), 'YYEXPRESS' => tr('YYEXPRESS', 'Name of carrier', 'woocommerce-paypal-payments'), 'ZTO_DOMESTIC' => tr('ZTO Express China', 'Name of carrier', 'woocommerce-paypal-payments'), 'ZUELLIGPHARMA_SFTP' => tr('Zuellig Pharma Korea', 'Name of carrier', 'woocommerce-paypal-payments'))), 'AG' => array('name' => tr('Argentina', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('FASTRACK' => tr('Fasttrack', 'Name of carrier', 'woocommerce-paypal-payments'), 'ANDREANI' => tr('Grupo logistico Andreani', 'Name of carrier', 'woocommerce-paypal-payments'), 'ARG_OCA' => tr('OCA Argentina', 'Name of carrier', 'woocommerce-paypal-payments'))), 'AU' => array('name' => tr('Australia', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('ADSONE' => tr('Adsone', 'Name of carrier', 'woocommerce-paypal-payments'), 'ALLIEDEXPRESS' => tr('Allied Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'ARAMEX_AU' => tr('Aramex Australia (formerly Fastway AU)', 'Name of carrier', 'woocommerce-paypal-payments'), 'AU_AU_POST' => tr('Australia Post', 'Name of carrier', 'woocommerce-paypal-payments'), 'BLUESTAR' => tr('Blue Star', 'Name of carrier', 'woocommerce-paypal-payments'), 'BONDSCOURIERS' => tr('Bonds Courier Service (bondscouriers.com.au)', 'Name of carrier', 'woocommerce-paypal-payments'), 'BORDEREXPRESS' => tr('Border Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'COPE' => tr('Cope Sensitive Freight', 'Name of carrier', 'woocommerce-paypal-payments'), 'COURIERS_PLEASE' => tr('CouriersPlease (couriersplease.com.au)', 'Name of carrier', 'woocommerce-paypal-payments'), 'DELIVERE' => tr('deliverE', 'Name of carrier', 'woocommerce-paypal-payments'), 'DESIGNERTRANSPORT_WEBHOOK' => tr('Designer Transport', 'Name of carrier', 'woocommerce-paypal-payments'), 'DHL_AU' => tr('DHL Supply Chain Australia', 'Name of carrier', 'woocommerce-paypal-payments'), 'DIRECTCOURIERS' => tr('Direct Couriers', 'Name of carrier', 'woocommerce-paypal-payments'), 'DTDC_AU' => tr('DTDC Australia', 'Name of carrier', 'woocommerce-paypal-payments'), 'ENDEAVOUR_DELIVERY' => tr('Endeavour Delivery', 'Name of carrier', 'woocommerce-paypal-payments'), 'HUNTER_EXPRESS' => tr('Hunter Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'ICUMULUS' => tr('iCumulus', 'Name of carrier', 'woocommerce-paypal-payments'), 'INTERPARCEL_AU' => tr('Interparcel Australia', 'Name of carrier', 'woocommerce-paypal-payments'), 'NEWAY' => tr('Neway Transport', 'Name of carrier', 'woocommerce-paypal-payments'), 'PARCELPOINT' => tr('Parcelpoint', 'Name of carrier', 'woocommerce-paypal-payments'), 'PFLOGISTICS' => tr('PFL', 'Name of carrier', 'woocommerce-paypal-payments'), 'SENDLE' => tr('Sendle', 'Name of carrier', 'woocommerce-paypal-payments'), 'SHIPPIT' => tr('Shippit', 'Name of carrier', 'woocommerce-paypal-payments'), 'THENILE_WEBHOOK' => tr('SortHub courier', 'Name of carrier', 'woocommerce-paypal-payments'), 'STAR_TRACK_EXPRESS' => tr('Star Track Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'AUS_STARTRACK' => tr('StarTrack (startrack.com.au)', 'Name of carrier', 'woocommerce-paypal-payments'), 'TFM' => tr('TFM Xpress', 'Name of carrier', 'woocommerce-paypal-payments'), 'TIGFREIGHT' => tr('TIG Freight', 'Name of carrier', 'woocommerce-paypal-payments'), 'TOLL' => tr('Toll IPEC', 'Name of carrier', 'woocommerce-paypal-payments'), 'UBI_LOGISTICS' => tr('UBI Smart Parcel', 'Name of carrier', 'woocommerce-paypal-payments'), 'XL_EXPRESS' => tr('XL Express', 'Name of carrier', 'woocommerce-paypal-payments'))), 'AT' => array('name' => tr('Austria', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('AUSTRIAN_POST_EXPRESS' => tr('Austrian Post', 'Name of carrier', 'woocommerce-paypal-payments'), 'AU_AUSTRIAN_POST' => tr('Austrian Post (Registered)', 'Name of carrier', 'woocommerce-paypal-payments'))), 'BGD' => array('name' => tr('Bangladesh', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('PAPERFLY' => tr('Paperfly Private Limited', 'Name of carrier', 'woocommerce-paypal-payments'))), 'BE' => array('name' => tr('Belgium', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('B_TWO_C_EUROPE' => tr('B2C courier Europe', 'Name of carrier', 'woocommerce-paypal-payments'), 'DHL_BENELUX' => tr('dhl benelux', 'Name of carrier', 'woocommerce-paypal-payments'), 'BEL_DHL' => tr('DHL Benelux', 'Name of carrier', 'woocommerce-paypal-payments'), 'LANDMARK_GLOBAL' => tr('Landmark Global', 'Name of carrier', 'woocommerce-paypal-payments'), 'LANDMARK_GLOBAL_REFERENCE' => tr('Landmark Global Reference', 'Name of carrier', 'woocommerce-paypal-payments'), 'MIKROPAKKET_BE' => tr('Mikropakket Belgium', 'Name of carrier', 'woocommerce-paypal-payments'))), 'BIH' => array('name' => tr('Bosnia and Herzegovina', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('BH_POSTA' => tr('BH Posta (www.posta.ba)', 'Name of carrier', 'woocommerce-paypal-payments'))), 'BR' => array('name' => tr('Brazil', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('BRA_CORREIOS' => tr('Correios Brazil', 'Name of carrier', 'woocommerce-paypal-payments'), 'DIRECTLOG' => tr('Directlog', 'Name of carrier', 'woocommerce-paypal-payments'), 'FRETERAPIDO' => tr('Frete Rapido', 'Name of carrier', 'woocommerce-paypal-payments'), 'INTELIPOST' => tr('Intelipost', 'Name of carrier', 'woocommerce-paypal-payments'), 'TOTAL_EXPRESS' => tr('Total Express', 'Name of carrier', 'woocommerce-paypal-payments'))), 'BG' => array('name' => tr('Bulgaria', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('A1POST' => tr('A1Post', 'Name of carrier', 'woocommerce-paypal-payments'), 'BG_BULGARIAN_POST' => tr('Bulgarian Posts', 'Name of carrier', 'woocommerce-paypal-payments'))), 'KHM' => array('name' => tr('Cambodia', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('AFLLOG_FTP' => tr('AFL LOGISTICS', 'Name of carrier', 'woocommerce-paypal-payments'), 'KHM_CAMBODIA_POST' => tr('Cambodia Post', 'Name of carrier', 'woocommerce-paypal-payments'), 'ROADRUNNER_FREIGHT' => tr('Roadbull Logistics', 'Name of carrier', 'woocommerce-paypal-payments'))), 'CA' => array('name' => tr('Canada', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('CA_CANADA_POST' => tr('Canada Post', 'Name of carrier', 'woocommerce-paypal-payments'), 'CHITCHATS' => tr('Chit Chats', 'Name of carrier', 'woocommerce-paypal-payments'), 'CORPORATECOURIERS_WEBHOOK' => tr('Corporate Couriers', 'Name of carrier', 'woocommerce-paypal-payments'), 'COURANT_PLUS' => tr('Courant Plus', 'Name of carrier', 'woocommerce-paypal-payments'), 'GLOBAL_ESTES' => tr('Estes Express Lines', 'Name of carrier', 'woocommerce-paypal-payments'), 'DICOM' => tr('GLS Logistic Systems Canada Ltd./Dicom', 'Name of carrier', 'woocommerce-paypal-payments'), 'LOCUS_WEBHOOK' => tr('Locus courier', 'Name of carrier', 'woocommerce-paypal-payments'), 'LOOMIS_EXPRESS' => tr('Loomis Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'MBW' => tr('MBW Courier Inc.', 'Name of carrier', 'woocommerce-paypal-payments'), 'NATIONEX' => tr('Nationex courier', 'Name of carrier', 'woocommerce-paypal-payments'), 'PARCELPAL_WEBHOOK' => tr('ParcelPal', 'Name of carrier', 'woocommerce-paypal-payments'), 'AIR_CANADA_GLOBAL' => tr('Rivo (Air canada)', 'Name of carrier', 'woocommerce-paypal-payments'), 'ROUTIFIC_WEBHOOK' => tr('Routific', 'Name of carrier', 'woocommerce-paypal-payments'), 'RPXLOGISTICS' => tr('RPX Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'STALLIONEXPRESS' => tr('Stallion Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'ZIINGFINALMILE' => tr('Ziing Final Mile Inc', 'Name of carrier', 'woocommerce-paypal-payments'))), 'CL' => array('name' => tr('Chile', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('BLUEX' => tr('Blue Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'STARKEN' => tr('STARKEN couriers', 'Name of carrier', 'woocommerce-paypal-payments'))), 'CN' => array('name' => tr('China', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('CN_17POST' => tr('17 Post Service', 'Name of carrier', 'woocommerce-paypal-payments'), 'ACSWORLDWIDE' => tr('ACS Worldwide Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'CAINIAO' => tr('AliExpress Standard Shipping', 'Name of carrier', 'woocommerce-paypal-payments'), 'ANJUN' => tr('Anjun couriers', 'Name of carrier', 'woocommerce-paypal-payments'), 'ANSERX' => tr('ANSERX courier', 'Name of carrier', 'woocommerce-paypal-payments'), 'AUPOST_CN' => tr('AuPost China', 'Name of carrier', 'woocommerce-paypal-payments'), 'BEL_RS' => tr('BEL North Russia', 'Name of carrier', 'woocommerce-paypal-payments'), 'CN_BESTEXPRESS' => tr('Best Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'CN_BOXC' => tr('BoxC courier', 'Name of carrier', 'woocommerce-paypal-payments'), 'BUYLOGIC' => tr('buylogic', 'Name of carrier', 'woocommerce-paypal-payments'), 'CPEX' => tr('Captain Express International', 'Name of carrier', 'woocommerce-paypal-payments'), 'CGS_EXPRESS' => tr('CGS Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'CN_CHINA_POST_EMS' => tr('China Post', 'Name of carrier', 'woocommerce-paypal-payments'), 'CHUKOU1' => tr('Chukou1', 'Name of carrier', 'woocommerce-paypal-payments'), 'CJPACKET' => tr('CJ Packet', 'Name of carrier', 'woocommerce-paypal-payments'), 'CLEVY_LINKS' => tr('Clevy Links', 'Name of carrier', 'woocommerce-paypal-payments'), 'CNDEXPRESS' => tr('CND Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'CNEXPS' => tr('CNE Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'COMET_TECH' => tr('CometTech', 'Name of carrier', 'woocommerce-paypal-payments'), 'CPACKET' => tr('Cpacket couriers', 'Name of carrier', 'woocommerce-paypal-payments'), 'CUCKOOEXPRESS' => tr('Cuckoo Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'DEX_I' => tr('DEX-I courier', 'Name of carrier', 'woocommerce-paypal-payments'), 'DIDADI' => tr('DIDADI Logistics tech', 'Name of carrier', 'woocommerce-paypal-payments'), 'DPE_EXPRESS' => tr('DPE Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'DTD_EXPR' => tr('DTD Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'EMPS_CN' => tr('EMPS Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'CN_EQUICK' => tr('Equick China', 'Name of carrier', 'woocommerce-paypal-payments'), 'ESHIPPING' => tr('Eshipping', 'Name of carrier', 'woocommerce-paypal-payments'), 'ZES_EXPRESS' => tr('Eshun international Logistic', 'Name of carrier', 'woocommerce-paypal-payments'), 'FAR_INTERNATIONAL' => tr('Far international', 'Name of carrier', 'woocommerce-paypal-payments'), 'FARGOOD' => tr('FarGood', 'Name of carrier', 'woocommerce-paypal-payments'), 'FULFILLME' => tr('Fulfillme', 'Name of carrier', 'woocommerce-paypal-payments'), 'GANGBAO' => tr('GANGBAO Supplychain', 'Name of carrier', 'woocommerce-paypal-payments'), 'GESWL' => tr('GESWL Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'CN_GOFLY' => tr('GoFly', 'Name of carrier', 'woocommerce-paypal-payments'), 'HDB' => tr('Haidaibao', 'Name of carrier', 'woocommerce-paypal-payments'), 'HDB_BOX' => tr('Haidaibao (BOX)', 'Name of carrier', 'woocommerce-paypal-payments'), 'HH_EXP' => tr('Hua Han Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'HUAHAN_EXPRESS' => tr('HUAHANG EXPRESS', 'Name of carrier', 'woocommerce-paypal-payments'), 'HUODULL' => tr('Huodull', 'Name of carrier', 'woocommerce-paypal-payments'), 'HX_EXPRESS' => tr('HX Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'IDEXPRESS' => tr('IDEX courier', 'Name of carrier', 'woocommerce-paypal-payments'), 'INTEL_VALLEY' => tr('Intel-Valley Supply chain (ShenZhen) Co. Ltd', 'Name of carrier', 'woocommerce-paypal-payments'), 'J_NET' => tr('J-Net', 'Name of carrier', 'woocommerce-paypal-payments'), 'JINDOUYUN' => tr('jindouyun courier', 'Name of carrier', 'woocommerce-paypal-payments'), 'JOOM_LOGIS' => tr('Joom Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'JOYING_BOX' => tr('Joying Box', 'Name of carrier', 'woocommerce-paypal-payments'), 'K1_EXPRESS' => tr('K1 Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'KY_EXPRESS' => tr('Kua Yue Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'LALAMOVE' => tr('Lalamove', 'Name of carrier', 'woocommerce-paypal-payments'), 'LEADER' => tr('leader', 'Name of carrier', 'woocommerce-paypal-payments'), 'SDH_SCM' => tr('lightning monkey', 'Name of carrier', 'woocommerce-paypal-payments'), 'LOGISTERS' => tr('Logisters', 'Name of carrier', 'woocommerce-paypal-payments'), 'LTIANEXP' => tr('LTIAN EXP', 'Name of carrier', 'woocommerce-paypal-payments'), 'LTL' => tr('LTL COURIER', 'Name of carrier', 'woocommerce-paypal-payments'), 'MORE_LINK' => tr('Morelink', 'Name of carrier', 'woocommerce-paypal-payments'), 'MXE' => tr('MXE Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'NANJINGWOYUAN' => tr('Nanjing Woyuan', 'Name of carrier', 'woocommerce-paypal-payments'), 'ONEWORLDEXPRESS' => tr('One World Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'PADTF' => tr('padtf.com', 'Name of carrier', 'woocommerce-paypal-payments'), 'PAGO' => tr('Pago Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'PAN_ASIA' => tr('Pan-Asia International', 'Name of carrier', 'woocommerce-paypal-payments'), 'CN_PAYPAL_PACKAGE' => tr('PayPal Package', 'Name of carrier', 'woocommerce-paypal-payments'), 'PFCEXPRESS' => tr('PFC Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'CN_POST56' => tr('Post56', 'Name of carrier', 'woocommerce-paypal-payments'), 'HKD' => tr('Qingdao HKD International Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'ETS_EXPRESS' => tr('RETS express', 'Name of carrier', 'woocommerce-paypal-payments'), 'RUSTON' => tr('Ruston', 'Name of carrier', 'woocommerce-paypal-payments'), 'CN_SF_EXPRESS' => tr('SF Express (www.sf-express.com)', 'Name of carrier', 'woocommerce-paypal-payments'), 'SFB2C' => tr('SF International', 'Name of carrier', 'woocommerce-paypal-payments'), 'SFC_LOGISTICS' => tr('SFC', 'Name of carrier', 'woocommerce-paypal-payments'), 'SFCSERVICE' => tr('SFC Service', 'Name of carrier', 'woocommerce-paypal-payments'), 'DAJIN' => tr('Shanghai Aqrum Chemical Logistics Co.Ltd', 'Name of carrier', 'woocommerce-paypal-payments'), 'SINOTRANS' => tr('Sinotrans', 'Name of carrier', 'woocommerce-paypal-payments'), 'STONE3PL' => tr('STONE3PL', 'Name of carrier', 'woocommerce-paypal-payments'), 'SYPOST' => tr('Sunyou Post', 'Name of carrier', 'woocommerce-paypal-payments'), 'TARRIVE' => tr('TONDA GLOBAL', 'Name of carrier', 'woocommerce-paypal-payments'), 'TOPHATTEREXPRESS' => tr('Tophatter Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'TOPYOU' => tr('TopYou', 'Name of carrier', 'woocommerce-paypal-payments'), 'UC_EXPRE' => tr('ucexpress', 'Name of carrier', 'woocommerce-paypal-payments'), 'VIWO' => tr('VIWO IoT', 'Name of carrier', 'woocommerce-paypal-payments'), 'WANBEXPRESS' => tr('WanbExpress', 'Name of carrier', 'woocommerce-paypal-payments'), 'WEASHIP' => tr('Weaship', 'Name of carrier', 'woocommerce-paypal-payments'), 'CN_WEDO' => tr('WeDo Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'WINIT' => tr('WinIt', 'Name of carrier', 'woocommerce-paypal-payments'), 'WISE_EXPRESS' => tr('Wise Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'CN_WISHPOST' => tr('WishPost', 'Name of carrier', 'woocommerce-paypal-payments'), 'XQ_EXPRESS' => tr('XQ Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'YANWEN' => tr('Yanwen Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'YDH_EXPRESS' => tr('YDH express', 'Name of carrier', 'woocommerce-paypal-payments'), 'ELIAN_POST' => tr('Yilian (Elian) Supply Chain', 'Name of carrier', 'woocommerce-paypal-payments'), 'YINGNUO_LOGISTICS' => tr('yingnuo logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'YTO' => tr('YTO Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'CN_YUNDA' => tr('Yunda Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'YUNEXPRESS' => tr('YunExpress', 'Name of carrier', 'woocommerce-paypal-payments'), 'ZJS_EXPRESS' => tr('ZJS International', 'Name of carrier', 'woocommerce-paypal-payments'), 'ZTO_EXPRESS' => tr('ZTO Express', 'Name of carrier', 'woocommerce-paypal-payments'))), 'COL' => array('name' => tr('Colombia', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('COORDINADORA' => tr('Coordinadora', 'Name of carrier', 'woocommerce-paypal-payments'))), 'HRV' => array('name' => tr('Croatia', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('GLS_CROTIA' => tr('GLS Croatia', 'Name of carrier', 'woocommerce-paypal-payments'), 'HRV_HRVATSKA' => tr('Hrvatska posta', 'Name of carrier', 'woocommerce-paypal-payments'), 'OVERSE_EXP' => tr('Overseas Express', 'Name of carrier', 'woocommerce-paypal-payments'))), 'CY' => array('name' => tr('Cyprus', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('CYPRUS_POST_CYP' => tr('Cyprus Post', 'Name of carrier', 'woocommerce-paypal-payments'))), 'CZ' => array('name' => tr('Czech Republic', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('CESKA_CZ' => tr('Ceska Post', 'Name of carrier', 'woocommerce-paypal-payments'), 'GLS_CZ' => tr('GLS Czech Republic', 'Name of carrier', 'woocommerce-paypal-payments'))), 'DNK' => array('name' => tr('Denmark', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('BUDBEE_WEBHOOK' => tr('Budbee courier', 'Name of carrier', 'woocommerce-paypal-payments'), 'DANSKE_FRAGT' => tr('Danske Fragtaend', 'Name of carrier', 'woocommerce-paypal-payments'), 'POSTNORD_LOGISTICS_DK' => tr('ostnord denmark', 'Name of carrier', 'woocommerce-paypal-payments'), 'POSTNORD_LOGISTICS' => tr('PostNord Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'XPRESSEN_DK' => tr('Xpressen courier', 'Name of carrier', 'woocommerce-paypal-payments'))), 'EST' => array('name' => tr('Estonia', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('OMNIVA' => tr('Omniva', 'Name of carrier', 'woocommerce-paypal-payments'))), 'FIN' => array('name' => tr('Finland', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('MATKAHUOLTO' => tr('Matkahuolto', 'Name of carrier', 'woocommerce-paypal-payments'), 'POSTI' => tr('Posti courier', 'Name of carrier', 'woocommerce-paypal-payments'))), 'FR' => array('name' => tr('France', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('CHRONOPOST_FR' => tr('Chronopost france (www.chronopost.fr)', 'Name of carrier', 'woocommerce-paypal-payments'), 'COLIS_PRIVE' => tr('Colis Privé', 'Name of carrier', 'woocommerce-paypal-payments'), 'FR_COLIS' => tr('Colissimo', 'Name of carrier', 'woocommerce-paypal-payments'), 'CUBYN' => tr('Cubyn', 'Name of carrier', 'woocommerce-paypal-payments'), 'DPD_FR' => tr('DPD France', 'Name of carrier', 'woocommerce-paypal-payments'), 'FR_EXAPAQ' => tr('DPD France (formerly exapaq)', 'Name of carrier', 'woocommerce-paypal-payments'), 'GEODIS_ESPACE' => tr('Geodis E-space', 'Name of carrier', 'woocommerce-paypal-payments'), 'HEPPNER_FR' => tr('Heppner France', 'Name of carrier', 'woocommerce-paypal-payments'), 'LA_POSTE_SUIVI' => tr('La Poste', 'Name of carrier', 'woocommerce-paypal-payments'), 'TNT_FR' => tr('TNT France', 'Name of carrier', 'woocommerce-paypal-payments'), 'VIRTRANSPORT' => tr('VIR Transport', 'Name of carrier', 'woocommerce-paypal-payments'))), 'DE' => array('name' => tr('Germany', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('HERMES_DE' => tr('Hermes Germany', 'Name of carrier', 'woocommerce-paypal-payments'), 'AO_DEUTSCHLAND' => tr('AO Deutschland', 'Name of carrier', 'woocommerce-paypal-payments'), 'DE_DPD_DELISTRACK' => tr('DPD Germany', 'Name of carrier', 'woocommerce-paypal-payments'), 'FIEGE' => tr('Fiege Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'GEIS' => tr('Geis CZ', 'Name of carrier', 'woocommerce-paypal-payments'), 'GEL_EXPRESS' => tr('Gel Express Logistik', 'Name of carrier', 'woocommerce-paypal-payments'), 'GENERAL_OVERNIGHT' => tr('Go!Express and logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'HEPPNER' => tr('Heppner Internationale Spedition GmbH & Co.', 'Name of carrier', 'woocommerce-paypal-payments'), 'HERMES_2MANN_HANDLING' => tr('Hermes Einrichtungs Service GmbH & Co. KG', 'Name of carrier', 'woocommerce-paypal-payments'), 'NOX_NACHTEXPRESS' => tr('Innight Express Germany GmbH (nox NachtExpress)', 'Name of carrier', 'woocommerce-paypal-payments'), 'LIEFERY' => tr('liefery', 'Name of carrier', 'woocommerce-paypal-payments'), 'NOX_NIGHT_TIME_EXPRESS' => tr('NOX NightTimeExpress', 'Name of carrier', 'woocommerce-paypal-payments'), 'PARCELONE' => tr('PARCEL ONE', 'Name of carrier', 'woocommerce-paypal-payments'), 'PRESSIODE' => tr('Pressio', 'Name of carrier', 'woocommerce-paypal-payments'), 'RABEN_GROUP' => tr('Raben Group', 'Name of carrier', 'woocommerce-paypal-payments'), 'STRECK_TRANSPORT' => tr('Streck Transport', 'Name of carrier', 'woocommerce-paypal-payments'), 'SWISHIP_DE' => tr('Swiship DE', 'Name of carrier', 'woocommerce-paypal-payments'))), 'GR' => array('name' => tr('Greece', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('ACS_GR' => tr('ACS Courier', 'Name of carrier', 'woocommerce-paypal-payments'), 'EASY_MAIL' => tr('Easy Mail', 'Name of carrier', 'woocommerce-paypal-payments'), 'GENIKI_GR' => tr('Geniki Taxydromiki', 'Name of carrier', 'woocommerce-paypal-payments'), 'SPEEDCOURIERS_GR' => tr('Speed Couriers', 'Name of carrier', 'woocommerce-paypal-payments'), 'SPEEDEXCOURIER' => tr('SPEEDEX couriers', 'Name of carrier', 'woocommerce-paypal-payments'))), 'HK' => array('name' => tr('Hong Kong', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('CFL_LOGISTICS' => tr('CFL Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'CJ_HK_INTERNATIONAL' => tr('CJ Logistics International(Hong Kong)', 'Name of carrier', 'woocommerce-paypal-payments'), 'CLE_LOGISTICS' => tr('CL E-Logistics Solutions Limited', 'Name of carrier', 'woocommerce-paypal-payments'), 'CONTINENTAL' => tr('Continental', 'Name of carrier', 'woocommerce-paypal-payments'), 'COSTMETICSNOW' => tr('Cosmetics Now', 'Name of carrier', 'woocommerce-paypal-payments'), 'DEALERSEND' => tr('DealerSend', 'Name of carrier', 'woocommerce-paypal-payments'), 'DHL_ECOMERCE_ASA' => tr('DHL eCommerce Asia (API)', 'Name of carrier', 'woocommerce-paypal-payments'), 'DHL_GLOBAL_MAIL_ASIA' => tr('DHL Global Mail Asia', 'Name of carrier', 'woocommerce-paypal-payments'), 'DHL_HK' => tr('DHL Hong Kong', 'Name of carrier', 'woocommerce-paypal-payments'), 'DPD_HK' => tr('DPD Hong Kong', 'Name of carrier', 'woocommerce-paypal-payments'), 'DTDC_EXPRESS' => tr('DTDC express', 'Name of carrier', 'woocommerce-paypal-payments'), 'GLOBAVEND' => tr('Globavend', 'Name of carrier', 'woocommerce-paypal-payments'), 'HK_POST' => tr('Hongkong Post', 'Name of carrier', 'woocommerce-paypal-payments'), 'JANCO' => tr('Janco Ecommerce', 'Name of carrier', 'woocommerce-paypal-payments'), 'JS_EXPRESS' => tr('JS EXPRESS', 'Name of carrier', 'woocommerce-paypal-payments'), 'KEC' => tr('KEC courier', 'Name of carrier', 'woocommerce-paypal-payments'), 'KERRY_ECOMMERCE' => tr('Kerry eCommerce', 'Name of carrier', 'woocommerce-paypal-payments'), 'LHT_EXPRESS' => tr('LHT Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'LOGISTICSWORLDWIDE_HK' => tr('Logistic Worldwide Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'MAINWAY' => tr('Mainway', 'Name of carrier', 'woocommerce-paypal-payments'), 'MORNING_EXPRESS' => tr('Morning Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'OKAYPARCEL' => tr('OkayParcel', 'Name of carrier', 'woocommerce-paypal-payments'), 'OMNIPARCEL' => tr('Omni Parcel', 'Name of carrier', 'woocommerce-paypal-payments'), 'PALEXPRESS' => tr('PAL Express Limited', 'Name of carrier', 'woocommerce-paypal-payments'), 'PICKUP' => tr('Pickupp', 'Name of carrier', 'woocommerce-paypal-payments'), 'QUANTIUM' => tr('Quantium', 'Name of carrier', 'woocommerce-paypal-payments'), 'RPX' => tr('RPX Online', 'Name of carrier', 'woocommerce-paypal-payments'), 'SEKOLOGISTICS' => tr('SEKO Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'SHIP_IT_ASIA' => tr('Ship It Asia', 'Name of carrier', 'woocommerce-paypal-payments'), 'SMOOTH' => tr('Smooth Couriers', 'Name of carrier', 'woocommerce-paypal-payments'), 'STEPFORWARDFS' => tr('STEP FORWARD FREIGHT SERVICE CO LTD', 'Name of carrier', 'woocommerce-paypal-payments'), 'SFPLUS_WEBHOOK' => tr('Zeek courier', 'Name of carrier', 'woocommerce-paypal-payments'), 'ZEEK_2_DOOR' => tr('Zeek2Door', 'Name of carrier', 'woocommerce-paypal-payments'))), 'HU' => array('name' => tr('Hungary', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('DPD_HGRY' => tr('DPD Hungary', 'Name of carrier', 'woocommerce-paypal-payments'), 'MAGYAR_HU' => tr('Magyar Post', 'Name of carrier', 'woocommerce-paypal-payments'))), 'IN' => array('name' => tr('India', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('BOMBINOEXP' => tr('Bombino Express Pvt', 'Name of carrier', 'woocommerce-paypal-payments'), 'IND_DELHIVERY' => tr('Delhivery India', 'Name of carrier', 'woocommerce-paypal-payments'), 'DELIVERYONTIME' => tr('DELIVERYONTIME LOGISTICS PVT LTD', 'Name of carrier', 'woocommerce-paypal-payments'), 'DTDC_IN' => tr('DTDC India', 'Name of carrier', 'woocommerce-paypal-payments'), 'IND_ECOM' => tr('Ecom Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'EKART' => tr('Ekart logistics (ekartlogistics.com)', 'Name of carrier', 'woocommerce-paypal-payments'), 'IND_FIRSTFLIGHT' => tr('First Flight Couriers', 'Name of carrier', 'woocommerce-paypal-payments'), 'IND_GATI' => tr('Gati-KWE', 'Name of carrier', 'woocommerce-paypal-payments'), 'IND_GOJAVAS' => tr('GoJavas', 'Name of carrier', 'woocommerce-paypal-payments'), 'HOLISOL' => tr('Holisol', 'Name of carrier', 'woocommerce-paypal-payments'), 'LEXSHIP' => tr('LexShip', 'Name of carrier', 'woocommerce-paypal-payments'), 'OCS' => tr('OCS ANA Group', 'Name of carrier', 'woocommerce-paypal-payments'), 'PARCELLED_IN' => tr('Parcelled.in', 'Name of carrier', 'woocommerce-paypal-payments'), 'PICKRR' => tr('Pickrr', 'Name of carrier', 'woocommerce-paypal-payments'), 'IND_SAFEEXPRESS' => tr('Safexpress', 'Name of carrier', 'woocommerce-paypal-payments'), 'SCUDEX_EXPRESS' => tr('Scudex Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'SHREE_ANJANI_COURIER' => tr('Shree Anjani Courier', 'Name of carrier', 'woocommerce-paypal-payments'), 'SHREE_MARUTI' => tr('Shree Maruti Courier Services Pvt Ltd', 'Name of carrier', 'woocommerce-paypal-payments'), 'SHREENANDANCOURIER' => tr('SHREE NANDAN COURIER', 'Name of carrier', 'woocommerce-paypal-payments'), 'SHREETIRUPATI' => tr('SHREE TIRUPATI COURIER SERVICES PVT. LTD.', 'Name of carrier', 'woocommerce-paypal-payments'), 'SPOTON' => tr('SPOTON Logistics Pvt Ltd', 'Name of carrier', 'woocommerce-paypal-payments'), 'TRACKON' => tr('Trackon Couriers Pvt. Ltd', 'Name of carrier', 'woocommerce-paypal-payments'))), 'ID' => array('name' => tr('Indonesia', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('ALFATREX' => tr('AlfaTrex', 'Name of carrier', 'woocommerce-paypal-payments'), 'CHOIR_EXP' => tr('Choir Express Indonesia', 'Name of carrier', 'woocommerce-paypal-payments'), 'INDOPAKET' => tr('INDOPAKET', 'Name of carrier', 'woocommerce-paypal-payments'), 'JX' => tr('JX courier', 'Name of carrier', 'woocommerce-paypal-payments'), 'KURASI' => tr('KURASI', 'Name of carrier', 'woocommerce-paypal-payments'), 'NINJAVAN_ID' => tr('Ninja Van Indonesia', 'Name of carrier', 'woocommerce-paypal-payments'), 'NINJAVAN_WB' => tr('Ninjavan Webhook', 'Name of carrier', 'woocommerce-paypal-payments'), 'MGLOBAL' => tr('PT MGLOBAL LOGISTICS INDONESIA', 'Name of carrier', 'woocommerce-paypal-payments'), 'PRIMAMULTICIPTA' => tr('PT Prima Multi Cipta', 'Name of carrier', 'woocommerce-paypal-payments'), 'RCL' => tr('Red Carpet Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'RPX_ID' => tr('RPX Indonesia', 'Name of carrier', 'woocommerce-paypal-payments'), 'SAP_EXPRESS' => tr('SAP EXPRESS', 'Name of carrier', 'woocommerce-paypal-payments'), 'SIN_GLBL' => tr('Sin Global Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'TIKI_ID' => tr('Tiki shipment', 'Name of carrier', 'woocommerce-paypal-payments'), 'TRANS_KARGO' => tr('Trans Kargo Internasional', 'Name of carrier', 'woocommerce-paypal-payments'), 'WAHANA_ID' => tr('Wahana express (www.wahana.com)', 'Name of carrier', 'woocommerce-paypal-payments'))), 'IE' => array('name' => tr('Ireland', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('AN_POST' => tr('An Post', 'Name of carrier', 'woocommerce-paypal-payments'), 'DPD_IR' => tr('DPD Ireland', 'Name of carrier', 'woocommerce-paypal-payments'), 'FASTWAY_IR' => tr('Fastway Ireland', 'Name of carrier', 'woocommerce-paypal-payments'), 'WISELOADS' => tr('Wiseloads', 'Name of carrier', 'woocommerce-paypal-payments'))), 'IL' => array('name' => tr('Israel', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('ISRAEL_POST' => tr('Israel Post', 'Name of carrier', 'woocommerce-paypal-payments'), 'ISR_POST_DOMESTIC' => tr('Israel Post Domestic', 'Name of carrier', 'woocommerce-paypal-payments'))), 'IT' => array('name' => tr('Italy', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('BRT_IT_PARCELID' => tr('BRT Bartolini (Parcel ID)', 'Name of carrier', 'woocommerce-paypal-payments'), 'BRT_IT' => tr('BRT Couriers Italy', 'Name of carrier', 'woocommerce-paypal-payments'), 'ARCO_SPEDIZIONI' => tr('Arco Spedizioni SP', 'Name of carrier', 'woocommerce-paypal-payments'), 'BLINKLASTMILE' => tr('Blink', 'Name of carrier', 'woocommerce-paypal-payments'), 'BRT_IT_SENDER_REF' => tr('BRT Bartolini (Sender Reference)', 'Name of carrier', 'woocommerce-paypal-payments'), 'DMM_NETWORK' => tr('DMM Network', 'Name of carrier', 'woocommerce-paypal-payments'), 'GLS_IT' => tr('GLS Italy', 'Name of carrier', 'woocommerce-paypal-payments'), 'HRPARCEL' => tr('HR Parcel', 'Name of carrier', 'woocommerce-paypal-payments'), 'I_DIKA' => tr('i-dika', 'Name of carrier', 'woocommerce-paypal-payments'), 'LICCARDI_EXPRESS' => tr('LICCARDI EXPRESS COURIER', 'Name of carrier', 'woocommerce-paypal-payments'), 'MILKMAN' => tr('Milkman Courier', 'Name of carrier', 'woocommerce-paypal-payments'), 'IT_NEXIVE' => tr('Nexive (TNT Post Italy)', 'Name of carrier', 'woocommerce-paypal-payments'), 'IT_POSTE_ITALIA' => tr('Poste Italiane', 'Name of carrier', 'woocommerce-paypal-payments'), 'SAILPOST' => tr('SAILPOST', 'Name of carrier', 'woocommerce-paypal-payments'), 'SDA_IT' => tr('SDA Italy', 'Name of carrier', 'woocommerce-paypal-payments'), 'TNT_CLICK_IT' => tr('TNT-Click Italy', 'Name of carrier', 'woocommerce-paypal-payments'))), 'JP' => array('name' => tr('Japan', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('EFEX' => tr('eFEx (E-Commerce Fulfillment & Express)', 'Name of carrier', 'woocommerce-paypal-payments'), 'JPN_JAPAN_POST' => tr('Japan Post', 'Name of carrier', 'woocommerce-paypal-payments'), 'KWE_GLOBAL' => tr('KWE Global', 'Name of carrier', 'woocommerce-paypal-payments'), 'MAIL_PLUS' => tr('MailPlus', 'Name of carrier', 'woocommerce-paypal-payments'), 'MAILPLUS_JPN' => tr('MailPlus (Japan)', 'Name of carrier', 'woocommerce-paypal-payments'), 'SEINO' => tr('Seino', 'Name of carrier', 'woocommerce-paypal-payments'))), 'JEY' => array('name' => tr('Jersey', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('JERSEY_POST' => tr('Jersey Post', 'Name of carrier', 'woocommerce-paypal-payments'))), 'KR' => array('name' => tr('Korea', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('CELLO_SQUARE' => tr('Cello Square', 'Name of carrier', 'woocommerce-paypal-payments'), 'CROSHOT' => tr('Croshot', 'Name of carrier', 'woocommerce-paypal-payments'), 'DOORA' => tr('Doora Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'EPARCEL_KR' => tr('eParcel Korea', 'Name of carrier', 'woocommerce-paypal-payments'), 'KPOST' => tr('Korea Post', 'Name of carrier', 'woocommerce-paypal-payments'), 'KR_KOREA_POST' => tr('Koreapost (www.koreapost.go.kr)', 'Name of carrier', 'woocommerce-paypal-payments'), 'KYUNGDONG_PARCEL' => tr('Kyungdong Parcel', 'Name of carrier', 'woocommerce-paypal-payments'), 'LOTTE' => tr('Lotte Global Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'RINCOS' => tr('Rincos', 'Name of carrier', 'woocommerce-paypal-payments'), 'ROCKET_PARCEL' => tr('Rocket Parcel International', 'Name of carrier', 'woocommerce-paypal-payments'), 'SHIP_GATE' => tr('ShipGate', 'Name of carrier', 'woocommerce-paypal-payments'), 'SHIPTER' => tr('SHIPTER', 'Name of carrier', 'woocommerce-paypal-payments'), 'SRE_KOREA' => tr('SRE Korea (www.srekorea.co.kr)', 'Name of carrier', 'woocommerce-paypal-payments'), 'TOLOS' => tr('Tolos courier', 'Name of carrier', 'woocommerce-paypal-payments'))), 'KWT' => array('name' => tr('Kuwait', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('POSTA_PLUS' => tr('Posta Plus', 'Name of carrier', 'woocommerce-paypal-payments'))), 'LAO' => array('name' => tr("Lao People's Democratic Republic (the)", 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('LAO_POST' => tr('Lao Post', 'Name of carrier', 'woocommerce-paypal-payments'))), 'LVA' => array('name' => tr('Latvia', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('CDEK' => tr('CDEK courier', 'Name of carrier', 'woocommerce-paypal-payments'), 'LATVIJAS_PASTS' => tr('Latvijas Pasts', 'Name of carrier', 'woocommerce-paypal-payments'))), 'LT' => array('name' => tr('Lithuania', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('VENIPAK' => tr('Venipak', 'Name of carrier', 'woocommerce-paypal-payments'))), 'MY' => array('name' => tr('Malaysia', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('ABXEXPRESS_MY' => tr('ABX Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'MYS_AIRPAK' => tr('Airpak Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'CITYLINK_MY' => tr('City-Link Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'CJ_CENTURY' => tr('CJ Century', 'Name of carrier', 'woocommerce-paypal-payments'), 'CJ_INT_MY' => tr('CJ International Malaysia', 'Name of carrier', 'woocommerce-paypal-payments'), 'COLLECTCO' => tr('CollectCo', 'Name of carrier', 'woocommerce-paypal-payments'), 'FMX' => tr('FMX', 'Name of carrier', 'woocommerce-paypal-payments'), 'MYS_GDEX' => tr('GDEX Courier', 'Name of carrier', 'woocommerce-paypal-payments'), 'JTEXPRESS' => tr('J&T EXPRESS MALAYSIA', 'Name of carrier', 'woocommerce-paypal-payments'), 'JINSUNG' => tr('JINSUNG TRADING', 'Name of carrier', 'woocommerce-paypal-payments'), 'JOCOM' => tr('Jocom', 'Name of carrier', 'woocommerce-paypal-payments'), 'KANGAROO_MY' => tr('Kangaroo Worldwide Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'LINE' => tr('Line Clear Express & Logistics Sdn Bhd', 'Name of carrier', 'woocommerce-paypal-payments'), 'LOGISTIKA' => tr('Logistika', 'Name of carrier', 'woocommerce-paypal-payments'), 'M_XPRESS' => tr('M Xpress Sdn Bhd', 'Name of carrier', 'woocommerce-paypal-payments'), 'MYS_MYS_POST' => tr('Malaysia Post', 'Name of carrier', 'woocommerce-paypal-payments'), 'MATDESPATCH' => tr('Matdespatch', 'Name of carrier', 'woocommerce-paypal-payments'), 'MYS_MYPOST_ONLINE' => tr('Mypostonline', 'Name of carrier', 'woocommerce-paypal-payments'), 'NATIONWIDE_MY' => tr('Nationwide Express Courier Services Bhd (www.nationwide.com.my)', 'Name of carrier', 'woocommerce-paypal-payments'), 'NINJAVAN_MY' => tr('Ninja Van (www.ninjavan.co)', 'Name of carrier', 'woocommerce-paypal-payments'), 'PICKUPP_MYS' => tr('PICK UPP', 'Name of carrier', 'woocommerce-paypal-payments'), 'MYS_SKYNET' => tr('Skynet Malaysia', 'Name of carrier', 'woocommerce-paypal-payments'), 'TAQBIN_MY' => tr('TAQBIN Malaysia', 'Name of carrier', 'woocommerce-paypal-payments'), 'WEPOST' => tr('WePost Sdn Bhd', 'Name of carrier', 'woocommerce-paypal-payments'), 'WYNGS' => tr('Wyngs', 'Name of carrier', 'woocommerce-paypal-payments'), 'ZEPTO_EXPRESS' => tr('ZeptoExpress', 'Name of carrier', 'woocommerce-paypal-payments'))), 'MX' => array('name' => tr('Mexico', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('CORREOS_DE_MEXICO' => tr('Correos Mexico', 'Name of carrier', 'woocommerce-paypal-payments'), 'MEX_ESTAFETA' => tr('Estafeta (www.estafeta.com)', 'Name of carrier', 'woocommerce-paypal-payments'), 'GRUPO' => tr('Grupo ampm', 'Name of carrier', 'woocommerce-paypal-payments'), 'HOUNDEXPRESS' => tr('Hound Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'IVOY_WEBHOOK' => tr('Ivoy courier', 'Name of carrier', 'woocommerce-paypal-payments'), 'MEX_SENDA' => tr('Mexico Senda Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'PAQUETEXPRESS' => tr('Paquetexpress', 'Name of carrier', 'woocommerce-paypal-payments'), 'MEX_REDPACK' => tr('Redpack', 'Name of carrier', 'woocommerce-paypal-payments'))), 'NL' => array('name' => tr('Netherlands', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('BROUWER_TRANSPORT' => tr('Brouwer Transport en Logistiek', 'Name of carrier', 'woocommerce-paypal-payments'), 'NLD_DHL' => tr('DHL Netherlands', 'Name of carrier', 'woocommerce-paypal-payments'), 'FIEGE_NL' => tr('Fiege Netherlands', 'Name of carrier', 'woocommerce-paypal-payments'), 'NLD_GLS' => tr('GLS Netherlands', 'Name of carrier', 'woocommerce-paypal-payments'), 'HAPPY2POINT' => tr('Happy 2ThePoint', 'Name of carrier', 'woocommerce-paypal-payments'), 'PAPER_EXPRESS' => tr('Paper Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'POSTNL_INTL_3S' => tr('PostNL International 3S', 'Name of carrier', 'woocommerce-paypal-payments'), 'TRUNKRS_WEBHOOK' => tr('Trunkrs courier', 'Name of carrier', 'woocommerce-paypal-payments'))), 'NZ' => array('name' => tr('New Zealand', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('FASTWAY_NZ' => tr('Fastway New Zealand', 'Name of carrier', 'woocommerce-paypal-payments'), 'INTERPARCEL_NZ' => tr('Interparcel New Zealand', 'Name of carrier', 'woocommerce-paypal-payments'), 'MAINFREIGHT' => tr('Mainfreight', 'Name of carrier', 'woocommerce-paypal-payments'), 'NZ_NZ_POST' => tr('New Zealand Post', 'Name of carrier', 'woocommerce-paypal-payments'), 'TOLL_NZ' => tr('Toll New Zealand', 'Name of carrier', 'woocommerce-paypal-payments'))), 'NG' => array('name' => tr('Nigeria', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('NIPOST_NG' => tr('NIpost (www.nipost.gov.ng)', 'Name of carrier', 'woocommerce-paypal-payments'))), 'NO' => array('name' => tr('Norway', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('HELTHJEM' => tr('Helthjem', 'Name of carrier', 'woocommerce-paypal-payments'))), 'PAK' => array('name' => tr('Pakistan', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('FORRUN' => tr('forrun Pvt Ltd (Arpatech Venture)', 'Name of carrier', 'woocommerce-paypal-payments'), 'TCS' => tr('TCS courier	', 'Name of carrier', 'woocommerce-paypal-payments'))), 'PRY' => array('name' => tr('Paraguay', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('AEX' => tr('AEX Group', 'Name of carrier', 'woocommerce-paypal-payments'))), 'PH' => array('name' => tr('Philippines', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('TWO_GO' => tr('2GO Courier', 'Name of carrier', 'woocommerce-paypal-payments'), 'PHL_JAMEXPRESS' => tr('Jam Express Philippines', 'Name of carrier', 'woocommerce-paypal-payments'), 'PIXSELL' => tr('PIXSELL LOGISTICS', 'Name of carrier', 'woocommerce-paypal-payments'), 'RAF_PH' => tr('RAF Philippines', 'Name of carrier', 'woocommerce-paypal-payments'), 'XDE_WEBHOOK' => tr('Ximex Delivery Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'XPOST' => tr('Xpost.ph', 'Name of carrier', 'woocommerce-paypal-payments'))), 'PL' => array('name' => tr('Poland', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('DHL_PL' => tr('DHL Poland', 'Name of carrier', 'woocommerce-paypal-payments'), 'DPD_POLAND' => tr('DPD Poland', 'Name of carrier', 'woocommerce-paypal-payments'), 'FEDEX_POLAND' => tr('FedEx® Poland Domestic', 'Name of carrier', 'woocommerce-paypal-payments'), 'INPOST_PACZKOMATY' => tr('InPost Paczkomaty', 'Name of carrier', 'woocommerce-paypal-payments'), 'PL_POCZTA_POLSKA' => tr('Poczta Polska', 'Name of carrier', 'woocommerce-paypal-payments'), 'ROYAL_MAIL' => tr('Royal Mail', 'Name of carrier', 'woocommerce-paypal-payments'))), 'PT' => array('name' => tr('Portugal', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('ADICIONAL' => tr('Adicional Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'BNEED' => tr('Bneed courier', 'Name of carrier', 'woocommerce-paypal-payments'), 'CARRIERS' => tr('Carriers courier', 'Name of carrier', 'woocommerce-paypal-payments'), 'PRT_CHRONOPOST' => tr('Chronopost Portugal', 'Name of carrier', 'woocommerce-paypal-payments'), 'PRT_CTT' => tr('CTT Portugal', 'Name of carrier', 'woocommerce-paypal-payments'), 'DELNEXT' => tr('Delnext', 'Name of carrier', 'woocommerce-paypal-payments'))), 'RO' => array('name' => tr('Romania', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('DPD_RO' => tr('DPD Romania', 'Name of carrier', 'woocommerce-paypal-payments'), 'POSTA_RO' => tr('Post Roman (www.posta-romana.ro)', 'Name of carrier', 'woocommerce-paypal-payments'))), 'RUS' => array('name' => tr('Russia', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('BOX_BERRY' => tr('Boxberry courier', 'Name of carrier', 'woocommerce-paypal-payments'), 'CSE' => tr('CSE courier', 'Name of carrier', 'woocommerce-paypal-payments'), 'DHL_PARCEL_RU' => tr('DHL Parcel Russia', 'Name of carrier', 'woocommerce-paypal-payments'), 'DOBROPOST' => tr('DobroPost', 'Name of carrier', 'woocommerce-paypal-payments'), 'DPD_RU' => tr('DPD Russia', 'Name of carrier', 'woocommerce-paypal-payments'), 'EXPRESSSALE' => tr('Expresssale', 'Name of carrier', 'woocommerce-paypal-payments'), 'GBS_BROKER' => tr('GBS-Broker', 'Name of carrier', 'woocommerce-paypal-payments'), 'PONY_EXPRESS' => tr('Pony express', 'Name of carrier', 'woocommerce-paypal-payments'), 'SHOPFANS' => tr('ShopfansRU LLC', 'Name of carrier', 'woocommerce-paypal-payments'))), 'SAU' => array('name' => tr('Saudi Arabia', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('SAU_SAUDI_POST' => tr('Saudi Post', 'Name of carrier', 'woocommerce-paypal-payments'), 'SMSA_EXPRESS' => tr('SMSA Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'THABIT_LOGISTICS' => tr('Thabit Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'ZAJIL_EXPRESS' => tr('Zajil Express Company', 'Name of carrier', 'woocommerce-paypal-payments'))), 'SRB' => array('name' => tr('Serbia', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('POST_SERBIA' => tr('Posta Serbia', 'Name of carrier', 'woocommerce-paypal-payments'))), 'SG' => array('name' => tr('Singapore', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('CLOUDWISH_ASIA' => tr('Cloudwish Asia', 'Name of carrier', 'woocommerce-paypal-payments'), 'SG_DETRACK' => tr('Detrack', 'Name of carrier', 'woocommerce-paypal-payments'), 'FONSEN' => tr('Fonsen Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'GRAB_WEBHOOK' => tr('Grab courier', 'Name of carrier', 'woocommerce-paypal-payments'), 'SIMPLYPOST' => tr('J&T Express Singapore', 'Name of carrier', 'woocommerce-paypal-payments'), 'JANIO' => tr('Janio Asia', 'Name of carrier', 'woocommerce-paypal-payments'), 'IND_JAYONEXPRESS' => tr('Jayon Express (JEX)', 'Name of carrier', 'woocommerce-paypal-payments'), 'JET_SHIP' => tr('Jet-Ship Worldwide', 'Name of carrier', 'woocommerce-paypal-payments'), 'KGMHUB' => tr('KGM Hub', 'Name of carrier', 'woocommerce-paypal-payments'), 'LEGION_EXPRESS' => tr('Legion Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'NHANS_SOLUTIONS' => tr('Nhans Solutions', 'Name of carrier', 'woocommerce-paypal-payments'), 'NINJAVAN_SG' => tr('Ninja van Singapore', 'Name of carrier', 'woocommerce-paypal-payments'), 'PARCELPOST_SG' => tr('Parcel Post Singapore', 'Name of carrier', 'woocommerce-paypal-payments'), 'PARKNPARCEL' => tr('Park N Parcel', 'Name of carrier', 'woocommerce-paypal-payments'), 'PICKUPP_SGP' => tr('PICK UPP (Singapore)', 'Name of carrier', 'woocommerce-paypal-payments'), 'SG_QXPRESS' => tr('Qxpress', 'Name of carrier', 'woocommerce-paypal-payments'), 'RAIDEREX' => tr('RaidereX', 'Name of carrier', 'woocommerce-paypal-payments'), 'ROADBULL' => tr('Red Carpet Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'RZYEXPRESS' => tr('RZY Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'SG_SG_POST' => tr('Singapore Post', 'Name of carrier', 'woocommerce-paypal-payments'), 'SG_SPEEDPOST' => tr('Singapore Speedpost', 'Name of carrier', 'woocommerce-paypal-payments'), 'TCK_EXPRESS' => tr('TCK Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'COUREX' => tr('Urbanfox', 'Name of carrier', 'woocommerce-paypal-payments'), 'WMG' => tr('WMG Delivery', 'Name of carrier', 'woocommerce-paypal-payments'), 'ZYLLEM' => tr('Zyllem', 'Name of carrier', 'woocommerce-paypal-payments'))), 'SVK' => array('name' => tr('Slovakia', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('GLS_SLOV' => tr('GLS General Logistics Systems Slovakia s.r.o.', 'Name of carrier', 'woocommerce-paypal-payments'))), 'SVN' => array('name' => tr('Slovenia', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('GLS_SLOVEN' => tr('GLS Slovenia', 'Name of carrier', 'woocommerce-paypal-payments'), 'POST_SLOVENIA' => tr('Post of Slovenia', 'Name of carrier', 'woocommerce-paypal-payments'))), 'ZA' => array('name' => tr('South Africa', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('ZA_COURIERIT' => tr('Courier IT', 'Name of carrier', 'woocommerce-paypal-payments'), 'DAWN_WING' => tr('Dawn Wing', 'Name of carrier', 'woocommerce-paypal-payments'), 'DPE_SOUTH_AFRC' => tr('DPE South Africa', 'Name of carrier', 'woocommerce-paypal-payments'), 'INTEXPRESS' => tr('Internet Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'COLLIVERY' => tr('MDS Collivery Pty (Ltd)', 'Name of carrier', 'woocommerce-paypal-payments'), 'RAM' => tr('RAM courier', 'Name of carrier', 'woocommerce-paypal-payments'), 'SKYNET_ZA' => tr('Skynet World Wide Express South Africa', 'Name of carrier', 'woocommerce-paypal-payments'), 'SOUTH_AFRICAN_POST_OFFICE' => tr('South African Post Office', 'Name of carrier', 'woocommerce-paypal-payments'), 'ZA_SPECIALISED_FREIGHT' => tr('Specialised Freight', 'Name of carrier', 'woocommerce-paypal-payments'), 'THECOURIERGUY' => tr('The Courier Guy', 'Name of carrier', 'woocommerce-paypal-payments'))), 'ES' => array('name' => tr('Spain', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('ABCUSTOM' => tr('AB Custom Group', 'Name of carrier', 'woocommerce-paypal-payments'), 'ADERONLINE' => tr('Ader couriers', 'Name of carrier', 'woocommerce-paypal-payments'), 'ASIGNA' => tr('ASIGNA courier', 'Name of carrier', 'woocommerce-paypal-payments'), 'ESP_ASM' => tr('ASM(GLS Spain)', 'Name of carrier', 'woocommerce-paypal-payments'), 'CBL_LOGISTICA' => tr('CBL Logistica', 'Name of carrier', 'woocommerce-paypal-payments'), 'CORREOS_EXPRESS' => tr('Correos Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'DHL_PARCEL_ES' => tr('DHL parcel Spain(www.dhl.com)', 'Name of carrier', 'woocommerce-paypal-payments'), 'DHL_ES' => tr('DHL Spain(www.dhl.com)', 'Name of carrier', 'woocommerce-paypal-payments'), 'ECOSCOOTING' => tr('ECOSCOOTING', 'Name of carrier', 'woocommerce-paypal-payments'), 'ESP_ENVIALIA' => tr('Envialia', 'Name of carrier', 'woocommerce-paypal-payments'), 'ENVIALIA_REFERENCE' => tr('Envialia Reference', 'Name of carrier', 'woocommerce-paypal-payments'), 'INTEGRA2_FTP' => tr('Integra2', 'Name of carrier', 'woocommerce-paypal-payments'), 'MRW_FTP' => tr('MRW courier', 'Name of carrier', 'woocommerce-paypal-payments'), 'ESP_MRW' => tr('MRW spain', 'Name of carrier', 'woocommerce-paypal-payments'), 'NACEX' => tr('NACEX', 'Name of carrier', 'woocommerce-paypal-payments'), 'NACEX_ES' => tr('NACEX Spain', 'Name of carrier', 'woocommerce-paypal-payments'), 'ESP_NACEX' => tr('NACEX Spain', 'Name of carrier', 'woocommerce-paypal-payments'), 'PAACK_WEBHOOK' => tr('Paack courier', 'Name of carrier', 'woocommerce-paypal-payments'), 'ESP_PACKLINK' => tr('Packlink', 'Name of carrier', 'woocommerce-paypal-payments'), 'ESP_REDUR' => tr('Redur Spain', 'Name of carrier', 'woocommerce-paypal-payments'), 'PRT_INT_SEUR' => tr('SEUR International', 'Name of carrier', 'woocommerce-paypal-payments'), 'PRT_SEUR' => tr('SEUR portugal', 'Name of carrier', 'woocommerce-paypal-payments'), 'SEUR_ES' => tr('Seur Spain', 'Name of carrier', 'woocommerce-paypal-payments'), 'SEUR_SP_API' => tr('Spanish Seur API', 'Name of carrier', 'woocommerce-paypal-payments'), 'SPRING_GDS' => tr('Spring GDS', 'Name of carrier', 'woocommerce-paypal-payments'), 'SZENDEX' => tr('SZENDEX', 'Name of carrier', 'woocommerce-paypal-payments'), 'TNT_NL' => tr('THT Netherland', 'Name of carrier', 'woocommerce-paypal-payments'), 'TIPSA' => tr('TIPSA courier', 'Name of carrier', 'woocommerce-paypal-payments'), 'TNT' => tr('TNT Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'GLOBAL_TNT' => tr('TNT global', 'Name of carrier', 'woocommerce-paypal-payments'), 'TOURLINE' => tr('tourline', 'Name of carrier', 'woocommerce-paypal-payments'), 'VAMOX' => tr('VAMOX', 'Name of carrier', 'woocommerce-paypal-payments'), 'VIA_EXPRESS' => tr('Viaxpress', 'Name of carrier', 'woocommerce-paypal-payments'), 'ZELERIS' => tr('Zeleris', 'Name of carrier', 'woocommerce-paypal-payments'))), 'SE' => array('name' => tr('Sweden', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('AIRMEE_WEBHOOK' => tr('Airmee couriers', 'Name of carrier', 'woocommerce-paypal-payments'), 'BRING' => tr('Bring', 'Name of carrier', 'woocommerce-paypal-payments'), 'DBSCHENKER_SE' => tr('DB Schenker (www.dbschenker.com)', 'Name of carrier', 'woocommerce-paypal-payments'), 'DBSCHENKER_SV' => tr('DB Schenker Sweden', 'Name of carrier', 'woocommerce-paypal-payments'))), 'CH' => array('name' => tr('Switzerland', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('ASENDIA_HK' => tr('Asendia HonKong', 'Name of carrier', 'woocommerce-paypal-payments'), 'PLANZER' => tr('Planzer Group', 'Name of carrier', 'woocommerce-paypal-payments'), 'SWISS_POST_FTP' => tr('Swiss Post FTP', 'Name of carrier', 'woocommerce-paypal-payments'), 'VIAEUROPE' => tr('ViaEurope', 'Name of carrier', 'woocommerce-paypal-payments'))), 'TW' => array('name' => tr('Taiwan', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('CNWANGTONG' => tr('cnwangtong', 'Name of carrier', 'woocommerce-paypal-payments'), 'CTC_EXPRESS' => tr('CTC Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'DIMERCO' => tr('Dimerco Express Group', 'Name of carrier', 'woocommerce-paypal-payments'), 'HCT_LOGISTICS' => tr('HCT LOGISTICS CO.LTD.', 'Name of carrier', 'woocommerce-paypal-payments'), 'KERRYTJ' => tr('Kerry TJ Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'PRESIDENT_TRANS' => tr('PRESIDENT TRANSNET CORP', 'Name of carrier', 'woocommerce-paypal-payments'), 'GLOBAL_EXPRESS' => tr('Tai Wan Global Business', 'Name of carrier', 'woocommerce-paypal-payments'))), 'TH' => array('name' => tr('Thailand', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('ALPHAFAST' => tr('Alphafast', 'Name of carrier', 'woocommerce-paypal-payments'), 'CJ_KR' => tr('CJ Korea Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'THA_DYNAMIC_LOGISTICS' => tr('Dynamic Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'FASTRK_SERV' => tr('Fastrak Services', 'Name of carrier', 'woocommerce-paypal-payments'), 'FLASHEXPRESS' => tr('Flash Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'NIM_EXPRESS' => tr('Nim Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'NINJAVAN_THAI' => tr('Ninja van Thai', 'Name of carrier', 'woocommerce-paypal-payments'), 'SENDIT' => tr('Sendit', 'Name of carrier', 'woocommerce-paypal-payments'), 'SKYBOX' => tr('SKYBOX', 'Name of carrier', 'woocommerce-paypal-payments'), 'THA_THAILAND_POST' => tr('Thailand Post', 'Name of carrier', 'woocommerce-paypal-payments'))), 'TR' => array('name' => tr('Turkey', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('ASE' => tr('ASE KARGO', 'Name of carrier', 'woocommerce-paypal-payments'), 'CDEK_TR' => tr('CDEK TR', 'Name of carrier', 'woocommerce-paypal-payments'), 'PTS' => tr('PTS courier', 'Name of carrier', 'woocommerce-paypal-payments'), 'PTT_POST' => tr('PTT Post', 'Name of carrier', 'woocommerce-paypal-payments'), 'SHIPENTEGRA' => tr('ShipEntegra', 'Name of carrier', 'woocommerce-paypal-payments'), 'YURTICI_KARGO' => tr('Yurtici Kargo', 'Name of carrier', 'woocommerce-paypal-payments'))), 'UA' => array('name' => tr('Ukraine', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('NOVA_POSHTA_INT' => tr('Nova Poshta (International)', 'Name of carrier', 'woocommerce-paypal-payments'), 'NOVA_POSHTA' => tr('Nova Poshta (novaposhta.ua)', 'Name of carrier', 'woocommerce-paypal-payments'), 'POSTA_UKR' => tr('UkrPoshta', 'Name of carrier', 'woocommerce-paypal-payments'))), 'AE' => array('name' => tr('United Arab Emirates', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('IBEONE' => tr('Beone Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'MARA_XPRESS' => tr('Mara Xpress', 'Name of carrier', 'woocommerce-paypal-payments'), 'FETCHR_WEBHOOK' => tr('Mena 360 (Fetchr)', 'Name of carrier', 'woocommerce-paypal-payments'), 'ONECLICK' => tr('One click delivery services', 'Name of carrier', 'woocommerce-paypal-payments'), 'SKYNET_UAE' => tr('SKYNET UAE', 'Name of carrier', 'woocommerce-paypal-payments'))), 'GB' => array('name' => tr('United Kingdom', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('AMAZON' => tr('Amazon Shipping', 'Name of carrier', 'woocommerce-paypal-payments'), 'AO_COURIER' => tr('AO Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'APC_OVERNIGHT' => tr('APC overnight (apc-overnight.com)', 'Name of carrier', 'woocommerce-paypal-payments'), 'APC_OVERNIGHT_CONNUM' => tr('APC Overnight Consignment', 'Name of carrier', 'woocommerce-paypal-payments'), 'APG' => tr('APG eCommerce Solutions', 'Name of carrier', 'woocommerce-paypal-payments'), 'ARK_LOGISTICS' => tr('ARK Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'GB_ARROW' => tr('Arrow XL', 'Name of carrier', 'woocommerce-paypal-payments'), 'ASENDIA_UK' => tr('Asendia UK', 'Name of carrier', 'woocommerce-paypal-payments'), 'BH_WORLDWIDE' => tr('B&H Worldwide', 'Name of carrier', 'woocommerce-paypal-payments'), 'BIRDSYSTEM' => tr('BirdSystem', 'Name of carrier', 'woocommerce-paypal-payments'), 'BLUECARE' => tr('Bluecare Express Ltd', 'Name of carrier', 'woocommerce-paypal-payments'), 'CAE_DELIVERS' => tr('CAE Delivers', 'Name of carrier', 'woocommerce-paypal-payments'), 'CARIBOU' => tr('Caribou', 'Name of carrier', 'woocommerce-paypal-payments'), 'DAIGLOBALTRACK' => tr('DAI Post', 'Name of carrier', 'woocommerce-paypal-payments'), 'DELTEC_UK' => tr('Deltec Courier', 'Name of carrier', 'woocommerce-paypal-payments'), 'DHL_REFR' => tr('DHl (Reference number)', 'Name of carrier', 'woocommerce-paypal-payments'), 'DHL_UK' => tr('dhl UK', 'Name of carrier', 'woocommerce-paypal-payments'), 'DIAMOND_EUROGISTICS' => tr('Diamond Eurogistics Limited', 'Name of carrier', 'woocommerce-paypal-payments'), 'DIRECTPARCELS' => tr('Direct Parcels', 'Name of carrier', 'woocommerce-paypal-payments'), 'DMS_MATRIX' => tr('DMSMatrix', 'Name of carrier', 'woocommerce-paypal-payments'), 'DPD_LOCAL' => tr('DPD Local', 'Name of carrier', 'woocommerce-paypal-payments'), 'DPD_LOCAL_REF' => tr('DPD Local reference', 'Name of carrier', 'woocommerce-paypal-payments'), 'DX_SFTP' => tr('DX (SFTP)', 'Name of carrier', 'woocommerce-paypal-payments'), 'EU_FLEET_SOLUTIONS' => tr('EU Fleet Solutions', 'Name of carrier', 'woocommerce-paypal-payments'), 'FEDEX_UK' => tr('FedEx® UK', 'Name of carrier', 'woocommerce-paypal-payments'), 'FURDECO' => tr('Furdeco', 'Name of carrier', 'woocommerce-paypal-payments'), 'GBA' => tr('GBA Services Ltd', 'Name of carrier', 'woocommerce-paypal-payments'), 'GEMWORLDWIDE' => tr('GEM Worldwide', 'Name of carrier', 'woocommerce-paypal-payments'), 'HERMES' => tr('HermesWorld UK', 'Name of carrier', 'woocommerce-paypal-payments'), 'HOME_DELIVERY_SOLUTIONS' => tr('Home Delivery Solutions Ltd', 'Name of carrier', 'woocommerce-paypal-payments'), 'INTERPARCEL_UK' => tr('Interparcel UK', 'Name of carrier', 'woocommerce-paypal-payments'), 'MYHERMES' => tr('MyHermes UK', 'Name of carrier', 'woocommerce-paypal-payments'), 'NATIONAL_SAMEDAY' => tr('National Sameday', 'Name of carrier', 'woocommerce-paypal-payments'), 'GB_NORSK' => tr('Norsk Global', 'Name of carrier', 'woocommerce-paypal-payments'), 'OCS_WORLDWIDE' => tr('OCS WORLDWIDE', 'Name of carrier', 'woocommerce-paypal-payments'), 'PALLETWAYS' => tr('Palletways', 'Name of carrier', 'woocommerce-paypal-payments'), 'GB_PANTHER' => tr('Panther', 'Name of carrier', 'woocommerce-paypal-payments'), 'PANTHER_REFERENCE' => tr('Panther Reference', 'Name of carrier', 'woocommerce-paypal-payments'), 'PARCEL2GO' => tr('Parcel2Go', 'Name of carrier', 'woocommerce-paypal-payments'), 'PARCELINKLOGISTICS' => tr('Parcelink Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'PLUS_LOG_UK' => tr('Plus UK Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'RPD2MAN' => tr('RPD2man Deliveries', 'Name of carrier', 'woocommerce-paypal-payments'), 'SKYNET_UK' => tr('Skynet UK', 'Name of carrier', 'woocommerce-paypal-payments'), 'AMAZON_FBA_SWISHIP' => tr('Swiship UK', 'Name of carrier', 'woocommerce-paypal-payments'), 'THEDELIVERYGROUP' => tr('TDG – The Delivery Group', 'Name of carrier', 'woocommerce-paypal-payments'), 'PALLET_NETWORK' => tr('The Pallet Network', 'Name of carrier', 'woocommerce-paypal-payments'), 'TNT_UK' => tr('TNT UK Limited (www.tnt.com)', 'Name of carrier', 'woocommerce-paypal-payments'), 'TNT_UK_REFR' => tr('TNT UK Reference', 'Name of carrier', 'woocommerce-paypal-payments'), 'GB_TUFFNELLS' => tr('Tuffnells Parcels Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'TUFFNELLS_REFERENCE' => tr('Tuffnells Parcels Express- Reference', 'Name of carrier', 'woocommerce-paypal-payments'), 'UK_UK_MAIL' => tr('UK mail (ukmail.com)', 'Name of carrier', 'woocommerce-paypal-payments'), 'WHISTL' => tr('Whistl', 'Name of carrier', 'woocommerce-paypal-payments'), 'WNDIRECT' => tr('wnDirect', 'Name of carrier', 'woocommerce-paypal-payments'), 'UK_XDP' => tr('XDP Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'XDP_UK_REFERENCE' => tr('XDP Express Reference', 'Name of carrier', 'woocommerce-paypal-payments'), 'XPERT_DELIVERY' => tr('Xpert Delivery', 'Name of carrier', 'woocommerce-paypal-payments'), 'UK_YODEL' => tr('Yodel (www.yodel.co.uk)', 'Name of carrier', 'woocommerce-paypal-payments'))), 'US' => array('name' => tr('United States', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('GIO_EXPRESS' => tr('Gio Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'GLOBALTRANZ' => tr('GlobalTranz', 'Name of carrier', 'woocommerce-paypal-payments'), 'GSI_EXPRESS' => tr('GSI EXPRESS', 'Name of carrier', 'woocommerce-paypal-payments'), 'GSO' => tr('GSO (GLS-USA)', 'Name of carrier', 'woocommerce-paypal-payments'), 'HIPSHIPPER' => tr('Hipshipper', 'Name of carrier', 'woocommerce-paypal-payments'), 'GLOBAL_IPARCEL' => tr('i-parcel', 'Name of carrier', 'woocommerce-paypal-payments'), 'DESCARTES' => tr('Innovel courier', 'Name of carrier', 'woocommerce-paypal-payments'), 'US_LASERSHIP' => tr('LaserShip', 'Name of carrier', 'woocommerce-paypal-payments'), 'LONESTAR' => tr('Lone Star Overnight', 'Name of carrier', 'woocommerce-paypal-payments'), 'MAILAMERICAS' => tr('MailAmericas', 'Name of carrier', 'woocommerce-paypal-payments'), 'NEWEGGEXPRESS' => tr('Newegg Express', 'Name of carrier', 'woocommerce-paypal-payments'), 'US_OLD_DOMINION' => tr('Old Dominion Freight Line', 'Name of carrier', 'woocommerce-paypal-payments'), 'OSM_WORLDWIDE' => tr('OSM Worldwide', 'Name of carrier', 'woocommerce-paypal-payments'), 'PCFCORP' => tr('PCF Final Mile', 'Name of carrier', 'woocommerce-paypal-payments'), 'PILOT_FREIGHT' => tr('Pilot Freight Services', 'Name of carrier', 'woocommerce-paypal-payments'), 'PITNEY_BOWES' => tr('Pitney Bowes', 'Name of carrier', 'woocommerce-paypal-payments'), 'PITTOHIO' => tr('PITT OHIO', 'Name of carrier', 'woocommerce-paypal-payments'), 'QWINTRY' => tr('Qwintry Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'RL_US' => tr('RL Carriers', 'Name of carrier', 'woocommerce-paypal-payments'), 'SAIA_FREIGHT' => tr('Saia LTL Freight', 'Name of carrier', 'woocommerce-paypal-payments'), 'SHIPTOR' => tr('Shiptor', 'Name of carrier', 'woocommerce-paypal-payments'), 'SONICTL' => tr('Sonic Transportation & Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'SEFL' => tr('Southeastern Freight Lines', 'Name of carrier', 'woocommerce-paypal-payments'), 'SPEEDEE' => tr('Spee-Dee Delivery', 'Name of carrier', 'woocommerce-paypal-payments'), 'SUTTON' => tr('Sutton Transport', 'Name of carrier', 'woocommerce-paypal-payments'), 'TAZMANIAN_FREIGHT' => tr('Tazmanian Freight Systems', 'Name of carrier', 'woocommerce-paypal-payments'), 'TFORCE_FINALMILE' => tr('TForce Final Mile', 'Name of carrier', 'woocommerce-paypal-payments'), 'LOGISTYX_TRANSGROUP' => tr('Transgroup courier', 'Name of carrier', 'woocommerce-paypal-payments'), 'TRUMPCARD' => tr('TRUMPCARD LLC', 'Name of carrier', 'woocommerce-paypal-payments'), 'USPS' => tr('United States Postal Service', 'Name of carrier', 'woocommerce-paypal-payments'), 'UPS_MAIL_INNOVATIONS' => tr('UPS Mail Innovations', 'Name of carrier', 'woocommerce-paypal-payments'), 'USF_REDDAWAY' => tr('USF Reddaway', 'Name of carrier', 'woocommerce-paypal-payments'), 'USHIP' => tr('uShip courier', 'Name of carrier', 'woocommerce-paypal-payments'), 'WESTBANK_COURIER' => tr('West Bank Courier', 'Name of carrier', 'woocommerce-paypal-payments'), 'WESTGATE_GL' => tr('Westgate Global', 'Name of carrier', 'woocommerce-paypal-payments'), 'WIZMO' => tr('Wizmo', 'Name of carrier', 'woocommerce-paypal-payments'), 'XPO_LOGISTICS' => tr('XPO logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'YAKIT' => tr('Yakit courier', 'Name of carrier', 'woocommerce-paypal-payments'), 'US_YRC' => tr('YRC courier', 'Name of carrier', 'woocommerce-paypal-payments'), 'ZINC' => tr('Zinc courier', 'Name of carrier', 'woocommerce-paypal-payments'))), 'URY' => array('name' => tr('Uruguay', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('CORREO_UY' => tr('Correo Uruguayo', 'Name of carrier', 'woocommerce-paypal-payments'))), 'VN' => array('name' => tr('Vietnam', 'Name of carrier country', 'woocommerce-paypal-payments'), 'items' => array('JTEXPRESS_VN' => tr('J&T Express Vietnam', 'Name of carrier', 'woocommerce-paypal-payments'), 'KERRYTTC_VN' => tr('Kerry Express (Vietnam) Co Ltd', 'Name of carrier', 'woocommerce-paypal-payments'), 'NTLOGISTICS_VN' => tr('Nhat Tin Logistics', 'Name of carrier', 'woocommerce-paypal-payments'), 'VNM_VIETNAM_POST' => tr('Vietnam Post', 'Name of carrier', 'woocommerce-paypal-payments'), 'VNM_VIETTELPOST' => tr('ViettelPost', 'Name of carrier', 'woocommerce-paypal-payments')))));
