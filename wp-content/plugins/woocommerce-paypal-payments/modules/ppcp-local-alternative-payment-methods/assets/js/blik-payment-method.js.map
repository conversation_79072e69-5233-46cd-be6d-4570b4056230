{"version": 3, "file": "js/blik-payment-method.js", "mappings": "mBAAA,MAAM,EAA+BA,OAAW,GAAoB,iBCA7D,SAASC,EAAGC,GAA2B,IAAvBC,EAAMD,EAANC,OAGnB,OAHqCD,EAAVE,WACtBC,mBAGDC,MAAAC,cAAA,WACID,MAAAC,cAAA,OACIC,UAAU,kGACVF,MAAAC,cAAA,OACIC,UAAS,oFAAAC,OAAsFN,EAAOO,IACtGC,IAAKR,EAAOS,KACZC,IAAKV,EAAOW,SAKhC,CCZA,IAAMX,EAASY,GAAGC,WAAWC,WAAY,mBAEzCC,EAAAA,EAAAA,uBAAuB,CACtBC,KAAMhB,EAAOO,GACbU,MAAOd,MAAAC,cAAA,OAAKc,wBAA0B,CAAEC,OAAQnB,EAAOW,SACvDS,QAASjB,MAAAC,cAACN,EAAG,CAACE,OAASA,IACvBqB,KAAMlB,MAAAC,cAAA,YACNkB,UAAWtB,EAAOW,MAClBY,eAAgB,WACf,OAAO,CACR,EACAC,SAAU,CACTC,SAAUzB,EAAOwB,W", "sources": ["webpack://ppcp-local-alternative-payment-methods/external window [\"wc\",\"wcBlocksRegistry\"]", "webpack://ppcp-local-alternative-payment-methods/./resources/js/apm-block.js", "webpack://ppcp-local-alternative-payment-methods/./resources/js/blik-payment-method.js"], "sourcesContent": ["const __WEBPACK_NAMESPACE_OBJECT__ = window[\"wc\"][\"wcBlocksRegistry\"];", "export function APM( { config, components } ) {\n\tconst { PaymentMethodIcons } = components;\n\n    return (\n        <div>\n            <div\n                className=\"wc-block-components-payment-method-icons wc-block-components-payment-method-icons--align-right\">\n                <img\n                    className={`wc-block-components-payment-method-icon wc-block-components-payment-method-icon--${config.id}`}\n                    src={config.icon}\n                    alt={config.title}\n                />\n            </div>\n        </div>\n    );\n}\n", "import { registerPaymentMethod } from '@woocommerce/blocks-registry';\nimport { APM } from './apm-block';\n\nconst config = wc.wcSettings.getSetting( 'ppcp-blik_data' );\n\nregisterPaymentMethod( {\n\tname: config.id,\n\tlabel: <div dangerouslySetInnerHTML={ { __html: config.title } } />,\n\tcontent: <APM config={ config } />,\n\tedit: <div></div>,\n\tariaLabel: config.title,\n\tcanMakePayment: () => {\n\t\treturn true;\n\t},\n\tsupports: {\n\t\tfeatures: config.supports,\n\t},\n} );\n"], "names": ["window", "APM", "_ref", "config", "components", "PaymentMethodIcons", "React", "createElement", "className", "concat", "id", "src", "icon", "alt", "title", "wc", "wcSettings", "getSetting", "registerPaymentMethod", "name", "label", "dangerouslySetInnerHTML", "__html", "content", "edit", "aria<PERSON><PERSON><PERSON>", "canMakePayment", "supports", "features"], "sourceRoot": ""}