document.addEventListener("DOMContentLoaded",(function(){var e=PayPalCommerceGatewayOrderTrackingCompat,n="#ppcp_order-tracking .ppcp-tracking-column.shipments",t=document.getElementById("order-shipments-save"),o=location.href+" "+n+">*",c=e.gzd_sync_enabled,i=e.wc_shipment_sync_enabled,u=e.wc_shipping_tax_sync_enabled,r=document.querySelector("#woocommerce-shipment-tracking .button-save-form"),d=document.getElementById("dhl-label-button"),a=function(){var e=document.querySelector(".ppcp-tracking-loader");e&&("none"===e.style.display||""===e.style.display?e.style.display="block":e.style.display="none")},s=function(e){"none"!==e.css("display")?setTimeout((function(){return s(e)}),100):jQuery(n).load(o,"",(function(){a()}))},l=function(e){document.body.contains(e)?setTimeout((function(){return l(e)}),100):jQuery(n).load(o,"",(function(){a()}))};c&&void 0!==t&&null!=t&&t.addEventListener("click",(function(e){a(),s(jQuery("#order-shipments-save"))})),i&&void 0!==r&&null!=r&&r.addEventListener("click",(function(e){a(),s(jQuery("#shipment-tracking-form"))})),void 0!==d&&null!=d&&d.addEventListener("click",(function(e){a(),l(d)})),jQuery(document).on("mouseover mouseout","#dhl_delete_label",(function(e){jQuery("#ppcp-shipment-status").val("CANCELLED").trigger("change"),document.querySelector(".update_shipment").click()})),u&&void 0!==u&&document.addEventListener("click",(function(e){e.target.closest(".components-modal__screen-overlay .label-purchase-modal__sidebar .purchase-section button.components-button")&&(a(),setTimeout((function(){jQuery(n).load(o,"",(function(){a()}))}),1e4))}))}));
//# sourceMappingURL=tracking-compat.js.map