{"version": 3, "file": "js/tracking-compat.js", "mappings": "AAAAA,SAASC,iBAAkB,oBAAoB,WAC9C,IAAMC,EAASC,yCAGTC,EACL,uDACKC,EAAgBL,SAASM,eAAgB,wBACzCC,EACLC,SAASC,KAAO,IAAML,EAAiC,KAClDM,EAAiBR,EAAOS,iBACxBC,EAAwBV,EAAOW,yBAC/BC,EAA2BZ,EAAOa,6BAClCC,EAAuBhB,SAASiB,cACrC,oDAIKC,EACLlB,SAASM,eAAgB,oBAEpBa,EAAyB,WAC9B,IAAMC,EAASpB,SAASiB,cAAe,yBAClCG,IAEsB,SAAzBA,EAAOC,MAAMC,SACY,KAAzBF,EAAOC,MAAMC,QAEbF,EAAOC,MAAMC,QAAU,QAEvBF,EAAOC,MAAMC,QAAU,OAG1B,EAEMC,EAAwB,SAAWC,GACC,SAApCA,EAAeC,IAAK,WACxBC,YAAY,kBAAMH,EAAuBC,EAAgB,GAAE,KAE3DG,OAAQvB,GAAiCwB,KACxCrB,EACA,IACA,WACCY,GACD,GAGH,EAEMU,EAAuB,SAAWC,GAClC9B,SAAS+B,KAAKC,SAAUF,GAC5BJ,YAAY,kBAAMG,EAAsBC,EAAQ,GAAE,KAElDH,OAAQvB,GAAiCwB,KACxCrB,EACA,IACA,WACCY,GACD,GAGH,EAGCT,QACyB,IAAlBL,GACU,MAAjBA,GAEAA,EAAcJ,iBAAkB,SAAS,SAAWgC,GACnDd,IACAI,EAAuBI,OAAQ,yBAChC,IAIAf,QACgC,IAAzBI,GACiB,MAAxBA,GAEAA,EAAqBf,iBAAkB,SAAS,SAAWgC,GAC1Dd,IACAI,EAAuBI,OAAQ,2BAChC,SAIkC,IAA3BT,GACmB,MAA1BA,GAEAA,EAAuBjB,iBAAkB,SAAS,SAAWgC,GAC5Dd,IACAU,EAAsBX,EACvB,IAGDS,OAAQ3B,UAAWkC,GAClB,qBACA,qBACA,SAAWD,GACVN,OAAQ,yBACNQ,IAAK,aACLC,QAAS,UACXpC,SAASiB,cAAe,oBAAqBoB,OAC9C,IAIAvB,QACoC,IAA7BA,GAEPd,SAASC,iBAAkB,SAAS,SAAWgC,GACVA,EAAMK,OAAOC,QA9FlD,iHAmGEpB,IACAO,YAAY,WACXC,OAAQvB,GAAiCwB,KACxCrB,EACA,IACA,WACCY,GACD,GAEF,GAAG,KAEL,GAEF", "sources": ["webpack://ppcp-compat/./resources/js/tracking-compat.js"], "sourcesContent": ["document.addEventListener( 'DOMContentLoaded', () => {\n\tconst config = PayPalCommerceGatewayOrderTrackingCompat;\n\n\tconst orderTrackingContainerId = 'ppcp_order-tracking';\n\tconst orderTrackingContainerSelector =\n\t\t'#ppcp_order-tracking .ppcp-tracking-column.shipments';\n\tconst gzdSaveButton = document.getElementById( 'order-shipments-save' );\n\tconst loadLocation =\n\t\tlocation.href + ' ' + orderTrackingContainerSelector + '>*';\n\tconst gzdSyncEnabled = config.gzd_sync_enabled;\n\tconst wcShipmentSyncEnabled = config.wc_shipment_sync_enabled;\n\tconst wcShippingTaxSyncEnabled = config.wc_shipping_tax_sync_enabled;\n\tconst wcShipmentSaveButton = document.querySelector(\n\t\t'#woocommerce-shipment-tracking .button-save-form'\n\t);\n\tconst wcShipmentTaxBuyLabelButtonSelector =\n\t\t'.components-modal__screen-overlay .label-purchase-modal__sidebar .purchase-section button.components-button';\n\tconst dhlGenerateLabelButton =\n\t\tdocument.getElementById( 'dhl-label-button' );\n\n\tconst toggleLoaderVisibility = function () {\n\t\tconst loader = document.querySelector( '.ppcp-tracking-loader' );\n\t\tif ( loader ) {\n\t\t\tif (\n\t\t\t\tloader.style.display === 'none' ||\n\t\t\t\tloader.style.display === ''\n\t\t\t) {\n\t\t\t\tloader.style.display = 'block';\n\t\t\t} else {\n\t\t\t\tloader.style.display = 'none';\n\t\t\t}\n\t\t}\n\t};\n\n\tconst waitForTrackingUpdate = function ( elementToCheck ) {\n\t\tif ( elementToCheck.css( 'display' ) !== 'none' ) {\n\t\t\tsetTimeout( () => waitForTrackingUpdate( elementToCheck ), 100 );\n\t\t} else {\n\t\t\tjQuery( orderTrackingContainerSelector ).load(\n\t\t\t\tloadLocation,\n\t\t\t\t'',\n\t\t\t\tfunction () {\n\t\t\t\t\ttoggleLoaderVisibility();\n\t\t\t\t}\n\t\t\t);\n\t\t}\n\t};\n\n\tconst waitForButtonRemoval = function ( button ) {\n\t\tif ( document.body.contains( button ) ) {\n\t\t\tsetTimeout( () => waitForButtonRemoval( button ), 100 );\n\t\t} else {\n\t\t\tjQuery( orderTrackingContainerSelector ).load(\n\t\t\t\tloadLocation,\n\t\t\t\t'',\n\t\t\t\tfunction () {\n\t\t\t\t\ttoggleLoaderVisibility();\n\t\t\t\t}\n\t\t\t);\n\t\t}\n\t};\n\n\tif (\n\t\tgzdSyncEnabled &&\n\t\ttypeof gzdSaveButton !== 'undefined' &&\n\t\tgzdSaveButton != null\n\t) {\n\t\tgzdSaveButton.addEventListener( 'click', function ( event ) {\n\t\t\ttoggleLoaderVisibility();\n\t\t\twaitForTrackingUpdate( jQuery( '#order-shipments-save' ) );\n\t\t} );\n\t}\n\n\tif (\n\t\twcShipmentSyncEnabled &&\n\t\ttypeof wcShipmentSaveButton !== 'undefined' &&\n\t\twcShipmentSaveButton != null\n\t) {\n\t\twcShipmentSaveButton.addEventListener( 'click', function ( event ) {\n\t\t\ttoggleLoaderVisibility();\n\t\t\twaitForTrackingUpdate( jQuery( '#shipment-tracking-form' ) );\n\t\t} );\n\t}\n\n\tif (\n\t\ttypeof dhlGenerateLabelButton !== 'undefined' &&\n\t\tdhlGenerateLabelButton != null\n\t) {\n\t\tdhlGenerateLabelButton.addEventListener( 'click', function ( event ) {\n\t\t\ttoggleLoaderVisibility();\n\t\t\twaitForButtonRemoval( dhlGenerateLabelButton );\n\t\t} );\n\t}\n\n\tjQuery( document ).on(\n\t\t'mouseover mouseout',\n\t\t'#dhl_delete_label',\n\t\tfunction ( event ) {\n\t\t\tjQuery( '#ppcp-shipment-status' )\n\t\t\t\t.val( 'CANCELLED' )\n\t\t\t\t.trigger( 'change' );\n\t\t\tdocument.querySelector( '.update_shipment' ).click();\n\t\t}\n\t);\n\n\tif (\n\t\twcShippingTaxSyncEnabled &&\n\t\ttypeof wcShippingTaxSyncEnabled !== 'undefined'\n\t) {\n\t\tdocument.addEventListener( 'click', function ( event ) {\n\t\t\tconst wcShipmentTaxBuyLabelButton = event.target.closest(\n\t\t\t\twcShipmentTaxBuyLabelButtonSelector\n\t\t\t);\n\n\t\t\tif ( wcShipmentTaxBuyLabelButton ) {\n\t\t\t\ttoggleLoaderVisibility();\n\t\t\t\tsetTimeout( function () {\n\t\t\t\t\tjQuery( orderTrackingContainerSelector ).load(\n\t\t\t\t\t\tloadLocation,\n\t\t\t\t\t\t'',\n\t\t\t\t\t\tfunction () {\n\t\t\t\t\t\t\ttoggleLoaderVisibility();\n\t\t\t\t\t\t}\n\t\t\t\t\t);\n\t\t\t\t}, 10000 );\n\t\t\t}\n\t\t} );\n\t}\n} );\n"], "names": ["document", "addEventListener", "config", "PayPalCommerceGatewayOrderTrackingCompat", "orderTrackingContainerSelector", "gzdSaveButton", "getElementById", "loadLocation", "location", "href", "gzdSyncEnabled", "gzd_sync_enabled", "wcShipmentSyncEnabled", "wc_shipment_sync_enabled", "wcShippingTaxSyncEnabled", "wc_shipping_tax_sync_enabled", "wcShipmentSaveButton", "querySelector", "dhlGenerateLabelButton", "toggleLoaderVisibility", "loader", "style", "display", "waitForTrackingUpdate", "elementToCheck", "css", "setTimeout", "j<PERSON><PERSON><PERSON>", "load", "waitForButtonRemoval", "button", "body", "contains", "event", "on", "val", "trigger", "click", "target", "closest"], "sourceRoot": ""}