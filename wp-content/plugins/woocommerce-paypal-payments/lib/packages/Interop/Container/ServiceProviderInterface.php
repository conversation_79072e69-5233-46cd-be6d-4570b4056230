<?php

namespace WooCommerce\PayPalCommerce\Vendor\Interop\Container;

/**
 * A service provider provides entries to a container.
 */
interface ServiceProviderInterface
{
    /**
     * Returns a list of all container entries registered by this service provider.
     *
     * - the key is the entry name
     * - the value is a callable that will return the entry, aka the **factory**
     *
     * Factories have the following signature:
     *        function(\WooCommerce\PayPalCommerce\Vendor\Psr\Container\ContainerInterface $container)
     *
     * @return callable[]
     */
    public function getFactories();
    /**
     * Returns a list of all container entries extended by this service provider.
     *
     * - the key is the entry name
     * - the value is a callable that will return the modified entry
     *
     * Callables have the following signature:
     *        function(WooCommerce\PayPalCommerce\Vendor\Psr\Container\ContainerInterface $container, $previous)
     *     or function(WooCommerce\PayPalCommerce\Vendor\Psr\Container\ContainerInterface $container, $previous = null)
     *
     * About factories parameters:
     *
     * - the container (instance of `WooCommerce\PayPalCommerce\Vendor\Psr\Container\ContainerInterface`)
     * - the entry to be extended. If the entry to be extended does not exist and the parameter is nullable, `null` will be passed.
     *
     * @return callable[]
     */
    public function getExtensions();
}
