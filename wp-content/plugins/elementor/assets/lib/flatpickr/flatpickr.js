/* flatpickr v4.1.4, @license MIT */
(function (global, factory) {
	typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :
		typeof define === 'function' && define.amd ? define(factory) :
			(global.flatpickr = factory());
}(this, (function () { 'use strict';

	/*! *****************************************************************************
	Copyright (c) Microsoft Corporation. All rights reserved.
	Licensed under the Apache License, Version 2.0 (the "License"); you may not use
	this file except in compliance with the License. You may obtain a copy of the
	License at http://www.apache.org/licenses/LICENSE-2.0

	THIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
	KIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED
	WARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,
	MERCHANTABLITY OR NON-INFRINGEMENT.

	See the Apache Version 2.0 License for specific language governing permissions
	and limitations under the License.
	***************************************************************************** */
	/* global Reflect, Promise */



	var __assign = Object.assign || function __assign(t) {
		for (var s, i = 1, n = arguments.length; i < n; i++) {
			s = arguments[i];
			for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
		}
		return t;
	};

	function compareDates(date1, date2, timeless) {
		if (timeless !== false) {
			return (new Date(date1.getTime()).setHours(0, 0, 0, 0) -
				new Date(date2.getTime()).setHours(0, 0, 0, 0));
		}
		return date1.getTime() - date2.getTime();
	}
	var monthToStr = function (monthNumber, shorthand, locale) { return locale.months[shorthand ? "shorthand" : "longhand"][monthNumber]; };
	var getWeek = function (givenDate) {
		var onejan = new Date(givenDate.getFullYear(), 0, 1);
		return Math.ceil(((givenDate.getTime() - onejan.getTime()) / 86400000 +
			onejan.getDay() +
			1) /
			7);
	};
	var duration = {
		DAY: 86400000,
	};

	var defaults = {
		_disable: [],
		_enable: [],
		allowInput: false,
		altFormat: "F j, Y",
		altInput: false,
		altInputClass: "form-control input",
		animate: typeof window === "object" &&
		window.navigator.userAgent.indexOf("MSIE") === -1,
		ariaDateFormat: "F j, Y",
		clickOpens: true,
		closeOnSelect: true,
		conjunction: ", ",
		dateFormat: "Y-m-d",
		defaultHour: 12,
		defaultMinute: 0,
		defaultSeconds: 0,
		disable: [],
		disableMobile: false,
		enable: [],
		enableSeconds: false,
		enableTime: false,
		errorHandler: console.warn,
		getWeek: getWeek,
		hourIncrement: 1,
		ignoredFocusElements: [],
		inline: false,
		locale: "default",
		minuteIncrement: 5,
		mode: "single",
		nextArrow: "<svg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' viewBox='0 0 17 17'><g></g><path d='M13.207 8.472l-7.854 7.854-0.707-0.707 7.146-7.146-7.146-7.148 0.707-0.707 7.854 7.854z' /></svg>",
		noCalendar: false,
		onChange: [],
		onClose: [],
		onDayCreate: [],
		onDestroy: [],
		onKeyDown: [],
		onMonthChange: [],
		onOpen: [],
		onParseConfig: [],
		onReady: [],
		onValueUpdate: [],
		onYearChange: [],
		plugins: [],
		position: "auto",
		positionElement: undefined,
		prevArrow: "<svg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' viewBox='0 0 17 17'><g></g><path d='M5.207 8.471l7.146 7.147-0.707 0.707-7.853-7.854 7.854-7.853 0.707 0.707-7.147 7.146z' /></svg>",
		shorthandCurrentMonth: false,
		static: false,
		time_24hr: false,
		weekNumbers: false,
		wrap: false,
	};

	var english = {
		weekdays: {
			shorthand: ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"],
			longhand: [
				"Sunday",
				"Monday",
				"Tuesday",
				"Wednesday",
				"Thursday",
				"Friday",
				"Saturday",
			],
		},
		months: {
			shorthand: [
				"Jan",
				"Feb",
				"Mar",
				"Apr",
				"May",
				"Jun",
				"Jul",
				"Aug",
				"Sep",
				"Oct",
				"Nov",
				"Dec",
			],
			longhand: [
				"January",
				"February",
				"March",
				"April",
				"May",
				"June",
				"July",
				"August",
				"September",
				"October",
				"November",
				"December",
			],
		},
		daysInMonth: [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31],
		firstDayOfWeek: 0,
		ordinal: function (nth) {
			var s = nth % 100;
			if (s > 3 && s < 21)
				return "th";
			switch (s % 10) {
				case 1:
					return "st";
				case 2:
					return "nd";
				case 3:
					return "rd";
				default:
					return "th";
			}
		},
		rangeSeparator: " to ",
		weekAbbreviation: "Wk",
		scrollTitle: "Scroll to increment",
		toggleTitle: "Click to toggle",
		amPM: ["AM", "PM"],
	};

	var pad = function (number) { return ("0" + number).slice(-2); };
	var int = function (bool) { return (bool === true ? 1 : 0); };
	function debounce(func, wait, immediate) {
		if (immediate === void 0) { immediate = false; }
		var timeout;
		return function () {
			var context = this, args = arguments;
			timeout !== null && clearTimeout(timeout);
			timeout = window.setTimeout(function () {
				timeout = null;
				if (!immediate)
					func.apply(context, args);
			}, wait);
			if (immediate && !timeout)
				func.apply(context, args);
		};
	}
	var arrayify = function (obj) {
		return obj instanceof Array ? obj : [obj];
	};
	function mouseDelta(e) {
		var delta = e.wheelDelta || -e.deltaY;
		return delta >= 0 ? 1 : -1;
	}

	function toggleClass(elem, className, bool) {
		if (bool === true)
			return elem.classList.add(className);
		elem.classList.remove(className);
	}
	function createElement(tag, className, content) {
		var e = window.document.createElement(tag);
		className = className || "";
		content = content || "";
		e.className = className;
		if (content !== undefined)
			e.textContent = content;
		return e;
	}
	function clearNode(node) {
		while (node.firstChild)
			node.removeChild(node.firstChild);
	}
	function findParent(node, condition) {
		if (condition(node))
			return node;
		else if (node.parentNode)
			return findParent(node.parentNode, condition);
		return undefined;
	}
	function createNumberInput(inputClassName) {
		var wrapper = createElement("div", "numInputWrapper"), numInput = createElement("input", "numInput " + inputClassName), arrowUp = createElement("span", "arrowUp"), arrowDown = createElement("span", "arrowDown");
		numInput.type = "text";
		numInput.pattern = "\\d*";
		wrapper.appendChild(numInput);
		wrapper.appendChild(arrowUp);
		wrapper.appendChild(arrowDown);
		return wrapper;
	}

	var do_nothing = function () { return undefined; };
	var revFormat = {
		D: do_nothing,
		F: function (dateObj, monthName, locale) {
			dateObj.setMonth(locale.months.longhand.indexOf(monthName));
		},
		G: function (dateObj, hour) {
			dateObj.setHours(parseFloat(hour));
		},
		H: function (dateObj, hour) {
			dateObj.setHours(parseFloat(hour));
		},
		J: function (dateObj, day) {
			dateObj.setDate(parseFloat(day));
		},
		K: function (dateObj, amPM, locale) {
			dateObj.setHours(dateObj.getHours() % 12 +
				12 * int(new RegExp(locale.amPM[1], "i").test(amPM)));
		},
		M: function (dateObj, shortMonth, locale) {
			dateObj.setMonth(locale.months.shorthand.indexOf(shortMonth));
		},
		S: function (dateObj, seconds) {
			dateObj.setSeconds(parseFloat(seconds));
		},
		U: function (_, unixSeconds) { return new Date(parseFloat(unixSeconds) * 1000); },
		W: function (dateObj, weekNum) {
			var weekNumber = parseInt(weekNum);
			return new Date(dateObj.getFullYear(), 0, 2 + (weekNumber - 1) * 7, 0, 0, 0, 0);
		},
		Y: function (dateObj, year) {
			dateObj.setFullYear(parseFloat(year));
		},
		Z: function (_, ISODate) { return new Date(ISODate); },
		d: function (dateObj, day) {
			dateObj.setDate(parseFloat(day));
		},
		h: function (dateObj, hour) {
			dateObj.setHours(parseFloat(hour));
		},
		i: function (dateObj, minutes) {
			dateObj.setMinutes(parseFloat(minutes));
		},
		j: function (dateObj, day) {
			dateObj.setDate(parseFloat(day));
		},
		l: do_nothing,
		m: function (dateObj, month) {
			dateObj.setMonth(parseFloat(month) - 1);
		},
		n: function (dateObj, month) {
			dateObj.setMonth(parseFloat(month) - 1);
		},
		s: function (dateObj, seconds) {
			dateObj.setSeconds(parseFloat(seconds));
		},
		w: do_nothing,
		y: function (dateObj, year) {
			dateObj.setFullYear(2000 + parseFloat(year));
		},
	};
	var tokenRegex = {
		D: "(\\w+)",
		F: "(\\w+)",
		G: "(\\d\\d|\\d)",
		H: "(\\d\\d|\\d)",
		J: "(\\d\\d|\\d)\\w+",
		K: "",
		M: "(\\w+)",
		S: "(\\d\\d|\\d)",
		U: "(.+)",
		W: "(\\d\\d|\\d)",
		Y: "(\\d{4})",
		Z: "(.+)",
		d: "(\\d\\d|\\d)",
		h: "(\\d\\d|\\d)",
		i: "(\\d\\d|\\d)",
		j: "(\\d\\d|\\d)",
		l: "(\\w+)",
		m: "(\\d\\d|\\d)",
		n: "(\\d\\d|\\d)",
		s: "(\\d\\d|\\d)",
		w: "(\\d\\d|\\d)",
		y: "(\\d{2})",
	};
	var formats = {
		Z: function (date) { return date.toISOString(); },
		D: function (date, locale, options) {
			return locale.weekdays.shorthand[formats.w(date, locale, options)];
		},
		F: function (date, locale, options) {
			return monthToStr(formats.n(date, locale, options) - 1, false, locale);
		},
		G: function (date, locale, options) {
			return pad(formats.h(date, locale, options));
		},
		H: function (date) { return pad(date.getHours()); },
		J: function (date, locale) {
			return locale.ordinal !== undefined
				? date.getDate() + locale.ordinal(date.getDate())
				: date.getDate();
		},
		K: function (date, locale) { return locale.amPM[int(date.getHours() > 11)]; },
		M: function (date, locale) {
			return monthToStr(date.getMonth(), true, locale);
		},
		S: function (date) { return pad(date.getSeconds()); },
		U: function (date) { return date.getTime() / 1000; },
		W: function (date, _, options) {
			return options.getWeek(date);
		},
		Y: function (date) { return date.getFullYear(); },
		d: function (date) { return pad(date.getDate()); },
		h: function (date) { return (date.getHours() % 12 ? date.getHours() % 12 : 12); },
		i: function (date) { return pad(date.getMinutes()); },
		j: function (date) { return date.getDate(); },
		l: function (date, locale) {
			return locale.weekdays.longhand[date.getDay()];
		},
		m: function (date) { return pad(date.getMonth() + 1); },
		n: function (date) { return date.getMonth() + 1; },
		s: function (date) { return date.getSeconds(); },
		w: function (date) { return date.getDay(); },
		y: function (date) { return String(date.getFullYear()).substring(2); },
	};

	if (typeof Object.assign !== "function") {
		Object.assign = function (target) {
			var args = [];
			for (var _i = 1; _i < arguments.length; _i++) {
				args[_i - 1] = arguments[_i];
			}
			if (!target) {
				throw TypeError("Cannot convert undefined or null to object");
			}
			var _loop_1 = function (source) {
				if (source) {
					Object.keys(source).forEach(function (key) { return (target[key] = source[key]); });
				}
			};
			for (var _a = 0, args_1 = args; _a < args_1.length; _a++) {
				var source = args_1[_a];
				_loop_1(source);
			}
			return target;
		};
	}

	function FlatpickrInstance(element, instanceConfig) {
		var self = {};
		self.parseDate = parseDate;
		self.formatDate = formatDate;
		self._animationLoop = [];
		self._handlers = [];
		self._bind = bind;
		self._setHoursFromDate = setHoursFromDate;
		self.changeMonth = changeMonth;
		self.changeYear = changeYear;
		self.clear = clear;
		self.close = close;
		self._createElement = createElement;
		self.destroy = destroy;
		self.isEnabled = isEnabled;
		self.jumpToDate = jumpToDate;
		self.open = open;
		self.redraw = redraw;
		self.set = set;
		self.setDate = setDate;
		self.toggle = toggle;
		function setupHelperFunctions() {
			self.utils = {
				getDaysInMonth: function (month, yr) {
					if (month === void 0) { month = self.currentMonth; }
					if (yr === void 0) { yr = self.currentYear; }
					if (month === 1 && ((yr % 4 === 0 && yr % 100 !== 0) || yr % 400 === 0))
						return 29;
					return self.l10n.daysInMonth[month];
				},
			};
		}
		function init() {
			self.element = self.input = element;
			self.isOpen = false;
			parseConfig();
			setupLocale();
			setupInputs();
			setupDates();
			setupHelperFunctions();
			if (!self.isMobile)
				build();
			bindEvents();
			if (self.selectedDates.length || self.config.noCalendar) {
				if (self.config.enableTime) {
					setHoursFromDate(self.config.noCalendar
						? self.latestSelectedDateObj || self.config.minDate
						: undefined);
				}
				updateValue(false);
			}
			self.showTimeInput =
				self.selectedDates.length > 0 || self.config.noCalendar;
			if (self.weekWrapper !== undefined && self.daysContainer !== undefined) {
				self.calendarContainer.style.width =
					self.daysContainer.offsetWidth + self.weekWrapper.offsetWidth + "px";
			}
			if (!self.isMobile)
				positionCalendar();
			triggerEvent("onReady");
		}
		function bindToInstance(fn) {
			return fn.bind(self);
		}
		function updateTime(e) {
			if (self.config.noCalendar && self.selectedDates.length === 0) {
				var minDate = self.config.minDate;
				self.setDate(new Date().setHours(!minDate ? self.config.defaultHour : minDate.getHours(), !minDate ? self.config.defaultMinute : minDate.getMinutes(), !minDate || !self.config.enableSeconds
					? self.config.defaultSeconds
					: minDate.getSeconds()), false);
				setHoursFromInputs();
				updateValue();
			}
			timeWrapper(e);
			if (self.selectedDates.length === 0)
				return;
			if (!self.minDateHasTime ||
				e.type !== "input" ||
				e.target.value.length >= 2) {
				setHoursFromInputs();
				updateValue();
			}
			else {
				setTimeout(function () {
					setHoursFromInputs();
					updateValue();
				}, 1000);
			}
		}
		function ampm2military(hour, amPM) {
			return hour % 12 + 12 * int(amPM === self.l10n.amPM[1]);
		}
		function military2ampm(hour) {
			switch (hour % 24) {
				case 0:
				case 12:
					return 12;
				default:
					return hour % 12;
			}
		}
		function setHoursFromInputs() {
			if (self.hourElement === undefined || self.minuteElement === undefined)
				return;
			var hours = (parseInt(self.hourElement.value.slice(-2), 10) || 0) % 24, minutes = (parseInt(self.minuteElement.value, 10) || 0) % 60, seconds = self.secondElement !== undefined
				? (parseInt(self.secondElement.value, 10) || 0) % 60
				: 0;
			if (self.amPM !== undefined)
				hours = ampm2military(hours, self.amPM.textContent);
			if (self.config.minDate &&
				self.minDateHasTime &&
				self.latestSelectedDateObj &&
				compareDates(self.latestSelectedDateObj, self.config.minDate) === 0) {
				hours = Math.max(hours, self.config.minDate.getHours());
				if (hours === self.config.minDate.getHours())
					minutes = Math.max(minutes, self.config.minDate.getMinutes());
			}
			if (self.config.maxDate &&
				self.maxDateHasTime &&
				self.latestSelectedDateObj &&
				compareDates(self.latestSelectedDateObj, self.config.maxDate) === 0) {
				hours = Math.min(hours, self.config.maxDate.getHours());
				if (hours === self.config.maxDate.getHours())
					minutes = Math.min(minutes, self.config.maxDate.getMinutes());
			}
			setHours(hours, minutes, seconds);
		}
		function setHoursFromDate(dateObj) {
			var date = dateObj || self.latestSelectedDateObj;
			if (date)
				setHours(date.getHours(), date.getMinutes(), date.getSeconds());
		}
		function setHours(hours, minutes, seconds) {
			if (self.latestSelectedDateObj !== undefined) {
				self.latestSelectedDateObj.setHours(hours % 24, minutes, seconds || 0, 0);
			}
			if (!self.hourElement || !self.minuteElement || self.isMobile)
				return;
			self.hourElement.value = pad(!self.config.time_24hr
				? (12 + hours) % 12 + 12 * int(hours % 12 === 0)
				: hours);
			self.minuteElement.value = pad(minutes);
			if (self.amPM !== undefined)
				self.amPM.textContent = self.l10n.amPM[int(hours >= 12)];
			if (self.secondElement !== undefined)
				self.secondElement.value = pad(seconds);
		}
		function onYearInput(event) {
			var year = parseInt(event.target.value) + (event.delta || 0);
			if (year.toString().length === 4 || event.key === "Enter") {
				self.currentYearElement.blur();
				if (!/[^\d]/.test(year.toString()))
					changeYear(year);
			}
		}
		function bind(element, event, handler) {
			if (event instanceof Array)
				return event.forEach(function (ev) { return bind(element, ev, handler); });
			if (element instanceof Array)
				return element.forEach(function (el) { return bind(el, event, handler); });
			element.addEventListener(event, handler);
			self._handlers.push({ element: element, event: event, handler: handler });
		}
		function onClick(handler) {
			return function (evt) {
				evt.which === 1 && handler(evt);
			};
		}
		function triggerChange() {
			triggerEvent("onChange");
		}
		function bindEvents() {
			if (self.config.wrap) {
				["open", "close", "toggle", "clear"].forEach(function (evt) {
					Array.prototype.forEach.call(self.element.querySelectorAll("[data-" + evt + "]"), function (el) {
						return bind(el, "click", self[evt]);
					});
				});
			}
			if (self.isMobile) {
				setupMobile();
				return;
			}
			var debouncedResize = debounce(onResize, 50);
			self._debouncedChange = debounce(triggerChange, 300);
			if (self.config.mode === "range" &&
				self.daysContainer &&
				!/iPhone|iPad|iPod/i.test(navigator.userAgent))
				bind(self.daysContainer, "mouseover", function (e) {
					return onMouseOver(e.target);
				});
			bind(window.document.body, "keydown", onKeyDown);
			if (!self.config.static)
				bind(self._input, "keydown", onKeyDown);
			if (!self.config.inline && !self.config.static)
				bind(window, "resize", debouncedResize);
			if (window.ontouchstart !== undefined)
				bind(window.document.body, "touchstart", documentClick);
			bind(window.document.body, "mousedown", onClick(documentClick));
			bind(self._input, "blur", documentClick);
			if (self.config.clickOpens === true) {
				bind(self._input, "focus", self.open);
				bind(self._input, "mousedown", onClick(self.open));
			}
			if (self.daysContainer !== undefined) {
				self.monthNav.addEventListener("wheel", function (e) { return e.preventDefault(); });
				bind(self.monthNav, "wheel", debounce(onMonthNavScroll, 10));
				bind(self.monthNav, "mousedown", onClick(onMonthNavClick));
				bind(self.monthNav, ["keyup", "increment"], onYearInput);
				bind(self.daysContainer, "mousedown", onClick(selectDate));
				if (self.config.animate) {
					bind(self.daysContainer, ["webkitAnimationEnd", "animationend"], animateDays);
					bind(self.monthNav, ["webkitAnimationEnd", "animationend"], animateMonths);
				}
			}
			if (self.timeContainer !== undefined &&
				self.minuteElement !== undefined &&
				self.hourElement !== undefined) {
				var selText = function (e) {
					return e.target.select();
				};
				bind(self.timeContainer, ["wheel", "input", "increment"], updateTime);
				bind(self.timeContainer, "mousedown", onClick(timeIncrement));
				bind(self.timeContainer, ["wheel", "increment"], self._debouncedChange);
				bind(self.timeContainer, "input", triggerChange);
				bind([self.hourElement, self.minuteElement], ["focus", "click"], selText);
				if (self.secondElement !== undefined)
					bind(self.secondElement, "focus", function () { return self.secondElement && self.secondElement.select(); });
				if (self.amPM !== undefined) {
					bind(self.amPM, "mousedown", onClick(function (e) {
						updateTime(e);
						triggerChange();
					}));
				}
			}
		}
		function processPostDayAnimation() {
			self._animationLoop.forEach(function (f) { return f(); });
			self._animationLoop = [];
		}
		function animateDays(e) {
			if (self.daysContainer && self.daysContainer.childNodes.length > 1) {
				switch (e.animationName) {
					case "fpSlideLeft":
						self.daysContainer.lastChild &&
						self.daysContainer.lastChild.classList.remove("slideLeftNew");
						self.daysContainer.removeChild(self.daysContainer
							.firstChild);
						self.days = self.daysContainer.firstChild;
						processPostDayAnimation();
						break;
					case "fpSlideRight":
						self.daysContainer.firstChild &&
						self.daysContainer.firstChild.classList.remove("slideRightNew");
						self.daysContainer.removeChild(self.daysContainer
							.lastChild);
						self.days = self.daysContainer.firstChild;
						processPostDayAnimation();
						break;
					default:
						break;
				}
			}
		}
		function animateMonths(e) {
			switch (e.animationName) {
				case "fpSlideLeftNew":
				case "fpSlideRightNew":
					self.navigationCurrentMonth.classList.remove("slideLeftNew");
					self.navigationCurrentMonth.classList.remove("slideRightNew");
					var nav = self.navigationCurrentMonth;
					while (nav.nextSibling &&
					/curr/.test(nav.nextSibling.className))
						self.monthNav.removeChild(nav.nextSibling);
					while (nav.previousSibling &&
					/curr/.test(nav.previousSibling.className))
						self.monthNav.removeChild(nav.previousSibling);
					self.oldCurMonth = undefined;
					break;
			}
		}
		function jumpToDate(jumpDate) {
			var jumpTo = jumpDate !== undefined
				? parseDate(jumpDate)
				: self.latestSelectedDateObj ||
				(self.config.minDate && self.config.minDate > self.now
					? self.config.minDate
					: self.config.maxDate && self.config.maxDate < self.now
						? self.config.maxDate
						: self.now);
			try {
				if (jumpTo !== undefined) {
					self.currentYear = jumpTo.getFullYear();
					self.currentMonth = jumpTo.getMonth();
				}
			}
			catch (e) {
				e.message = "Invalid date supplied: " + jumpTo;
				self.config.errorHandler(e);
			}
			self.redraw();
		}
		function timeIncrement(e) {
			if (~e.target.className.indexOf("arrow"))
				incrementNumInput(e, e.target.classList.contains("arrowUp") ? 1 : -1);
		}
		function incrementNumInput(e, delta, inputElem) {
			var target = e && e.target;
			var input = inputElem ||
				(target && target.parentNode && target.parentNode.firstChild);
			var event = createEvent("increment");
			event.delta = delta;
			input && input.dispatchEvent(event);
		}
		function build() {
			var fragment = window.document.createDocumentFragment();
			self.calendarContainer = createElement("div", "flatpickr-calendar");
			self.calendarContainer.tabIndex = -1;
			if (!self.config.noCalendar) {
				fragment.appendChild(buildMonthNav());
				self.innerContainer = createElement("div", "flatpickr-innerContainer");
				if (self.config.weekNumbers) {
					var _a = buildWeeks(), weekWrapper = _a.weekWrapper, weekNumbers = _a.weekNumbers;
					self.innerContainer.appendChild(weekWrapper);
					self.weekNumbers = weekNumbers;
					self.weekWrapper = weekWrapper;
				}
				self.rContainer = createElement("div", "flatpickr-rContainer");
				self.rContainer.appendChild(buildWeekdays());
				if (!self.daysContainer) {
					self.daysContainer = createElement("div", "flatpickr-days");
					self.daysContainer.tabIndex = -1;
				}
				buildDays();
				self.rContainer.appendChild(self.daysContainer);
				self.innerContainer.appendChild(self.rContainer);
				fragment.appendChild(self.innerContainer);
			}
			if (self.config.enableTime) {
				fragment.appendChild(buildTime());
			}
			toggleClass(self.calendarContainer, "rangeMode", self.config.mode === "range");
			toggleClass(self.calendarContainer, "animate", self.config.animate);
			self.calendarContainer.appendChild(fragment);
			var customAppend = self.config.appendTo !== undefined && self.config.appendTo.nodeType;
			if (self.config.inline || self.config.static) {
				self.calendarContainer.classList.add(self.config.inline ? "inline" : "static");
				if (self.config.inline) {
					if (!customAppend && self.element.parentNode)
						self.element.parentNode.insertBefore(self.calendarContainer, self._input.nextSibling);
					else if (self.config.appendTo !== undefined)
						self.config.appendTo.appendChild(self.calendarContainer);
				}
				if (self.config.static) {
					var wrapper = createElement("div", "flatpickr-wrapper");
					if (self.element.parentNode)
						self.element.parentNode.insertBefore(wrapper, self.element);
					wrapper.appendChild(self.element);
					if (self.altInput)
						wrapper.appendChild(self.altInput);
					wrapper.appendChild(self.calendarContainer);
				}
			}
			if (!self.config.static && !self.config.inline)
				(self.config.appendTo !== undefined
					? self.config.appendTo
					: window.document.body).appendChild(self.calendarContainer);
		}
		function createDay(className, date, dayNumber, i) {
			var dateIsEnabled = isEnabled(date, true), dayElement = createElement("span", "flatpickr-day " + className, date.getDate().toString());
			dayElement.dateObj = date;
			dayElement.$i = i;
			dayElement.setAttribute("aria-label", self.formatDate(date, self.config.ariaDateFormat));
			if (compareDates(date, self.now) === 0) {
				self.todayDateElem = dayElement;
				dayElement.classList.add("today");
			}
			if (dateIsEnabled) {
				dayElement.tabIndex = -1;
				if (isDateSelected(date)) {
					dayElement.classList.add("selected");
					self.selectedDateElem = dayElement;
					if (self.config.mode === "range") {
						toggleClass(dayElement, "startRange", self.selectedDates[0] &&
							compareDates(date, self.selectedDates[0]) === 0);
						toggleClass(dayElement, "endRange", self.selectedDates[1] &&
							compareDates(date, self.selectedDates[1]) === 0);
					}
				}
			}
			else {
				dayElement.classList.add("disabled");
				if (self.selectedDates[0] &&
					self.minRangeDate &&
					date > self.minRangeDate &&
					date < self.selectedDates[0])
					self.minRangeDate = date;
				else if (self.selectedDates[0] &&
					self.maxRangeDate &&
					date < self.maxRangeDate &&
					date > self.selectedDates[0])
					self.maxRangeDate = date;
			}
			if (self.config.mode === "range") {
				if (isDateInRange(date) && !isDateSelected(date))
					dayElement.classList.add("inRange");
				if (self.selectedDates.length === 1 &&
					self.minRangeDate !== undefined &&
					self.maxRangeDate !== undefined &&
					(date < self.minRangeDate || date > self.maxRangeDate))
					dayElement.classList.add("notAllowed");
			}
			if (self.weekNumbers &&
				className !== "prevMonthDay" &&
				dayNumber % 7 === 1) {
				self.weekNumbers.insertAdjacentHTML("beforeend", "<span class='disabled flatpickr-day'>" +
					self.config.getWeek(date) +
					"</span>");
			}
			triggerEvent("onDayCreate", dayElement);
			return dayElement;
		}
		function focusOnDay(currentIndex, offset) {
			var newIndex = currentIndex + offset || 0, targetNode = (currentIndex !== undefined
				? self.days.childNodes[newIndex]
				: self.selectedDateElem ||
				self.todayDateElem ||
				self.days.childNodes[0]);
			var focus = function () {
				targetNode = targetNode || self.days.childNodes[newIndex];
				targetNode.focus();
				if (self.config.mode === "range")
					onMouseOver(targetNode);
			};
			if (targetNode === undefined && offset !== 0) {
				if (offset > 0) {
					self.changeMonth(1, true, undefined, true);
					newIndex = newIndex % 42;
				}
				else if (offset < 0) {
					self.changeMonth(-1, true, undefined, true);
					newIndex += 42;
				}
				return afterDayAnim(focus);
			}
			focus();
		}
		function afterDayAnim(fn) {
			self.config.animate === true ? self._animationLoop.push(fn) : fn();
		}
		function buildDays(delta) {
			if (self.daysContainer === undefined) {
				return;
			}
			var firstOfMonth = (new Date(self.currentYear, self.currentMonth, 1).getDay() -
				self.l10n.firstDayOfWeek +
				7) %
				7, isRangeMode = self.config.mode === "range";
			var prevMonthDays = self.utils.getDaysInMonth((self.currentMonth - 1 + 12) % 12);
			var daysInMonth = self.utils.getDaysInMonth(), days = window.document.createDocumentFragment();
			var dayNumber = prevMonthDays + 1 - firstOfMonth, dayIndex = 0;
			if (self.weekNumbers && self.weekNumbers.firstChild)
				self.weekNumbers.textContent = "";
			if (isRangeMode) {
				self.minRangeDate = new Date(self.currentYear, self.currentMonth - 1, dayNumber);
				self.maxRangeDate = new Date(self.currentYear, self.currentMonth + 1, (42 - firstOfMonth) % daysInMonth);
			}
			for (; dayNumber <= prevMonthDays; dayNumber++, dayIndex++) {
				days.appendChild(createDay("prevMonthDay", new Date(self.currentYear, self.currentMonth - 1, dayNumber), dayNumber, dayIndex));
			}
			for (dayNumber = 1; dayNumber <= daysInMonth; dayNumber++, dayIndex++) {
				days.appendChild(createDay("", new Date(self.currentYear, self.currentMonth, dayNumber), dayNumber, dayIndex));
			}
			for (var dayNum = daysInMonth + 1; dayNum <= 42 - firstOfMonth; dayNum++, dayIndex++) {
				days.appendChild(createDay("nextMonthDay", new Date(self.currentYear, self.currentMonth + 1, dayNum % daysInMonth), dayNum, dayIndex));
			}
			if (isRangeMode && self.selectedDates.length === 1 && days.childNodes[0]) {
				self._hidePrevMonthArrow =
					self._hidePrevMonthArrow ||
					(!!self.minRangeDate &&
						self.minRangeDate > days.childNodes[0].dateObj);
				self._hideNextMonthArrow =
					self._hideNextMonthArrow ||
					(!!self.maxRangeDate &&
						self.maxRangeDate <
						new Date(self.currentYear, self.currentMonth + 1, 1));
			}
			else
				updateNavigationCurrentMonth();
			var dayContainer = createElement("div", "dayContainer");
			dayContainer.appendChild(days);
			if (!self.config.animate || delta === undefined)
				clearNode(self.daysContainer);
			else {
				while (self.daysContainer.childNodes.length > 1)
					self.daysContainer.removeChild(self.daysContainer.firstChild);
			}
			if (delta && delta >= 0)
				self.daysContainer.appendChild(dayContainer);
			else
				self.daysContainer.insertBefore(dayContainer, self.daysContainer.firstChild);
			self.days = self.daysContainer.childNodes[0];
		}
		function buildMonthNav() {
			var monthNavFragment = window.document.createDocumentFragment();
			self.monthNav = createElement("div", "flatpickr-month");
			self.prevMonthNav = createElement("span", "flatpickr-prev-month");
			self.prevMonthNav.innerHTML = self.config.prevArrow;
			self.currentMonthElement = createElement("span", "cur-month");
			self.currentMonthElement.title = self.l10n.scrollTitle;
			var yearInput = createNumberInput("cur-year");
			self.currentYearElement = yearInput.childNodes[0];
			self.currentYearElement.title = self.l10n.scrollTitle;
			if (self.config.minDate)
				self.currentYearElement.min = self.config.minDate
					.getFullYear()
					.toString();
			if (self.config.maxDate) {
				self.currentYearElement.max = self.config.maxDate
					.getFullYear()
					.toString();
				self.currentYearElement.disabled =
					!!self.config.minDate &&
					self.config.minDate.getFullYear() === self.config.maxDate.getFullYear();
			}
			self.nextMonthNav = createElement("span", "flatpickr-next-month");
			self.nextMonthNav.innerHTML = self.config.nextArrow;
			self.navigationCurrentMonth = createElement("div", "flatpickr-current-month");
			self.navigationCurrentMonth.appendChild(self.currentMonthElement);
			self.navigationCurrentMonth.appendChild(yearInput);
			monthNavFragment.appendChild(self.prevMonthNav);
			monthNavFragment.appendChild(self.navigationCurrentMonth);
			monthNavFragment.appendChild(self.nextMonthNav);
			self.monthNav.appendChild(monthNavFragment);
			Object.defineProperty(self, "_hidePrevMonthArrow", {
				get: function () { return self.__hidePrevMonthArrow; },
				set: function (bool) {
					if (self.__hidePrevMonthArrow !== bool)
						self.prevMonthNav.style.display = bool ? "none" : "block";
					self.__hidePrevMonthArrow = bool;
				},
			});
			Object.defineProperty(self, "_hideNextMonthArrow", {
				get: function () { return self.__hideNextMonthArrow; },
				set: function (bool) {
					if (self.__hideNextMonthArrow !== bool)
						self.nextMonthNav.style.display = bool ? "none" : "block";
					self.__hideNextMonthArrow = bool;
				},
			});
			updateNavigationCurrentMonth();
			return self.monthNav;
		}
		function buildTime() {
			self.calendarContainer.classList.add("hasTime");
			if (self.config.noCalendar)
				self.calendarContainer.classList.add("noCalendar");
			self.timeContainer = createElement("div", "flatpickr-time");
			self.timeContainer.tabIndex = -1;
			var separator = createElement("span", "flatpickr-time-separator", ":");
			var hourInput = createNumberInput("flatpickr-hour");
			self.hourElement = hourInput.childNodes[0];
			var minuteInput = createNumberInput("flatpickr-minute");
			self.minuteElement = minuteInput.childNodes[0];
			self.hourElement.tabIndex = self.minuteElement.tabIndex = -1;
			self.hourElement.value = pad(self.latestSelectedDateObj
				? self.latestSelectedDateObj.getHours()
				: self.config.time_24hr
					? self.config.defaultHour
					: military2ampm(self.config.defaultHour));
			self.minuteElement.value = pad(self.latestSelectedDateObj
				? self.latestSelectedDateObj.getMinutes()
				: self.config.defaultMinute);
			self.hourElement.step = self.config.hourIncrement.toString();
			self.minuteElement.step = self.config.minuteIncrement.toString();
			self.hourElement.min = self.config.time_24hr ? "0" : "1";
			self.hourElement.max = self.config.time_24hr ? "23" : "12";
			self.minuteElement.min = "0";
			self.minuteElement.max = "59";
			self.hourElement.title = self.minuteElement.title = self.l10n.scrollTitle;
			self.timeContainer.appendChild(hourInput);
			self.timeContainer.appendChild(separator);
			self.timeContainer.appendChild(minuteInput);
			if (self.config.time_24hr)
				self.timeContainer.classList.add("time24hr");
			if (self.config.enableSeconds) {
				self.timeContainer.classList.add("hasSeconds");
				var secondInput = createNumberInput("flatpickr-second");
				self.secondElement = secondInput.childNodes[0];
				self.secondElement.value = pad(self.latestSelectedDateObj
					? self.latestSelectedDateObj.getSeconds()
					: self.config.defaultSeconds);
				self.secondElement.step = self.minuteElement.step;
				self.secondElement.min = self.minuteElement.min;
				self.secondElement.max = self.minuteElement.max;
				self.timeContainer.appendChild(createElement("span", "flatpickr-time-separator", ":"));
				self.timeContainer.appendChild(secondInput);
			}
			if (!self.config.time_24hr) {
				self.amPM = createElement("span", "flatpickr-am-pm", self.l10n.amPM[int((self.latestSelectedDateObj
					? self.hourElement.value
					: self.config.defaultHour) > 11)]);
				self.amPM.title = self.l10n.toggleTitle;
				self.amPM.tabIndex = -1;
				self.timeContainer.appendChild(self.amPM);
			}
			return self.timeContainer;
		}
		function buildWeekdays() {
			if (!self.weekdayContainer)
				self.weekdayContainer = createElement("div", "flatpickr-weekdays");
			var firstDayOfWeek = self.l10n.firstDayOfWeek;
			var weekdays = self.l10n.weekdays.shorthand.slice();
			if (firstDayOfWeek > 0 && firstDayOfWeek < weekdays.length) {
				weekdays = weekdays.splice(firstDayOfWeek, weekdays.length).concat(weekdays.splice(0, firstDayOfWeek));
			}
			self.weekdayContainer.innerHTML = "\n    <span class=flatpickr-weekday>\n      " + weekdays.join("</span><span class=flatpickr-weekday>") + "\n    </span>\n    ";
			return self.weekdayContainer;
		}
		function buildWeeks() {
			self.calendarContainer.classList.add("hasWeeks");
			var weekWrapper = createElement("div", "flatpickr-weekwrapper");
			weekWrapper.appendChild(createElement("span", "flatpickr-weekday", self.l10n.weekAbbreviation));
			var weekNumbers = createElement("div", "flatpickr-weeks");
			weekWrapper.appendChild(weekNumbers);
			return {
				weekWrapper: weekWrapper,
				weekNumbers: weekNumbers,
			};
		}
		function changeMonth(value, is_offset, animate, from_keyboard) {
			if (is_offset === void 0) { is_offset = true; }
			if (animate === void 0) { animate = self.config.animate; }
			if (from_keyboard === void 0) { from_keyboard = false; }
			var delta = is_offset ? value : value - self.currentMonth;
			if ((delta < 0 && self._hidePrevMonthArrow) ||
				(delta > 0 && self._hideNextMonthArrow))
				return;
			self.currentMonth += delta;
			if (self.currentMonth < 0 || self.currentMonth > 11) {
				self.currentYear += self.currentMonth > 11 ? 1 : -1;
				self.currentMonth = (self.currentMonth + 12) % 12;
				triggerEvent("onYearChange");
			}
			buildDays(animate ? delta : undefined);
			if (!animate) {
				triggerEvent("onMonthChange");
				return updateNavigationCurrentMonth();
			}
			var nav = self.navigationCurrentMonth;
			if (delta < 0) {
				while (nav.nextSibling &&
				/curr/.test(nav.nextSibling.className))
					self.monthNav.removeChild(nav.nextSibling);
			}
			else if (delta > 0) {
				while (nav.previousSibling &&
				/curr/.test(nav.previousSibling.className))
					self.monthNav.removeChild(nav.previousSibling);
			}
			self.oldCurMonth = self.navigationCurrentMonth;
			self.navigationCurrentMonth = self.monthNav.insertBefore(self.oldCurMonth.cloneNode(true), delta > 0 ? self.oldCurMonth.nextSibling : self.oldCurMonth);
			var daysContainer = self.daysContainer;
			if (daysContainer.firstChild && daysContainer.lastChild) {
				if (delta > 0) {
					daysContainer.firstChild.classList.add("slideLeft");
					daysContainer.lastChild.classList.add("slideLeftNew");
					self.oldCurMonth.classList.add("slideLeft");
					self.navigationCurrentMonth.classList.add("slideLeftNew");
				}
				else if (delta < 0) {
					daysContainer.firstChild.classList.add("slideRightNew");
					daysContainer.lastChild.classList.add("slideRight");
					self.oldCurMonth.classList.add("slideRight");
					self.navigationCurrentMonth.classList.add("slideRightNew");
				}
			}
			self.currentMonthElement = self.navigationCurrentMonth
				.firstChild;
			self.currentYearElement = self.navigationCurrentMonth.lastChild
				.childNodes[0];
			updateNavigationCurrentMonth();
			if (self.oldCurMonth.firstChild)
				self.oldCurMonth.firstChild.textContent = monthToStr(self.currentMonth - delta, self.config.shorthandCurrentMonth, self.l10n);
			afterDayAnim(function () { return triggerEvent("onMonthChange"); });
			if (from_keyboard &&
				document.activeElement &&
				document.activeElement.$i) {
				var index_1 = document.activeElement.$i;
				afterDayAnim(function () {
					focusOnDay(index_1, 0);
				});
			}
		}
		function clear(triggerChangeEvent) {
			if (triggerChangeEvent === void 0) { triggerChangeEvent = true; }
			self.input.value = "";
			if (self.altInput)
				self.altInput.value = "";
			if (self.mobileInput)
				self.mobileInput.value = "";
			self.selectedDates = [];
			self.latestSelectedDateObj = undefined;
			self.showTimeInput = false;
			self.redraw();
			if (triggerChangeEvent)
				triggerEvent("onChange");
		}
		function close() {
			self.isOpen = false;
			if (!self.isMobile) {
				self.calendarContainer.classList.remove("open");
				self._input.classList.remove("active");
			}
			triggerEvent("onClose");
		}
		function destroy() {
			if (self.config !== undefined)
				triggerEvent("onDestroy");
			for (var i = self._handlers.length; i--;) {
				var h = self._handlers[i];
				h.element.removeEventListener(h.event, h.handler);
			}
			self._handlers = [];
			if (self.mobileInput) {
				if (self.mobileInput.parentNode)
					self.mobileInput.parentNode.removeChild(self.mobileInput);
				self.mobileInput = undefined;
			}
			else if (self.calendarContainer && self.calendarContainer.parentNode)
				self.calendarContainer.parentNode.removeChild(self.calendarContainer);
			if (self.altInput) {
				self.input.type = "text";
				if (self.altInput.parentNode)
					self.altInput.parentNode.removeChild(self.altInput);
				delete self.altInput;
			}
			if (self.input) {
				self.input.type = self.input._type;
				self.input.classList.remove("flatpickr-input");
				self.input.removeAttribute("readonly");
				self.input.value = "";
			}
			[
				"_showTimeInput",
				"latestSelectedDateObj",
				"_hideNextMonthArrow",
				"_hidePrevMonthArrow",
				"__hideNextMonthArrow",
				"__hidePrevMonthArrow",
				"isMobile",
				"isOpen",
				"selectedDateElem",
				"minDateHasTime",
				"maxDateHasTime",
				"days",
				"daysContainer",
				"_input",
				"_positionElement",
				"innerContainer",
				"rContainer",
				"monthNav",
				"todayDateElem",
				"calendarContainer",
				"weekdayContainer",
				"prevMonthNav",
				"nextMonthNav",
				"currentMonthElement",
				"currentYearElement",
				"navigationCurrentMonth",
				"selectedDateElem",
				"config",
			].forEach(function (k) {
				try {
					delete self[k];
				}
				catch (_) { }
			});
		}
		function isCalendarElem(elem) {
			if (self.config.appendTo && self.config.appendTo.contains(elem))
				return true;
			return self.calendarContainer.contains(elem);
		}
		function documentClick(e) {
			if (self.isOpen && !self.config.inline) {
				var isCalendarElement = isCalendarElem(e.target);
				var isInput = e.target === self.input ||
					e.target === self.altInput ||
					self.element.contains(e.target) ||
					(e.path &&
						e.path.indexOf &&
						(~e.path.indexOf(self.input) ||
							~e.path.indexOf(self.altInput)));
				var lostFocus = e.type === "blur"
					? isInput &&
					e.relatedTarget &&
					!isCalendarElem(e.relatedTarget)
					: !isInput && !isCalendarElement;
				if (lostFocus &&
					self.config.ignoredFocusElements.indexOf(e.target) === -1) {
					self.close();
					if (self.config.mode === "range" && self.selectedDates.length === 1) {
						self.clear(false);
						self.redraw();
					}
				}
			}
		}
		function changeYear(newYear) {
			if (!newYear ||
				(self.currentYearElement.min &&
					newYear < parseInt(self.currentYearElement.min)) ||
				(self.currentYearElement.max &&
					newYear > parseInt(self.currentYearElement.max)))
				return;
			var newYearNum = newYear, isNewYear = self.currentYear !== newYearNum;
			self.currentYear = newYearNum || self.currentYear;
			if (self.config.maxDate &&
				self.currentYear === self.config.maxDate.getFullYear()) {
				self.currentMonth = Math.min(self.config.maxDate.getMonth(), self.currentMonth);
			}
			else if (self.config.minDate &&
				self.currentYear === self.config.minDate.getFullYear()) {
				self.currentMonth = Math.max(self.config.minDate.getMonth(), self.currentMonth);
			}
			if (isNewYear) {
				self.redraw();
				triggerEvent("onYearChange");
			}
		}
		function isEnabled(date, timeless) {
			if (timeless === void 0) { timeless = true; }
			var dateToCheck = self.parseDate(date, undefined, timeless);
			if ((self.config.minDate &&
					dateToCheck &&
					compareDates(dateToCheck, self.config.minDate, timeless !== undefined ? timeless : !self.minDateHasTime) < 0) ||
				(self.config.maxDate &&
					dateToCheck &&
					compareDates(dateToCheck, self.config.maxDate, timeless !== undefined ? timeless : !self.maxDateHasTime) > 0))
				return false;
			if (!self.config.enable.length && !self.config.disable.length)
				return true;
			if (dateToCheck === undefined)
				return false;
			var bool = self.config.enable.length > 0, array = bool ? self.config.enable : self.config.disable;
			for (var i = 0, d = void 0; i < array.length; i++) {
				d = array[i];
				if (typeof d === "function" &&
					d(dateToCheck))
					return bool;
				else if (d instanceof Date &&
					dateToCheck !== undefined &&
					d.getTime() === dateToCheck.getTime())
					return bool;
				else if (typeof d === "string" && dateToCheck !== undefined) {
					var parsed = self.parseDate(d, undefined, true);
					return parsed && parsed.getTime() === dateToCheck.getTime()
						? bool
						: !bool;
				}
				else if (typeof d === "object" &&
					dateToCheck !== undefined &&
					d.from &&
					d.to &&
					dateToCheck.getTime() >= d.from.getTime() &&
					dateToCheck.getTime() <= d.to.getTime())
					return bool;
			}
			return !bool;
		}
		function onKeyDown(e) {
			var isInput = e.target === self._input;
			var calendarElem = isCalendarElem(e.target);
			var allowInput = self.config.allowInput;
			var allowKeydown = self.isOpen && (!allowInput || !isInput);
			var allowInlineKeydown = self.config.inline && isInput && !allowInput;
			if (e.key === "Enter" && isInput) {
				if (allowInput) {
					self.setDate(self._input.value, true, e.target === self.altInput
						? self.config.altFormat
						: self.config.dateFormat);
					return e.target.blur();
				}
				else
					self.open();
			}
			else if (calendarElem || allowKeydown || allowInlineKeydown) {
				var isTimeObj = !!self.timeContainer &&
					self.timeContainer.contains(e.target);
				switch (e.key) {
					case "Enter":
						if (isTimeObj)
							updateValue();
						else
							selectDate(e);
						break;
					case "Escape":
						e.preventDefault();
						self.close();
						break;
					case "Backspace":
					case "Delete":
						if (isInput && !self.config.allowInput)
							self.clear();
						break;
					case "ArrowLeft":
					case "ArrowRight":
						if (!isTimeObj) {
							e.preventDefault();
							if (self.daysContainer) {
								var delta_1 = e.key === "ArrowRight" ? 1 : -1;
								if (!e.ctrlKey)
									focusOnDay(e.target.$i, delta_1);
								else
									changeMonth(delta_1, true, undefined, true);
							}
						}
						else if (self.hourElement)
							self.hourElement.focus();
						break;
					case "ArrowUp":
					case "ArrowDown":
						e.preventDefault();
						var delta = e.key === "ArrowDown" ? 1 : -1;
						if (self.daysContainer && e.target.$i !== undefined) {
							if (e.ctrlKey) {
								changeYear(self.currentYear - delta);
								focusOnDay(e.target.$i, 0);
							}
							else if (!isTimeObj)
								focusOnDay(e.target.$i, delta * 7);
						}
						else if (self.config.enableTime) {
							if (!isTimeObj && self.hourElement)
								self.hourElement.focus();
							updateTime(e);
							self._debouncedChange();
						}
						break;
					case "Tab":
						if (e.target === self.hourElement) {
							e.preventDefault();
							self.minuteElement.select();
						}
						else if (e.target === self.minuteElement &&
							(self.secondElement || self.amPM)) {
							e.preventDefault();
							if (self.secondElement !== undefined)
								self.secondElement.focus();
							else if (self.amPM !== undefined)
								self.amPM.focus();
						}
						else if (e.target === self.secondElement && self.amPM) {
							e.preventDefault();
							self.amPM.focus();
						}
						break;
					case self.l10n.amPM[0].charAt(0):
						if (self.amPM !== undefined && e.target === self.amPM) {
							self.amPM.textContent = self.l10n.amPM[0];
							setHoursFromInputs();
							updateValue();
						}
						break;
					case self.l10n.amPM[1].charAt(0):
						if (self.amPM !== undefined && e.target === self.amPM) {
							self.amPM.textContent = self.l10n.amPM[1];
							setHoursFromInputs();
							updateValue();
						}
						break;
					default:
						break;
				}
				triggerEvent("onKeyDown", e);
			}
		}
		function onMouseOver(elem) {
			if (self.selectedDates.length !== 1 ||
				!elem.classList.contains("flatpickr-day") ||
				self.minRangeDate === undefined ||
				self.maxRangeDate === undefined)
				return;
			var hoverDate = elem.dateObj, initialDate = self.parseDate(self.selectedDates[0], undefined, true), rangeStartDate = Math.min(hoverDate.getTime(), self.selectedDates[0].getTime()), rangeEndDate = Math.max(hoverDate.getTime(), self.selectedDates[0].getTime()), containsDisabled = false;
			for (var t = rangeStartDate; t < rangeEndDate; t += duration.DAY) {
				if (!isEnabled(new Date(t))) {
					containsDisabled = true;
					break;
				}
			}
			var _loop_1 = function (i, date) {
				var timestamp = date.getTime();
				var outOfRange = timestamp < self.minRangeDate.getTime() ||
					timestamp > self.maxRangeDate.getTime(), dayElem = self.days.childNodes[i];
				if (outOfRange) {
					dayElem.classList.add("notAllowed");
					["inRange", "startRange", "endRange"].forEach(function (c) {
						dayElem.classList.remove(c);
					});
					return "continue";
				}
				else if (containsDisabled && !outOfRange)
					return "continue";
				["startRange", "inRange", "endRange", "notAllowed"].forEach(function (c) {
					dayElem.classList.remove(c);
				});
				var minRangeDate = Math.max(self.minRangeDate.getTime(), rangeStartDate), maxRangeDate = Math.min(self.maxRangeDate.getTime(), rangeEndDate);
				elem.classList.add(hoverDate < self.selectedDates[0] ? "startRange" : "endRange");
				if (initialDate < hoverDate && timestamp === initialDate.getTime())
					dayElem.classList.add("startRange");
				else if (initialDate > hoverDate && timestamp === initialDate.getTime())
					dayElem.classList.add("endRange");
				if (timestamp >= minRangeDate && timestamp <= maxRangeDate)
					dayElem.classList.add("inRange");
			};
			for (var i = 0, date = self.days.childNodes[i].dateObj; i < 42; i++, date =
				self.days.childNodes[i] &&
				self.days.childNodes[i].dateObj) {
				_loop_1(i, date);
			}
		}
		function onResize() {
			if (self.isOpen && !self.config.static && !self.config.inline)
				positionCalendar();
		}
		function open(e, positionElement) {
			if (positionElement === void 0) { positionElement = self._input; }
			if (self.isMobile) {
				if (e) {
					e.preventDefault();
					e.target && e.target.blur();
				}
				setTimeout(function () {
					self.mobileInput !== undefined && self.mobileInput.click();
				}, 0);
				triggerEvent("onOpen");
				return;
			}
			if (self._input.disabled || self.config.inline)
				return;
			var wasOpen = self.isOpen;
			self.isOpen = true;
			positionCalendar(positionElement);
			self.calendarContainer.classList.add("open");
			self._input.classList.add("active");
			!wasOpen && triggerEvent("onOpen");
		}
		function minMaxDateSetter(type) {
			return function (date) {
				var dateObj = (self.config["_" + type + "Date"] = self.parseDate(date));
				var inverseDateObj = self.config["_" + (type === "min" ? "max" : "min") + "Date"];
				if (dateObj !== undefined) {
					self[type === "min" ? "minDateHasTime" : "maxDateHasTime"] =
						dateObj.getHours() > 0 ||
						dateObj.getMinutes() > 0 ||
						dateObj.getSeconds() > 0;
				}
				if (self.selectedDates) {
					self.selectedDates = self.selectedDates.filter(function (d) { return isEnabled(d); });
					if (!self.selectedDates.length && type === "min")
						setHoursFromDate(dateObj);
					updateValue();
				}
				if (self.daysContainer) {
					redraw();
					if (dateObj !== undefined)
						self.currentYearElement[type] = dateObj.getFullYear().toString();
					else
						self.currentYearElement.removeAttribute(type);
					self.currentYearElement.disabled =
						!!inverseDateObj &&
						dateObj !== undefined &&
						inverseDateObj.getFullYear() === dateObj.getFullYear();
				}
			};
		}
		function parseConfig() {
			var boolOpts = [
				"wrap",
				"weekNumbers",
				"allowInput",
				"clickOpens",
				"time_24hr",
				"enableTime",
				"noCalendar",
				"altInput",
				"shorthandCurrentMonth",
				"inline",
				"static",
				"enableSeconds",
				"disableMobile",
			];
			var hooks = [
				"onChange",
				"onClose",
				"onDayCreate",
				"onDestroy",
				"onKeyDown",
				"onMonthChange",
				"onOpen",
				"onParseConfig",
				"onReady",
				"onValueUpdate",
				"onYearChange",
			];
			self.config = __assign({}, flatpickr.defaultConfig);
			var userConfig = __assign({}, instanceConfig, JSON.parse(JSON.stringify(element.dataset || {})));
			var formats$$1 = {};
			Object.defineProperty(self.config, "enable", {
				get: function () { return self.config._enable || []; },
				set: function (dates) {
					self.config._enable = parseDateRules(dates);
				},
			});
			Object.defineProperty(self.config, "disable", {
				get: function () { return self.config._disable || []; },
				set: function (dates) {
					self.config._disable = parseDateRules(dates);
				},
			});
			if (!userConfig.dateFormat && userConfig.enableTime) {
				formats$$1.dateFormat = userConfig.noCalendar
					? "H:i" + (userConfig.enableSeconds ? ":S" : "")
					: flatpickr.defaultConfig.dateFormat +
					" H:i" +
					(userConfig.enableSeconds ? ":S" : "");
			}
			if (userConfig.altInput && userConfig.enableTime && !userConfig.altFormat) {
				formats$$1.altFormat = userConfig.noCalendar
					? "h:i" + (userConfig.enableSeconds ? ":S K" : " K")
					: flatpickr.defaultConfig.altFormat +
					(" h:i" + (userConfig.enableSeconds ? ":S" : "") + " K");
			}
			Object.defineProperty(self.config, "minDate", {
				get: function () { return self.config._minDate; },
				set: minMaxDateSetter("min"),
			});
			Object.defineProperty(self.config, "maxDate", {
				get: function () { return self.config._maxDate; },
				set: minMaxDateSetter("max"),
			});
			Object.assign(self.config, formats$$1, userConfig);
			for (var i = 0; i < boolOpts.length; i++)
				self.config[boolOpts[i]] =
					self.config[boolOpts[i]] === true ||
					self.config[boolOpts[i]] === "true";
			for (var i = hooks.length; i--;) {
				if (self.config[hooks[i]] !== undefined) {
					self.config[hooks[i]] = arrayify(self.config[hooks[i]] || []).map(bindToInstance);
				}
			}
			for (var i = 0; i < self.config.plugins.length; i++) {
				var pluginConf = self.config.plugins[i](self) || {};
				for (var key in pluginConf) {
					if (~hooks.indexOf(key)) {
						self.config[key] = arrayify(pluginConf[key])
							.map(bindToInstance)
							.concat(self.config[key]);
					}
					else if (typeof userConfig[key] === "undefined")
						self.config[key] = pluginConf[key];
				}
			}
			self.isMobile =
				!self.config.disableMobile &&
				!self.config.inline &&
				self.config.mode === "single" &&
				!self.config.disable.length &&
				!self.config.enable.length &&
				!self.config.weekNumbers &&
				/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
			triggerEvent("onParseConfig");
		}
		function setupLocale() {
			if (typeof self.config.locale !== "object" &&
				typeof flatpickr.l10ns[self.config.locale] === "undefined")
				self.config.errorHandler(new Error("flatpickr: invalid locale " + self.config.locale));
			self.l10n = __assign({}, flatpickr.l10ns.default, typeof self.config.locale === "object"
				? self.config.locale
				: self.config.locale !== "default"
					? flatpickr.l10ns[self.config.locale]
					: undefined);
			tokenRegex.K = "(" + self.l10n.amPM[0] + "|" + self.l10n.amPM[1] + "|" + self.l10n.amPM[0].toLowerCase() + "|" + self.l10n.amPM[1].toLowerCase() + ")";
		}
		function positionCalendar(positionElement) {
			if (positionElement === void 0) { positionElement = self._positionElement; }
			if (self.calendarContainer === undefined)
				return;
			var calendarHeight = self.calendarContainer.offsetHeight, calendarWidth = self.calendarContainer.offsetWidth, configPos = self.config.position, inputBounds = positionElement.getBoundingClientRect(), distanceFromBottom = window.innerHeight - inputBounds.bottom, showOnTop = configPos === "above" ||
				(configPos !== "below" &&
					distanceFromBottom < calendarHeight &&
					inputBounds.top > calendarHeight);
			var top = window.pageYOffset +
				inputBounds.top +
				(!showOnTop ? positionElement.offsetHeight + 2 : -calendarHeight - 2);
			toggleClass(self.calendarContainer, "arrowTop", !showOnTop);
			toggleClass(self.calendarContainer, "arrowBottom", showOnTop);
			if (self.config.inline)
				return;
			var left = window.pageXOffset + inputBounds.left;
			var right = window.document.body.offsetWidth - inputBounds.right;
			var rightMost = left + calendarWidth > window.document.body.offsetWidth;
			toggleClass(self.calendarContainer, "rightMost", rightMost);
			if (self.config.static)
				return;
			self.calendarContainer.style.top = top + "px";
			if (!rightMost) {
				self.calendarContainer.style.left = left + "px";
				self.calendarContainer.style.right = "auto";
			}
			else {
				self.calendarContainer.style.left = "auto";
				self.calendarContainer.style.right = right + "px";
			}
		}
		function redraw() {
			if (self.config.noCalendar || self.isMobile)
				return;
			buildWeekdays();
			updateNavigationCurrentMonth();
			buildDays();
		}
		function selectDate(e) {
			e.preventDefault();
			e.stopPropagation();
			var isSelectable = function (day) {
				return day.classList &&
					day.classList.contains("flatpickr-day") &&
					!day.classList.contains("disabled") &&
					!day.classList.contains("notAllowed");
			};
			var t = findParent(e.target, isSelectable);
			if (t === undefined)
				return;
			var target = t;
			var selectedDate = (self.latestSelectedDateObj = new Date(target.dateObj.getTime()));
			var shouldChangeMonth = selectedDate.getMonth() !== self.currentMonth &&
				self.config.mode !== "range";
			self.selectedDateElem = target;
			if (self.config.mode === "single")
				self.selectedDates = [selectedDate];
			else if (self.config.mode === "multiple") {
				var selectedIndex = isDateSelected(selectedDate);
				if (selectedIndex)
					self.selectedDates.splice(parseInt(selectedIndex), 1);
				else
					self.selectedDates.push(selectedDate);
			}
			else if (self.config.mode === "range") {
				if (self.selectedDates.length === 2)
					self.clear();
				self.selectedDates.push(selectedDate);
				if (compareDates(selectedDate, self.selectedDates[0], true) !== 0)
					self.selectedDates.sort(function (a, b) { return a.getTime() - b.getTime(); });
			}
			setHoursFromInputs();
			if (shouldChangeMonth) {
				var isNewYear = self.currentYear !== selectedDate.getFullYear();
				self.currentYear = selectedDate.getFullYear();
				self.currentMonth = selectedDate.getMonth();
				if (isNewYear)
					triggerEvent("onYearChange");
				triggerEvent("onMonthChange");
			}
			buildDays();
			if (self.config.minDate &&
				self.minDateHasTime &&
				self.config.enableTime &&
				compareDates(selectedDate, self.config.minDate) === 0)
				setHoursFromDate(self.config.minDate);
			updateValue();
			if (self.config.enableTime)
				setTimeout(function () { return (self.showTimeInput = true); }, 50);
			if (self.config.mode === "range") {
				if (self.selectedDates.length === 1) {
					onMouseOver(target);
					self._hidePrevMonthArrow =
						self._hidePrevMonthArrow ||
						(self.minRangeDate !== undefined &&
							self.minRangeDate >
							self.days.childNodes[0].dateObj);
					self._hideNextMonthArrow =
						self._hideNextMonthArrow ||
						(self.maxRangeDate !== undefined &&
							self.maxRangeDate <
							new Date(self.currentYear, self.currentMonth + 1, 1));
				}
				else
					updateNavigationCurrentMonth();
			}
			triggerEvent("onChange");
			if (!shouldChangeMonth)
				focusOnDay(target.$i, 0);
			else
				afterDayAnim(function () { return self.selectedDateElem && self.selectedDateElem.focus(); });
			if (self.hourElement !== undefined)
				setTimeout(function () { return self.hourElement !== undefined && self.hourElement.select(); }, 451);
			if (self.config.closeOnSelect) {
				var single = self.config.mode === "single" && !self.config.enableTime;
				var range = self.config.mode === "range" &&
					self.selectedDates.length === 2 &&
					!self.config.enableTime;
				if (single || range)
					self.close();
			}
		}
		function set(option, value) {
			if (option !== null && typeof option === "object")
				Object.assign(self.config, option);
			else
				self.config[option] = value;
			self.redraw();
			jumpToDate();
		}
		function setSelectedDate(inputDate, format) {
			var dates = [];
			if (inputDate instanceof Array)
				dates = inputDate.map(function (d) { return self.parseDate(d, format); });
			else if (inputDate instanceof Date || typeof inputDate === "number")
				dates = [self.parseDate(inputDate, format)];
			else if (typeof inputDate === "string") {
				switch (self.config.mode) {
					case "single":
						dates = [self.parseDate(inputDate, format)];
						break;
					case "multiple":
						dates = inputDate
							.split(self.config.conjunction)
							.map(function (date) { return self.parseDate(date, format); });
						break;
					case "range":
						dates = inputDate
							.split(self.l10n.rangeSeparator)
							.map(function (date) { return self.parseDate(date, format); });
						break;
					default:
						break;
				}
			}
			else
				self.config.errorHandler(new Error("Invalid date supplied: " + JSON.stringify(inputDate)));
			self.selectedDates = dates.filter(function (d) { return d instanceof Date && isEnabled(d, false); });
			self.selectedDates.sort(function (a, b) { return a.getTime() - b.getTime(); });
		}
		function setDate(date, triggerChange, format) {
			if (triggerChange === void 0) { triggerChange = false; }
			if (date !== 0 && !date)
				return self.clear(triggerChange);
			setSelectedDate(date, format);
			self.showTimeInput = self.selectedDates.length > 0;
			self.latestSelectedDateObj = self.selectedDates[0];
			self.redraw();
			jumpToDate();
			setHoursFromDate();
			updateValue(triggerChange);
			if (triggerChange)
				triggerEvent("onChange");
		}
		function parseDateRules(arr) {
			return arr
				.map(function (rule) {
					if (typeof rule === "string" ||
						typeof rule === "number" ||
						rule instanceof Date) {
						return self.parseDate(rule, undefined, true);
					}
					else if (rule &&
						typeof rule === "object" &&
						rule.from &&
						rule.to)
						return {
							from: self.parseDate(rule.from, undefined),
							to: self.parseDate(rule.to, undefined),
						};
					return rule;
				})
				.filter(function (x) { return x; });
		}
		function setupDates() {
			self.selectedDates = [];
			self.now = new Date();
			var preloadedDate = self.config.defaultDate || self.input.value;
			if (preloadedDate)
				setSelectedDate(preloadedDate, self.config.dateFormat);
			var initialDate = self.selectedDates.length
				? self.selectedDates[0]
				: self.config.minDate &&
				self.config.minDate.getTime() > self.now.getTime()
					? self.config.minDate
					: self.config.maxDate &&
					self.config.maxDate.getTime() < self.now.getTime()
						? self.config.maxDate
						: self.now;
			self.currentYear = initialDate.getFullYear();
			self.currentMonth = initialDate.getMonth();
			if (self.selectedDates.length)
				self.latestSelectedDateObj = self.selectedDates[0];
			self.minDateHasTime =
				!!self.config.minDate &&
				(self.config.minDate.getHours() > 0 ||
					self.config.minDate.getMinutes() > 0 ||
					self.config.minDate.getSeconds() > 0);
			self.maxDateHasTime =
				!!self.config.maxDate &&
				(self.config.maxDate.getHours() > 0 ||
					self.config.maxDate.getMinutes() > 0 ||
					self.config.maxDate.getSeconds() > 0);
			Object.defineProperty(self, "showTimeInput", {
				get: function () { return self._showTimeInput; },
				set: function (bool) {
					self._showTimeInput = bool;
					if (self.calendarContainer)
						toggleClass(self.calendarContainer, "showTimeInput", bool);
					positionCalendar();
				},
			});
		}
		function formatDate(dateObj, frmt) {
			if (self.config !== undefined && self.config.formatDate !== undefined)
				return self.config.formatDate(dateObj, frmt);
			return frmt
				.split("")
				.map(function (c, i, arr) {
					return formats[c] && arr[i - 1] !== "\\"
						? formats[c](dateObj, self.l10n, self.config)
						: c !== "\\" ? c : "";
				})
				.join("");
		}
		function parseDate(date, givenFormat, timeless) {
			if (date !== 0 && !date)
				return undefined;
			var parsedDate;
			var date_orig = date;
			if (date instanceof Date)
				parsedDate = new Date(date.getTime());
			else if (typeof date !== "string" &&
				date.toFixed !== undefined)
				parsedDate = new Date(date);
			else if (typeof date === "string") {
				var format = givenFormat || (self.config || flatpickr.defaultConfig).dateFormat;
				var datestr = String(date).trim();
				if (datestr === "today") {
					parsedDate = new Date();
					timeless = true;
				}
				else if (/Z$/.test(datestr) ||
					/GMT$/.test(datestr))
					parsedDate = new Date(date);
				else if (self.config && self.config.parseDate)
					parsedDate = self.config.parseDate(date, format);
				else {
					parsedDate =
						!self.config || !self.config.noCalendar
							? new Date(new Date().getFullYear(), 0, 1, 0, 0, 0, 0)
							: new Date(new Date().setHours(0, 0, 0, 0));
					var matched = void 0, ops = [];
					for (var i = 0, matchIndex = 0, regexStr = ""; i < format.length; i++) {
						var token = format[i];
						var isBackSlash = token === "\\";
						var escaped = format[i - 1] === "\\" || isBackSlash;
						if (tokenRegex[token] && !escaped) {
							regexStr += tokenRegex[token];
							var match = new RegExp(regexStr).exec(date);
							if (match && (matched = true)) {
								ops[token !== "Y" ? "push" : "unshift"]({
									fn: revFormat[token],
									val: match[++matchIndex],
								});
							}
						}
						else if (!isBackSlash)
							regexStr += ".";
						ops.forEach(function (_a) {
							var fn = _a.fn, val = _a.val;
							return (parsedDate =
								fn(parsedDate, val, self.l10n) || parsedDate);
						});
					}
					parsedDate = matched ? parsedDate : undefined;
				}
			}
			if (!(parsedDate instanceof Date)) {
				self.config.errorHandler(new Error("Invalid date provided: " + date_orig));
				return undefined;
			}
			if (timeless === true)
				parsedDate.setHours(0, 0, 0, 0);
			return parsedDate;
		}
		function setupInputs() {
			self.input = self.config.wrap
				? element.querySelector("[data-input]")
				: element;
			if (!self.input) {
				self.config.errorHandler(new Error("Invalid input element specified"));
				return;
			}
			self.input._type = self.input.type;
			self.input.type = "text";
			self.input.classList.add("flatpickr-input");
			self._input = self.input;
			if (self.config.altInput) {
				self.altInput = createElement(self.input.nodeName, self.input.className + " " + self.config.altInputClass);
				self._input = self.altInput;
				self.altInput.placeholder = self.input.placeholder;
				self.altInput.disabled = self.input.disabled;
				self.altInput.required = self.input.required;
				self.altInput.type = "text";
				self.input.type = "hidden";
				if (!self.config.static && self.input.parentNode)
					self.input.parentNode.insertBefore(self.altInput, self.input.nextSibling);
			}
			if (!self.config.allowInput)
				self._input.setAttribute("readonly", "readonly");
			self._positionElement = self.config.positionElement || self._input;
		}
		function setupMobile() {
			var inputType = self.config.enableTime
				? self.config.noCalendar ? "time" : "datetime-local"
				: "date";
			self.mobileInput = createElement("input", self.input.className + " flatpickr-mobile");
			self.mobileInput.step = self.input.getAttribute("step") || "any";
			self.mobileInput.tabIndex = 1;
			self.mobileInput.type = inputType;
			self.mobileInput.disabled = self.input.disabled;
			self.mobileInput.placeholder = self.input.placeholder;
			self.mobileFormatStr =
				inputType === "datetime-local"
					? "Y-m-d\\TH:i:S"
					: inputType === "date" ? "Y-m-d" : "H:i:S";
			if (self.selectedDates.length) {
				self.mobileInput.defaultValue = self.mobileInput.value = self.formatDate(self.selectedDates[0], self.mobileFormatStr);
			}
			if (self.config.minDate)
				self.mobileInput.min = self.formatDate(self.config.minDate, "Y-m-d");
			if (self.config.maxDate)
				self.mobileInput.max = self.formatDate(self.config.maxDate, "Y-m-d");
			self.input.type = "hidden";
			if (self.altInput !== undefined)
				self.altInput.type = "hidden";
			try {
				if (self.input.parentNode)
					self.input.parentNode.insertBefore(self.mobileInput, self.input.nextSibling);
			}
			catch (_a) { }
			bind(self.mobileInput, "change", function (e) {
				self.setDate(e.target.value, false, self.mobileFormatStr);
				triggerEvent("onChange");
				triggerEvent("onClose");
			});
		}
		function toggle() {
			if (self.isOpen)
				return self.close();
			self.open();
		}
		function triggerEvent(event, data) {
			var hooks = self.config[event];
			if (hooks !== undefined && hooks.length > 0) {
				for (var i = 0; hooks[i] && i < hooks.length; i++)
					hooks[i](self.selectedDates, self.input.value, self, data);
			}
			if (event === "onChange") {
				self.input.dispatchEvent(createEvent("change"));
				self.input.dispatchEvent(createEvent("input"));
			}
		}
		function createEvent(name) {
			var e = document.createEvent("Event");
			e.initEvent(name, true, true);
			return e;
		}
		function isDateSelected(date) {
			for (var i = 0; i < self.selectedDates.length; i++) {
				if (compareDates(self.selectedDates[i], date) === 0)
					return "" + i;
			}
			return false;
		}
		function isDateInRange(date) {
			if (self.config.mode !== "range" || self.selectedDates.length < 2)
				return false;
			return (compareDates(date, self.selectedDates[0]) >= 0 &&
				compareDates(date, self.selectedDates[1]) <= 0);
		}
		function updateNavigationCurrentMonth() {
			if (self.config.noCalendar || self.isMobile || !self.monthNav)
				return;
			self.currentMonthElement.textContent =
				monthToStr(self.currentMonth, self.config.shorthandCurrentMonth, self.l10n) + " ";
			self.currentYearElement.value = self.currentYear.toString();
			self._hidePrevMonthArrow =
				self.config.minDate !== undefined &&
				(self.currentYear === self.config.minDate.getFullYear()
					? self.currentMonth <= self.config.minDate.getMonth()
					: self.currentYear < self.config.minDate.getFullYear());
			self._hideNextMonthArrow =
				self.config.maxDate !== undefined &&
				(self.currentYear === self.config.maxDate.getFullYear()
					? self.currentMonth + 1 > self.config.maxDate.getMonth()
					: self.currentYear > self.config.maxDate.getFullYear());
		}
		function updateValue(triggerChange) {
			if (triggerChange === void 0) { triggerChange = true; }
			if (!self.selectedDates.length)
				return self.clear(triggerChange);
			if (self.mobileInput !== undefined && self.mobileFormatStr) {
				self.mobileInput.value =
					self.latestSelectedDateObj !== undefined
						? self.formatDate(self.latestSelectedDateObj, self.mobileFormatStr)
						: "";
			}
			var joinChar = self.config.mode !== "range"
				? self.config.conjunction
				: self.l10n.rangeSeparator;
			self.input.value = self.selectedDates
				.map(function (dObj) { return self.formatDate(dObj, self.config.dateFormat); })
				.join(joinChar);
			if (self.altInput !== undefined) {
				self.altInput.value = self.selectedDates
					.map(function (dObj) { return self.formatDate(dObj, self.config.altFormat); })
					.join(joinChar);
			}
			if (triggerChange !== false)
				triggerEvent("onValueUpdate");
		}
		function onMonthNavScroll(e) {
			e.preventDefault();
			var isYear = self.currentYearElement.parentNode &&
				self.currentYearElement.parentNode.contains(e.target);
			if (e.target === self.currentMonthElement || isYear) {
				var delta = mouseDelta(e);
				if (isYear) {
					changeYear(self.currentYear + delta);
					e.target.value = self.currentYear.toString();
				}
				else
					self.changeMonth(delta, true, false);
			}
		}
		function onMonthNavClick(e) {
			var isPrevMonth = self.prevMonthNav.contains(e.target);
			var isNextMonth = self.nextMonthNav.contains(e.target);
			if (isPrevMonth || isNextMonth)
				changeMonth(isPrevMonth ? -1 : 1);
			else if (e.target === self.currentYearElement) {
				e.preventDefault();
				self.currentYearElement.select();
			}
			else if (e.target.className === "arrowUp")
				self.changeYear(self.currentYear + 1);
			else if (e.target.className === "arrowDown")
				self.changeYear(self.currentYear - 1);
		}
		function timeWrapper(e) {
			e.preventDefault();
			var isKeyDown = e.type === "keydown", input = e.target;
			if (self.amPM !== undefined && e.target === self.amPM) {
				self.amPM.textContent =
					self.l10n.amPM[int(self.amPM.textContent === self.l10n.amPM[0])];
			}
			var min = Number(input.min), max = Number(input.max), step = Number(input.step), curValue = parseInt(input.value, 10), delta = e.delta ||
				(isKeyDown
					? e.which === 38 ? 1 : -1
					: Math.max(-1, Math.min(1, e.wheelDelta || -e.deltaY)) || 0);
			var newValue = curValue + step * delta;
			if (typeof input.value !== "undefined" && input.value.length === 2) {
				var isHourElem = input === self.hourElement, isMinuteElem = input === self.minuteElement;
				if (newValue < min) {
					newValue =
						max +
						newValue +
						int(!isHourElem) +
						(int(isHourElem) && int(!self.amPM));
					if (isMinuteElem)
						incrementNumInput(undefined, -1, self.hourElement);
				}
				else if (newValue > max) {
					newValue =
						input === self.hourElement ? newValue - max - int(!self.amPM) : min;
					if (isMinuteElem)
						incrementNumInput(undefined, 1, self.hourElement);
				}
				if (self.amPM &&
					isHourElem &&
					(step === 1
						? newValue + curValue === 23
						: Math.abs(newValue - curValue) > step)) {
					self.amPM.textContent =
						self.l10n.amPM[int(self.amPM.textContent === self.l10n.amPM[0])];
				}
				input.value = pad(newValue);
			}
		}
		init();
		return self;
	}
	function _flatpickr(nodeList, config) {
		var nodes = Array.prototype.slice.call(nodeList);
		var instances = [];
		for (var i = 0; i < nodes.length; i++) {
			var node = nodes[i];
			try {
				if (node.getAttribute("data-fp-omit") !== null)
					continue;
				if (node._flatpickr !== undefined) {
					node._flatpickr.destroy();
					node._flatpickr = undefined;
				}
				node._flatpickr = FlatpickrInstance(node, config || {});
				instances.push(node._flatpickr);
			}
			catch (e) {
				console.error(e);
			}
		}
		return instances.length === 1 ? instances[0] : instances;
	}
	if (typeof HTMLElement !== "undefined") {
		HTMLCollection.prototype.flatpickr = NodeList.prototype.flatpickr = function (config) {
			return _flatpickr(this, config);
		};
		HTMLElement.prototype.flatpickr = function (config) {
			return _flatpickr([this], config);
		};
	}
	var flatpickr;
	flatpickr = function (selector, config) {
		if (selector instanceof NodeList)
			return _flatpickr(selector, config);
		else if (typeof selector === "string")
			return _flatpickr(window.document.querySelectorAll(selector), config);
		return _flatpickr([selector], config);
	};
	if (typeof window === "object")
		window.flatpickr = flatpickr;
	flatpickr.defaultConfig = defaults;
	flatpickr.l10ns = {
		en: __assign({}, english),
		default: __assign({}, english),
	};
	flatpickr.localize = function (l10n) {
		flatpickr.l10ns.default = __assign({}, flatpickr.l10ns.default, l10n);
	};
	flatpickr.setDefaults = function (config) {
		flatpickr.defaultConfig = __assign({}, flatpickr.defaultConfig, config);
	};
	if (typeof jQuery !== "undefined") {
		jQuery.fn.flatpickr = function (config) {
			return _flatpickr(this, config);
		};
	}
	Date.prototype.fp_incr = function (days) {
		return new Date(this.getFullYear(), this.getMonth(), this.getDate() + (typeof days === "string" ? parseInt(days, 10) : days));
	};
	var flatpickr$1 = flatpickr;

	return flatpickr$1;

})));