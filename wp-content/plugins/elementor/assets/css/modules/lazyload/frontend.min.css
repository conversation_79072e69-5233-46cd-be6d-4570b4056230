.lazyloaded:not(.elementor-motion-effects-element-type-background),.lazyloaded>.elementor-motion-effects-container>.elementor-motion-effects-layer,.lazyloaded>[class*=-wrap]>.elementor-motion-effects-container>.elementor-motion-effects-layer,body.e-lazyload .e-con.lazyloaded:before,body.e-lazyload .lazyloaded,body.e-lazyload .lazyloaded .elementor-background-overlay{--e-bg-lazyload-loaded:var(--e-bg-lazyload)}