/*! elementor - v3.23.0 - 05-08-2024 */
"use strict";(self.webpackChunkelementor=self.webpackChunkelementor||[]).push([[192],{1351:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;class Progress extends elementorModules.frontend.handlers.Base{getDefaultSettings(){return{selectors:{progressNumber:".elementor-progress-bar"}}}getDefaultElements(){const e=this.getSettings("selectors");return{$progressNumber:this.$element.find(e.progressNumber)}}onInit(){super.onInit();this.createObserver().observe(this.elements.$progressNumber[0])}createObserver(){return new IntersectionObserver((e=>{e.forEach((e=>{if(e.isIntersecting){const e=this.elements.$progressNumber;e.css("width",e.data("max")+"%")}}))}),{root:null,threshold:0,rootMargin:"0px"})}}r.default=Progress}}]);