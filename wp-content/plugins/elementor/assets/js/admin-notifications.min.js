/*! elementor - v3.23.0 - 05-08-2024 */
(()=>{var e={61650:(e,t,r)=>{"use strict";var s,n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})})(u,{QueryClient:()=>l.QueryClient,QueryClientProvider:()=>l.QueryClientProvider,createQueryClient:()=>createQueryClient,useInfiniteQuery:()=>l.useInfiniteQuery,useMutation:()=>l.useMutation,useQuery:()=>l.useQuery,useQueryClient:()=>l.useQueryClient}),e.exports=(s=u,((e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))o.call(e,u)||u===r||n(e,u,{get:()=>t[u],enumerable:!(s=i(t,u))||s.enumerable});return e})(n({},"__esModule",{value:!0}),s));var c=r(39937),l=r(39937);function createQueryClient(){return new c.QueryClient({defaultOptions:{queries:{refetchOnWindowFocus:!1,refetchOnReconnect:!1}}})}},5418:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getNotifications=void 0;t.getNotifications=function getNotifications(){return function request(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return new Promise((function(r,s){elementorCommon.ajax.addRequest(e,{success:r,error:s,data:t})}))}("notifications_get")}},13849:(e,t,r)=>{"use strict";var s=r(23615),n=r(73203),i=r(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.BarButtonNotification=void 0;var a=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==i(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var s={},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var o=n?Object.getOwnPropertyDescriptor(e,a):null;o&&(o.get||o.set)?Object.defineProperty(s,a,o):s[a]=e[a]}s.default=e,r&&r.set(e,s);return s}(r(87363)),o=n(r(40131)),u=r(49005),c=r(36626),l=r(17564);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}var h=function BarButtonNotification(e){var t=e.defaultIsRead,r=(0,a.useState)(!1),s=(0,o.default)(r,2),n=s[0],i=s[1],h=(0,a.useState)(t),p=(0,o.default)(h,2),f=p[0],d=p[1];return a.default.createElement(a.default.Fragment,null,a.default.createElement("button",{className:"e-admin-top-bar__bar-button",style:{backgroundColor:"transparent",border:"none"},onClick:function onClick(e){e.preventDefault(),i(!0)}},a.default.createElement(c.Badge,{color:"primary",variant:"dot",invisible:f,sx:{mx:.5}},a.default.createElement(l.GiftIcon,{fontSize:"inherit"})),a.default.createElement("span",{className:"e-admin-top-bar__bar-button-title"},e.children)),a.default.createElement(u.WhatsNew,{isOpen:n,setIsOpen:i,setIsRead:d}))};t.BarButtonNotification=h,h.propTypes={defaultIsRead:s.bool,children:s.any.isRequired}},65059:(e,t,r)=>{"use strict";var s=r(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.WhatsNewDrawerContent=void 0;var n=s(r(87363)),i=r(61650),a=r(5418),o=r(36626),u=r(38308);t.WhatsNewDrawerContent=function WhatsNewDrawerContent(){var e=(0,i.useQuery)({queryKey:["e-notifications"],queryFn:a.getNotifications}),t=e.isPending,r=e.error,s=e.data;return t?n.default.createElement(o.Box,null,n.default.createElement(o.LinearProgress,{color:"secondary"})):r?n.default.createElement(o.Box,null,"An error has occurred: ",r):s.map((function(e,t){return n.default.createElement(u.WhatsNewItem,{key:t,item:e,itemIndex:t,itemsLength:s.length})}))}},78596:(e,t,r)=>{"use strict";var s=r(23615),n=r(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.WhatsNewItemChips=void 0;var i=n(r(87363)),a=n(r(73119)),o=r(36626),u=function WhatsNewItemChips(e){var t=e.chipPlan,r=e.chipTags,s=e.itemIndex,n=[];return t&&n.push({color:"promotion",size:"small",label:t}),r&&r.forEach((function(e){n.push({variant:"outlined",size:"small",label:e})})),n.length?i.default.createElement(o.Stack,{direction:"row",flexWrap:"wrap",gap:1,sx:{pb:1}},n.map((function(e,t){return i.default.createElement(o.Chip,(0,a.default)({key:"chip-".concat(s).concat(t)},e))}))):null};t.WhatsNewItemChips=u,u.propTypes={chipPlan:s.string,chipTags:s.array,itemIndex:s.number.isRequired}},48235:(e,t,r)=>{"use strict";var s=r(23615),n=r(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.WhatsNewItemThumbnail=void 0;var i=n(r(87363)),a=r(36626),o=r(72331),u=function WhatsNewItemThumbnail(e){var t=e.imageSrc,r=e.title,s=e.link;return i.default.createElement(a.Box,{sx:{pb:2}},i.default.createElement(o.WrapperWithLink,{link:s},i.default.createElement("img",{src:t,alt:r,style:{maxWidth:"100%"}})))};t.WhatsNewItemThumbnail=u,u.propTypes={imageSrc:s.string.isRequired,title:s.string.isRequired,link:s.string}},87691:(e,t,r)=>{"use strict";var s=r(23615),n=r(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.WhatsNewItemTopicLine=void 0;var i=n(r(87363)),a=r(36626),o=function WhatsNewItemTopicLine(e){var t=e.topic,r=e.date;return i.default.createElement(a.Stack,{direction:"row",divider:i.default.createElement(a.Divider,{orientation:"vertical",flexItem:!0}),spacing:1,color:"text.tertiary",sx:{pb:1}},t&&i.default.createElement(a.Box,null,t),r&&i.default.createElement(a.Box,null,r))};t.WhatsNewItemTopicLine=o,o.propTypes={topic:s.string,date:s.string}},38308:(e,t,r)=>{"use strict";var s=r(23615),n=r(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.WhatsNewItem=void 0;var i=n(r(87363)),a=r(36626),o=r(87691),u=r(72331),c=r(48235),l=r(78596),h=function WhatsNewItem(e){var t=e.item,r=e.itemIndex,s=e.itemsLength;return i.default.createElement(a.Box,{key:r,display:"flex",flexDirection:"column",sx:{pt:2}},(t.topic||t.date)&&i.default.createElement(o.WhatsNewItemTopicLine,{topic:t.topic,date:t.date}),i.default.createElement(u.WrapperWithLink,{link:t.link},i.default.createElement(a.Typography,{variant:"subtitle1",sx:{pb:2}},t.title)),t.imageSrc&&i.default.createElement(c.WhatsNewItemThumbnail,{imageSrc:t.imageSrc,link:t.link,title:t.title}),i.default.createElement(l.WhatsNewItemChips,{chipPlan:t.chipPlan,chipTags:t.chipTags,itemIndex:r}),t.description&&i.default.createElement(a.Typography,{variant:"body2",color:"text.secondary",sx:{pb:2}},t.description,t.readMoreText&&i.default.createElement(i.default.Fragment,null," ",i.default.createElement(a.Link,{href:t.link,color:"info.main",target:"_blank"},t.readMoreText))),t.cta&&t.ctaLink&&i.default.createElement(a.Box,{sx:{pb:2}},i.default.createElement(a.Button,{href:t.ctaLink,target:"_blank",variant:"contained",size:"small",color:"promotion"},t.cta)),r!==s-1&&i.default.createElement(a.Divider,{sx:{my:1}}))};t.WhatsNewItem=h,h.propTypes={item:s.object.isRequired,itemIndex:s.number.isRequired,itemsLength:s.number.isRequired}},41500:(e,t,r)=>{"use strict";var s=r(23615),n=r(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.WhatsNewTopBar=void 0;var i=n(r(87363)),a=r(36626),o=r(38003),u=r(64888),c=function WhatsNewTopBar(e){var t=e.setIsOpen;return i.default.createElement(i.default.Fragment,null,i.default.createElement(a.AppBar,{elevation:0,position:"sticky",sx:{backgroundColor:"background.default"}},i.default.createElement(a.Toolbar,{variant:"dense"},i.default.createElement(a.Typography,{variant:"overline",sx:{flexGrow:1}},(0,o.__)("What's New","elementor")),i.default.createElement(a.IconButton,{"aria-label":"close",size:"small",onClick:function onClick(){return t(!1)}},i.default.createElement(u.XIcon,null)))),i.default.createElement(a.Divider,null))};t.WhatsNewTopBar=c,c.propTypes={setIsOpen:s.func.isRequired}},49005:(e,t,r)=>{"use strict";var s=r(23615),n=r(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.WhatsNew=void 0;var i=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==n(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var s={},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var o=i?Object.getOwnPropertyDescriptor(e,a):null;o&&(o.get||o.set)?Object.defineProperty(s,a,o):s[a]=e[a]}s.default=e,r&&r.set(e,s);return s}(r(87363)),a=r(36626),o=r(61650),u=r(41500),c=r(65059);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}var l=new o.QueryClient({defaultOptions:{queries:{refetchOnWindowFocus:!1,retry:!1,staleTime:18e5}}}),h=function WhatsNew(e){var t,r,s=e.isOpen,n=e.setIsOpen,h=e.setIsRead,p=e.anchorPosition,f=void 0===p?"right":p;return(0,i.useEffect)((function(){s&&h(!0)}),[s,h]),i.default.createElement(i.default.Fragment,null,i.default.createElement(o.QueryClientProvider,{client:l},i.default.createElement(a.DirectionProvider,{rtl:elementorCommon.config.isRTL},i.default.createElement(a.ThemeProvider,{colorScheme:(null===(t=window.elementor)||void 0===t||null===(r=t.getPreferences)||void 0===r?void 0:r.call(t,"ui_theme"))||"auto"},i.default.createElement(a.Drawer,{anchor:f,open:s,onClose:function onClose(){return n(!1)},ModalProps:{style:{zIndex:999999}}},i.default.createElement(a.Box,{sx:{width:320,backgroundColor:"background.default"},role:"presentation"},i.default.createElement(u.WhatsNewTopBar,{setIsOpen:n}),i.default.createElement(a.Box,{sx:{padding:"16px"}},i.default.createElement(c.WhatsNewDrawerContent,null))))))))};t.WhatsNew=h,h.propTypes={isOpen:s.bool.isRequired,setIsOpen:s.func.isRequired,setIsRead:s.func.isRequired,anchorPosition:s.oneOf(["left","top","right","bottom"])}},72331:(e,t,r)=>{"use strict";var s=r(23615),n=r(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.WrapperWithLink=void 0;var i=n(r(87363)),a=r(36626),o=function WrapperWithLink(e){var t=e.link,r=e.children;return t?i.default.createElement(a.Link,{href:t,target:"_blank",underline:"none",color:"inherit",sx:{"&:hover":{color:"inherit"}}},r):r};t.WrapperWithLink=o,o.propTypes={link:s.string,children:s.any.isRequired}},17564:(e,t,r)=>{"use strict";var s=r(73203),n=r(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.GiftIcon=void 0;var i=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==n(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var s={},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var o=i?Object.getOwnPropertyDescriptor(e,a):null;o&&(o.get||o.set)?Object.defineProperty(s,a,o):s[a]=e[a]}s.default=e,r&&r.set(e,s);return s}(r(87363)),a=s(r(73119)),o=r(36626);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}var u=(0,i.forwardRef)((function(e,t){return i.default.createElement(o.SvgIcon,(0,a.default)({viewBox:"0 0 24 24"},e,{ref:t}),i.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M9.65527 4.84484C8.95951 4.07178 8.20923 3.73771 7.51306 3.74984L7.5 3.75007C7.03587 3.75007 6.59075 3.93433 6.26256 4.26252C5.93437 4.59071 5.75 5.03583 5.75 5.49995C5.75 5.96408 5.93437 6.4092 6.26256 6.73739C6.59075 7.06558 7.03587 7.24995 7.5 7.24995C7.50295 7.24995 7.5059 7.24997 7.50884 7.25001H11.0002C10.6592 6.26394 10.1939 5.44328 9.65527 4.84484ZM11.25 8.75001V11.25H4C3.86193 11.25 3.75 11.1381 3.75 11V9.00001C3.75 8.86193 3.86193 8.75001 4 8.75001H11.25ZM4.25 12.75H4C3.0335 12.75 2.25 11.9665 2.25 11V9.00001C2.25 8.03351 3.0335 7.25001 4 7.25001H4.76141C4.43004 6.73144 4.25 6.12498 4.25 5.49995C4.25 4.638 4.59241 3.81135 5.2019 3.20186C5.80984 2.59392 6.63384 2.2517 7.49342 2.24996C8.72414 2.23069 9.86213 2.83242 10.7702 3.84139C11.2484 4.37275 11.6608 5.01284 12 5.73103C12.3392 5.01284 12.7516 4.37275 13.2298 3.84139C14.1379 2.83242 15.2759 2.23069 16.5066 2.24996C17.3662 2.2517 18.1902 2.59392 18.7981 3.20186C19.4076 3.81135 19.75 4.638 19.75 5.49995C19.75 6.12498 19.57 6.73144 19.2386 7.25001H20C20.9665 7.25001 21.75 8.03351 21.75 9.00001V11C21.75 11.9665 20.9665 12.75 20 12.75H19.75V19C19.75 19.7294 19.4603 20.4288 18.9445 20.9445C18.4288 21.4603 17.7293 21.75 17 21.75H7C6.27065 21.75 5.57118 21.4603 5.05546 20.9445C4.53973 20.4288 4.25 19.7294 4.25 19V12.75ZM11.25 20.25H7C6.66848 20.25 6.35054 20.1183 6.11612 19.8839C5.8817 19.6495 5.75 19.3315 5.75 19V12.75H11.25V20.25ZM12.75 20.25H17C17.3315 20.25 17.6495 20.1183 17.8839 19.8839C18.1183 19.6495 18.25 19.3315 18.25 19V12.75H12.75V20.25ZM12.75 11.25V8.75001H20C20.1381 8.75001 20.25 8.86193 20.25 9.00001V11C20.25 11.1381 20.1381 11.25 20 11.25H12.75ZM16.4912 7.25001C16.4941 7.24997 16.497 7.24995 16.5 7.24995C16.9641 7.24995 17.4092 7.06558 17.7374 6.73739C18.0656 6.4092 18.25 5.96408 18.25 5.49995C18.25 5.03583 18.0656 4.59071 17.7374 4.26252C17.4092 3.93433 16.9641 3.74995 16.5 3.74995H16.4869C15.7908 3.73783 15.0405 4.07178 14.3447 4.84484C13.8061 5.44328 13.3408 6.26394 12.9998 7.25001H16.4912Z"}))}));t.GiftIcon=u},64888:(e,t,r)=>{"use strict";var s=r(73203),n=r(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.XIcon=void 0;var i=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==n(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var s={},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var o=i?Object.getOwnPropertyDescriptor(e,a):null;o&&(o.get||o.set)?Object.defineProperty(s,a,o):s[a]=e[a]}s.default=e,r&&r.set(e,s);return s}(r(87363)),a=s(r(73119)),o=r(36626);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}var u=(0,i.forwardRef)((function(e,t){return i.default.createElement(o.SvgIcon,(0,a.default)({viewBox:"0 0 24 24"},e,{ref:t}),i.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M18.5303 5.46967C18.8232 5.76256 18.8232 6.23744 18.5303 6.53033L6.53033 18.5303C6.23744 18.8232 5.76256 18.8232 5.46967 18.5303C5.17678 18.2374 5.17678 17.7626 5.46967 17.4697L17.4697 5.46967C17.7626 5.17678 18.2374 5.17678 18.5303 5.46967Z"}),i.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M5.46967 5.46967C5.76256 5.17678 6.23744 5.17678 6.53033 5.46967L18.5303 17.4697C18.8232 17.7626 18.8232 18.2374 18.5303 18.5303C18.2374 18.8232 17.7626 18.8232 17.4697 18.5303L5.46967 6.53033C5.17678 6.23744 5.17678 5.76256 5.46967 5.46967Z"}))}));t.XIcon=u},58772:(e,t,r)=>{"use strict";var s=r(90331);function emptyFunction(){}function emptyFunctionWithReset(){}emptyFunctionWithReset.resetWarningCache=emptyFunction,e.exports=function(){function shim(e,t,r,n,i,a){if(a!==s){var o=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw o.name="Invariant Violation",o}}function getShim(){return shim}shim.isRequired=shim;var e={array:shim,bigint:shim,bool:shim,func:shim,number:shim,object:shim,string:shim,symbol:shim,any:shim,arrayOf:getShim,element:shim,elementType:shim,instanceOf:getShim,node:shim,objectOf:getShim,oneOf:getShim,oneOfType:getShim,shape:getShim,exact:getShim,checkPropTypes:emptyFunctionWithReset,resetWarningCache:emptyFunction};return e.PropTypes=e,e}},23615:(e,t,r)=>{e.exports=r(58772)()},90331:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},87363:e=>{"use strict";e.exports=React},36626:e=>{"use strict";e.exports=elementorV2.ui},38003:e=>{"use strict";e.exports=wp.i18n},98106:e=>{e.exports=function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,s=new Array(t);r<t;r++)s[r]=e[r];return s},e.exports.__esModule=!0,e.exports.default=e.exports},17358:e=>{e.exports=function _arrayWithHoles(e){if(Array.isArray(e))return e},e.exports.__esModule=!0,e.exports.default=e.exports},73119:e=>{function _extends(){return e.exports=_extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var s in r)Object.prototype.hasOwnProperty.call(r,s)&&(e[s]=r[s])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,_extends.apply(this,arguments)}e.exports=_extends,e.exports.__esModule=!0,e.exports.default=e.exports},73203:e=>{e.exports=function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports},40608:e=>{e.exports=function _iterableToArrayLimit(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var s,n,i,a,o=[],u=!0,c=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;u=!1}else for(;!(u=(s=i.call(r)).done)&&(o.push(s.value),o.length!==t);u=!0);}catch(e){c=!0,n=e}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw n}}return o}},e.exports.__esModule=!0,e.exports.default=e.exports},56894:e=>{e.exports=function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports},40131:(e,t,r)=>{var s=r(17358),n=r(40608),i=r(35068),a=r(56894);e.exports=function _slicedToArray(e,t){return s(e)||n(e,t)||i(e,t)||a()},e.exports.__esModule=!0,e.exports.default=e.exports},7501:e=>{function _typeof(t){return e.exports=_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,_typeof(t)}e.exports=_typeof,e.exports.__esModule=!0,e.exports.default=e.exports},35068:(e,t,r)=>{var s=r(98106);e.exports=function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return s(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?s(e,t):void 0}},e.exports.__esModule=!0,e.exports.default=e.exports},62238:(e,t,r)=>{"use strict";var s,n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})})(u,{FocusManager:()=>h,focusManager:()=>p}),e.exports=(s=u,((e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))o.call(e,u)||u===r||n(e,u,{get:()=>t[u],enumerable:!(s=i(t,u))||s.enumerable});return e})(n({},"__esModule",{value:!0}),s));var c=r(33949),l=r(14525),h=class extends c.Subscribable{#e;#t;#r;constructor(){super(),this.#r=e=>{if(!l.isServer&&window.addEventListener){const listener=()=>e();return window.addEventListener("visibilitychange",listener,!1),()=>{window.removeEventListener("visibilitychange",listener)}}}}onSubscribe(){this.#t||this.setEventListener(this.#r)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#r=e,this.#t?.(),this.#t=e((e=>{"boolean"==typeof e?this.setFocused(e):this.onFocus()}))}setFocused(e){this.#e!==e&&(this.#e=e,this.onFocus())}onFocus(){this.listeners.forEach((e=>{e()}))}isFocused(){return"boolean"==typeof this.#e?this.#e:"hidden"!==globalThis.document?.visibilityState}},p=new h},13654:e=>{"use strict";var t,r=Object.defineProperty,s=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,a={};function dehydrateMutation(e){return{mutationKey:e.options.mutationKey,state:e.state,...e.meta&&{meta:e.meta}}}function dehydrateQuery(e){return{state:e.state,queryKey:e.queryKey,queryHash:e.queryHash,...e.meta&&{meta:e.meta}}}function defaultShouldDehydrateMutation(e){return e.state.isPaused}function defaultShouldDehydrateQuery(e){return"success"===e.state.status}function dehydrate(e,t={}){const r=t.shouldDehydrateMutation??defaultShouldDehydrateMutation,s=e.getMutationCache().getAll().flatMap((e=>r(e)?[dehydrateMutation(e)]:[])),n=t.shouldDehydrateQuery??defaultShouldDehydrateQuery;return{mutations:s,queries:e.getQueryCache().getAll().flatMap((e=>n(e)?[dehydrateQuery(e)]:[]))}}function hydrate(e,t,r){if("object"!=typeof t||null===t)return;const s=e.getMutationCache(),n=e.getQueryCache(),i=t.mutations||[],a=t.queries||[];i.forEach((t=>{s.build(e,{...r?.defaultOptions?.mutations,mutationKey:t.mutationKey,meta:t.meta},t.state)})),a.forEach((({queryKey:t,state:s,queryHash:i,meta:a})=>{const o=n.get(i);if(o){if(o.state.dataUpdatedAt<s.dataUpdatedAt){const{fetchStatus:e,...t}=s;o.setState(t)}}else n.build(e,{...r?.defaultOptions?.queries,queryKey:t,queryHash:i,meta:a},{...s,fetchStatus:"idle"})}))}((e,t)=>{for(var s in t)r(e,s,{get:t[s],enumerable:!0})})(a,{defaultShouldDehydrateMutation:()=>defaultShouldDehydrateMutation,defaultShouldDehydrateQuery:()=>defaultShouldDehydrateQuery,dehydrate:()=>dehydrate,hydrate:()=>hydrate}),e.exports=(t=a,((e,t,a,o)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of n(t))i.call(e,u)||u===a||r(e,u,{get:()=>t[u],enumerable:!(o=s(t,u))||o.enumerable});return e})(r({},"__esModule",{value:!0}),t))},61528:(e,t,r)=>{"use strict";var s,n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))o.call(e,u)||u===r||n(e,u,{get:()=>t[u],enumerable:!(s=i(t,u))||s.enumerable});return e},u={};((e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})})(u,{CancelledError:()=>c.CancelledError,InfiniteQueryObserver:()=>d.InfiniteQueryObserver,MutationCache:()=>y.MutationCache,MutationObserver:()=>b.MutationObserver,QueriesObserver:()=>f.QueriesObserver,Query:()=>j.Query,QueryCache:()=>l.QueryCache,QueryClient:()=>h.QueryClient,QueryObserver:()=>p.QueryObserver,defaultShouldDehydrateMutation:()=>w.defaultShouldDehydrateMutation,defaultShouldDehydrateQuery:()=>w.defaultShouldDehydrateQuery,dehydrate:()=>w.dehydrate,focusManager:()=>O.focusManager,hashKey:()=>g.hashKey,hydrate:()=>w.hydrate,isCancelledError:()=>P.isCancelledError,isServer:()=>g.isServer,keepPreviousData:()=>g.keepPreviousData,matchQuery:()=>g.matchQuery,notifyManager:()=>m.notifyManager,onlineManager:()=>v.onlineManager,replaceEqualDeep:()=>g.replaceEqualDeep}),e.exports=(s=u,__copyProps(n({},"__esModule",{value:!0}),s));var c=r(71739),l=r(17029),h=r(33489),p=r(59716),f=r(65631),d=r(77698),y=r(1516),b=r(17724),m=r(27842),O=r(62238),v=r(11044),g=r(14525),P=r(71739),w=r(13654);((e,t,r)=>{__copyProps(e,t,"default"),r&&__copyProps(r,t,"default")})(u,r(53749),e.exports);var j=r(6881)},13144:(e,t,r)=>{"use strict";var s,n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})})(u,{hasNextPage:()=>hasNextPage,hasPreviousPage:()=>hasPreviousPage,infiniteQueryBehavior:()=>infiniteQueryBehavior}),e.exports=(s=u,((e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))o.call(e,u)||u===r||n(e,u,{get:()=>t[u],enumerable:!(s=i(t,u))||s.enumerable});return e})(n({},"__esModule",{value:!0}),s));var c=r(14525);function infiniteQueryBehavior(e){return{onFetch:(t,r)=>{const fetchFn=async()=>{const r=t.options,s=t.fetchOptions?.meta?.fetchMore?.direction,n=t.state.data?.pages||[],i=t.state.data?.pageParams||[],a={pages:[],pageParams:[]};let o=!1;const u=t.options.queryFn||(()=>Promise.reject(new Error(`Missing queryFn: '${t.options.queryHash}'`))),fetchPage=async(e,r,s)=>{if(o)return Promise.reject();if(null==r&&e.pages.length)return Promise.resolve(e);const n={queryKey:t.queryKey,pageParam:r,direction:s?"backward":"forward",meta:t.options.meta};var i;i=n,Object.defineProperty(i,"signal",{enumerable:!0,get:()=>(t.signal.aborted?o=!0:t.signal.addEventListener("abort",(()=>{o=!0})),t.signal)});const a=await u(n),{maxPages:l}=t.options,h=s?c.addToStart:c.addToEnd;return{pages:h(e.pages,a,l),pageParams:h(e.pageParams,r,l)}};let l;if(s&&n.length){const e="backward"===s,t={pages:n,pageParams:i},a=(e?getPreviousPageParam:getNextPageParam)(r,t);l=await fetchPage(t,a,e)}else{l=await fetchPage(a,i[0]??r.initialPageParam);const t=e??n.length;for(let e=1;e<t;e++){const e=getNextPageParam(r,l);l=await fetchPage(l,e)}}return l};t.options.persister?t.fetchFn=()=>t.options.persister?.(fetchFn,{queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},r):t.fetchFn=fetchFn}}}function getNextPageParam(e,{pages:t,pageParams:r}){const s=t.length-1;return e.getNextPageParam(t[s],t,r[s],r)}function getPreviousPageParam(e,{pages:t,pageParams:r}){return e.getPreviousPageParam?.(t[0],t,r[0],r)}function hasNextPage(e,t){return!!t&&null!=getNextPageParam(e,t)}function hasPreviousPage(e,t){return!(!t||!e.getPreviousPageParam)&&null!=getPreviousPageParam(e,t)}},77698:(e,t,r)=>{"use strict";var s,n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})})(u,{InfiniteQueryObserver:()=>h}),e.exports=(s=u,((e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))o.call(e,u)||u===r||n(e,u,{get:()=>t[u],enumerable:!(s=i(t,u))||s.enumerable});return e})(n({},"__esModule",{value:!0}),s));var c=r(59716),l=r(13144),h=class extends c.QueryObserver{constructor(e,t){super(e,t)}bindMethods(){super.bindMethods(),this.fetchNextPage=this.fetchNextPage.bind(this),this.fetchPreviousPage=this.fetchPreviousPage.bind(this)}setOptions(e,t){super.setOptions({...e,behavior:(0,l.infiniteQueryBehavior)()},t)}getOptimisticResult(e){return e.behavior=(0,l.infiniteQueryBehavior)(),super.getOptimisticResult(e)}fetchNextPage(e){return this.fetch({...e,meta:{fetchMore:{direction:"forward"}}})}fetchPreviousPage(e){return this.fetch({...e,meta:{fetchMore:{direction:"backward"}}})}createResult(e,t){const{state:r}=e,s=super.createResult(e,t),{isFetching:n,isRefetching:i}=s,a=n&&"forward"===r.fetchMeta?.fetchMore?.direction,o=n&&"backward"===r.fetchMeta?.fetchMore?.direction;return{...s,fetchNextPage:this.fetchNextPage,fetchPreviousPage:this.fetchPreviousPage,hasNextPage:(0,l.hasNextPage)(t,r.data),hasPreviousPage:(0,l.hasPreviousPage)(t,r.data),isFetchingNextPage:a,isFetchingPreviousPage:o,isRefetching:i&&!a&&!o}}}},4251:(e,t,r)=>{"use strict";var s,n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})})(u,{Mutation:()=>p,getDefaultState:()=>getDefaultState}),e.exports=(s=u,((e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))o.call(e,u)||u===r||n(e,u,{get:()=>t[u],enumerable:!(s=i(t,u))||s.enumerable});return e})(n({},"__esModule",{value:!0}),s));var c=r(27842),l=r(85603),h=r(71739),p=class extends l.Removable{constructor(e){super(),this.mutationId=e.mutationId,this.#s=e.defaultOptions,this.#n=e.mutationCache,this.#i=[],this.state=e.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(e.options),this.scheduleGc()}#i;#s;#n;#a;setOptions(e){this.options={...this.#s,...e},this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#i.includes(e)||(this.#i.push(e),this.clearGcTimeout(),this.#n.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#i=this.#i.filter((t=>t!==e)),this.scheduleGc(),this.#n.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#i.length||("pending"===this.state.status?this.scheduleGc():this.#n.remove(this))}continue(){return this.#a?.continue()??this.execute(this.state.variables)}async execute(e){const executeMutation=()=>(this.#a=(0,h.createRetryer)({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(new Error("No mutationFn found")),onFail:(e,t)=>{this.#o({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#o({type:"pause"})},onContinue:()=>{this.#o({type:"continue"})},retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode}),this.#a.promise),t="pending"===this.state.status;try{if(!t){this.#o({type:"pending",variables:e}),await(this.#n.config.onMutate?.(e,this));const t=await(this.options.onMutate?.(e));t!==this.state.context&&this.#o({type:"pending",context:t,variables:e})}const r=await executeMutation();return await(this.#n.config.onSuccess?.(r,e,this.state.context,this)),await(this.options.onSuccess?.(r,e,this.state.context)),await(this.#n.config.onSettled?.(r,null,this.state.variables,this.state.context,this)),await(this.options.onSettled?.(r,null,e,this.state.context)),this.#o({type:"success",data:r}),r}catch(t){try{throw await(this.#n.config.onError?.(t,e,this.state.context,this)),await(this.options.onError?.(t,e,this.state.context)),await(this.#n.config.onSettled?.(void 0,t,this.state.variables,this.state.context,this)),await(this.options.onSettled?.(void 0,t,e,this.state.context)),t}finally{this.#o({type:"error",error:t})}}}#o(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"pending":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:!(0,h.canFetch)(this.options.networkMode),status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),c.notifyManager.batch((()=>{this.#i.forEach((t=>{t.onMutationUpdate(e)})),this.#n.notify({mutation:this,type:"updated",action:e})}))}};function getDefaultState(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}},1516:(e,t,r)=>{"use strict";var s,n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})})(u,{MutationCache:()=>f}),e.exports=(s=u,((e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))o.call(e,u)||u===r||n(e,u,{get:()=>t[u],enumerable:!(s=i(t,u))||s.enumerable});return e})(n({},"__esModule",{value:!0}),s));var c=r(27842),l=r(4251),h=r(14525),p=r(33949),f=class extends p.Subscribable{constructor(e={}){super(),this.config=e,this.#u=[],this.#c=0}#u;#c;#l;build(e,t,r){const s=new l.Mutation({mutationCache:this,mutationId:++this.#c,options:e.defaultMutationOptions(t),state:r});return this.add(s),s}add(e){this.#u.push(e),this.notify({type:"added",mutation:e})}remove(e){this.#u=this.#u.filter((t=>t!==e)),this.notify({type:"removed",mutation:e})}clear(){c.notifyManager.batch((()=>{this.#u.forEach((e=>{this.remove(e)}))}))}getAll(){return this.#u}find(e){const t={exact:!0,...e};return this.#u.find((e=>(0,h.matchMutation)(t,e)))}findAll(e={}){return this.#u.filter((t=>(0,h.matchMutation)(e,t)))}notify(e){c.notifyManager.batch((()=>{this.listeners.forEach((t=>{t(e)}))}))}resumePausedMutations(){return this.#l=(this.#l??Promise.resolve()).then((()=>{const e=this.#u.filter((e=>e.state.isPaused));return c.notifyManager.batch((()=>e.reduce(((e,t)=>e.then((()=>t.continue().catch(h.noop)))),Promise.resolve())))})).then((()=>{this.#l=void 0})),this.#l}}},17724:(e,t,r)=>{"use strict";var s,n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})})(u,{MutationObserver:()=>f}),e.exports=(s=u,((e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))o.call(e,u)||u===r||n(e,u,{get:()=>t[u],enumerable:!(s=i(t,u))||s.enumerable});return e})(n({},"__esModule",{value:!0}),s));var c=r(4251),l=r(27842),h=r(33949),p=r(14525),f=class extends h.Subscribable{constructor(e,t){super(),this.#h=void 0,this.#p=e,this.setOptions(t),this.bindMethods(),this.#f()}#p;#h;#d;#y;bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){const t=this.options;this.options=this.#p.defaultMutationOptions(e),(0,p.shallowEqualObjects)(t,this.options)||this.#p.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#d,observer:this}),this.#d?.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#d?.removeObserver(this)}onMutationUpdate(e){this.#f(),this.#b(e)}getCurrentResult(){return this.#h}reset(){this.#d=void 0,this.#f(),this.#b()}mutate(e,t){return this.#y=t,this.#d?.removeObserver(this),this.#d=this.#p.getMutationCache().build(this.#p,this.options),this.#d.addObserver(this),this.#d.execute(e)}#f(){const e=this.#d?.state??(0,c.getDefaultState)();this.#h={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#b(e){l.notifyManager.batch((()=>{this.#y&&this.hasListeners()&&("success"===e?.type?(this.#y.onSuccess?.(e.data,this.#h.variables,this.#h.context),this.#y.onSettled?.(e.data,null,this.#h.variables,this.#h.context)):"error"===e?.type&&(this.#y.onError?.(e.error,this.#h.variables,this.#h.context),this.#y.onSettled?.(void 0,e.error,this.#h.variables,this.#h.context))),this.listeners.forEach((e=>{e(this.#h)}))}))}}},27842:(e,t,r)=>{"use strict";var s,n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})})(u,{createNotifyManager:()=>createNotifyManager,notifyManager:()=>l}),e.exports=(s=u,((e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))o.call(e,u)||u===r||n(e,u,{get:()=>t[u],enumerable:!(s=i(t,u))||s.enumerable});return e})(n({},"__esModule",{value:!0}),s));var c=r(14525);function createNotifyManager(){let e=[],t=0,notifyFn=e=>{e()},batchNotifyFn=e=>{e()};const schedule=r=>{t?e.push(r):(0,c.scheduleMicrotask)((()=>{notifyFn(r)}))},flush=()=>{const t=e;e=[],t.length&&(0,c.scheduleMicrotask)((()=>{batchNotifyFn((()=>{t.forEach((e=>{notifyFn(e)}))}))}))};return{batch:e=>{let r;t++;try{r=e()}finally{t--,t||flush()}return r},batchCalls:e=>(...t)=>{schedule((()=>{e(...t)}))},schedule,setNotifyFunction:e=>{notifyFn=e},setBatchNotifyFunction:e=>{batchNotifyFn=e}}}var l=createNotifyManager()},11044:(e,t,r)=>{"use strict";var s,n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})})(u,{OnlineManager:()=>h,onlineManager:()=>p}),e.exports=(s=u,((e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))o.call(e,u)||u===r||n(e,u,{get:()=>t[u],enumerable:!(s=i(t,u))||s.enumerable});return e})(n({},"__esModule",{value:!0}),s));var c=r(33949),l=r(14525),h=class extends c.Subscribable{#m=!0;#t;#r;constructor(){super(),this.#r=e=>{if(!l.isServer&&window.addEventListener){const onlineListener=()=>e(!0),offlineListener=()=>e(!1);return window.addEventListener("online",onlineListener,!1),window.addEventListener("offline",offlineListener,!1),()=>{window.removeEventListener("online",onlineListener),window.removeEventListener("offline",offlineListener)}}}}onSubscribe(){this.#t||this.setEventListener(this.#r)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#r=e,this.#t?.(),this.#t=e(this.setOnline.bind(this))}setOnline(e){this.#m!==e&&(this.#m=e,this.listeners.forEach((t=>{t(e)})))}isOnline(){return this.#m}},p=new h},65631:(e,t,r)=>{"use strict";var s,n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})})(u,{QueriesObserver:()=>f}),e.exports=(s=u,((e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))o.call(e,u)||u===r||n(e,u,{get:()=>t[u],enumerable:!(s=i(t,u))||s.enumerable});return e})(n({},"__esModule",{value:!0}),s));var c=r(27842),l=r(59716),h=r(33949),p=r(14525);function difference(e,t){return e.filter((e=>!t.includes(e)))}var f=class extends h.Subscribable{#p;#O;#v;#i;#g;#P;constructor(e,t,r){super(),this.#p=e,this.#v=[],this.#i=[],this.#w([]),this.setQueries(t,r)}#w(e){this.#O=e,this.#P=this.#j(e)}onSubscribe(){1===this.listeners.size&&this.#i.forEach((e=>{e.subscribe((t=>{this.#M(e,t)}))}))}onUnsubscribe(){this.listeners.size||this.destroy()}destroy(){this.listeners=new Set,this.#i.forEach((e=>{e.destroy()}))}setQueries(e,t,r){this.#v=e,this.#g=t,c.notifyManager.batch((()=>{const e=this.#i,t=this.#C(this.#v);t.forEach((e=>e.observer.setOptions(e.defaultedQueryOptions,r)));const s=t.map((e=>e.observer)),n=s.map((e=>e.getCurrentResult())),i=s.some(((t,r)=>t!==e[r]));(e.length!==s.length||i)&&(this.#i=s,this.#w(n),this.hasListeners()&&(difference(e,s).forEach((e=>{e.destroy()})),difference(s,e).forEach((e=>{e.subscribe((t=>{this.#M(e,t)}))})),this.#b()))}))}getCurrentResult(){return this.#P}getQueries(){return this.#i.map((e=>e.getCurrentQuery()))}getObservers(){return this.#i}getOptimisticResult(e){const t=this.#C(e),r=t.map((e=>e.observer.getOptimisticResult(e.defaultedQueryOptions)));return[r,e=>this.#j(e??r),()=>t.map(((e,t)=>{const s=r[t];return e.defaultedQueryOptions.notifyOnChangeProps?s:e.observer.trackResult(s)}))]}#j(e){const t=this.#g?.combine;return t?(0,p.replaceEqualDeep)(this.#P,t(e)):e}#C(e){const t=this.#i,r=new Map(t.map((e=>[e.options.queryHash,e]))),s=e.map((e=>this.#p.defaultQueryOptions(e))),n=s.flatMap((e=>{const t=r.get(e.queryHash);return null!=t?[{defaultedQueryOptions:e,observer:t}]:[]})),i=new Set(n.map((e=>e.defaultedQueryOptions.queryHash))),a=s.filter((e=>!i.has(e.queryHash))),getObserver=e=>{const t=this.#p.defaultQueryOptions(e);return this.#i.find((e=>e.options.queryHash===t.queryHash))??new l.QueryObserver(this.#p,t)},o=a.map((e=>({defaultedQueryOptions:e,observer:getObserver(e)})));return n.concat(o).sort(((e,t)=>s.indexOf(e.defaultedQueryOptions)-s.indexOf(t.defaultedQueryOptions)))}#M(e,t){const r=this.#i.indexOf(e);-1!==r&&(this.#w(function replaceAt(e,t,r){const s=e.slice(0);return s[t]=r,s}(this.#O,r,t)),this.#b())}#b(){c.notifyManager.batch((()=>{this.listeners.forEach((e=>{e(this.#O)}))}))}}},6881:(e,t,r)=>{"use strict";var s,n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})})(u,{Query:()=>f}),e.exports=(s=u,((e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))o.call(e,u)||u===r||n(e,u,{get:()=>t[u],enumerable:!(s=i(t,u))||s.enumerable});return e})(n({},"__esModule",{value:!0}),s));var c=r(14525),l=r(27842),h=r(71739),p=r(85603),f=class extends p.Removable{constructor(e){super(),this.#R=!1,this.#s=e.defaultOptions,this.#Q(e.options),this.#i=[],this.#_=e.cache,this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.#x=e.state||function getDefaultState(e){const t="function"==typeof e.initialData?e.initialData():e.initialData,r=void 0!==t,s=r?"function"==typeof e.initialDataUpdatedAt?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:r?s??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:r?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=this.#x,this.scheduleGc()}#x;#S;#_;#E;#a;#i;#s;#R;get meta(){return this.options.meta}#Q(e){this.options={...this.#s,...e},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.#i.length||"idle"!==this.state.fetchStatus||this.#_.remove(this)}setData(e,t){const r=(0,c.replaceData)(this.state.data,e,this.options);return this.#o({data:r,type:"success",dataUpdatedAt:t?.updatedAt,manual:t?.manual}),r}setState(e,t){this.#o({type:"setState",state:e,setStateOptions:t})}cancel(e){const t=this.#E;return this.#a?.cancel(e),t?t.then(c.noop).catch(c.noop):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#x)}isActive(){return this.#i.some((e=>!1!==e.options.enabled))}isDisabled(){return this.getObserversCount()>0&&!this.isActive()}isStale(){return this.state.isInvalidated||!this.state.dataUpdatedAt||this.#i.some((e=>e.getCurrentResult().isStale))}isStaleByTime(e=0){return this.state.isInvalidated||!this.state.dataUpdatedAt||!(0,c.timeUntilStale)(this.state.dataUpdatedAt,e)}onFocus(){const e=this.#i.find((e=>e.shouldFetchOnWindowFocus()));e?.refetch({cancelRefetch:!1}),this.#a?.continue()}onOnline(){const e=this.#i.find((e=>e.shouldFetchOnReconnect()));e?.refetch({cancelRefetch:!1}),this.#a?.continue()}addObserver(e){this.#i.includes(e)||(this.#i.push(e),this.clearGcTimeout(),this.#_.notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.#i.includes(e)&&(this.#i=this.#i.filter((t=>t!==e)),this.#i.length||(this.#a&&(this.#R?this.#a.cancel({revert:!0}):this.#a.cancelRetry()),this.scheduleGc()),this.#_.notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.#i.length}invalidate(){this.state.isInvalidated||this.#o({type:"invalidate"})}fetch(e,t){if("idle"!==this.state.fetchStatus)if(this.state.dataUpdatedAt&&t?.cancelRefetch)this.cancel({silent:!0});else if(this.#E)return this.#a?.continueRetry(),this.#E;if(e&&this.#Q(e),!this.options.queryFn){const e=this.#i.find((e=>e.options.queryFn));e&&this.#Q(e.options)}const r=new AbortController,s={queryKey:this.queryKey,meta:this.meta},addSignalProperty=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(this.#R=!0,r.signal)})};addSignalProperty(s);const n={fetchOptions:t,options:this.options,queryKey:this.queryKey,state:this.state,fetchFn:()=>this.options.queryFn?(this.#R=!1,this.options.persister?this.options.persister(this.options.queryFn,s,this):this.options.queryFn(s)):Promise.reject(new Error(`Missing queryFn: '${this.options.queryHash}'`))};addSignalProperty(n),this.options.behavior?.onFetch(n,this),this.#S=this.state,"idle"!==this.state.fetchStatus&&this.state.fetchMeta===n.fetchOptions?.meta||this.#o({type:"fetch",meta:n.fetchOptions?.meta});const onError=e=>{(0,h.isCancelledError)(e)&&e.silent||this.#o({type:"error",error:e}),(0,h.isCancelledError)(e)||(this.#_.config.onError?.(e,this),this.#_.config.onSettled?.(this.state.data,e,this)),this.isFetchingOptimistic||this.scheduleGc(),this.isFetchingOptimistic=!1};return this.#a=(0,h.createRetryer)({fn:n.fetchFn,abort:r.abort.bind(r),onSuccess:e=>{void 0!==e?(this.setData(e),this.#_.config.onSuccess?.(e,this),this.#_.config.onSettled?.(e,this.state.error,this),this.isFetchingOptimistic||this.scheduleGc(),this.isFetchingOptimistic=!1):onError(new Error(`${this.queryHash} data is undefined`))},onError,onFail:(e,t)=>{this.#o({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#o({type:"pause"})},onContinue:()=>{this.#o({type:"continue"})},retry:n.options.retry,retryDelay:n.options.retryDelay,networkMode:n.options.networkMode}),this.#E=this.#a.promise,this.#E}#o(e){this.state=(t=>{switch(e.type){case"failed":return{...t,fetchFailureCount:e.failureCount,fetchFailureReason:e.error};case"pause":return{...t,fetchStatus:"paused"};case"continue":return{...t,fetchStatus:"fetching"};case"fetch":return{...t,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:e.meta??null,fetchStatus:(0,h.canFetch)(this.options.networkMode)?"fetching":"paused",...!t.dataUpdatedAt&&{error:null,status:"pending"}};case"success":return{...t,data:e.data,dataUpdateCount:t.dataUpdateCount+1,dataUpdatedAt:e.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const r=e.error;return(0,h.isCancelledError)(r)&&r.revert&&this.#S?{...this.#S,fetchStatus:"idle"}:{...t,error:r,errorUpdateCount:t.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:t.fetchFailureCount+1,fetchFailureReason:r,fetchStatus:"idle",status:"error"};case"invalidate":return{...t,isInvalidated:!0};case"setState":return{...t,...e.state}}})(this.state),l.notifyManager.batch((()=>{this.#i.forEach((e=>{e.onQueryUpdate()})),this.#_.notify({query:this,type:"updated",action:e})}))}}},17029:(e,t,r)=>{"use strict";var s,n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})})(u,{QueryCache:()=>f}),e.exports=(s=u,((e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))o.call(e,u)||u===r||n(e,u,{get:()=>t[u],enumerable:!(s=i(t,u))||s.enumerable});return e})(n({},"__esModule",{value:!0}),s));var c=r(14525),l=r(6881),h=r(27842),p=r(33949),f=class extends p.Subscribable{constructor(e={}){super(),this.config=e,this.#v=new Map}#v;build(e,t,r){const s=t.queryKey,n=t.queryHash??(0,c.hashQueryKeyByOptions)(s,t);let i=this.get(n);return i||(i=new l.Query({cache:this,queryKey:s,queryHash:n,options:e.defaultQueryOptions(t),state:r,defaultOptions:e.getQueryDefaults(s)}),this.add(i)),i}add(e){this.#v.has(e.queryHash)||(this.#v.set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){const t=this.#v.get(e.queryHash);t&&(e.destroy(),t===e&&this.#v.delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){h.notifyManager.batch((()=>{this.getAll().forEach((e=>{this.remove(e)}))}))}get(e){return this.#v.get(e)}getAll(){return[...this.#v.values()]}find(e){const t={exact:!0,...e};return this.getAll().find((e=>(0,c.matchQuery)(t,e)))}findAll(e={}){const t=this.getAll();return Object.keys(e).length>0?t.filter((t=>(0,c.matchQuery)(e,t))):t}notify(e){h.notifyManager.batch((()=>{this.listeners.forEach((t=>{t(e)}))}))}onFocus(){h.notifyManager.batch((()=>{this.getAll().forEach((e=>{e.onFocus()}))}))}onOnline(){h.notifyManager.batch((()=>{this.getAll().forEach((e=>{e.onOnline()}))}))}}},33489:(e,t,r)=>{"use strict";var s,n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})})(u,{QueryClient:()=>b}),e.exports=(s=u,((e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))o.call(e,u)||u===r||n(e,u,{get:()=>t[u],enumerable:!(s=i(t,u))||s.enumerable});return e})(n({},"__esModule",{value:!0}),s));var c=r(14525),l=r(17029),h=r(1516),p=r(62238),f=r(11044),d=r(27842),y=r(13144),b=class{#q;#n;#s;#D;#I;#T;#F;#N;constructor(e={}){this.#q=e.queryCache||new l.QueryCache,this.#n=e.mutationCache||new h.MutationCache,this.#s=e.defaultOptions||{},this.#D=new Map,this.#I=new Map,this.#T=0}mount(){this.#T++,1===this.#T&&(this.#F=p.focusManager.subscribe((()=>{p.focusManager.isFocused()&&(this.resumePausedMutations(),this.#q.onFocus())})),this.#N=f.onlineManager.subscribe((()=>{f.onlineManager.isOnline()&&(this.resumePausedMutations(),this.#q.onOnline())})))}unmount(){this.#T--,0===this.#T&&(this.#F?.(),this.#F=void 0,this.#N?.(),this.#N=void 0)}isFetching(e){return this.#q.findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return this.#n.findAll({...e,status:"pending"}).length}getQueryData(e){return this.#q.find({queryKey:e})?.state.data}ensureQueryData(e){const t=this.getQueryData(e.queryKey);return void 0!==t?Promise.resolve(t):this.fetchQuery(e)}getQueriesData(e){return this.getQueryCache().findAll(e).map((({queryKey:e,state:t})=>[e,t.data]))}setQueryData(e,t,r){const s=this.#q.find({queryKey:e}),n=s?.state.data,i=(0,c.functionalUpdate)(t,n);if(void 0===i)return;const a=this.defaultQueryOptions({queryKey:e});return this.#q.build(this,a).setData(i,{...r,manual:!0})}setQueriesData(e,t,r){return d.notifyManager.batch((()=>this.getQueryCache().findAll(e).map((({queryKey:e})=>[e,this.setQueryData(e,t,r)]))))}getQueryState(e){return this.#q.find({queryKey:e})?.state}removeQueries(e){const t=this.#q;d.notifyManager.batch((()=>{t.findAll(e).forEach((e=>{t.remove(e)}))}))}resetQueries(e,t){const r=this.#q,s={type:"active",...e};return d.notifyManager.batch((()=>(r.findAll(e).forEach((e=>{e.reset()})),this.refetchQueries(s,t))))}cancelQueries(e={},t={}){const r={revert:!0,...t},s=d.notifyManager.batch((()=>this.#q.findAll(e).map((e=>e.cancel(r)))));return Promise.all(s).then(c.noop).catch(c.noop)}invalidateQueries(e={},t={}){return d.notifyManager.batch((()=>{if(this.#q.findAll(e).forEach((e=>{e.invalidate()})),"none"===e.refetchType)return Promise.resolve();const r={...e,type:e.refetchType??e.type??"active"};return this.refetchQueries(r,t)}))}refetchQueries(e={},t){const r={...t,cancelRefetch:t?.cancelRefetch??!0},s=d.notifyManager.batch((()=>this.#q.findAll(e).filter((e=>!e.isDisabled())).map((e=>{let t=e.fetch(void 0,r);return r.throwOnError||(t=t.catch(c.noop)),"paused"===e.state.fetchStatus?Promise.resolve():t}))));return Promise.all(s).then(c.noop)}fetchQuery(e){const t=this.defaultQueryOptions(e);void 0===t.retry&&(t.retry=!1);const r=this.#q.build(this,t);return r.isStaleByTime(t.staleTime)?r.fetch(t):Promise.resolve(r.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(c.noop).catch(c.noop)}fetchInfiniteQuery(e){return e.behavior=(0,y.infiniteQueryBehavior)(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(c.noop).catch(c.noop)}resumePausedMutations(){return this.#n.resumePausedMutations()}getQueryCache(){return this.#q}getMutationCache(){return this.#n}getDefaultOptions(){return this.#s}setDefaultOptions(e){this.#s=e}setQueryDefaults(e,t){this.#D.set((0,c.hashKey)(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...this.#D.values()];let r={};return t.forEach((t=>{(0,c.partialMatchKey)(e,t.queryKey)&&(r={...r,...t.defaultOptions})})),r}setMutationDefaults(e,t){this.#I.set((0,c.hashKey)(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...this.#I.values()];let r={};return t.forEach((t=>{(0,c.partialMatchKey)(e,t.mutationKey)&&(r={...r,...t.defaultOptions})})),r}defaultQueryOptions(e){if(e?._defaulted)return e;const t={...this.#s.queries,...e?.queryKey&&this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=(0,c.hashQueryKeyByOptions)(t.queryKey,t)),void 0===t.refetchOnReconnect&&(t.refetchOnReconnect="always"!==t.networkMode),void 0===t.throwOnError&&(t.throwOnError=!!t.suspense),void 0===t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t}defaultMutationOptions(e){return e?._defaulted?e:{...this.#s.mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){this.#q.clear(),this.#n.clear()}}},59716:(e,t,r)=>{"use strict";var s,n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})})(u,{QueryObserver:()=>d}),e.exports=(s=u,((e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))o.call(e,u)||u===r||n(e,u,{get:()=>t[u],enumerable:!(s=i(t,u))||s.enumerable});return e})(n({},"__esModule",{value:!0}),s));var c=r(14525),l=r(27842),h=r(62238),p=r(33949),f=r(71739),d=class extends p.Subscribable{constructor(e,t){super(),this.#k=void 0,this.#W=void 0,this.#h=void 0,this.#A=new Set,this.#p=e,this.options=t,this.#B=null,this.bindMethods(),this.setOptions(t)}#p;#k;#W;#h;#U;#K;#B;#L;#H;#V;#G;#z;#Z;#A;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(this.#k.addObserver(this),shouldFetchOnMount(this.#k,this.options)&&this.#$(),this.#X())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return shouldFetchOn(this.#k,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return shouldFetchOn(this.#k,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#J(),this.#Y(),this.#k.removeObserver(this)}setOptions(e,t){const r=this.options,s=this.#k;if(this.options=this.#p.defaultQueryOptions(e),(0,c.shallowEqualObjects)(r,this.options)||this.#p.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#k,observer:this}),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled)throw new Error("Expected enabled to be a boolean");this.options.queryKey||(this.options.queryKey=r.queryKey),this.#ee();const n=this.hasListeners();n&&shouldFetchOptionally(this.#k,s,this.options,r)&&this.#$(),this.updateResult(t),!n||this.#k===s&&this.options.enabled===r.enabled&&this.options.staleTime===r.staleTime||this.#te();const i=this.#re();!n||this.#k===s&&this.options.enabled===r.enabled&&i===this.#Z||this.#se(i)}getOptimisticResult(e){const t=this.#p.getQueryCache().build(this.#p,e),r=this.createResult(t,e);return function shouldAssignObserverCurrentProperties(e,t){if(!(0,c.shallowEqualObjects)(e.getCurrentResult(),t))return!0;return!1}(this,r)&&(this.#h=r,this.#K=this.options,this.#U=this.#k.state),r}getCurrentResult(){return this.#h}trackResult(e){const t={};return Object.keys(e).forEach((r=>{Object.defineProperty(t,r,{configurable:!1,enumerable:!0,get:()=>(this.#A.add(r),e[r])})})),t}getCurrentQuery(){return this.#k}refetch({...e}={}){return this.fetch({...e})}fetchOptimistic(e){const t=this.#p.defaultQueryOptions(e),r=this.#p.getQueryCache().build(this.#p,t);return r.isFetchingOptimistic=!0,r.fetch().then((()=>this.createResult(r,t)))}fetch(e){return this.#$({...e,cancelRefetch:e.cancelRefetch??!0}).then((()=>(this.updateResult(),this.#h)))}#$(e){this.#ee();let t=this.#k.fetch(this.options,e);return e?.throwOnError||(t=t.catch(c.noop)),t}#te(){if(this.#J(),c.isServer||this.#h.isStale||!(0,c.isValidTimeout)(this.options.staleTime))return;const e=(0,c.timeUntilStale)(this.#h.dataUpdatedAt,this.options.staleTime)+1;this.#G=setTimeout((()=>{this.#h.isStale||this.updateResult()}),e)}#re(){return("function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.#k):this.options.refetchInterval)??!1}#se(e){this.#Y(),this.#Z=e,!c.isServer&&!1!==this.options.enabled&&(0,c.isValidTimeout)(this.#Z)&&0!==this.#Z&&(this.#z=setInterval((()=>{(this.options.refetchIntervalInBackground||h.focusManager.isFocused())&&this.#$()}),this.#Z))}#X(){this.#te(),this.#se(this.#re())}#J(){this.#G&&(clearTimeout(this.#G),this.#G=void 0)}#Y(){this.#z&&(clearInterval(this.#z),this.#z=void 0)}createResult(e,t){const r=this.#k,s=this.options,n=this.#h,i=this.#U,a=this.#K,o=e!==r?e.state:this.#W,{state:u}=e;let l,{error:h,errorUpdatedAt:p,fetchStatus:d,status:y}=u,b=!1;if(t._optimisticResults){const n=this.hasListeners(),i=!n&&shouldFetchOnMount(e,t),a=n&&shouldFetchOptionally(e,r,t,s);(i||a)&&(d=(0,f.canFetch)(e.options.networkMode)?"fetching":"paused",u.dataUpdatedAt||(y="pending")),"isRestoring"===t._optimisticResults&&(d="idle")}if(t.select&&void 0!==u.data)if(n&&u.data===i?.data&&t.select===this.#L)l=this.#H;else try{this.#L=t.select,l=t.select(u.data),l=(0,c.replaceData)(n?.data,l,t),this.#H=l,this.#B=null}catch(e){this.#B=e}else l=u.data;if(void 0!==t.placeholderData&&void 0===l&&"pending"===y){let e;if(n?.isPlaceholderData&&t.placeholderData===a?.placeholderData)e=n.data;else if(e="function"==typeof t.placeholderData?t.placeholderData(this.#V?.state.data,this.#V):t.placeholderData,t.select&&void 0!==e)try{e=t.select(e),this.#B=null}catch(e){this.#B=e}void 0!==e&&(y="success",l=(0,c.replaceData)(n?.data,e,t),b=!0)}this.#B&&(h=this.#B,l=this.#H,p=Date.now(),y="error");const m="fetching"===d,O="pending"===y,v="error"===y,g=O&&m;return{status:y,fetchStatus:d,isPending:O,isSuccess:"success"===y,isError:v,isInitialLoading:g,isLoading:g,data:l,dataUpdatedAt:u.dataUpdatedAt,error:h,errorUpdatedAt:p,failureCount:u.fetchFailureCount,failureReason:u.fetchFailureReason,errorUpdateCount:u.errorUpdateCount,isFetched:u.dataUpdateCount>0||u.errorUpdateCount>0,isFetchedAfterMount:u.dataUpdateCount>o.dataUpdateCount||u.errorUpdateCount>o.errorUpdateCount,isFetching:m,isRefetching:m&&!O,isLoadingError:v&&0===u.dataUpdatedAt,isPaused:"paused"===d,isPlaceholderData:b,isRefetchError:v&&0!==u.dataUpdatedAt,isStale:isStale(e,t),refetch:this.refetch}}updateResult(e){const t=this.#h,r=this.createResult(this.#k,this.options);if(this.#U=this.#k.state,this.#K=this.options,(0,c.shallowEqualObjects)(r,t))return;void 0!==this.#U.data&&(this.#V=this.#k),this.#h=r;const s={};!1!==e?.listeners&&(()=>{if(!t)return!0;const{notifyOnChangeProps:e}=this.options,r="function"==typeof e?e():e;if("all"===r||!r&&!this.#A.size)return!0;const s=new Set(r??this.#A);return this.options.throwOnError&&s.add("error"),Object.keys(this.#h).some((e=>{const r=e;return this.#h[r]!==t[r]&&s.has(r)}))})()&&(s.listeners=!0),this.#b({...s,...e})}#ee(){const e=this.#p.getQueryCache().build(this.#p,this.options);if(e===this.#k)return;const t=this.#k;this.#k=e,this.#W=e.state,this.hasListeners()&&(t?.removeObserver(this),e.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#X()}#b(e){l.notifyManager.batch((()=>{e.listeners&&this.listeners.forEach((e=>{e(this.#h)})),this.#p.getQueryCache().notify({query:this.#k,type:"observerResultsUpdated"})}))}};function shouldFetchOnMount(e,t){return function shouldLoadOnMount(e,t){return!(!1===t.enabled||e.state.dataUpdatedAt||"error"===e.state.status&&!1===t.retryOnMount)}(e,t)||e.state.dataUpdatedAt>0&&shouldFetchOn(e,t,t.refetchOnMount)}function shouldFetchOn(e,t,r){if(!1!==t.enabled){const s="function"==typeof r?r(e):r;return"always"===s||!1!==s&&isStale(e,t)}return!1}function shouldFetchOptionally(e,t,r,s){return!1!==r.enabled&&(e!==t||!1===s.enabled)&&(!r.suspense||"error"!==e.state.status)&&isStale(e,r)}function isStale(e,t){return e.isStaleByTime(t.staleTime)}},85603:(e,t,r)=>{"use strict";var s,n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})})(u,{Removable:()=>l}),e.exports=(s=u,((e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))o.call(e,u)||u===r||n(e,u,{get:()=>t[u],enumerable:!(s=i(t,u))||s.enumerable});return e})(n({},"__esModule",{value:!0}),s));var c=r(14525),l=class{#ne;destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),(0,c.isValidTimeout)(this.gcTime)&&(this.#ne=setTimeout((()=>{this.optionalRemove()}),this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(c.isServer?1/0:3e5))}clearGcTimeout(){this.#ne&&(clearTimeout(this.#ne),this.#ne=void 0)}}},71739:(e,t,r)=>{"use strict";var s,n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})})(u,{CancelledError:()=>p,canFetch:()=>canFetch,createRetryer:()=>createRetryer,isCancelledError:()=>isCancelledError}),e.exports=(s=u,((e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))o.call(e,u)||u===r||n(e,u,{get:()=>t[u],enumerable:!(s=i(t,u))||s.enumerable});return e})(n({},"__esModule",{value:!0}),s));var c=r(62238),l=r(11044),h=r(14525);function defaultRetryDelay(e){return Math.min(1e3*2**e,3e4)}function canFetch(e){return"online"!==(e??"online")||l.onlineManager.isOnline()}var p=class{constructor(e){this.revert=e?.revert,this.silent=e?.silent}};function isCancelledError(e){return e instanceof p}function createRetryer(e){let t,r,s,n=!1,i=0,a=!1;const o=new Promise(((e,t)=>{r=e,s=t})),shouldPause=()=>!c.focusManager.isFocused()||"always"!==e.networkMode&&!l.onlineManager.isOnline(),resolve=s=>{a||(a=!0,e.onSuccess?.(s),t?.(),r(s))},reject=r=>{a||(a=!0,e.onError?.(r),t?.(),s(r))},pause=()=>new Promise((r=>{t=e=>{const t=a||!shouldPause();return t&&r(e),t},e.onPause?.()})).then((()=>{t=void 0,a||e.onContinue?.()})),run=()=>{if(a)return;let t;try{t=e.fn()}catch(e){t=Promise.reject(e)}Promise.resolve(t).then(resolve).catch((t=>{if(a)return;const r=e.retry??(h.isServer?0:3),s=e.retryDelay??defaultRetryDelay,o="function"==typeof s?s(i,t):s,u=!0===r||"number"==typeof r&&i<r||"function"==typeof r&&r(i,t);!n&&u?(i++,e.onFail?.(i,t),(0,h.sleep)(o).then((()=>{if(shouldPause())return pause()})).then((()=>{n?reject(t):run()}))):reject(t)}))};return canFetch(e.networkMode)?run():pause().then(run),{promise:o,cancel:t=>{a||(reject(new p(t)),e.abort?.())},continue:()=>{const e=t?.();return e?o:Promise.resolve()},cancelRetry:()=>{n=!0},continueRetry:()=>{n=!1}}}},33949:e=>{"use strict";var t,r=Object.defineProperty,s=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,a={};((e,t)=>{for(var s in t)r(e,s,{get:t[s],enumerable:!0})})(a,{Subscribable:()=>o}),e.exports=(t=a,((e,t,a,o)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of n(t))i.call(e,u)||u===a||r(e,u,{get:()=>t[u],enumerable:!(o=s(t,u))||o.enumerable});return e})(r({},"__esModule",{value:!0}),t));var o=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}}},53749:e=>{"use strict";var t,r=Object.defineProperty,s=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty;e.exports=(t={},((e,t,a,o)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of n(t))i.call(e,u)||u===a||r(e,u,{get:()=>t[u],enumerable:!(o=s(t,u))||o.enumerable});return e})(r({},"__esModule",{value:!0}),t))},14525:e=>{"use strict";var t,r=Object.defineProperty,s=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,a={};((e,t)=>{for(var s in t)r(e,s,{get:t[s],enumerable:!0})})(a,{addToEnd:()=>addToEnd,addToStart:()=>addToStart,functionalUpdate:()=>functionalUpdate,hashKey:()=>hashKey,hashQueryKeyByOptions:()=>hashQueryKeyByOptions,isPlainArray:()=>isPlainArray,isPlainObject:()=>isPlainObject,isServer:()=>o,isValidTimeout:()=>isValidTimeout,keepPreviousData:()=>keepPreviousData,matchMutation:()=>matchMutation,matchQuery:()=>matchQuery,noop:()=>noop,partialMatchKey:()=>partialMatchKey,replaceData:()=>replaceData,replaceEqualDeep:()=>replaceEqualDeep,scheduleMicrotask:()=>scheduleMicrotask,shallowEqualObjects:()=>shallowEqualObjects,sleep:()=>sleep,timeUntilStale:()=>timeUntilStale}),e.exports=(t=a,((e,t,a,o)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of n(t))i.call(e,u)||u===a||r(e,u,{get:()=>t[u],enumerable:!(o=s(t,u))||o.enumerable});return e})(r({},"__esModule",{value:!0}),t));var o="undefined"==typeof window||"Deno"in window;function noop(){}function functionalUpdate(e,t){return"function"==typeof e?e(t):e}function isValidTimeout(e){return"number"==typeof e&&e>=0&&e!==1/0}function timeUntilStale(e,t){return Math.max(e+(t||0)-Date.now(),0)}function matchQuery(e,t){const{type:r="all",exact:s,fetchStatus:n,predicate:i,queryKey:a,stale:o}=e;if(a)if(s){if(t.queryHash!==hashQueryKeyByOptions(a,t.options))return!1}else if(!partialMatchKey(t.queryKey,a))return!1;if("all"!==r){const e=t.isActive();if("active"===r&&!e)return!1;if("inactive"===r&&e)return!1}return("boolean"!=typeof o||t.isStale()===o)&&((void 0===n||n===t.state.fetchStatus)&&!(i&&!i(t)))}function matchMutation(e,t){const{exact:r,status:s,predicate:n,mutationKey:i}=e;if(i){if(!t.options.mutationKey)return!1;if(r){if(hashKey(t.options.mutationKey)!==hashKey(i))return!1}else if(!partialMatchKey(t.options.mutationKey,i))return!1}return(!s||t.state.status===s)&&!(n&&!n(t))}function hashQueryKeyByOptions(e,t){return(t?.queryKeyHashFn||hashKey)(e)}function hashKey(e){return JSON.stringify(e,((e,t)=>isPlainObject(t)?Object.keys(t).sort().reduce(((e,r)=>(e[r]=t[r],e)),{}):t))}function partialMatchKey(e,t){return e===t||typeof e==typeof t&&(!(!e||!t||"object"!=typeof e||"object"!=typeof t)&&!Object.keys(t).some((r=>!partialMatchKey(e[r],t[r]))))}function replaceEqualDeep(e,t){if(e===t)return e;const r=isPlainArray(e)&&isPlainArray(t);if(r||isPlainObject(e)&&isPlainObject(t)){const s=r?e.length:Object.keys(e).length,n=r?t:Object.keys(t),i=n.length,a=r?[]:{};let o=0;for(let s=0;s<i;s++){const i=r?s:n[s];a[i]=replaceEqualDeep(e[i],t[i]),a[i]===e[i]&&o++}return s===i&&o===s?e:a}return t}function shallowEqualObjects(e,t){if(e&&!t||t&&!e)return!1;for(const r in e)if(e[r]!==t[r])return!1;return!0}function isPlainArray(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function isPlainObject(e){if(!hasObjectPrototype(e))return!1;const t=e.constructor;if(void 0===t)return!0;const r=t.prototype;return!!hasObjectPrototype(r)&&!!r.hasOwnProperty("isPrototypeOf")}function hasObjectPrototype(e){return"[object Object]"===Object.prototype.toString.call(e)}function sleep(e){return new Promise((t=>{setTimeout(t,e)}))}function scheduleMicrotask(e){sleep(0).then(e)}function replaceData(e,t,r){return"function"==typeof r.structuralSharing?r.structuralSharing(e,t):!1!==r.structuralSharing?replaceEqualDeep(e,t):t}function keepPreviousData(e){return e}function addToEnd(e,t,r=0){const s=[...e,t];return r&&s.length>r?s.slice(1):s}function addToStart(e,t,r=0){const s=[t,...e];return r&&s.length>r?s.slice(0,-1):s}},32969:(e,t,r)=>{"use strict";var s,n=Object.create,i=Object.defineProperty,a=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,u=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let n of o(t))c.call(e,n)||n===r||i(e,n,{get:()=>t[n],enumerable:!(s=a(t,n))||s.enumerable});return e},l={};((e,t)=>{for(var r in t)i(e,r,{get:t[r],enumerable:!0})})(l,{HydrationBoundary:()=>HydrationBoundary}),e.exports=(s=l,__copyProps(i({},"__esModule",{value:!0}),s));var h=((e,t,r)=>(r=null!=e?n(u(e)):{},__copyProps(!t&&e&&e.__esModule?r:i(r,"default",{value:e,enumerable:!0}),e)))(r(87363),1),p=r(61528),f=r(43653),HydrationBoundary=({children:e,options:t={},state:r,queryClient:s})=>{const n=(0,f.useQueryClient)(s),[i,a]=h.useState(),o=h.useRef(t);return o.current=t,h.useMemo((()=>{if(r){if("object"!=typeof r)return;const e=n.getQueryCache(),t=r.queries||[],s=[],u=[];for(const r of t){const t=e.get(r.queryHash);if(t){const e=r.state.dataUpdatedAt>t.state.dataUpdatedAt,s=i?.find((e=>e.queryHash===r.queryHash));e&&(!s||r.state.dataUpdatedAt>s.state.dataUpdatedAt)&&u.push(r)}else s.push(r)}s.length>0&&(0,p.hydrate)(n,{queries:s},o.current),u.length>0&&a((e=>e?[...e,...u]:u))}}),[n,i,r]),h.useEffect((()=>{i&&((0,p.hydrate)(n,{queries:i},o.current),a(void 0))}),[n,i]),e}},43653:(e,t,r)=>{"use strict";var s,n=Object.create,i=Object.defineProperty,a=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,u=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let n of o(t))c.call(e,n)||n===r||i(e,n,{get:()=>t[n],enumerable:!(s=a(t,n))||s.enumerable});return e},l={};((e,t)=>{for(var r in t)i(e,r,{get:t[r],enumerable:!0})})(l,{QueryClientContext:()=>p,QueryClientProvider:()=>QueryClientProvider,useQueryClient:()=>useQueryClient}),e.exports=(s=l,__copyProps(i({},"__esModule",{value:!0}),s));var h=((e,t,r)=>(r=null!=e?n(u(e)):{},__copyProps(!t&&e&&e.__esModule?r:i(r,"default",{value:e,enumerable:!0}),e)))(r(87363),1),p=h.createContext(void 0),useQueryClient=e=>{const t=h.useContext(p);if(e)return e;if(!t)throw new Error("No QueryClient set, use QueryClientProvider to set one");return t},QueryClientProvider=({client:e,children:t})=>(h.useEffect((()=>(e.mount(),()=>{e.unmount()})),[e]),h.createElement(p.Provider,{value:e},t))},15178:(e,t,r)=>{"use strict";var s,n=Object.create,i=Object.defineProperty,a=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,u=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let n of o(t))c.call(e,n)||n===r||i(e,n,{get:()=>t[n],enumerable:!(s=a(t,n))||s.enumerable});return e},l={};((e,t)=>{for(var r in t)i(e,r,{get:t[r],enumerable:!0})})(l,{QueryErrorResetBoundary:()=>QueryErrorResetBoundary,useQueryErrorResetBoundary:()=>useQueryErrorResetBoundary}),e.exports=(s=l,__copyProps(i({},"__esModule",{value:!0}),s));var h=((e,t,r)=>(r=null!=e?n(u(e)):{},__copyProps(!t&&e&&e.__esModule?r:i(r,"default",{value:e,enumerable:!0}),e)))(r(87363),1);function createValue(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}var p=h.createContext(createValue()),useQueryErrorResetBoundary=()=>h.useContext(p),QueryErrorResetBoundary=({children:e})=>{const[t]=h.useState((()=>createValue()));return h.createElement(p.Provider,{value:t},"function"==typeof e?e(t):e)}},94025:(e,t,r)=>{"use strict";var s,n=Object.create,i=Object.defineProperty,a=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,u=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let n of o(t))c.call(e,n)||n===r||i(e,n,{get:()=>t[n],enumerable:!(s=a(t,n))||s.enumerable});return e},l={};((e,t)=>{for(var r in t)i(e,r,{get:t[r],enumerable:!0})})(l,{ensurePreventErrorBoundaryRetry:()=>ensurePreventErrorBoundaryRetry,getHasError:()=>getHasError,useClearResetErrorBoundary:()=>useClearResetErrorBoundary}),e.exports=(s=l,__copyProps(i({},"__esModule",{value:!0}),s));var h=((e,t,r)=>(r=null!=e?n(u(e)):{},__copyProps(!t&&e&&e.__esModule?r:i(r,"default",{value:e,enumerable:!0}),e)))(r(87363),1),p=r(6609),ensurePreventErrorBoundaryRetry=(e,t)=>{(e.suspense||e.throwOnError)&&(t.isReset()||(e.retryOnMount=!1))},useClearResetErrorBoundary=e=>{h.useEffect((()=>{e.clearReset()}),[e])},getHasError=({result:e,errorResetBoundary:t,throwOnError:r,query:s})=>e.isError&&!t.isReset()&&!e.isFetching&&(0,p.shouldThrowError)(r,[e.error,s])},39937:(e,t,r)=>{"use strict";var s,n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))o.call(e,u)||u===r||n(e,u,{get:()=>t[u],enumerable:!(s=i(t,u))||s.enumerable});return e},__reExport=(e,t,r)=>(__copyProps(e,t,"default"),r&&__copyProps(r,t,"default")),u={};((e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})})(u,{HydrationBoundary:()=>m.HydrationBoundary,IsRestoringProvider:()=>j.IsRestoringProvider,QueryClientContext:()=>b.QueryClientContext,QueryClientProvider:()=>b.QueryClientProvider,QueryErrorResetBoundary:()=>O.QueryErrorResetBoundary,infiniteQueryOptions:()=>y.infiniteQueryOptions,queryOptions:()=>d.queryOptions,useInfiniteQuery:()=>w.useInfiniteQuery,useIsFetching:()=>v.useIsFetching,useIsMutating:()=>g.useIsMutating,useIsRestoring:()=>j.useIsRestoring,useMutation:()=>P.useMutation,useMutationState:()=>g.useMutationState,useQueries:()=>c.useQueries,useQuery:()=>l.useQuery,useQueryClient:()=>b.useQueryClient,useQueryErrorResetBoundary:()=>O.useQueryErrorResetBoundary,useSuspenseInfiniteQuery:()=>p.useSuspenseInfiniteQuery,useSuspenseQueries:()=>f.useSuspenseQueries,useSuspenseQuery:()=>h.useSuspenseQuery}),e.exports=(s=u,__copyProps(n({},"__esModule",{value:!0}),s)),__reExport(u,r(61528),e.exports),__reExport(u,r(429),e.exports);var c=r(25405),l=r(47313),h=r(29134),p=r(22715),f=r(15605),d=r(56701),y=r(19777),b=r(43653),m=r(32969),O=r(15178),v=r(43351),g=r(72836),P=r(89339),w=r(93217),j=r(46732)},19777:e=>{"use strict";var t,r=Object.defineProperty,s=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,a={};function infiniteQueryOptions(e){return e}((e,t)=>{for(var s in t)r(e,s,{get:t[s],enumerable:!0})})(a,{infiniteQueryOptions:()=>infiniteQueryOptions}),e.exports=(t=a,((e,t,a,o)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of n(t))i.call(e,u)||u===a||r(e,u,{get:()=>t[u],enumerable:!(o=s(t,u))||o.enumerable});return e})(r({},"__esModule",{value:!0}),t))},46732:(e,t,r)=>{"use strict";var s,n=Object.create,i=Object.defineProperty,a=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,u=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let n of o(t))c.call(e,n)||n===r||i(e,n,{get:()=>t[n],enumerable:!(s=a(t,n))||s.enumerable});return e},l={};((e,t)=>{for(var r in t)i(e,r,{get:t[r],enumerable:!0})})(l,{IsRestoringProvider:()=>f,useIsRestoring:()=>useIsRestoring}),e.exports=(s=l,__copyProps(i({},"__esModule",{value:!0}),s));var h=((e,t,r)=>(r=null!=e?n(u(e)):{},__copyProps(!t&&e&&e.__esModule?r:i(r,"default",{value:e,enumerable:!0}),e)))(r(87363),1),p=h.createContext(!1),useIsRestoring=()=>h.useContext(p),f=p.Provider},56701:e=>{"use strict";var t,r=Object.defineProperty,s=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,a={};function queryOptions(e){return e}((e,t)=>{for(var s in t)r(e,s,{get:t[s],enumerable:!0})})(a,{queryOptions:()=>queryOptions}),e.exports=(t=a,((e,t,a,o)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of n(t))i.call(e,u)||u===a||r(e,u,{get:()=>t[u],enumerable:!(o=s(t,u))||o.enumerable});return e})(r({},"__esModule",{value:!0}),t))},28495:e=>{"use strict";var t,r=Object.defineProperty,s=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,a={};((e,t)=>{for(var s in t)r(e,s,{get:t[s],enumerable:!0})})(a,{defaultThrowOnError:()=>defaultThrowOnError,ensureStaleTime:()=>ensureStaleTime,fetchOptimistic:()=>fetchOptimistic,shouldSuspend:()=>shouldSuspend,willFetch:()=>willFetch}),e.exports=(t=a,((e,t,a,o)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of n(t))i.call(e,u)||u===a||r(e,u,{get:()=>t[u],enumerable:!(o=s(t,u))||o.enumerable});return e})(r({},"__esModule",{value:!0}),t));var defaultThrowOnError=(e,t)=>void 0===t.state.data,ensureStaleTime=e=>{e.suspense&&"number"!=typeof e.staleTime&&(e.staleTime=1e3)},willFetch=(e,t)=>e.isLoading&&e.isFetching&&!t,shouldSuspend=(e,t,r)=>e?.suspense&&willFetch(t,r),fetchOptimistic=(e,t,r)=>t.fetchOptimistic(e).catch((()=>{r.clearReset()}))},429:e=>{"use strict";var t,r=Object.defineProperty,s=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty;e.exports=(t={},((e,t,a,o)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of n(t))i.call(e,u)||u===a||r(e,u,{get:()=>t[u],enumerable:!(o=s(t,u))||o.enumerable});return e})(r({},"__esModule",{value:!0}),t))},84238:(e,t,r)=>{"use strict";var s,n=Object.create,i=Object.defineProperty,a=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,u=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let n of o(t))c.call(e,n)||n===r||i(e,n,{get:()=>t[n],enumerable:!(s=a(t,n))||s.enumerable});return e},l={};((e,t)=>{for(var r in t)i(e,r,{get:t[r],enumerable:!0})})(l,{useBaseQuery:()=>useBaseQuery}),e.exports=(s=l,__copyProps(i({},"__esModule",{value:!0}),s));var h=((e,t,r)=>(r=null!=e?n(u(e)):{},__copyProps(!t&&e&&e.__esModule?r:i(r,"default",{value:e,enumerable:!0}),e)))(r(87363),1),p=r(61528),f=r(15178),d=r(43653),y=r(46732),b=r(94025),m=r(28495);function useBaseQuery(e,t,r){const s=(0,d.useQueryClient)(r),n=(0,y.useIsRestoring)(),i=(0,f.useQueryErrorResetBoundary)(),a=s.defaultQueryOptions(e);a._optimisticResults=n?"isRestoring":"optimistic",(0,m.ensureStaleTime)(a),(0,b.ensurePreventErrorBoundaryRetry)(a,i),(0,b.useClearResetErrorBoundary)(i);const[o]=h.useState((()=>new t(s,a))),u=o.getOptimisticResult(a);if(h.useSyncExternalStore(h.useCallback((e=>{const t=n?()=>{}:o.subscribe(p.notifyManager.batchCalls(e));return o.updateResult(),t}),[o,n]),(()=>o.getCurrentResult()),(()=>o.getCurrentResult())),h.useEffect((()=>{o.setOptions(a,{listeners:!1})}),[a,o]),(0,m.shouldSuspend)(a,u,n))throw(0,m.fetchOptimistic)(a,o,i);if((0,b.getHasError)({result:u,errorResetBoundary:i,throwOnError:a.throwOnError,query:o.getCurrentQuery()}))throw u.error;return a.notifyOnChangeProps?u:o.trackResult(u)}},93217:(e,t,r)=>{"use strict";var s,n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})})(u,{useInfiniteQuery:()=>useInfiniteQuery}),e.exports=(s=u,((e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))o.call(e,u)||u===r||n(e,u,{get:()=>t[u],enumerable:!(s=i(t,u))||s.enumerable});return e})(n({},"__esModule",{value:!0}),s));var c=r(61528),l=r(84238);function useInfiniteQuery(e,t){return(0,l.useBaseQuery)(e,c.InfiniteQueryObserver,t)}},43351:(e,t,r)=>{"use strict";var s,n=Object.create,i=Object.defineProperty,a=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,u=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let n of o(t))c.call(e,n)||n===r||i(e,n,{get:()=>t[n],enumerable:!(s=a(t,n))||s.enumerable});return e},l={};((e,t)=>{for(var r in t)i(e,r,{get:t[r],enumerable:!0})})(l,{useIsFetching:()=>useIsFetching}),e.exports=(s=l,__copyProps(i({},"__esModule",{value:!0}),s));var h=((e,t,r)=>(r=null!=e?n(u(e)):{},__copyProps(!t&&e&&e.__esModule?r:i(r,"default",{value:e,enumerable:!0}),e)))(r(87363),1),p=r(61528),f=r(43653);function useIsFetching(e,t){const r=(0,f.useQueryClient)(t),s=r.getQueryCache();return h.useSyncExternalStore(h.useCallback((e=>s.subscribe(p.notifyManager.batchCalls(e))),[s]),(()=>r.isFetching(e)),(()=>r.isFetching(e)))}},89339:(e,t,r)=>{"use strict";var s,n=Object.create,i=Object.defineProperty,a=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,u=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let n of o(t))c.call(e,n)||n===r||i(e,n,{get:()=>t[n],enumerable:!(s=a(t,n))||s.enumerable});return e},l={};((e,t)=>{for(var r in t)i(e,r,{get:t[r],enumerable:!0})})(l,{useMutation:()=>useMutation}),e.exports=(s=l,__copyProps(i({},"__esModule",{value:!0}),s));var h=((e,t,r)=>(r=null!=e?n(u(e)):{},__copyProps(!t&&e&&e.__esModule?r:i(r,"default",{value:e,enumerable:!0}),e)))(r(87363),1),p=r(61528),f=r(43653),d=r(6609);function useMutation(e,t){const r=(0,f.useQueryClient)(t),[s]=h.useState((()=>new p.MutationObserver(r,e)));h.useEffect((()=>{s.setOptions(e)}),[s,e]);const n=h.useSyncExternalStore(h.useCallback((e=>s.subscribe(p.notifyManager.batchCalls(e))),[s]),(()=>s.getCurrentResult()),(()=>s.getCurrentResult())),i=h.useCallback(((e,t)=>{s.mutate(e,t).catch(noop)}),[s]);if(n.error&&(0,d.shouldThrowError)(s.options.throwOnError,[n.error]))throw n.error;return{...n,mutate:i,mutateAsync:n.mutate}}function noop(){}},72836:(e,t,r)=>{"use strict";var s,n=Object.create,i=Object.defineProperty,a=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,u=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let n of o(t))c.call(e,n)||n===r||i(e,n,{get:()=>t[n],enumerable:!(s=a(t,n))||s.enumerable});return e},l={};((e,t)=>{for(var r in t)i(e,r,{get:t[r],enumerable:!0})})(l,{useIsMutating:()=>useIsMutating,useMutationState:()=>useMutationState}),e.exports=(s=l,__copyProps(i({},"__esModule",{value:!0}),s));var h=((e,t,r)=>(r=null!=e?n(u(e)):{},__copyProps(!t&&e&&e.__esModule?r:i(r,"default",{value:e,enumerable:!0}),e)))(r(87363),1),p=r(61528),f=r(43653);function useIsMutating(e,t){return useMutationState({filters:{...e,status:"pending"}},(0,f.useQueryClient)(t)).length}function getResult(e,t){return e.findAll(t.filters).map((e=>t.select?t.select(e):e.state))}function useMutationState(e={},t){const r=(0,f.useQueryClient)(t).getMutationCache(),s=h.useRef(e),n=h.useRef();return n.current||(n.current=getResult(r,e)),h.useEffect((()=>{s.current=e})),h.useSyncExternalStore(h.useCallback((e=>r.subscribe((()=>{const t=(0,p.replaceEqualDeep)(n.current,getResult(r,s.current));n.current!==t&&(n.current=t,p.notifyManager.schedule(e))}))),[r]),(()=>n.current),(()=>n.current))}},25405:(e,t,r)=>{"use strict";var s,n=Object.create,i=Object.defineProperty,a=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,u=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let n of o(t))c.call(e,n)||n===r||i(e,n,{get:()=>t[n],enumerable:!(s=a(t,n))||s.enumerable});return e},l={};((e,t)=>{for(var r in t)i(e,r,{get:t[r],enumerable:!0})})(l,{useQueries:()=>useQueries}),e.exports=(s=l,__copyProps(i({},"__esModule",{value:!0}),s));var h=((e,t,r)=>(r=null!=e?n(u(e)):{},__copyProps(!t&&e&&e.__esModule?r:i(r,"default",{value:e,enumerable:!0}),e)))(r(87363),1),p=r(61528),f=r(43653),d=r(46732),y=r(15178),b=r(94025),m=r(28495);function useQueries({queries:e,...t},r){const s=(0,f.useQueryClient)(r),n=(0,d.useIsRestoring)(),i=(0,y.useQueryErrorResetBoundary)(),a=h.useMemo((()=>e.map((e=>{const t=s.defaultQueryOptions(e);return t._optimisticResults=n?"isRestoring":"optimistic",t}))),[e,s,n]);a.forEach((e=>{(0,m.ensureStaleTime)(e),(0,b.ensurePreventErrorBoundaryRetry)(e,i)})),(0,b.useClearResetErrorBoundary)(i);const[o]=h.useState((()=>new p.QueriesObserver(s,a,t))),[u,c,l]=o.getOptimisticResult(a);h.useSyncExternalStore(h.useCallback((e=>n?()=>{}:o.subscribe(p.notifyManager.batchCalls(e))),[o,n]),(()=>o.getCurrentResult()),(()=>o.getCurrentResult())),h.useEffect((()=>{o.setQueries(a,t,{listeners:!1})}),[a,t,o]);const O=u.some(((e,t)=>(0,m.shouldSuspend)(a[t],e,n)))?u.flatMap(((e,t)=>{const r=a[t];if(r){const t=new p.QueryObserver(s,r);if((0,m.shouldSuspend)(r,e,n))return(0,m.fetchOptimistic)(r,t,i);(0,m.willFetch)(e,n)&&(0,m.fetchOptimistic)(r,t,i)}return[]})):[];if(O.length>0)throw Promise.all(O);const v=o.getQueries(),g=u.find(((e,t)=>(0,b.getHasError)({result:e,errorResetBoundary:i,throwOnError:a[t]?.throwOnError??!1,query:v[t]})));if(g?.error)throw g.error;return c(l())}},47313:(e,t,r)=>{"use strict";var s,n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})})(u,{useQuery:()=>useQuery}),e.exports=(s=u,((e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))o.call(e,u)||u===r||n(e,u,{get:()=>t[u],enumerable:!(s=i(t,u))||s.enumerable});return e})(n({},"__esModule",{value:!0}),s));var c=r(61528),l=r(84238);function useQuery(e,t){return(0,l.useBaseQuery)(e,c.QueryObserver,t)}},22715:(e,t,r)=>{"use strict";var s,n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})})(u,{useSuspenseInfiniteQuery:()=>useSuspenseInfiniteQuery}),e.exports=(s=u,((e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))o.call(e,u)||u===r||n(e,u,{get:()=>t[u],enumerable:!(s=i(t,u))||s.enumerable});return e})(n({},"__esModule",{value:!0}),s));var c=r(61528),l=r(84238),h=r(28495);function useSuspenseInfiniteQuery(e,t){return(0,l.useBaseQuery)({...e,enabled:!0,suspense:!0,throwOnError:h.defaultThrowOnError},c.InfiniteQueryObserver,t)}},15605:(e,t,r)=>{"use strict";var s,n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})})(u,{useSuspenseQueries:()=>useSuspenseQueries}),e.exports=(s=u,((e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))o.call(e,u)||u===r||n(e,u,{get:()=>t[u],enumerable:!(s=i(t,u))||s.enumerable});return e})(n({},"__esModule",{value:!0}),s));var c=r(25405),l=r(28495);function useSuspenseQueries(e,t){return(0,c.useQueries)({...e,queries:e.queries.map((e=>({...e,suspense:!0,throwOnError:l.defaultThrowOnError,enabled:!0})))},t)}},29134:(e,t,r)=>{"use strict";var s,n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})})(u,{useSuspenseQuery:()=>useSuspenseQuery}),e.exports=(s=u,((e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))o.call(e,u)||u===r||n(e,u,{get:()=>t[u],enumerable:!(s=i(t,u))||s.enumerable});return e})(n({},"__esModule",{value:!0}),s));var c=r(61528),l=r(84238),h=r(28495);function useSuspenseQuery(e,t){return(0,l.useBaseQuery)({...e,enabled:!0,suspense:!0,throwOnError:h.defaultThrowOnError},c.QueryObserver,t)}},6609:e=>{"use strict";var t,r=Object.defineProperty,s=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,a={};function shouldThrowError(e,t){return"function"==typeof e?e(...t):!!e}((e,t)=>{for(var s in t)r(e,s,{get:t[s],enumerable:!0})})(a,{shouldThrowError:()=>shouldThrowError}),e.exports=(t=a,((e,t,a,o)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of n(t))i.call(e,u)||u===a||r(e,u,{get:()=>t[u],enumerable:!(o=s(t,u))||o.enumerable});return e})(r({},"__esModule",{value:!0}),t))}},t={};function __webpack_require__(r){var s=t[r];if(void 0!==s)return s.exports;var n=t[r]={exports:{}};return e[r](n,n.exports,__webpack_require__),n.exports}(()=>{"use strict";var e=__webpack_require__(13849);window.elementorNotificationCenter={BarButtonNotification:e.BarButtonNotification}})()})();