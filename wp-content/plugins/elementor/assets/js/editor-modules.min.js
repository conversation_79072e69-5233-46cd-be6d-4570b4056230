/*! elementor - v3.23.0 - 05-08-2024 */
/*! For license information please see editor-modules.min.js.LICENSE.txt */
(()=>{var t={61909:(t,r,o)=>{"use strict";var i=o(38003).__,a=o(73203);Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var l=a(o(7501)),u=a(o(40131)),c=a(o(78983)),d=a(o(42081)),p=a(o(77266)),h=a(o(58724)),v=a(o(71173)),y=a(o(74910)),g=a(o(93231)),m=a(o(42618)),_=a(o(63225)),b=a(o(40871));function _createSuper(t){var r=function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function _createSuperInternal(){var o,i=(0,y.default)(t);if(r){var a=(0,y.default)(this).constructor;o=Reflect.construct(i,arguments,a)}else o=i.apply(this,arguments);return(0,v.default)(this,o)}}var C=function(t){(0,h.default)(Container,t);var r=_createSuper(Container);function Container(t){var o;if((0,c.default)(this,Container),o=r.call(this,t),(0,g.default)((0,p.default)(o),"type",void 0),(0,g.default)((0,p.default)(o),"id",void 0),(0,g.default)((0,p.default)(o),"document",void 0),(0,g.default)((0,p.default)(o),"model",void 0),(0,g.default)((0,p.default)(o),"settings",void 0),(0,g.default)((0,p.default)(o),"view",void 0),(0,g.default)((0,p.default)(o),"parent",void 0),(0,g.default)((0,p.default)(o),"children",new b.default),(0,g.default)((0,p.default)(o),"dynamic",void 0),(0,g.default)((0,p.default)(o),"globals",void 0),(0,g.default)((0,p.default)(o),"label",void 0),(0,g.default)((0,p.default)(o),"controls",{}),(0,g.default)((0,p.default)(o),"repeaters",{}),(0,g.default)((0,p.default)(o),"renderer",void 0),(0,g.default)((0,p.default)(o),"panel",void 0),(0,g.default)((0,p.default)(o),"placeholders",{}),o.validateArgs(t),0===(t=Object.entries(t)).length)throw Error("Container cannot be empty.");return t.forEach((function(t){var r=(0,u.default)(t,2),i=r[0],a=r[1];o[i]=void 0===a?o[i]:a})),void 0===o.renderer&&(o.renderer=(0,p.default)(o)),o.document||(o.document=elementor.documents.getCurrent()),o.dynamic=new Backbone.Model(o.settings.get("__dynamic__")),o.globals=new Backbone.Model(o.settings.get("__globals__")),o.panel=new _.default((0,p.default)(o)),o.initialize(),o}return(0,d.default)(Container,[{key:"initialize",value:function initialize(){this.isViewElement()&&(this.addToParent(),this.handleChildrenRecursive(),this.view.on("destroy",this.removeFromParent.bind(this))),this.handleRepeaterChildren()}},{key:"validateArgs",value:function validateArgs(t){this.requireArgumentType("type","string",t),this.requireArgumentType("id","string",t),this.requireArgumentInstance("settings",Backbone.Model,t),this.requireArgumentInstance("model",Backbone.Model,t),!1!==t.parent&&this.requireArgumentInstance("parent",elementorModules.editor.Container,t)}},{key:"getGroupRelatedControls",value:function getGroupRelatedControls(t){var r=this,o={};return Object.keys(t).forEach((function(t){Object.values(r.controls).forEach((function(i){var a;if(t===i.name)o[i.name]=i;else if(null!==(a=r.controls[t])&&void 0!==a&&a.groupPrefix){var l=r.controls[t].groupPrefix;i.name.toString().startsWith(l)&&(o[i.name]=i)}}))})),o}},{key:"getAffectingControls",value:function getAffectingControls(){var t=this,r={},o=this.settings.getActiveControls();return Object.entries(o).forEach((function(o){var i,a=(0,u.default)(o,2),c=a[0],d=a[1],p=t.settings.get(d.name);if(d.global&&(null==p||!p.length)&&(null!==(i=t.globals.get(d.name))&&void 0!==i&&i.length||t.getGlobalDefault(c).length))return d.global.utilized=!0,void(r[c]=d);if(d.dynamic&&t.dynamic.get(c))return d.dynamic.utilized=!0,void(r[c]=d);p!==d.default&&p&&("object"===(0,l.default)(p)&&Object.values(p).join()===Object.values(d.default).join()||(r[c]=d))})),r}},{key:"getParentAncestry",value:function getParentAncestry(){for(var t=[],r=this;r;)t.push(r),r=r.parent;return t}},{key:"handleChildrenRecursive",value:function handleChildrenRecursive(){var t;null!==(t=this.view.children)&&void 0!==t&&t.length?Object.values(this.view.children._views).forEach((function(t){if(t.container){var r=t.container;r.parent.children&&(r.parent.children[t._index]=r),r.handleChildrenRecursive()}})):this.children.clear()}},{key:"addToParent",value:function addToParent(){this.parent.children&&!this.isRepeaterItem()&&this.parent.children.splice(this.view._index,0,this)}},{key:"removeFromParent",value:function removeFromParent(){var t=this;this.parent.children&&!this.isRepeater()&&(this.parent.children=this.parent.children.filter((function(r){return r.id!==t.id})))}},{key:"handleRepeaterChildren",value:function handleRepeaterChildren(){var t=this;if(Object.values(this.controls).forEach((function(r){if(r.is_repeater){var o=new Backbone.Model({name:r.name});t.repeaters[r.name]=new elementorModules.editor.Container({type:Container.TYPE_REPEATER,id:r.name,model:o,settings:o,view:t.view,parent:t,label:r.label||r.name,controls:{},renderer:t.renderer}),t.settings.get(r.name).forEach((function(o,i){t.addRepeaterItem(r.name,o,i)}))}})),["widget","document"].includes(this.type)){var r=Object.values(this.controls).filter((function(t){return"repeater"===t.type}));this.model.get("supportRepeaterChildren")||1!==r.length||Object.defineProperty(this,"children",{get:function get(){return elementorDevTools.deprecation.deprecated("children","3.0.0","container.repeaters[ repeaterName ].children"),this.repeaters[r[0].name].children}})}}},{key:"addRepeaterItem",value:function addRepeaterItem(t,r,o){var a=r.get("_id");return a||(a="bc-"+elementorCommon.helpers.getUniqueId(),r.set("_id",a)),this.repeaters[t].children.splice(o,0,new elementorModules.editor.Container({type:Container.TYPE_REPEATER_ITEM,id:r.get("_id"),model:new Backbone.Model({name:t}),settings:r,view:this.view,parent:this.repeaters[t],label:this.label+" "+i("Item","elementor"),controls:r.options.controls,renderer:this.renderer})),this.repeaters[t]}},{key:"lookup",value:function lookup(){var t,r=this;if(!this.renderer)return this;if(this!==this.renderer&&null!==(t=this.renderer.view)&&void 0!==t&&t.isDisconnected&&this.renderer.view.isDisconnected()&&(this.renderer=this.renderer.lookup()),void 0===this.view||!this.view.lookup||!this.view.isDisconnected())return Container.TYPE_REPEATER_ITEM===this.type&&(this.settings=this.parent.parent.settings.get(this.model.get("name")).findWhere({_id:this.id})),r;var lookup=this.view.lookup();if(lookup){if(r=lookup.getContainer(),Container.REPEATER===this.type)return this.settings=r.settings.get(this.model.get("name")).findWhere({_id:this.id}),this;r.parent.children&&(r.parent.children[r.view._index]=r)}return r}},{key:"findChildrenRecursive",value:function findChildrenRecursive(t){return elementorDevTools.deprecation.deprecated("container.findChildrenRecursive( callback )","3.5.0","container.children.findRecursive( callback )"),this.children.findRecursive(t)}},{key:"forEachChildrenRecursive",value:function forEachChildrenRecursive(t){return elementorDevTools.deprecation.deprecated("container.forEachChildrenRecursive( callback )","3.5.0","container.children.forEachRecursive( callback )"),this.children.forEachRecursive(t)}},{key:"render",value:function render(){this.renderer&&this.renderer.view.renderOnChange(this.settings)}},{key:"renderUI",value:function renderUI(){this.renderer&&this.renderer.view.renderUI()}},{key:"isEditable",value:function isEditable(){return"edit"===elementor.channels.dataEditMode.request("activeMode")&&"open"===this.document.editor.status}},{key:"isDesignable",value:function isDesignable(){return elementor.userCan("design")&&this.isEditable()}},{key:"isGridContainer",value:function isGridContainer(){return"grid"===this.parent.settings.get("container_type")}},{key:"isLocked",value:function isLocked(){return this.model.get("isLocked")}},{key:"isRepeater",value:function isRepeater(){return Container.TYPE_REPEATER===this.type}},{key:"isRepeaterItem",value:function isRepeaterItem(){return Container.TYPE_REPEATER_ITEM===this.type}},{key:"isViewElement",value:function isViewElement(){return this.view&&this.model.get("elType")}},{key:"getSetting",value:function getSetting(t){var r,o=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=this.settings.get(t);return o?i:(this.getGlobalKey(t)&&(r=this.getGlobalValue(t)),r||i||this.getGlobalDefault(t))}},{key:"getGlobalKey",value:function getGlobalKey(t){return this.globals.get(t)}},{key:"getGlobalValue",value:function getGlobalValue(t){var r=this.controls[t],o=this.getGlobalKey(t),i=$e.data.commandExtractArgs(o),a=$e.data.getCache($e.components.get("globals"),i.command,i.args.query);if(null!=a&&a.value){var l,u=a.id;if(r.groupType){var c=elementor.breakpoints.getActiveMatchRegex(),d=r.name.replace(r.groupPrefix,"").replace(c,"");if(!a.value[elementor.config.kit_config.typography_prefix+d])return;d=d.replace("_","-"),l="var( --e-global-".concat(r.groupType,"-").concat(u,"-").concat(d," )"),elementor.config.ui.defaultGenericFonts&&r.groupPrefix+"font_family"===r.name&&(l+=", ".concat(elementor.config.ui.defaultGenericFonts))}else l="var( --e-global-".concat(r.type,"-").concat(u," )");return l}}},{key:"isGlobalApplied",value:function isGlobalApplied(t){return this.getSetting(t)!==this.settings.get(t)}},{key:"getGlobalDefault",value:function getGlobalDefault(t){var r,o=null===(r=this.controls[t])||void 0===r?void 0:r.global;if(null!=o&&o.default){var i=this.controls[t].type;if("color"===i&&(i="colors"),!elementor.config.globals.defaults_enabled[i])return"";var a=$e.data.commandExtractArgs(o.default),l=a.command,u=a.args,c=$e.data.getCache($e.components.get("globals"),l,u.query);return null==c?void 0:c.value}return""}}]),Container}(m.default);r.default=C,(0,g.default)(C,"TYPE_REPEATER","repeater-control"),(0,g.default)(C,"TYPE_REPEATER_ITEM","repeater")},40871:(t,r,o)=>{"use strict";var i=o(73203);Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var a=i(o(78983)),l=i(o(42081)),u=i(o(58724)),c=i(o(71173)),d=i(o(74910));function _createForOfIteratorHelper(t,r){var o="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!o){if(Array.isArray(t)||(o=function _unsupportedIterableToArray(t,r){if(!t)return;if("string"==typeof t)return _arrayLikeToArray(t,r);var o=Object.prototype.toString.call(t).slice(8,-1);"Object"===o&&t.constructor&&(o=t.constructor.name);if("Map"===o||"Set"===o)return Array.from(t);if("Arguments"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o))return _arrayLikeToArray(t,r)}(t))||r&&t&&"number"==typeof t.length){o&&(t=o);var i=0,a=function F(){};return{s:a,n:function n(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function e(t){throw t},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var l,u=!0,c=!1;return{s:function s(){o=o.call(t)},n:function n(){var t=o.next();return u=t.done,t},e:function e(t){c=!0,l=t},f:function f(){try{u||null==o.return||o.return()}finally{if(c)throw l}}}}function _arrayLikeToArray(t,r){(null==r||r>t.length)&&(r=t.length);for(var o=0,i=new Array(r);o<r;o++)i[o]=t[o];return i}function _createSuper(t){var r=function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function _createSuperInternal(){var o,i=(0,d.default)(t);if(r){var a=(0,d.default)(this).constructor;o=Reflect.construct(i,arguments,a)}else o=i.apply(this,arguments);return(0,c.default)(this,o)}}var p=function(t){(0,u.default)(ChildrenArray,t);var r=_createSuper(ChildrenArray);function ChildrenArray(){return(0,a.default)(this,ChildrenArray),r.apply(this,arguments)}return(0,l.default)(ChildrenArray,[{key:"clear",value:function clear(){this.length=0}},{key:"findRecursive",value:function findRecursive(t){var r,o=_createForOfIteratorHelper(this);try{for(o.s();!(r=o.n()).done;){var i=r.value;if(t(i))return i;if(i.children.length){var a=i.children.findRecursive(t);if(a)return a}}}catch(t){o.e(t)}finally{o.f()}return!1}},{key:"forEachRecursive",value:function forEachRecursive(t){var r,o=_createForOfIteratorHelper(this);try{for(o.s();!(r=o.n()).done;){var i=r.value;t(i),i.children.length&&i.children.forEachRecursive(t)}}catch(t){o.e(t)}finally{o.f()}}},{key:"someRecursive",value:function someRecursive(t){var r,o=_createForOfIteratorHelper(this);try{for(o.s();!(r=o.n()).done;){var i,a=r.value;if(t(a))return!0;if(null!==(i=a.children)&&void 0!==i&&i.length&&a.children.someRecursive(t))return!0}}catch(t){o.e(t)}finally{o.f()}return!1}}]),ChildrenArray}((0,i(o(19952)).default)(Array));r.default=p},63225:(t,r,o)=>{"use strict";var i=o(73203);Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var a=i(o(78983)),l=i(o(42081)),u=function(){function Panel(t){(0,a.default)(this,Panel),this.container=t}return(0,l.default)(Panel,[{key:"refresh",value:function refresh(){$e.routes.isPartOf("panel/editor")&&$e.routes.refreshContainer("panel")}},{key:"closeEditor",value:function closeEditor(){$e.route("panel/elements/categories")}},{key:"getControlView",value:function getControlView(t){return elementor.getPanelView().getCurrentPageView().children.findByModelCid(this.getControlModel(t).cid)}},{key:"getControlModel",value:function getControlModel(t){return elementor.getPanelView().getCurrentPageView().collection.findWhere({name:t})}}]),Panel}();r.default=u},59472:(t,r,o)=>{"use strict";var i,a=o(73203)(o(7501));i=Backbone.Model.extend({options:{},initialize:function initialize(t,r){var o=this;if(o.options=r,o.controls=elementor.mergeControlsSettings(r.controls),o.validators={},o.controls){var i=t||{},l={};_.each(o.controls,(function(t){if(!(t.features&&-1!==t.features.indexOf("ui"))){var r=t.name;"object"===(0,a.default)(t.default)?l[r]=structuredClone(t.default):l[r]=t.default;var u=t.dynamic&&t.dynamic.active,c=u&&i.__dynamic__&&i.__dynamic__[r];u&&!c&&t.dynamic.default&&(i.__dynamic__||(i.__dynamic__={}),i.__dynamic__[r]=t.dynamic.default,c=!0);var d=jQuery.isPlainObject(t.default);void 0===i[r]||!d||_.isObject(i[r])||c||(elementorCommon.debug.addCustomError(new TypeError("An invalid argument supplied as multiple control value"),"InvalidElementData","Element `"+(o.get("widgetType")||o.get("elType"))+"` got <"+i[r]+"> as `"+r+"` value. Expected array or object."),delete i[r]),void 0===i[r]&&(i[r]=l[r])}})),o.defaults=l,o.handleRepeaterData(i),o.set(i)}},convertRepeaterValueToCollection:function convertRepeaterValueToCollection(t,r){return new Backbone.Collection(t[r.name],{model:function model(t,o){return(o=o||{}).controls={},Object.values(r.fields).forEach((function(t){o.controls[t.name]=t})),t._id||(t._id=elementorCommon.helpers.getUniqueId()),new i(t,o)}})},handleRepeaterData:function handleRepeaterData(t){var r=this;_.each(this.controls,(function(o){o.is_repeater&&(t[o.name]instanceof Backbone.Collection||(t[o.name]=r.convertRepeaterValueToCollection(t,o)))}))},getFontControls:function getFontControls(){return this.getControlsByType("font")},getIconsControls:function getIconsControls(){return this.getControlsByType("icons")},getControlsByType:function getControlsByType(t){return _.filter(this.getActiveControls(),(function(r){return t===r.type}))},getStyleControls:function getStyleControls(t,r){var o=this;t=structuredClone(o.getActiveControls(t,r));var i=[];return jQuery.each(t,(function(){var r,a=this,l=elementor.config.controls[a.type];if((a=jQuery.extend({},l,a)).fields){var u=[];o.attributes[a.name]instanceof Backbone.Collection||(o.attributes[a.name]=o.convertRepeaterValueToCollection(o.attributes,a)),o.attributes[a.name].each((function(t){u.push(o.getStyleControls(a.fields,t.attributes))})),a.styleFields=u}(a.fields||null!==(r=a.dynamic)&&void 0!==r&&r.active||o.isGlobalControl(a,t)||o.isStyleControl(a.name,t))&&i.push(a)})),i},isGlobalControl:function isGlobalControl(t,r){var o,i,a=t.name;return t.groupType&&(a=t.groupPrefix+t.groupType),!(null===(o=r[a].global)||void 0===o||!o.active)&&!!(null===(i=this.attributes.__globals__)||void 0===i?void 0:i[a])},isStyleControl:function isStyleControl(t,r){r=r||this.controls;var o=_.find(r,(function(r){return t===r.name}));return o&&!_.isEmpty(o.selectors)},getClassControls:function getClassControls(t){return t=t||this.controls,_.filter(t,(function(t){return!_.isUndefined(t.prefix_class)}))},isClassControl:function isClassControl(t){var r=_.find(this.controls,(function(r){return t===r.name}));return r&&!_.isUndefined(r.prefix_class)},getControl:function getControl(t){return _.find(this.controls,(function(r){return t===r.name}))},getActiveControls:function getActiveControls(t,r){var o={};return t||(t=this.controls),r||(r=this.attributes),r=this.parseGlobalSettings(r,t),jQuery.each(t,(function(i,a){elementor.helpers.isActiveControl(a,r,t)&&(o[i]=a)})),o},clone:function clone(){return new i(elementorCommon.helpers.cloneObject(this.attributes),elementorCommon.helpers.cloneObject(this.options))},setExternalChange:function setExternalChange(t,r){var o,i=this;"object"===(0,a.default)(t)?o=t:(o={})[t]=r,i.set(o),jQuery.each(o,(function(t,r){i.trigger("change:external:"+t,r)}))},parseDynamicSettings:function parseDynamicSettings(t,r,o){var i=this;return t=elementorCommon.helpers.cloneObject(t||i.attributes),r=r||{},o=o||this.controls,jQuery.each(o,(function(){var o,a=this;if(a.is_repeater)(o=t[a.name]).forEach((function(t,l){o[l]=i.parseDynamicSettings(t,r,a.fields)}));else if(o=t.__dynamic__&&t.__dynamic__[a.name]){var l=a.dynamic;if(void 0===l&&(l=elementor.config.controls[a.type].dynamic),l&&l.active){var u;try{u=elementor.dynamicTags.parseTagsText(o,l,elementor.dynamicTags.getTagDataContent)}catch(t){if(elementor.dynamicTags.CACHE_KEY_NOT_FOUND_ERROR!==t.message)throw t;u="",r.onServerRequestStart&&r.onServerRequestStart(),elementor.dynamicTags.refreshCacheFromServer((function(){r.onServerRequestEnd&&r.onServerRequestEnd()}))}l.property?t[a.name][l.property]=u:t[a.name]=u}}})),t},parseGlobalSettings:function parseGlobalSettings(t,r){var o=this;return t=elementorCommon.helpers.cloneObject(t),r=r||this.controls,jQuery.each(r,(function(r,i){var a,l,u;if(i.is_repeater)(u=t[i.name]).forEach((function(t,r){u[r]=o.parseGlobalSettings(t,i.fields)}));else if(u=null===(a=t.__globals__)||void 0===a?void 0:a[i.name]){var c=i.global;if(void 0===c&&(c=elementor.config.controls[i.type].global),null!==(l=c)&&void 0!==l&&l.active){var d=$e.data.commandExtractArgs(u),p=d.command,h=d.args,v=$e.data.getCache($e.components.get("globals"),p,h.query);i.groupType?t[i.name]="custom":t[i.name]=v}}})),t},removeDataDefaults:function removeDataDefaults(t,r){var o=this;jQuery.each(t,(function(i){var a=r[i];a&&(a.save_default||("text"===a.type||"textarea"===a.type)&&t[i]||(a.is_repeater?t[i].forEach((function(t){o.removeDataDefaults(t,a.fields)})):_.isEqual(t[i],a.default)&&delete t[i]))}))},toJSON:function toJSON(t){var r=Backbone.Model.prototype.toJSON.call(this);return t=t||{},delete r.widgetType,delete r.elType,delete r.isInner,_.each(r,(function(t,o){t&&t.toJSON&&(r[o]=t.toJSON())})),t.remove&&-1!==t.remove.indexOf("default")&&this.removeDataDefaults(r,this.controls),structuredClone(r)}}),t.exports=i},10367:t=>{"use strict";var r;r=Marionette.Behavior.extend({onRenderCollection:function onRenderCollection(){this.handleInnerTabs(this.view)},handleInnerTabs:function handleInnerTabs(t){var r=t.children.filter((function(t){return"tabs"===t.model.get("type")}));_.each(r,(function(r){r.$el.find(".elementor-control-content").remove();var o=r.model.get("name"),i=t.children.filter((function(t){return"tab"===t.model.get("type")&&t.model.get("tabs_wrapper")===o}));_.each(i,(function(o,i){r._addChildView(o);var a=o.model.get("name"),l=t.children.filter((function(t){return a===t.model.get("inner_tab")}));0===i?o.$el.addClass("e-tab-active"):_.each(l,(function(t){t.$el.addClass("e-tab-close")}))}))}))},onChildviewControlTabClicked:function onChildviewControlTabClicked(t){var r="e-tab-close",o="e-tab-active",i=t.model.get("name"),a=this.view.children.filter((function(r){return"tab"!==r.model.get("type")&&t.model.get("tabs_wrapper")===r.model.get("tabs_wrapper")})),l=this.view.children.filter((function(r){return"tab"===r.model.get("type")&&t.model.get("tabs_wrapper")===r.model.get("tabs_wrapper")}));_.each(l,(function(t){t.$el.removeClass(o)})),t.$el.addClass(o),_.each(a,(function(t){t.model.get("inner_tab")===i?t.$el.removeClass(r):t.$el.addClass(r)})),elementor.getPanelView().updateScrollbar()}}),t.exports=r},90381:(t,r)=>{"use strict";function _createForOfIteratorHelper(t,r){var o="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!o){if(Array.isArray(t)||(o=function _unsupportedIterableToArray(t,r){if(!t)return;if("string"==typeof t)return _arrayLikeToArray(t,r);var o=Object.prototype.toString.call(t).slice(8,-1);"Object"===o&&t.constructor&&(o=t.constructor.name);if("Map"===o||"Set"===o)return Array.from(t);if("Arguments"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o))return _arrayLikeToArray(t,r)}(t))||r&&t&&"number"==typeof t.length){o&&(t=o);var i=0,a=function F(){};return{s:a,n:function n(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function e(t){throw t},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var l,u=!0,c=!1;return{s:function s(){o=o.call(t)},n:function n(){var t=o.next();return u=t.done,t},e:function e(t){c=!0,l=t},f:function f(){try{u||null==o.return||o.return()}finally{if(c)throw l}}}}function _arrayLikeToArray(t,r){(null==r||r>t.length)&&(r=t.length);for(var o=0,i=new Array(r);o<r;o++)i[o]=t[o];return i}Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;r.default=function _default(t,r){var o,i=_createForOfIteratorHelper(r=Array.isArray(r)?r:[r]);try{for(i.s();!(o=i.n()).done;){var a=o.value;if(t.constructor.name===a.prototype[Symbol.toStringTag])return!0}}catch(t){i.e(t)}finally{i.f()}return!1}},4746:t=>{"use strict";var r=elementorModules.Module.extend({onInit:function onInit(){var t=this,r=jQuery(window);r.on("elementor:init-components",this.onElementorInitComponents.bind(this)),r.on("elementor:loaded",(function(){t.onElementorLoaded(),elementor.on("document:loaded",t.onDocumentLoaded.bind(t))})),r.on("elementor:init",this.onElementorReady)},getEditorControlView:function getEditorControlView(t){return elementor.getPanelView().getCurrentPageView().children.findByModelCid(this.getEditorControlModel(t).cid)},getEditorControlModel:function getEditorControlModel(t){return elementor.getPanelView().getCurrentPageView().collection.findWhere({name:t})},onElementorReady:function onElementorReady(){this.onElementorInit(),elementor.on("frontend:init",this.onElementorFrontendInit.bind(this)).on("preview:loaded",this.onElementorPreviewLoaded.bind(this))}});r.prototype.onElementorLoaded=function(){},r.prototype.onElementorInit=function(){},r.prototype.onElementorPreviewLoaded=function(){},r.prototype.onDocumentLoaded=function(){},r.prototype.onElementorFrontendInit=function(){},r.prototype.onElementorInitComponents=function(){},t.exports=r},59010:(t,r,o)=>{"use strict";var i=o(38003).__,a=o(73203);Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var l=a(o(93231)),u=a(o(78983)),c=a(o(42081)),d=function(){function ControlsPopover(t){(0,u.default)(this,ControlsPopover),this.child=t,this.$popover=jQuery("<div>",{class:"elementor-controls-popover"}),t.$el.before(this.$popover),this.$popover.append(t.$el),this.popoverToggleView=t._parent.children.findByIndex(t._index-1),"typography"===this.child.model.attributes.groupType&&this.createPopoverHeader()}return(0,c.default)(ControlsPopover,[{key:"addChild",value:function addChild(t){this.$popover.append(t.$el)}},{key:"createPopoverHeader",value:function createPopoverHeader(){var t=this,r=this.$popover.prev().find(".elementor-control-popover-toggle-reset-label");this.$popoverHeader=jQuery("<div>",{class:"e-group-control-header"}).html("<span>"+i("Typography","elementor")+"</span>"),this.$headerControlsWrapper=jQuery("<div>",{class:"e-control-tools"}),r.addClass("e-control-tool").on("click",(function(){return t.onResetButtonClick()})),this.$headerControlsWrapper.append(r),this.$popoverHeader.append(this.$headerControlsWrapper);var o=this.popoverToggleView.model.get("global");null!=o&&o.active&&this.createAddButton(),this.$popover.prepend(this.$popoverHeader).addClass("e-controls-popover--typography")}},{key:"onResetButtonClick",value:function onResetButtonClick(){this.$popover.hide();var t=this.child.model.get("groupPrefix")+"typography",r={container:this.child.options.container,settings:(0,l.default)({},t,"")};this.child.options.container.globals.get(t)?$e.run("document/globals/disable",r):$e.run("document/elements/settings",r)}},{key:"onAddButtonClick",value:function onAddButtonClick(){this.popoverToggleView.onAddGlobalButtonClick()}},{key:"createAddButton",value:function createAddButton(){var t=this;this.$addButton=jQuery("<button>",{class:"e-control-tool"}).html(jQuery("<i>",{class:"eicon-plus"})),this.$headerControlsWrapper.append(this.$addButton),this.$addButton.on("click",(function(){return t.onAddButtonClick()})),this.$addButton.tipsy({title:function title(){return i("Create New Global Font","elementor")},gravity:function gravity(){return"s"}})}},{key:"destroy",value:function destroy(){this.$popover.remove()}}]),ControlsPopover}();r.default=d},99533:(t,r,o)=>{"use strict";var i=o(73203);Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var a,l=i(o(59010)),u=a=Marionette.CompositeView.extend({classes:{popover:"elementor-controls-popover"},activeTab:null,activeSection:null,className:function className(){return"elementor-controls-stack"},templateHelpers:function templateHelpers(){return{elementData:elementor.getElementData(this.model)}},childViewOptions:function childViewOptions(){return{elementSettingsModel:this.model}},ui:function ui(){return{tabs:".elementor-panel-navigation-tab",reloadButton:".elementor-update-preview-button"}},events:function events(){return{"click @ui.reloadButton":"onReloadButtonClick"}},modelEvents:{destroy:"onModelDestroy"},behaviors:{HandleInnerTabs:{behaviorClass:o(10367)}},initialize:function initialize(t){this.initCollection(),t.tab&&(this.activeTab=t.tab,this.activateFirstSection()),this.listenTo(elementor.channels.deviceMode,"change",this.onDeviceModeChange)},onDestroy:function onDestroy(){this.stopListening(elementor.channels.deviceMode,"change",this.onDeviceModeChange)},initCollection:function initCollection(){this.collection=new Backbone.Collection(_.values(elementor.mergeControlsSettings(this.getOption("controls"))))},filter:function filter(t){if(t.get("tab")!==this.activeTab)return!1;if("section"===t.get("type"))return!0;var r=t.get("section");return!r||r===this.activeSection},getControlViewByModel:function getControlViewByModel(t){return this.children.findByModelCid(t.cid)},getControlViewByName:function getControlViewByName(t){return this.getControlViewByModel(this.getControlModel(t))},getControlModel:function getControlModel(t){return this.collection.findWhere({name:t})},isVisibleSectionControl:function isVisibleSectionControl(t){return this.activeTab===t.get("tab")},activateTab:function activateTab(t){return this.activeTab=t,this.activateFirstSection(),this._renderChildren(),this},activateSection:function activateSection(t){return this.activeSection=t,this},activateFirstSection:function activateFirstSection(){var t,r=this,o=r.collection.filter((function(t){return"section"===t.get("type")&&r.isVisibleSectionControl(t)}));if(o[0]?t=o[0].get("name"):(r.activeSection=null,t=null),!o.filter((function(t){return r.activeSection===t.get("name")}))[0])return r.activateSection(t),this},getChildView:function getChildView(t){var r=t.get("type");return elementor.getControlView(r)},getNamespaceArray:function getNamespaceArray(){return[elementor.getPanelView().getCurrentPageName()]},openActiveSection:function openActiveSection(){var t=this.activeSection,r=this.children.filter((function(r){return t===r.model.get("name")}));if(r[0]){r[0].$el.addClass("e-open");var o=this.getNamespaceArray();o.push(t,"activated"),elementor.channels.editor.trigger(o.join(":"),this)}},onRenderCollection:function onRenderCollection(){this.openActiveSection(),a.handlePopovers(this)},onModelDestroy:function onModelDestroy(){this.destroy()},onReloadButtonClick:function onReloadButtonClick(){elementor.reloadPreview()},onDeviceModeChange:function onDeviceModeChange(){"desktop"===elementor.channels.deviceMode.request("currentMode")&&this.$el.removeClass("elementor-responsive-switchers-open")},onChildviewControlSectionClicked:function onChildviewControlSectionClicked(t){var r=t.$el.hasClass("e-open");this.activateSection(r?null:t.model.get("name")),this._renderChildren()},onChildviewResponsiveSwitcherClick:function onChildviewResponsiveSwitcherClick(t,r){"desktop"===r&&this.$el.toggleClass("elementor-responsive-switchers-open")}},{handlePopovers:function handlePopovers(t){var r;this.removePopovers(t),t.popovers=[],t.children.each((function(o){r&&r.addChild(o);var i=o.model.get("popover");i&&(i.start&&(r=new l.default(o),t.popovers.push(r)),i.end&&(r=null))}))},removePopovers:function removePopovers(t){var r;null===(r=t.popovers)||void 0===r||r.forEach((function(t){return t.destroy()}))}});r.default=u},42618:(t,r,o)=>{"use strict";var i=o(73203);Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var a=i(o(7501)),l=i(o(78983)),u=i(o(42081)),c=i(o(58724)),d=i(o(71173)),p=i(o(74910)),h=i(o(27597)),v=i(o(90381));function _createSuper(t){var r=function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function _createSuperInternal(){var o,i=(0,p.default)(t);if(r){var a=(0,p.default)(this).constructor;o=Reflect.construct(i,arguments,a)}else o=i.apply(this,arguments);return(0,d.default)(this,o)}}var y=function(t){(0,c.default)(ArgsObject,t);var r=_createSuper(ArgsObject);function ArgsObject(t){var o;return(0,l.default)(this,ArgsObject),(o=r.call(this)).args=t,o}return(0,u.default)(ArgsObject,[{key:"requireArgument",value:function requireArgument(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.args;if(!Object.prototype.hasOwnProperty.call(r,t))throw Error("".concat(t," is required."))}},{key:"requireArgumentType",value:function requireArgumentType(t,r){var o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.args;if(this.requireArgument(t,o),(0,a.default)(o[t])!==r)throw Error("".concat(t," invalid type: ").concat(r,"."))}},{key:"requireArgumentInstance",value:function requireArgumentInstance(t,r){var o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.args;if(this.requireArgument(t,o),!(o[t]instanceof r||(0,v.default)(o[t],r)))throw Error("".concat(t," invalid instance."))}},{key:"requireArgumentConstructor",value:function requireArgumentConstructor(t,r){var o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.args;if(this.requireArgument(t,o),o[t].constructor.toString()!==r.prototype.constructor.toString())throw Error("".concat(t," invalid constructor type."))}}],[{key:"getInstanceType",value:function getInstanceType(){return"ArgsObject"}}]),ArgsObject}(h.default);r.default=y},27597:(t,r,o)=>{"use strict";var i=o(73203);Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var a=i(o(78983)),l=i(o(42081)),u=i(o(51121)),c=i(o(74910)),d=function(t){function InstanceType(){var t=this;(0,a.default)(this,InstanceType);for(var r=this instanceof InstanceType?this.constructor:void 0,o=[];r.__proto__&&r.__proto__.name;)o.push(r.__proto__),r=r.__proto__;o.reverse().forEach((function(r){return t instanceof r}))}return(0,l.default)(InstanceType,null,[{key:t,value:function value(t){var r=(0,u.default)((0,c.default)(InstanceType),Symbol.hasInstance,this).call(this,t);if(t&&!t.constructor.getInstanceType)return r;if(t&&(t.instanceTypes||(t.instanceTypes=[]),r||this.getInstanceType()===t.constructor.getInstanceType()&&(r=!0),r)){var o=this.getInstanceType===InstanceType.getInstanceType?"BaseInstanceType":this.getInstanceType();-1===t.instanceTypes.indexOf(o)&&t.instanceTypes.push(o)}return!r&&t&&(r=t.instanceTypes&&Array.isArray(t.instanceTypes)&&-1!==t.instanceTypes.indexOf(this.getInstanceType())),r}},{key:"getInstanceType",value:function getInstanceType(){elementorModules.ForceMethodImplementation()}}]),InstanceType}(Symbol.hasInstance);r.default=d},60834:(t,r,o)=>{"use strict";var i=o(73203);Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var a=i(o(50824)),l=i(o(10029)),u=i(o(78983)),c=i(o(42081)),d=i(o(77266)),p=i(o(58724)),h=i(o(71173)),v=i(o(74910)),y=i(o(93231));function _createSuper(t){var r=function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function _createSuperInternal(){var o,i=(0,v.default)(t);if(r){var a=(0,v.default)(this).constructor;o=Reflect.construct(i,arguments,a)}else o=i.apply(this,arguments);return(0,h.default)(this,o)}}var g=function(t){(0,p.default)(_default,t);var r,o=_createSuper(_default);function _default(){var t;(0,u.default)(this,_default);for(var r=arguments.length,i=new Array(r),a=0;a<r;a++)i[a]=arguments[a];return t=o.call.apply(o,[this].concat(i)),(0,y.default)((0,d.default)(t),"introductionMap",null),t.initDialog(),t}return(0,c.default)(_default,[{key:"setIntroductionMap",value:function setIntroductionMap(t){this.introductionMap=t}},{key:"getIntroductionMap",value:function getIntroductionMap(){return this.introductionMap||elementor.config.user.introduction}},{key:"getDefaultSettings",value:function getDefaultSettings(){return{dialogType:"buttons",dialogOptions:{effects:{hide:"hide",show:"show"},hide:{onBackgroundClick:!1}}}}},{key:"initDialog",value:function initDialog(){var t,r=this;this.getDialog=function(){if(!t){var o=r.getSettings();t=elementorCommon.dialogsManager.createWidget(o.dialogType,o.dialogOptions),o.onDialogInitCallback&&o.onDialogInitCallback.call(r,t)}return t}}},{key:"show",value:function show(t){if(!this.introductionViewed){var r=this.getDialog();t&&r.setSettings("position",{of:t}),r.show()}}},{key:"introductionViewed",get:function get(){var t=this.getSettings("introductionKey");return this.getIntroductionMap()[t]},set:function set(t){var r=this.getSettings("introductionKey");this.getIntroductionMap()[r]=t}},{key:"setViewed",value:(r=(0,l.default)(a.default.mark((function _callee(){var t=this;return a.default.wrap((function _callee$(r){for(;;)switch(r.prev=r.next){case 0:return this.introductionViewed=!0,r.abrupt("return",new Promise((function(r,o){elementorCommon.ajax.addRequest("introduction_viewed",{data:{introductionKey:t.getSettings("introductionKey")},success:r,error:o})})));case 2:case"end":return r.stop()}}),_callee,this)}))),function setViewed(){return r.apply(this,arguments)})}]),_default}(elementorModules.Module);r.default=g},38003:t=>{"use strict";t.exports=wp.i18n},98106:t=>{t.exports=function _arrayLikeToArray(t,r){(null==r||r>t.length)&&(r=t.length);for(var o=0,i=new Array(r);o<r;o++)i[o]=t[o];return i},t.exports.__esModule=!0,t.exports.default=t.exports},17358:t=>{t.exports=function _arrayWithHoles(t){if(Array.isArray(t))return t},t.exports.__esModule=!0,t.exports.default=t.exports},77266:t=>{t.exports=function _assertThisInitialized(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t},t.exports.__esModule=!0,t.exports.default=t.exports},10029:t=>{function asyncGeneratorStep(t,r,o,i,a,l,u){try{var c=t[l](u),d=c.value}catch(t){return void o(t)}c.done?r(d):Promise.resolve(d).then(i,a)}t.exports=function _asyncToGenerator(t){return function(){var r=this,o=arguments;return new Promise((function(i,a){var l=t.apply(r,o);function _next(t){asyncGeneratorStep(l,i,a,_next,_throw,"next",t)}function _throw(t){asyncGeneratorStep(l,i,a,_next,_throw,"throw",t)}_next(void 0)}))}},t.exports.__esModule=!0,t.exports.default=t.exports},78983:t=>{t.exports=function _classCallCheck(t,r){if(!(t instanceof r))throw new TypeError("Cannot call a class as a function")},t.exports.__esModule=!0,t.exports.default=t.exports},76824:(t,r,o)=>{var i=o(96196),a=o(14161);t.exports=function _construct(t,r,o){if(a())return Reflect.construct.apply(null,arguments);var l=[null];l.push.apply(l,r);var u=new(t.bind.apply(t,l));return o&&i(u,o.prototype),u},t.exports.__esModule=!0,t.exports.default=t.exports},42081:(t,r,o)=>{var i=o(74040);function _defineProperties(t,r){for(var o=0;o<r.length;o++){var a=r[o];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(t,i(a.key),a)}}t.exports=function _createClass(t,r,o){return r&&_defineProperties(t.prototype,r),o&&_defineProperties(t,o),Object.defineProperty(t,"prototype",{writable:!1}),t},t.exports.__esModule=!0,t.exports.default=t.exports},93231:(t,r,o)=>{var i=o(74040);t.exports=function _defineProperty(t,r,o){return(r=i(r))in t?Object.defineProperty(t,r,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[r]=o,t},t.exports.__esModule=!0,t.exports.default=t.exports},51121:(t,r,o)=>{var i=o(79443);function _get(){return"undefined"!=typeof Reflect&&Reflect.get?(t.exports=_get=Reflect.get.bind(),t.exports.__esModule=!0,t.exports.default=t.exports):(t.exports=_get=function _get(t,r,o){var a=i(t,r);if(a){var l=Object.getOwnPropertyDescriptor(a,r);return l.get?l.get.call(arguments.length<3?t:o):l.value}},t.exports.__esModule=!0,t.exports.default=t.exports),_get.apply(this,arguments)}t.exports=_get,t.exports.__esModule=!0,t.exports.default=t.exports},74910:t=>{function _getPrototypeOf(r){return t.exports=_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function _getPrototypeOf(t){return t.__proto__||Object.getPrototypeOf(t)},t.exports.__esModule=!0,t.exports.default=t.exports,_getPrototypeOf(r)}t.exports=_getPrototypeOf,t.exports.__esModule=!0,t.exports.default=t.exports},58724:(t,r,o)=>{var i=o(96196);t.exports=function _inherits(t,r){if("function"!=typeof r&&null!==r)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(r&&r.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),r&&i(t,r)},t.exports.__esModule=!0,t.exports.default=t.exports},73203:t=>{t.exports=function _interopRequireDefault(t){return t&&t.__esModule?t:{default:t}},t.exports.__esModule=!0,t.exports.default=t.exports},94346:t=>{t.exports=function _isNativeFunction(t){try{return-1!==Function.toString.call(t).indexOf("[native code]")}catch(r){return"function"==typeof t}},t.exports.__esModule=!0,t.exports.default=t.exports},14161:t=>{function _isNativeReflectConstruct(){try{var r=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(r){}return(t.exports=_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!r},t.exports.__esModule=!0,t.exports.default=t.exports)()}t.exports=_isNativeReflectConstruct,t.exports.__esModule=!0,t.exports.default=t.exports},40608:t=>{t.exports=function _iterableToArrayLimit(t,r){var o=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=o){var i,a,l,u,c=[],d=!0,p=!1;try{if(l=(o=o.call(t)).next,0===r){if(Object(o)!==o)return;d=!1}else for(;!(d=(i=l.call(o)).done)&&(c.push(i.value),c.length!==r);d=!0);}catch(t){p=!0,a=t}finally{try{if(!d&&null!=o.return&&(u=o.return(),Object(u)!==u))return}finally{if(p)throw a}}return c}},t.exports.__esModule=!0,t.exports.default=t.exports},56894:t=>{t.exports=function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},t.exports.__esModule=!0,t.exports.default=t.exports},71173:(t,r,o)=>{var i=o(7501).default,a=o(77266);t.exports=function _possibleConstructorReturn(t,r){if(r&&("object"===i(r)||"function"==typeof r))return r;if(void 0!==r)throw new TypeError("Derived constructors may only return object or undefined");return a(t)},t.exports.__esModule=!0,t.exports.default=t.exports},21337:(t,r,o)=>{var i=o(7501).default;function _regeneratorRuntime(){"use strict";t.exports=_regeneratorRuntime=function _regeneratorRuntime(){return o},t.exports.__esModule=!0,t.exports.default=t.exports;var r,o={},a=Object.prototype,l=a.hasOwnProperty,u=Object.defineProperty||function(t,r,o){t[r]=o.value},c="function"==typeof Symbol?Symbol:{},d=c.iterator||"@@iterator",p=c.asyncIterator||"@@asyncIterator",h=c.toStringTag||"@@toStringTag";function define(t,r,o){return Object.defineProperty(t,r,{value:o,enumerable:!0,configurable:!0,writable:!0}),t[r]}try{define({},"")}catch(r){define=function define(t,r,o){return t[r]=o}}function wrap(t,r,o,i){var a=r&&r.prototype instanceof Generator?r:Generator,l=Object.create(a.prototype),c=new Context(i||[]);return u(l,"_invoke",{value:makeInvokeMethod(t,o,c)}),l}function tryCatch(t,r,o){try{return{type:"normal",arg:t.call(r,o)}}catch(t){return{type:"throw",arg:t}}}o.wrap=wrap;var v="suspendedStart",y="suspendedYield",g="executing",m="completed",_={};function Generator(){}function GeneratorFunction(){}function GeneratorFunctionPrototype(){}var b={};define(b,d,(function(){return this}));var C=Object.getPrototypeOf,x=C&&C(C(values([])));x&&x!==a&&l.call(x,d)&&(b=x);var w=GeneratorFunctionPrototype.prototype=Generator.prototype=Object.create(b);function defineIteratorMethods(t){["next","throw","return"].forEach((function(r){define(t,r,(function(t){return this._invoke(r,t)}))}))}function AsyncIterator(t,r){function invoke(o,a,u,c){var d=tryCatch(t[o],t,a);if("throw"!==d.type){var p=d.arg,h=p.value;return h&&"object"==i(h)&&l.call(h,"__await")?r.resolve(h.__await).then((function(t){invoke("next",t,u,c)}),(function(t){invoke("throw",t,u,c)})):r.resolve(h).then((function(t){p.value=t,u(p)}),(function(t){return invoke("throw",t,u,c)}))}c(d.arg)}var o;u(this,"_invoke",{value:function value(t,i){function callInvokeWithMethodAndArg(){return new r((function(r,o){invoke(t,i,r,o)}))}return o=o?o.then(callInvokeWithMethodAndArg,callInvokeWithMethodAndArg):callInvokeWithMethodAndArg()}})}function makeInvokeMethod(t,o,i){var a=v;return function(l,u){if(a===g)throw new Error("Generator is already running");if(a===m){if("throw"===l)throw u;return{value:r,done:!0}}for(i.method=l,i.arg=u;;){var c=i.delegate;if(c){var d=maybeInvokeDelegate(c,i);if(d){if(d===_)continue;return d}}if("next"===i.method)i.sent=i._sent=i.arg;else if("throw"===i.method){if(a===v)throw a=m,i.arg;i.dispatchException(i.arg)}else"return"===i.method&&i.abrupt("return",i.arg);a=g;var p=tryCatch(t,o,i);if("normal"===p.type){if(a=i.done?m:y,p.arg===_)continue;return{value:p.arg,done:i.done}}"throw"===p.type&&(a=m,i.method="throw",i.arg=p.arg)}}}function maybeInvokeDelegate(t,o){var i=o.method,a=t.iterator[i];if(a===r)return o.delegate=null,"throw"===i&&t.iterator.return&&(o.method="return",o.arg=r,maybeInvokeDelegate(t,o),"throw"===o.method)||"return"!==i&&(o.method="throw",o.arg=new TypeError("The iterator does not provide a '"+i+"' method")),_;var l=tryCatch(a,t.iterator,o.arg);if("throw"===l.type)return o.method="throw",o.arg=l.arg,o.delegate=null,_;var u=l.arg;return u?u.done?(o[t.resultName]=u.value,o.next=t.nextLoc,"return"!==o.method&&(o.method="next",o.arg=r),o.delegate=null,_):u:(o.method="throw",o.arg=new TypeError("iterator result is not an object"),o.delegate=null,_)}function pushTryEntry(t){var r={tryLoc:t[0]};1 in t&&(r.catchLoc=t[1]),2 in t&&(r.finallyLoc=t[2],r.afterLoc=t[3]),this.tryEntries.push(r)}function resetTryEntry(t){var r=t.completion||{};r.type="normal",delete r.arg,t.completion=r}function Context(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(pushTryEntry,this),this.reset(!0)}function values(t){if(t||""===t){var o=t[d];if(o)return o.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,u=function next(){for(;++a<t.length;)if(l.call(t,a))return next.value=t[a],next.done=!1,next;return next.value=r,next.done=!0,next};return u.next=u}}throw new TypeError(i(t)+" is not iterable")}return GeneratorFunction.prototype=GeneratorFunctionPrototype,u(w,"constructor",{value:GeneratorFunctionPrototype,configurable:!0}),u(GeneratorFunctionPrototype,"constructor",{value:GeneratorFunction,configurable:!0}),GeneratorFunction.displayName=define(GeneratorFunctionPrototype,h,"GeneratorFunction"),o.isGeneratorFunction=function(t){var r="function"==typeof t&&t.constructor;return!!r&&(r===GeneratorFunction||"GeneratorFunction"===(r.displayName||r.name))},o.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,GeneratorFunctionPrototype):(t.__proto__=GeneratorFunctionPrototype,define(t,h,"GeneratorFunction")),t.prototype=Object.create(w),t},o.awrap=function(t){return{__await:t}},defineIteratorMethods(AsyncIterator.prototype),define(AsyncIterator.prototype,p,(function(){return this})),o.AsyncIterator=AsyncIterator,o.async=function(t,r,i,a,l){void 0===l&&(l=Promise);var u=new AsyncIterator(wrap(t,r,i,a),l);return o.isGeneratorFunction(r)?u:u.next().then((function(t){return t.done?t.value:u.next()}))},defineIteratorMethods(w),define(w,h,"Generator"),define(w,d,(function(){return this})),define(w,"toString",(function(){return"[object Generator]"})),o.keys=function(t){var r=Object(t),o=[];for(var i in r)o.push(i);return o.reverse(),function next(){for(;o.length;){var t=o.pop();if(t in r)return next.value=t,next.done=!1,next}return next.done=!0,next}},o.values=values,Context.prototype={constructor:Context,reset:function reset(t){if(this.prev=0,this.next=0,this.sent=this._sent=r,this.done=!1,this.delegate=null,this.method="next",this.arg=r,this.tryEntries.forEach(resetTryEntry),!t)for(var o in this)"t"===o.charAt(0)&&l.call(this,o)&&!isNaN(+o.slice(1))&&(this[o]=r)},stop:function stop(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function dispatchException(t){if(this.done)throw t;var o=this;function handle(i,a){return u.type="throw",u.arg=t,o.next=i,a&&(o.method="next",o.arg=r),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],u=a.completion;if("root"===a.tryLoc)return handle("end");if(a.tryLoc<=this.prev){var c=l.call(a,"catchLoc"),d=l.call(a,"finallyLoc");if(c&&d){if(this.prev<a.catchLoc)return handle(a.catchLoc,!0);if(this.prev<a.finallyLoc)return handle(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return handle(a.catchLoc,!0)}else{if(!d)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return handle(a.finallyLoc)}}}},abrupt:function abrupt(t,r){for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o];if(i.tryLoc<=this.prev&&l.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===t||"continue"===t)&&a.tryLoc<=r&&r<=a.finallyLoc&&(a=null);var u=a?a.completion:{};return u.type=t,u.arg=r,a?(this.method="next",this.next=a.finallyLoc,_):this.complete(u)},complete:function complete(t,r){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&r&&(this.next=r),_},finish:function finish(t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.finallyLoc===t)return this.complete(o.completion,o.afterLoc),resetTryEntry(o),_}},catch:function _catch(t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc===t){var i=o.completion;if("throw"===i.type){var a=i.arg;resetTryEntry(o)}return a}}throw new Error("illegal catch attempt")},delegateYield:function delegateYield(t,o,i){return this.delegate={iterator:values(t),resultName:o,nextLoc:i},"next"===this.method&&(this.arg=r),_}},o}t.exports=_regeneratorRuntime,t.exports.__esModule=!0,t.exports.default=t.exports},96196:t=>{function _setPrototypeOf(r,o){return t.exports=_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function _setPrototypeOf(t,r){return t.__proto__=r,t},t.exports.__esModule=!0,t.exports.default=t.exports,_setPrototypeOf(r,o)}t.exports=_setPrototypeOf,t.exports.__esModule=!0,t.exports.default=t.exports},40131:(t,r,o)=>{var i=o(17358),a=o(40608),l=o(35068),u=o(56894);t.exports=function _slicedToArray(t,r){return i(t)||a(t,r)||l(t,r)||u()},t.exports.__esModule=!0,t.exports.default=t.exports},79443:(t,r,o)=>{var i=o(74910);t.exports=function _superPropBase(t,r){for(;!Object.prototype.hasOwnProperty.call(t,r)&&null!==(t=i(t)););return t},t.exports.__esModule=!0,t.exports.default=t.exports},56027:(t,r,o)=>{var i=o(7501).default;t.exports=function toPrimitive(t,r){if("object"!=i(t)||!t)return t;var o=t[Symbol.toPrimitive];if(void 0!==o){var a=o.call(t,r||"default");if("object"!=i(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(t)},t.exports.__esModule=!0,t.exports.default=t.exports},74040:(t,r,o)=>{var i=o(7501).default,a=o(56027);t.exports=function toPropertyKey(t){var r=a(t,"string");return"symbol"==i(r)?r:String(r)},t.exports.__esModule=!0,t.exports.default=t.exports},7501:t=>{function _typeof(r){return t.exports=_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t.exports.__esModule=!0,t.exports.default=t.exports,_typeof(r)}t.exports=_typeof,t.exports.__esModule=!0,t.exports.default=t.exports},35068:(t,r,o)=>{var i=o(98106);t.exports=function _unsupportedIterableToArray(t,r){if(t){if("string"==typeof t)return i(t,r);var o=Object.prototype.toString.call(t).slice(8,-1);return"Object"===o&&t.constructor&&(o=t.constructor.name),"Map"===o||"Set"===o?Array.from(t):"Arguments"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o)?i(t,r):void 0}},t.exports.__esModule=!0,t.exports.default=t.exports},19952:(t,r,o)=>{var i=o(74910),a=o(96196),l=o(94346),u=o(76824);function _wrapNativeSuper(r){var o="function"==typeof Map?new Map:void 0;return t.exports=_wrapNativeSuper=function _wrapNativeSuper(t){if(null===t||!l(t))return t;if("function"!=typeof t)throw new TypeError("Super expression must either be null or a function");if(void 0!==o){if(o.has(t))return o.get(t);o.set(t,Wrapper)}function Wrapper(){return u(t,arguments,i(this).constructor)}return Wrapper.prototype=Object.create(t.prototype,{constructor:{value:Wrapper,enumerable:!1,writable:!0,configurable:!0}}),a(Wrapper,t)},t.exports.__esModule=!0,t.exports.default=t.exports,_wrapNativeSuper(r)}t.exports=_wrapNativeSuper,t.exports.__esModule=!0,t.exports.default=t.exports},50824:(t,r,o)=>{var i=o(21337)();t.exports=i;try{regeneratorRuntime=i}catch(t){"object"==typeof globalThis?globalThis.regeneratorRuntime=i:Function("r","regeneratorRuntime = r")(i)}}},r={};function __webpack_require__(o){var i=r[o];if(void 0!==i)return i.exports;var a=r[o]={exports:{}};return t[o](a,a.exports,__webpack_require__),a.exports}(()=>{"use strict";var t=__webpack_require__(73203),r=t(__webpack_require__(4746)),o=t(__webpack_require__(60834)),i=t(__webpack_require__(99533)),a=t(__webpack_require__(59472)),l=t(__webpack_require__(61909));elementorModules.editor={elements:{models:{BaseSettings:a.default}},utils:{Module:r.default,Introduction:o.default},views:{ControlsStack:i.default},Container:l.default}})()})();