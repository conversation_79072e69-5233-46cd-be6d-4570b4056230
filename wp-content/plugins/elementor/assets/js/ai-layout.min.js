/*! elementor - v3.23.0 - 05-08-2024 */
/*! For license information please see ai-layout.min.js.LICENSE.txt */
(()=>{var n={66535:(n,o,a)=>{"use strict";var i=a(7501);Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;_interopRequireWildcard(a(87363));var l=_interopRequireWildcard(a(61533)),u=a(37634);function _getRequireWildcardCache(n){if("function"!=typeof WeakMap)return null;var o=new WeakMap,a=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(n){return n?a:o})(n)}function _interopRequireWildcard(n,o){if(!o&&n&&n.__esModule)return n;if(null===n||"object"!==i(n)&&"function"!=typeof n)return{default:n};var a=_getRequireWildcardCache(o);if(a&&a.has(n))return a.get(n);var l={},u=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var c in n)if("default"!==c&&Object.prototype.hasOwnProperty.call(n,c)){var p=u?Object.getOwnPropertyDescriptor(n,c):null;p&&(p.get||p.set)?Object.defineProperty(l,c,p):l[c]=n[c]}return l.default=n,a&&a.set(n,l),l}var c={render:function render(n,o){var a;try{var i=(0,u.createRoot)(o);i.render(n),a=function unmountFunction(){i.unmount()}}catch(i){l.render(n,o),a=function unmountFunction(){l.unmountComponentAtNode(o)}}return{unmount:a}}};o.default=c},70299:(n,o,a)=>{"use strict";var i=a(73203);Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var l=i(a(78983)),u=i(a(42081)),c=i(a(77266)),p=i(a(58724)),w=i(a(71173)),C=i(a(74910)),x=i(a(93231)),S=a(38003),O=a(36619),j=a(25455);function _createSuper(n){var o=function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(n){return!1}}();return function _createSuperInternal(){var a,i=(0,C.default)(n);if(o){var l=(0,C.default)(this).constructor;a=Reflect.construct(i,arguments,l)}else a=i.apply(this,arguments);return(0,w.default)(this,a)}}var k=function(n){(0,p.default)(AiLayoutBehavior,n);var o=_createSuper(AiLayoutBehavior);function AiLayoutBehavior(){var n;(0,l.default)(this,AiLayoutBehavior);for(var a=arguments.length,i=new Array(a),u=0;u<a;u++)i[u]=arguments[u];return n=o.call.apply(o,[this].concat(i)),(0,x.default)((0,c.default)(n),"previewContainer",null),n}return(0,u.default)(AiLayoutBehavior,[{key:"ui",value:function ui(){return{aiButton:".e-ai-layout-button",addTemplateButton:".elementor-add-template-button"}}},{key:"events",value:function events(){return{"click @ui.aiButton":"onAiButtonClick"}}},{key:"onAiButtonClick",value:function onAiButtonClick(n){n.stopPropagation(),window.elementorAiCurrentContext=this.getOption("context"),(0,O.renderLayoutApp)({parentContainer:elementor.getPreviewContainer(),mode:j.MODE_LAYOUT,at:this.view.getOption("at"),onInsert:this.onInsert.bind(this),onRenderApp:function onRenderApp(n){n.previewContainer.init()},onGenerate:function onGenerate(n){n.previewContainer.reset()}})}},{key:"hideDropArea",value:function hideDropArea(){this.view.onCloseButtonClick()}},{key:"onInsert",value:function onInsert(n){this.hideDropArea(),(0,O.importToEditor)({parentContainer:elementor.getPreviewContainer(),at:this.view.getOption("at"),template:n,historyTitle:(0,S.__)("AI Layout","elementor")})}},{key:"onRender",value:function onRender(){var n=jQuery("<div>",{class:"e-ai-layout-button elementor-add-section-area-button e-button-primary",title:(0,S.__)("Build with AI","elementor"),role:"button"});n.html('\n\t\t\t<div class="e-ai-layout-button--sparkle"></div>\n\t\t\t<div class="e-ai-layout-button--sparkle"></div>\n\t\t\t<div class="e-ai-layout-button--sparkle"></div>\n\t\t\t<div class="e-ai-layout-button--sparkle"></div>\n\t\t\t<div class="e-ai-layout-button--sparkle"></div>\n\t\t\t<div class="e-ai-layout-button--sparkle"></div>\n\t\t\t<div class="e-ai-layout-button--sparkle"></div>\n\t\t\t<i class="eicon-ai"></i>\n\t\t'),this.ui.addTemplateButton.after(n)}}]),AiLayoutBehavior}(Marionette.Behavior);o.default=k},46183:(n,o,a)=>{"use strict";var i=a(73203);Object.defineProperty(o,"__esModule",{value:!0}),o.uploadImage=o.toggleFavoriteHistoryItem=o.setStatusFeedback=o.setGetStarted=o.getUserInformation=o.getTextToImageGeneration=o.getRemoteConfig=o.getLayoutPromptEnhanced=o.getImageToImageUpscale=o.getImageToImageReplaceBackground=o.getImageToImageRemoveText=o.getImageToImageRemoveBackground=o.getImageToImageOutPainting=o.getImageToImageMaskGeneration=o.getImageToImageGeneration=o.getImagePromptEnhanced=o.getHistory=o.getFeaturedImage=o.getExcerpt=o.getEditText=o.getCustomCode=o.getCustomCSS=o.getCompletionText=o.generateLayout=o.deleteHistoryItem=void 0;var l=i(a(93231));function ownKeys(n,o){var a=Object.keys(n);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(n);o&&(i=i.filter((function(o){return Object.getOwnPropertyDescriptor(n,o).enumerable}))),a.push.apply(a,i)}return a}var u=function request(n){var o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=arguments.length>3?arguments[3]:void 0;return Object.keys(o).length&&(window.elementorAiCurrentContext?o.context=window.elementorAiCurrentContext:o.context=window.elementorWpAiCurrentContext),new Promise((function(l,u){var c=elementorCommon.ajax.addRequest(n,{success:l,error:u,data:o},a);i&&c.jqXhr&&i.addEventListener("abort",c.jqXhr.abort)}))};o.getUserInformation=function getUserInformation(n){return u("ai_get_user_information",void 0,n)};o.getRemoteConfig=function getRemoteConfig(){return u("ai_get_remote_config")};o.getCompletionText=function getCompletionText(n){return u("ai_get_completion_text",{payload:n})};o.getExcerpt=function getExcerpt(n){return u("ai_get_excerpt",{payload:n})};o.getFeaturedImage=function getFeaturedImage(n){return u("ai_get_featured_image",{payload:n})};o.getEditText=function getEditText(n){return u("ai_get_edit_text",{payload:n})};o.getCustomCode=function getCustomCode(n){return u("ai_get_custom_code",{payload:n})};o.getCustomCSS=function getCustomCSS(n){return u("ai_get_custom_css",{payload:n})};o.setGetStarted=function setGetStarted(){return u("ai_set_get_started")};o.setStatusFeedback=function setStatusFeedback(n){return u("ai_set_status_feedback",{response_id:n})};o.getTextToImageGeneration=function getTextToImageGeneration(n){return u("ai_get_text_to_image",{payload:n})};o.getImageToImageGeneration=function getImageToImageGeneration(n){return u("ai_get_image_to_image",{payload:n})};o.getImageToImageMaskGeneration=function getImageToImageMaskGeneration(n){return u("ai_get_image_to_image_mask",{payload:n})};o.getImageToImageOutPainting=function getImageToImageOutPainting(n){return u("ai_get_image_to_image_outpainting",{payload:n})};o.getImageToImageUpscale=function getImageToImageUpscale(n){return u("ai_get_image_to_image_upscale",{payload:n})};o.getImageToImageRemoveBackground=function getImageToImageRemoveBackground(n){return u("ai_get_image_to_image_remove_background",{payload:n})};o.getImageToImageReplaceBackground=function getImageToImageReplaceBackground(n){return u("ai_get_image_to_image_replace_background",{payload:n})};o.getImageToImageRemoveText=function getImageToImageRemoveText(n){return u("ai_get_image_to_image_remove_text",{image:n})};o.getImagePromptEnhanced=function getImagePromptEnhanced(n){return u("ai_get_image_prompt_enhancer",{prompt:n})};o.uploadImage=function uploadImage(n){return u("ai_upload_image",function _objectSpread(n){for(var o=1;o<arguments.length;o++){var a=null!=arguments[o]?arguments[o]:{};o%2?ownKeys(Object(a),!0).forEach((function(o){(0,l.default)(n,o,a[o])})):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(a)):ownKeys(Object(a)).forEach((function(o){Object.defineProperty(n,o,Object.getOwnPropertyDescriptor(a,o))}))}return n}({},n))};o.generateLayout=function generateLayout(n,o){return u("ai_generate_layout",n,!0,o)};o.getLayoutPromptEnhanced=function getLayoutPromptEnhanced(n,o){return u("ai_get_layout_prompt_enhancer",{prompt:n,enhance_type:o})};o.getHistory=function getHistory(n,o,a){return u("ai_get_history",{type:n,page:o,limit:a})};o.deleteHistoryItem=function deleteHistoryItem(n){return u("ai_delete_history_item",{id:n})};o.toggleFavoriteHistoryItem=function toggleFavoriteHistoryItem(n){return u("ai_toggle_favorite_history_item",{id:n})}},95748:(n,o,a)=>{"use strict";var i=a(73203),l=a(7501);Object.defineProperty(o,"__esModule",{value:!0}),o.AlertDialog=void 0;var u=function _interopRequireWildcard(n,o){if(!o&&n&&n.__esModule)return n;if(null===n||"object"!==l(n)&&"function"!=typeof n)return{default:n};var a=_getRequireWildcardCache(o);if(a&&a.has(n))return a.get(n);var i={},u=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var c in n)if("default"!==c&&Object.prototype.hasOwnProperty.call(n,c)){var p=u?Object.getOwnPropertyDescriptor(n,c):null;p&&(p.get||p.set)?Object.defineProperty(i,c,p):i[c]=n[c]}i.default=n,a&&a.set(n,i);return i}(a(87363)),c=i(a(40131)),p=a(36626),w=a(38003),C=i(a(23615));function _getRequireWildcardCache(n){if("function"!=typeof WeakMap)return null;var o=new WeakMap,a=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(n){return n?a:o})(n)}var x=function AlertDialog(n){var o=(0,u.useState)(!0),a=(0,c.default)(o,2),i=a[0],l=a[1];return i?u.default.createElement(p.Dialog,{open:!0,maxWidth:"lg"},u.default.createElement(p.DialogContent,{sx:{padding:0}},u.default.createElement(p.Typography,{sx:{textAlign:"center",padding:3}},n.message),u.default.createElement(p.Stack,{alignItems:"center",spacing:2,marginBottom:2},u.default.createElement(p.Button,{variant:"contained",type:"button",color:"primary",onClick:function onClick(){var o;l(!1),null===(o=n.onClose)||void 0===o||o.call(n)}},(0,w.__)("Close","elementor"))))):null};o.AlertDialog=x,x.propTypes={message:C.default.string.isRequired,onClose:C.default.func}},78029:(n,o,a)=>{"use strict";var i=a(73203);Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var l=i(a(87363)),u=i(a(73119)),c=a(36626),p=a(38003),w=i(a(23615)),C=a(34029),x=(0,c.styled)((function ElementorLogo(n){return l.default.createElement(c.SvgIcon,(0,u.default)({viewBox:"0 0 32 32"},n),l.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2.69648 24.8891C0.938383 22.2579 0 19.1645 0 16C0 11.7566 1.68571 7.68687 4.68629 4.68629C7.68687 1.68571 11.7566 0 16 0C19.1645 0 22.2579 0.938383 24.8891 2.69648C27.5203 4.45459 29.5711 6.95344 30.7821 9.87706C31.9931 12.8007 32.3099 16.0177 31.6926 19.1214C31.0752 22.2251 29.5514 25.0761 27.3137 27.3137C25.0761 29.5514 22.2251 31.0752 19.1214 31.6926C16.0177 32.3099 12.8007 31.9931 9.87706 30.7821C6.95344 29.5711 4.45459 27.5203 2.69648 24.8891ZM12.0006 9.33281H9.33437V22.6665H12.0006V9.33281ZM22.6657 9.33281H14.6669V11.9991H22.6657V9.33281ZM22.6657 14.6654H14.6669V17.3316H22.6657V14.6654ZM22.6657 20.0003H14.6669V22.6665H22.6657V20.0003Z"}))}))((function(n){var o=n.theme;return{width:o.spacing(3),height:o.spacing(3),"& path":{fill:o.palette.text.primary}}})),S=function DialogHeader(n){return l.default.createElement(c.AppBar,{sx:{fontWeight:"normal"},color:"transparent",position:"relative"},l.default.createElement(c.Toolbar,{variant:"dense"},l.default.createElement(x,{sx:{mr:1}}),l.default.createElement(c.Typography,{component:"span",variant:"subtitle2",sx:{fontWeight:"bold",textTransform:"uppercase"}},(0,p.__)("AI","elementor")),l.default.createElement(c.Chip,{label:(0,p.__)("Beta","elementor"),color:"default",size:"small",sx:{ml:1}}),l.default.createElement(c.Stack,{direction:"row",spacing:1,alignItems:"center",sx:{ml:"auto"}},n.children,l.default.createElement(c.IconButton,{size:"small","aria-label":"close",onClick:n.onClose,sx:{"&.MuiButtonBase-root":{mr:-1}}},l.default.createElement(C.XIcon,null)))))};S.propTypes={onClose:w.default.func.isRequired,children:w.default.oneOfType([w.default.arrayOf(w.default.node),w.default.node])};var O=S;o.default=O},71871:(n,o,a)=>{"use strict";var i=a(73203);Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var l=i(a(87363)),u=i(a(73119)),c=i(a(93231)),p=i(a(70966)),w=a(36626),C=i(a(23615)),x=["sx","BoxProps"];function ownKeys(n,o){var a=Object.keys(n);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(n);o&&(i=i.filter((function(o){return Object.getOwnPropertyDescriptor(n,o).enumerable}))),a.push.apply(a,i)}return a}function _objectSpread(n){for(var o=1;o<arguments.length;o++){var a=null!=arguments[o]?arguments[o]:{};o%2?ownKeys(Object(a),!0).forEach((function(o){(0,c.default)(n,o,a[o])})):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(a)):ownKeys(Object(a)).forEach((function(o){Object.defineProperty(n,o,Object.getOwnPropertyDescriptor(a,o))}))}return n}var S=function Loader(n){var o=n.sx,a=void 0===o?{}:o,i=n.BoxProps,c=void 0===i?{}:i,C=(0,p.default)(n,x);return l.default.createElement(w.Box,(0,u.default)({width:"100%",display:"flex",alignItems:"center"},c,{sx:_objectSpread({px:1.5,minHeight:function minHeight(n){return n.spacing(5)}},c.sx||{})}),l.default.createElement(w.LinearProgress,(0,u.default)({color:"secondary"},C,{sx:_objectSpread({width:"100%"},a)})))};S.propTypes={sx:C.default.object,BoxProps:C.default.object};var O=S;o.default=O},59441:(n,o,a)=>{"use strict";var i=a(73203),l=a(7501);Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var u=function _interopRequireWildcard(n,o){if(!o&&n&&n.__esModule)return n;if(null===n||"object"!==l(n)&&"function"!=typeof n)return{default:n};var a=_getRequireWildcardCache(o);if(a&&a.has(n))return a.get(n);var i={},u=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var c in n)if("default"!==c&&Object.prototype.hasOwnProperty.call(n,c)){var p=u?Object.getOwnPropertyDescriptor(n,c):null;p&&(p.get||p.set)?Object.defineProperty(i,c,p):i[c]=n[c]}i.default=n,a&&a.set(n,i);return i}(a(87363)),c=i(a(73119)),p=i(a(93231)),w=i(a(40131)),C=a(36626),x=i(a(23615)),S=i(a(49529)),O=i(a(78029));function _getRequireWildcardCache(n){if("function"!=typeof WeakMap)return null;var o=new WeakMap,a=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(n){return n?a:o})(n)}function ownKeys(n,o){var a=Object.keys(n);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(n);o&&(i=i.filter((function(o){return Object.getOwnPropertyDescriptor(n,o).enumerable}))),a.push.apply(a,i)}return a}function _objectSpread(n){for(var o=1;o<arguments.length;o++){var a=null!=arguments[o]?arguments[o]:{};o%2?ownKeys(Object(a),!0).forEach((function(o){(0,p.default)(n,o,a[o])})):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(a)):ownKeys(Object(a)).forEach((function(o){Object.defineProperty(n,o,Object.getOwnPropertyDescriptor(a,o))}))}return n}var j=function DraggablePaper(n){var o=(0,u.useState)({x:0,y:0}),a=(0,w.default)(o,2),i=a[0],l=a[1],p=(0,u.useRef)(null),x=(0,u.useRef)(null),O=function handlePositionBoundaries(){clearTimeout(x.current),x.current=setTimeout((function(){var n,o=null===(n=p.current)||void 0===n?void 0:n.getBoundingClientRect().top;o<0&&l((function(n){return _objectSpread(_objectSpread({},n),{},{y:n.y-o})}))}),50)};return(0,u.useEffect)((function(){var n=new ResizeObserver(O);return n.observe(p.current),function(){n.disconnect()}}),[]),u.default.createElement(S.default,{position:i,onDrag:function onDrag(n,o){var a=o.x,i=o.y;return l({x:a,y:i})},handle:".MuiAppBar-root",cancel:'[class*="MuiDialogContent-root"]',bounds:"parent"},u.default.createElement(C.Paper,(0,c.default)({},n,{ref:p})))},k=function PromptDialog(n){return u.default.createElement(C.Dialog,(0,c.default)({scroll:"paper",open:!0,fullWidth:!0,hideBackdrop:!0,PaperComponent:j,disableScrollLock:!0,sx:{"& .MuiDialog-container":{alignItems:"flex-start",mt:"18vh"}},PaperProps:{sx:{m:0,maxHeight:"76vh"}}},n),n.children)};k.propTypes={onClose:x.default.func.isRequired,children:x.default.node,maxWidth:x.default.oneOf(["xs","sm","md","lg","xl",!1])},k.Header=O.default,k.Content=C.DialogContent;var R=k;o.default=R},86960:(n,o,a)=>{"use strict";var i=a(38003).sprintf,l=a(73203);Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var u=l(a(87363)),c=l(a(73119)),p=l(a(70966)),w=a(36626),C=a(38003),x=l(a(23615)),S=["error","onRetry","actionPosition"],O=function PromptErrorMessage(n){var o=n.error,a=n.onRetry,l=void 0===a?function(){}:a,x=n.actionPosition,O=void 0===x?"default":x,j=(0,p.default)(n,S);function getQuotaReachedTrailMessage(n){return n?{text:u.default.createElement(w.AlertTitle,null,i((0,C.__)("You've used all AI credits for %s.","elementor"),n.toLowerCase())),description:(0,C.__)("Upgrade now to keep using this feature. You still have credits for other AI features (Text, Code, Images, Containers, etc.)","elementor"),buttonText:(0,C.__)("Upgrade now","elementor"),buttonAction:function buttonAction(){return window.open("https://go.elementor.com/ai-popup-purchase-limit-reached/","_blank")}}:{text:u.default.createElement(w.AlertTitle,null,(0,C.__)("It's time to upgrade.","elementor")),description:(0,C.__)("Enjoy the free trial? Upgrade now for unlimited access to built-in image, text and custom code generators.","elementor"),buttonText:(0,C.__)("Upgrade","elementor"),buttonAction:function buttonAction(){return window.open("https://go.elementor.com/ai-popup-purchase-limit-reached/","_blank")}}}var k=function getErrorMessage(){var n,a=o.message||o,i=null===(n=o.extra_data)||void 0===n?void 0:n.featureName,c={default:{text:u.default.createElement(w.AlertTitle,null,(0,C.__)("There was a glitch.","elementor")),description:(0,C.__)("Wait a moment and give it another go, or try tweaking the prompt.","elementor"),buttonText:(0,C.__)("Try again","elementor"),buttonAction:l},service_outage_internal:{text:u.default.createElement(w.AlertTitle,null,(0,C.__)("There was a glitch.","elementor")),description:(0,C.__)("Wait a moment and give it another go.","elementor"),buttonText:(0,C.__)("Try again","elementor"),buttonAction:l},invalid_connect_data:{text:u.default.createElement(w.AlertTitle,null,(0,C.__)("There was a glitch.","elementor")),description:u.default.createElement(u.default.Fragment,null,(0,C.__)("Try exiting Elementor and sign in again.","elementor")," ",u.default.createElement("a",{href:"https://elementor.com/help/disconnecting-reconnecting-your-elementor-account/",target:"_blank",rel:"noreferrer"},(0,C.__)("Show me how","elementor"))),buttonText:(0,C.__)("Reconnect","elementor"),buttonAction:function buttonAction(){return window.open(window.ElementorAiConfig.connect_url)}},not_connected:{text:u.default.createElement(w.AlertTitle,null,(0,C.__)("You aren't connected to Elementor AI.","elementor")),description:(0,C.__)("Elementor AI is just a few clicks away. Connect your account to instantly create texts and custom code.","elementor"),buttonText:(0,C.__)("Connect","elementor"),buttonAction:function buttonAction(){return window.open(window.ElementorAiConfig.connect_url)}},quota_reached_trail:getQuotaReachedTrailMessage(i),quota_reached_subscription:{text:u.default.createElement(w.AlertTitle,null,(0,C.__)("Looks like you're out of credits.","elementor")),description:(0,C.__)("Ready to take it to the next level?","elementor"),buttonText:(0,C.__)("Upgrade now","elementor"),buttonAction:function buttonAction(){return window.open("https://go.elementor.com/ai-popup-purchase-limit-reached/","_blank")}},rate_limit_network:{text:u.default.createElement(w.AlertTitle,null,(0,C.__)("Whoa! Slow down there.","elementor")),description:(0,C.__)("We can’t process that many requests so fast. Try again in 15 minutes.","elementor")},invalid_prompts:{text:u.default.createElement(w.AlertTitle,null,(0,C.__)("We were unable to generate that prompt.","elementor")),description:(0,C.__)("Seems like the prompt contains words that could generate harmful content. Write a different prompt to continue.","elementor")},service_unavailable:{text:u.default.createElement(w.AlertTitle,null,(0,C.__)("There was a glitch.","elementor")),description:(0,C.__)("Wait a moment and give it another go, or try tweaking the prompt.","elementor"),buttonText:(0,C.__)("Try again","elementor"),buttonAction:l},request_timeout_error:{text:u.default.createElement(w.AlertTitle,null,(0,C.__)("There was a glitch.","elementor")),description:(0,C.__)("Wait a moment and give it another go, or try tweaking the prompt.","elementor"),buttonText:(0,C.__)("Try again","elementor"),buttonAction:l},invalid_token:{text:u.default.createElement(w.AlertTitle,null,(0,C.__)("Try again","elementor")),description:(0,C.__)("Try exiting Elementor and sign in again.","elementor"),buttonText:(0,C.__)("Reconnect","elementor"),buttonAction:l},file_too_large:{text:u.default.createElement(w.AlertTitle,null,(0,C.__)("The file is too large.","elementor")),description:(0,C.__)("Please upload a file that is less than 4MB.","elementor")}};return c[a]||c.default}(),R=(null==k?void 0:k.buttonText)&&u.default.createElement(w.Button,{color:"inherit",size:"small",variant:"outlined",onClick:k.buttonAction},k.buttonText);return u.default.createElement(w.Alert,(0,c.default)({severity:k.severity||"error",action:"default"===O&&R},j),k.text,k.description,"bottom"===O&&u.default.createElement(w.Box,{sx:{mt:1}},R))};O.propTypes={error:x.default.oneOfType([x.default.object,x.default.string]),onRetry:x.default.func,actionPosition:x.default.oneOf(["default","bottom"])};var j=O;o.default=j},84515:(n,o,a)=>{"use strict";var i=a(73203);Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var l=i(a(87363)),u=a(36626),c=a(38003),p=i(a(23615)),w=function PromptLibraryLink(n){return l.default.createElement(u.Typography,{variant:"body2",color:"text.secondary"},(0,c.__)("For more suggestions, explore our")," ",l.default.createElement(u.Link,{href:n.libraryLink,className:"elementor-clickable",target:"_blank"},(0,c.__)("prompt library")))};w.propTypes={libraryLink:p.default.string};var C=w;o.default=C},46369:(n,o,a)=>{"use strict";var i=a(73203),l=a(7501);Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var u=function _interopRequireWildcard(n,o){if(!o&&n&&n.__esModule)return n;if(null===n||"object"!==l(n)&&"function"!=typeof n)return{default:n};var a=_getRequireWildcardCache(o);if(a&&a.has(n))return a.get(n);var i={},u=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var c in n)if("default"!==c&&Object.prototype.hasOwnProperty.call(n,c)){var p=u?Object.getOwnPropertyDescriptor(n,c):null;p&&(p.get||p.set)?Object.defineProperty(i,c,p):i[c]=n[c]}i.default=n,a&&a.set(n,i);return i}(a(87363)),c=i(a(40131)),p=a(36626),w=a(38003),C=i(a(23615)),x=a(34029);function _getRequireWildcardCache(n){if("function"!=typeof WeakMap)return null;var o=new WeakMap,a=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(n){return n?a:o})(n)}var S=(0,p.styled)(p.Paper)((function(n){var o=n.theme;return{position:"relative",'[data-popper-placement="top"] &':{marginBottom:o.spacing(2.5)},'[data-popper-placement="bottom"] &':{marginTop:o.spacing(2.5)},padding:o.spacing(3),boxShadow:o.shadows[4],zIndex:"9999"}})),O=(0,p.styled)(p.Box)((function(n){var o=n.theme;return{width:o.spacing(5),height:o.spacing(2.5),position:"absolute",overflow:"hidden",left:"50% !important",transform:"translateX(-50%) rotate(var(--rotate, 0deg)) !important",'[data-popper-placement="top"] &':{top:"100%"},'[data-popper-placement="bottom"] &':{"--rotate":"180deg",top:"calc(".concat(o.spacing(2.5)," * -1)")},"&::after":{backgroundColor:o.palette.background.paper,content:'""',display:"block",position:"absolute",width:o.spacing(2.5),height:o.spacing(2.5),top:0,left:"50%",transform:"translateX(-50%) translateY(-50%) rotate(45deg)",boxShadow:"1px 1px 5px 0px rgba(0, 0, 0, 0.2)",backgroundImage:"linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))"}}})),j=[(0,w.__)("Get spot-on suggestions from AI Copilot and AI Context with appropriate designs, layouts, and content for your business.","elementor"),(0,w.__)("Generate professional texts about any topic, in any tone.","elementor"),(0,w.__)("Effortlessly create or enhance stunning images and bring your ideas to life.","elementor"),(0,w.__)("Unleash infinite possibilities with the custom code generator.","elementor"),(0,w.__)("Access 30-days of AI History with the AI Starter plan and 90-days with the Power plan.","elementor")],k=(0,p.styled)(p.Chip)((function(){return{"& .MuiChip-label":{lineHeight:1.5},"& .MuiSvgIcon-root.MuiChip-icon":{fontSize:"1.25rem"}}})),R=function UpgradeChip(n){var o=n.hasSubscription,a=void 0!==o&&o,i=n.usagePercentage,l=void 0===i?0:i,C=(0,u.useState)(!1),R=(0,c.default)(C,2),I=R[0],W=R[1],N=(0,u.useRef)(null),$=(0,u.useRef)(null),G="https://go.elementor.com/ai-popup-purchase-dropdown/";a&&(G=l>=100?"https://go.elementor.com/ai-popup-upgrade-limit-reached/":"https://go.elementor.com/ai-popup-upgrade-limit-reached-80-percent/");var J=a?(0,w.__)("Upgrade Elementor AI","elementor"):(0,w.__)("Get Elementor AI","elementor");return u.default.createElement(p.Box,{component:"span","aria-owns":I?"e-ai-upgrade-popover":void 0,"aria-haspopup":"true",onMouseEnter:function showPopover(){return W(!0)},onMouseLeave:function hidePopover(){return W(!1)},ref:N,display:"flex",alignItems:"center"},u.default.createElement(k,{color:"promotion",label:(0,w.__)("Upgrade","elementor"),icon:u.default.createElement(x.AIIcon,null),size:"small"}),u.default.createElement(p.Popper,{open:I,anchorEl:N.current,sx:{zIndex:"170001",maxWidth:300},modifiers:[{name:"arrow",enabled:!0,options:{element:$.current}}]},u.default.createElement(S,null,u.default.createElement(O,{ref:$}),u.default.createElement(p.Typography,{variant:"h5",color:"text.primary"},(0,w.__)("Unlimited access to Elementor AI","elementor")),u.default.createElement(p.List,{sx:{mb:1}},j.map((function(n,o){return u.default.createElement(p.ListItem,{key:o,disableGutters:!0,sx:{alignItems:"flex-start"}},u.default.createElement(p.ListItemIcon,null,u.default.createElement(x.CheckedCircleIcon,null)),u.default.createElement(p.ListItemText,{sx:{m:0}},u.default.createElement(p.Typography,{variant:"body2"},n)))}))),u.default.createElement(p.Button,{variant:"contained",color:"promotion",size:"small",href:G,target:"_blank",startIcon:u.default.createElement(x.AIIcon,null),sx:{"&:hover":{color:"promotion.contrastText"}}},J))))},I=R;o.default=I,R.propTypes={hasSubscription:C.default.bool,usagePercentage:C.default.number}},90246:(n,o,a)=>{"use strict";var i=a(73203);Object.defineProperty(o,"__esModule",{value:!0}),o.default=o.VoicePromotionAlert=void 0;var l=i(a(93231)),u=i(a(87363)),c=a(36626),p=i(a(21103)),w=i(a(24395)),C=i(a(23615)),x=a(38003);function ownKeys(n,o){var a=Object.keys(n);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(n);o&&(i=i.filter((function(o){return Object.getOwnPropertyDescriptor(n,o).enumerable}))),a.push.apply(a,i)}return a}function _objectSpread(n){for(var o=1;o<arguments.length;o++){var a=null!=arguments[o]?arguments[o]:{};o%2?ownKeys(Object(a),!0).forEach((function(o){(0,l.default)(n,o,a[o])})):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(a)):ownKeys(Object(a)).forEach((function(o){Object.defineProperty(n,o,Object.getOwnPropertyDescriptor(a,o))}))}return n}var S=function VoicePromotionAlert(n){var o=(0,w.default)(n.introductionKey),a=o.isViewed,i=o.markAsViewed;return a?null:u.default.createElement(c.Box,{sx:_objectSpread({mt:2},n.sx),alignItems:"top"},u.default.createElement(c.Alert,{severity:"info",variant:"standard",icon:u.default.createElement(p.default,{sx:{alignSelf:"flex-start"}}),onClose:i},(0,x.__)("Get improved results from AI by adding personal context.","elementor"),u.default.createElement(c.Link,{onClick:function onClick(){return $e.route("panel/global/menu")},className:"elementor-clickable",style:{textDecoration:"none"},color:"info.main",href:"#"},(0,x.__)("Let’s do it","elementor"))))};o.VoicePromotionAlert=S,S.propTypes={sx:C.default.object,introductionKey:C.default.string};var O=S;o.default=O},30313:(n,o,a)=>{"use strict";var i=a(73203);Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var l=i(a(87363)),u=i(a(73119)),c=i(a(93231)),p=i(a(70966)),w=a(36626),C=i(a(23615)),x=i(a(78029)),S=["sx"];function ownKeys(n,o){var a=Object.keys(n);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(n);o&&(i=i.filter((function(o){return Object.getOwnPropertyDescriptor(n,o).enumerable}))),a.push.apply(a,i)}return a}function _objectSpread(n){for(var o=1;o<arguments.length;o++){var a=null!=arguments[o]?arguments[o]:{};o%2?ownKeys(Object(a),!0).forEach((function(o){(0,c.default)(n,o,a[o])})):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(a)):ownKeys(Object(a)).forEach((function(o){Object.defineProperty(n,o,Object.getOwnPropertyDescriptor(a,o))}))}return n}var O=function WizardDialog(n){return l.default.createElement(w.Dialog,{open:!0,onClose:n.onClose,fullWidth:!0,hideBackdrop:!0,maxWidth:"lg",PaperProps:{sx:{height:"88vh"}},sx:{zIndex:9999}},n.children)};O.propTypes={onClose:C.default.func.isRequired,children:C.default.node.isRequired};var j=function WizardDialogContent(n){var o=n.sx,a=void 0===o?{}:o,i=(0,p.default)(n,S);return l.default.createElement(w.DialogContent,(0,u.default)({},i,{sx:_objectSpread({display:"flex",flexDirection:"column",justifyContent:"center"},a)}))};j.propTypes={sx:C.default.object},O.Header=x.default,O.Content=j;var k=O;o.default=k},35879:(n,o,a)=>{"use strict";var i=a(73203),l=a(7501);Object.defineProperty(o,"__esModule",{value:!0}),o.default=o.RequestIdsProvider=void 0,o.generateIds=function generateIds(n){var o;n.id=C().toString(),null!==(o=n.elements)&&void 0!==o&&o.length&&n.elements.map((function(n){return generateIds(n)}));return n},o.useRequestIds=o.getUniqueId=void 0;var u=function _interopRequireWildcard(n,o){if(!o&&n&&n.__esModule)return n;if(null===n||"object"!==l(n)&&"function"!=typeof n)return{default:n};var a=_getRequireWildcardCache(o);if(a&&a.has(n))return a.get(n);var i={},u=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var c in n)if("default"!==c&&Object.prototype.hasOwnProperty.call(n,c)){var p=u?Object.getOwnPropertyDescriptor(n,c):null;p&&(p.get||p.set)?Object.defineProperty(i,c,p):i[c]=n[c]}i.default=n,a&&a.set(n,i);return i}(a(87363)),c=i(a(40131)),p=i(a(23615));function _getRequireWildcardCache(n){if("function"!=typeof WeakMap)return null;var o=new WeakMap,a=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(n){return n?a:o})(n)}var w=(0,u.createContext)({});o.useRequestIds=function useRequestIds(){var n=(0,u.useContext)(w);if(!n)throw new Error("useRequestIds must be used within a RequestIdsProvider");return n};var C=function getUniqueId(n){return n+"-"+Math.random().toString(16).substr(2,7)};o.getUniqueId=C,window.EDITOR_SESSION_ID=window.EDITOR_SESSION_ID||C("editor-session");var x=function RequestIdsProvider(n){var o=(0,u.useRef)(window.EDITOR_SESSION_ID),a=(0,u.useRef)(""),i=(0,u.useRef)(""),l=(0,u.useRef)(""),p=(0,u.useRef)("");a.current=C("session");var x=(0,u.useState)(0),S=(0,c.default)(x,2),O=S[0],j=S[1];return u.default.createElement(w.Provider,{value:{editorSessionId:o,sessionId:a,generateId:i,batchId:l,requestId:p,setGenerate:function setGenerate(){return i.current=C("generate"),i},setBatch:function setBatch(){return l.current=C("batch"),l},setRequest:function setRequest(){return p.current=C("request"),p},usagePercentage:O,updateUsagePercentage:function updateUsagePercentage(n){j(n)}}},n.children)};o.RequestIdsProvider=x,x.propTypes={children:p.default.node.isRequired};var S=w;o.default=S},24395:(n,o,a)=>{"use strict";var i=a(73203);Object.defineProperty(o,"__esModule",{value:!0}),o.default=function useIntroduction(n){var o,a,i,c,p=window.elementor?null===(o=window.elementor.config)||void 0===o?void 0:o.user:null===(a=window.elementorAdmin)||void 0===a||null===(i=a.config)||void 0===i?void 0:i.user,w=(0,u.useState)(!(null==p||null===(c=p.introduction)||void 0===c||!c[n])),C=(0,l.default)(w,2),x=C[0],S=C[1];return{isViewed:x,markAsViewed:function markAsViewed(){return n?new Promise((function(o,a){x&&a(),S(!0),elementorCommon.ajax.addRequest("introduction_viewed",{data:{introductionKey:n},error:function error(){S(!1),a()},success:function success(){S(!0),null!=p&&p.introduction&&(p.introduction[n]=!0),o()}})})):Promise.reject()}}};var l=i(a(40131)),u=a(87363)},8817:(n,o,a)=>{"use strict";var i=a(73203);Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var l=a(46183),u=i(a(60675)),c=a(25455),p=new Map([["media",l.getImagePromptEnhanced],["layout",l.getLayoutPromptEnhanced]]),w=function usePromptEnhancer(n,o){var a=(0,c.useConfig)().mode,i=(0,u.default)((function(){return function getResult(n,o,a){if(!p.has(o))throw new Error("Invalid prompt type: ".concat(o));return p.get(o)(n,a)}(n,o,a)}),n),l=i.data,w=i.isLoading;return{enhance:i.send,isEnhancing:w,enhancedPrompt:null==l?void 0:l.result}};o.default=w},60675:(n,o,a)=>{"use strict";var i=a(73203);Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var l=i(a(50824)),u=i(a(93231)),c=i(a(10029)),p=i(a(40131)),w=i(a(70966)),C=a(87363),x=a(46183),S=a(35879),O=["text","response_id","usage","images"];function ownKeys(n,o){var a=Object.keys(n);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(n);o&&(i=i.filter((function(o){return Object.getOwnPropertyDescriptor(n,o).enumerable}))),a.push.apply(a,i)}return a}function _objectSpread(n){for(var o=1;o<arguments.length;o++){var a=null!=arguments[o]?arguments[o]:{};o%2?ownKeys(Object(a),!0).forEach((function(o){(0,u.default)(n,o,a[o])})):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(a)):ownKeys(Object(a)).forEach((function(o){Object.defineProperty(n,o,Object.getOwnPropertyDescriptor(a,o))}))}return n}var j=function normalizeResponse(n){var o=n.text,a=n.response_id,i=n.usage,l=n.images,u=(0,w.default)(n,O),c=i?i.quota-i.usedQuota:0,p={result:o||l,responseId:a,credits:Math.max(c,0),usagePercentage:null==i?void 0:i.usagePercentage};return u.base_template_id&&(p.baseTemplateId=u.base_template_id),p.type=u.template_type,p},k=function usePrompt(n,o){var a=(0,C.useState)(!1),i=(0,p.default)(a,2),u=i[0],w=i[1],O=(0,C.useState)(""),k=(0,p.default)(O,2),R=k[0],I=k[1],W=(0,C.useState)(o),N=(0,p.default)(W,2),$=N[0],G=N[1],J=(0,S.useRequestIds)(),re=J.updateUsagePercentage,oe=J.usagePercentage;(0,C.useEffect)((function(){var n=null==$?void 0:$.usagePercentage;n&&n!==oe&&re(n)}),[$,oe,re]);var ie=(0,S.useRequestIds)(),le=ie.setRequest,ue=ie.editorSessionId,ce=ie.sessionId,se=ie.generateId,de=ie.batchId,he=function(){var o=(0,c.default)(l.default.mark((function _callee(o){return l.default.wrap((function _callee$(a){for(;;)switch(a.prev=a.next){case 0:return a.abrupt("return",new Promise((function(a,i){I(""),w(!0);var l=le(),u={editorSessionId:ue.current,sessionId:ce.current,generateId:se.current,batchId:de.current,requestId:l.current};o=_objectSpread(_objectSpread({},o),{},{requestIds:u}),n(o).then((function(n){var o=j(n);G(o),a(o)})).catch((function(n){var o=(null==n?void 0:n.responseText)||n;I(o),i(o)})).finally((function(){return w(!1)}))})));case 1:case"end":return a.stop()}}),_callee)})));return function send(n){return o.apply(this,arguments)}}();return{isLoading:u,error:R,data:$,setResult:function setResult(n){var o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,a=_objectSpread({},$);a.result=n,o&&(a.responseId=o),G(a)},reset:function reset(){G((function(n){return{credits:n.credits,result:"",responseId:""}})),I(""),w(!1)},send:he,sendUsageData:function sendUsageData(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:$;return n.responseId&&(0,x.setStatusFeedback)(n.responseId)}}};o.default=k},74859:(n,o,a)=>{"use strict";var i=a(73203);Object.defineProperty(o,"__esModule",{value:!0}),o.useTimeout=void 0;var l=i(a(40131)),u=a(87363);o.useTimeout=function useTimeout(n){var o=(0,u.useState)(!1),a=(0,l.default)(o,2),i=a[0],c=a[1],p=(0,u.useRef)(null);return(0,u.useEffect)((function(){return p.current=setTimeout((function(){c(!0)}),n),function(){clearTimeout(p.current)}}),[n]),[i,function turnOffTimeout(){clearTimeout(p.current),c(!1)}]}},5647:(n,o,a)=>{"use strict";var i=a(73203);Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var l=i(a(50824)),u=i(a(93231)),c=i(a(10029)),p=i(a(40131)),w=a(87363),C=a(46183),x=i(a(23615));function ownKeys(n,o){var a=Object.keys(n);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(n);o&&(i=i.filter((function(o){return Object.getOwnPropertyDescriptor(n,o).enumerable}))),a.push.apply(a,i)}return a}function _objectSpread(n){for(var o=1;o<arguments.length;o++){var a=null!=arguments[o]?arguments[o]:{};o%2?ownKeys(Object(a),!0).forEach((function(o){(0,u.default)(n,o,a[o])})):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(a)):ownKeys(Object(a)).forEach((function(o){Object.defineProperty(n,o,Object.getOwnPropertyDescriptor(a,o))}))}return n}var S=function useUserInfo(){var n=arguments.length>0&&void 0!==arguments[0]&&arguments[0],o=(0,w.useState)(!1),a=(0,p.default)(o,2),i=a[0],u=a[1],x=(0,w.useState)(!1),S=(0,p.default)(x,2),O=S[0],j=S[1],k=(0,w.useState)({is_connected:!1,is_get_started:!1,connect_url:"",usage:{hasAiSubscription:!1,quota:0,usedQuota:0}}),R=(0,p.default)(k,2),I=R[0],W=R[1],N=I.usage.quota-I.usage.usedQuota,$=I.usage.quota?I.usage.usedQuota/I.usage.quota*100:0,G=function(){var o=(0,c.default)(l.default.mark((function _callee(){var o;return l.default.wrap((function _callee$(a){for(;;)switch(a.prev=a.next){case 0:return j(!0),a.next=3,(0,C.getUserInformation)(n);case 3:o=a.sent,W((function(n){return _objectSpread(_objectSpread({},n),o)})),u(!0),j(!1);case 7:case"end":return a.stop()}}),_callee)})));return function fetchData(){return o.apply(this,arguments)}}();return i||O||G(),{isLoading:O,isLoaded:i,isConnected:I.is_connected,isGetStarted:I.is_get_started,connectUrl:I.connect_url,builderUrl:I.usage.builderUrl,hasSubscription:I.usage.hasAiSubscription,credits:N<0?0:N,usagePercentage:Math.round($),fetchData:G}};S.propTypes={immediately:x.default.bool};var O=S;o.default=O},62682:(n,o,a)=>{"use strict";var i=a(73203);Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var l=i(a(73119)),u=i(a(87363)),c=a(36626),p=u.default.forwardRef((function(n,o){return u.default.createElement(c.SvgIcon,(0,l.default)({viewBox:"0 0 24 24"},n,{ref:o}),u.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M9.53033 7.46967C9.82322 7.76256 9.82322 8.23744 9.53033 8.53033L6.81066 11.25H19C19.4142 11.25 19.75 11.5858 19.75 12C19.75 12.4142 19.4142 12.75 19 12.75H6.81066L9.53033 15.4697C9.82322 15.7626 9.82322 16.2374 9.53033 16.5303C9.23744 16.8232 8.76256 16.8232 8.46967 16.5303L4.46967 12.5303C4.17678 12.2374 4.17678 11.7626 4.46967 11.4697L8.46967 7.46967C8.76256 7.17678 9.23744 7.17678 9.53033 7.46967Z"}))}));o.default=p},21103:(n,o,a)=>{"use strict";var i=a(73203);Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var l=i(a(73119)),u=i(a(87363)),c=a(36626),p=u.default.forwardRef((function(n,o){return u.default.createElement(c.SvgIcon,(0,l.default)({},n,{ref:o}),u.default.createElement("svg",{width:"22",height:"22",viewBox:"0 0 22 22",fill:"none",xmlns:"http://www.w3.org/2000/svg"},u.default.createElement("g",{clipPath:"url(#clip0_10743_8902)"},u.default.createElement("path",{d:"M2.75 10.0833H3.66667M11 2.75V3.66667M18.3333 10.0833H19.25M5.13333 5.13333L5.775 5.775M16.8667 5.13333L16.225 5.775",stroke:"#2563EB",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),u.default.createElement("path",{d:"M9.16675 16.041C8.70841 15.1243 6.91205 13.2842 6.62523 12.366C6.3384 11.4477 6.34775 10.4626 6.65195 9.54997C6.95615 8.63738 7.53978 7.84362 8.32016 7.28116C9.10054 6.71869 10.0381 6.41602 11.0001 6.41602C11.962 6.41602 12.8996 6.71869 13.68 7.28116C14.4604 7.84362 15.044 8.63738 15.3482 9.54997C15.6524 10.4626 15.6618 11.4477 15.3749 12.366C15.0881 13.2842 13.2917 15.1243 12.8334 16.041C12.8334 16.041 12.7597 17.3762 12.8334 17.8743C12.8334 18.3606 12.6403 18.8269 12.2964 19.1707C11.9526 19.5145 11.4863 19.7077 11.0001 19.7077C10.5139 19.7077 10.0475 19.5145 9.70372 19.1707C9.3599 18.8269 9.16675 18.3606 9.16675 17.8743C9.2405 17.3762 9.16675 16.041 9.16675 16.041Z",stroke:"#2563EB",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),u.default.createElement("path",{d:"M10.0833 16.5H11.9166",stroke:"#2563EB",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})),u.default.createElement("defs",null,u.default.createElement("clipPath",{id:"clip0_10743_8902"},u.default.createElement("rect",{width:"22",height:"22",fill:"white"})))))}));o.default=p},68873:(n,o,a)=>{"use strict";var i=a(73203);Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var l=i(a(73119)),u=i(a(87363)),c=a(36626),p=u.default.forwardRef((function(n,o){return u.default.createElement(c.SvgIcon,(0,l.default)({viewBox:"0 0 24 24"},n,{ref:o}),u.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M16.6667 0.208496C17.0534 0.208496 17.4244 0.362142 17.6979 0.635632C17.9714 0.909123 18.125 1.28006 18.125 1.66683V11.6668C18.125 12.0536 17.9714 12.4245 17.6979 12.698C17.4244 12.9715 17.0534 13.1252 16.6667 13.1252H14.7917V16.6668C14.7917 17.0536 14.638 17.4245 14.3645 17.698C14.091 17.9715 13.7201 18.1252 13.3333 18.1252H3.33333C2.94656 18.1252 2.57563 17.9715 2.30214 17.698C2.02865 17.4245 1.875 17.0536 1.875 16.6668V6.66683C1.875 6.28005 2.02865 5.90912 2.30214 5.63563C2.57563 5.36214 2.94656 5.2085 3.33333 5.2085H5.20833V1.66683C5.20833 1.28005 5.36198 0.909122 5.63547 0.635632C5.90896 0.362142 6.27989 0.208496 6.66667 0.208496H16.6667ZM6.66667 1.4585C6.61141 1.4585 6.55842 1.48045 6.51935 1.51952C6.48028 1.55859 6.45833 1.61158 6.45833 1.66683V3.54183H8.54167V1.4585H6.66667ZM3.125 9.79183V16.6668C3.125 16.7221 3.14695 16.7751 3.18602 16.8141C3.22509 16.8532 3.27808 16.8752 3.33333 16.8752H13.3333C13.3886 16.8752 13.4416 16.8532 13.4806 16.8141C13.5197 16.7751 13.5417 16.7221 13.5417 16.6668V13.1252H6.66667C6.27989 13.1252 5.90896 12.9715 5.63547 12.698C5.36198 12.4245 5.20833 12.0536 5.20833 11.6668V9.79183H3.125ZM5.20833 8.54183H3.125V6.66683C3.125 6.61158 3.14695 6.55859 3.18602 6.51952C3.22509 6.48045 3.27808 6.4585 3.33333 6.4585H5.20833V8.54183ZM6.45833 11.6668C6.45833 11.7221 6.48028 11.7751 6.51935 11.8141C6.55842 11.8532 6.61141 11.8752 6.66667 11.8752H16.6667C16.7219 11.8752 16.7749 11.8532 16.814 11.8141C16.853 11.7751 16.875 11.7221 16.875 11.6668V4.79183H6.45833V11.6668ZM9.79167 1.4585V3.54183H16.875V1.66683C16.875 1.61157 16.853 1.55858 16.814 1.51952C16.7749 1.48045 16.7219 1.4585 16.6667 1.4585H9.79167Z"}))}));o.default=p},33375:(n,o,a)=>{"use strict";var i=a(73203);Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var l=i(a(73119)),u=i(a(87363)),c=a(36626),p=u.default.forwardRef((function(n,o){return u.default.createElement(c.SvgIcon,(0,l.default)({viewBox:"0 0 24 24"},n,{ref:o}),u.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M13.9697 4.96967C14.6408 4.29858 15.5509 3.92157 16.5 3.92157C17.4491 3.92157 18.3592 4.29858 19.0303 4.96967C19.7014 5.64075 20.0784 6.55094 20.0784 7.5C20.0784 8.44905 19.7014 9.35924 19.0303 10.0303L8.53033 20.5303C8.38968 20.671 8.19891 20.75 8 20.75H4C3.58579 20.75 3.25 20.4142 3.25 20V16C3.25 15.8011 3.32902 15.6103 3.46967 15.4697L13.9697 4.96967ZM16.5 5.42157C15.9488 5.42157 15.4201 5.64055 15.0303 6.03033L4.75 16.3107V19.25H7.68934L17.9697 8.96967C18.3595 8.57989 18.5784 8.05123 18.5784 7.5C18.5784 6.94876 18.3595 6.42011 17.9697 6.03033C17.5799 5.64055 17.0512 5.42157 16.5 5.42157Z"}),u.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12.9697 5.96967C13.2626 5.67677 13.7374 5.67677 14.0303 5.96967L18.0303 9.96967C18.3232 10.2626 18.3232 10.7374 18.0303 11.0303C17.7374 11.3232 17.2626 11.3232 16.9697 11.0303L12.9697 7.03033C12.6768 6.73743 12.6768 6.26256 12.9697 5.96967Z"}))}));o.default=p},49294:(n,o,a)=>{"use strict";var i=a(73203);Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var l=i(a(73119)),u=i(a(87363)),c=a(36626),p=u.default.forwardRef((function(n,o){return u.default.createElement(c.SvgIcon,(0,l.default)({viewBox:"0 0 24 24"},n,{ref:o}),u.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4 3.25H8C8.41421 3.25 8.75 3.58579 8.75 4C8.75 4.41421 8.41421 4.75 8 4.75H5.81066L10.5303 9.46967C10.8232 9.76256 10.8232 10.2374 10.5303 10.5303C10.2374 10.8232 9.76256 10.8232 9.46967 10.5303L4.75 5.81066V8C4.75 8.41421 4.41421 8.75 4 8.75C3.58579 8.75 3.25 8.41421 3.25 8V4C3.25 3.58579 3.58579 3.25 4 3.25ZM13.4697 13.4697C13.7626 13.1768 14.2374 13.1768 14.5303 13.4697L19.25 18.1893V16C19.25 15.5858 19.5858 15.25 20 15.25C20.4142 15.25 20.75 15.5858 20.75 16V20C20.75 20.4142 20.4142 20.75 20 20.75H16C15.5858 20.75 15.25 20.4142 15.25 20C15.25 19.5858 15.5858 19.25 16 19.25H18.1893L13.4697 14.5303C13.1768 14.2374 13.1768 13.7626 13.4697 13.4697Z"}))}));o.default=p},30344:(n,o,a)=>{"use strict";var i=a(73203);Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var l=i(a(73119)),u=i(a(87363)),c=a(36626),p=u.default.forwardRef((function(n,o){return u.default.createElement(c.SvgIcon,(0,l.default)({viewBox:"0 0 24 24"},n,{ref:o}),u.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7.8125 11.9996C7.29473 11.9996 6.875 12.4473 6.875 12.9996V18.9996C6.875 19.5519 7.29473 19.9996 7.8125 19.9996H17.1875C17.7053 19.9996 18.125 19.5519 18.125 18.9996V12.9996C18.125 12.4473 17.7053 11.9996 17.1875 11.9996H7.8125ZM5 12.9996C5 11.3428 6.2592 9.99963 7.8125 9.99963H17.1875C18.7408 9.99963 20 11.3428 20 12.9996V18.9996C20 20.6565 18.7408 21.9996 17.1875 21.9996H7.8125C6.2592 21.9996 5 20.6565 5 18.9996V12.9996Z"}),u.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12.5 3.90527C11.7044 3.90527 10.9413 4.22134 10.3787 4.78395C9.81607 5.34656 9.5 6.10962 9.5 6.90527V10.9053C9.5 11.4576 9.05228 11.9053 8.5 11.9053C7.94772 11.9053 7.5 11.4576 7.5 10.9053V6.90527C7.5 5.57919 8.02678 4.30742 8.96447 3.36974C9.90215 2.43206 11.1739 1.90527 12.5 1.90527C13.8261 1.90527 15.0979 2.43206 16.0355 3.36974C16.9732 4.30742 17.5 5.57919 17.5 6.90527V10.9053C17.5 11.4576 17.0523 11.9053 16.5 11.9053C15.9477 11.9053 15.5 11.4576 15.5 10.9053V6.90527C15.5 6.10962 15.1839 5.34656 14.6213 4.78395C14.0587 4.22134 13.2956 3.90527 12.5 3.90527Z"}),u.default.createElement("path",{d:"M6 12H19V20H6V12Z"}))}));o.default=p},7377:(n,o,a)=>{"use strict";var i=a(73203);Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var l=i(a(73119)),u=i(a(87363)),c=a(36626),p=u.default.forwardRef((function(n,o){return u.default.createElement(c.SvgIcon,(0,l.default)({viewBox:"0 0 24 24"},n,{ref:o}),u.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.46967 3.46967C3.76256 3.17678 4.23744 3.17678 4.53033 3.46967L9.25 8.18934V6C9.25 5.58579 9.58579 5.25 10 5.25C10.4142 5.25 10.75 5.58579 10.75 6V10C10.75 10.4142 10.4142 10.75 10 10.75H6C5.58579 10.75 5.25 10.4142 5.25 10C5.25 9.58579 5.58579 9.25 6 9.25H8.18934L3.46967 4.53033C3.17678 4.23744 3.17678 3.76256 3.46967 3.46967ZM14 13.25H18C18.4142 13.25 18.75 13.5858 18.75 14C18.75 14.4142 18.4142 14.75 18 14.75H15.8107L20.5303 19.4697C20.8232 19.7626 20.8232 20.2374 20.5303 20.5303C20.2374 20.8232 19.7626 20.8232 19.4697 20.5303L14.75 15.8107V18C14.75 18.4142 14.4142 18.75 14 18.75C13.5858 18.75 13.25 18.4142 13.25 18V14C13.25 13.5858 13.5858 13.25 14 13.25Z"}))}));o.default=p},78170:(n,o,a)=>{"use strict";var i=a(73203);Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var l=i(a(73119)),u=i(a(87363)),c=a(36626),p=u.default.forwardRef((function(n,o){return u.default.createElement(c.SvgIcon,(0,l.default)({viewBox:"0 0 24 24"},n,{ref:o}),u.default.createElement("path",{d:"M12 2.69231C6.8595 2.69231 2.69231 6.8595 2.69231 12C2.69231 17.1405 6.8595 21.3077 12 21.3077C17.1405 21.3077 21.3077 17.1405 21.3077 12C21.3077 6.8595 17.1405 2.69231 12 2.69231ZM1 12C1 5.92487 5.92487 1 12 1C18.0751 1 23 5.92487 23 12C23 18.0751 18.0751 23 12 23C5.92487 23 1 18.0751 1 12ZM12 7.76923C12.4673 7.76923 12.8462 8.14807 12.8462 8.61538V11.1538H15.3846C15.8519 11.1538 16.2308 11.5327 16.2308 12C16.2308 12.4673 15.8519 12.8462 15.3846 12.8462H12.8462V15.3846C12.8462 15.8519 12.4673 16.2308 12 16.2308C11.5327 16.2308 11.1538 15.8519 11.1538 15.3846V12.8462H8.61538C8.14807 12.8462 7.76923 12.4673 7.76923 12C7.76923 11.5327 8.14807 11.1538 8.61538 11.1538H11.1538V8.61538C11.1538 8.14807 11.5327 7.76923 12 7.76923Z"}))}));o.default=p},28965:(n,o,a)=>{"use strict";var i=a(73203);Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var l=i(a(73119)),u=i(a(87363)),c=a(36626),p=u.default.forwardRef((function(n,o){return u.default.createElement(c.SvgIcon,(0,l.default)({viewBox:"0 0 24 24"},n,{ref:o}),u.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7.55012 4.45178C9.23098 3.48072 11.1845 3.08925 13.1097 3.33767C15.035 3.58609 16.8251 4.46061 18.2045 5.82653C19.5838 7.19245 20.4757 8.97399 20.743 10.8967C20.8 11.307 20.5136 11.6858 20.1033 11.7428C19.6931 11.7998 19.3142 11.5135 19.2572 11.1032C19.0353 9.50635 18.2945 8.02677 17.149 6.89236C16.0035 5.75795 14.5167 5.03165 12.9178 4.82534C11.3189 4.61902 9.69644 4.94414 8.30047 5.75061C7.24361 6.36117 6.36093 7.22198 5.72541 8.24995H8.00009C8.41431 8.24995 8.75009 8.58574 8.75009 8.99995C8.75009 9.41417 8.41431 9.74995 8.00009 9.74995H4.51686C4.5055 9.75021 4.49412 9.75021 4.48272 9.74995H4.00009C3.58588 9.74995 3.25009 9.41417 3.25009 8.99995V4.99995C3.25009 4.58574 3.58588 4.24995 4.00009 4.24995C4.41431 4.24995 4.75009 4.58574 4.75009 4.99995V7.00691C5.48358 5.96916 6.43655 5.0951 7.55012 4.45178Z"}),u.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.89686 12.2571C4.30713 12.2001 4.68594 12.4864 4.74295 12.8967C4.96487 14.4936 5.70565 15.9731 6.85119 17.1075C7.99673 18.242 9.48347 18.9683 11.0824 19.1746C12.6813 19.3809 14.3037 19.0558 15.6997 18.2493C16.7566 17.6387 17.6393 16.7779 18.2748 15.75H16.0001C15.5859 15.75 15.2501 15.4142 15.2501 15C15.2501 14.5857 15.5859 14.25 16.0001 14.25H19.4833C19.4947 14.2497 19.5061 14.2497 19.5175 14.25H20.0001C20.4143 14.25 20.7501 14.5857 20.7501 15V19C20.7501 19.4142 20.4143 19.75 20.0001 19.75C19.5859 19.75 19.2501 19.4142 19.2501 19V16.993C18.5166 18.0307 17.5636 18.9048 16.4501 19.5481C14.7692 20.5192 12.8157 20.9107 10.8904 20.6622C8.9652 20.4138 7.17504 19.5393 5.79572 18.1734C4.4164 16.8074 3.52443 15.0259 3.25723 13.1032C3.20022 12.6929 3.48658 12.3141 3.89686 12.2571Z"}))}));o.default=p},55995:(n,o,a)=>{"use strict";var i=a(73203);Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var l=i(a(73119)),u=i(a(87363)),c=a(36626),p=u.default.forwardRef((function(n,o){return u.default.createElement(c.SvgIcon,(0,l.default)({viewBox:"0 0 24 24"},n,{ref:o}),u.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M9 2.25C9.41421 2.25 9.75 2.58579 9.75 3C9.75 3.33152 9.8817 3.64946 10.1161 3.88388C10.3505 4.1183 10.6685 4.25 11 4.25C11.4142 4.25 11.75 4.58579 11.75 5C11.75 5.41421 11.4142 5.75 11 5.75C10.6685 5.75 10.3505 5.8817 10.1161 6.11612C9.8817 6.35054 9.75 6.66848 9.75 7C9.75 7.41421 9.41421 7.75 9 7.75C8.58579 7.75 8.25 7.41421 8.25 7C8.25 6.66848 8.1183 6.35054 7.88388 6.11612C7.64946 5.8817 7.33152 5.75 7 5.75C6.58579 5.75 6.25 5.41421 6.25 5C6.25 4.58579 6.58579 4.25 7 4.25C7.33152 4.25 7.64946 4.1183 7.88388 3.88388C8.1183 3.64946 8.25 3.33152 8.25 3C8.25 2.58579 8.58579 2.25 9 2.25ZM9 4.88746C8.98182 4.90673 8.96333 4.92576 8.94454 4.94454C8.92576 4.96333 8.90673 4.98182 8.88746 5C8.90673 5.01818 8.92576 5.03667 8.94454 5.05546C8.96333 5.07424 8.98182 5.09327 9 5.11254C9.01818 5.09327 9.03667 5.07424 9.05546 5.05546C9.07424 5.03667 9.09327 5.01818 9.11254 5C9.09327 4.98182 9.07424 4.96333 9.05546 4.94454C9.03667 4.92576 9.01818 4.90673 9 4.88746Z"}),u.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M18.5303 2.46967C18.2374 2.17678 17.7626 2.17678 17.4697 2.46967L2.46967 17.4697C2.17678 17.7626 2.17678 18.2374 2.46967 18.5303L5.46967 21.5303C5.76256 21.8232 6.23744 21.8232 6.53033 21.5303L21.5303 6.53033C21.8232 6.23744 21.8232 5.76256 21.5303 5.46967L18.5303 2.46967ZM18 7.93934L19.9393 6L18 4.06066L16.0607 6L18 7.93934ZM15 7.06066L16.9393 9L6 19.9393L4.06066 18L15 7.06066Z"}),u.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M19.75 13C19.75 12.5858 19.4142 12.25 19 12.25C18.5858 12.25 18.25 12.5858 18.25 13C18.25 13.3315 18.1183 13.6495 17.8839 13.8839C17.6495 14.1183 17.3315 14.25 17 14.25C16.5858 14.25 16.25 14.5858 16.25 15C16.25 15.4142 16.5858 15.75 17 15.75C17.3315 15.75 17.6495 15.8817 17.8839 16.1161C18.1183 16.3505 18.25 16.6685 18.25 17C18.25 17.4142 18.5858 17.75 19 17.75C19.4142 17.75 19.75 17.4142 19.75 17C19.75 16.6685 19.8817 16.3505 20.1161 16.1161C20.3505 15.8817 20.6685 15.75 21 15.75C21.4142 15.75 21.75 15.4142 21.75 15C21.75 14.5858 21.4142 14.25 21 14.25C20.6685 14.25 20.3505 14.1183 20.1161 13.8839C19.8817 13.6495 19.75 13.3315 19.75 13ZM18.9445 14.9445C18.9633 14.9258 18.9818 14.9067 19 14.8875C19.0182 14.9067 19.0367 14.9258 19.0555 14.9445C19.0742 14.9633 19.0933 14.9818 19.1125 15C19.0933 15.0182 19.0742 15.0367 19.0555 15.0555C19.0367 15.0742 19.0182 15.0933 19 15.1125C18.9818 15.0933 18.9633 15.0742 18.9445 15.0555C18.9258 15.0367 18.9067 15.0182 18.8875 15C18.9067 14.9818 18.9258 14.9633 18.9445 14.9445Z"}))}));o.default=p},54572:(n,o,a)=>{"use strict";var i=a(73203);Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var l=i(a(73119)),u=i(a(87363)),c=a(36626),p=u.default.forwardRef((function(n,o){return u.default.createElement(c.SvgIcon,(0,l.default)({viewBox:"0 0 24 24"},n,{ref:o}),u.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4.16707 3.95837C4.11182 3.95837 4.05883 3.98032 4.01976 4.01939C3.98069 4.05846 3.95874 4.11145 3.95874 4.16671V6.04171H6.04207V3.95837H4.16707ZM4.16707 2.70837C3.7803 2.70837 3.40937 2.86202 3.13588 3.13551C2.86239 3.409 2.70874 3.77993 2.70874 4.16671V15.8334C2.70874 16.2201 2.86239 16.5911 3.13588 16.8646C3.40937 17.1381 3.7803 17.2917 4.16707 17.2917H15.8337C16.2205 17.2917 16.5914 17.1381 16.8649 16.8646C17.1384 16.5911 17.2921 16.2201 17.2921 15.8334V4.16671C17.2921 3.77993 17.1384 3.409 16.8649 3.13551C16.5914 2.86202 16.2205 2.70837 15.8337 2.70837H4.16707ZM7.29207 3.95837V6.04171H16.0421V4.16671C16.0421 4.11145 16.0201 4.05846 15.9811 4.01939C15.942 3.98032 15.889 3.95837 15.8337 3.95837H7.29207ZM16.0421 7.29171H3.95874V15.8334C3.95874 15.8886 3.98069 15.9416 4.01976 15.9807C4.05883 16.0198 4.11182 16.0417 4.16707 16.0417H15.8337C15.889 16.0417 15.942 16.0198 15.9811 15.9807C16.0201 15.9416 16.0421 15.8886 16.0421 15.8334V7.29171Z"}))}));o.default=p},941:(n,o,a)=>{"use strict";var i=a(73203);Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var l=i(a(73119)),u=i(a(87363)),c=a(36626),p=u.default.forwardRef((function(n,o){return u.default.createElement(c.SvgIcon,(0,l.default)({viewBox:"0 0 24 24"},n,{ref:o}),u.default.createElement("path",{d:"M12 2.69231C6.8595 2.69231 2.69231 6.8595 2.69231 12C2.69231 17.1405 6.8595 21.3077 12 21.3077C17.1405 21.3077 21.3077 17.1405 21.3077 12C21.3077 6.8595 17.1405 2.69231 12 2.69231ZM1 12C1 5.92487 5.92487 1 12 1C18.0751 1 23 5.92487 23 12C23 18.0751 18.0751 23 12 23C5.92487 23 1 18.0751 1 12ZM9.14527 9.14527C9.47571 8.81483 10.0115 8.81483 10.3419 9.14527L12 10.8034L13.6581 9.14527C13.9885 8.81483 14.5243 8.81483 14.8547 9.14527C15.1852 9.47571 15.1852 10.0115 14.8547 10.3419L13.1966 12L14.8547 13.6581C15.1852 13.9885 15.1852 14.5243 14.8547 14.8547C14.5243 15.1852 13.9885 15.1852 13.6581 14.8547L12 13.1966L10.3419 14.8547C10.0115 15.1852 9.47571 15.1852 9.14527 14.8547C8.81483 14.5243 8.81483 13.9885 9.14527 13.6581L10.8034 12L9.14527 10.3419C8.81483 10.0115 8.81483 9.47571 9.14527 9.14527Z"}))}));o.default=p},5637:(n,o,a)=>{"use strict";var i,l=a(36619),u=l.renderLayoutApp,c=l.importToEditor,p=a(25455).MODE_VARIATION,w=a(38003).__,C=a(28378),x=C.ATTACHMENT_TYPE_JSON,S=C.ELEMENTOR_LIBRARY_SOURCE;i=Marionette.Behavior.extend({ui:{applyButton:".elementor-template-library-template-apply-ai",generateVariation:".elementor-template-library-template-generate-variation"},events:{"click @ui.applyButton":"onApplyButtonClick","click @ui.generateVariation":"onGenerateVariationClick"},onGenerateVariationClick:function onGenerateVariationClick(){var n,o,a={model:this.view.model},i=$e.components.get("library"),l=null===(n=i.manager.modalConfig)||void 0===n||null===(o=n.importOptions)||void 0===o?void 0:o.at;i.downloadTemplate(a,(function(n){var o=a.model,i={type:x,previewHTML:'<img src="'.concat(o.get("thumbnail"),'" />'),content:n.content[0],label:"".concat(o.get("template_id")," - ").concat(o.get("title")),source:S};u({parentContainer:elementor.getPreviewContainer(),mode:p,at:l,attachments:[i],onInsert:function onInsert(n){c({parentContainer:elementor.getPreviewContainer(),at:l,template:n,historyTitle:w("AI Variation from library","elementor")})}}),$e.run("library/close")}))},onApplyButtonClick:function onApplyButtonClick(){var n={model:this.view.model};this.ui.applyButton.addClass("elementor-disabled"),"remote"!==n.model.get("source")||elementor.config.library_connect.is_connected?$e.run("library/generate-ai-variation",n):$e.route("library/connect",n)}}),n.exports=i},42637:(n,o,a)=>{"use strict";var i=a(73203);Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var l=i(a(87363)),u=a(36626),c=i(a(23615)),p=function LayoutAppWrapper(n){return l.default.createElement(u.DirectionProvider,{rtl:n.isRTL},l.default.createElement(u.ThemeProvider,{colorScheme:n.colorScheme},n.children))};p.propTypes={children:c.default.node,isRTL:c.default.bool,colorScheme:c.default.oneOf(["auto","light","dark"])};var w=p;o.default=w},78949:(n,o,a)=>{"use strict";var i=a(73203);Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var l=i(a(87363)),u=i(a(23615)),c=i(a(26457)),p=a(77292),w=a(25455),C=a(84157),x=a(35879),S=function LayoutApp(n){return l.default.createElement(C.RemoteConfigProvider,{onError:n.onClose},l.default.createElement(x.RequestIdsProvider,null,l.default.createElement(w.ConfigProvider,{mode:n.mode,attachmentsTypes:n.attachmentsTypes,onClose:n.onClose,onConnect:n.onConnect,onData:n.onData,onInsert:n.onInsert,onSelect:n.onSelect,onGenerate:n.onGenerate,currentContext:n.currentContext,hasPro:n.hasPro},l.default.createElement(c.default,{attachments:n.attachments}))))};S.propTypes={mode:u.default.oneOf(w.LAYOUT_APP_MODES).isRequired,attachmentsTypes:p.AttachmentsTypesPropType,attachments:u.default.arrayOf(p.AttachmentPropType),onClose:u.default.func.isRequired,onConnect:u.default.func.isRequired,onData:u.default.func.isRequired,onInsert:u.default.func.isRequired,onSelect:u.default.func.isRequired,onGenerate:u.default.func.isRequired,currentContext:u.default.object,hasPro:u.default.bool};var O=S;o.default=O},26457:(n,o,a)=>{"use strict";var i=a(73203),l=a(7501);Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var u=function _interopRequireWildcard(n,o){if(!o&&n&&n.__esModule)return n;if(null===n||"object"!==l(n)&&"function"!=typeof n)return{default:n};var a=_getRequireWildcardCache(o);if(a&&a.has(n))return a.get(n);var i={},u=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var c in n)if("default"!==c&&Object.prototype.hasOwnProperty.call(n,c)){var p=u?Object.getOwnPropertyDescriptor(n,c):null;p&&(p.get||p.set)?Object.defineProperty(i,c,p):i[c]=n[c]}i.default=n,a&&a.set(n,i);return i}(a(87363)),c=i(a(40131)),p=i(a(65345)),w=i(a(16068)),C=i(a(96587)),x=i(a(71871)),S=i(a(46369)),O=i(a(5647)),j=i(a(30313)),k=i(a(42493)),R=i(a(23615)),I=a(77292),W=a(25455),N=a(35879);function _getRequireWildcardCache(n){if("function"!=typeof WeakMap)return null;var o=new WeakMap,a=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(n){return n?a:o})(n)}var $=function LayoutContent(n){var o=(0,O.default)(),a=o.isLoading,i=o.isConnected,l=o.isGetStarted,R=o.connectUrl,I=o.fetchData,$=o.hasSubscription,G=o.usagePercentage,J=(0,W.useConfig)(),re=J.onClose,oe=J.onConnect,ie=(0,N.useRequestIds)(),le=ie.updateUsagePercentage,ue=ie.usagePercentage,ce=(0,u.useState)(!1),se=(0,c.default)(ce,2),de=se[0],he=se[1];if((0,u.useEffect)((function(){de||a||!G&&0!==G||(le(G),he(!0))}),[a,G,de,le]),a||!de)return u.default.createElement(k.default,{onClose:re},u.default.createElement(k.default.Header,{onClose:re}),u.default.createElement(k.default.Content,{dividers:!0},u.default.createElement(x.default,{BoxProps:{sx:{px:3}}})));if(!i)return u.default.createElement(j.default,{onClose:re},u.default.createElement(k.default,{onClose:re}),u.default.createElement(j.default.Content,{dividers:!0},u.default.createElement(p.default,{connectUrl:R,onSuccess:function onSuccess(n){oe(n),I()}})));if(!l)return u.default.createElement(j.default,{onClose:re},u.default.createElement(k.default,{onClose:re}),u.default.createElement(j.default.Content,{dividers:!0},u.default.createElement(C.default,{onSuccess:I})));var ge=!$||80<=ue;return u.default.createElement(w.default,{attachments:n.attachments,DialogHeaderProps:{children:ge&&u.default.createElement(S.default,{hasSubscription:$,usagePercentage:ue})}})};$.propTypes={attachments:R.default.arrayOf(I.AttachmentPropType)};var G=$;o.default=G},65345:(n,o,a)=>{"use strict";var i=a(73203),l=a(7501);Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var u=function _interopRequireWildcard(n,o){if(!o&&n&&n.__esModule)return n;if(null===n||"object"!==l(n)&&"function"!=typeof n)return{default:n};var a=_getRequireWildcardCache(o);if(a&&a.has(n))return a.get(n);var i={},u=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var c in n)if("default"!==c&&Object.prototype.hasOwnProperty.call(n,c)){var p=u?Object.getOwnPropertyDescriptor(n,c):null;p&&(p.get||p.set)?Object.defineProperty(i,c,p):i[c]=n[c]}i.default=n,a&&a.set(n,i);return i}(a(87363)),c=a(36626),p=a(38003),w=i(a(23615)),C=a(34029);function _getRequireWildcardCache(n){if("function"!=typeof WeakMap)return null;var o=new WeakMap,a=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(n){return n?a:o})(n)}var x=function Connect(n){var o=n.connectUrl,a=n.onSuccess,i=(0,u.useRef)();return(0,u.useEffect)((function(){jQuery.fn.elementorConnect&&jQuery(i.current).elementorConnect({success:function success(n,o){return a(o)},error:function error(){throw new Error("Elementor AI: Failed to connect.")}})}),[]),u.default.createElement(c.Stack,{alignItems:"center",gap:2},u.default.createElement(C.AIIcon,{sx:{color:"text.primary",fontSize:"60px",mb:1}}),u.default.createElement(c.Typography,{variant:"h4",sx:{color:"text.primary"}},(0,p.__)("Step into the future with Elementor AI","elementor")),u.default.createElement(c.Typography,{variant:"body2"},(0,p.__)("Create smarter with AI text and code generators built right into the editor.","elementor")),u.default.createElement(c.Typography,{variant:"caption",sx:{maxWidth:520,textAlign:"center"}},(0,p.__)('By clicking "Connect", I approve the ',"elementor"),u.default.createElement(c.Link,{href:"https://go.elementor.com/ai-terms/",target:"_blank",color:"info.main"},(0,p.__)("Terms of Service","elementor"))," & ",u.default.createElement(c.Link,{href:"https://go.elementor.com/ai-privacy-policy/",target:"_blank",color:"info.main"},(0,p.__)("Privacy Policy","elementor")),(0,p.__)(" of the Elementor AI service.","elementor")),u.default.createElement(c.Button,{ref:i,href:o,variant:"contained",sx:{mt:1,"&:hover":{color:"primary.contrastText"}}},(0,p.__)("Connect","elementor")))};x.propTypes={connectUrl:w.default.string.isRequired,onSuccess:w.default.func.isRequired};var S=x;o.default=S},28378:(n,o,a)=>{"use strict";var i=a(73203);Object.defineProperty(o,"__esModule",{value:!0}),o.default=o.USER_VARIATION_SOURCE=o.USER_URL_SOURCE=o.MENU_TYPE_LIBRARY=o.ELEMENTOR_LIBRARY_SOURCE=o.ATTACHMENT_TYPE_URL=o.ATTACHMENT_TYPE_JSON=void 0;var l=i(a(87363)),u=i(a(73119)),c=a(46567),p=i(a(865)),w=i(a(40097)),C=i(a(54572)),x=i(a(68873)),S=a(38003),O=i(a(23615)),j=a(77292),k=a(36626),R="json";o.ATTACHMENT_TYPE_JSON=R;var I="url";o.ATTACHMENT_TYPE_URL=I;var W="library";o.MENU_TYPE_LIBRARY=W;o.USER_VARIATION_SOURCE="user-variation";o.ELEMENTOR_LIBRARY_SOURCE="elementor-library";o.USER_URL_SOURCE="user-url";var N=function Attachments(n){return n.attachments.length?l.default.createElement(k.Stack,{direction:"row",spacing:1},n.attachments.map((function(o,a){switch(o.type){case R:return l.default.createElement(p.default,(0,u.default)({key:a},n));case I:return l.default.createElement(w.default,(0,u.default)({key:a},n));default:return null}}))):l.default.createElement(c.Menu,{disabled:n.disabled,onAttach:n.onAttach,items:[{title:(0,S.__)("Reference a website","elementor"),icon:C.default,type:I},{title:(0,S.__)("Create variations from Template Library","elementor"),icon:x.default,type:W}]})};N.propTypes={attachments:O.default.arrayOf(j.AttachmentPropType).isRequired,onAttach:O.default.func.isRequired,onDetach:O.default.func,disabled:O.default.bool};var $=N;o.default=$},31282:(n,o,a)=>{"use strict";var i=a(73203);Object.defineProperty(o,"__esModule",{value:!0}),o.default=o.AttachDialog=void 0;var l=i(a(87363)),u=a(51542),c=i(a(23615)),p=a(73202),w=a(28378),C=function AttachDialog(n){var o=n.type,a=n.url;switch(o){case w.ATTACHMENT_TYPE_URL:return l.default.createElement(u.UrlDialog,{url:a,onAttach:n.onAttach,onClose:n.onClose});case w.MENU_TYPE_LIBRARY:return l.default.createElement(p.LibraryDialog,{onAttach:n.onAttach,onClose:n.onClose})}return null};o.AttachDialog=C,C.propTypes={type:c.default.string,onAttach:c.default.func,onClose:c.default.func,url:c.default.string};var x=C;o.default=x},73202:(n,o,a)=>{"use strict";var i=a(73203);Object.defineProperty(o,"__esModule",{value:!0}),o.LibraryDialog=void 0;var l=i(a(23615)),u=a(87363),c=a(28378),p=function LibraryDialog(n){var o=(0,u.useRef)(!1);return(0,u.useEffect)((function(){var a=function onLibraryHide(){o.current||n.onClose()};return $e.components.get("library").layout.getModal().on("hide",a),function(){$e.components.get("library").layout.getModal().off("hide",a)}}),[n]),(0,u.useEffect)((function(){var a=function onMessage(a){var i=a.data,l=i.type,u=i.json,p=i.html,w=i.label,C=i.source;switch(l){case"library/attach:start":o.current=!0;break;case"library/attach":n.onAttach([{type:c.ATTACHMENT_TYPE_JSON,previewHTML:p,content:u,label:w,source:C}]),o.current=!1,n.onClose()}};return window.addEventListener("message",a),function(){window.removeEventListener("message",a)}})),$e.run("library/open",{toDefault:!0,mode:"ai-attachment"}),o.current=!1,null};o.LibraryDialog=p,p.propTypes={onAttach:l.default.func.isRequired,onClose:l.default.func.isRequired}},46567:(n,o,a)=>{"use strict";var i=a(73203),l=a(7501);Object.defineProperty(o,"__esModule",{value:!0}),o.Menu=void 0;var u=function _interopRequireWildcard(n,o){if(!o&&n&&n.__esModule)return n;if(null===n||"object"!==l(n)&&"function"!=typeof n)return{default:n};var a=_getRequireWildcardCache(o);if(a&&a.has(n))return a.get(n);var i={},u=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var c in n)if("default"!==c&&Object.prototype.hasOwnProperty.call(n,c)){var p=u?Object.getOwnPropertyDescriptor(n,c):null;p&&(p.get||p.set)?Object.defineProperty(i,c,p):i[c]=n[c]}i.default=n,a&&a.set(n,i);return i}(a(87363)),c=i(a(40131)),p=a(36626),w=i(a(941)),C=i(a(78170)),x=i(a(23615)),S=a(31282),O=i(a(24395));function _getRequireWildcardCache(n){if("function"!=typeof WeakMap)return null;var o=new WeakMap,a=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(n){return n?a:o})(n)}var j=function Menu(n){var o=(0,u.useState)(!1),a=(0,c.default)(o,2),i=a[0],l=a[1],x=(0,u.useState)(null),j=(0,c.default)(x,2),k=j[0],R=j[1],I=(0,p.useTheme)().direction,W=(0,u.useRef)(null),N=(0,O.default)("e-ai-attachment-badge"),$=N.isViewed,G=N.markAsViewed;return u.default.createElement(u.default.Fragment,null,u.default.createElement(p.IconButton,{size:"small",ref:W,disabled:n.disabled,onClick:function onClick(){l(!0),$||G()},color:"secondary"},i?u.default.createElement(w.default,{fontSize:"small"}):$?u.default.createElement(C.default,{fontSize:"small"}):u.default.createElement(p.Badge,{color:"primary",badgeContent:" ",variant:"dot"},u.default.createElement(C.default,{fontSize:"small"}))),u.default.createElement(p.Popover,{open:i,anchorEl:W.current,onClose:function onClose(){return l(!1)},anchorOrigin:{vertical:"bottom",horizontal:"rtl"===I?"right":"left"},transformOrigin:{vertical:"top",horizontal:"rtl"===I?"right":"left"}},u.default.createElement(p.Stack,{sx:{width:440}},n.items.map((function(n){var o=n.icon;return u.default.createElement(p.MenuItem,{key:n.type,onClick:function onClick(){R(n.type),l(!1)}},u.default.createElement(p.ListItemIcon,null,u.default.createElement(o,null)),n.title)})))),u.default.createElement(S.AttachDialog,{type:k,onAttach:n.onAttach,onClose:function onClose(){l(!1),R(null)}}))};o.Menu=j,j.propTypes={items:x.default.arrayOf(x.default.shape({title:x.default.string.isRequired,type:x.default.string.isRequired,icon:x.default.elementType})).isRequired,onAttach:x.default.func.isRequired,disabled:x.default.bool}},42335:(n,o,a)=>{"use strict";var i=a(73203);Object.defineProperty(o,"__esModule",{value:!0}),o.PromptPowerNotice=void 0;var l=i(a(87363)),u=a(36626),c=a(38003),p=i(a(24395));o.PromptPowerNotice=function PromptPowerNotice(){var n=(0,p.default)("e-ai-builder-attachments-power"),o=n.isViewed,a=n.markAsViewed;return o?null:l.default.createElement(u.Box,{sx:{pt:2,px:2,pb:0}},l.default.createElement(u.Alert,{severity:"info",onClose:function onClose(){return a()}},l.default.createElement(u.Typography,{variant:"body2",display:"inline-block",sx:{paddingInlineEnd:1}},(0,c.__)("You’ve got the power.","elementor")),l.default.createElement(u.Typography,{variant:"body2",display:"inline-block"},(0,c.__)("Craft your prompt to affect content, images and/or colors - whichever you decide.","elementor"))))}},865:(n,o,a)=>{"use strict";var i=a(73203);Object.defineProperty(o,"__esModule",{value:!0}),o.default=o.ThumbnailJson=void 0;var l=i(a(87363)),u=a(88482),c=i(a(23615)),p=a(36626),w=a(77292),C=function ThumbnailJson(n){var o,a=null===(o=n.attachments)||void 0===o?void 0:o.find((function(n){return"json"===n.type}));return a?a.previewHTML?l.default.createElement(u.Thumbnail,{html:a.previewHTML,disabled:n.disabled}):l.default.createElement(p.Skeleton,{animation:"wave",variant:"rounded",width:60,height:60}):null};o.ThumbnailJson=C,C.propTypes={attachments:c.default.arrayOf(w.AttachmentPropType).isRequired,disabled:c.default.bool};var x=C;o.default=x},40097:(n,o,a)=>{"use strict";var i=a(73203);Object.defineProperty(o,"__esModule",{value:!0}),o.default=o.ThumbnailUrl=void 0;var l=i(a(87363)),u=a(88482),c=a(38003),p=i(a(23615)),w=a(36626),C=a(34029),x=a(77292),S=function ThumbnailUrl(n){var o,a=null===(o=n.attachments)||void 0===o?void 0:o.find((function(n){return"url"===n.type}));return a?l.default.createElement(w.Box,{sx:{position:"relative","&:hover::before":{content:'""',position:"absolute",userSelect:"none",inset:0,backgroundColor:"rgba(0,0,0,0.6)",borderRadius:1,zIndex:1},"&:hover .remove-attachment":{display:"flex"}}},l.default.createElement(w.IconButton,{className:"remove-attachment",size:"small","aria-label":(0,c.__)("Remove","elementor"),disabled:n.disabled,onClick:function onClick(o){o.stopPropagation(),n.onDetach()},sx:{display:"none",position:"absolute",insetInlineEnd:4,insetBlockStart:4,backgroundColor:"secondary.main",zIndex:1,borderRadius:1,p:"3px","&:hover":{backgroundColor:"secondary.dark"}}},l.default.createElement(C.TrashIcon,{sx:{fontSize:"1.125rem",color:"common.white"}})),l.default.createElement(u.Thumbnail,{disabled:n.disabled,html:a.previewHTML})):null};o.ThumbnailUrl=S,S.propTypes={attachments:p.default.arrayOf(x.AttachmentPropType).isRequired,disabled:p.default.bool,onDetach:p.default.func};var O=S;o.default=O},88482:(n,o,a)=>{"use strict";var i=a(73203);Object.defineProperty(o,"__esModule",{value:!0}),o.Thumbnail=o.THUMBNAIL_SIZE=void 0;var l,u=i(a(87363)),c=i(a(79769)),p=a(36626),w=a(38003),C=i(a(23615)),x=i(a(63993)),S=64;o.THUMBNAIL_SIZE=S;var O=x.default.body(l||(l=(0,c.default)(["\n\thtml, body {\n\t\tmargin: 0;\n\t\tpadding: 0;\n\t\toverflow: hidden;\n\t}\n\n\tbody > * {\n\t\twidth: 100% !important;\n\t}\n\n\tbody > img {\n\t\theight: 100%;\n\t\tobject-fit: cover;\n\t}\n\n\tbody:has(> img) {\n\t\theight: ","px\n\t}\n"])),S),j=function Thumbnail(n){var o,a,i,l,c=null===(o=n.html.match('data-width="(?<width>\\d+)"'))||void 0===o||null===(a=o.groups)||void 0===a?void 0:a.width,C=null===(i=n.html.match('data-height="(?<height>\\d+)"'))||void 0===i||null===(l=i.groups)||void 0===l?void 0:l.height,x=c?parseInt(c):S,j=C?parseInt(C):S,k=Math.min(j,x),R=S/k,I=j>x?(S-S*(j/x))/2:0,W=x>j?(S-S*(x/j))/2:0;return u.default.createElement(p.Box,{dir:"ltr",sx:{position:"relative",cursor:"default",overflow:"hidden",border:"1px solid",borderColor:"grey.300",borderRadius:1,boxSizing:"border-box",width:S,height:S,opacity:n.disabled?.5:1}},u.default.createElement("iframe",{title:(0,w.__)("Preview","elementor"),sandbox:"",srcDoc:"<style>"+O.componentStyle.rules.join("")+"</style>"+n.html,style:{border:"none",overflow:"hidden",width:x,height:j,transform:"scale(".concat(R,")"),transformOrigin:"".concat(W,"px ").concat(I,"px")}}))};o.Thumbnail=j,j.propTypes={html:C.default.string.isRequired,disabled:C.default.bool}},51542:(n,o,a)=>{"use strict";var i=a(73203),l=a(7501);Object.defineProperty(o,"__esModule",{value:!0}),o.UrlDialog=void 0;var u=function _interopRequireWildcard(n,o){if(!o&&n&&n.__esModule)return n;if(null===n||"object"!==l(n)&&"function"!=typeof n)return{default:n};var a=_getRequireWildcardCache(o);if(a&&a.has(n))return a.get(n);var i={},u=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var c in n)if("default"!==c&&Object.prototype.hasOwnProperty.call(n,c)){var p=u?Object.getOwnPropertyDescriptor(n,c):null;p&&(p.get||p.set)?Object.defineProperty(i,c,p):i[c]=n[c]}i.default=n,a&&a.set(n,i);return i}(a(87363)),c=i(a(40131)),p=a(36626),w=i(a(23615)),C=a(38003),x=a(14638),S=a(95748),O=a(74859),j=a(28378),k=a(84157),R=i(a(5647)),I=a(35879);function _getRequireWildcardCache(n){if("function"!=typeof WeakMap)return null;var o=new WeakMap,a=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(n){return n?a:o})(n)}var W=function UrlDialog(n){var o=(0,u.useRef)(null),a=(0,x.useAttachUrlService)({targetUrl:n.url}).iframeSource,i=a?new URL(a).origin:"",l=(0,O.useTimeout)(1e4),w=(0,c.default)(l,2),W=w[0],N=w[1],$=(0,R.default)(),G=$.isConnected,J=$.hasSubscription,re=$.credits,oe=$.isLoading,ie=$.usagePercentage,le=(0,k.useRemoteConfig)(),ue=le.isLoaded,ce=le.isError,se=le.remoteConfig,de=(0,I.useRequestIds)(),he=de.updateUsagePercentage,ge=de.usagePercentage,ye=(0,u.useState)(!1),ve=(0,c.default)(ye,2),Ce=ve[0],Pe=ve[1];return(0,u.useEffect)((function(){Ce||oe||!ie&&0!==ie||(he(ie),Pe(!0))}),[oe,ie,Ce,he]),(0,u.useEffect)((function(){var o=function onMessage(o){if(o.origin===i){var a=o.data,l=a.type,u=a.html,c=a.url;switch(l){case"element-selector/close":n.onClose();break;case"element-selector/loaded":N();break;case"element-selector/attach":n.onAttach([{type:"url",previewHTML:u,content:u,label:c?new URL(c).host:"",source:j.USER_URL_SOURCE}])}}};return window.addEventListener("message",o),function(){window.removeEventListener("message",o)}}),[i,a,n,N]),a?!ue||ce?null:u.default.createElement(p.Dialog,{open:!0,fullScreen:!0,hideBackdrop:!0,maxWidth:"md",sx:{"& .MuiPaper-root":{backgroundColor:"transparent"}}},u.default.createElement(p.DialogContent,{sx:{padding:0}},W&&u.default.createElement(S.AlertDialog,{message:(0,C.__)("The app is not responding. Please try again later.","elementor"),onClose:n.onClose}),!W&&u.default.createElement("iframe",{ref:o,title:(0,C.__)("URL as a reference","elementor"),src:a,onLoad:function onLoad(){var n=window.elementorAppConfig["kit-library"],a=n.access_level,l=n.access_tier,u=n.is_pro;o.current.contentWindow.postMessage({type:"referrer/info",info:{page:{url:window.location.href},authToken:se[k.CONFIG_KEYS.AUTH_TOKEN]||"",products:{core:{version:window.elementor.config.version},pro:{isPro:u,accessLevel:a,accessTier:l},ai:{isConnected:G,hasSubscription:J,credits:re,usagePercentage:ge}},user:{isAdmin:window.elementor.config.user.is_administrator}}},i)},style:{border:"none",overflow:"scroll",width:"100%",height:"100%",backgroundColor:"rgba(255,255,255,0.6)"}}))):u.default.createElement(S.AlertDialog,{message:(0,C.__)("The app is not available. Please try again later.","elementor"),onClose:n.onClose})};o.UrlDialog=W,W.propTypes={onAttach:w.default.func.isRequired,onClose:w.default.func.isRequired,url:w.default.string}},42493:(n,o,a)=>{"use strict";var i=a(73203),l=a(7501);Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var u=function _interopRequireWildcard(n,o){if(!o&&n&&n.__esModule)return n;if(null===n||"object"!==l(n)&&"function"!=typeof n)return{default:n};var a=_getRequireWildcardCache(o);if(a&&a.has(n))return a.get(n);var i={},u=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var c in n)if("default"!==c&&Object.prototype.hasOwnProperty.call(n,c)){var p=u?Object.getOwnPropertyDescriptor(n,c):null;p&&(p.get||p.set)?Object.defineProperty(i,c,p):i[c]=n[c]}i.default=n,a&&a.set(n,i);return i}(a(87363)),c=i(a(73119)),p=i(a(93231)),w=i(a(40131)),C=i(a(70966)),x=a(36626),S=a(38003),O=i(a(23615)),j=i(a(59441)),k=a(34029),R=["sx","PaperProps"];function _getRequireWildcardCache(n){if("function"!=typeof WeakMap)return null;var o=new WeakMap,a=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(n){return n?a:o})(n)}function ownKeys(n,o){var a=Object.keys(n);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(n);o&&(i=i.filter((function(o){return Object.getOwnPropertyDescriptor(n,o).enumerable}))),a.push.apply(a,i)}return a}function _objectSpread(n){for(var o=1;o<arguments.length;o++){var a=null!=arguments[o]?arguments[o]:{};o%2?ownKeys(Object(a),!0).forEach((function(o){(0,p.default)(n,o,a[o])})):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(a)):ownKeys(Object(a)).forEach((function(o){Object.defineProperty(n,o,Object.getOwnPropertyDescriptor(a,o))}))}return n}var I=(0,x.styled)(j.default)((function(){return{"& .MuiDialog-container":{marginTop:0,alignItems:"flex-end",paddingBottom:"16vh"},"& .MuiPaper-root":{margin:0,maxHeight:"55vh"}}})),W=function DialogHeader(n){var o=n.onClose,a=n.children;return u.default.createElement(x.AppBar,{sx:{fontWeight:"normal"},color:"transparent",position:"relative"},u.default.createElement(x.Toolbar,{variant:"dense"},u.default.createElement(k.AIIcon,{sx:{mr:1}}),u.default.createElement(x.Typography,{component:"span",variant:"subtitle2",sx:{fontWeight:"bold",textTransform:"uppercase"}},(0,S.__)("AI","elementor")),u.default.createElement(x.Chip,{label:(0,S.__)("Beta","elementor"),color:"default",size:"small",sx:{ml:1}}),u.default.createElement(x.Stack,{direction:"row",spacing:1,alignItems:"center",sx:{ml:"auto"}},a,u.default.createElement(x.IconButton,{size:"small","aria-label":"close",onClick:o,sx:{"&.MuiButtonBase-root":{mr:-1}}},u.default.createElement(k.XIcon,null)))))};W.propTypes={children:O.default.node,onClose:O.default.func.isRequired};var N=(0,x.styled)(j.default.Content)((function(){return{"&.MuiDialogContent-root":{padding:0}}})),$=function LayoutDialog(n){var o=n.sx,a=void 0===o?{}:o,i=n.PaperProps,l=void 0===i?{}:i,p=(0,C.default)(n,R),x=(0,u.useState)({pointerEvents:"none"}),S=(0,w.default)(x,2),O=S[0],j=S[1],k=(0,u.useRef)(null);return u.default.createElement(I,(0,c.default)({maxWidth:"md",PaperProps:_objectSpread({sx:{pointerEvents:"auto"},onMouseEnter:function onMouseEnter(){clearTimeout(k.current),j({pointerEvents:"all"})},onMouseLeave:function onMouseLeave(){clearTimeout(k.current),k.current=setTimeout((function(){j({pointerEvents:"none"})}),200)}},l)},p,{sx:_objectSpread(_objectSpread({},O),a)}))};$.propTypes={sx:O.default.object,PaperProps:O.default.object},$.Header=W,$.Content=N;var G=$;o.default=G},71855:(n,o,a)=>{"use strict";var i=a(73203),l=a(7501);Object.defineProperty(o,"__esModule",{value:!0}),o.ProTemplateIndicator=void 0;var u=function _interopRequireWildcard(n,o){if(!o&&n&&n.__esModule)return n;if(null===n||"object"!==l(n)&&"function"!=typeof n)return{default:n};var a=_getRequireWildcardCache(o);if(a&&a.has(n))return a.get(n);var i={},u=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var c in n)if("default"!==c&&Object.prototype.hasOwnProperty.call(n,c)){var p=u?Object.getOwnPropertyDescriptor(n,c):null;p&&(p.get||p.set)?Object.defineProperty(i,c,p):i[c]=n[c]}i.default=n,a&&a.set(n,i);return i}(a(87363)),c=i(a(40131)),p=a(38003),w=a(36626),C=i(a(30344));function _getRequireWildcardCache(n){if("function"!=typeof WeakMap)return null;var o=new WeakMap,a=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(n){return n?a:o})(n)}var x=(0,w.styled)(w.Paper)((function(n){var o=n.theme;return{position:"relative",padding:o.spacing(3),boxShadow:o.shadows[4],zIndex:"9999"}})),S=(0,w.styled)(w.Box)((function(n){var o=n.theme;return{position:"absolute",width:o.spacing(5),height:o.spacing(5),overflow:"hidden",left:"100% !important",transform:"translateX(-50%) translateY(-50%) rotate(var(--rotate, 0deg)) !important","&::after":{backgroundColor:o.palette.background.paper,content:'""',display:"block",position:"absolute",width:o.spacing(2.5),height:o.spacing(2.5),top:"50%",left:"50%",transform:"translateX(-50%) translateY(-50%) rotate(45deg)",boxShadow:"5px -5px 5px 0px rgba(0, 0, 0, 0.2)",backgroundImage:"linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))"}}}));o.ProTemplateIndicator=function ProTemplateIndicator(){var n=(0,p.__)("Go Pro","elementor"),o=(0,u.useState)(!1),a=(0,c.default)(o,2),i=a[0],l=a[1],O=(0,u.useRef)(null),j=(0,u.useRef)(null);return u.default.createElement(w.Box,{flexDirection:"row-reverse",component:"span",display:"flex",onMouseLeave:function hidePopover(){return l(!1)},alignItems:"center"},u.default.createElement(w.IconButton,{ref:O,onMouseEnter:function showPopover(){return l(!0)},onClick:function onClick(n){return n.stopPropagation()},"aria-owns":i?"e-pro-upgrade-popover":void 0,"aria-haspopup":"true",sx:{m:1,"&:hover":{backgroundColor:"action.selected"}}},u.default.createElement(C.default,{sx:{color:"text.primary"}})),u.default.createElement(w.Popper,{open:i,popperOptions:{placement:"left-start",modifiers:[{name:"arrow",enabled:!0,options:{element:j.current,padding:5}},{name:"offset",options:{offset:[0,10]}}]},anchorEl:O.current,sx:{zIndex:"9999",maxWidth:300}},u.default.createElement(x,null,u.default.createElement(S,{ref:j}),u.default.createElement(w.Stack,{alignItems:"start",spacing:2},u.default.createElement(w.Chip,{color:"promotion",variant:"outlined",size:"small",label:(0,p.__)("Pro","elementor"),icon:u.default.createElement(C.default,null)}),u.default.createElement(w.Typography,{variant:"body2"},(0,p.__)("This result includes an Elementor Pro widget that's not available with your current plan. Upgrade to use all the widgets in this result.","elementor")),u.default.createElement(w.Button,{variant:"contained",color:"promotion",size:"small",href:"https://go.elementor.com/go-pro-ai/",target:"_blank",sx:{alignSelf:"flex-end"}},n)))))}},40271:(n,o,a)=>{"use strict";var i=a(73203),l=a(7501);Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var u=function _interopRequireWildcard(n,o){if(!o&&n&&n.__esModule)return n;if(null===n||"object"!==l(n)&&"function"!=typeof n)return{default:n};var a=_getRequireWildcardCache(o);if(a&&a.has(n))return a.get(n);var i={},u=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var c in n)if("default"!==c&&Object.prototype.hasOwnProperty.call(n,c)){var p=u?Object.getOwnPropertyDescriptor(n,c):null;p&&(p.get||p.set)?Object.defineProperty(i,c,p):i[c]=n[c]}i.default=n,a&&a.set(n,i);return i}(a(87363)),c=i(a(40131)),p=i(a(70966)),w=i(a(73119)),C=i(a(93231)),x=a(36626),S=i(a(23615)),O=a(38003),j=i(a(84515)),k=a(25455),R=["onSubmit"];function _getRequireWildcardCache(n){if("function"!=typeof WeakMap)return null;var o=new WeakMap,a=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(n){return n?a:o})(n)}function ownKeys(n,o){var a=Object.keys(n);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(n);o&&(i=i.filter((function(o){return Object.getOwnPropertyDescriptor(n,o).enumerable}))),a.push.apply(a,i)}return a}function _objectSpread(n){for(var o=1;o<arguments.length;o++){var a=null!=arguments[o]?arguments[o]:{};o%2?ownKeys(Object(a),!0).forEach((function(o){(0,C.default)(n,o,a[o])})):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(a)):ownKeys(Object(a)).forEach((function(o){Object.defineProperty(n,o,Object.getOwnPropertyDescriptor(a,o))}))}return n}var I=(0,u.forwardRef)((function(n,o){return u.default.createElement(x.TextField,(0,w.default)({autoFocus:!0,multiline:!0,size:"small",maxRows:3,color:"secondary",variant:"standard"},n,{inputRef:o,InputProps:_objectSpread(_objectSpread({},n.InputProps),{},{type:"search",sx:{pt:0}})}))}));I.propTypes={InputProps:S.default.object};var W=function PaperComponent(n){var o=(0,k.useConfig)().mode,a=k.MODE_VARIATION===o?"https://go.elementor.com/ai-prompt-library-variations/":"https://go.elementor.com/ai-prompt-library-containers/";return u.default.createElement(x.Paper,(0,w.default)({},n,{elevation:8,sx:{borderRadius:2}}),u.default.createElement(x.Typography,{component:x.Box,color:function color(n){return n.palette.text.tertiary},variant:"caption",paddingX:2,paddingY:1},(0,O.__)("Suggested Prompts","elementor")),u.default.createElement(x.Divider,null),n.children,u.default.createElement(x.Stack,{sx:{m:2}},u.default.createElement(j.default,{libraryLink:a})))};W.propTypes={children:S.default.node};var N=function PromptAutocomplete(n){var o=n.onSubmit,a=(0,p.default)(n,R),i=(0,u.useState)(!1),l=(0,c.default)(i,2),C=l[0],S=l[1],O=(0,x.useTheme)(),j=parseInt(O.spacing(4));return u.default.createElement(x.Autocomplete,(0,w.default)({PaperComponent:W,ListboxProps:{sx:{maxHeight:5*j}},renderOption:function renderOption(n,o){return u.default.createElement(x.Typography,(0,w.default)({},n,{title:o.text,noWrap:!0,variant:"body2",component:x.Box,sx:{"&.MuiAutocomplete-option":{display:"block",minHeight:j}}}),o.text)},freeSolo:!0,fullWidth:!0,disableClearable:!0,open:C,onClose:function onClose(n){var o;return S("A"===(null===(o=n.relatedTarget)||void 0===o?void 0:o.tagName))},onKeyDown:function onKeyDown(n){"Enter"!==n.key||n.shiftKey||C?"/"===n.key&&""===n.target.value&&(n.preventDefault(),S(!0)):o(n)}},a))};N.propTypes={onSubmit:S.default.func.isRequired},N.TextInput=I;var $=N;o.default=$},47432:(n,o,a)=>{"use strict";var i=a(73203),l=a(7501);Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var u=function _interopRequireWildcard(n,o){if(!o&&n&&n.__esModule)return n;if(null===n||"object"!==l(n)&&"function"!=typeof n)return{default:n};var a=_getRequireWildcardCache(o);if(a&&a.has(n))return a.get(n);var i={},u=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var c in n)if("default"!==c&&Object.prototype.hasOwnProperty.call(n,c)){var p=u?Object.getOwnPropertyDescriptor(n,c):null;p&&(p.get||p.set)?Object.defineProperty(i,c,p):i[c]=n[c]}i.default=n,a&&a.set(n,i);return i}(a(87363)),c=i(a(40131)),p=i(a(73119)),w=i(a(70966)),C=a(36626),x=a(38003),S=i(a(23615)),O=i(a(40271)),j=i(a(89216)),k=i(a(78025)),R=i(a(62682)),I=i(a(33375)),W=i(a(8817)),N=i(a(28378)),$=a(25455),G=a(77292),J=["tooltip"];function _getRequireWildcardCache(n){if("function"!=typeof WeakMap)return null;var o=new WeakMap,a=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(n){return n?a:o})(n)}var re=Object.freeze([{text:(0,x.__)("Hero section on [topic] with heading, text, buttons on the right, and an image on the left","elementor.com")},{text:(0,x.__)("About Us section on [topic] with heading, text, and big image below","elementor.com")},{text:(0,x.__)("Team section with four image boxes showcasing team members","elementor.com")},{text:(0,x.__)("FAQ section with a toggle widget showcasing FAQs about [topic]","elementor.com")},{text:(0,x.__)("Gallery section with a carousel displaying three images at once","elementor.com")},{text:(0,x.__)("Contact section with a form for [topic]","elementor.com")},{text:(0,x.__)("Client section featuring companies' logos","elementor.com")},{text:(0,x.__)("Testimonial section with testimonials, each featuring a star rating and an image","elementor.com")},{text:(0,x.__)("Service section about [topic], showcasing four services with buttons","elementor.com")},{text:(0,x.__)("Stats section with counters displaying data about [topic]","elementor.com")},{text:(0,x.__)("Quote section with colored background, featuring a centered quote","elementor.com")},{text:(0,x.__)("Pricing section for [topic] with a pricing list","elementor.com")},{text:(0,x.__)("Subscribe section featuring a simple email form, inviting users to stay informed on [topic]","elementor.com")}]),oe=function IconButtonWithTooltip(n){var o=n.tooltip,a=(0,w.default)(n,J);return u.default.createElement(C.Tooltip,{title:o},u.default.createElement(C.Box,{component:"span",sx:{cursor:a.disabled?"default":"pointer"}},u.default.createElement(C.IconButton,a)))};oe.propTypes={tooltip:S.default.string,disabled:S.default.bool};var ie=function BackButton(n){return u.default.createElement(oe,(0,p.default)({size:"small",color:"secondary",tooltip:(0,x.__)("Back to results","elementor")},n),u.default.createElement(R.default,null))},le=function EditButton(n){return u.default.createElement(oe,(0,p.default)({size:"small",color:"primary",tooltip:(0,x.__)("Edit prompt","elementor")},n),u.default.createElement(I.default,null))},ue=function GenerateButton(n){return u.default.createElement(k.default,(0,p.default)({size:"small",fullWidth:!1},n),(0,x.__)("Generate","elementor"))},ce=(0,u.forwardRef)((function(n,o){var a,i=n.attachments,l=n.isActive,w=n.isLoading,S=n.showActions,k=void 0!==S&&S,R=n.onAttach,I=n.onDetach,G=n.onSubmit,J=n.onBack,oe=n.onEdit,ce=n.shouldResetPrompt,se=void 0!==ce&&ce,de=(0,u.useState)(""),he=(0,c.default)(de,2),ge=he[0],ye=he[1];(0,u.useEffect)((function(){se&&ye("")}),[se]);var ve=(0,W.default)(ge,"layout"),Ce=ve.isEnhancing,Pe=ve.enhance,Se=(0,u.useRef)(""),Re=(0,$.useConfig)().attachmentsTypes,Ie=w||Ce||!l,Me=""===ge&&!i.length,De=Ie||Me,He=Re[(null===(a=i[0])||void 0===a?void 0:a.type)||""],Ue=(null==He?void 0:He.promptSuggestions)||re,Fe=(null==He?void 0:He.promptPlaceholder)||(0,x.__)("Press '/' for suggested prompts or describe the layout you want to create","elementor");return u.default.createElement(C.Stack,{component:"form",onSubmit:function onSubmit(n){return G(n,ge)},direction:"row",sx:{p:3},alignItems:"start",gap:1},u.default.createElement(C.Stack,{direction:"row",alignItems:"start",flexGrow:1,spacing:2},k&&(l?u.default.createElement(ie,{disabled:w||Ce,onClick:function handleBack(){ye(Se.current),J()}}):u.default.createElement(le,{disabled:w,onClick:function handleEdit(){Se.current=ge,oe()}})),u.default.createElement(N.default,{attachments:i,onAttach:R,onDetach:I,disabled:Ie}),u.default.createElement(O.default,{value:ge,disabled:Ie,onSubmit:function onSubmit(n){return G(n,ge)},options:Ue,onChange:function onChange(n,o){return ye(o.text+" ")},renderInput:function renderInput(n){return u.default.createElement(O.default.TextInput,(0,p.default)({},n,{ref:o,onChange:function onChange(n){return ye(n.target.value)},placeholder:Fe}))}})),u.default.createElement(j.default,{size:"small",disabled:De||""===ge,isLoading:Ce,onClick:function onClick(){return Pe().then((function(n){var o=n.result;return ye(o)}))}}),u.default.createElement(ue,{disabled:De}))}));ce.propTypes={isActive:S.default.bool,onAttach:S.default.func,onDetach:S.default.func,isLoading:S.default.bool,showActions:S.default.bool,onSubmit:S.default.func.isRequired,onBack:S.default.func.isRequired,onEdit:S.default.func.isRequired,attachments:S.default.arrayOf(G.AttachmentPropType),shouldResetPrompt:S.default.bool};var se=ce;o.default=se},88976:(n,o,a)=>{"use strict";Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var i=a(36626),l=(0,i.styled)(i.Box,{shouldForwardProp:function shouldForwardProp(n){return"outlineOffset"!==n}})((function(n){var o=n.theme,a=n.selected,i=n.height,l=n.disabled,u=n.outlineOffset,c=void 0===u?"0px":u,p=a?o.palette.text.primary:o.palette.text.disabled,w="2px solid ".concat(p);return{height:i,cursor:l?"default":"pointer",overflow:"hidden",boxSizing:"border-box",backgroundPosition:"top center",backgroundSize:"100% auto",backgroundRepeat:"no-repeat",backgroundColor:o.palette.common.white,borderRadius:.5*o.shape.borderRadius,outlineOffset:c,outline:w,opacity:l?"0.4":"1",transition:"all 50ms linear","&:hover":l?{}:{outlineColor:o.palette.text.primary}}}));o.default=l},78305:(n,o,a)=>{"use strict";var i=a(73203);Object.defineProperty(o,"__esModule",{value:!0}),o.default=ScreenshotUnavailable;var l=i(a(87363)),u=i(a(73119)),c=i(a(93231)),p=i(a(23615)),w=a(38003),C=i(a(88976));function ownKeys(n,o){var a=Object.keys(n);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(n);o&&(i=i.filter((function(o){return Object.getOwnPropertyDescriptor(n,o).enumerable}))),a.push.apply(a,i)}return a}function _objectSpread(n){for(var o=1;o<arguments.length;o++){var a=null!=arguments[o]?arguments[o]:{};o%2?ownKeys(Object(a),!0).forEach((function(o){(0,c.default)(n,o,a[o])})):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(a)):ownKeys(Object(a)).forEach((function(o){Object.defineProperty(n,o,Object.getOwnPropertyDescriptor(a,o))}))}return n}function ScreenshotUnavailable(n){return l.default.createElement(C.default,(0,u.default)({},n,{sx:_objectSpread(_objectSpread({},n.sx||{}),{},{display:"flex",alignItems:"center",justifyContent:"center",backgroundColor:"background.paper",color:"text.tertiary",fontStyle:"italic",fontSize:"12px",paddingInline:12,textAlign:"center",lineHeight:1.5})}),(0,w.__)("Preview unavailable","elementor"))}ScreenshotUnavailable.propTypes={sx:p.default.object}},90031:(n,o,a)=>{"use strict";var i=a(73203);Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var l=i(a(87363)),u=i(a(93231)),c=a(36626),p=i(a(23615)),w=i(a(88976)),C=i(a(78305)),x=i(a(10987));function ownKeys(n,o){var a=Object.keys(n);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(n);o&&(i=i.filter((function(o){return Object.getOwnPropertyDescriptor(n,o).enumerable}))),a.push.apply(a,i)}return a}function _objectSpread(n){for(var o=1;o<arguments.length;o++){var a=null!=arguments[o]?arguments[o]:{};o%2?ownKeys(Object(a),!0).forEach((function(o){(0,u.default)(n,o,a[o])})):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(a)):ownKeys(Object(a)).forEach((function(o){Object.defineProperty(n,o,Object.getOwnPropertyDescriptor(a,o))}))}return n}var S="138px",O=function Screenshot(n){var o=n.url,a=n.type,i=n.isLoading,u=void 0!==i&&i,p=n.isSelected,O=void 0!==p&&p,j=n.isPlaceholder,k=n.disabled,R=n.onClick,I=n.sx,W=void 0===I?{}:I,N=n.outlineOffset;return j?l.default.createElement(c.Box,{sx:_objectSpread({height:S},W)}):u?l.default.createElement(c.Skeleton,{width:"100%",animation:"wave",variant:"rounded",height:S,sx:W}):o?l.default.createElement(w.default,{selected:O,disabled:k,sx:_objectSpread({backgroundImage:"url('".concat(o,"')")},W),onClick:R,height:S,outlineOffset:N},l.default.createElement(x.default,{type:a})):l.default.createElement(C.default,{selected:O,disabled:k,sx:W,onClick:R,height:S,outlineOffset:N})};O.propTypes={isSelected:p.default.bool,isLoading:p.default.bool,isPlaceholder:p.default.bool,disabled:p.default.bool,onClick:p.default.func.isRequired,url:p.default.string,type:p.default.string,sx:p.default.object,outlineOffset:p.default.string};var j=O;o.default=j},10987:(n,o,a)=>{"use strict";var i=a(73203);Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var l=i(a(87363)),u=i(a(23615)),c=a(25455),p=a(71855),w=function TemplateBadge(n){var o=(0,c.useConfig)().hasPro;return"Pro"!==n.type||o?null:l.default.createElement(p.ProTemplateIndicator,null)},C=w;o.default=C,w.propTypes={type:u.default.string}},88387:(n,o,a)=>{"use strict";var i=a(73203);Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var l=i(a(87363)),u=i(a(73119)),c=i(a(70966)),p=a(36626),w=a(38003),C=i(a(23615)),x=["onClose","onCancel","title","text"],S=function UnsavedChangesAlert(n){var o=n.onClose,a=n.onCancel,i=n.title,C=n.text,S=(0,c.default)(n,x);return l.default.createElement(p.Dialog,(0,u.default)({"aria-labelledby":"unsaved-changes-alert-title","aria-describedby":"unsaved-changes-alert-description"},S),l.default.createElement(p.DialogTitle,{id:"unsaved-changes-alert-title"},i),l.default.createElement(p.DialogContent,null,l.default.createElement(p.DialogContentText,{id:"unsaved-changes-alert-description"},C)),l.default.createElement(p.DialogActions,null,l.default.createElement(p.Button,{onClick:a,color:"secondary"},(0,w.__)("Cancel","elementor")),l.default.createElement(p.Button,{onClick:o,color:"error",variant:"contained"},(0,w.__)("Yes, leave","elementor"))))};S.propTypes={title:C.default.string,text:C.default.string,onCancel:C.default.func,onClose:C.default.func};var O=S;o.default=O},25455:(n,o,a)=>{"use strict";var i=a(73203);Object.defineProperty(o,"__esModule",{value:!0}),o.useConfig=o.default=o.MODE_VARIATION=o.MODE_LAYOUT=o.LAYOUT_APP_MODES=o.ConfigProvider=void 0;var l=i(a(87363)),u=i(a(23615)),c="layout";o.MODE_LAYOUT=c;var p="variation";o.MODE_VARIATION=p;var w=[c,p];o.LAYOUT_APP_MODES=w;var C=l.default.createContext({});o.useConfig=function useConfig(){return l.default.useContext(C)};var x=function ConfigProvider(n){return l.default.createElement(C.Provider,{value:{mode:n.mode,attachmentsTypes:n.attachmentsTypes,onClose:n.onClose,onConnect:n.onConnect,onData:n.onData,onInsert:n.onInsert,onSelect:n.onSelect,onGenerate:n.onGenerate,currentContext:n.currentContext,hasPro:n.hasPro}},n.children)};o.ConfigProvider=x,x.propTypes={mode:u.default.oneOf(w).isRequired,children:u.default.node.isRequired,attachmentsTypes:u.default.object.isRequired,onClose:u.default.func.isRequired,onConnect:u.default.func.isRequired,onData:u.default.func.isRequired,onInsert:u.default.func.isRequired,onSelect:u.default.func.isRequired,onGenerate:u.default.func.isRequired,currentContext:u.default.object,hasPro:u.default.bool};var S=C;o.default=S},84157:(n,o,a)=>{"use strict";var i=a(73203),l=a(7501);Object.defineProperty(o,"__esModule",{value:!0}),o.useRemoteConfig=o.RemoteConfigProvider=o.CONFIG_KEYS=void 0;var u=i(a(50824)),c=i(a(10029)),p=i(a(40131)),w=function _interopRequireWildcard(n,o){if(!o&&n&&n.__esModule)return n;if(null===n||"object"!==l(n)&&"function"!=typeof n)return{default:n};var a=_getRequireWildcardCache(o);if(a&&a.has(n))return a.get(n);var i={},u=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var c in n)if("default"!==c&&Object.prototype.hasOwnProperty.call(n,c)){var p=u?Object.getOwnPropertyDescriptor(n,c):null;p&&(p.get||p.set)?Object.defineProperty(i,c,p):i[c]=n[c]}i.default=n,a&&a.set(n,i);return i}(a(87363)),C=i(a(23615)),x=a(46183);function _getRequireWildcardCache(n){if("function"!=typeof WeakMap)return null;var o=new WeakMap,a=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(n){return n?a:o})(n)}var S=w.default.createContext({});o.useRemoteConfig=function useRemoteConfig(){return w.default.useContext(S)};o.CONFIG_KEYS={WEB_BASED_BUILDER_URL:"webBasedBuilderUrl",AUTH_TOKEN:"jwt"};var O=function RemoteConfigProvider(n){var o=(0,w.useState)(!1),a=(0,p.default)(o,2),i=a[0],l=a[1],C=(0,w.useState)(!1),O=(0,p.default)(C,2),j=O[0],k=O[1],R=(0,w.useState)(!1),I=(0,p.default)(R,2),W=I[0],N=I[1],$=(0,w.useState)({}),G=(0,p.default)($,2),J=G[0],re=G[1],oe=function(){var n=(0,c.default)(u.default.mark((function _callee(){var n;return u.default.wrap((function _callee$(o){for(;;)switch(o.prev=o.next){case 0:return l(!0),o.prev=1,o.next=4,(0,x.getRemoteConfig)().finally((function(){k(!0),l(!1)}));case 4:if((n=o.sent).config){o.next=7;break}throw new Error("Invalid remote config");case 7:re(n.config),o.next=15;break;case 10:o.prev=10,o.t0=o.catch(1),N(!0),k(!0),l(!1);case 15:case"end":return o.stop()}}),_callee,null,[[1,10]])})));return function fetchData(){return n.apply(this,arguments)}}();return j||i||oe(),w.default.createElement(S.Provider,{value:{isLoading:i,isLoaded:j,isError:W,remoteConfig:J}},n.children)};o.RemoteConfigProvider=O,O.propTypes={children:C.default.node.isRequired,onError:C.default.func.isRequired}},14638:(n,o,a)=>{"use strict";var i=a(73203);Object.defineProperty(o,"__esModule",{value:!0}),o.useAttachUrlService=void 0;var l=i(a(40131)),u=a(36626),c=a(87363),p=a(84157);o.useAttachUrlService=function useAttachUrlService(n){var o,a,i=(0,c.useState)(n.targetUrl),w=(0,l.default)(i,2),C=w[0],x=w[1],S=(0,u.useTheme)(),O=(0,p.useRemoteConfig)(),j=O.isLoaded,k=O.isError,R=O.remoteConfig;if(!j||k||!R[p.CONFIG_KEYS.WEB_BASED_BUILDER_URL])return{iframeSource:"",currentUrl:C,setCurrentUrl:x};var I=new URL(R[p.CONFIG_KEYS.WEB_BASED_BUILDER_URL]);return I.searchParams.append("colorScheme",S.palette.mode),I.searchParams.append("isRTL","rtl"===S.direction?"true":"false"),I.searchParams.append("version",null===(o=window.elementorCommon)||void 0===o||null===(a=o.config)||void 0===a?void 0:a.version),C&&I.searchParams.append("url",C),{iframeSource:I.toString(),currentUrl:C,setCurrentUrl:x}}},88882:(n,o,a)=>{"use strict";var i=a(73203);Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var l=a(46183),u=i(a(60675)),c=function useLayoutPrompt(n,o){return(0,u.default)((function(o,a){return o.variationType=n,(0,l.generateLayout)(o,a)}),o)};o.default=c},15764:(n,o,a)=>{"use strict";var i=a(73203);Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var l=i(a(50824)),u=i(a(10029)),c=i(a(40131)),p=a(87363),w=i(a(88882)),C=function useScreenshot(n,o){var a=(0,p.useState)(""),i=(0,c.default)(a,2),C=i[0],x=i[1],S=(0,p.useState)(!1),O=(0,c.default)(S,2),j=O[0],k=O[1],R=(0,w.default)(n,null);return{generate:function generate(n,a){return k(!0),x(""),R.send(n,a).then(function(){var n=(0,u.default)(l.default.mark((function _callee(n){var a;return l.default.wrap((function _callee$(i){for(;;)switch(i.prev=i.next){case 0:return i.next=2,o(n.result);case 2:return(a=i.sent).sendUsageData=function(){return R.sendUsageData(n)},a.baseTemplateId=n.baseTemplateId,a.type=n.type,i.abrupt("return",a);case 7:case"end":return i.stop()}}),_callee)})));return function(o){return n.apply(this,arguments)}}()).catch((function(n){throw x(n.extra_data?n:n.message||n),n})).finally((function(){return k(!1)}))},error:C,isLoading:j}};o.default=C},12451:(n,o,a)=>{"use strict";var i=a(73203);Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var l=i(a(50824)),u=i(a(9833)),c=i(a(10029)),p=i(a(40131)),w=a(87363),C=i(a(15764)),x=a(25455),S=a(35879),O={isPending:!0},j=function useScreenshots(n){var o=n.onData,a=(0,w.useState)([]),i=(0,p.default)(a,2),j=i[0],k=i[1],R=(0,x.useConfig)().currentContext,I=(0,S.useRequestIds)(),W=I.editorSessionId,N=I.sessionId,$=I.setRequest,G=I.setBatch,J=I.setGenerate,re=(0,w.useRef)(""),oe=G(),ie=[(0,C.default)(0,o),(0,C.default)(1,o),(0,C.default)(2,o)],le=ie.length,ue=ie.every((function(n){return null==n?void 0:n.error}))?ie[0].error:"",ce=ie.some((function(n){return null==n?void 0:n.isLoading})),se=(0,w.useRef)(null),de=function(){var n=(0,c.default)(l.default.mark((function _callee(n,o){var a,i,c,p;return l.default.wrap((function _callee$(l){for(;;)switch(l.prev=l.next){case 0:return se.current=new AbortController,a=function onGenerate(n){return k((function(o){var a=(0,u.default)(o),i=a.indexOf(O);return a[i]=n,a})),!0},i=function onError(){return k((function(n){var o=(0,u.default)(n),a=o.lastIndexOf(O);return o[a]={isError:!0},o})),!1},c=ie.map((function(l){var u=l.generate,c=j.map((function(n){return n.baseTemplateId||""}));return u({prompt:n,prevGeneratedIds:c,currentContext:R,ids:{editorSessionId:W.current,sessionId:N.current,generateId:re.current,batchId:oe.current,requestId:$().current},attachments:o.map((function(n){return{type:n.type,content:n.content,label:n.label,source:n.source}}))},se.current.signal).then(a).catch(i)})),l.next=6,Promise.all(c);case 6:p=l.sent,p.every((function(n){return!1===n}))&&k((function(n){var o=(0,u.default)(n);return o.splice(-1*le),o}));case 9:case"end":return l.stop()}}),_callee)})));return function createScreenshots(o,a){return n.apply(this,arguments)}}();return{generate:function generate(n,o){var a=Array(le).fill(O);re.current=J().current,k(a),de(n,o)},regenerate:function regenerate(n,o){var a=Array(le).fill(O);k((function(n){return[].concat((0,u.default)(n),(0,u.default)(a))})),de(n,o)},screenshots:j,isLoading:ce,error:ue,abort:function abort(){var n;return null===(n=se.current)||void 0===n?void 0:n.abort()}}};o.default=j},24:(n,o,a)=>{"use strict";var i=a(73203);Object.defineProperty(o,"__esModule",{value:!0}),o.default=o.SCREENSHOTS_PER_PAGE=o.MAX_PAGES=void 0;var l=i(a(40131)),u=a(87363);o.SCREENSHOTS_PER_PAGE=3;o.MAX_PAGES=5;var c=function useSlider(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},o=n.slidesCount,a=void 0===o?0:o,i=n.slidesPerPage,c=void 0===i?3:i,p=n.gapPercentage,w=void 0===p?2:p,C=(0,u.useState)(1),x=(0,l.default)(C,2),S=x[0],O=x[1],j=(100-w*(c-1))/c,k=(j+w)*c*(S-1)*-1,R=Math.ceil(a/c);return(0,u.useEffect)((function(){S>1&&S>R&&O(R)}),[R]),{currentPage:S,setCurrentPage:O,pagesCount:R,slidesPerPage:c,gapPercentage:w,offsetXPercentage:k,slideWidthPercentage:j}};o.default=c},16068:(n,o,a)=>{"use strict";var i=a(73203),l=a(7501);Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var u=_interopRequireWildcard(a(87363)),c=i(a(9833)),p=i(a(70966)),w=i(a(40131)),C=i(a(73119)),x=i(a(23615)),S=a(38003),O=a(36626),j=i(a(86960)),k=i(a(88387)),R=i(a(42493)),I=i(a(47432)),W=i(a(28965)),N=i(a(90031)),$=i(a(12451)),G=_interopRequireWildcard(a(24)),J=i(a(7377)),re=i(a(49294)),oe=a(25455),ie=a(77292),le=a(42335),ue=a(28378),ce=i(a(31282)),se=i(a(50429)),de=a(90246),he=["children"];function _getRequireWildcardCache(n){if("function"!=typeof WeakMap)return null;var o=new WeakMap,a=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(n){return n?a:o})(n)}function _interopRequireWildcard(n,o){if(!o&&n&&n.__esModule)return n;if(null===n||"object"!==l(n)&&"function"!=typeof n)return{default:n};var a=_getRequireWildcardCache(o);if(a&&a.has(n))return a.get(n);var i={},u=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var c in n)if("default"!==c&&Object.prototype.hasOwnProperty.call(n,c)){var p=u?Object.getOwnPropertyDescriptor(n,c):null;p&&(p.get||p.set)?Object.defineProperty(i,c,p):i[c]=n[c]}return i.default=n,a&&a.set(n,i),i}var ge=(0,O.withDirection)(J.default),ye=(0,O.withDirection)(re.default),ve=function RegenerateButton(n){return u.default.createElement(O.Button,(0,C.default)({size:"small",color:"secondary",startIcon:u.default.createElement(W.default,null)},n),(0,S.__)("Regenerate","elementor"))},Ce=function UseLayoutButton(n){return u.default.createElement(O.Button,(0,C.default)({size:"small",variant:"contained"},n),(0,S.__)("Use Layout","elementor"))};Ce.propTypes={sx:x.default.object};var Pe=function isRegenerateButtonDisabled(n,o,a){return!(!o&&!a)||n.length>=G.SCREENSHOTS_PER_PAGE*G.MAX_PAGES},Se=function FormLayout(n){var o,a,i=n.DialogHeaderProps,l=void 0===i?{}:i,x=n.DialogContentProps,W=void 0===x?{}:x,J=n.attachments,re=(0,oe.useConfig)(),ie=re.attachmentsTypes,Se=re.onData,Re=re.onInsert,Ie=re.onSelect,Me=re.onClose,De=re.onGenerate,He=(0,$.default)({onData:Se}),Ue=He.screenshots,Fe=He.generate,Ge=He.regenerate,Ke=He.isLoading,Ze=He.error,Qe=He.abort,et=(0,G.default)({slidesCount:Ue.length}),tt=et.currentPage,rt=et.setCurrentPage,nt=et.pagesCount,ot=et.gapPercentage,at=et.slidesPerPage,it=et.offsetXPercentage,lt=et.slideWidthPercentage,ut=(0,u.useState)(-1),ct=(0,w.default)(ut,2),st=ct[0],dt=ct[1],ft=(0,u.useState)(!1),pt=(0,w.default)(ft,2),mt=pt[0],ht=pt[1],gt=(0,u.useState)(!0),yt=(0,w.default)(gt,2),vt=yt[0],bt=yt[1],_t=(0,u.useState)([]),wt=(0,w.default)(_t,2),Ct=wt[0],xt=wt[1],Pt=(0,u.useState)(!1),St=(0,w.default)(Pt,2),Ot=St[0],Et=St[1],jt=(0,u.useState)(!1),Tt=(0,w.default)(jt,2),kt=Tt[0],Rt=Tt[1],At=(0,u.useRef)((function(){})),It=(0,u.useRef)(null),Mt=null===(o=Ue[st])||void 0===o?void 0:o.template,Dt=W.children,Lt=(0,p.default)(W,he),qt=!(!Ze||0!==Ue.length),Wt=vt||qt,Bt=function abortAndClose(){Qe(),Me()},Nt=function onCloseIntent(){if(""!==It.current.value.trim()||Ue.length>0)return ht(!0);Bt()},Ht=function handleScreenshotClick(n,o){return function(){Wt||(dt(n),Ie(o))}},Ut=function onAttach(n){n.forEach((function(n){if(!ie[n.type])throw new Error("Invalid attachment type: ".concat(n.type));var o=ie[n.type];!n.previewHTML&&o.previewGenerator&&o.previewGenerator(n.content).then((function(o){n.previewHTML=o,xt((function(o){return o.map((function(o){return o.content===n.content?n:o}))}))}))})),xt(n),Et(!1),bt(!0)};return(0,u.useEffect)((function(){var n;(null===(n=Ue[0])||void 0===n?void 0:n.template)&&(Ie(Ue[0].template),dt(0))}),[null===(a=Ue[0])||void 0===a?void 0:a.template]),(0,u.useEffect)((function(){null!=J&&J.length&&Ut(J)}),[]),u.default.createElement(R.default,{onClose:Nt},u.default.createElement(R.default.Header,(0,C.default)({onClose:Nt},l),l.children,u.default.createElement(O.Tooltip,{title:kt?(0,S.__)("Expand","elementor"):(0,S.__)("Minimize","elementor")},u.default.createElement(O.IconButton,{size:"small","aria-label":"minimize",onClick:function onClick(){return Rt((function(n){return!n}))}},kt?u.default.createElement(ye,null):u.default.createElement(ge,null)))),u.default.createElement(R.default.Content,(0,C.default)({dividers:!0},Lt),u.default.createElement(O.Collapse,{in:!kt},Dt&&u.default.createElement(O.Box,{sx:{pt:2,px:2,pb:0}},Dt),Ct.length>0&&u.default.createElement(le.PromptPowerNotice,null),Ze&&u.default.createElement(O.Box,{sx:{pt:2,px:2,pb:0}},u.default.createElement(j.default,{error:Ze,onRetry:At.current})),mt&&u.default.createElement(k.default,{open:mt,title:(0,S.__)("Leave Elementor AI?","elementor"),text:(0,S.__)("Your progress will be deleted, and can't be recovered.","elementor"),onClose:Bt,onCancel:function onCancel(){return ht(!1)}}),Ot&&u.default.createElement(ce.default,{type:ue.ATTACHMENT_TYPE_URL,url:It.current.value,onAttach:Ut,onClose:function onClose(){Et(!1)}}),u.default.createElement(I.default,{shouldResetPrompt:Ot,ref:It,isActive:Wt,isLoading:Ke,showActions:Ue.length>0||Ke,attachmentsTypes:ie,attachments:Ct,onAttach:Ut,onDetach:function onDetach(n){xt((function(o){var a=(0,c.default)(o);return a.splice(n,1),a})),bt(!0)},onSubmit:function handleGenerate(n,o){n.preventDefault(),""===o.trim()&&0===Ct.length||((0,se.default)(o)?Et(!0):(De(),At.current=function(){dt(-1),Fe(o,Ct)},At.current(),bt(!1),rt(1)))},onBack:function onBack(){return bt(!1)},onEdit:function onEdit(){return bt(!0)}}),(Ue.length>0||Ke)&&u.default.createElement(u.default.Fragment,null,u.default.createElement(O.Divider,null),u.default.createElement(O.Box,{sx:{p:1.5}},u.default.createElement(O.Box,{sx:{overflow:"hidden",p:.5}},u.default.createElement(O.Box,{sx:{display:"flex",transition:"all 0.4s ease",gap:"".concat(ot,"%"),transform:"translateX(".concat(it,"%)")}},Ue.map((function(n,o){var a=n.screenshot,i=n.type,l=n.template,c=n.isError,p=n.isPending;return u.default.createElement(N.default,{key:o,url:a,type:i,disabled:Wt,isPlaceholder:c,isLoading:p,isSelected:st===o,onClick:Ht(o,l),outlineOffset:"2px",sx:{flex:"0 0 ".concat(lt,"%")}})})))),u.default.createElement(de.VoicePromotionAlert,{introductionKey:"ai-context-layout-promotion"})),Ue.length>0&&u.default.createElement(O.Box,{sx:{pt:0,px:2,pb:2},display:"grid",gridTemplateColumns:"repeat(3, 1fr)",justifyItems:"center"},u.default.createElement(ve,{onClick:function handleRegenerate(){At.current=function(){Ge(It.current.value,Ct),rt(nt+1)},At.current()},disabled:Pe(Ue,Ke,Wt),sx:{justifySelf:"start"}}),Ue.length>at&&u.default.createElement(O.Pagination,{page:tt,count:nt,disabled:Wt,onChange:function onChange(n,o){return rt(o)}}),u.default.createElement(Ce,{onClick:function applyTemplate(){Re(Mt),Ue[st].sendUsageData(),Bt()},disabled:Wt||-1===st,sx:{justifySelf:"end",gridColumn:3}}))))))};Se.propTypes={DialogHeaderProps:x.default.object,DialogContentProps:x.default.object,attachments:x.default.arrayOf(ie.AttachmentPropType)};var Re=Se;o.default=Re},89216:(n,o,a)=>{"use strict";var i=a(73203);Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var l=i(a(87363)),u=i(a(73119)),c=i(a(70966)),p=a(36626),w=a(38003),C=i(a(23615)),x=i(a(55995)),S=["isLoading"],O=(0,p.withDirection)(x.default),j=function EnhanceButton(n){var o=n.isLoading,a=(0,c.default)(n,S);return l.default.createElement(p.Tooltip,{title:(0,w.__)("Enhance prompt","elementor")},l.default.createElement(p.Box,{component:"span",sx:{cursor:a.disabled?"default":"pointer"}},l.default.createElement(p.IconButton,(0,u.default)({size:"small",color:"secondary"},a),o?l.default.createElement(p.CircularProgress,{color:"secondary",size:20}):l.default.createElement(O,{fontSize:"small"}))))};j.propTypes={disabled:C.default.bool,isLoading:C.default.bool};var k=j;o.default=k},78025:(n,o,a)=>{"use strict";var i=a(73203);Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var l=i(a(87363)),u=i(a(73119)),c=a(36626),p=a(38003),w=i(a(23615)),C=function GenerateSubmit(n){return l.default.createElement(c.Button,(0,u.default)({fullWidth:!0,size:"medium",type:"submit",variant:"contained"},n),n.children||(0,p.__)("Generate","elementor"))};C.propTypes={children:w.default.node};var x=C;o.default=x},96587:(n,o,a)=>{"use strict";var i=a(73203),l=a(7501);Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var u=function _interopRequireWildcard(n,o){if(!o&&n&&n.__esModule)return n;if(null===n||"object"!==l(n)&&"function"!=typeof n)return{default:n};var a=_getRequireWildcardCache(o);if(a&&a.has(n))return a.get(n);var i={},u=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var c in n)if("default"!==c&&Object.prototype.hasOwnProperty.call(n,c)){var p=u?Object.getOwnPropertyDescriptor(n,c):null;p&&(p.get||p.set)?Object.defineProperty(i,c,p):i[c]=n[c]}i.default=n,a&&a.set(n,i);return i}(a(87363)),c=i(a(50824)),p=i(a(10029)),w=i(a(40131)),C=a(36626),x=a(38003),S=i(a(23615)),O=a(46183),j=a(34029);function _getRequireWildcardCache(n){if("function"!=typeof WeakMap)return null;var o=new WeakMap,a=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(n){return n?a:o})(n)}var k=function GetStarted(n){var o=n.onSuccess,a=(0,u.useState)(!1),i=(0,w.default)(a,2),l=i[0],S=i[1],k=function(){var n=(0,p.default)(c.default.mark((function _callee(){return c.default.wrap((function _callee$(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,(0,O.setGetStarted)();case 2:o();case 3:case"end":return n.stop()}}),_callee)})));return function onGetStartedClick(){return n.apply(this,arguments)}}();return u.default.createElement(C.Stack,{alignItems:"center",gap:1.5},u.default.createElement(j.AIIcon,{sx:{color:"text.primary",fontSize:"60px",mb:1}}),u.default.createElement(C.Typography,{variant:"h4",sx:{color:"text.primary"}},(0,x.__)("Step into the future with Elementor AI","elementor")),u.default.createElement(C.Typography,{variant:"body2"},(0,x.__)("Create smarter with AI text and code generators built right into the editor.","elementor")),u.default.createElement(C.Stack,{direction:"row",gap:1.5,alignItems:"flex-start"},u.default.createElement(C.Checkbox,{id:"e-ai-terms-approval",color:"secondary",checked:l,onClick:function onClick(){return S((function(n){return!n}))}}),u.default.createElement(C.Stack,null,u.default.createElement(C.Typography,{variant:"caption",sx:{maxWidth:520},component:"label",htmlFor:"e-ai-terms-approval"},(0,x.__)("I approve the ","elementor"),u.default.createElement(C.Link,{href:"https://go.elementor.com/ai-terms/",target:"_blank",color:"info.main"},(0,x.__)("Terms of Service","elementor"))," & ",u.default.createElement(C.Link,{href:"https://go.elementor.com/ai-privacy-policy/",target:"_blank",color:"info.main"},(0,x.__)("Privacy Policy","elementor")),(0,x.__)(" of the Elementor AI service.","elementor"),u.default.createElement("br",null),(0,x.__)("This includes consenting to the collection and use of data to improve user experience.","elementor")))),u.default.createElement(C.Button,{disabled:!l,variant:"contained",onClick:k,sx:{mt:1,"&:hover":{color:"primary.contrastText"}}},(0,x.__)("Get Started","elementor")))};k.propTypes={onSuccess:S.default.func.isRequired};var R=k;o.default=R},77292:(n,o,a)=>{"use strict";var i=a(73203);Object.defineProperty(o,"__esModule",{value:!0}),o.AttachmentsTypesPropType=o.AttachmentPropType=void 0;var l=i(a(23615)),u=l.default.shape({type:l.default.string,previewHTML:l.default.string,content:l.default.string,label:l.default.string,source:l.default.string});o.AttachmentPropType=u;var c=l.default.shape({type:l.default.shape({promptPlaceholder:l.default.string,promptSuggestions:l.default.arrayOf(l.default.shape({text:l.default.string.isRequired})),previewGenerator:l.default.func})});o.AttachmentsTypesPropType=c},36619:(n,o,a)=>{"use strict";var i=a(73203);Object.defineProperty(o,"__esModule",{value:!0}),o.renderLayoutApp=o.openPanel=o.onConnect=o.importToEditor=o.getUiConfig=o.closePanel=o.WEB_BASED_PROMPTS=o.VARIATIONS_PROMPTS=void 0;var l=i(a(87363)),u=i(a(50824)),c=i(a(10029)),p=i(a(66535)),w=a(5389),C=i(a(78949)),x=a(24271),S=a(48096),O=a(38003),j=i(a(42637)),k=a(35879),R=function closePanel(){$e.run("panel/close"),$e.components.get("panel").blockUserInteractions()};o.closePanel=R;var I=function openPanel(){$e.run("panel/open"),$e.components.get("panel").unblockUserInteractions()};o.openPanel=I;var W=function onConnect(n){elementorCommon.config.library_connect.is_connected=!0,elementorCommon.config.library_connect.current_access_level=n.kits_access_level||n.access_level||0,elementorCommon.config.library_connect.current_access_tier=n.access_tier};o.onConnect=W;var N=function getUiConfig(){var n,o;return{colorScheme:(null===(n=elementor)||void 0===n||null===(o=n.getPreferences)||void 0===o?void 0:o.call(n,"ui_theme"))||"auto",isRTL:elementorCommon.config.isRTL}};o.getUiConfig=N;var $=[{text:(0,O.__)("Minimalist design with bold typography about","elementor")},{text:(0,O.__)("Elegant style with serif fonts discussing","elementor")},{text:(0,O.__)("Retro vibe with muted colors and classic fonts about","elementor")},{text:(0,O.__)("Futuristic design with neon accents about","elementor")},{text:(0,O.__)("Professional look with clean lines for","elementor")},{text:(0,O.__)("Earthy tones and organic shapes featuring","elementor")},{text:(0,O.__)("Luxurious theme with rich colors discussing","elementor")},{text:(0,O.__)("Tech-inspired style with modern fonts about","elementor")},{text:(0,O.__)("Warm hues with comforting visuals about","elementor")}];o.VARIATIONS_PROMPTS=$;var G=[{text:(0,O.__)("Change the content to be about [topic]","elementor")},{text:(0,O.__)("Generate lorem ipsum placeholder text for all paragraphs","elementor")},{text:(0,O.__)("Revise the content to focus on [topic] and then translate it into Spanish","elementor")},{text:(0,O.__)("Shift the focus of the content to [topic] in order to showcase our company's mission and values","elementor")},{text:(0,O.__)("Alter the content to provide helpful tips related to [topic]","elementor")},{text:(0,O.__)("Adjust the content to include FAQs and answers for common inquiries about [topic]","elementor")}];o.WEB_BASED_PROMPTS=G;var J=(0,O.__)("Press '/' for suggestions or describe the changes you want to apply (optional)...","elementor");o.renderLayoutApp=function renderLayoutApp(){var n,o=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{parentContainer:null,mode:"",at:null,onClose:null,onGenerate:null,onInsert:null,onRenderApp:null,onSelect:null,attachments:[]};R();var a=(0,w.createPreviewContainer)(o.parentContainer,{at:o.at}),i=N(),S=i.colorScheme,O=i.isRTL,k=document.createElement("div");document.body.append(k);var re,oe=window.elementorFrontend.elements.$window[0].getComputedStyle(window.elementorFrontend.elements.$body[0]),ie=p.default.render(l.default.createElement(j.default,{isRTL:O,colorScheme:S},l.default.createElement(C.default,{mode:o.mode,currentContext:{body:{backgroundColor:oe.backgroundColor,backgroundImage:oe.backgroundImage}},attachmentsTypes:{json:{promptSuggestions:$,promptPlaceholder:J,previewGenerator:(re=(0,c.default)(u.default.mark((function _callee(n){var o;return u.default.wrap((function _callee$(a){for(;;)switch(a.prev=a.next){case 0:return a.next=2,(0,x.takeScreenshot)(n);case 2:return o=a.sent,a.abrupt("return",'<img src="'.concat(o,'" />'));case 4:case"end":return a.stop()}}),_callee)}))),function previewGenerator(n){return re.apply(this,arguments)})},url:{promptPlaceholder:J,promptSuggestions:G}},attachments:o.attachments||[],onClose:function onClose(){var n;a.destroy(),null===(n=o.onClose)||void 0===n||n.call(o),le(),k.remove(),I()},onConnect:W,onGenerate:function onGenerate(){var n;null===(n=o.onGenerate)||void 0===n||n.call(o,{previewContainer:a})},onData:function(){var n=(0,c.default)(u.default.mark((function _callee2(n){var o;return u.default.wrap((function _callee2$(a){for(;;)switch(a.prev=a.next){case 0:return a.next=2,(0,x.takeScreenshot)(n);case 2:return o=a.sent,a.abrupt("return",{screenshot:o,template:n});case 4:case"end":return a.stop()}}),_callee2)})));return function(o){return n.apply(this,arguments)}}(),onSelect:function onSelect(n){var i;null===(i=o.onSelect)||void 0===i||i.call(o),a.setContent(n)},onInsert:o.onInsert,hasPro:elementor.helpers.hasPro()})),k),le=ie.unmount;null===(n=o.onRenderApp)||void 0===n||n.call(o,{previewContainer:a})};o.importToEditor=function importToEditor(n){var o=n.parentContainer,a=n.at,i=n.template,l=n.historyTitle,u=n.replace,c=void 0!==u&&u,p=(0,S.startHistoryLog)({type:"import",title:l});c&&$e.run("document/elements/delete",{container:o.children.at(a)}),$e.run("document/elements/create",{container:o,model:(0,k.generateIds)(i),options:{at:a,edit:!0}}),p()}},48096:(n,o)=>{"use strict";Object.defineProperty(o,"__esModule",{value:!0}),o.startHistoryLog=function startHistoryLog(n){var o=n.type,a=n.title,i=$e.internal("document/history/start-log",{type:o,title:a});return function(){return $e.internal("document/history/end-log",{id:i})}},o.toggleHistory=function toggleHistory(n){elementor.documents.getCurrent().history.setActive(n)}},5389:(n,o,a)=>{"use strict";var i=a(73203);Object.defineProperty(o,"__esModule",{value:!0}),o.createPreviewContainer=function createPreviewContainer(n){var o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=new Map,i=function createIdleContainer(n){var o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=createContainer(n,{elType:"container"},o);return a.view.$el.addClass(C),a}(n,o);function getAllContainers(){return[].concat((0,u.default)(a.values()),[i])}return{init:function init(){showContainer(i)},reset:function reset(){deleteContainers((0,u.default)(a.values())),a.clear(),showContainer(i)},setContent:function setContent(i){if(i){if(function hideContainers(n){n.forEach((function(n){n.view.$el.addClass(w)}))}(getAllContainers()),!a.has(i)){var l=createContainer(n,i,o);a.set(i,l)}showContainer(a.get(i))}},destroy:function destroy(){deleteContainers(getAllContainers()),a.clear()}}};var l=i(a(93231)),u=i(a(9833)),c=a(48096);function ownKeys(n,o){var a=Object.keys(n);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(n);o&&(i=i.filter((function(o){return Object.getOwnPropertyDescriptor(n,o).enumerable}))),a.push.apply(a,i)}return a}function _objectSpread(n){for(var o=1;o<arguments.length;o++){var a=null!=arguments[o]?arguments[o]:{};o%2?ownKeys(Object(a),!0).forEach((function(o){(0,l.default)(n,o,a[o])})):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(a)):ownKeys(Object(a)).forEach((function(o){Object.defineProperty(n,o,Object.getOwnPropertyDescriptor(a,o))}))}return n}var p="e-ai-preview-container",w=p+"--hidden",C=p+"--idle";function createContainer(n,o){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};(0,c.toggleHistory)(!1);var i=$e.run("document/elements/create",{container:n,model:_objectSpread(_objectSpread({},o),{},{id:"".concat(p,"-").concat(elementorCommon.helpers.getUniqueId().toString())}),options:_objectSpread(_objectSpread({},a),{},{edit:!1})});return(0,c.toggleHistory)(!0),i.view.$el.addClass(w),i}function showContainer(n){n.view.$el.removeClass(w),setTimeout((function(){n.view.$el[0].scrollIntoView({behavior:"smooth",block:"start"})}))}function deleteContainers(n){(0,c.toggleHistory)(!1),$e.run("document/elements/delete",{containers:n}),(0,c.toggleHistory)(!0)}},24271:(n,o,a)=>{"use strict";var i=a(73203);Object.defineProperty(o,"__esModule",{value:!0}),o.takeScreenshot=void 0;var l=i(a(50824)),u=i(a(9833)),c=i(a(10029)),p=a(62478),w=a(48096),C=a(35879),x=function(){var n=(0,c.default)(l.default.mark((function _callee(n){var o,a,i;return l.default.wrap((function _callee$(l){for(;;)switch(l.prev=l.next){case 0:if(n){l.next=2;break}return l.abrupt("return","");case 2:return(0,w.toggleHistory)(!1),o=createHiddenWrapper(),wrapContainer(a=createContainer(n),o),elementor.getPreviewView().$childViewContainer[0].appendChild(o),l.next=9,waitForContainer(a.id);case 9:if(!n.elements.length){l.next=12;break}return l.next=12,Promise.all(n.elements.map((function(n){return waitForContainer(n.id)})));case 12:return l.prev=12,l.next=15,function toWebp(n){return _toWebp.apply(this,arguments)}(a.view.$el[0],{quality:.01,imagePlaceholder:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII="});case 15:i=l.sent,l.next=21;break;case 18:l.prev=18,l.t0=l.catch(12),i="";case 21:return deleteContainer(a),o.remove(),(0,w.toggleHistory)(!0),l.abrupt("return",i);case 25:case"end":return l.stop()}}),_callee,null,[[12,18]])})));return function takeScreenshot(o){return n.apply(this,arguments)}}();function _toWebp(){return _toWebp=(0,c.default)(l.default.mark((function _callee3(n){var o,a,i,u=arguments;return l.default.wrap((function _callee3$(l){for(;;)switch(l.prev=l.next){case 0:return a=u.length>1&&void 0!==u[1]?u[1]:{},l.next=3,(0,p.toCanvas)(n,a);case 3:return i=l.sent,l.abrupt("return",i.toDataURL("image/webp",null!==(o=a.quality)&&void 0!==o?o:1));case 5:case"end":return l.stop()}}),_callee3)}))),_toWebp.apply(this,arguments)}function createHiddenWrapper(){var n=document.createElement("div");return n.style.position="fixed",n.style.opacity="0",n.style.inset="0",n}function createContainer(n){var o=(0,C.generateIds)(n);return o.id="e-ai-screenshot-container-".concat(o.id),$e.run("document/elements/create",{container:elementor.getPreviewContainer(),model:o,options:{edit:!1}})}function deleteContainer(n){return $e.run("document/elements/delete",{container:n})}function waitForContainer(n){var o=function sleep(n){return new Promise((function(o){return setTimeout(o,n)}))}(arguments.length>1&&void 0!==arguments[1]?arguments[1]:5e3),a=new Promise((function(o){elementorFrontend.hooks.addAction("frontend/element_ready/global",function(){var a=(0,c.default)(l.default.mark((function _callee2(a){var i;return l.default.wrap((function _callee2$(l){for(;;)switch(l.prev=l.next){case 0:if(a.data("id")!==n){l.next=5;break}return i=(0,u.default)(a[0].querySelectorAll("img")),l.next=4,Promise.all(i.map(waitForImage));case 4:o();case 5:case"end":return l.stop()}}),_callee2)})));return function(n){return a.apply(this,arguments)}}())}));return Promise.any([o,a])}function waitForImage(n){return n.complete?Promise.resolve():new Promise((function(o){n.addEventListener("load",o),n.addEventListener("error",(function(){n.remove(),o()}))}))}function wrapContainer(n,o){var a=n.view.$el[0];a.parentNode.insertBefore(o,a),o.appendChild(a)}o.takeScreenshot=x},38944:(n,o,a)=>{"use strict";function r(n){var o,a,i="";if("string"==typeof n||"number"==typeof n)i+=n;else if("object"==typeof n)if(Array.isArray(n))for(o=0;o<n.length;o++)n[o]&&(a=r(n[o]))&&(i&&(i+=" "),i+=a);else for(o in n)n[o]&&(i&&(i+=" "),i+=o);return i}function clsx(){for(var n,o,a=0,i="";a<arguments.length;)(n=arguments[a++])&&(o=r(n))&&(i&&(i+=" "),i+=o);return i}a.r(o),a.d(o,{clsx:()=>clsx,default:()=>i});const i=clsx},55839:(n,o,a)=>{"use strict";var i=a(12097),l={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},u={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},c={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},p={};function getStatics(n){return i.isMemo(n)?c:p[n.$$typeof]||l}p[i.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},p[i.Memo]=c;var w=Object.defineProperty,C=Object.getOwnPropertyNames,x=Object.getOwnPropertySymbols,S=Object.getOwnPropertyDescriptor,O=Object.getPrototypeOf,j=Object.prototype;n.exports=function hoistNonReactStatics(n,o,a){if("string"!=typeof o){if(j){var i=O(o);i&&i!==j&&hoistNonReactStatics(n,i,a)}var l=C(o);x&&(l=l.concat(x(o)));for(var c=getStatics(n),p=getStatics(o),k=0;k<l.length;++k){var R=l[k];if(!(u[R]||a&&a[R]||p&&p[R]||c&&c[R])){var I=S(o,R);try{w(n,R,I)}catch(n){}}}}return n}},14173:(n,o)=>{"use strict";var a="function"==typeof Symbol&&Symbol.for,i=a?Symbol.for("react.element"):60103,l=a?Symbol.for("react.portal"):60106,u=a?Symbol.for("react.fragment"):60107,c=a?Symbol.for("react.strict_mode"):60108,p=a?Symbol.for("react.profiler"):60114,w=a?Symbol.for("react.provider"):60109,C=a?Symbol.for("react.context"):60110,x=a?Symbol.for("react.async_mode"):60111,S=a?Symbol.for("react.concurrent_mode"):60111,O=a?Symbol.for("react.forward_ref"):60112,j=a?Symbol.for("react.suspense"):60113,k=a?Symbol.for("react.suspense_list"):60120,R=a?Symbol.for("react.memo"):60115,I=a?Symbol.for("react.lazy"):60116,W=a?Symbol.for("react.block"):60121,N=a?Symbol.for("react.fundamental"):60117,$=a?Symbol.for("react.responder"):60118,G=a?Symbol.for("react.scope"):60119;function z(n){if("object"==typeof n&&null!==n){var o=n.$$typeof;switch(o){case i:switch(n=n.type){case x:case S:case u:case p:case c:case j:return n;default:switch(n=n&&n.$$typeof){case C:case O:case I:case R:case w:return n;default:return o}}case l:return o}}}function A(n){return z(n)===S}o.AsyncMode=x,o.ConcurrentMode=S,o.ContextConsumer=C,o.ContextProvider=w,o.Element=i,o.ForwardRef=O,o.Fragment=u,o.Lazy=I,o.Memo=R,o.Portal=l,o.Profiler=p,o.StrictMode=c,o.Suspense=j,o.isAsyncMode=function(n){return A(n)||z(n)===x},o.isConcurrentMode=A,o.isContextConsumer=function(n){return z(n)===C},o.isContextProvider=function(n){return z(n)===w},o.isElement=function(n){return"object"==typeof n&&null!==n&&n.$$typeof===i},o.isForwardRef=function(n){return z(n)===O},o.isFragment=function(n){return z(n)===u},o.isLazy=function(n){return z(n)===I},o.isMemo=function(n){return z(n)===R},o.isPortal=function(n){return z(n)===l},o.isProfiler=function(n){return z(n)===p},o.isStrictMode=function(n){return z(n)===c},o.isSuspense=function(n){return z(n)===j},o.isValidElementType=function(n){return"string"==typeof n||"function"==typeof n||n===u||n===S||n===p||n===c||n===j||n===k||"object"==typeof n&&null!==n&&(n.$$typeof===I||n.$$typeof===R||n.$$typeof===w||n.$$typeof===C||n.$$typeof===O||n.$$typeof===N||n.$$typeof===$||n.$$typeof===G||n.$$typeof===W)},o.typeOf=z},12097:(n,o,a)=>{"use strict";n.exports=a(14173)},62478:(n,o,a)=>{"use strict";a.r(o),a.d(o,{getFontEmbedCSS:()=>getFontEmbedCSS,toBlob:()=>toBlob,toCanvas:()=>toCanvas,toJpeg:()=>toJpeg,toPixelData:()=>toPixelData,toPng:()=>toPng,toSvg:()=>toSvg});const i=(()=>{let n=0;return()=>(n+=1,`u${`0000${(Math.random()*36**4<<0).toString(36)}`.slice(-4)}${n}`)})();function toArray(n){const o=[];for(let a=0,i=n.length;a<i;a++)o.push(n[a]);return o}function px(n,o){const a=(n.ownerDocument.defaultView||window).getComputedStyle(n).getPropertyValue(o);return a?parseFloat(a.replace("px","")):0}function getImageSize(n,o={}){return{width:o.width||function getNodeWidth(n){const o=px(n,"border-left-width"),a=px(n,"border-right-width");return n.clientWidth+o+a}(n),height:o.height||function getNodeHeight(n){const o=px(n,"border-top-width"),a=px(n,"border-bottom-width");return n.clientHeight+o+a}(n)}}const l=16384;function createImage(n){return new Promise(((o,a)=>{const i=new Image;i.decode=()=>o(i),i.onload=()=>o(i),i.onerror=a,i.crossOrigin="anonymous",i.decoding="async",i.src=n}))}async function nodeToDataURL(n,o,a){const i="http://www.w3.org/2000/svg",l=document.createElementNS(i,"svg"),u=document.createElementNS(i,"foreignObject");return l.setAttribute("width",`${o}`),l.setAttribute("height",`${a}`),l.setAttribute("viewBox",`0 0 ${o} ${a}`),u.setAttribute("width","100%"),u.setAttribute("height","100%"),u.setAttribute("x","0"),u.setAttribute("y","0"),u.setAttribute("externalResourcesRequired","true"),l.appendChild(u),u.appendChild(n),async function svgToDataURL(n){return Promise.resolve().then((()=>(new XMLSerializer).serializeToString(n))).then(encodeURIComponent).then((n=>`data:image/svg+xml;charset=utf-8,${n}`))}(l)}const isInstanceOfElement=(n,o)=>{if(n instanceof o)return!0;const a=Object.getPrototypeOf(n);return null!==a&&(a.constructor.name===o.name||isInstanceOfElement(a,o))};function getPseudoElementStyle(n,o,a){const i=`.${n}:${o}`,l=a.cssText?function formatCSSText(n){const o=n.getPropertyValue("content");return`${n.cssText} content: '${o.replace(/'|"/g,"")}';`}(a):function formatCSSProperties(n){return toArray(n).map((o=>`${o}: ${n.getPropertyValue(o)}${n.getPropertyPriority(o)?" !important":""};`)).join(" ")}(a);return document.createTextNode(`${i}{${l}}`)}function clonePseudoElement(n,o,a){const l=window.getComputedStyle(n,a),u=l.getPropertyValue("content");if(""===u||"none"===u)return;const c=i();try{o.className=`${o.className} ${c}`}catch(n){return}const p=document.createElement("style");p.appendChild(getPseudoElementStyle(c,a,l)),o.appendChild(p)}const u="application/font-woff",c="image/jpeg",p={woff:u,woff2:u,ttf:"application/font-truetype",eot:"application/vnd.ms-fontobject",png:"image/png",jpg:c,jpeg:c,gif:"image/gif",tiff:"image/tiff",svg:"image/svg+xml",webp:"image/webp"};function getMimeType(n){const o=function getExtension(n){const o=/\.([^./]*?)$/g.exec(n);return o?o[1]:""}(n).toLowerCase();return p[o]||""}function isDataUrl(n){return-1!==n.search(/^(data:)/)}function makeDataUrl(n,o){return`data:${o};base64,${n}`}async function fetchAsDataURL(n,o,a){const i=await fetch(n,o);if(404===i.status)throw new Error(`Resource "${i.url}" not found`);const l=await i.blob();return new Promise(((n,o)=>{const u=new FileReader;u.onerror=o,u.onloadend=()=>{try{n(a({res:i,result:u.result}))}catch(n){o(n)}},u.readAsDataURL(l)}))}const w={};async function resourceToDataURL(n,o,a){const i=function getCacheKey(n,o,a){let i=n.replace(/\?.*/,"");return a&&(i=n),/ttf|otf|eot|woff2?/i.test(i)&&(i=i.replace(/.*\//,"")),o?`[${o}]${i}`:i}(n,o,a.includeQueryParams);if(null!=w[i])return w[i];let l;a.cacheBust&&(n+=(/\?/.test(n)?"&":"?")+(new Date).getTime());try{const i=await fetchAsDataURL(n,a.fetchRequestInit,(({res:n,result:a})=>(o||(o=n.headers.get("Content-Type")||""),function getContentFromDataUrl(n){return n.split(/,/)[1]}(a))));l=makeDataUrl(i,o)}catch(o){l=a.imagePlaceholder||"";let i=`Failed to fetch resource: ${n}`;o&&(i="string"==typeof o?o:o.message),i&&console.warn(i)}return w[i]=l,l}async function cloneSingleNode(n,o){return isInstanceOfElement(n,HTMLCanvasElement)?async function cloneCanvasElement(n){const o=n.toDataURL();return"data:,"===o?n.cloneNode(!1):createImage(o)}(n):isInstanceOfElement(n,HTMLVideoElement)?async function cloneVideoElement(n,o){if(n.currentSrc){const o=document.createElement("canvas"),a=o.getContext("2d");return o.width=n.clientWidth,o.height=n.clientHeight,null==a||a.drawImage(n,0,0,o.width,o.height),createImage(o.toDataURL())}const a=n.poster,i=getMimeType(a);return createImage(await resourceToDataURL(a,i,o))}(n,o):isInstanceOfElement(n,HTMLIFrameElement)?async function cloneIFrameElement(n){var o;try{if(null===(o=null==n?void 0:n.contentDocument)||void 0===o?void 0:o.body)return await cloneNode(n.contentDocument.body,{},!0)}catch(n){}return n.cloneNode(!1)}(n):n.cloneNode(!1)}const isSlotElement=n=>null!=n.tagName&&"SLOT"===n.tagName.toUpperCase();function decorate(n,o){return isInstanceOfElement(o,Element)&&(!function cloneCSSStyle(n,o){const a=o.style;if(!a)return;const i=window.getComputedStyle(n);i.cssText?(a.cssText=i.cssText,a.transformOrigin=i.transformOrigin):toArray(i).forEach((l=>{let u=i.getPropertyValue(l);if("font-size"===l&&u.endsWith("px")){const n=Math.floor(parseFloat(u.substring(0,u.length-2)))-.1;u=`${n}px`}isInstanceOfElement(n,HTMLIFrameElement)&&"display"===l&&"inline"===u&&(u="block"),"d"===l&&o.getAttribute("d")&&(u=`path(${o.getAttribute("d")})`),a.setProperty(l,u,i.getPropertyPriority(l))}))}(n,o),function clonePseudoElements(n,o){clonePseudoElement(n,o,":before"),clonePseudoElement(n,o,":after")}(n,o),function cloneInputValue(n,o){isInstanceOfElement(n,HTMLTextAreaElement)&&(o.innerHTML=n.value),isInstanceOfElement(n,HTMLInputElement)&&o.setAttribute("value",n.value)}(n,o),function cloneSelectValue(n,o){if(isInstanceOfElement(n,HTMLSelectElement)){const a=o,i=Array.from(a.children).find((o=>n.value===o.getAttribute("value")));i&&i.setAttribute("selected","")}}(n,o)),o}async function cloneNode(n,o,a){return a||!o.filter||o.filter(n)?Promise.resolve(n).then((n=>cloneSingleNode(n,o))).then((a=>async function cloneChildren(n,o,a){var i,l;let u=[];return u=isSlotElement(n)&&n.assignedNodes?toArray(n.assignedNodes()):isInstanceOfElement(n,HTMLIFrameElement)&&(null===(i=n.contentDocument)||void 0===i?void 0:i.body)?toArray(n.contentDocument.body.childNodes):toArray((null!==(l=n.shadowRoot)&&void 0!==l?l:n).childNodes),0===u.length||isInstanceOfElement(n,HTMLVideoElement)||await u.reduce(((n,i)=>n.then((()=>cloneNode(i,a))).then((n=>{n&&o.appendChild(n)}))),Promise.resolve()),o}(n,a,o))).then((o=>decorate(n,o))).then((n=>async function ensureSVGSymbols(n,o){const a=n.querySelectorAll?n.querySelectorAll("use"):[];if(0===a.length)return n;const i={};for(let l=0;l<a.length;l++){const u=a[l].getAttribute("xlink:href");if(u){const a=n.querySelector(u),l=document.querySelector(u);a||!l||i[u]||(i[u]=await cloneNode(l,o,!0))}}const l=Object.values(i);if(l.length){const o="http://www.w3.org/1999/xhtml",a=document.createElementNS(o,"svg");a.setAttribute("xmlns",o),a.style.position="absolute",a.style.width="0",a.style.height="0",a.style.overflow="hidden",a.style.display="none";const i=document.createElementNS(o,"defs");a.appendChild(i);for(let n=0;n<l.length;n++)i.appendChild(l[n]);n.appendChild(a)}return n}(n,o))):null}const C=/url\((['"]?)([^'"]+?)\1\)/g,x=/url\([^)]+\)\s*format\((["']?)([^"']+)\1\)/g,S=/src:\s*(?:url\([^)]+\)\s*format\([^)]+\)[,;]\s*)+/g;async function embed_resources_embed(n,o,a,i,l){try{const u=a?function resolveUrl(n,o){if(n.match(/^[a-z]+:\/\//i))return n;if(n.match(/^\/\//))return window.location.protocol+n;if(n.match(/^[a-z]+:/i))return n;const a=document.implementation.createHTMLDocument(),i=a.createElement("base"),l=a.createElement("a");return a.head.appendChild(i),a.body.appendChild(l),o&&(i.href=o),l.href=n,l.href}(o,a):o,c=getMimeType(o);let p;if(l){p=makeDataUrl(await l(u),c)}else p=await resourceToDataURL(u,c,i);return n.replace(function toRegex(n){const o=n.replace(/([.*+?^${}()|\[\]\/\\])/g,"\\$1");return new RegExp(`(url\\(['"]?)(${o})(['"]?\\))`,"g")}(o),`$1${p}$3`)}catch(n){}return n}function shouldEmbed(n){return-1!==n.search(C)}async function embedResources(n,o,a){if(!shouldEmbed(n))return n;const i=function filterPreferredFontFormat(n,{preferredFontFormat:o}){return o?n.replace(S,(n=>{for(;;){const[a,,i]=x.exec(n)||[];if(!i)return"";if(i===o)return`src: ${a};`}})):n}(n,a),l=function parseURLs(n){const o=[];return n.replace(C,((n,a,i)=>(o.push(i),n))),o.filter((n=>!isDataUrl(n)))}(i);return l.reduce(((n,i)=>n.then((n=>embed_resources_embed(n,i,o,a)))),Promise.resolve(i))}async function embedProp(n,o,a){var i;const l=null===(i=o.style)||void 0===i?void 0:i.getPropertyValue(n);if(l){const i=await embedResources(l,null,a);return o.style.setProperty(n,i,o.style.getPropertyPriority(n)),!0}return!1}async function embedImages(n,o){isInstanceOfElement(n,Element)&&(await async function embedBackground(n,o){await embedProp("background",n,o)||await embedProp("background-image",n,o),await embedProp("mask",n,o)||await embedProp("mask-image",n,o)}(n,o),await async function embedImageNode(n,o){const a=isInstanceOfElement(n,HTMLImageElement);if((!a||isDataUrl(n.src))&&(!isInstanceOfElement(n,SVGImageElement)||isDataUrl(n.href.baseVal)))return;const i=a?n.src:n.href.baseVal,l=await resourceToDataURL(i,getMimeType(i),o);await new Promise(((o,i)=>{n.onload=o,n.onerror=i;const u=n;u.decode&&(u.decode=o),"lazy"===u.loading&&(u.loading="eager"),a?(n.srcset="",n.src=l):n.href.baseVal=l}))}(n,o),await async function embedChildren(n,o){const a=toArray(n.childNodes).map((n=>embedImages(n,o)));await Promise.all(a).then((()=>n))}(n,o))}const O={};async function fetchCSS(n){let o=O[n];if(null!=o)return o;const a=await fetch(n);return o={url:n,cssText:await a.text()},O[n]=o,o}async function embedFonts(n,o){let a=n.cssText;const i=/url\(["']?([^"')]+)["']?\)/g,l=(a.match(/url\([^)]+\)/g)||[]).map((async l=>{let u=l.replace(i,"$1");return u.startsWith("https://")||(u=new URL(u,n.url).href),fetchAsDataURL(u,o.fetchRequestInit,(({result:n})=>(a=a.replace(l,`url(${n})`),[l,n])))}));return Promise.all(l).then((()=>a))}function parseCSS(n){if(null==n)return[];const o=[];let a=n.replace(/(\/\*[\s\S]*?\*\/)/gi,"");const i=new RegExp("((@.*?keyframes [\\s\\S]*?){([\\s\\S]*?}\\s*?)})","gi");for(;;){const n=i.exec(a);if(null===n)break;o.push(n[0])}a=a.replace(i,"");const l=/@import[\s\S]*?url\([^)]*\)[\s\S]*?;/gi,u=new RegExp("((\\s*?(?:\\/\\*[\\s\\S]*?\\*\\/)?\\s*?@media[\\s\\S]*?){([\\s\\S]*?)}\\s*?})|(([\\s\\S]*?){([\\s\\S]*?)})","gi");for(;;){let n=l.exec(a);if(null===n){if(n=u.exec(a),null===n)break;l.lastIndex=u.lastIndex}else u.lastIndex=l.lastIndex;o.push(n[0])}return o}async function parseWebFontRules(n,o){if(null==n.ownerDocument)throw new Error("Provided element is not within a Document");const a=toArray(n.ownerDocument.styleSheets),i=await async function getCSSRules(n,o){const a=[],i=[];return n.forEach((a=>{if("cssRules"in a)try{toArray(a.cssRules||[]).forEach(((n,l)=>{if(n.type===CSSRule.IMPORT_RULE){let u=l+1;const c=fetchCSS(n.href).then((n=>embedFonts(n,o))).then((n=>parseCSS(n).forEach((n=>{try{a.insertRule(n,n.startsWith("@import")?u+=1:a.cssRules.length)}catch(o){console.error("Error inserting rule from remote css",{rule:n,error:o})}})))).catch((n=>{console.error("Error loading remote css",n.toString())}));i.push(c)}}))}catch(l){const u=n.find((n=>null==n.href))||document.styleSheets[0];null!=a.href&&i.push(fetchCSS(a.href).then((n=>embedFonts(n,o))).then((n=>parseCSS(n).forEach((n=>{u.insertRule(n,a.cssRules.length)})))).catch((n=>{console.error("Error loading remote stylesheet",n)}))),console.error("Error inlining remote css file",l)}})),Promise.all(i).then((()=>(n.forEach((n=>{if("cssRules"in n)try{toArray(n.cssRules||[]).forEach((n=>{a.push(n)}))}catch(o){console.error(`Error while reading CSS rules from ${n.href}`,o)}})),a)))}(a,o);return function getWebFontRules(n){return n.filter((n=>n.type===CSSRule.FONT_FACE_RULE)).filter((n=>shouldEmbed(n.style.getPropertyValue("src"))))}(i)}async function getWebFontCSS(n,o){const a=await parseWebFontRules(n,o);return(await Promise.all(a.map((n=>{const a=n.parentStyleSheet?n.parentStyleSheet.href:null;return embedResources(n.cssText,a,o)})))).join("\n")}async function toSvg(n,o={}){const{width:a,height:i}=getImageSize(n,o),l=await cloneNode(n,o,!0);await async function embedWebFonts(n,o){const a=null!=o.fontEmbedCSS?o.fontEmbedCSS:o.skipFonts?null:await getWebFontCSS(n,o);if(a){const o=document.createElement("style"),i=document.createTextNode(a);o.appendChild(i),n.firstChild?n.insertBefore(o,n.firstChild):n.appendChild(o)}}(l,o),await embedImages(l,o),function applyStyle(n,o){const{style:a}=n;o.backgroundColor&&(a.backgroundColor=o.backgroundColor),o.width&&(a.width=`${o.width}px`),o.height&&(a.height=`${o.height}px`);const i=o.style;return null!=i&&Object.keys(i).forEach((n=>{a[n]=i[n]})),n}(l,o);return await nodeToDataURL(l,a,i)}async function toCanvas(n,o={}){const{width:a,height:i}=getImageSize(n,o),u=await toSvg(n,o),c=await createImage(u),p=document.createElement("canvas"),w=p.getContext("2d"),C=o.pixelRatio||function getPixelRatio(){let n,o;try{o=process}catch(n){}const a=o&&o.env?o.env.devicePixelRatio:null;return a&&(n=parseInt(a,10),Number.isNaN(n)&&(n=1)),n||window.devicePixelRatio||1}(),x=o.canvasWidth||a,S=o.canvasHeight||i;return p.width=x*C,p.height=S*C,o.skipAutoScale||function checkCanvasDimensions(n){(n.width>l||n.height>l)&&(n.width>l&&n.height>l?n.width>n.height?(n.height*=l/n.width,n.width=l):(n.width*=l/n.height,n.height=l):n.width>l?(n.height*=l/n.width,n.width=l):(n.width*=l/n.height,n.height=l))}(p),p.style.width=`${x}`,p.style.height=`${S}`,o.backgroundColor&&(w.fillStyle=o.backgroundColor,w.fillRect(0,0,p.width,p.height)),w.drawImage(c,0,0,p.width,p.height),p}async function toPixelData(n,o={}){const{width:a,height:i}=getImageSize(n,o);return(await toCanvas(n,o)).getContext("2d").getImageData(0,0,a,i).data}async function toPng(n,o={}){return(await toCanvas(n,o)).toDataURL()}async function toJpeg(n,o={}){return(await toCanvas(n,o)).toDataURL("image/jpeg",o.quality||1)}async function toBlob(n,o={}){const a=await toCanvas(n,o),i=await function canvasToBlob(n,o={}){return n.toBlob?new Promise((a=>{n.toBlob(a,o.type?o.type:"image/png",o.quality?o.quality:1)})):new Promise((a=>{const i=window.atob(n.toDataURL(o.type?o.type:void 0,o.quality?o.quality:void 0).split(",")[1]),l=i.length,u=new Uint8Array(l);for(let n=0;n<l;n+=1)u[n]=i.charCodeAt(n);a(new Blob([u],{type:o.type?o.type:"image/png"}))}))}(a);return i}async function getFontEmbedCSS(n,o={}){return getWebFontCSS(n,o)}},58772:(n,o,a)=>{"use strict";var i=a(90331);function emptyFunction(){}function emptyFunctionWithReset(){}emptyFunctionWithReset.resetWarningCache=emptyFunction,n.exports=function(){function shim(n,o,a,l,u,c){if(c!==i){var p=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw p.name="Invariant Violation",p}}function getShim(){return shim}shim.isRequired=shim;var n={array:shim,bigint:shim,bool:shim,func:shim,number:shim,object:shim,string:shim,symbol:shim,any:shim,arrayOf:getShim,element:shim,elementType:shim,instanceOf:getShim,node:shim,objectOf:getShim,oneOf:getShim,oneOfType:getShim,shape:getShim,exact:getShim,checkPropTypes:emptyFunctionWithReset,resetWarningCache:emptyFunction};return n.PropTypes=n,n}},23615:(n,o,a)=>{n.exports=a(58772)()},90331:n=>{"use strict";n.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},37634:(n,o,a)=>{"use strict";var i=a(61533);o.createRoot=i.createRoot,o.hydrateRoot=i.hydrateRoot},55322:(n,o,a)=>{"use strict";function _typeof(n){return _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},_typeof(n)}Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"DraggableCore",{enumerable:!0,get:function get(){return x.default}}),o.default=void 0;var i=function _interopRequireWildcard(n,o){if(!o&&n&&n.__esModule)return n;if(null===n||"object"!==_typeof(n)&&"function"!=typeof n)return{default:n};var a=_getRequireWildcardCache(o);if(a&&a.has(n))return a.get(n);var i={},l=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var u in n)if("default"!==u&&Object.prototype.hasOwnProperty.call(n,u)){var c=l?Object.getOwnPropertyDescriptor(n,u):null;c&&(c.get||c.set)?Object.defineProperty(i,u,c):i[u]=n[u]}i.default=n,a&&a.set(n,i);return i}(a(87363)),l=_interopRequireDefault(a(23615)),u=_interopRequireDefault(a(61533)),c=_interopRequireDefault(a(38944)),p=a(88065),w=a(39237),C=a(70972),x=_interopRequireDefault(a(50700)),S=_interopRequireDefault(a(73936)),O=["axis","bounds","children","defaultPosition","defaultClassName","defaultClassNameDragging","defaultClassNameDragged","position","positionOffset","scale"];function _interopRequireDefault(n){return n&&n.__esModule?n:{default:n}}function _getRequireWildcardCache(n){if("function"!=typeof WeakMap)return null;var o=new WeakMap,a=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(n){return n?a:o})(n)}function _extends(){return _extends=Object.assign||function(n){for(var o=1;o<arguments.length;o++){var a=arguments[o];for(var i in a)Object.prototype.hasOwnProperty.call(a,i)&&(n[i]=a[i])}return n},_extends.apply(this,arguments)}function _objectWithoutProperties(n,o){if(null==n)return{};var a,i,l=function _objectWithoutPropertiesLoose(n,o){if(null==n)return{};var a,i,l={},u=Object.keys(n);for(i=0;i<u.length;i++)a=u[i],o.indexOf(a)>=0||(l[a]=n[a]);return l}(n,o);if(Object.getOwnPropertySymbols){var u=Object.getOwnPropertySymbols(n);for(i=0;i<u.length;i++)a=u[i],o.indexOf(a)>=0||Object.prototype.propertyIsEnumerable.call(n,a)&&(l[a]=n[a])}return l}function ownKeys(n,o){var a=Object.keys(n);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(n);o&&(i=i.filter((function(o){return Object.getOwnPropertyDescriptor(n,o).enumerable}))),a.push.apply(a,i)}return a}function _objectSpread(n){for(var o=1;o<arguments.length;o++){var a=null!=arguments[o]?arguments[o]:{};o%2?ownKeys(Object(a),!0).forEach((function(o){_defineProperty(n,o,a[o])})):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(a)):ownKeys(Object(a)).forEach((function(o){Object.defineProperty(n,o,Object.getOwnPropertyDescriptor(a,o))}))}return n}function _slicedToArray(n,o){return function _arrayWithHoles(n){if(Array.isArray(n))return n}(n)||function _iterableToArrayLimit(n,o){var a=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null==a)return;var i,l,u=[],c=!0,p=!1;try{for(a=a.call(n);!(c=(i=a.next()).done)&&(u.push(i.value),!o||u.length!==o);c=!0);}catch(n){p=!0,l=n}finally{try{c||null==a.return||a.return()}finally{if(p)throw l}}return u}(n,o)||function _unsupportedIterableToArray(n,o){if(!n)return;if("string"==typeof n)return _arrayLikeToArray(n,o);var a=Object.prototype.toString.call(n).slice(8,-1);"Object"===a&&n.constructor&&(a=n.constructor.name);if("Map"===a||"Set"===a)return Array.from(n);if("Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a))return _arrayLikeToArray(n,o)}(n,o)||function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _arrayLikeToArray(n,o){(null==o||o>n.length)&&(o=n.length);for(var a=0,i=new Array(o);a<o;a++)i[a]=n[a];return i}function _defineProperties(n,o){for(var a=0;a<o.length;a++){var i=o[a];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(n,i.key,i)}}function _setPrototypeOf(n,o){return _setPrototypeOf=Object.setPrototypeOf||function _setPrototypeOf(n,o){return n.__proto__=o,n},_setPrototypeOf(n,o)}function _createSuper(n){var o=function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(n){return!1}}();return function _createSuperInternal(){var a,i=_getPrototypeOf(n);if(o){var l=_getPrototypeOf(this).constructor;a=Reflect.construct(i,arguments,l)}else a=i.apply(this,arguments);return function _possibleConstructorReturn(n,o){if(o&&("object"===_typeof(o)||"function"==typeof o))return o;if(void 0!==o)throw new TypeError("Derived constructors may only return object or undefined");return _assertThisInitialized(n)}(this,a)}}function _assertThisInitialized(n){if(void 0===n)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return n}function _getPrototypeOf(n){return _getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf:function _getPrototypeOf(n){return n.__proto__||Object.getPrototypeOf(n)},_getPrototypeOf(n)}function _defineProperty(n,o,a){return o in n?Object.defineProperty(n,o,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[o]=a,n}var j=function(n){!function _inherits(n,o){if("function"!=typeof o&&null!==o)throw new TypeError("Super expression must either be null or a function");n.prototype=Object.create(o&&o.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),o&&_setPrototypeOf(n,o)}(Draggable,n);var o=_createSuper(Draggable);function Draggable(n){var a;return function _classCallCheck(n,o){if(!(n instanceof o))throw new TypeError("Cannot call a class as a function")}(this,Draggable),_defineProperty(_assertThisInitialized(a=o.call(this,n)),"onDragStart",(function(n,o){if((0,S.default)("Draggable: onDragStart: %j",o),!1===a.props.onStart(n,(0,w.createDraggableData)(_assertThisInitialized(a),o)))return!1;a.setState({dragging:!0,dragged:!0})})),_defineProperty(_assertThisInitialized(a),"onDrag",(function(n,o){if(!a.state.dragging)return!1;(0,S.default)("Draggable: onDrag: %j",o);var i=(0,w.createDraggableData)(_assertThisInitialized(a),o),l={x:i.x,y:i.y};if(a.props.bounds){var u=l.x,c=l.y;l.x+=a.state.slackX,l.y+=a.state.slackY;var p=_slicedToArray((0,w.getBoundPosition)(_assertThisInitialized(a),l.x,l.y),2),C=p[0],x=p[1];l.x=C,l.y=x,l.slackX=a.state.slackX+(u-l.x),l.slackY=a.state.slackY+(c-l.y),i.x=l.x,i.y=l.y,i.deltaX=l.x-a.state.x,i.deltaY=l.y-a.state.y}if(!1===a.props.onDrag(n,i))return!1;a.setState(l)})),_defineProperty(_assertThisInitialized(a),"onDragStop",(function(n,o){if(!a.state.dragging)return!1;if(!1===a.props.onStop(n,(0,w.createDraggableData)(_assertThisInitialized(a),o)))return!1;(0,S.default)("Draggable: onDragStop: %j",o);var i={dragging:!1,slackX:0,slackY:0};if(Boolean(a.props.position)){var l=a.props.position,u=l.x,c=l.y;i.x=u,i.y=c}a.setState(i)})),a.state={dragging:!1,dragged:!1,x:n.position?n.position.x:n.defaultPosition.x,y:n.position?n.position.y:n.defaultPosition.y,prevPropsPosition:_objectSpread({},n.position),slackX:0,slackY:0,isElementSVG:!1},!n.position||n.onDrag||n.onStop||console.warn("A `position` was applied to this <Draggable>, without drag handlers. This will make this component effectively undraggable. Please attach `onDrag` or `onStop` handlers so you can adjust the `position` of this element."),a}return function _createClass(n,o,a){return o&&_defineProperties(n.prototype,o),a&&_defineProperties(n,a),Object.defineProperty(n,"prototype",{writable:!1}),n}(Draggable,[{key:"componentDidMount",value:function componentDidMount(){void 0!==window.SVGElement&&this.findDOMNode()instanceof window.SVGElement&&this.setState({isElementSVG:!0})}},{key:"componentWillUnmount",value:function componentWillUnmount(){this.setState({dragging:!1})}},{key:"findDOMNode",value:function findDOMNode(){var n,o,a;return null!==(n=null===(o=this.props)||void 0===o||null===(a=o.nodeRef)||void 0===a?void 0:a.current)&&void 0!==n?n:u.default.findDOMNode(this)}},{key:"render",value:function render(){var n,o=this.props,a=(o.axis,o.bounds,o.children),l=o.defaultPosition,u=o.defaultClassName,C=o.defaultClassNameDragging,S=o.defaultClassNameDragged,j=o.position,k=o.positionOffset,R=(o.scale,_objectWithoutProperties(o,O)),I={},W=null,N=!Boolean(j)||this.state.dragging,$=j||l,G={x:(0,w.canDragX)(this)&&N?this.state.x:$.x,y:(0,w.canDragY)(this)&&N?this.state.y:$.y};this.state.isElementSVG?W=(0,p.createSVGTransform)(G,k):I=(0,p.createCSSTransform)(G,k);var J=(0,c.default)(a.props.className||"",u,(_defineProperty(n={},C,this.state.dragging),_defineProperty(n,S,this.state.dragged),n));return i.createElement(x.default,_extends({},R,{onStart:this.onDragStart,onDrag:this.onDrag,onStop:this.onDragStop}),i.cloneElement(i.Children.only(a),{className:J,style:_objectSpread(_objectSpread({},a.props.style),I),transform:W}))}}],[{key:"getDerivedStateFromProps",value:function getDerivedStateFromProps(n,o){var a=n.position,i=o.prevPropsPosition;return!a||i&&a.x===i.x&&a.y===i.y?null:((0,S.default)("Draggable: getDerivedStateFromProps %j",{position:a,prevPropsPosition:i}),{x:a.x,y:a.y,prevPropsPosition:_objectSpread({},a)})}}]),Draggable}(i.Component);o.default=j,_defineProperty(j,"displayName","Draggable"),_defineProperty(j,"propTypes",_objectSpread(_objectSpread({},x.default.propTypes),{},{axis:l.default.oneOf(["both","x","y","none"]),bounds:l.default.oneOfType([l.default.shape({left:l.default.number,right:l.default.number,top:l.default.number,bottom:l.default.number}),l.default.string,l.default.oneOf([!1])]),defaultClassName:l.default.string,defaultClassNameDragging:l.default.string,defaultClassNameDragged:l.default.string,defaultPosition:l.default.shape({x:l.default.number,y:l.default.number}),positionOffset:l.default.shape({x:l.default.oneOfType([l.default.number,l.default.string]),y:l.default.oneOfType([l.default.number,l.default.string])}),position:l.default.shape({x:l.default.number,y:l.default.number}),className:C.dontSetMe,style:C.dontSetMe,transform:C.dontSetMe})),_defineProperty(j,"defaultProps",_objectSpread(_objectSpread({},x.default.defaultProps),{},{axis:"both",bounds:!1,defaultClassName:"react-draggable",defaultClassNameDragging:"react-draggable-dragging",defaultClassNameDragged:"react-draggable-dragged",defaultPosition:{x:0,y:0},scale:1}))},50700:(n,o,a)=>{"use strict";function _typeof(n){return _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},_typeof(n)}Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var i=function _interopRequireWildcard(n,o){if(!o&&n&&n.__esModule)return n;if(null===n||"object"!==_typeof(n)&&"function"!=typeof n)return{default:n};var a=_getRequireWildcardCache(o);if(a&&a.has(n))return a.get(n);var i={},l=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var u in n)if("default"!==u&&Object.prototype.hasOwnProperty.call(n,u)){var c=l?Object.getOwnPropertyDescriptor(n,u):null;c&&(c.get||c.set)?Object.defineProperty(i,u,c):i[u]=n[u]}i.default=n,a&&a.set(n,i);return i}(a(87363)),l=_interopRequireDefault(a(23615)),u=_interopRequireDefault(a(61533)),c=a(88065),p=a(39237),w=a(70972),C=_interopRequireDefault(a(73936));function _interopRequireDefault(n){return n&&n.__esModule?n:{default:n}}function _getRequireWildcardCache(n){if("function"!=typeof WeakMap)return null;var o=new WeakMap,a=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(n){return n?a:o})(n)}function _slicedToArray(n,o){return function _arrayWithHoles(n){if(Array.isArray(n))return n}(n)||function _iterableToArrayLimit(n,o){var a=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null==a)return;var i,l,u=[],c=!0,p=!1;try{for(a=a.call(n);!(c=(i=a.next()).done)&&(u.push(i.value),!o||u.length!==o);c=!0);}catch(n){p=!0,l=n}finally{try{c||null==a.return||a.return()}finally{if(p)throw l}}return u}(n,o)||function _unsupportedIterableToArray(n,o){if(!n)return;if("string"==typeof n)return _arrayLikeToArray(n,o);var a=Object.prototype.toString.call(n).slice(8,-1);"Object"===a&&n.constructor&&(a=n.constructor.name);if("Map"===a||"Set"===a)return Array.from(n);if("Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a))return _arrayLikeToArray(n,o)}(n,o)||function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _arrayLikeToArray(n,o){(null==o||o>n.length)&&(o=n.length);for(var a=0,i=new Array(o);a<o;a++)i[a]=n[a];return i}function _defineProperties(n,o){for(var a=0;a<o.length;a++){var i=o[a];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(n,i.key,i)}}function _setPrototypeOf(n,o){return _setPrototypeOf=Object.setPrototypeOf||function _setPrototypeOf(n,o){return n.__proto__=o,n},_setPrototypeOf(n,o)}function _createSuper(n){var o=function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(n){return!1}}();return function _createSuperInternal(){var a,i=_getPrototypeOf(n);if(o){var l=_getPrototypeOf(this).constructor;a=Reflect.construct(i,arguments,l)}else a=i.apply(this,arguments);return function _possibleConstructorReturn(n,o){if(o&&("object"===_typeof(o)||"function"==typeof o))return o;if(void 0!==o)throw new TypeError("Derived constructors may only return object or undefined");return _assertThisInitialized(n)}(this,a)}}function _assertThisInitialized(n){if(void 0===n)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return n}function _getPrototypeOf(n){return _getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf:function _getPrototypeOf(n){return n.__proto__||Object.getPrototypeOf(n)},_getPrototypeOf(n)}function _defineProperty(n,o,a){return o in n?Object.defineProperty(n,o,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[o]=a,n}var x={start:"touchstart",move:"touchmove",stop:"touchend"},S={start:"mousedown",move:"mousemove",stop:"mouseup"},O=S,j=function(n){!function _inherits(n,o){if("function"!=typeof o&&null!==o)throw new TypeError("Super expression must either be null or a function");n.prototype=Object.create(o&&o.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),o&&_setPrototypeOf(n,o)}(DraggableCore,n);var o=_createSuper(DraggableCore);function DraggableCore(){var n;!function _classCallCheck(n,o){if(!(n instanceof o))throw new TypeError("Cannot call a class as a function")}(this,DraggableCore);for(var a=arguments.length,i=new Array(a),l=0;l<a;l++)i[l]=arguments[l];return _defineProperty(_assertThisInitialized(n=o.call.apply(o,[this].concat(i))),"state",{dragging:!1,lastX:NaN,lastY:NaN,touchIdentifier:null}),_defineProperty(_assertThisInitialized(n),"mounted",!1),_defineProperty(_assertThisInitialized(n),"handleDragStart",(function(o){if(n.props.onMouseDown(o),!n.props.allowAnyClick&&"number"==typeof o.button&&0!==o.button)return!1;var a=n.findDOMNode();if(!a||!a.ownerDocument||!a.ownerDocument.body)throw new Error("<DraggableCore> not mounted on DragStart!");var i=a.ownerDocument;if(!(n.props.disabled||!(o.target instanceof i.defaultView.Node)||n.props.handle&&!(0,c.matchesSelectorAndParentsTo)(o.target,n.props.handle,a)||n.props.cancel&&(0,c.matchesSelectorAndParentsTo)(o.target,n.props.cancel,a))){"touchstart"===o.type&&o.preventDefault();var l=(0,c.getTouchIdentifier)(o);n.setState({touchIdentifier:l});var u=(0,p.getControlPosition)(o,l,_assertThisInitialized(n));if(null!=u){var w=u.x,x=u.y,S=(0,p.createCoreData)(_assertThisInitialized(n),w,x);(0,C.default)("DraggableCore: handleDragStart: %j",S),(0,C.default)("calling",n.props.onStart),!1!==n.props.onStart(o,S)&&!1!==n.mounted&&(n.props.enableUserSelectHack&&(0,c.addUserSelectStyles)(i),n.setState({dragging:!0,lastX:w,lastY:x}),(0,c.addEvent)(i,O.move,n.handleDrag),(0,c.addEvent)(i,O.stop,n.handleDragStop))}}})),_defineProperty(_assertThisInitialized(n),"handleDrag",(function(o){var a=(0,p.getControlPosition)(o,n.state.touchIdentifier,_assertThisInitialized(n));if(null!=a){var i=a.x,l=a.y;if(Array.isArray(n.props.grid)){var u=i-n.state.lastX,c=l-n.state.lastY,w=_slicedToArray((0,p.snapToGrid)(n.props.grid,u,c),2);if(u=w[0],c=w[1],!u&&!c)return;i=n.state.lastX+u,l=n.state.lastY+c}var x=(0,p.createCoreData)(_assertThisInitialized(n),i,l);if((0,C.default)("DraggableCore: handleDrag: %j",x),!1!==n.props.onDrag(o,x)&&!1!==n.mounted)n.setState({lastX:i,lastY:l});else try{n.handleDragStop(new MouseEvent("mouseup"))}catch(o){var S=document.createEvent("MouseEvents");S.initMouseEvent("mouseup",!0,!0,window,0,0,0,0,0,!1,!1,!1,!1,0,null),n.handleDragStop(S)}}})),_defineProperty(_assertThisInitialized(n),"handleDragStop",(function(o){if(n.state.dragging){var a=(0,p.getControlPosition)(o,n.state.touchIdentifier,_assertThisInitialized(n));if(null!=a){var i=a.x,l=a.y;if(Array.isArray(n.props.grid)){var u=i-n.state.lastX||0,w=l-n.state.lastY||0,x=_slicedToArray((0,p.snapToGrid)(n.props.grid,u,w),2);u=x[0],w=x[1],i=n.state.lastX+u,l=n.state.lastY+w}var S=(0,p.createCoreData)(_assertThisInitialized(n),i,l);if(!1===n.props.onStop(o,S)||!1===n.mounted)return!1;var j=n.findDOMNode();j&&n.props.enableUserSelectHack&&(0,c.removeUserSelectStyles)(j.ownerDocument),(0,C.default)("DraggableCore: handleDragStop: %j",S),n.setState({dragging:!1,lastX:NaN,lastY:NaN}),j&&((0,C.default)("DraggableCore: Removing handlers"),(0,c.removeEvent)(j.ownerDocument,O.move,n.handleDrag),(0,c.removeEvent)(j.ownerDocument,O.stop,n.handleDragStop))}}})),_defineProperty(_assertThisInitialized(n),"onMouseDown",(function(o){return O=S,n.handleDragStart(o)})),_defineProperty(_assertThisInitialized(n),"onMouseUp",(function(o){return O=S,n.handleDragStop(o)})),_defineProperty(_assertThisInitialized(n),"onTouchStart",(function(o){return O=x,n.handleDragStart(o)})),_defineProperty(_assertThisInitialized(n),"onTouchEnd",(function(o){return O=x,n.handleDragStop(o)})),n}return function _createClass(n,o,a){return o&&_defineProperties(n.prototype,o),a&&_defineProperties(n,a),Object.defineProperty(n,"prototype",{writable:!1}),n}(DraggableCore,[{key:"componentDidMount",value:function componentDidMount(){this.mounted=!0;var n=this.findDOMNode();n&&(0,c.addEvent)(n,x.start,this.onTouchStart,{passive:!1})}},{key:"componentWillUnmount",value:function componentWillUnmount(){this.mounted=!1;var n=this.findDOMNode();if(n){var o=n.ownerDocument;(0,c.removeEvent)(o,S.move,this.handleDrag),(0,c.removeEvent)(o,x.move,this.handleDrag),(0,c.removeEvent)(o,S.stop,this.handleDragStop),(0,c.removeEvent)(o,x.stop,this.handleDragStop),(0,c.removeEvent)(n,x.start,this.onTouchStart,{passive:!1}),this.props.enableUserSelectHack&&(0,c.removeUserSelectStyles)(o)}}},{key:"findDOMNode",value:function findDOMNode(){var n,o,a;return null!==(n=this.props)&&void 0!==n&&n.nodeRef?null===(o=this.props)||void 0===o||null===(a=o.nodeRef)||void 0===a?void 0:a.current:u.default.findDOMNode(this)}},{key:"render",value:function render(){return i.cloneElement(i.Children.only(this.props.children),{onMouseDown:this.onMouseDown,onMouseUp:this.onMouseUp,onTouchEnd:this.onTouchEnd})}}]),DraggableCore}(i.Component);o.default=j,_defineProperty(j,"displayName","DraggableCore"),_defineProperty(j,"propTypes",{allowAnyClick:l.default.bool,disabled:l.default.bool,enableUserSelectHack:l.default.bool,offsetParent:function offsetParent(n,o){if(n[o]&&1!==n[o].nodeType)throw new Error("Draggable's offsetParent must be a DOM Node.")},grid:l.default.arrayOf(l.default.number),handle:l.default.string,cancel:l.default.string,nodeRef:l.default.object,onStart:l.default.func,onDrag:l.default.func,onStop:l.default.func,onMouseDown:l.default.func,scale:l.default.number,className:w.dontSetMe,style:w.dontSetMe,transform:w.dontSetMe}),_defineProperty(j,"defaultProps",{allowAnyClick:!1,disabled:!1,enableUserSelectHack:!0,onStart:function onStart(){},onDrag:function onDrag(){},onStop:function onStop(){},onMouseDown:function onMouseDown(){},scale:1})},49529:(n,o,a)=>{"use strict";var i=a(55322),l=i.default,u=i.DraggableCore;n.exports=l,n.exports.default=l,n.exports.DraggableCore=u},88065:(n,o,a)=>{"use strict";function _typeof(n){return _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},_typeof(n)}Object.defineProperty(o,"__esModule",{value:!0}),o.addClassName=addClassName,o.addEvent=function addEvent(n,o,a,i){if(!n)return;var l=_objectSpread({capture:!0},i);n.addEventListener?n.addEventListener(o,a,l):n.attachEvent?n.attachEvent("on"+o,a):n["on"+o]=a},o.addUserSelectStyles=function addUserSelectStyles(n){if(!n)return;var o=n.getElementById("react-draggable-style-el");o||((o=n.createElement("style")).type="text/css",o.id="react-draggable-style-el",o.innerHTML=".react-draggable-transparent-selection *::-moz-selection {all: inherit;}\n",o.innerHTML+=".react-draggable-transparent-selection *::selection {all: inherit;}\n",n.getElementsByTagName("head")[0].appendChild(o));n.body&&addClassName(n.body,"react-draggable-transparent-selection")},o.createCSSTransform=function createCSSTransform(n,o){var a=getTranslation(n,o,"px");return _defineProperty({},(0,l.browserPrefixToKey)("transform",l.default),a)},o.createSVGTransform=function createSVGTransform(n,o){return getTranslation(n,o,"")},o.getTouch=function getTouch(n,o){return n.targetTouches&&(0,i.findInArray)(n.targetTouches,(function(n){return o===n.identifier}))||n.changedTouches&&(0,i.findInArray)(n.changedTouches,(function(n){return o===n.identifier}))},o.getTouchIdentifier=function getTouchIdentifier(n){if(n.targetTouches&&n.targetTouches[0])return n.targetTouches[0].identifier;if(n.changedTouches&&n.changedTouches[0])return n.changedTouches[0].identifier},o.getTranslation=getTranslation,o.innerHeight=function innerHeight(n){var o=n.clientHeight,a=n.ownerDocument.defaultView.getComputedStyle(n);return o-=(0,i.int)(a.paddingTop),o-=(0,i.int)(a.paddingBottom)},o.innerWidth=function innerWidth(n){var o=n.clientWidth,a=n.ownerDocument.defaultView.getComputedStyle(n);return o-=(0,i.int)(a.paddingLeft),o-=(0,i.int)(a.paddingRight)},o.matchesSelector=matchesSelector,o.matchesSelectorAndParentsTo=function matchesSelectorAndParentsTo(n,o,a){var i=n;do{if(matchesSelector(i,o))return!0;if(i===a)return!1;i=i.parentNode}while(i);return!1},o.offsetXYFromParent=function offsetXYFromParent(n,o,a){var i=o===o.ownerDocument.body?{left:0,top:0}:o.getBoundingClientRect(),l=(n.clientX+o.scrollLeft-i.left)/a,u=(n.clientY+o.scrollTop-i.top)/a;return{x:l,y:u}},o.outerHeight=function outerHeight(n){var o=n.clientHeight,a=n.ownerDocument.defaultView.getComputedStyle(n);return o+=(0,i.int)(a.borderTopWidth),o+=(0,i.int)(a.borderBottomWidth)},o.outerWidth=function outerWidth(n){var o=n.clientWidth,a=n.ownerDocument.defaultView.getComputedStyle(n);return o+=(0,i.int)(a.borderLeftWidth),o+=(0,i.int)(a.borderRightWidth)},o.removeClassName=removeClassName,o.removeEvent=function removeEvent(n,o,a,i){if(!n)return;var l=_objectSpread({capture:!0},i);n.removeEventListener?n.removeEventListener(o,a,l):n.detachEvent?n.detachEvent("on"+o,a):n["on"+o]=null},o.removeUserSelectStyles=function removeUserSelectStyles(n){if(!n)return;try{if(n.body&&removeClassName(n.body,"react-draggable-transparent-selection"),n.selection)n.selection.empty();else{var o=(n.defaultView||window).getSelection();o&&"Caret"!==o.type&&o.removeAllRanges()}}catch(n){}};var i=a(70972),l=function _interopRequireWildcard(n,o){if(!o&&n&&n.__esModule)return n;if(null===n||"object"!==_typeof(n)&&"function"!=typeof n)return{default:n};var a=_getRequireWildcardCache(o);if(a&&a.has(n))return a.get(n);var i={},l=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var u in n)if("default"!==u&&Object.prototype.hasOwnProperty.call(n,u)){var c=l?Object.getOwnPropertyDescriptor(n,u):null;c&&(c.get||c.set)?Object.defineProperty(i,u,c):i[u]=n[u]}i.default=n,a&&a.set(n,i);return i}(a(42249));function _getRequireWildcardCache(n){if("function"!=typeof WeakMap)return null;var o=new WeakMap,a=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(n){return n?a:o})(n)}function ownKeys(n,o){var a=Object.keys(n);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(n);o&&(i=i.filter((function(o){return Object.getOwnPropertyDescriptor(n,o).enumerable}))),a.push.apply(a,i)}return a}function _objectSpread(n){for(var o=1;o<arguments.length;o++){var a=null!=arguments[o]?arguments[o]:{};o%2?ownKeys(Object(a),!0).forEach((function(o){_defineProperty(n,o,a[o])})):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(a)):ownKeys(Object(a)).forEach((function(o){Object.defineProperty(n,o,Object.getOwnPropertyDescriptor(a,o))}))}return n}function _defineProperty(n,o,a){return o in n?Object.defineProperty(n,o,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[o]=a,n}var u="";function matchesSelector(n,o){return u||(u=(0,i.findInArray)(["matches","webkitMatchesSelector","mozMatchesSelector","msMatchesSelector","oMatchesSelector"],(function(o){return(0,i.isFunction)(n[o])}))),!!(0,i.isFunction)(n[u])&&n[u](o)}function getTranslation(n,o,a){var i=n.x,l=n.y,u="translate(".concat(i).concat(a,",").concat(l).concat(a,")");if(o){var c="".concat("string"==typeof o.x?o.x:o.x+a),p="".concat("string"==typeof o.y?o.y:o.y+a);u="translate(".concat(c,", ").concat(p,")")+u}return u}function addClassName(n,o){n.classList?n.classList.add(o):n.className.match(new RegExp("(?:^|\\s)".concat(o,"(?!\\S)")))||(n.className+=" ".concat(o))}function removeClassName(n,o){n.classList?n.classList.remove(o):n.className=n.className.replace(new RegExp("(?:^|\\s)".concat(o,"(?!\\S)"),"g"),"")}},42249:(n,o)=>{"use strict";Object.defineProperty(o,"__esModule",{value:!0}),o.browserPrefixToKey=browserPrefixToKey,o.browserPrefixToStyle=function browserPrefixToStyle(n,o){return o?"-".concat(o.toLowerCase(),"-").concat(n):n},o.default=void 0,o.getPrefix=getPrefix;var a=["Moz","Webkit","O","ms"];function getPrefix(){var n,o,i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"transform";if("undefined"==typeof window)return"";var l=null===(n=window.document)||void 0===n||null===(o=n.documentElement)||void 0===o?void 0:o.style;if(!l)return"";if(i in l)return"";for(var u=0;u<a.length;u++)if(browserPrefixToKey(i,a[u])in l)return a[u];return""}function browserPrefixToKey(n,o){return o?"".concat(o).concat(function kebabToTitleCase(n){for(var o="",a=!0,i=0;i<n.length;i++)a?(o+=n[i].toUpperCase(),a=!1):"-"===n[i]?a=!0:o+=n[i];return o}(n)):n}var i=getPrefix();o.default=i},73936:(n,o)=>{"use strict";Object.defineProperty(o,"__esModule",{value:!0}),o.default=function log(){0}},39237:(n,o,a)=>{"use strict";Object.defineProperty(o,"__esModule",{value:!0}),o.canDragX=function canDragX(n){return"both"===n.props.axis||"x"===n.props.axis},o.canDragY=function canDragY(n){return"both"===n.props.axis||"y"===n.props.axis},o.createCoreData=function createCoreData(n,o,a){var l=n.state,u=!(0,i.isNum)(l.lastX),c=findDOMNode(n);return u?{node:c,deltaX:0,deltaY:0,lastX:o,lastY:a,x:o,y:a}:{node:c,deltaX:o-l.lastX,deltaY:a-l.lastY,lastX:l.lastX,lastY:l.lastY,x:o,y:a}},o.createDraggableData=function createDraggableData(n,o){var a=n.props.scale;return{node:o.node,x:n.state.x+o.deltaX/a,y:n.state.y+o.deltaY/a,deltaX:o.deltaX/a,deltaY:o.deltaY/a,lastX:n.state.x,lastY:n.state.y}},o.getBoundPosition=function getBoundPosition(n,o,a){if(!n.props.bounds)return[o,a];var u=n.props.bounds;u="string"==typeof u?u:function cloneBounds(n){return{left:n.left,top:n.top,right:n.right,bottom:n.bottom}}(u);var c=findDOMNode(n);if("string"==typeof u){var p,w=c.ownerDocument,C=w.defaultView;if(!((p="parent"===u?c.parentNode:w.querySelector(u))instanceof C.HTMLElement))throw new Error('Bounds selector "'+u+'" could not find an element.');var x=p,S=C.getComputedStyle(c),O=C.getComputedStyle(x);u={left:-c.offsetLeft+(0,i.int)(O.paddingLeft)+(0,i.int)(S.marginLeft),top:-c.offsetTop+(0,i.int)(O.paddingTop)+(0,i.int)(S.marginTop),right:(0,l.innerWidth)(x)-(0,l.outerWidth)(c)-c.offsetLeft+(0,i.int)(O.paddingRight)-(0,i.int)(S.marginRight),bottom:(0,l.innerHeight)(x)-(0,l.outerHeight)(c)-c.offsetTop+(0,i.int)(O.paddingBottom)-(0,i.int)(S.marginBottom)}}(0,i.isNum)(u.right)&&(o=Math.min(o,u.right));(0,i.isNum)(u.bottom)&&(a=Math.min(a,u.bottom));(0,i.isNum)(u.left)&&(o=Math.max(o,u.left));(0,i.isNum)(u.top)&&(a=Math.max(a,u.top));return[o,a]},o.getControlPosition=function getControlPosition(n,o,a){var i="number"==typeof o?(0,l.getTouch)(n,o):null;if("number"==typeof o&&!i)return null;var u=findDOMNode(a),c=a.props.offsetParent||u.offsetParent||u.ownerDocument.body;return(0,l.offsetXYFromParent)(i||n,c,a.props.scale)},o.snapToGrid=function snapToGrid(n,o,a){var i=Math.round(o/n[0])*n[0],l=Math.round(a/n[1])*n[1];return[i,l]};var i=a(70972),l=a(88065);function findDOMNode(n){var o=n.findDOMNode();if(!o)throw new Error("<DraggableCore>: Unmounted during event!");return o}},70972:(n,o)=>{"use strict";Object.defineProperty(o,"__esModule",{value:!0}),o.dontSetMe=function dontSetMe(n,o,a){if(n[o])return new Error("Invalid prop ".concat(o," passed to ").concat(a," - do not set this, set it on the child."))},o.findInArray=function findInArray(n,o){for(var a=0,i=n.length;a<i;a++)if(o.apply(o,[n[a],a,n]))return n[a]},o.int=function int(n){return parseInt(n,10)},o.isFunction=function isFunction(n){return"function"==typeof n||"[object Function]"===Object.prototype.toString.call(n)},o.isNum=function isNum(n){return"number"==typeof n&&!isNaN(n)}},58702:(n,o)=>{"use strict";var a,i=Symbol.for("react.element"),l=Symbol.for("react.portal"),u=Symbol.for("react.fragment"),c=Symbol.for("react.strict_mode"),p=Symbol.for("react.profiler"),w=Symbol.for("react.provider"),C=Symbol.for("react.context"),x=Symbol.for("react.server_context"),S=Symbol.for("react.forward_ref"),O=Symbol.for("react.suspense"),j=Symbol.for("react.suspense_list"),k=Symbol.for("react.memo"),R=Symbol.for("react.lazy"),I=Symbol.for("react.offscreen");function v(n){if("object"==typeof n&&null!==n){var o=n.$$typeof;switch(o){case i:switch(n=n.type){case u:case p:case c:case O:case j:return n;default:switch(n=n&&n.$$typeof){case x:case C:case S:case R:case k:case w:return n;default:return o}}case l:return o}}}a=Symbol.for("react.module.reference"),o.isValidElementType=function(n){return"string"==typeof n||"function"==typeof n||n===u||n===p||n===c||n===O||n===j||n===I||"object"==typeof n&&null!==n&&(n.$$typeof===R||n.$$typeof===k||n.$$typeof===w||n.$$typeof===C||n.$$typeof===S||n.$$typeof===a||void 0!==n.getModuleId)},o.typeOf=v},19185:(n,o,a)=>{"use strict";n.exports=a(58702)},74445:n=>{n.exports=function shallowEqual(n,o,a,i){var l=a?a.call(i,n,o):void 0;if(void 0!==l)return!!l;if(n===o)return!0;if("object"!=typeof n||!n||"object"!=typeof o||!o)return!1;var u=Object.keys(n),c=Object.keys(o);if(u.length!==c.length)return!1;for(var p=Object.prototype.hasOwnProperty.bind(o),w=0;w<u.length;w++){var C=u[w];if(!p(C))return!1;var x=n[C],S=o[C];if(!1===(l=a?a.call(i,x,S,C):void 0)||void 0===l&&x!==S)return!1}return!0}},63993:(n,o,a)=>{"use strict";a.r(o),a.d(o,{ServerStyleSheet:()=>at,StyleSheetConsumer:()=>Me,StyleSheetContext:()=>Ie,StyleSheetManager:()=>me,ThemeConsumer:()=>rt,ThemeContext:()=>tt,ThemeProvider:()=>Le,__PRIVATE__:()=>it,createGlobalStyle:()=>$e,css:()=>Ae,default:()=>lt,isStyledComponent:()=>_,keyframes:()=>We,useTheme:()=>Xe,version:()=>W,withTheme:()=>Je});var i=a(19185),l=a(87363),u=a.n(l),c=a(74445),p=a.n(c);const w=function stylis_min(n){function M(n,i,l,u,c){for(var p,S,j,k,R,I=0,W=0,oe=0,le=0,ce=0,se=0,de=j=p=0,he=0,ge=0,ye=0,ve=0,Ce=l.length,Pe=Ce-1,Se="",Re="",Ie="",Me="";he<Ce;){if(S=l.charCodeAt(he),he===Pe&&0!==W+le+oe+I&&(0!==W&&(S=47===W?10:47),le=oe=I=0,Ce++,Pe++),0===W+le+oe+I){if(he===Pe&&(0<ge&&(Se=Se.replace(a,"")),0<Se.trim().length)){switch(S){case 32:case 9:case 59:case 13:case 10:break;default:Se+=l.charAt(he)}S=59}switch(S){case 123:for(p=(Se=Se.trim()).charCodeAt(0),j=1,ve=++he;he<Ce;){switch(S=l.charCodeAt(he)){case 123:j++;break;case 125:j--;break;case 47:switch(S=l.charCodeAt(he+1)){case 42:case 47:e:{for(de=he+1;de<Pe;++de)switch(l.charCodeAt(de)){case 47:if(42===S&&42===l.charCodeAt(de-1)&&he+2!==de){he=de+1;break e}break;case 10:if(47===S){he=de+1;break e}}he=de}}break;case 91:S++;case 40:S++;case 34:case 39:for(;he++<Pe&&l.charCodeAt(he)!==S;);}if(0===j)break;he++}if(j=l.substring(ve,he),0===p&&(p=(Se=Se.replace(o,"").trim()).charCodeAt(0)),64===p){switch(0<ge&&(Se=Se.replace(a,"")),S=Se.charCodeAt(1)){case 100:case 109:case 115:case 45:ge=i;break;default:ge=re}if(ve=(j=M(i,ge,j,S,c+1)).length,0<ie&&(R=H(3,j,ge=X(re,Se,ye),i,$,N,ve,S,c,u),Se=ge.join(""),void 0!==R&&0===(ve=(j=R.trim()).length)&&(S=0,j="")),0<ve)switch(S){case 115:Se=Se.replace(O,ea);case 100:case 109:case 45:j=Se+"{"+j+"}";break;case 107:j=(Se=Se.replace(w,"$1 $2"))+"{"+j+"}",j=1===J||2===J&&L("@"+j,3)?"@-webkit-"+j+"@"+j:"@"+j;break;default:j=Se+j,112===u&&(Re+=j,j="")}else j=""}else j=M(i,X(i,Se,ye),j,u,c+1);Ie+=j,j=ye=ge=de=p=0,Se="",S=l.charCodeAt(++he);break;case 125:case 59:if(1<(ve=(Se=(0<ge?Se.replace(a,""):Se).trim()).length))switch(0===de&&(p=Se.charCodeAt(0),45===p||96<p&&123>p)&&(ve=(Se=Se.replace(" ",":")).length),0<ie&&void 0!==(R=H(1,Se,i,n,$,N,Re.length,u,c,u))&&0===(ve=(Se=R.trim()).length)&&(Se="\0\0"),p=Se.charCodeAt(0),S=Se.charCodeAt(1),p){case 0:break;case 64:if(105===S||99===S){Me+=Se+l.charAt(he);break}default:58!==Se.charCodeAt(ve-1)&&(Re+=P(Se,p,S,Se.charCodeAt(2)))}ye=ge=de=p=0,Se="",S=l.charCodeAt(++he)}}switch(S){case 13:case 10:47===W?W=0:0===1+p&&107!==u&&0<Se.length&&(ge=1,Se+="\0"),0<ie*ue&&H(0,Se,i,n,$,N,Re.length,u,c,u),N=1,$++;break;case 59:case 125:if(0===W+le+oe+I){N++;break}default:switch(N++,k=l.charAt(he),S){case 9:case 32:if(0===le+I+W)switch(ce){case 44:case 58:case 9:case 32:k="";break;default:32!==S&&(k=" ")}break;case 0:k="\\0";break;case 12:k="\\f";break;case 11:k="\\v";break;case 38:0===le+W+I&&(ge=ye=1,k="\f"+k);break;case 108:if(0===le+W+I+G&&0<de)switch(he-de){case 2:112===ce&&58===l.charCodeAt(he-3)&&(G=ce);case 8:111===se&&(G=se)}break;case 58:0===le+W+I&&(de=he);break;case 44:0===W+oe+le+I&&(ge=1,k+="\r");break;case 34:case 39:0===W&&(le=le===S?0:0===le?S:le);break;case 91:0===le+W+oe&&I++;break;case 93:0===le+W+oe&&I--;break;case 41:0===le+W+I&&oe--;break;case 40:if(0===le+W+I){if(0===p)if(2*ce+3*se==533);else p=1;oe++}break;case 64:0===W+oe+le+I+de+j&&(j=1);break;case 42:case 47:if(!(0<le+I+oe))switch(W){case 0:switch(2*S+3*l.charCodeAt(he+1)){case 235:W=47;break;case 220:ve=he,W=42}break;case 42:47===S&&42===ce&&ve+2!==he&&(33===l.charCodeAt(ve+2)&&(Re+=l.substring(ve,he+1)),k="",W=0)}}0===W&&(Se+=k)}se=ce,ce=S,he++}if(0<(ve=Re.length)){if(ge=i,0<ie&&(void 0!==(R=H(2,Re,ge,n,$,N,ve,u,c,u))&&0===(Re=R).length))return Me+Re+Ie;if(Re=ge.join(",")+"{"+Re+"}",0!=J*G){switch(2!==J||L(Re,2)||(G=0),G){case 111:Re=Re.replace(x,":-moz-$1")+Re;break;case 112:Re=Re.replace(C,"::-webkit-input-$1")+Re.replace(C,"::-moz-$1")+Re.replace(C,":-ms-input-$1")+Re}G=0}}return Me+Re+Ie}function X(n,o,a){var i=o.trim().split(c);o=i;var l=i.length,u=n.length;switch(u){case 0:case 1:var p=0;for(n=0===u?"":n[0]+" ";p<l;++p)o[p]=Z(n,o[p],a).trim();break;default:var w=p=0;for(o=[];p<l;++p)for(var C=0;C<u;++C)o[w++]=Z(n[C]+" ",i[p],a).trim()}return o}function Z(n,o,a){var i=o.charCodeAt(0);switch(33>i&&(i=(o=o.trim()).charCodeAt(0)),i){case 38:return o.replace(p,"$1"+n.trim());case 58:return n.trim()+o.replace(p,"$1"+n.trim());default:if(0<1*a&&0<o.indexOf("\f"))return o.replace(p,(58===n.charCodeAt(0)?"":"$1")+n.trim())}return n+o}function P(n,o,a,c){var p=n+";",w=2*o+3*a+4*c;if(944===w){n=p.indexOf(":",9)+1;var C=p.substring(n,p.length-1).trim();return C=p.substring(0,n).trim()+C+";",1===J||2===J&&L(C,1)?"-webkit-"+C+C:C}if(0===J||2===J&&!L(p,1))return p;switch(w){case 1015:return 97===p.charCodeAt(10)?"-webkit-"+p+p:p;case 951:return 116===p.charCodeAt(3)?"-webkit-"+p+p:p;case 963:return 110===p.charCodeAt(5)?"-webkit-"+p+p:p;case 1009:if(100!==p.charCodeAt(4))break;case 969:case 942:return"-webkit-"+p+p;case 978:return"-webkit-"+p+"-moz-"+p+p;case 1019:case 983:return"-webkit-"+p+"-moz-"+p+"-ms-"+p+p;case 883:if(45===p.charCodeAt(8))return"-webkit-"+p+p;if(0<p.indexOf("image-set(",11))return p.replace(W,"$1-webkit-$2")+p;break;case 932:if(45===p.charCodeAt(4))switch(p.charCodeAt(5)){case 103:return"-webkit-box-"+p.replace("-grow","")+"-webkit-"+p+"-ms-"+p.replace("grow","positive")+p;case 115:return"-webkit-"+p+"-ms-"+p.replace("shrink","negative")+p;case 98:return"-webkit-"+p+"-ms-"+p.replace("basis","preferred-size")+p}return"-webkit-"+p+"-ms-"+p+p;case 964:return"-webkit-"+p+"-ms-flex-"+p+p;case 1023:if(99!==p.charCodeAt(8))break;return"-webkit-box-pack"+(C=p.substring(p.indexOf(":",15)).replace("flex-","").replace("space-between","justify"))+"-webkit-"+p+"-ms-flex-pack"+C+p;case 1005:return l.test(p)?p.replace(i,":-webkit-")+p.replace(i,":-moz-")+p:p;case 1e3:switch(o=(C=p.substring(13).trim()).indexOf("-")+1,C.charCodeAt(0)+C.charCodeAt(o)){case 226:C=p.replace(S,"tb");break;case 232:C=p.replace(S,"tb-rl");break;case 220:C=p.replace(S,"lr");break;default:return p}return"-webkit-"+p+"-ms-"+C+p;case 1017:if(-1===p.indexOf("sticky",9))break;case 975:switch(o=(p=n).length-10,w=(C=(33===p.charCodeAt(o)?p.substring(0,o):p).substring(n.indexOf(":",7)+1).trim()).charCodeAt(0)+(0|C.charCodeAt(7))){case 203:if(111>C.charCodeAt(8))break;case 115:p=p.replace(C,"-webkit-"+C)+";"+p;break;case 207:case 102:p=p.replace(C,"-webkit-"+(102<w?"inline-":"")+"box")+";"+p.replace(C,"-webkit-"+C)+";"+p.replace(C,"-ms-"+C+"box")+";"+p}return p+";";case 938:if(45===p.charCodeAt(5))switch(p.charCodeAt(6)){case 105:return C=p.replace("-items",""),"-webkit-"+p+"-webkit-box-"+C+"-ms-flex-"+C+p;case 115:return"-webkit-"+p+"-ms-flex-item-"+p.replace(k,"")+p;default:return"-webkit-"+p+"-ms-flex-line-pack"+p.replace("align-content","").replace(k,"")+p}break;case 973:case 989:if(45!==p.charCodeAt(3)||122===p.charCodeAt(4))break;case 931:case 953:if(!0===I.test(n))return 115===(C=n.substring(n.indexOf(":")+1)).charCodeAt(0)?P(n.replace("stretch","fill-available"),o,a,c).replace(":fill-available",":stretch"):p.replace(C,"-webkit-"+C)+p.replace(C,"-moz-"+C.replace("fill-",""))+p;break;case 962:if(p="-webkit-"+p+(102===p.charCodeAt(5)?"-ms-"+p:"")+p,211===a+c&&105===p.charCodeAt(13)&&0<p.indexOf("transform",10))return p.substring(0,p.indexOf(";",27)+1).replace(u,"$1-webkit-$2")+p}return p}function L(n,o){var a=n.indexOf(1===o?":":"{"),i=n.substring(0,3!==o?a:10);return a=n.substring(a+1,n.length-1),le(2!==o?i:i.replace(R,"$1"),a,o)}function ea(n,o){var a=P(o,o.charCodeAt(0),o.charCodeAt(1),o.charCodeAt(2));return a!==o+";"?a.replace(j," or ($1)").substring(4):"("+o+")"}function H(n,o,a,i,l,u,c,p,w,C){for(var x,S=0,O=o;S<ie;++S)switch(x=oe[S].call(B,n,O,a,i,l,u,c,p,w,C)){case void 0:case!1:case!0:case null:break;default:O=x}if(O!==o)return O}function U(n){return void 0!==(n=n.prefix)&&(le=null,n?"function"!=typeof n?J=1:(J=2,le=n):J=0),U}function B(n,o){var a=n;if(33>a.charCodeAt(0)&&(a=a.trim()),a=[a],0<ie){var i=H(-1,o,a,a,$,N,0,0,0,0);void 0!==i&&"string"==typeof i&&(o=i)}var l=M(re,a,o,0,0);return 0<ie&&(void 0!==(i=H(-2,l,a,a,$,N,l.length,0,0,0))&&(l=i)),"",G=0,N=$=1,l}var o=/^\0+/g,a=/[\0\r\f]/g,i=/: */g,l=/zoo|gra/,u=/([,: ])(transform)/g,c=/,\r+?/g,p=/([\t\r\n ])*\f?&/g,w=/@(k\w+)\s*(\S*)\s*/,C=/::(place)/g,x=/:(read-only)/g,S=/[svh]\w+-[tblr]{2}/,O=/\(\s*(.*)\s*\)/g,j=/([\s\S]*?);/g,k=/-self|flex-/g,R=/[^]*?(:[rp][el]a[\w-]+)[^]*/,I=/stretch|:\s*\w+\-(?:conte|avail)/,W=/([^-])(image-set\()/,N=1,$=1,G=0,J=1,re=[],oe=[],ie=0,le=null,ue=0;return B.use=function T(n){switch(n){case void 0:case null:ie=oe.length=0;break;default:if("function"==typeof n)oe[ie++]=n;else if("object"==typeof n)for(var o=0,a=n.length;o<a;++o)T(n[o]);else ue=0|!!n}return T},B.set=U,void 0!==n&&U(n),B};const C={animationIterationCount:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1};function memoize(n){var o=Object.create(null);return function(a){return void 0===o[a]&&(o[a]=n(a)),o[a]}}var x=/^((children|dangerouslySetInnerHTML|key|ref|autoFocus|defaultValue|defaultChecked|innerHTML|suppressContentEditableWarning|suppressHydrationWarning|valueLink|abbr|accept|acceptCharset|accessKey|action|allow|allowUserMedia|allowPaymentRequest|allowFullScreen|allowTransparency|alt|async|autoComplete|autoPlay|capture|cellPadding|cellSpacing|challenge|charSet|checked|cite|classID|className|cols|colSpan|content|contentEditable|contextMenu|controls|controlsList|coords|crossOrigin|data|dateTime|decoding|default|defer|dir|disabled|disablePictureInPicture|download|draggable|encType|enterKeyHint|form|formAction|formEncType|formMethod|formNoValidate|formTarget|frameBorder|headers|height|hidden|high|href|hrefLang|htmlFor|httpEquiv|id|inputMode|integrity|is|keyParams|keyType|kind|label|lang|list|loading|loop|low|marginHeight|marginWidth|max|maxLength|media|mediaGroup|method|min|minLength|multiple|muted|name|nonce|noValidate|open|optimum|pattern|placeholder|playsInline|poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbox|scope|scoped|scrolling|seamless|selected|shape|size|sizes|slot|span|spellCheck|src|srcDoc|srcLang|srcSet|start|step|style|summary|tabIndex|target|title|translate|type|useMap|value|width|wmode|wrap|about|datatype|inlist|prefix|property|resource|typeof|vocab|autoCapitalize|autoCorrect|autoSave|color|incremental|fallback|inert|itemProp|itemScope|itemType|itemID|itemRef|on|option|results|security|unselectable|accentHeight|accumulate|additive|alignmentBaseline|allowReorder|alphabetic|amplitude|arabicForm|ascent|attributeName|attributeType|autoReverse|azimuth|baseFrequency|baselineShift|baseProfile|bbox|begin|bias|by|calcMode|capHeight|clip|clipPathUnits|clipPath|clipRule|colorInterpolation|colorInterpolationFilters|colorProfile|colorRendering|contentScriptType|contentStyleType|cursor|cx|cy|d|decelerate|descent|diffuseConstant|direction|display|divisor|dominantBaseline|dur|dx|dy|edgeMode|elevation|enableBackground|end|exponent|externalResourcesRequired|fill|fillOpacity|fillRule|filter|filterRes|filterUnits|floodColor|floodOpacity|focusable|fontFamily|fontSize|fontSizeAdjust|fontStretch|fontStyle|fontVariant|fontWeight|format|from|fr|fx|fy|g1|g2|glyphName|glyphOrientationHorizontal|glyphOrientationVertical|glyphRef|gradientTransform|gradientUnits|hanging|horizAdvX|horizOriginX|ideographic|imageRendering|in|in2|intercept|k|k1|k2|k3|k4|kernelMatrix|kernelUnitLength|kerning|keyPoints|keySplines|keyTimes|lengthAdjust|letterSpacing|lightingColor|limitingConeAngle|local|markerEnd|markerMid|markerStart|markerHeight|markerUnits|markerWidth|mask|maskContentUnits|maskUnits|mathematical|mode|numOctaves|offset|opacity|operator|order|orient|orientation|origin|overflow|overlinePosition|overlineThickness|panose1|paintOrder|pathLength|patternContentUnits|patternTransform|patternUnits|pointerEvents|points|pointsAtX|pointsAtY|pointsAtZ|preserveAlpha|preserveAspectRatio|primitiveUnits|r|radius|refX|refY|renderingIntent|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|result|rotate|rx|ry|scale|seed|shapeRendering|slope|spacing|specularConstant|specularExponent|speed|spreadMethod|startOffset|stdDeviation|stemh|stemv|stitchTiles|stopColor|stopOpacity|strikethroughPosition|strikethroughThickness|string|stroke|strokeDasharray|strokeDashoffset|strokeLinecap|strokeLinejoin|strokeMiterlimit|strokeOpacity|strokeWidth|surfaceScale|systemLanguage|tableValues|targetX|targetY|textAnchor|textDecoration|textRendering|textLength|to|transform|u1|u2|underlinePosition|underlineThickness|unicode|unicodeBidi|unicodeRange|unitsPerEm|vAlphabetic|vHanging|vIdeographic|vMathematical|values|vectorEffect|version|vertAdvY|vertOriginX|vertOriginY|viewBox|viewTarget|visibility|widths|wordSpacing|writingMode|x|xHeight|x1|x2|xChannelSelector|xlinkActuate|xlinkArcrole|xlinkHref|xlinkRole|xlinkShow|xlinkTitle|xlinkType|xmlBase|xmlns|xmlnsXlink|xmlLang|xmlSpace|y|y1|y2|yChannelSelector|z|zoomAndPan|for|class|autofocus)|(([Dd][Aa][Tt][Aa]|[Aa][Rr][Ii][Aa]|x)-.*))$/,S=memoize((function(n){return x.test(n)||111===n.charCodeAt(0)&&110===n.charCodeAt(1)&&n.charCodeAt(2)<91})),O=a(55839),j=a.n(O);function y(){return(y=Object.assign||function(n){for(var o=1;o<arguments.length;o++){var a=arguments[o];for(var i in a)Object.prototype.hasOwnProperty.call(a,i)&&(n[i]=a[i])}return n}).apply(this,arguments)}var v=function(n,o){for(var a=[n[0]],i=0,l=o.length;i<l;i+=1)a.push(o[i],n[i+1]);return a},g=function(n){return null!==n&&"object"==typeof n&&"[object Object]"===(n.toString?n.toString():Object.prototype.toString.call(n))&&!(0,i.typeOf)(n)},k=Object.freeze([]),R=Object.freeze({});function E(n){return"function"==typeof n}function b(n){return n.displayName||n.name||"Component"}function _(n){return n&&"string"==typeof n.styledComponentId}var I="undefined"!=typeof process&&void 0!==process.env&&(process.env.REACT_APP_SC_ATTR||process.env.SC_ATTR)||"data-styled",W="5.3.11",N="undefined"!=typeof window&&"HTMLElement"in window,$=Boolean("boolean"==typeof SC_DISABLE_SPEEDY?SC_DISABLE_SPEEDY:"undefined"!=typeof process&&void 0!==process.env&&(void 0!==process.env.REACT_APP_SC_DISABLE_SPEEDY&&""!==process.env.REACT_APP_SC_DISABLE_SPEEDY?"false"!==process.env.REACT_APP_SC_DISABLE_SPEEDY&&process.env.REACT_APP_SC_DISABLE_SPEEDY:void 0!==process.env.SC_DISABLE_SPEEDY&&""!==process.env.SC_DISABLE_SPEEDY&&("false"!==process.env.SC_DISABLE_SPEEDY&&process.env.SC_DISABLE_SPEEDY))),G={};function D(n){for(var o=arguments.length,a=new Array(o>1?o-1:0),i=1;i<o;i++)a[i-1]=arguments[i];throw new Error("An error occurred. See https://git.io/JUIaE#"+n+" for more information."+(a.length>0?" Args: "+a.join(", "):""))}var J=function(){function e(n){this.groupSizes=new Uint32Array(512),this.length=512,this.tag=n}var n=e.prototype;return n.indexOfGroup=function(n){for(var o=0,a=0;a<n;a++)o+=this.groupSizes[a];return o},n.insertRules=function(n,o){if(n>=this.groupSizes.length){for(var a=this.groupSizes,i=a.length,l=i;n>=l;)(l<<=1)<0&&D(16,""+n);this.groupSizes=new Uint32Array(l),this.groupSizes.set(a),this.length=l;for(var u=i;u<l;u++)this.groupSizes[u]=0}for(var c=this.indexOfGroup(n+1),p=0,w=o.length;p<w;p++)this.tag.insertRule(c,o[p])&&(this.groupSizes[n]++,c++)},n.clearGroup=function(n){if(n<this.length){var o=this.groupSizes[n],a=this.indexOfGroup(n),i=a+o;this.groupSizes[n]=0;for(var l=a;l<i;l++)this.tag.deleteRule(a)}},n.getGroup=function(n){var o="";if(n>=this.length||0===this.groupSizes[n])return o;for(var a=this.groupSizes[n],i=this.indexOfGroup(n),l=i+a,u=i;u<l;u++)o+=this.tag.getRule(u)+"/*!sc*/\n";return o},e}(),re=new Map,oe=new Map,ie=1,V=function(n){if(re.has(n))return re.get(n);for(;oe.has(ie);)ie++;var o=ie++;return re.set(n,o),oe.set(o,n),o},B=function(n){return oe.get(n)},z=function(n,o){o>=ie&&(ie=o+1),re.set(n,o),oe.set(o,n)},le="style["+I+'][data-styled-version="5.3.11"]',ue=new RegExp("^"+I+'\\.g(\\d+)\\[id="([\\w\\d-]+)"\\].*?"([^"]*)'),L=function(n,o,a){for(var i,l=a.split(","),u=0,c=l.length;u<c;u++)(i=l[u])&&n.registerName(o,i)},F=function(n,o){for(var a=(o.textContent||"").split("/*!sc*/\n"),i=[],l=0,u=a.length;l<u;l++){var c=a[l].trim();if(c){var p=c.match(ue);if(p){var w=0|parseInt(p[1],10),C=p[2];0!==w&&(z(C,w),L(n,C,p[3]),n.getTag().insertRules(w,i)),i.length=0}else i.push(c)}}},Y=function(){return a.nc},q=function(n){var o=document.head,a=n||o,i=document.createElement("style"),l=function(n){for(var o=n.childNodes,a=o.length;a>=0;a--){var i=o[a];if(i&&1===i.nodeType&&i.hasAttribute(I))return i}}(a),u=void 0!==l?l.nextSibling:null;i.setAttribute(I,"active"),i.setAttribute("data-styled-version","5.3.11");var c=Y();return c&&i.setAttribute("nonce",c),a.insertBefore(i,u),i},ce=function(){function e(n){var o=this.element=q(n);o.appendChild(document.createTextNode("")),this.sheet=function(n){if(n.sheet)return n.sheet;for(var o=document.styleSheets,a=0,i=o.length;a<i;a++){var l=o[a];if(l.ownerNode===n)return l}D(17)}(o),this.length=0}var n=e.prototype;return n.insertRule=function(n,o){try{return this.sheet.insertRule(o,n),this.length++,!0}catch(n){return!1}},n.deleteRule=function(n){this.sheet.deleteRule(n),this.length--},n.getRule=function(n){var o=this.sheet.cssRules[n];return void 0!==o&&"string"==typeof o.cssText?o.cssText:""},e}(),se=function(){function e(n){var o=this.element=q(n);this.nodes=o.childNodes,this.length=0}var n=e.prototype;return n.insertRule=function(n,o){if(n<=this.length&&n>=0){var a=document.createTextNode(o),i=this.nodes[n];return this.element.insertBefore(a,i||null),this.length++,!0}return!1},n.deleteRule=function(n){this.element.removeChild(this.nodes[n]),this.length--},n.getRule=function(n){return n<this.length?this.nodes[n].textContent:""},e}(),de=function(){function e(n){this.rules=[],this.length=0}var n=e.prototype;return n.insertRule=function(n,o){return n<=this.length&&(this.rules.splice(n,0,o),this.length++,!0)},n.deleteRule=function(n){this.rules.splice(n,1),this.length--},n.getRule=function(n){return n<this.length?this.rules[n]:""},e}(),he=N,ge={isServer:!N,useCSSOMInjection:!$},ye=function(){function e(n,o,a){void 0===n&&(n=R),void 0===o&&(o={}),this.options=y({},ge,{},n),this.gs=o,this.names=new Map(a),this.server=!!n.isServer,!this.server&&N&&he&&(he=!1,function(n){for(var o=document.querySelectorAll(le),a=0,i=o.length;a<i;a++){var l=o[a];l&&"active"!==l.getAttribute(I)&&(F(n,l),l.parentNode&&l.parentNode.removeChild(l))}}(this))}e.registerId=function(n){return V(n)};var n=e.prototype;return n.reconstructWithOptions=function(n,o){return void 0===o&&(o=!0),new e(y({},this.options,{},n),this.gs,o&&this.names||void 0)},n.allocateGSInstance=function(n){return this.gs[n]=(this.gs[n]||0)+1},n.getTag=function(){return this.tag||(this.tag=(a=(o=this.options).isServer,i=o.useCSSOMInjection,l=o.target,n=a?new de(l):i?new ce(l):new se(l),new J(n)));var n,o,a,i,l},n.hasNameForId=function(n,o){return this.names.has(n)&&this.names.get(n).has(o)},n.registerName=function(n,o){if(V(n),this.names.has(n))this.names.get(n).add(o);else{var a=new Set;a.add(o),this.names.set(n,a)}},n.insertRules=function(n,o,a){this.registerName(n,o),this.getTag().insertRules(V(n),a)},n.clearNames=function(n){this.names.has(n)&&this.names.get(n).clear()},n.clearRules=function(n){this.getTag().clearGroup(V(n)),this.clearNames(n)},n.clearTag=function(){this.tag=void 0},n.toString=function(){return function(n){for(var o=n.getTag(),a=o.length,i="",l=0;l<a;l++){var u=B(l);if(void 0!==u){var c=n.names.get(u),p=o.getGroup(l);if(c&&p&&c.size){var w=I+".g"+l+'[id="'+u+'"]',C="";void 0!==c&&c.forEach((function(n){n.length>0&&(C+=n+",")})),i+=""+p+w+'{content:"'+C+'"}/*!sc*/\n'}}}return i}(this)},e}(),ve=/(a)(d)/gi,K=function(n){return String.fromCharCode(n+(n>25?39:97))};function Q(n){var o,a="";for(o=Math.abs(n);o>52;o=o/52|0)a=K(o%52)+a;return(K(o%52)+a).replace(ve,"$1-$2")}var ee=function(n,o){for(var a=o.length;a;)n=33*n^o.charCodeAt(--a);return n},te=function(n){return ee(5381,n)};function ne(n){for(var o=0;o<n.length;o+=1){var a=n[o];if(E(a)&&!_(a))return!1}return!0}var Ce=te("5.3.11"),Pe=function(){function e(n,o,a){this.rules=n,this.staticRulesId="",this.isStatic=(void 0===a||a.isStatic)&&ne(n),this.componentId=o,this.baseHash=ee(Ce,o),this.baseStyle=a,ye.registerId(o)}return e.prototype.generateAndInjectStyles=function(n,o,a){var i=this.componentId,l=[];if(this.baseStyle&&l.push(this.baseStyle.generateAndInjectStyles(n,o,a)),this.isStatic&&!a.hash)if(this.staticRulesId&&o.hasNameForId(i,this.staticRulesId))l.push(this.staticRulesId);else{var u=_e(this.rules,n,o,a).join(""),c=Q(ee(this.baseHash,u)>>>0);if(!o.hasNameForId(i,c)){var p=a(u,"."+c,void 0,i);o.insertRules(i,c,p)}l.push(c),this.staticRulesId=c}else{for(var w=this.rules.length,C=ee(this.baseHash,a.hash),x="",S=0;S<w;S++){var O=this.rules[S];if("string"==typeof O)x+=O;else if(O){var j=_e(O,n,o,a),k=Array.isArray(j)?j.join(""):j;C=ee(C,k+S),x+=k}}if(x){var R=Q(C>>>0);if(!o.hasNameForId(i,R)){var I=a(x,"."+R,void 0,i);o.insertRules(i,R,I)}l.push(R)}}return l.join(" ")},e}(),Se=/^\s*\/\/.*$/gm,Re=[":","[",".","#"];function ae(n){var o,a,i,l,u=void 0===n?R:n,c=u.options,p=void 0===c?R:c,C=u.plugins,x=void 0===C?k:C,S=new w(p),O=[],j=function(n){function t(o){if(o)try{n(o+"}")}catch(n){}}return function(o,a,i,l,u,c,p,w,C,x){switch(o){case 1:if(0===C&&64===a.charCodeAt(0))return n(a+";"),"";break;case 2:if(0===w)return a+"/*|*/";break;case 3:switch(w){case 102:case 112:return n(i[0]+a),"";default:return a+(0===x?"/*|*/":"")}case-2:a.split("/*|*/}").forEach(t)}}}((function(n){O.push(n)})),f=function(n,i,u){return 0===i&&-1!==Re.indexOf(u[a.length])||u.match(l)?n:"."+o};function m(n,u,c,p){void 0===p&&(p="&");var w=n.replace(Se,""),C=u&&c?c+" "+u+" { "+w+" }":w;return o=p,a=u,i=new RegExp("\\"+a+"\\b","g"),l=new RegExp("(\\"+a+"\\b){2,}"),S(c||!u?"":u,C)}return S.use([].concat(x,[function(n,o,l){2===n&&l.length&&l[0].lastIndexOf(a)>0&&(l[0]=l[0].replace(i,f))},j,function(n){if(-2===n){var o=O;return O=[],o}}])),m.hash=x.length?x.reduce((function(n,o){return o.name||D(15),ee(n,o.name)}),5381).toString():"",m}var Ie=u().createContext(),Me=Ie.Consumer,De=u().createContext(),He=(De.Consumer,new ye),Ue=ae();function pe(){return(0,l.useContext)(Ie)||He}function fe(){return(0,l.useContext)(De)||Ue}function me(n){var o=(0,l.useState)(n.stylisPlugins),a=o[0],i=o[1],c=pe(),w=(0,l.useMemo)((function(){var o=c;return n.sheet?o=n.sheet:n.target&&(o=o.reconstructWithOptions({target:n.target},!1)),n.disableCSSOMInjection&&(o=o.reconstructWithOptions({useCSSOMInjection:!1})),o}),[n.disableCSSOMInjection,n.sheet,n.target]),C=(0,l.useMemo)((function(){return ae({options:{prefix:!n.disableVendorPrefixes},plugins:a})}),[n.disableVendorPrefixes,a]);return(0,l.useEffect)((function(){p()(a,n.stylisPlugins)||i(n.stylisPlugins)}),[n.stylisPlugins]),u().createElement(Ie.Provider,{value:w},u().createElement(De.Provider,{value:C},n.children))}var Fe=function(){function e(n,o){var a=this;this.inject=function(n,o){void 0===o&&(o=Ue);var i=a.name+o.hash;n.hasNameForId(a.id,i)||n.insertRules(a.id,i,o(a.rules,i,"@keyframes"))},this.toString=function(){return D(12,String(a.name))},this.name=n,this.id="sc-keyframes-"+n,this.rules=o}return e.prototype.getName=function(n){return void 0===n&&(n=Ue),this.name+n.hash},e}(),Ge=/([A-Z])/,Ke=/([A-Z])/g,Ze=/^ms-/,we=function(n){return"-"+n.toLowerCase()};function Ee(n){return Ge.test(n)?n.replace(Ke,we).replace(Ze,"-ms-"):n}var be=function(n){return null==n||!1===n||""===n};function _e(n,o,a,i){if(Array.isArray(n)){for(var l,u=[],c=0,p=n.length;c<p;c+=1)""!==(l=_e(n[c],o,a,i))&&(Array.isArray(l)?u.push.apply(u,l):u.push(l));return u}return be(n)?"":_(n)?"."+n.styledComponentId:E(n)?"function"!=typeof(w=n)||w.prototype&&w.prototype.isReactComponent||!o?n:_e(n(o),o,a,i):n instanceof Fe?a?(n.inject(a,i),n.getName(i)):n:g(n)?function e(n,o){var a,i,l=[];for(var u in n)n.hasOwnProperty(u)&&!be(n[u])&&(Array.isArray(n[u])&&n[u].isCss||E(n[u])?l.push(Ee(u)+":",n[u],";"):g(n[u])?l.push.apply(l,e(n[u],u)):l.push(Ee(u)+": "+(a=u,(null==(i=n[u])||"boolean"==typeof i||""===i?"":"number"!=typeof i||0===i||a in C||a.startsWith("--")?String(i).trim():i+"px")+";")));return o?[o+" {"].concat(l,["}"]):l}(n):n.toString();var w}var Ne=function(n){return Array.isArray(n)&&(n.isCss=!0),n};function Ae(n){for(var o=arguments.length,a=new Array(o>1?o-1:0),i=1;i<o;i++)a[i-1]=arguments[i];return E(n)||g(n)?Ne(_e(v(k,[n].concat(a)))):0===a.length&&1===n.length&&"string"==typeof n[0]?n:Ne(_e(v(n,a)))}new Set;var Oe=function(n,o,a){return void 0===a&&(a=R),n.theme!==a.theme&&n.theme||o||a.theme},Qe=/[!"#$%&'()*+,./:;<=>?@[\\\]^`{|}~-]+/g,et=/(^-|-$)/g;function je(n){return n.replace(Qe,"-").replace(et,"")}var Te=function(n){return Q(te(n)>>>0)};function xe(n){return"string"==typeof n&&!0}var ke=function(n){return"function"==typeof n||"object"==typeof n&&null!==n&&!Array.isArray(n)},Ve=function(n){return"__proto__"!==n&&"constructor"!==n&&"prototype"!==n};function Be(n,o,a){var i=n[a];ke(o)&&ke(i)?ze(i,o):n[a]=o}function ze(n){for(var o=arguments.length,a=new Array(o>1?o-1:0),i=1;i<o;i++)a[i-1]=arguments[i];for(var l=0,u=a;l<u.length;l++){var c=u[l];if(ke(c))for(var p in c)Ve(p)&&Be(n,c[p],p)}return n}var tt=u().createContext(),rt=tt.Consumer;function Le(n){var o=(0,l.useContext)(tt),a=(0,l.useMemo)((function(){return function(n,o){return n?E(n)?n(o):Array.isArray(n)||"object"!=typeof n?D(8):o?y({},o,{},n):n:D(14)}(n.theme,o)}),[n.theme,o]);return n.children?u().createElement(tt.Provider,{value:a},n.children):null}var nt={};function Ye(n,o,a){var i=_(n),c=!xe(n),p=o.attrs,w=void 0===p?k:p,C=o.componentId,x=void 0===C?function(n,o){var a="string"!=typeof n?"sc":je(n);nt[a]=(nt[a]||0)+1;var i=a+"-"+Te("5.3.11"+a+nt[a]);return o?o+"-"+i:i}(o.displayName,o.parentComponentId):C,O=o.displayName,I=void 0===O?function(n){return xe(n)?"styled."+n:"Styled("+b(n)+")"}(n):O,W=o.displayName&&o.componentId?je(o.displayName)+"-"+o.componentId:o.componentId||x,N=i&&n.attrs?Array.prototype.concat(n.attrs,w).filter(Boolean):w,$=o.shouldForwardProp;i&&n.shouldForwardProp&&($=o.shouldForwardProp?function(a,i,l){return n.shouldForwardProp(a,i,l)&&o.shouldForwardProp(a,i,l)}:n.shouldForwardProp);var G,J=new Pe(a,W,i?n.componentStyle:void 0),re=J.isStatic&&0===w.length,P=function(n,o){return function(n,o,a,i){var u=n.attrs,c=n.componentStyle,p=n.defaultProps,w=n.foldedComponentIds,C=n.shouldForwardProp,x=n.styledComponentId,O=n.target,j=function(n,o,a){void 0===n&&(n=R);var i=y({},o,{theme:n}),l={};return a.forEach((function(n){var o,a,u,c=n;for(o in E(c)&&(c=c(i)),c)i[o]=l[o]="className"===o?(a=l[o],u=c[o],a&&u?a+" "+u:a||u):c[o]})),[i,l]}(Oe(o,(0,l.useContext)(tt),p)||R,o,u),k=j[0],I=j[1],W=function(n,o,a,i){var l=pe(),u=fe();return o?n.generateAndInjectStyles(R,l,u):n.generateAndInjectStyles(a,l,u)}(c,i,k),N=a,$=I.$as||o.$as||I.as||o.as||O,G=xe($),J=I!==o?y({},o,{},I):o,re={};for(var oe in J)"$"!==oe[0]&&"as"!==oe&&("forwardedAs"===oe?re.as=J[oe]:(C?C(oe,S,$):!G||S(oe))&&(re[oe]=J[oe]));return o.style&&I.style!==o.style&&(re.style=y({},o.style,{},I.style)),re.className=Array.prototype.concat(w,x,W!==x?W:null,o.className,I.className).filter(Boolean).join(" "),re.ref=N,(0,l.createElement)($,re)}(G,n,o,re)};return P.displayName=I,(G=u().forwardRef(P)).attrs=N,G.componentStyle=J,G.displayName=I,G.shouldForwardProp=$,G.foldedComponentIds=i?Array.prototype.concat(n.foldedComponentIds,n.styledComponentId):k,G.styledComponentId=W,G.target=i?n.target:n,G.withComponent=function(n){var i=o.componentId,l=function(n,o){if(null==n)return{};var a,i,l={},u=Object.keys(n);for(i=0;i<u.length;i++)a=u[i],o.indexOf(a)>=0||(l[a]=n[a]);return l}(o,["componentId"]),u=i&&i+"-"+(xe(n)?n:je(b(n)));return Ye(n,y({},l,{attrs:N,componentId:u}),a)},Object.defineProperty(G,"defaultProps",{get:function(){return this._foldedDefaultProps},set:function(o){this._foldedDefaultProps=i?ze({},n.defaultProps,o):o}}),Object.defineProperty(G,"toString",{value:function(){return"."+G.styledComponentId}}),c&&j()(G,n,{attrs:!0,componentStyle:!0,displayName:!0,foldedComponentIds:!0,shouldForwardProp:!0,styledComponentId:!0,target:!0,withComponent:!0}),G}var qe=function(n){return function e(n,o,a){if(void 0===a&&(a=R),!(0,i.isValidElementType)(o))return D(1,String(o));var s=function(){return n(o,a,Ae.apply(void 0,arguments))};return s.withConfig=function(i){return e(n,o,y({},a,{},i))},s.attrs=function(i){return e(n,o,y({},a,{attrs:Array.prototype.concat(a.attrs,i).filter(Boolean)}))},s}(Ye,n)};["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","marquee","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","title","tr","track","u","ul","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","marker","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","textPath","tspan"].forEach((function(n){qe[n]=qe(n)}));var ot=function(){function e(n,o){this.rules=n,this.componentId=o,this.isStatic=ne(n),ye.registerId(this.componentId+1)}var n=e.prototype;return n.createStyles=function(n,o,a,i){var l=i(_e(this.rules,o,a,i).join(""),""),u=this.componentId+n;a.insertRules(u,u,l)},n.removeStyles=function(n,o){o.clearRules(this.componentId+n)},n.renderStyles=function(n,o,a,i){n>2&&ye.registerId(this.componentId+n),this.removeStyles(n,a),this.createStyles(n,o,a,i)},e}();function $e(n){for(var o=arguments.length,a=new Array(o>1?o-1:0),i=1;i<o;i++)a[i-1]=arguments[i];var c=Ae.apply(void 0,[n].concat(a)),p="sc-global-"+Te(JSON.stringify(c)),w=new ot(c,p);function d(n){var o=pe(),a=fe(),i=(0,l.useContext)(tt),u=(0,l.useRef)(o.allocateGSInstance(p)).current;return o.server&&h(u,n,o,i,a),(0,l.useLayoutEffect)((function(){if(!o.server)return h(u,n,o,i,a),function(){return w.removeStyles(u,o)}}),[u,n,o,i,a]),null}function h(n,o,a,i,l){if(w.isStatic)w.renderStyles(n,G,a,l);else{var u=y({},o,{theme:Oe(o,i,d.defaultProps)});w.renderStyles(n,u,a,l)}}return u().memo(d)}function We(n){for(var o=arguments.length,a=new Array(o>1?o-1:0),i=1;i<o;i++)a[i-1]=arguments[i];var l=Ae.apply(void 0,[n].concat(a)).join(""),u=Te(l);return new Fe(u,l)}var at=function(){function e(){var n=this;this._emitSheetCSS=function(){var o=n.instance.toString();if(!o)return"";var a=Y();return"<style "+[a&&'nonce="'+a+'"',I+'="true"','data-styled-version="5.3.11"'].filter(Boolean).join(" ")+">"+o+"</style>"},this.getStyleTags=function(){return n.sealed?D(2):n._emitSheetCSS()},this.getStyleElement=function(){var o;if(n.sealed)return D(2);var a=((o={})[I]="",o["data-styled-version"]="5.3.11",o.dangerouslySetInnerHTML={__html:n.instance.toString()},o),i=Y();return i&&(a.nonce=i),[u().createElement("style",y({},a,{key:"sc-0-0"}))]},this.seal=function(){n.sealed=!0},this.instance=new ye({isServer:!0}),this.sealed=!1}var n=e.prototype;return n.collectStyles=function(n){return this.sealed?D(2):u().createElement(me,{sheet:this.instance},n)},n.interleaveWithNodeStream=function(n){return D(3)},e}(),Je=function(n){var o=u().forwardRef((function(o,a){var i=(0,l.useContext)(tt),c=n.defaultProps,p=Oe(o,i,c);return u().createElement(n,y({},o,{theme:p,ref:a}))}));return j()(o,n),o.displayName="WithTheme("+b(n)+")",o},Xe=function(){return(0,l.useContext)(tt)},it={StyleSheet:ye,masterSheet:He};const lt=qe},37244:(n,o,a)=>{"use strict";Object.defineProperty(o,"__esModule",{value:!0}),o.default=function isFQDN(n,o){(0,i.default)(n),(o=(0,l.default)(o,u)).allow_trailing_dot&&"."===n[n.length-1]&&(n=n.substring(0,n.length-1));!0===o.allow_wildcard&&0===n.indexOf("*.")&&(n=n.substring(2));var a=n.split("."),c=a[a.length-1];if(o.require_tld){if(a.length<2)return!1;if(!o.allow_numeric_tld&&!/^([a-z\u00A1-\u00A8\u00AA-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]{2,}|xn[a-z0-9-]{2,})$/i.test(c))return!1;if(/\s/.test(c))return!1}if(!o.allow_numeric_tld&&/^\d+$/.test(c))return!1;return a.every((function(n){return!(n.length>63&&!o.ignore_max_length)&&(!!/^[a-z_\u00a1-\uffff0-9-]+$/i.test(n)&&(!/[\uff01-\uff5e]/.test(n)&&(!/^-|-$/.test(n)&&!(!o.allow_underscores&&/_/.test(n)))))}))};var i=_interopRequireDefault(a(7774)),l=_interopRequireDefault(a(85092));function _interopRequireDefault(n){return n&&n.__esModule?n:{default:n}}var u={require_tld:!0,allow_underscores:!1,allow_trailing_dot:!1,allow_numeric_tld:!1,allow_wildcard:!1,ignore_max_length:!1};n.exports=o.default,n.exports.default=o.default},91540:(n,o,a)=>{"use strict";Object.defineProperty(o,"__esModule",{value:!0}),o.default=function isIP(n){var o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";if((0,i.default)(n),!(o=String(o)))return isIP(n,4)||isIP(n,6);if("4"===o)return c.test(n);if("6"===o)return w.test(n);return!1};var i=function _interopRequireDefault(n){return n&&n.__esModule?n:{default:n}}(a(7774));var l="(?:[0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])",u="(".concat(l,"[.]){3}").concat(l),c=new RegExp("^".concat(u,"$")),p="(?:[0-9a-fA-F]{1,4})",w=new RegExp("^("+"(?:".concat(p,":){7}(?:").concat(p,"|:)|")+"(?:".concat(p,":){6}(?:").concat(u,"|:").concat(p,"|:)|")+"(?:".concat(p,":){5}(?::").concat(u,"|(:").concat(p,"){1,2}|:)|")+"(?:".concat(p,":){4}(?:(:").concat(p,"){0,1}:").concat(u,"|(:").concat(p,"){1,3}|:)|")+"(?:".concat(p,":){3}(?:(:").concat(p,"){0,2}:").concat(u,"|(:").concat(p,"){1,4}|:)|")+"(?:".concat(p,":){2}(?:(:").concat(p,"){0,3}:").concat(u,"|(:").concat(p,"){1,5}|:)|")+"(?:".concat(p,":){1}(?:(:").concat(p,"){0,4}:").concat(u,"|(:").concat(p,"){1,6}|:)|")+"(?::((?::".concat(p,"){0,5}:").concat(u,"|(?::").concat(p,"){1,7}|:))")+")(%[0-9a-zA-Z-.:]{1,})?$");n.exports=o.default,n.exports.default=o.default},50429:(n,o,a)=>{"use strict";Object.defineProperty(o,"__esModule",{value:!0}),o.default=function isURL(n,o){if((0,i.default)(n),!n||/[\s<>]/.test(n))return!1;if(0===n.indexOf("mailto:"))return!1;if((o=(0,c.default)(o,p)).validate_length&&n.length>=2083)return!1;if(!o.allow_fragments&&n.includes("#"))return!1;if(!o.allow_query_components&&(n.includes("?")||n.includes("&")))return!1;var a,C,x,S,O,j,k,R;if(k=n.split("#"),n=k.shift(),k=n.split("?"),n=k.shift(),(k=n.split("://")).length>1){if(a=k.shift().toLowerCase(),o.require_valid_protocol&&-1===o.protocols.indexOf(a))return!1}else{if(o.require_protocol)return!1;if("//"===n.slice(0,2)){if(!o.allow_protocol_relative_urls)return!1;k[0]=n.slice(2)}}if(""===(n=k.join("://")))return!1;if(k=n.split("/"),""===(n=k.shift())&&!o.require_host)return!0;if((k=n.split("@")).length>1){if(o.disallow_auth)return!1;if(""===k[0])return!1;if((C=k.shift()).indexOf(":")>=0&&C.split(":").length>2)return!1;var I=function _slicedToArray(n,o){return function _arrayWithHoles(n){if(Array.isArray(n))return n}(n)||function _iterableToArrayLimit(n,o){if("undefined"==typeof Symbol||!(Symbol.iterator in Object(n)))return;var a=[],i=!0,l=!1,u=void 0;try{for(var c,p=n[Symbol.iterator]();!(i=(c=p.next()).done)&&(a.push(c.value),!o||a.length!==o);i=!0);}catch(n){l=!0,u=n}finally{try{i||null==p.return||p.return()}finally{if(l)throw u}}return a}(n,o)||function _unsupportedIterableToArray(n,o){if(!n)return;if("string"==typeof n)return _arrayLikeToArray(n,o);var a=Object.prototype.toString.call(n).slice(8,-1);"Object"===a&&n.constructor&&(a=n.constructor.name);if("Map"===a||"Set"===a)return Array.from(n);if("Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a))return _arrayLikeToArray(n,o)}(n,o)||function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(C.split(":"),2),W=I[0],N=I[1];if(""===W&&""===N)return!1}S=k.join("@"),j=null,R=null;var $=S.match(w);$?(x="",R=$[1],j=$[2]||null):(x=(k=S.split(":")).shift(),k.length&&(j=k.join(":")));if(null!==j&&j.length>0){if(O=parseInt(j,10),!/^[0-9]+$/.test(j)||O<=0||O>65535)return!1}else if(o.require_port)return!1;if(o.host_whitelist)return checkHost(x,o.host_whitelist);if(""===x&&!o.require_host)return!0;if(!((0,u.default)(x)||(0,l.default)(x,o)||R&&(0,u.default)(R,6)))return!1;if(x=x||R,o.host_blacklist&&checkHost(x,o.host_blacklist))return!1;return!0};var i=_interopRequireDefault(a(7774)),l=_interopRequireDefault(a(37244)),u=_interopRequireDefault(a(91540)),c=_interopRequireDefault(a(85092));function _interopRequireDefault(n){return n&&n.__esModule?n:{default:n}}function _arrayLikeToArray(n,o){(null==o||o>n.length)&&(o=n.length);for(var a=0,i=new Array(o);a<o;a++)i[a]=n[a];return i}var p={protocols:["http","https","ftp"],require_tld:!0,require_protocol:!1,require_host:!0,require_port:!1,require_valid_protocol:!0,allow_underscores:!1,allow_trailing_dot:!1,allow_protocol_relative_urls:!1,allow_fragments:!0,allow_query_components:!0,validate_length:!0},w=/^\[([^\]]+)\](?::([0-9]+))?$/;function checkHost(n,o){for(var a=0;a<o.length;a++){var i=o[a];if(n===i||(l=i,"[object RegExp]"===Object.prototype.toString.call(l)&&i.test(n)))return!0}var l;return!1}n.exports=o.default,n.exports.default=o.default},7774:(n,o)=>{"use strict";function _typeof(n){return _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function _typeof(n){return typeof n}:function _typeof(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},_typeof(n)}Object.defineProperty(o,"__esModule",{value:!0}),o.default=function assertString(n){if(!("string"==typeof n||n instanceof String)){var o=_typeof(n);throw null===n?o="null":"object"===o&&(o=n.constructor.name),new TypeError("Expected a string but received a ".concat(o))}},n.exports=o.default,n.exports.default=o.default},85092:(n,o)=>{"use strict";Object.defineProperty(o,"__esModule",{value:!0}),o.default=function merge(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},o=arguments.length>1?arguments[1]:void 0;for(var a in o)void 0===n[a]&&(n[a]=o[a]);return n},n.exports=o.default,n.exports.default=o.default},87363:n=>{"use strict";n.exports=React},61533:n=>{"use strict";n.exports=ReactDOM},34029:n=>{"use strict";n.exports=elementorV2.icons},36626:n=>{"use strict";n.exports=elementorV2.ui},38003:n=>{"use strict";n.exports=wp.i18n},98106:n=>{n.exports=function _arrayLikeToArray(n,o){(null==o||o>n.length)&&(o=n.length);for(var a=0,i=new Array(o);a<o;a++)i[a]=n[a];return i},n.exports.__esModule=!0,n.exports.default=n.exports},17358:n=>{n.exports=function _arrayWithHoles(n){if(Array.isArray(n))return n},n.exports.__esModule=!0,n.exports.default=n.exports},34102:(n,o,a)=>{var i=a(98106);n.exports=function _arrayWithoutHoles(n){if(Array.isArray(n))return i(n)},n.exports.__esModule=!0,n.exports.default=n.exports},77266:n=>{n.exports=function _assertThisInitialized(n){if(void 0===n)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return n},n.exports.__esModule=!0,n.exports.default=n.exports},10029:n=>{function asyncGeneratorStep(n,o,a,i,l,u,c){try{var p=n[u](c),w=p.value}catch(n){return void a(n)}p.done?o(w):Promise.resolve(w).then(i,l)}n.exports=function _asyncToGenerator(n){return function(){var o=this,a=arguments;return new Promise((function(i,l){var u=n.apply(o,a);function _next(n){asyncGeneratorStep(u,i,l,_next,_throw,"next",n)}function _throw(n){asyncGeneratorStep(u,i,l,_next,_throw,"throw",n)}_next(void 0)}))}},n.exports.__esModule=!0,n.exports.default=n.exports},78983:n=>{n.exports=function _classCallCheck(n,o){if(!(n instanceof o))throw new TypeError("Cannot call a class as a function")},n.exports.__esModule=!0,n.exports.default=n.exports},42081:(n,o,a)=>{var i=a(74040);function _defineProperties(n,o){for(var a=0;a<o.length;a++){var l=o[a];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(n,i(l.key),l)}}n.exports=function _createClass(n,o,a){return o&&_defineProperties(n.prototype,o),a&&_defineProperties(n,a),Object.defineProperty(n,"prototype",{writable:!1}),n},n.exports.__esModule=!0,n.exports.default=n.exports},93231:(n,o,a)=>{var i=a(74040);n.exports=function _defineProperty(n,o,a){return(o=i(o))in n?Object.defineProperty(n,o,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[o]=a,n},n.exports.__esModule=!0,n.exports.default=n.exports},73119:n=>{function _extends(){return n.exports=_extends=Object.assign?Object.assign.bind():function(n){for(var o=1;o<arguments.length;o++){var a=arguments[o];for(var i in a)Object.prototype.hasOwnProperty.call(a,i)&&(n[i]=a[i])}return n},n.exports.__esModule=!0,n.exports.default=n.exports,_extends.apply(this,arguments)}n.exports=_extends,n.exports.__esModule=!0,n.exports.default=n.exports},74910:n=>{function _getPrototypeOf(o){return n.exports=_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function _getPrototypeOf(n){return n.__proto__||Object.getPrototypeOf(n)},n.exports.__esModule=!0,n.exports.default=n.exports,_getPrototypeOf(o)}n.exports=_getPrototypeOf,n.exports.__esModule=!0,n.exports.default=n.exports},58724:(n,o,a)=>{var i=a(96196);n.exports=function _inherits(n,o){if("function"!=typeof o&&null!==o)throw new TypeError("Super expression must either be null or a function");n.prototype=Object.create(o&&o.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),o&&i(n,o)},n.exports.__esModule=!0,n.exports.default=n.exports},73203:n=>{n.exports=function _interopRequireDefault(n){return n&&n.__esModule?n:{default:n}},n.exports.__esModule=!0,n.exports.default=n.exports},68:n=>{n.exports=function _iterableToArray(n){if("undefined"!=typeof Symbol&&null!=n[Symbol.iterator]||null!=n["@@iterator"])return Array.from(n)},n.exports.__esModule=!0,n.exports.default=n.exports},40608:n=>{n.exports=function _iterableToArrayLimit(n,o){var a=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=a){var i,l,u,c,p=[],w=!0,C=!1;try{if(u=(a=a.call(n)).next,0===o){if(Object(a)!==a)return;w=!1}else for(;!(w=(i=u.call(a)).done)&&(p.push(i.value),p.length!==o);w=!0);}catch(n){C=!0,l=n}finally{try{if(!w&&null!=a.return&&(c=a.return(),Object(c)!==c))return}finally{if(C)throw l}}return p}},n.exports.__esModule=!0,n.exports.default=n.exports},56894:n=>{n.exports=function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},n.exports.__esModule=!0,n.exports.default=n.exports},91282:n=>{n.exports=function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},n.exports.__esModule=!0,n.exports.default=n.exports},70966:(n,o,a)=>{var i=a(22412);n.exports=function _objectWithoutProperties(n,o){if(null==n)return{};var a,l,u=i(n,o);if(Object.getOwnPropertySymbols){var c=Object.getOwnPropertySymbols(n);for(l=0;l<c.length;l++)a=c[l],o.indexOf(a)>=0||Object.prototype.propertyIsEnumerable.call(n,a)&&(u[a]=n[a])}return u},n.exports.__esModule=!0,n.exports.default=n.exports},22412:n=>{n.exports=function _objectWithoutPropertiesLoose(n,o){if(null==n)return{};var a,i,l={},u=Object.keys(n);for(i=0;i<u.length;i++)a=u[i],o.indexOf(a)>=0||(l[a]=n[a]);return l},n.exports.__esModule=!0,n.exports.default=n.exports},71173:(n,o,a)=>{var i=a(7501).default,l=a(77266);n.exports=function _possibleConstructorReturn(n,o){if(o&&("object"===i(o)||"function"==typeof o))return o;if(void 0!==o)throw new TypeError("Derived constructors may only return object or undefined");return l(n)},n.exports.__esModule=!0,n.exports.default=n.exports},21337:(n,o,a)=>{var i=a(7501).default;function _regeneratorRuntime(){"use strict";n.exports=_regeneratorRuntime=function _regeneratorRuntime(){return a},n.exports.__esModule=!0,n.exports.default=n.exports;var o,a={},l=Object.prototype,u=l.hasOwnProperty,c=Object.defineProperty||function(n,o,a){n[o]=a.value},p="function"==typeof Symbol?Symbol:{},w=p.iterator||"@@iterator",C=p.asyncIterator||"@@asyncIterator",x=p.toStringTag||"@@toStringTag";function define(n,o,a){return Object.defineProperty(n,o,{value:a,enumerable:!0,configurable:!0,writable:!0}),n[o]}try{define({},"")}catch(o){define=function define(n,o,a){return n[o]=a}}function wrap(n,o,a,i){var l=o&&o.prototype instanceof Generator?o:Generator,u=Object.create(l.prototype),p=new Context(i||[]);return c(u,"_invoke",{value:makeInvokeMethod(n,a,p)}),u}function tryCatch(n,o,a){try{return{type:"normal",arg:n.call(o,a)}}catch(n){return{type:"throw",arg:n}}}a.wrap=wrap;var S="suspendedStart",O="suspendedYield",j="executing",k="completed",R={};function Generator(){}function GeneratorFunction(){}function GeneratorFunctionPrototype(){}var I={};define(I,w,(function(){return this}));var W=Object.getPrototypeOf,N=W&&W(W(values([])));N&&N!==l&&u.call(N,w)&&(I=N);var $=GeneratorFunctionPrototype.prototype=Generator.prototype=Object.create(I);function defineIteratorMethods(n){["next","throw","return"].forEach((function(o){define(n,o,(function(n){return this._invoke(o,n)}))}))}function AsyncIterator(n,o){function invoke(a,l,c,p){var w=tryCatch(n[a],n,l);if("throw"!==w.type){var C=w.arg,x=C.value;return x&&"object"==i(x)&&u.call(x,"__await")?o.resolve(x.__await).then((function(n){invoke("next",n,c,p)}),(function(n){invoke("throw",n,c,p)})):o.resolve(x).then((function(n){C.value=n,c(C)}),(function(n){return invoke("throw",n,c,p)}))}p(w.arg)}var a;c(this,"_invoke",{value:function value(n,i){function callInvokeWithMethodAndArg(){return new o((function(o,a){invoke(n,i,o,a)}))}return a=a?a.then(callInvokeWithMethodAndArg,callInvokeWithMethodAndArg):callInvokeWithMethodAndArg()}})}function makeInvokeMethod(n,a,i){var l=S;return function(u,c){if(l===j)throw new Error("Generator is already running");if(l===k){if("throw"===u)throw c;return{value:o,done:!0}}for(i.method=u,i.arg=c;;){var p=i.delegate;if(p){var w=maybeInvokeDelegate(p,i);if(w){if(w===R)continue;return w}}if("next"===i.method)i.sent=i._sent=i.arg;else if("throw"===i.method){if(l===S)throw l=k,i.arg;i.dispatchException(i.arg)}else"return"===i.method&&i.abrupt("return",i.arg);l=j;var C=tryCatch(n,a,i);if("normal"===C.type){if(l=i.done?k:O,C.arg===R)continue;return{value:C.arg,done:i.done}}"throw"===C.type&&(l=k,i.method="throw",i.arg=C.arg)}}}function maybeInvokeDelegate(n,a){var i=a.method,l=n.iterator[i];if(l===o)return a.delegate=null,"throw"===i&&n.iterator.return&&(a.method="return",a.arg=o,maybeInvokeDelegate(n,a),"throw"===a.method)||"return"!==i&&(a.method="throw",a.arg=new TypeError("The iterator does not provide a '"+i+"' method")),R;var u=tryCatch(l,n.iterator,a.arg);if("throw"===u.type)return a.method="throw",a.arg=u.arg,a.delegate=null,R;var c=u.arg;return c?c.done?(a[n.resultName]=c.value,a.next=n.nextLoc,"return"!==a.method&&(a.method="next",a.arg=o),a.delegate=null,R):c:(a.method="throw",a.arg=new TypeError("iterator result is not an object"),a.delegate=null,R)}function pushTryEntry(n){var o={tryLoc:n[0]};1 in n&&(o.catchLoc=n[1]),2 in n&&(o.finallyLoc=n[2],o.afterLoc=n[3]),this.tryEntries.push(o)}function resetTryEntry(n){var o=n.completion||{};o.type="normal",delete o.arg,n.completion=o}function Context(n){this.tryEntries=[{tryLoc:"root"}],n.forEach(pushTryEntry,this),this.reset(!0)}function values(n){if(n||""===n){var a=n[w];if(a)return a.call(n);if("function"==typeof n.next)return n;if(!isNaN(n.length)){var l=-1,c=function next(){for(;++l<n.length;)if(u.call(n,l))return next.value=n[l],next.done=!1,next;return next.value=o,next.done=!0,next};return c.next=c}}throw new TypeError(i(n)+" is not iterable")}return GeneratorFunction.prototype=GeneratorFunctionPrototype,c($,"constructor",{value:GeneratorFunctionPrototype,configurable:!0}),c(GeneratorFunctionPrototype,"constructor",{value:GeneratorFunction,configurable:!0}),GeneratorFunction.displayName=define(GeneratorFunctionPrototype,x,"GeneratorFunction"),a.isGeneratorFunction=function(n){var o="function"==typeof n&&n.constructor;return!!o&&(o===GeneratorFunction||"GeneratorFunction"===(o.displayName||o.name))},a.mark=function(n){return Object.setPrototypeOf?Object.setPrototypeOf(n,GeneratorFunctionPrototype):(n.__proto__=GeneratorFunctionPrototype,define(n,x,"GeneratorFunction")),n.prototype=Object.create($),n},a.awrap=function(n){return{__await:n}},defineIteratorMethods(AsyncIterator.prototype),define(AsyncIterator.prototype,C,(function(){return this})),a.AsyncIterator=AsyncIterator,a.async=function(n,o,i,l,u){void 0===u&&(u=Promise);var c=new AsyncIterator(wrap(n,o,i,l),u);return a.isGeneratorFunction(o)?c:c.next().then((function(n){return n.done?n.value:c.next()}))},defineIteratorMethods($),define($,x,"Generator"),define($,w,(function(){return this})),define($,"toString",(function(){return"[object Generator]"})),a.keys=function(n){var o=Object(n),a=[];for(var i in o)a.push(i);return a.reverse(),function next(){for(;a.length;){var n=a.pop();if(n in o)return next.value=n,next.done=!1,next}return next.done=!0,next}},a.values=values,Context.prototype={constructor:Context,reset:function reset(n){if(this.prev=0,this.next=0,this.sent=this._sent=o,this.done=!1,this.delegate=null,this.method="next",this.arg=o,this.tryEntries.forEach(resetTryEntry),!n)for(var a in this)"t"===a.charAt(0)&&u.call(this,a)&&!isNaN(+a.slice(1))&&(this[a]=o)},stop:function stop(){this.done=!0;var n=this.tryEntries[0].completion;if("throw"===n.type)throw n.arg;return this.rval},dispatchException:function dispatchException(n){if(this.done)throw n;var a=this;function handle(i,l){return c.type="throw",c.arg=n,a.next=i,l&&(a.method="next",a.arg=o),!!l}for(var i=this.tryEntries.length-1;i>=0;--i){var l=this.tryEntries[i],c=l.completion;if("root"===l.tryLoc)return handle("end");if(l.tryLoc<=this.prev){var p=u.call(l,"catchLoc"),w=u.call(l,"finallyLoc");if(p&&w){if(this.prev<l.catchLoc)return handle(l.catchLoc,!0);if(this.prev<l.finallyLoc)return handle(l.finallyLoc)}else if(p){if(this.prev<l.catchLoc)return handle(l.catchLoc,!0)}else{if(!w)throw new Error("try statement without catch or finally");if(this.prev<l.finallyLoc)return handle(l.finallyLoc)}}}},abrupt:function abrupt(n,o){for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a];if(i.tryLoc<=this.prev&&u.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var l=i;break}}l&&("break"===n||"continue"===n)&&l.tryLoc<=o&&o<=l.finallyLoc&&(l=null);var c=l?l.completion:{};return c.type=n,c.arg=o,l?(this.method="next",this.next=l.finallyLoc,R):this.complete(c)},complete:function complete(n,o){if("throw"===n.type)throw n.arg;return"break"===n.type||"continue"===n.type?this.next=n.arg:"return"===n.type?(this.rval=this.arg=n.arg,this.method="return",this.next="end"):"normal"===n.type&&o&&(this.next=o),R},finish:function finish(n){for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o];if(a.finallyLoc===n)return this.complete(a.completion,a.afterLoc),resetTryEntry(a),R}},catch:function _catch(n){for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o];if(a.tryLoc===n){var i=a.completion;if("throw"===i.type){var l=i.arg;resetTryEntry(a)}return l}}throw new Error("illegal catch attempt")},delegateYield:function delegateYield(n,a,i){return this.delegate={iterator:values(n),resultName:a,nextLoc:i},"next"===this.method&&(this.arg=o),R}},a}n.exports=_regeneratorRuntime,n.exports.__esModule=!0,n.exports.default=n.exports},96196:n=>{function _setPrototypeOf(o,a){return n.exports=_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function _setPrototypeOf(n,o){return n.__proto__=o,n},n.exports.__esModule=!0,n.exports.default=n.exports,_setPrototypeOf(o,a)}n.exports=_setPrototypeOf,n.exports.__esModule=!0,n.exports.default=n.exports},40131:(n,o,a)=>{var i=a(17358),l=a(40608),u=a(35068),c=a(56894);n.exports=function _slicedToArray(n,o){return i(n)||l(n,o)||u(n,o)||c()},n.exports.__esModule=!0,n.exports.default=n.exports},79769:n=>{n.exports=function _taggedTemplateLiteral(n,o){return o||(o=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(o)}}))},n.exports.__esModule=!0,n.exports.default=n.exports},9833:(n,o,a)=>{var i=a(34102),l=a(68),u=a(35068),c=a(91282);n.exports=function _toConsumableArray(n){return i(n)||l(n)||u(n)||c()},n.exports.__esModule=!0,n.exports.default=n.exports},56027:(n,o,a)=>{var i=a(7501).default;n.exports=function toPrimitive(n,o){if("object"!=i(n)||!n)return n;var a=n[Symbol.toPrimitive];if(void 0!==a){var l=a.call(n,o||"default");if("object"!=i(l))return l;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===o?String:Number)(n)},n.exports.__esModule=!0,n.exports.default=n.exports},74040:(n,o,a)=>{var i=a(7501).default,l=a(56027);n.exports=function toPropertyKey(n){var o=l(n,"string");return"symbol"==i(o)?o:String(o)},n.exports.__esModule=!0,n.exports.default=n.exports},7501:n=>{function _typeof(o){return n.exports=_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},n.exports.__esModule=!0,n.exports.default=n.exports,_typeof(o)}n.exports=_typeof,n.exports.__esModule=!0,n.exports.default=n.exports},35068:(n,o,a)=>{var i=a(98106);n.exports=function _unsupportedIterableToArray(n,o){if(n){if("string"==typeof n)return i(n,o);var a=Object.prototype.toString.call(n).slice(8,-1);return"Object"===a&&n.constructor&&(a=n.constructor.name),"Map"===a||"Set"===a?Array.from(n):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?i(n,o):void 0}},n.exports.__esModule=!0,n.exports.default=n.exports},50824:(n,o,a)=>{var i=a(21337)();n.exports=i;try{regeneratorRuntime=i}catch(n){"object"==typeof globalThis?globalThis.regeneratorRuntime=i:Function("r","regeneratorRuntime = r")(i)}}},o={};function __webpack_require__(a){var i=o[a];if(void 0!==i)return i.exports;var l=o[a]={exports:{}};return n[a](l,l.exports,__webpack_require__),l.exports}__webpack_require__.n=n=>{var o=n&&n.__esModule?()=>n.default:()=>n;return __webpack_require__.d(o,{a:o}),o},__webpack_require__.d=(n,o)=>{for(var a in o)__webpack_require__.o(o,a)&&!__webpack_require__.o(n,a)&&Object.defineProperty(n,a,{enumerable:!0,get:o[a]})},__webpack_require__.o=(n,o)=>Object.prototype.hasOwnProperty.call(n,o),__webpack_require__.r=n=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(n,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(n,"__esModule",{value:!0})},__webpack_require__.nc=void 0;(()=>{"use strict";var n=__webpack_require__(73203);var o=n(__webpack_require__(50824)),a=n(__webpack_require__(10029)),i=n(__webpack_require__(78983)),l=n(__webpack_require__(42081)),u=n(__webpack_require__(77266)),c=n(__webpack_require__(58724)),p=n(__webpack_require__(71173)),w=n(__webpack_require__(74910)),C=n(__webpack_require__(93231)),x=n(__webpack_require__(70299)),S=__webpack_require__(36619),O=__webpack_require__(38003),j=__webpack_require__(25455),k=n(__webpack_require__(5637)),R=__webpack_require__(28378);function _createSuper(n){var o=function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(n){return!1}}();return function _createSuperInternal(){var a,i=(0,w.default)(n);if(o){var l=(0,w.default)(this).constructor;a=Reflect.construct(i,arguments,l)}else a=i.apply(this,arguments);return(0,p.default)(this,a)}}var I="ai-attachment";var W=function(n){(0,c.default)(Module,n);var p=_createSuper(Module);function Module(){var n;(0,i.default)(this,Module);for(var l=arguments.length,c=new Array(l),w=0;w<l;w++)c[w]=arguments[w];return n=p.call.apply(p,[this].concat(c)),(0,C.default)((0,u.default)(n),"registerVariationsContextMenu",(function(n,i){var l,u,c=n.find((function(n){return"save"===n.name}));if(!c)return n;var p,w={name:"ai",icon:"eicon-ai",isEnabled:function isEnabled(){return 0!==i.getContainer().children.length},title:(0,O.__)("Generate variations with AI","elementor"),callback:(p=(0,a.default)(o.default.mark((function _callee(){var n,a,l;return o.default.wrap((function _callee$(o){for(;;)switch(o.prev=o.next){case 0:n=i.getContainer(),a=n.model.toJSON({remove:["default"]}),l=[{type:"json",previewHTML:"",content:a,label:n.model.get("title"),source:R.USER_VARIATION_SOURCE}],(0,S.renderLayoutApp)({parentContainer:n.parent,mode:j.MODE_VARIATION,at:n.view._index,attachments:l,onSelect:function onSelect(){n.view.$el.hide()},onClose:function onClose(){n.view.$el.show()},onInsert:function onInsert(o){(0,S.importToEditor)({parentContainer:n.parent,at:n.view._index,template:o,historyTitle:(0,O.__)("AI Variation","elementor"),replace:!0})}});case 4:case"end":return o.stop()}}),_callee)}))),function callback(){return p.apply(this,arguments)})};if(null===(l=ElementorAiConfig)||void 0===l||null===(u=l.usage)||void 0===u||!u.hasAiSubscription){w.shortcut='<div class="elementor-context-menu-list__item__ai-badge">'.concat('\n\t\t\t<svg viewBox="0 0 24 24">\n\t\t\t\t<path d="M12 5.25C12.2508 5.25 12.485 5.37533 12.6241 5.58397L16.1703 10.9033L20.5315 7.41435C20.7777\n\t\t\t\t7.21743 21.1207 7.19544 21.39 7.35933C21.6592 7.52321 21.7973 7.83798 21.7355 8.14709L19.7355\n\t\t\t\t18.1471C19.6654 18.4977 19.3576 18.75 19 18.75H5.00004C4.64253 18.75 4.33472 18.4977 4.26461\n\t\t\t\t18.1471L2.2646 8.14709C2.20278 7.83798 2.34084 7.52321 2.61012 7.35933C2.8794 7.19544 3.22241\n\t\t\t\t7.21743 3.46856 7.41435L7.82977 10.9033L11.376 5.58397C11.5151 5.37533 11.7493 5.25 12 5.25ZM12\n\t\t\t\t7.35208L8.62408 12.416C8.50748 12.5909 8.32282 12.7089 8.1151 12.7411C7.90738 12.7734 7.69566\n\t\t\t\t12.717 7.53152 12.5857L4.13926 9.87185L5.61489 17.25H18.3852L19.8608 9.87185L16.4686 12.5857C16.3044\n\t\t\t\t12.717 16.0927 12.7734 15.885 12.7411C15.6773 12.7089 15.4926 12.5909 15.376 12.416L12 7.35208Z">\n\t\t\t\t</path>\n    \t\t</svg>',"</div>")}return c.actions.unshift(w),n})),n}return(0,l.default)(Module,[{key:"onElementorInit",value:function onElementorInit(){var n=this;elementor.hooks.addFilter("views/add-section/behaviors",this.registerAiLayoutBehavior),elementor.hooks.addFilter("elements/container/contextMenuGroups",this.registerVariationsContextMenu),elementor.hooks.addFilter("elementor/editor/template-library/template/behaviors",this.registerLibraryActionButtonBehavior),elementor.hooks.addFilter("elementor/editor/template-library/template/action-button",this.filterLibraryActionButtonTemplate,11),$e.commands.register("library","generate-ai-variation",(function(o){return n.applyTemplate(o)}))}},{key:"applyTemplate",value:function applyTemplate(n){window.postMessage({type:"library/attach:start"}),$e.components.get("library").downloadTemplate(n,(function(o){var a=n.model;window.postMessage({type:"library/attach",json:o.content[0],html:'<img src="'.concat(a.get("thumbnail"),'" />'),label:"".concat(a.get("template_id")," - ").concat(a.get("title")),source:R.ELEMENTOR_LIBRARY_SOURCE},window.location.origin)}))}},{key:"registerLibraryActionButtonBehavior",value:function registerLibraryActionButtonBehavior(n){return n.applyAiTemplate={behaviorClass:k.default},n}},{key:"registerAiLayoutBehavior",value:function registerAiLayoutBehavior(n){return n.ai={behaviorClass:x.default,context:{documentType:window.elementor.documents.getCurrent().config.type}},n}},{key:"filterLibraryActionButtonTemplate",value:function filterLibraryActionButtonTemplate(n){var o=$e.components.get("library").manager.modalConfig;return"#tmpl-elementor-template-library-insert-button"!==n||"library/templates/blocks"!==$e.routes.current.library?n:n=I===o.mode?"#tmpl-elementor-template-library-apply-ai-button":"#tmpl-elementor-template-library-insert-and-ai-variations-buttons"}}]),Module}(elementorModules.editor.utils.Module);new W})()})();