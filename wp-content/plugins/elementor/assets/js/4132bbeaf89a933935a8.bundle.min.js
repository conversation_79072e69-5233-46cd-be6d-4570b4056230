/*! elementor - v3.14.0 - 26-06-2023 */
"use strict";(self.webpackChunkelementor=self.webpackChunkelementor||[]).push([[684],{97684:(t,r,a)=>{var o=a(73203);Object.defineProperty(r,"__esModule",{value:!0}),r.default=function App(){return u.default.createElement(d.SettingsProvider,null,u.default.createElement(m.default,null,u.default.createElement(p.default,null,u.default.createElement(y.default,null),u.default.createElement(b,null,u.default.createElement(v.default,null),u.default.createElement(g.default,null)))))};var l,i=o(a(79769)),u=o(a(87363)),c=o(a(3924)),d=a(56802),p=o(a(47244)),y=o(a(57447)),v=o(a(4087)),g=o(a(20226)),m=o(a(7250)),b=c.default.div(l||(l=(0,i.default)(["\n\tpadding: 48px 0;\n"])))},7250:(t,r,a)=>{var o=a(23615),l=a(73203);Object.defineProperty(r,"__esModule",{value:!0}),r.default=AppWrapper;var i=l(a(87363)),u=a(56802),c=l(a(24898));function AppWrapper(t){var r=(0,u.useSettings)(),a=r.settings;if(!r.isReady)return i.default.createElement(c.default,null);var o=a.get("config").get("is_debug")?i.default.StrictMode:i.default.Fragment;return i.default.createElement(o,null,t.children)}AppWrapper.propTypes={children:o.oneOfType([o.node,o.arrayOf(o.node)]).isRequired}},74866:(t,r,a)=>{var o=a(73203);Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var l,i=o(a(79769)),u=o(a(3924)).default.h2(l||(l=(0,i.default)(["\n\tcolor: var(--e-a-color-txt);\n\tfont-family: Roboto, sans-serif;\n\tfont-size: 30px;\n\tfont-weight: 400;\n\ttext-transform: capitalize;\n\tfont-style: normal;\n\ttext-decoration: none;\n\tletter-spacing: 0;\n\tword-spacing: 0;\n\ttext-align: center;\n\tpadding: 0;\n\tmargin: 0 0 48px 0;\n"])));r.default=u},10658:(t,r,a)=>{var o=a(23615),l=a(73203);Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var i,u=l(a(79769)),c=l(a(87363)),d=l(a(3924)),p=a(56802),y=l(a(24898)),v=l(a(70282)),g=l(a(74866)),m=l(a(96238)),b=(0,d.default)(v.default)(i||(i=(0,u.default)(["\n\twidth: 100%;\n  \tpadding-top: 96px;\n\tmin-height: 100px;\n\n\t@media (max-width: 1024px) {\n      \tpadding-top: 50px;\n\t}\n"]))),h=c.default.forwardRef((function(t,r){var a=t.config,o=(0,p.useSettings)(),l=o.settings,i=o.isReady;return c.default.createElement(b,{ref:r},c.default.createElement(g.default,{name:a.type},a.title),i?c.default.createElement(c.default.Fragment,null,a.sections.map((function(t){var r=l.get(a.type).get(t.type);return r.length?c.default.createElement(m.default,{key:t.type,title:t.title,items:r,columns:t.columns,component:a.component,type:t.type}):null}))):c.default.createElement(y.default,null))}));h.propTypes={config:o.shape({type:o.string.isRequired,title:o.string.isRequired,sections:o.arrayOf(o.shape({type:o.string.isRequired,title:o.string.isRequired,columns:o.object})).isRequired,component:o.func.isRequired}).isRequired};var _=h;r.default=_},4087:(t,r,a)=>{var o=a(38003).__,l=a(73203);Object.defineProperty(r,"__esModule",{value:!0}),r.default=function ColorsArea(){var t=(0,u.useActiveContext)().colorsAreaRef,r={title:o("Global Colors","elementor"),type:"colors",component:d.default,sections:[{type:"system_colors",title:o("System Colors","elementor"),columns:{desktop:4,mobile:2}},{type:"custom_colors",title:o("Custom Colors","elementor"),columns:{desktop:6,mobile:2}}]};return i.default.createElement(c.default,{ref:t,config:r})};var i=l(a(87363)),u=a(47244),c=l(a(10658)),d=l(a(59207))},20226:(t,r,a)=>{var o=a(38003).__,l=a(73203);Object.defineProperty(r,"__esModule",{value:!0}),r.default=function FontsArea(){var t=(0,u.useActiveContext)().fontsAreaRef,r={title:o("Global Fonts","elementor"),type:"fonts",component:d.default,sections:[{type:"system_typography",title:o("System Fonts","elementor"),flex:"column",columns:{desktop:1,mobile:1}},{type:"custom_typography",title:o("Custom Fonts","elementor"),flex:"column",columns:{desktop:1,mobile:1}}]};return i.default.createElement(c.default,{ref:t,config:r})};var i=l(a(87363)),u=a(47244),c=l(a(10658)),d=l(a(32259))},70282:(t,r,a)=>{var o=a(73203);Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var l,i=o(a(79769)),u=o(a(3924)).default.div(l||(l=(0,i.default)(["\n\tbox-sizing: border-box;\n\tposition: relative;\n"])));r.default=u},13746:(t,r,a)=>{var o=a(73203);Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var l,i=o(a(79769)),u=o(a(3924)).default.p(l||(l=(0,i.default)(["\n\tcolor: var(--e-a-color-txt);\n\tfont-family: Roboto, sans-serif;\n\tfont-size: 12px;\n\tfont-weight: 500;\n\ttext-transform: capitalize;\n\tfont-style: normal;\n\ttext-decoration: none;\n\tline-height: 1.1em;\n\tletter-spacing: 0;\n\tword-spacing: 0;\n\tpadding: 0;\n\tmargin: 0;\n"])));r.default=u},30879:(t,r,a)=>{var o=a(23615),l=a(73203),i=a(7501);Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var u,c,d,p=l(a(73119)),y=l(a(79769)),v=l(a(87363)),g=function _interopRequireWildcard(t,r){if(!r&&t&&t.__esModule)return t;if(null===t||"object"!==i(t)&&"function"!=typeof t)return{default:t};var a=_getRequireWildcardCache(r);if(a&&a.has(t))return a.get(t);var o={},l=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var u in t)if("default"!==u&&Object.prototype.hasOwnProperty.call(t,u)){var c=l?Object.getOwnPropertyDescriptor(t,u):null;c&&(c.get||c.set)?Object.defineProperty(o,u,c):o[u]=t[u]}o.default=t,a&&a.set(t,o);return o}(a(3924)),m=l(a(70282));function _getRequireWildcardCache(t){if("function"!=typeof WeakMap)return null;var r=new WeakMap,a=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(t){return t?a:r})(t)}var b=(0,g.default)(m.default)(u||(u=(0,y.default)(["\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 12px;\n\talign-items: flex-start;\n\tborder: 1px solid transparent;\n\tborder-radius: 3px;\n\tpadding: 12px;\n\tcursor: pointer;\n\t","\n\n\t&:hover:not(.active) {\n\t\tbackground-color: var(--e-a-bg-hover);\n\t\tborder-color: var(--e-a-border-color-bold);\n\t}\n\n\t&.active {\n\t\tbackground-color: var(--e-a-bg-active);\n\t\tborder-color: var(--e-a-border-color-accent);\n\t}\n\n\t@media (max-width: 767px) {\n\t\t","\n\t}\n"])),(function(t){var r,a=100/(null!==(r=t.columns.desktop)&&void 0!==r?r:1);return(0,g.css)(c||(c=(0,y.default)(["\n\t\t\tflex: 0 0 ","%;\n\t\t"])),a)}),(function(t){var r,a=100/(null!==(r=t.columns.mobile)&&void 0!==r?r:1);return(0,g.css)(d||(d=(0,y.default)(["\n\t\t\t\tflex: 0 0 ","%;\n\t\t\t"])),a)})),h=v.default.forwardRef((function(t,r){var a=t.isActive,o=t.children;return v.default.createElement(b,(0,p.default)({},t,{ref:r,className:a?"active":""}),o)})),_=h;r.default=_,h.propTypes={isActive:o.bool,children:o.oneOfType([o.node,o.arrayOf(o.node)])}},22022:(t,r,a)=>{var o=a(73203);Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var l,i=o(a(79769)),u=o(a(3924)),c=o(a(70282)),d=(0,u.default)(c.default)(l||(l=(0,i.default)(["\n\tdisplay: flex;\n\talign-items: center;\n\twidth: 100%;\n\tmax-width: 1140px;\n\tmargin: auto;\n\tflex-wrap: wrap;\n\tflex-direction: ",";\n\n\t@media (max-width: 1140px) {\n\t\tpadding: 0 15px;\n\t}\n\n\t@media (max-width: 767px) {\n\t\tpadding: 0 13px;\n\t}\n"])),(function(t){var r;return null!==(r=t.flexDirection)&&void 0!==r?r:"row"}));r.default=d},24898:(t,r,a)=>{var o=a(73203);Object.defineProperty(r,"__esModule",{value:!0}),r.default=function Loader(){return l.default.createElement("div",{className:"e-styleguide-loader"},l.default.createElement("i",{className:"eicon-loading eicon-animation-spin"}))};var l=o(a(87363))},57447:(t,r,a)=>{var o=a(38003).__,l=a(23615),i=a(73203);Object.defineProperty(r,"__esModule",{value:!0}),r.default=function Header(){return v.default.createElement(O,null,v.default.createElement(h.default,null,v.default.createElement(P,null,o("Style Guide Preview","elementor")),v.default.createElement(x,null,v.default.createElement(w,{area:"colors"},o("Colors","elementor")),v.default.createElement(w,{area:"fonts"},o("Fonts","elementor")))))};var u,c,d,p,y=i(a(79769)),v=i(a(87363)),g=i(a(3924)),m=a(47244),b=i(a(70282)),h=i(a(22022)),_=g.default.button.attrs((function(t){return{"data-e-active":!!t.isActive||null}}))(u||(u=(0,y.default)(["\n\tfont-size: 16px;\n\theight: 100%;\n\tfont-weight: 500;\n\tfont-style: normal;\n\ttext-decoration: none;\n\tline-height: 1.5em;\n\tletter-spacing: 0;\n\tcolor: var(--e-a-color-txt);\n\tborder: none;\n\tbackground: none;\n\ttext-transform: capitalize;\n\tfont-family: Roboto, sans-serif;\n\tpadding: 0;\n\n\t&:hover, &[data-e-active='true'], &:focus {\n\t\toutline: none;\n\t\tbackground: none;\n\t\tcolor: var(--e-a-color-txt-accent);\n\t}\n"]))),w=function AreaButton(t){var r=(0,m.useActiveContext)(),a=r.activeArea,o=r.activateArea,l=t.area,i=t.children;return v.default.createElement(_,{variant:"transparent",size:"s",onClick:function onClick(){o(l)},isActive:l===a},i)},O=(0,g.default)(b.default)(c||(c=(0,y.default)(["\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\twidth: 100%;\n\theight: 48px;\n\tdisplay: flex;\n\tbackground: var(--e-a-bg-default);\n\tborder-bottom: 1px solid var(--e-a-border-color-bold);\n\tz-index: 1;\n"]))),x=(0,g.default)(b.default)(d||(d=(0,y.default)(["\n\tdisplay: flex;\n\tjustify-content: flex-end;\n\tflex-grow: 1;\n\tgap: 20px;\n"]))),P=g.default.h2(p||(p=(0,y.default)(["\n\tcolor: var(--e-a-color-txt-accent);\n\tfont-family: Roboto, sans-serif;\n\tfont-size: 16px;\n\tfont-weight: 600;\n\ttext-transform: capitalize;\n\tfont-style: normal;\n\ttext-decoration: none;\n\tline-height: 1.2em;\n\tletter-spacing: 0;\n\tword-spacing: 0;\n\tmargin: 0;\n"])));w.propTypes={area:l.string.isRequired,children:l.node.isRequired}},59207:(t,r,a)=>{var o=a(23615),l=a(73203),i=a(7501);Object.defineProperty(r,"__esModule",{value:!0}),r.default=Color;var u,c,d=l(a(79769)),p=function _interopRequireWildcard(t,r){if(!r&&t&&t.__esModule)return t;if(null===t||"object"!==i(t)&&"function"!=typeof t)return{default:t};var a=_getRequireWildcardCache(r);if(a&&a.has(t))return a.get(t);var o={},l=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var u in t)if("default"!==u&&Object.prototype.hasOwnProperty.call(t,u)){var c=l?Object.getOwnPropertyDescriptor(t,u):null;c&&(c.get||c.set)?Object.defineProperty(o,u,c):o[u]=t[u]}o.default=t,a&&a.set(t,o);return o}(a(87363)),y=l(a(3924)),v=l(a(70282)),g=l(a(13746)),m=l(a(30879)),b=a(47244);function _getRequireWildcardCache(t){if("function"!=typeof WeakMap)return null;var r=new WeakMap,a=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(t){return t?a:r})(t)}var h=(0,y.default)(v.default)(u||(u=(0,d.default)(["\n\tdisplay: flex;\n\twidth: 100%;\n\theight: 100px;\n\tbackground-color: ",";\n\tborder: 1px solid var(--e-a-border-color-focus);\n\tborder-radius: 3px;\n\talign-items: end;\n"])),(function(t){return t.hex})),_=y.default.p(c||(c=(0,d.default)(["\n\tcolor: var(--e-a-color-txt-invert);\n\tfont-family: Roboto, sans-serif;\n\theight: 12px;\n\tfont-size: 12px;\n\tfont-weight: 500;\n\ttext-transform: uppercase;\n\tfont-style: normal;\n\ttext-decoration: none;\n\tline-height: 1.1em;\n\tletter-spacing: 0;\n\tword-spacing: 0;\n\tmargin: 12px;\n"])));function Color(t){var r=(0,b.useActiveContext)(),a=r.activeElement,o=r.activateElement,l=r.getElementControl,i=t.item,u=t.type,c="color",d=i._id,y=i.title,v=i.color,w=l(u,c,d),O=(0,p.useRef)(null);(0,p.useEffect)((function(){w===a&&O.current.scrollIntoView({behavior:"smooth",block:"center",inline:"center"})}),[a]);return p.default.createElement(m.default,{columns:t.columns,ref:O,isActive:w===a,onClick:function onClick(){o(u,c,d)}},p.default.createElement(g.default,null,y),p.default.createElement(h,{hex:v},p.default.createElement(_,null,v)))}Color.propTypes={item:o.shape({_id:o.string.isRequired,title:o.string.isRequired,color:o.string}).isRequired,type:o.string.isRequired,columns:o.shape({desktop:o.number,mobile:o.number})}},32259:(t,r,a)=>{var o=a(38003).__,l=a(23615),i=a(73203),u=a(7501);Object.defineProperty(r,"__esModule",{value:!0}),r.default=Font;var c,d,p=i(a(79769)),y=function _interopRequireWildcard(t,r){if(!r&&t&&t.__esModule)return t;if(null===t||"object"!==u(t)&&"function"!=typeof t)return{default:t};var a=_getRequireWildcardCache(r);if(a&&a.has(t))return a.get(t);var o={},l=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in t)if("default"!==i&&Object.prototype.hasOwnProperty.call(t,i)){var c=l?Object.getOwnPropertyDescriptor(t,i):null;c&&(c.get||c.set)?Object.defineProperty(o,i,c):o[i]=t[i]}o.default=t,a&&a.set(t,o);return o}(a(87363)),v=i(a(3924)),g=a(47244),m=a(56802),b=i(a(30879)),h=i(a(13746));function _getRequireWildcardCache(t){if("function"!=typeof WeakMap)return null;var r=new WeakMap,a=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(t){return t?a:r})(t)}var _=(0,v.default)(h.default)(c||(c=(0,p.default)(["\n\tfont-size: 18px;\n"]))),w=v.default.p.withConfig({shouldForwardProp:function shouldForwardProp(t){return"style"!==t}})(d||(d=(0,p.default)(["\n\t",";\n"])),(function(t){var r=t.style,a=function styleObjectToString(t){return Object.keys(t).reduce((function(r,a){return r+"".concat(a,": ").concat(t[a],";")}),"")};return"\n\t\t\t".concat(a(r.style),"\n\n\t\t\t@media (max-width: 1024px) {\n\t\t\t\t").concat(a(r.tablet),"\n\t\t\t}\n\n\t\t\t@media (max-width: 767px) {\n\t\t\t\t").concat(a(r.mobile),"\n\t\t\t}\n\t\t")})),O=function parseFontToStyle(t,r){var a=function defaultKeyParser(t){return t.replace("typography_","").replace("_","-")},o=r.toLowerCase(),l=function sizeParser(t){return t&&t.size?"".concat(t.size).concat(t.unit):""},i=function defaultParser(t){return t},u={typography_font_family:{valueParser:function familyParser(t){return t?t+", ".concat(o):o},keyParser:a},typography_font_size:{valueParser:l,keyParser:a},typography_letter_spacing:{valueParser:l,keyParser:a},typography_line_height:{valueParser:l,keyParser:a},typography_word_spacing:{valueParser:l,keyParser:a},typography_font_style:{valueParser:i,keyParser:a},typography_font_weight:{valueParser:i,keyParser:a},typography_text_transform:{valueParser:i,keyParser:a},typography_text_decoration:{valueParser:i,keyParser:a}},c=["typography_font_size","typography_letter_spacing","typography_line_height","typography_word_spacing"],d=function reducer(r,a,o){var l=u[a],i=l.keyParser(a),c=a+(o?"_"+o:""),d=l.valueParser(t[c]);return d&&(r[i]=d),r};return{style:Object.keys(u).reduce((function(t,r){return d(t,r,"")}),{}),tablet:c.reduce((function(t,r){return d(t,r,"tablet")}),{}),mobile:c.reduce((function(t,r){return d(t,r,"mobile")}),{})}};function Font(t){var r=(0,g.useActiveContext)(),a=r.activeElement,l=r.activateElement,i=r.getElementControl,u=t.item,c=t.type,d="typography",p=u._id,v=u.title,h=i(c,d,p),x=(0,y.useRef)(null),P=(0,m.useSettings)(),j=P.settings,R=P.isReady,k=(0,y.useMemo)((function(){return R?O(u,j.get("fonts").get("fallback_font")):""}),[u,j]);return(0,y.useEffect)((function(){h===a&&x.current.scrollIntoView({behavior:"smooth",block:"center",inline:"center"})}),[a]),y.default.createElement(b.default,{columns:t.columns,ref:x,isActive:h===a,onClick:function onClick(){l(c,d,p)}},y.default.createElement(_,null,v),y.default.createElement(w,{style:k},o("The five boxing wizards jump quickly.","elementor")))}Font.propTypes={item:l.shape({_id:l.string.isRequired,title:l.string.isRequired,color:l.string}).isRequired,type:l.string.isRequired,columns:l.shape({desktop:l.number,mobile:l.number})}},32431:(t,r,a)=>{var o=a(73203);Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var l,i=o(a(79769)),u=o(a(3924)).default.h3(l||(l=(0,i.default)(["\n\tpadding: 16px 12px;\n\tborder-style: solid;\n\tborder-width: 0 0 1px 0;\n\tborder-color: var(--e-a-border-color-bold);\n\tcolor: var(--e-a-color-txt);\n\tfont-family: Roboto, sans-serif;\n\tfont-size: 16px;\n\tfont-weight: 500;\n\ttext-transform: capitalize;\n\tfont-style: normal;\n\ttext-decoration: none;\n\tline-height: 1.5em;\n\tletter-spacing: 0;\n\tword-spacing: 0;\n\tmargin: 0 auto 25px;\n\twidth: 100%;\n\tmax-width: 1140px;\n"])));r.default=u},96238:(t,r,a)=>{var o=a(23615),l=a(73203),i=a(7501);Object.defineProperty(r,"__esModule",{value:!0}),r.default=Section;var u,c,d,p=l(a(79769)),y=l(a(87363)),v=function _interopRequireWildcard(t,r){if(!r&&t&&t.__esModule)return t;if(null===t||"object"!==i(t)&&"function"!=typeof t)return{default:t};var a=_getRequireWildcardCache(r);if(a&&a.has(t))return a.get(t);var o={},l=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var u in t)if("default"!==u&&Object.prototype.hasOwnProperty.call(t,u)){var c=l?Object.getOwnPropertyDescriptor(t,u):null;c&&(c.get||c.set)?Object.defineProperty(o,u,c):o[u]=t[u]}o.default=t,a&&a.set(t,o);return o}(a(3924)),g=l(a(32431)),m=l(a(70282)),b=l(a(22022));function _getRequireWildcardCache(t){if("function"!=typeof WeakMap)return null;var r=new WeakMap,a=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(t){return t?a:r})(t)}var h=(0,v.default)(m.default)(u||(u=(0,p.default)(["\n\tmargin-top: 55px;\n"]))),_=(0,v.default)(m.default)(c||(c=(0,p.default)(["\n\tdisplay: flex;\n\twidth: 100%;\n\n\t",";\n"])),(function(t){var r=t.flex;return r&&(0,v.css)(d||(d=(0,p.default)(["\n\t\tflex-direction: ",";\n\t\tflex-wrap: ",";\n\t"])),"column"===r?"column":"row","column"===r?"nowrap":"wrap")}));function Section(t){var r=t.title,a=t.items,o=t.columns,l=t.component,i=t.type,u=t.flex,c=void 0===u?"row":u;return y.default.createElement(h,null,y.default.createElement(g.default,null,r),y.default.createElement(b.default,null,y.default.createElement(_,{flex:c},a.map((function(t){return y.default.createElement(l,{key:t._id,item:t,type:i||null,columns:o})})))))}Section.propTypes={title:o.string.isRequired,items:o.array.isRequired,columns:o.shape({desktop:o.number,mobile:o.number}),component:o.func.isRequired,type:o.string,flex:o.oneOf(["row","column"])}},47244:(t,r,a)=>{var o=a(73203),l=a(7501);Object.defineProperty(r,"__esModule",{value:!0}),r.default=r.ActiveContext=void 0,r.useActiveContext=function useActiveContext(){return(0,d.useContext)(v)};var i=o(a(73119)),u=o(a(93231)),c=o(a(40131)),d=function _interopRequireWildcard(t,r){if(!r&&t&&t.__esModule)return t;if(null===t||"object"!==l(t)&&"function"!=typeof t)return{default:t};var a=_getRequireWildcardCache(r);if(a&&a.has(t))return a.get(t);var o={},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var u in t)if("default"!==u&&Object.prototype.hasOwnProperty.call(t,u)){var c=i?Object.getOwnPropertyDescriptor(t,u):null;c&&(c.get||c.set)?Object.defineProperty(o,u,c):o[u]=t[u]}o.default=t,a&&a.set(t,o);return o}(a(87363)),p=a(56802),y=o(a(42762));function _getRequireWildcardCache(t){if("function"!=typeof WeakMap)return null;var r=new WeakMap,a=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(t){return t?a:r})(t)}function ownKeys(t,r){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);r&&(o=o.filter((function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable}))),a.push.apply(a,o)}return a}function _objectSpread(t){for(var r=1;r<arguments.length;r++){var a=null!=arguments[r]?arguments[r]:{};r%2?ownKeys(Object(a),!0).forEach((function(r){(0,u.default)(t,r,a[r])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):ownKeys(Object(a)).forEach((function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(a,r))}))}return t}var v=(0,d.createContext)(null);r.ActiveContext=v;var g=function ActiveProvider(t){var r=(0,d.useState)({element:"",area:""}),a=(0,c.default)(r,2),o=a[0],l=a[1],u=(0,d.useRef)(null),g=(0,d.useRef)(null),m=(0,p.useSettings)().isReady,b=(0,y.default)((function(t){u.current!==t.target?g.current===t.target&&h("fonts",{scroll:!1}):h("colors",{scroll:!1})})).setObservedElements,h=function activateArea(t){var r=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}).scroll;(void 0===r||r)&&_(t),l((function(r){return _objectSpread(_objectSpread({},r),{},{area:t})}))},_=function scrollToArea(t){("colors"===t?u:g).current.scrollIntoView({behavior:"smooth",block:"start",inline:"start"})};(0,d.useEffect)((function(){window.top.$e.routes.is("panel/global/global-colors")&&_("colors"),window.top.$e.routes.is("panel/global/global-typography")&&_("fonts")}),[]),(0,d.useEffect)((function(){m&&(b([u.current,g.current]),window.top.$e.routes.on("run:after",(function(t,r,a){"panel/global/global-typography"===r&&l((function(){return{area:"fonts",element:a.activeControl}})),"panel/global/global-colors"===r&&l((function(){return{area:"colors",element:a.activeControl}}))})))}),[m]);var w={activeElement:o.element,activeArea:o.area,activateElement:function activateElement(t,r,a){"color"===r&&window.top.$e.route("panel/global/global-colors",{activeControl:"".concat(t,"/").concat(a,"/color")},{history:!1}),"typography"===r&&window.top.$e.route("panel/global/global-typography",{activeControl:"".concat(t,"/").concat(a,"/typography_typography")},{history:!1})},activateArea:h,colorsAreaRef:u,fontsAreaRef:g,getElementControl:function getElementControl(t,r,a){return"color"===r?"".concat(t,"/").concat(a,"/color"):"typography"===r?"".concat(t,"/").concat(a,"/typography_typography"):void 0}};return d.default.createElement(v.Provider,(0,i.default)({value:w},t))};r.default=g},56802:(t,r,a)=>{var o=a(73203),l=a(7501);Object.defineProperty(r,"__esModule",{value:!0}),r.useSettings=r.SettingsProvider=void 0;var i=function _interopRequireWildcard(t,r){if(!r&&t&&t.__esModule)return t;if(null===t||"object"!==l(t)&&"function"!=typeof t)return{default:t};var a=_getRequireWildcardCache(r);if(a&&a.has(t))return a.get(t);var o={},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var u in t)if("default"!==u&&Object.prototype.hasOwnProperty.call(t,u)){var c=i?Object.getOwnPropertyDescriptor(t,u):null;c&&(c.get||c.set)?Object.defineProperty(o,u,c):o[u]=t[u]}o.default=t,a&&a.set(t,o);return o}(a(87363)),u=o(a(73119)),c=o(a(9833)),d=o(a(93231)),p=o(a(40131)),y=o(a(94539));function _getRequireWildcardCache(t){if("function"!=typeof WeakMap)return null;var r=new WeakMap,a=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(t){return t?a:r})(t)}function ownKeys(t,r){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);r&&(o=o.filter((function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable}))),a.push.apply(a,o)}return a}function _objectSpread(t){for(var r=1;r<arguments.length;r++){var a=null!=arguments[r]?arguments[r]:{};r%2?ownKeys(Object(a),!0).forEach((function(r){(0,d.default)(t,r,a[r])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):ownKeys(Object(a)).forEach((function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(a,r))}))}return t}function _createForOfIteratorHelper(t,r){var a="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!a){if(Array.isArray(t)||(a=function _unsupportedIterableToArray(t,r){if(!t)return;if("string"==typeof t)return _arrayLikeToArray(t,r);var a=Object.prototype.toString.call(t).slice(8,-1);"Object"===a&&t.constructor&&(a=t.constructor.name);if("Map"===a||"Set"===a)return Array.from(t);if("Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a))return _arrayLikeToArray(t,r)}(t))||r&&t&&"number"==typeof t.length){a&&(t=a);var o=0,l=function F(){};return{s:l,n:function n(){return o>=t.length?{done:!0}:{done:!1,value:t[o++]}},e:function e(t){throw t},f:l}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,u=!0,c=!1;return{s:function s(){a=a.call(t)},n:function n(){var t=a.next();return u=t.done,t},e:function e(t){c=!0,i=t},f:function f(){try{u||null==a.return||a.return()}finally{if(c)throw i}}}}function _arrayLikeToArray(t,r){(null==r||r>t.length)&&(r=t.length);for(var a=0,o=new Array(r);a<r;a++)o[a]=t[a];return o}var v=(0,i.createContext)(null);r.useSettings=function useSettings(){return(0,i.useContext)(v)};r.SettingsProvider=function SettingsProvider(t){var r=(0,i.useState)("idle"),a=(0,p.default)(r,2),o=a[0],l=a[1],d=(0,i.useState)(new Map),g=(0,p.default)(d,2),m=g[0],b=g[1],h=(0,i.useRef)(m),_=function setSettings(t){h.current=t,b(t)};(0,i.useEffect)((function(){l("loaded")}),[m]);var w=(0,i.useCallback)((function(t){switch(t.detail.command){case"document/elements/settings":O(t.detail.args);break;case"document/repeater/insert":x(t.detail.args);break;case"document/repeater/remove":P(t.detail.args)}}),[]),O=(0,y.default)((function(t){var r,a=t.container.model.attributes.name,o=new Map(h.current),l=_createForOfIteratorHelper(o.entries());try{for(l.s();!(r=l.n()).done;){var i=(0,p.default)(r.value,2),u=i[0],c=i[1];if(c.has(a))if(Array.isArray(c.get(a))){var d=c.get(a).findIndex((function(r){return r._id===t.container.id}));if(-1===d)return;o.get(u).get(a)[d]=_objectSpread(_objectSpread({},c.get(a)[d]),t.settings)}else o.get(u).set(a,t.settings)}}catch(t){l.e(t)}finally{l.f()}_(o)}),100),x=function onInsert(t){var r,a=t.name,o=new Map(h.current),l=_createForOfIteratorHelper(o.entries());try{for(l.s();!(r=l.n()).done;){var i,u=(0,p.default)(r.value,2),d=u[0],y=u[1];if(y.has(a)){var v=(0,c.default)(y.get(a)),g=void 0===(null===(i=t.options)||void 0===i?void 0:i.at)?v.length:t.options.at;o.get(d).set(a,[].concat((0,c.default)(v.slice(0,g)),[t.model],(0,c.default)(v.slice(g))))}}}catch(t){l.e(t)}finally{l.f()}_(o)},P=function onRemove(t){var r,a=t.name,o=new Map(h.current),l=_createForOfIteratorHelper(o.entries());try{for(l.s();!(r=l.n()).done;){var i=(0,p.default)(r.value,2),u=i[0],d=i[1];if(d.has(a)){var y=(0,c.default)(d.get(a));o.get(u).set(a,y.filter((function(r,a){return a!==t.index})))}}}catch(t){l.e(t)}finally{l.f()}_(o)};(0,i.useEffect)((function(){return function getInitialSettings(){l("loading");var t=elementor.documents.getCurrent().config.settings.settings,r=new Map([["colors",new Map([["system_colors",t.system_colors],["custom_colors",t.custom_colors]])],["fonts",new Map([["system_typography",t.system_typography],["custom_typography",t.custom_typography],["fallback_font",t.default_generic_fonts]])],["config",new Map([["is_debug",elementorCommon.config.isElementorDebug]])]]);_(r)}(),window.top.addEventListener("elementor/commands/run/after",w,{passive:!0}),function(){window.top.removeEventListener("elementor/commands/run/after",w)}}),[]);var j={settings:m,isReady:"loaded"===o};return i.default.createElement(v.Provider,(0,u.default)({value:j},t))}},94539:(t,r,a)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.default=function useDebouncedCallback(t,r){var a=(0,o.useRef)();return(0,o.useCallback)((function(){for(var o=arguments.length,l=new Array(o),i=0;i<o;i++)l[i]=arguments[i];clearTimeout(a.current),a.current=setTimeout((function later(){clearTimeout(a.current),t.apply(void 0,l)}),r)}),[t,r])};var o=a(87363)},42762:(t,r,a)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.default=function useIntersectionObserver(t){var r,a=[];(0,o.useEffect)((function(){return r=new IntersectionObserver((function(r){var a=r.find((function(t){return t.isIntersecting}));a&&t(a)}),{}),function(){r.disconnect()}}),[]);return{setObservedElements:function setObservedElements(t){!function unobserve(){0!==a.length&&a.forEach((function(t){t&&r.unobserve(t)}))}(),a=t,function observe(){0!==a.length&&a.forEach((function(t){t&&r.observe(t)}))}()}}};var o=a(87363)}}]);