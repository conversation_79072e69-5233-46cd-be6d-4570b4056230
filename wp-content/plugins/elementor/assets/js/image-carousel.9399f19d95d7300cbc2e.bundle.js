/*! elementor - v3.23.0 - 05-08-2024 */
"use strict";
(self["webpackChunkelementor"] = self["webpackChunkelementor"] || []).push([["image-carousel"],{

/***/ "../assets/dev/js/frontend/handlers/image-carousel.js":
/*!************************************************************!*\
  !*** ../assets/dev/js/frontend/handlers/image-carousel.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, exports) => {



Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = void 0;
class ImageCarousel extends elementorModules.frontend.handlers.CarouselBase {
  getDefaultSettings() {
    const settings = super.getDefaultSettings();
    settings.selectors.carousel = '.elementor-image-carousel-wrapper';
    return settings;
  }
}
exports["default"] = ImageCarousel;

/***/ })

}]);
//# sourceMappingURL=image-carousel.9399f19d95d7300cbc2e.bundle.js.map