/*! elementor - v3.23.0 - 05-08-2024 */
(()=>{var e={7033:(e,t,o)=>{"use strict";var r=o(73203),n=o(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var u=r(o(78983)),a=r(o(42081)),i=r(o(58724)),s=r(o(71173)),l=r(o(74910)),c=_interopRequireWildcard(o(44171)),f=_interopRequireWildcard(o(14606));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,o=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?o:t})(e)}function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==n(e)&&"function"!=typeof e)return{default:e};var o=_getRequireWildcardCache(t);if(o&&o.has(e))return o.get(e);var r={},u=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var i=u?Object.getOwnPropertyDescriptor(e,a):null;i&&(i.get||i.set)?Object.defineProperty(r,a,i):r[a]=e[a]}return r.default=e,o&&o.set(e,r),r}function _createSuper(e){var t=function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function _createSuperInternal(){var o,r=(0,l.default)(e);if(t){var n=(0,l.default)(this).constructor;o=Reflect.construct(r,arguments,n)}else o=r.apply(this,arguments);return(0,s.default)(this,o)}}var p=function(e){(0,i.default)(EComponent,e);var t=_createSuper(EComponent);function EComponent(e){var o;return(0,u.default)(this,EComponent),(o=t.call(this,e)).loadModules(),o}return(0,a.default)(EComponent,[{key:"getNamespace",value:function getNamespace(){return"notes"}},{key:"defaultHooks",value:function defaultHooks(){return this.importHooks(f)}},{key:"loadModules",value:function loadModules(){for(var e in c)new c[e]}}]),EComponent}($e.modules.ComponentBase);t.default=p},14606:(e,t,o)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"NotesAddPanelMenuItem",{enumerable:!0,get:function get(){return r.NotesAddPanelMenuItem}});var r=o(36938)},36938:(e,t,o)=>{"use strict";var r=o(38003).__,n=o(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.NotesAddPanelMenuItem=void 0;var u=n(o(78983)),a=n(o(42081)),i=n(o(58724)),s=n(o(71173)),l=n(o(74910));function _createSuper(e){var t=function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function _createSuperInternal(){var o,r=(0,l.default)(e);if(t){var n=(0,l.default)(this).constructor;o=Reflect.construct(r,arguments,n)}else o=r.apply(this,arguments);return(0,s.default)(this,o)}}var c=function(e){(0,i.default)(NotesAddPanelMenuItem,e);var t=_createSuper(NotesAddPanelMenuItem);function NotesAddPanelMenuItem(){return(0,u.default)(this,NotesAddPanelMenuItem),t.apply(this,arguments)}return(0,a.default)(NotesAddPanelMenuItem,[{key:"getCommand",value:function getCommand(){return"panel/state-ready"}},{key:"getId",value:function getId(){return"notes-add-panel-menu-item"}},{key:"apply",value:function apply(){elementor.modules.layouts.panel.pages.menu.Menu.addItem({name:"notes",icon:"eicon-commenting-o",title:r("Notes","elementor")+'<i class="elementor-panel-menu-item-title-badge eicon-pro-icon"></i>',callback:function callback(){var e=elementor.helpers.hasProAndNotConnected();elementor.promotion.showDialog({title:r("Notes","elementor"),content:r("With Notes, teamwork gets even better. Stay in sync with comments, feedback & more on your website.","elementor"),position:{blockStart:"-3",inlineStart:"+10"},targetElement:this.$el,actionButton:{url:e?elementorProEditorConfig.urls.connect:elementor.config.promotions.notes.upgrade_url||"https://go.elementor.com/go-pro-notes/",text:r(e?"Connect & Activate":"Upgrade","elementor")}})}},"navigate_from_page","finder")}}]),NotesAddPanelMenuItem}($e.modules.hookUI.After);t.NotesAddPanelMenuItem=c;var f=c;t.default=f},65419:(e,t,o)=>{"use strict";var r=o(38003).__,n=o(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.NotesContextMenu=void 0;var u=n(o(78983)),a=n(o(42081)),i=n(o(58724)),s=n(o(71173)),l=n(o(74910));function _createSuper(e){var t=function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function _createSuperInternal(){var o,r=(0,l.default)(e);if(t){var n=(0,l.default)(this).constructor;o=Reflect.construct(r,arguments,n)}else o=r.apply(this,arguments);return(0,s.default)(this,o)}}var c=function(e){(0,i.default)(NotesContextMenu,e);var t=_createSuper(NotesContextMenu);function NotesContextMenu(){return(0,u.default)(this,NotesContextMenu),t.apply(this,arguments)}return(0,a.default)(NotesContextMenu,[{key:"onInit",value:function onInit(){this.contextMenuNotesGroup()}},{key:"contextMenuNotesGroup",value:function contextMenuNotesGroup(){var e=this;["widget","section","column","container"].forEach((function(t){elementor.hooks.addFilter("elements/".concat(t,"/contextMenuGroups"),e.contextMenuAddGroup)}))}},{key:"contextMenuAddGroup",value:function contextMenuAddGroup(e){var t=_.findWhere(e,{name:"delete"}),o=e.indexOf(t);return-1===o&&(o=e.length),e.splice(o,0,{name:"notes",actions:[{name:"open_notes",title:r("Notes","elementor"),shortcut:'<i class="eicon-pro-icon"></i>',promotionURL:"https://go.elementor.com/go-pro-notes-context-menu/",isEnabled:function isEnabled(){return!1},callback:function callback(){}}]}),e}}]),NotesContextMenu}(elementorModules.editor.utils.Module);t.NotesContextMenu=c;var f=c;t.default=f},44171:(e,t,o)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"NotesContextMenu",{enumerable:!0,get:function get(){return r.NotesContextMenu}});var r=o(65419)},38003:e=>{"use strict";e.exports=wp.i18n},77266:e=>{e.exports=function _assertThisInitialized(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e},e.exports.__esModule=!0,e.exports.default=e.exports},78983:e=>{e.exports=function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},e.exports.__esModule=!0,e.exports.default=e.exports},42081:(e,t,o)=>{var r=o(74040);function _defineProperties(e,t){for(var o=0;o<t.length;o++){var n=t[o];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,r(n.key),n)}}e.exports=function _createClass(e,t,o){return t&&_defineProperties(e.prototype,t),o&&_defineProperties(e,o),Object.defineProperty(e,"prototype",{writable:!1}),e},e.exports.__esModule=!0,e.exports.default=e.exports},74910:e=>{function _getPrototypeOf(t){return e.exports=_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function _getPrototypeOf(e){return e.__proto__||Object.getPrototypeOf(e)},e.exports.__esModule=!0,e.exports.default=e.exports,_getPrototypeOf(t)}e.exports=_getPrototypeOf,e.exports.__esModule=!0,e.exports.default=e.exports},58724:(e,t,o)=>{var r=o(96196);e.exports=function _inherits(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&r(e,t)},e.exports.__esModule=!0,e.exports.default=e.exports},73203:e=>{e.exports=function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports},71173:(e,t,o)=>{var r=o(7501).default,n=o(77266);e.exports=function _possibleConstructorReturn(e,t){if(t&&("object"===r(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return n(e)},e.exports.__esModule=!0,e.exports.default=e.exports},96196:e=>{function _setPrototypeOf(t,o){return e.exports=_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function _setPrototypeOf(e,t){return e.__proto__=t,e},e.exports.__esModule=!0,e.exports.default=e.exports,_setPrototypeOf(t,o)}e.exports=_setPrototypeOf,e.exports.__esModule=!0,e.exports.default=e.exports},56027:(e,t,o)=>{var r=o(7501).default;e.exports=function toPrimitive(e,t){if("object"!=r(e)||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var n=o.call(e,t||"default");if("object"!=r(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)},e.exports.__esModule=!0,e.exports.default=e.exports},74040:(e,t,o)=>{var r=o(7501).default,n=o(56027);e.exports=function toPropertyKey(e){var t=n(e,"string");return"symbol"==r(t)?t:String(t)},e.exports.__esModule=!0,e.exports.default=e.exports},7501:e=>{function _typeof(t){return e.exports=_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,_typeof(t)}e.exports=_typeof,e.exports.__esModule=!0,e.exports.default=e.exports}},t={};function __webpack_require__(o){var r=t[o];if(void 0!==r)return r.exports;var n=t[o]={exports:{}};return e[o](n,n.exports,__webpack_require__),n.exports}(()=>{"use strict";var e=__webpack_require__(73203)(__webpack_require__(7033));window.top.$e.components.register(new e.default)})()})();