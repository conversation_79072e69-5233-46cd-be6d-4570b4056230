/*! elementor - v3.23.0 - 05-08-2024 */
/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
var __webpack_exports__ = {};
/*!************************************************************!*\
  !*** ../core/editor/loader/v2/js/editor-environment-v2.js ***!
  \************************************************************/


var _window$elementorV;
if (!((_window$elementorV = window.elementorV2) !== null && _window$elementorV !== void 0 && _window$elementorV.env)) {
  throw new Error('The "@elementor/env" package was not loaded.');
}
window.elementorV2.env.initEnv(window.elementorEditorV2Env);
/******/ })()
;
//# sourceMappingURL=editor-environment-v2.js.map