/*! elementor - v3.23.0 - 05-08-2024 */
/*! For license information please see element-manager-admin.min.js.LICENSE.txt */
(()=>{var e={2234:(e,t,n)=>{"use strict";n.r(t),n.d(t,{Children:()=>r.Children,Component:()=>r.Component,Fragment:()=>r.Fragment,Platform:()=>d,PureComponent:()=>r.PureComponent,RawHTML:()=>RawHTML,StrictMode:()=>r.StrictMode,Suspense:()=>r.Suspense,cloneElement:()=>r.cloneElement,concatChildren:()=>concatChildren,createContext:()=>r.createContext,createElement:()=>r.createElement,createInterpolateElement:()=>create_interpolate_element,createPortal:()=>u.createPortal,createRef:()=>r.createRef,createRoot:()=>c.createRoot,findDOMNode:()=>u.findDOMNode,flushSync:()=>u.flushSync,forwardRef:()=>r.forwardRef,hydrate:()=>u.hydrate,hydrateRoot:()=>c.hydrateRoot,isEmptyElement:()=>isEmptyElement,isValidElement:()=>r.isValidElement,lazy:()=>r.lazy,memo:()=>r.memo,render:()=>u.render,renderToString:()=>C,startTransition:()=>r.startTransition,switchChildrenNodeName:()=>switchChildrenNodeName,unmountComponentAtNode:()=>u.unmountComponentAtNode,useCallback:()=>r.useCallback,useContext:()=>r.useContext,useDebugValue:()=>r.useDebugValue,useDeferredValue:()=>r.useDeferredValue,useEffect:()=>r.useEffect,useId:()=>r.useId,useImperativeHandle:()=>r.useImperativeHandle,useInsertionEffect:()=>r.useInsertionEffect,useLayoutEffect:()=>r.useLayoutEffect,useMemo:()=>r.useMemo,useReducer:()=>r.useReducer,useRef:()=>r.useRef,useState:()=>r.useState,useSyncExternalStore:()=>r.useSyncExternalStore,useTransition:()=>r.useTransition});var r=n(87363);let a,o,l,i;const s=/<(\/)?(\w+)\s*(\/)?>/g;function createFrame(e,t,n,r,a){return{element:e,tokenStart:t,tokenLength:n,prevOffset:r,leadingTextStart:a,children:[]}}const isValidConversionMap=e=>{const t="object"==typeof e,n=t&&Object.values(e);return t&&n.length&&n.every((e=>(0,r.isValidElement)(e)))};function proceed(e){const t=function nextToken(){const e=s.exec(a);if(null===e)return["no-more-tokens"];const t=e.index,[n,r,o,l]=e,i=n.length;if(l)return["self-closed",o,t,i];if(r)return["closer",o,t,i];return["opener",o,t,i]}(),[n,u,c,d]=t,f=i.length,p=c>o?o:null;if(!e[u])return addText(),!1;switch(n){case"no-more-tokens":if(0!==f){const{leadingTextStart:e,tokenStart:t}=i.pop();l.push(a.substr(e,t))}return addText(),!1;case"self-closed":return 0===f?(null!==p&&l.push(a.substr(p,c-p)),l.push(e[u]),o=c+d,!0):(addChild(createFrame(e[u],c,d)),o=c+d,!0);case"opener":return i.push(createFrame(e[u],c,d,c+d,p)),o=c+d,!0;case"closer":if(1===f)return function closeOuterElement(e){const{element:t,leadingTextStart:n,prevOffset:o,tokenStart:s,children:u}=i.pop(),c=e?a.substr(o,e-o):a.substr(o);c&&u.push(c);null!==n&&l.push(a.substr(n,s-n));l.push((0,r.cloneElement)(t,null,...u))}(c),o=c+d,!0;const t=i.pop(),n=a.substr(t.prevOffset,c-t.prevOffset);t.children.push(n),t.prevOffset=c+d;const s=createFrame(t.element,t.tokenStart,t.tokenLength,c+d);return s.children=t.children,addChild(s),o=c+d,!0;default:return addText(),!1}}function addText(){const e=a.length-o;0!==e&&l.push(a.substr(o,e))}function addChild(e){const{element:t,tokenStart:n,tokenLength:o,prevOffset:l,children:s}=e,u=i[i.length-1],c=a.substr(u.prevOffset,n-u.prevOffset);c&&u.children.push(c),u.children.push((0,r.cloneElement)(t,null,...s)),u.prevOffset=l||n+o}const create_interpolate_element=(e,t)=>{if(a=e,o=0,l=[],i=[],s.lastIndex=0,!isValidConversionMap(t))throw new TypeError("The conversionMap provided is not valid. It must be an object with values that are React Elements");do{}while(proceed(t));return(0,r.createElement)(r.Fragment,null,...l)};function concatChildren(...e){return e.reduce(((e,t,n)=>(r.Children.forEach(t,((t,a)=>{t&&"string"!=typeof t&&(t=(0,r.cloneElement)(t,{key:[n,a].join()})),e.push(t)})),e)),[])}function switchChildrenNodeName(e,t){return e&&r.Children.map(e,((e,n)=>{if("string"==typeof e?.valueOf())return(0,r.createElement)(t,{key:n},e);const{children:a,...o}=e.props;return(0,r.createElement)(t,{key:n,...o},a)}))}var u=n(61533),c=n(37634);const isEmptyElement=e=>"number"!=typeof e&&("string"==typeof e?.valueOf()||Array.isArray(e)?!e.length:!e),d={OS:"web",select:e=>"web"in e?e.web:e.default,isWeb:!0};function isObject(e){return"[object Object]"===Object.prototype.toString.call(e)}var __assign=function(){return __assign=Object.assign||function __assign(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},__assign.apply(this,arguments)};Object.create;Object.create;"function"==typeof SuppressedError&&SuppressedError;var tslib_es6_assign=function(){return tslib_es6_assign=Object.assign||function __assign(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},tslib_es6_assign.apply(this,arguments)};Object.create;Object.create;"function"==typeof SuppressedError&&SuppressedError;function lowerCase(e){return e.toLowerCase()}var f=[/([a-z0-9])([A-Z])/g,/([A-Z])([A-Z][a-z])/g],p=/[^A-Z0-9]+/gi;function replace(e,t,n){return t instanceof RegExp?e.replace(t,n):t.reduce((function(e,t){return e.replace(t,n)}),e)}function dotCase(e,t){return void 0===t&&(t={}),function noCase(e,t){void 0===t&&(t={});for(var n=t.splitRegexp,r=void 0===n?f:n,a=t.stripRegexp,o=void 0===a?p:a,l=t.transform,i=void 0===l?lowerCase:l,s=t.delimiter,u=void 0===s?" ":s,c=replace(replace(e,r,"$1\0$2"),o,"\0"),d=0,m=c.length;"\0"===c.charAt(d);)d++;for(;"\0"===c.charAt(m-1);)m--;return c.slice(d,m).split("\0").map(i).join(u)}(e,tslib_es6_assign({delimiter:"."},t))}function paramCase(e,t){return void 0===t&&(t={}),dotCase(e,__assign({delimiter:"-"},t))}const m=/[\u007F-\u009F "'>/="\uFDD0-\uFDEF]/;function escapeAmpersand(e){return e.replace(/&(?!([a-z0-9]+|#[0-9]+|#x[a-f0-9]+);)/gi,"&amp;")}function escapeLessThan(e){return e.replace(/</g,"&lt;")}function escapeAttribute(e){return function __unstableEscapeGreaterThan(e){return e.replace(/>/g,"&gt;")}(function escapeQuotationMark(e){return e.replace(/"/g,"&quot;")}(escapeAmpersand(e)))}function isValidAttributeName(e){return!m.test(e)}function RawHTML({children:e,...t}){let n="";return r.Children.toArray(e).forEach((e=>{"string"==typeof e&&""!==e.trim()&&(n+=e)})),(0,r.createElement)("div",{dangerouslySetInnerHTML:{__html:n},...t})}const{Provider:h,Consumer:g}=(0,r.createContext)(void 0),y=(0,r.forwardRef)((()=>null)),v=new Set(["string","boolean","number"]),_=new Set(["area","base","br","col","command","embed","hr","img","input","keygen","link","meta","param","source","track","wbr"]),x=new Set(["allowfullscreen","allowpaymentrequest","allowusermedia","async","autofocus","autoplay","checked","controls","default","defer","disabled","download","formnovalidate","hidden","ismap","itemscope","loop","multiple","muted","nomodule","novalidate","open","playsinline","readonly","required","reversed","selected","typemustmatch"]),b=new Set(["autocapitalize","autocomplete","charset","contenteditable","crossorigin","decoding","dir","draggable","enctype","formenctype","formmethod","http-equiv","inputmode","kind","method","preload","scope","shape","spellcheck","translate","type","wrap"]),w=new Set(["animation","animationIterationCount","baselineShift","borderImageOutset","borderImageSlice","borderImageWidth","columnCount","cx","cy","fillOpacity","flexGrow","flexShrink","floodOpacity","fontWeight","gridColumnEnd","gridColumnStart","gridRowEnd","gridRowStart","lineHeight","opacity","order","orphans","r","rx","ry","shapeImageThreshold","stopOpacity","strokeDasharray","strokeDashoffset","strokeMiterlimit","strokeOpacity","strokeWidth","tabSize","widows","x","y","zIndex","zoom"]);function hasPrefix(e,t){return t.some((t=>0===e.indexOf(t)))}function isInternalAttribute(e){return"key"===e||"children"===e}function getNormalAttributeValue(e,t){return"style"===e?function renderStyle(e){if(!function isPlainObject(e){var t,n;return!1!==isObject(e)&&(void 0===(t=e.constructor)||!1!==isObject(n=t.prototype)&&!1!==n.hasOwnProperty("isPrototypeOf"))}(e))return e;let t;for(const n in e){const r=e[n];if(null==r)continue;t?t+=";":t="";t+=getNormalStylePropertyName(n)+":"+getNormalStylePropertyValue(n,r)}return t}(t):t}const E=["accentHeight","alignmentBaseline","arabicForm","baselineShift","capHeight","clipPath","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","dominantBaseline","enableBackground","fillOpacity","fillRule","floodColor","floodOpacity","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","horizAdvX","horizOriginX","imageRendering","letterSpacing","lightingColor","markerEnd","markerMid","markerStart","overlinePosition","overlineThickness","paintOrder","panose1","pointerEvents","renderingIntent","shapeRendering","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","textAnchor","textDecoration","textRendering","underlinePosition","underlineThickness","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","vHanging","vIdeographic","vMathematical","vectorEffect","vertAdvY","vertOriginX","vertOriginY","wordSpacing","writingMode","xmlnsXlink","xHeight"].reduce(((e,t)=>(e[t.toLowerCase()]=t,e)),{}),S=["allowReorder","attributeName","attributeType","autoReverse","baseFrequency","baseProfile","calcMode","clipPathUnits","contentScriptType","contentStyleType","diffuseConstant","edgeMode","externalResourcesRequired","filterRes","filterUnits","glyphRef","gradientTransform","gradientUnits","kernelMatrix","kernelUnitLength","keyPoints","keySplines","keyTimes","lengthAdjust","limitingConeAngle","markerHeight","markerUnits","markerWidth","maskContentUnits","maskUnits","numOctaves","pathLength","patternContentUnits","patternTransform","patternUnits","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","refX","refY","repeatCount","repeatDur","requiredExtensions","requiredFeatures","specularConstant","specularExponent","spreadMethod","startOffset","stdDeviation","stitchTiles","suppressContentEditableWarning","suppressHydrationWarning","surfaceScale","systemLanguage","tableValues","targetX","targetY","textLength","viewBox","viewTarget","xChannelSelector","yChannelSelector"].reduce(((e,t)=>(e[t.toLowerCase()]=t,e)),{}),k=["xlink:actuate","xlink:arcrole","xlink:href","xlink:role","xlink:show","xlink:title","xlink:type","xml:base","xml:lang","xml:space","xmlns:xlink"].reduce(((e,t)=>(e[t.replace(":","").toLowerCase()]=t,e)),{});function getNormalAttributeName(e){switch(e){case"htmlFor":return"for";case"className":return"class"}const t=e.toLowerCase();return S[t]?S[t]:E[t]?paramCase(E[t]):k[t]?k[t]:t}function getNormalStylePropertyName(e){return e.startsWith("--")?e:hasPrefix(e,["ms","O","Moz","Webkit"])?"-"+paramCase(e):paramCase(e)}function getNormalStylePropertyValue(e,t){return"number"!=typeof t||0===t||w.has(e)?t:t+"px"}function renderElement(e,t,n={}){if(null==e||!1===e)return"";if(Array.isArray(e))return renderChildren(e,t,n);switch(typeof e){case"string":return function escapeHTML(e){return escapeLessThan(escapeAmpersand(e))}(e);case"number":return e.toString()}const{type:a,props:o}=e;switch(a){case r.StrictMode:case r.Fragment:return renderChildren(o.children,t,n);case RawHTML:const{children:e,...a}=o;return renderNativeComponent(Object.keys(a).length?"div":null,{...a,dangerouslySetInnerHTML:{__html:e}},t,n)}switch(typeof a){case"string":return renderNativeComponent(a,o,t,n);case"function":return a.prototype&&"function"==typeof a.prototype.render?function renderComponent(e,t,n,r={}){const a=new e(t,r);"function"==typeof a.getChildContext&&Object.assign(r,a.getChildContext());const o=renderElement(a.render(),n,r);return o}(a,o,t,n):renderElement(a(o,n),t,n)}switch(a&&a.$$typeof){case h.$$typeof:return renderChildren(o.children,o.value,n);case g.$$typeof:return renderElement(o.children(t||a._currentValue),t,n);case y.$$typeof:return renderElement(a.render(o),t,n)}return""}function renderNativeComponent(e,t,n,r={}){let a="";if("textarea"===e&&t.hasOwnProperty("value")){a=renderChildren(t.value,n,r);const{value:e,...o}=t;t=o}else t.dangerouslySetInnerHTML&&"string"==typeof t.dangerouslySetInnerHTML.__html?a=t.dangerouslySetInnerHTML.__html:void 0!==t.children&&(a=renderChildren(t.children,n,r));if(!e)return a;const o=function renderAttributes(e){let t="";for(const n in e){const r=getNormalAttributeName(n);if(!isValidAttributeName(r))continue;let a=getNormalAttributeValue(n,e[n]);if(!v.has(typeof a))continue;if(isInternalAttribute(n))continue;const o=x.has(r);if(o&&!1===a)continue;const l=o||hasPrefix(n,["data-","aria-"])||b.has(r);("boolean"!=typeof a||l)&&(t+=" "+r,o||("string"==typeof a&&(a=escapeAttribute(a)),t+='="'+a+'"'))}return t}(t);return _.has(e)?"<"+e+o+"/>":"<"+e+o+">"+a+"</"+e+">"}function renderChildren(e,t,n={}){let r="";e=Array.isArray(e)?e:[e];for(let a=0;a<e.length;a++){r+=renderElement(e[a],t,n)}return r}const C=renderElement},99764:(e,t,n)=>{"use strict";var r=n(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.saveDisabledWidgets=t.markNoticeViewed=t.getUsageWidgets=t.getAdminAppData=void 0;var a=r(n(50824)),o=r(n(10029)),l=function(){var e=(0,o.default)(a.default.mark((function _callee(e){var t,n,r=arguments;return a.default.wrap((function _callee$(a){for(;;)switch(a.prev=a.next){case 0:return t=r.length>1&&void 0!==r[1]?r[1]:{},a.prev=1,n={action:"elementor_element_manager_save_disabled_elements",nonce:eElementManagerConfig.nonce,widgets:JSON.stringify(e)},null!==t&&(n.elements_restriction=JSON.stringify(t)),a.next=6,fetch(eElementManagerConfig.ajaxurl,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:new URLSearchParams(n)});case 6:a.next=11;break;case 8:a.prev=8,a.t0=a.catch(1),console.error(a.t0);case 11:case"end":return a.stop()}}),_callee,null,[[1,8]])})));return function saveDisabledWidgets(t){return e.apply(this,arguments)}}();t.saveDisabledWidgets=l;var i=function(){var e=(0,o.default)(a.default.mark((function _callee2(){var e,t;return a.default.wrap((function _callee2$(n){for(;;)switch(n.prev=n.next){case 0:return n.prev=0,n.next=3,fetch(eElementManagerConfig.ajaxurl,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:new URLSearchParams({action:"elementor_element_manager_get_admin_app_data",nonce:eElementManagerConfig.nonce})});case 3:return e=n.sent,n.next=6,e.json();case 6:if(!(t=n.sent).success){n.next=9;break}return n.abrupt("return",t.data);case 9:n.next=14;break;case 11:n.prev=11,n.t0=n.catch(0),console.error(n.t0);case 14:case"end":return n.stop()}}),_callee2,null,[[0,11]])})));return function getAdminAppData(){return e.apply(this,arguments)}}();t.getAdminAppData=i;var s=function(){var e=(0,o.default)(a.default.mark((function _callee3(){var e,t;return a.default.wrap((function _callee3$(n){for(;;)switch(n.prev=n.next){case 0:return n.prev=0,n.next=3,fetch(eElementManagerConfig.ajaxurl,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:new URLSearchParams({action:"elementor_element_manager_get_widgets_usage",nonce:eElementManagerConfig.nonce})});case 3:return e=n.sent,n.next=6,e.json();case 6:if(!(t=n.sent).success){n.next=9;break}return n.abrupt("return",t.data);case 9:n.next=14;break;case 11:n.prev=11,n.t0=n.catch(0),console.error(n.t0);case 14:case"end":return n.stop()}}),_callee3,null,[[0,11]])})));return function getUsageWidgets(){return e.apply(this,arguments)}}();t.getUsageWidgets=s;var u=function(){var e=(0,o.default)(a.default.mark((function _callee4(e){var t,n;return a.default.wrap((function _callee4$(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,fetch(eElementManagerConfig.ajaxurl,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:new URLSearchParams({action:"elementor_set_admin_notice_viewed",notice_id:e})});case 3:return t=r.sent,r.next=6,t.json();case 6:if(!(n=r.sent).success){r.next=9;break}return r.abrupt("return",n.data);case 9:r.next=14;break;case 11:r.prev=11,r.t0=r.catch(0),console.error(r.t0);case 14:case"end":return r.stop()}}),_callee4,null,[[0,11]])})));return function markNoticeViewed(t){return e.apply(this,arguments)}}();t.markNoticeViewed=u},18187:(e,t,n)=>{"use strict";var r=n(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.App=void 0;var a=r(n(87363)),o=r(n(50824)),l=r(n(9833)),i=r(n(93231)),s=r(n(10029)),u=r(n(40131)),c=n(2234),d=n(7537),f=n(38003),p=n(10603),m=n(99764),h=n(59910);function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach((function(t){(0,i.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}t.App=function App(){var e=(0,c.useState)(!0),t=(0,u.default)(e,2),n=t[0],r=t[1],i=(0,c.useState)(""),g=(0,u.default)(i,2),y=g[0],v=g[1],_=(0,c.useState)([]),x=(0,u.default)(_,2),b=x[0],w=x[1],E=(0,c.useState)([]),S=(0,u.default)(E,2),k=S[0],C=S[1],O=(0,c.useState)([]),j=(0,u.default)(O,2),P=j[0],A=j[1],R=(0,c.useState)([]),T=(0,u.default)(R,2),M=T[0],I=T[1],L=(0,c.useState)({isLoading:!1,data:null}),N=(0,u.default)(L,2),F=N[0],D=N[1],B=(0,c.useState)([]),U=(0,u.default)(B,2),W=U[0],G=U[1],H=(0,c.useState)("widget"),q=(0,u.default)(H,2),z=q[0],V=q[1],$=(0,c.useState)("asc"),X=(0,u.default)($,2),Y=X[0],K=X[1],Z=(0,c.useState)(""),J=(0,u.default)(Z,2),Q=J[0],ee=J[1],te=(0,c.useState)("all"),ne=(0,u.default)(te,2),re=ne[0],ae=ne[1],oe=(0,c.useState)({isSaving:!1,isUnsavedChanges:!1}),le=(0,u.default)(oe,2),ie=le[0],se=le[1],ue=(0,c.useState)(!1),ce=(0,u.default)(ue,2),de=ce[0],fe=ce[1],pe=(0,c.useState)(!1),me=(0,u.default)(pe,2),he=me[0],ge=me[1],ye=(0,c.useState)(null),ve=(0,u.default)(ye,2),_e=ve[0],xe=ve[1],be=(0,c.useState)(null),we=(0,u.default)(be,2),Ee=we[0],Se=we[1],ke=(0,c.useState)([]),Ce=(0,u.default)(ke,2),Oe=Ce[0],je=Ce[1],Pe=Oe.manager_permissions,Ae=Oe.element_manager,Re=function getWidgetUsage(e){return F.data&&F.data.hasOwnProperty(e)?F.data[e]:0},Te=(0,c.useMemo)((function(){var e=b.filter((function(e){return e.title.toLowerCase().includes(y.toLowerCase())}));return""!==Q&&(e=e.filter((function(e){return e.plugin.toLowerCase()===Q.toLowerCase()}))),"all"!==re&&(e=e.filter((function(e){return"active"===re?!W.includes(e.name):W.includes(e.name)}))),e.sort((function(e,t){var n,r;return"widget"===z&&(n=e.title,r=t.title),"usage"===z&&(n=Re(e.name),r=Re(t.name)),n===r?0:"asc"===Y?n<r?-1:1:n>r?-1:1})),e}),[b,y,z,Y,Q,F,re,W]),Me=function getSortingIndicatorClasses(e){return z!==e?"":"asc"===Y?"sorted asc":"sorted desc"},Ie=function onSortingClicked(e){z===e?K("asc"===Y?"desc":"asc"):(V(e),K("asc"))},Le=function(){var e=(0,s.default)(o.default.mark((function _callee(){return o.default.wrap((function _callee$(e){for(;;)switch(e.prev=e.next){case 0:return fe(!1),se(_objectSpread(_objectSpread({},ie),{},{isSaving:!0})),e.next=4,(0,m.saveDisabledWidgets)(W,Ee);case 4:se(_objectSpread(_objectSpread({},ie),{},{isSaving:!1,isUnsavedChanges:!1})),ge(!0);case 6:case"end":return e.stop()}}),_callee)})));return function onSaveClicked(){return e.apply(this,arguments)}}(),Ne=function(){var e=(0,s.default)(o.default.mark((function _callee2(){var e;return o.default.wrap((function _callee2$(t){for(;;)switch(t.prev=t.next){case 0:return D(_objectSpread(_objectSpread({},F),{},{isLoading:!0})),t.next=3,(0,m.getUsageWidgets)();case 3:e=t.sent,D({data:e,isLoading:!1}),V("usage"),K("desc");case 7:case"end":return t.stop()}}),_callee2)})));return function onScanUsageElementsClicked(){return e.apply(this,arguments)}}(),Fe=function UsageTimesColumn(e){var t=e.widgetName;return null!==F.data?a.default.createElement(a.default.Fragment,null,Re(t)," ",(0,f.__)("times","elementor")):F.isLoading?a.default.createElement(d.Spinner,null):a.default.createElement(d.Button,{onClick:Ne,size:"small",variant:"secondary"},(0,f.__)("Show","elementor"))};return(0,c.useEffect)((function(){var e=function(){var e=(0,s.default)(o.default.mark((function _callee3(){var e,t,n,a;return o.default.wrap((function _callee3$(o){for(;;)switch(o.prev=o.next){case 0:return o.next=2,(0,m.getAdminAppData)();case 2:n=o.sent,xe(n.notice_data),G(n.disabled_elements),w(n.widgets),C(n.promotion_widgets),je(n.promotion_data),null!==(e=n.additional_data)&&void 0!==e&&e.roles&&I(n.additional_data.roles),null!==(t=n.additional_data)&&void 0!==t&&t.role_restrictions&&Se(n.additional_data.role_restrictions),(a=n.plugins.map((function(e){return{label:e,value:e}}))).unshift({label:(0,f.__)("All Plugins","elementor"),value:""}),A(a),r(!1);case 14:case"end":return o.stop()}}),_callee3)})));return function onLoading(){return e.apply(this,arguments)}}();e()}),[]),(0,c.useEffect)((function(){n||se(_objectSpread(_objectSpread({},ie),{},{isUnsavedChanges:!0}))}),[W,Ee]),(0,c.useEffect)((function(){var e=function handleBeforeUnload(e){e.preventDefault(),e.returnValue=""};return ie.isUnsavedChanges?window.addEventListener("beforeunload",e):window.removeEventListener("beforeunload",e),function(){window.removeEventListener("beforeunload",e)}}),[ie.isUnsavedChanges]),n?a.default.createElement(d.Flex,{justify:"center",style:{margin:"100px"}},a.default.createElement(d.Spinner,{style:{height:"calc(4px * 20)",width:"calc(4px * 20)"}})):a.default.createElement(a.default.Fragment,null,a.default.createElement("p",{style:{marginBottom:"20px",maxWidth:"800px"}},(0,f.__)("Here's where you can fine-tune Elementor to your workflow. Disable elements you don't use for a cleaner interface, more focused creative experience, and improved performance.","elementor")," ",a.default.createElement("a",{href:"https://go.elementor.com/wp-dash-element-manager/",rel:"noreferrer",target:"_blank"},(0,f.__)("Learn More","elementor"))),!_e.is_viewed&&a.default.createElement("p",null,a.default.createElement(d.Notice,{onRemove:function onRemove(){(0,m.markNoticeViewed)(_e.notice_id),xe(_objectSpread(_objectSpread({},_e),{},{is_viewed:!0}))},status:"warning"},a.default.createElement("strong",null,(0,f.__)("Before you continue:","elementor"))," ",(0,f.__)("Deactivating widgets here will remove them from both the Elementor Editor and your website, which can cause changes to your overall layout, design and what visitors see.","elementor"))),a.default.createElement(d.Panel,null,a.default.createElement(d.PanelBody,null,a.default.createElement(d.Flex,{style:{position:"sticky",top:"32px",background:"rgb(255, 255, 255)",zIndex:10,padding:"20px 16px",boxShadow:"rgba(0, 0, 0, 0.15) 0 5px 10px 0",margin:"-16px -16px 24px"}},a.default.createElement(d.FlexItem,null,a.default.createElement(d.Flex,{align:"center"},a.default.createElement(d.SearchControl,{label:(0,f.__)("Search widgets","elementor"),value:y,size:"compact",style:{height:"40px",border:"1px solid rgba(30, 30, 30, 0.5)",background:"transparent"},__nextHasNoMarginBottom:!0,onChange:v}),a.default.createElement(d.FlexItem,{style:{maxWidth:"130px"}},a.default.createElement(d.SelectControl,{onChange:ee,size:"__unstable-large",__nextHasNoMarginBottom:!0,options:P})),a.default.createElement(d.FlexItem,{style:{maxWidth:"130px"}},a.default.createElement(d.SelectControl,{onChange:ae,size:"__unstable-large",__nextHasNoMarginBottom:!0,options:[{label:(0,f.__)("All Statuses","elementor"),value:"all"},{label:(0,f.__)("Active","elementor"),value:"active"},{label:(0,f.__)("Inactive","elementor"),value:"inactive"}]})),a.default.createElement("hr",{style:{height:"30px",margin:"0 5px",borderWidth:"0 1px 0 0",borderStyle:"solid",borderColor:"rgba(30, 30, 30, 0.5)"}}),a.default.createElement(d.ButtonGroup,null,a.default.createElement(d.Button,{variant:"secondary",style:{marginInlineEnd:"10px"},disabled:F.isLoading,isBusy:F.isLoading,onClick:Ne},(0,f.__)("Scan Element Usage","elementor")),a.default.createElement(d.Button,{variant:"secondary",style:{marginInlineEnd:"10px"},onClick:function deactivateAllUnusedWidgets(){var e=b.filter((function(e){return!F.data.hasOwnProperty(e.name)||W.includes(e.name)}));G(e.map((function(e){return e.name})))},disabled:null===F.data},(0,f.__)("Deactivate Unused Elements","elementor")),a.default.createElement(d.Button,{variant:"secondary",disabled:!W.length,style:{marginInlineEnd:"10px"},onClick:function enableAllWidgets(){G([])}},(0,f.__)("Enable All","elementor"))))),a.default.createElement(d.FlexItem,null,a.default.createElement(d.Button,{variant:"primary",disabled:ie.isSaving||!ie.isUnsavedChanges,isBusy:ie.isSaving,onClick:function onClick(){fe(!0)}},(0,f.__)("Save Changes","elementor")))),a.default.createElement(d.PanelRow,null,Te.length?a.default.createElement("table",{className:"wp-list-table widefat fixed striped table-view-list"},a.default.createElement("thead",null,a.default.createElement("tr",null,a.default.createElement("th",{className:"manage-column sortable ".concat(Me("widget"))},a.default.createElement(d.Button,{href:"#",onClick:function onClick(e){e.preventDefault(),Ie("widget")}},a.default.createElement("span",null,(0,f.__)("Element","elementor")),a.default.createElement("span",{className:"sorting-indicators"},a.default.createElement("span",{className:"sorting-indicator asc","aria-hidden":"true"}),a.default.createElement("span",{className:"sorting-indicator desc","aria-hidden":"true"})))),a.default.createElement("th",null,(0,f.__)("Status","elementor")),a.default.createElement("th",{className:"manage-column sortable ".concat(Me("usage"))},a.default.createElement(d.Button,{href:"#",onClick:function onClick(e){e.preventDefault(),Ie("usage")}},a.default.createElement("span",null,(0,f.__)("Usage","elementor")),a.default.createElement("span",{className:"sorting-indicators"},a.default.createElement("span",{className:"sorting-indicator asc","aria-hidden":"true"}),a.default.createElement("span",{className:"sorting-indicator desc","aria-hidden":"true"})))),a.default.createElement("th",null,(0,f.__)("Plugin","elementor")),a.default.createElement("th",null,a.default.createElement(d.Flex,{justify:"flex-start",gap:0},a.default.createElement(d.FlexItem,null,(0,f.__)("Permission","elementor")),a.default.createElement(d.FlexItem,null,a.default.createElement(d.Tooltip,{placement:"top",delay:100,text:(0,f.__)("Choose which users will have access to each widget.","elementor")},a.default.createElement(d.Button,{icon:"info-outline",iconSize:16}))),null===Ee&&a.default.createElement(d.FlexItem,{style:{marginInlineStart:"10px"}},a.default.createElement(p.UpgradeButton,{href:k.length?Pe.pro.url:Pe.advanced.url,size:"small",text:k.length?Pe.pro.text:Pe.advanced.text})))))),a.default.createElement("tbody",null,Te.map((function(e){return a.default.createElement("tr",{key:e.name,"data-key-id":e.name},a.default.createElement("td",null,a.default.createElement("i",{style:{marginInlineEnd:"5px",marginInlineStart:"0",display:"inline-block"},className:"".concat(e.icon)})," ",e.title),a.default.createElement("td",null,a.default.createElement(d.ToggleControl,{checked:!W.includes(e.name),__nextHasNoMarginBottom:!0,onChange:function onChange(){W.includes(e.name)?G(W.filter((function(t){return t!==e.name}))):G([].concat((0,l.default)(W),[e.name]))}})),a.default.createElement("td",null,a.default.createElement(Fe,{widgetName:e.name})),a.default.createElement("td",null,e.plugin),a.default.createElement("td",null,null===Ee||W.includes(e.name)?a.default.createElement(h.EditButtonDisabled,null):a.default.createElement(h.RolePermissions,{widgetName:e.name,roles:M,widgetsRoleRestrictions:Ee,setWidgetsRoleRestrictions:Se})))})))):a.default.createElement(a.default.Fragment,null,(0,f.__)("No elements found.","elementor"))),k.length>0&&a.default.createElement(a.default.Fragment,null,a.default.createElement(d.PanelRow,null,a.default.createElement(d.Flex,{style:{marginTop:"40px",marginBottom:"20px"}},a.default.createElement(d.FlexItem,null,a.default.createElement("h3",null,(0,f.__)("Elementor Pro Elements","elementor")),a.default.createElement("p",null,(0,f.__)("Unleash the full power of Elementor's features and web creation tools.","elementor"))),a.default.createElement(d.FlexItem,null,a.default.createElement(p.UpgradeButton,{href:Ae.url,text:Ae.text})))),a.default.createElement(d.PanelRow,null,a.default.createElement("table",{className:"wp-list-table widefat fixed striped table-view-list"},a.default.createElement("thead",null,a.default.createElement("tr",null,a.default.createElement("th",{className:"manage-column"},a.default.createElement("span",null,(0,f.__)("Element","elementor"))),a.default.createElement("th",null,(0,f.__)("Status","elementor")),a.default.createElement("th",null,(0,f.__)("Usage","elementor")),a.default.createElement("th",null,(0,f.__)("Plugin","elementor")),a.default.createElement("th",null,a.default.createElement(d.Flex,{justify:"flex-start"},a.default.createElement(d.FlexItem,null,(0,f.__)("Permission","elementor")),a.default.createElement(d.FlexItem,null,a.default.createElement(d.Tooltip,{placement:"top",delay:100,text:(0,f.__)("Choose which role will have access to a specific widget.","elementor")},a.default.createElement(d.Button,{icon:"info-outline"}))))))),a.default.createElement("tbody",null,k.map((function(e){return a.default.createElement("tr",{key:e.name},a.default.createElement("td",null,a.default.createElement("i",{style:{marginInlineEnd:"5px"},className:"".concat(e.icon)})," ",e.title),a.default.createElement("td",null,a.default.createElement(d.ToggleControl,{__nextHasNoMarginBottom:!0,checked:!1,disabled:!0})),a.default.createElement("td",null),a.default.createElement("td",null,(0,f.__)("Elementor Pro","elementor")),a.default.createElement("td",null,a.default.createElement(h.EditButtonDisabled,null)))})))))))),de&&a.default.createElement(d.Modal,{title:(0,f.__)("Sure you want to save these changes?","elementor"),size:"small",isDismissible:!1,onRequestClose:function onRequestClose(){fe(!1)}},a.default.createElement("p",{style:{maxWidth:"400px",marginBlockEnd:"30px",marginBlockStart:"0"}},(0,f.__)("Turning widgets off will hide them from the editor panel, and can potentially affect your layout or front-end.","elementor"),a.default.createElement("span",{style:{display:"block",marginTop:"20px"}},(0,f.__)("If you’re adding widgets back in, enjoy them!","elementor"))),a.default.createElement(d.ButtonGroup,{style:{display:"flex",justifyContent:"flex-end",gap:"30px"}},a.default.createElement(d.Button,{variant:"link",onClick:function onClick(){fe(!1)}},(0,f.__)("Cancel","elementor")),a.default.createElement(d.Button,{variant:"primary",onClick:Le},(0,f.__)("Save","elementor")))),a.default.createElement("div",{style:{position:"fixed",bottom:"40px",left:"50%",transform:"translateX(-50%)",display:he?"block":"none"}},a.default.createElement(d.Snackbar,{isDismissible:!0,status:"success",onRemove:function onRemove(){return ge(!1)}},(0,f.__)("We saved your changes.","elementor"))))}},59910:(e,t,n)=>{"use strict";var r=n(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.RolePermissions=t.EditButtonDisabled=void 0;var a=r(n(87363)),o=r(n(93231)),l=n(7537),i=n(38003);function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach((function(t){(0,o.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var s=function RolesList(e){var t=e.roles,n=e.widgetRoleRestrictions,r=t.filter((function(e){return!n.includes(e.id)}));return r.length?r.length===t.length?a.default.createElement(a.default.Fragment,null,"(",(0,i.__)("All Roles","elementor"),")"):a.default.createElement(a.default.Fragment,null,"(",r.map((function(e){return e.name})).join(", "),")"):a.default.createElement(a.default.Fragment,null,"(",(0,i.__)("Admin","elementor"),")")};t.RolePermissions=function RolePermissions(e){var t=e.roles,n=e.widgetName,r=e.widgetsRoleRestrictions,u=e.setWidgetsRoleRestrictions,c=r[n]||[];return a.default.createElement(a.default.Fragment,null,a.default.createElement(l.Dropdown,{className:"my-container-class-name",contentClassName:"my-dropdown-content-classname",popoverProps:{placement:"bottom-start"},renderToggle:function renderToggle(e){var n=e.isOpen,r=e.onToggle;return a.default.createElement(a.default.Fragment,null,a.default.createElement(l.Button,{variant:"link",onClick:r,"aria-expanded":n,style:{textDecoration:"none"}},(0,i.__)("Edit","elementor"))," ",a.default.createElement("span",{style:{color:"var(--e-a-color-txt-muted)"}},a.default.createElement(s,{roles:t,widgetRoleRestrictions:c})))},renderContent:function renderContent(){var e=t.every((function(e){return!c.includes(e.id)})),i=!e&&t.some((function(e){return!c.includes(e.id)}));return a.default.createElement("div",{style:{minWidth:"150px",paddingInline:"10px",paddingBlockStart:"10px"}},a.default.createElement(l.CheckboxControl,{checked:e,indeterminate:i,label:"All",onChange:function onChange(e){u(_objectSpread(_objectSpread({},r),{},e?(0,o.default)({},n,[]):(0,o.default)({},n,t.map((function(e){return e.id})))))}}),t.map((function(e){return a.default.createElement("div",{key:e.id},a.default.createElement(l.CheckboxControl,{checked:!c.includes(e.id),label:e.name,onChange:function onChange(){!function toggleRoleRestrictions(e,t,n,r){var a=n[e]||[];a.includes(t)?a.splice(a.indexOf(t),1):a.push(t),r(_objectSpread(_objectSpread({},n),{},(0,o.default)({},e,a)))}(n,e.id,r,u)}}))})))}}))};t.EditButtonDisabled=function EditButtonDisabled(){return a.default.createElement(a.default.Fragment,null,a.default.createElement(l.Button,{variant:"link",disabled:!0,style:{textDecoration:"none"}},(0,i.__)("Edit","elementor")))}},10603:(e,t,n)=>{"use strict";var r=n(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.UpgradeButton=void 0;var a=r(n(87363)),o=r(n(73119)),l=n(7537);t.UpgradeButton=function UpgradeButton(e){return a.default.createElement(l.Button,(0,o.default)({},e,{variant:"primary",target:"_blank",rel:"noreferrer",style:{background:"var(--e-a-btn-bg-accent, #93003f)"}}))}},37634:(e,t,n)=>{"use strict";var r=n(61533);t.createRoot=r.createRoot,t.hydrateRoot=r.hydrateRoot},87363:e=>{"use strict";e.exports=React},61533:e=>{"use strict";e.exports=ReactDOM},7537:e=>{"use strict";e.exports=wp.components},66850:e=>{"use strict";e.exports=wp.domReady},38003:e=>{"use strict";e.exports=wp.i18n},98106:e=>{e.exports=function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r},e.exports.__esModule=!0,e.exports.default=e.exports},17358:e=>{e.exports=function _arrayWithHoles(e){if(Array.isArray(e))return e},e.exports.__esModule=!0,e.exports.default=e.exports},34102:(e,t,n)=>{var r=n(98106);e.exports=function _arrayWithoutHoles(e){if(Array.isArray(e))return r(e)},e.exports.__esModule=!0,e.exports.default=e.exports},10029:e=>{function asyncGeneratorStep(e,t,n,r,a,o,l){try{var i=e[o](l),s=i.value}catch(e){return void n(e)}i.done?t(s):Promise.resolve(s).then(r,a)}e.exports=function _asyncToGenerator(e){return function(){var t=this,n=arguments;return new Promise((function(r,a){var o=e.apply(t,n);function _next(e){asyncGeneratorStep(o,r,a,_next,_throw,"next",e)}function _throw(e){asyncGeneratorStep(o,r,a,_next,_throw,"throw",e)}_next(void 0)}))}},e.exports.__esModule=!0,e.exports.default=e.exports},93231:(e,t,n)=>{var r=n(74040);e.exports=function _defineProperty(e,t,n){return(t=r(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e},e.exports.__esModule=!0,e.exports.default=e.exports},73119:e=>{function _extends(){return e.exports=_extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,_extends.apply(this,arguments)}e.exports=_extends,e.exports.__esModule=!0,e.exports.default=e.exports},73203:e=>{e.exports=function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports},68:e=>{e.exports=function _iterableToArray(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)},e.exports.__esModule=!0,e.exports.default=e.exports},40608:e=>{e.exports=function _iterableToArrayLimit(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,l,i=[],s=!0,u=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(r=o.call(n)).done)&&(i.push(r.value),i.length!==t);s=!0);}catch(e){u=!0,a=e}finally{try{if(!s&&null!=n.return&&(l=n.return(),Object(l)!==l))return}finally{if(u)throw a}}return i}},e.exports.__esModule=!0,e.exports.default=e.exports},56894:e=>{e.exports=function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports},91282:e=>{e.exports=function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports},21337:(e,t,n)=>{var r=n(7501).default;function _regeneratorRuntime(){"use strict";e.exports=_regeneratorRuntime=function _regeneratorRuntime(){return n},e.exports.__esModule=!0,e.exports.default=e.exports;var t,n={},a=Object.prototype,o=a.hasOwnProperty,l=Object.defineProperty||function(e,t,n){e[t]=n.value},i="function"==typeof Symbol?Symbol:{},s=i.iterator||"@@iterator",u=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function define(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{define({},"")}catch(t){define=function define(e,t,n){return e[t]=n}}function wrap(e,t,n,r){var a=t&&t.prototype instanceof Generator?t:Generator,o=Object.create(a.prototype),i=new Context(r||[]);return l(o,"_invoke",{value:makeInvokeMethod(e,n,i)}),o}function tryCatch(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}n.wrap=wrap;var d="suspendedStart",f="suspendedYield",p="executing",m="completed",h={};function Generator(){}function GeneratorFunction(){}function GeneratorFunctionPrototype(){}var g={};define(g,s,(function(){return this}));var y=Object.getPrototypeOf,v=y&&y(y(values([])));v&&v!==a&&o.call(v,s)&&(g=v);var _=GeneratorFunctionPrototype.prototype=Generator.prototype=Object.create(g);function defineIteratorMethods(e){["next","throw","return"].forEach((function(t){define(e,t,(function(e){return this._invoke(t,e)}))}))}function AsyncIterator(e,t){function invoke(n,a,l,i){var s=tryCatch(e[n],e,a);if("throw"!==s.type){var u=s.arg,c=u.value;return c&&"object"==r(c)&&o.call(c,"__await")?t.resolve(c.__await).then((function(e){invoke("next",e,l,i)}),(function(e){invoke("throw",e,l,i)})):t.resolve(c).then((function(e){u.value=e,l(u)}),(function(e){return invoke("throw",e,l,i)}))}i(s.arg)}var n;l(this,"_invoke",{value:function value(e,r){function callInvokeWithMethodAndArg(){return new t((function(t,n){invoke(e,r,t,n)}))}return n=n?n.then(callInvokeWithMethodAndArg,callInvokeWithMethodAndArg):callInvokeWithMethodAndArg()}})}function makeInvokeMethod(e,n,r){var a=d;return function(o,l){if(a===p)throw new Error("Generator is already running");if(a===m){if("throw"===o)throw l;return{value:t,done:!0}}for(r.method=o,r.arg=l;;){var i=r.delegate;if(i){var s=maybeInvokeDelegate(i,r);if(s){if(s===h)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(a===d)throw a=m,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);a=p;var u=tryCatch(e,n,r);if("normal"===u.type){if(a=r.done?m:f,u.arg===h)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(a=m,r.method="throw",r.arg=u.arg)}}}function maybeInvokeDelegate(e,n){var r=n.method,a=e.iterator[r];if(a===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,maybeInvokeDelegate(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),h;var o=tryCatch(a,e.iterator,n.arg);if("throw"===o.type)return n.method="throw",n.arg=o.arg,n.delegate=null,h;var l=o.arg;return l?l.done?(n[e.resultName]=l.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,h):l:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,h)}function pushTryEntry(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function resetTryEntry(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function Context(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(pushTryEntry,this),this.reset(!0)}function values(e){if(e||""===e){var n=e[s];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,l=function next(){for(;++a<e.length;)if(o.call(e,a))return next.value=e[a],next.done=!1,next;return next.value=t,next.done=!0,next};return l.next=l}}throw new TypeError(r(e)+" is not iterable")}return GeneratorFunction.prototype=GeneratorFunctionPrototype,l(_,"constructor",{value:GeneratorFunctionPrototype,configurable:!0}),l(GeneratorFunctionPrototype,"constructor",{value:GeneratorFunction,configurable:!0}),GeneratorFunction.displayName=define(GeneratorFunctionPrototype,c,"GeneratorFunction"),n.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===GeneratorFunction||"GeneratorFunction"===(t.displayName||t.name))},n.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,GeneratorFunctionPrototype):(e.__proto__=GeneratorFunctionPrototype,define(e,c,"GeneratorFunction")),e.prototype=Object.create(_),e},n.awrap=function(e){return{__await:e}},defineIteratorMethods(AsyncIterator.prototype),define(AsyncIterator.prototype,u,(function(){return this})),n.AsyncIterator=AsyncIterator,n.async=function(e,t,r,a,o){void 0===o&&(o=Promise);var l=new AsyncIterator(wrap(e,t,r,a),o);return n.isGeneratorFunction(t)?l:l.next().then((function(e){return e.done?e.value:l.next()}))},defineIteratorMethods(_),define(_,c,"Generator"),define(_,s,(function(){return this})),define(_,"toString",(function(){return"[object Generator]"})),n.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function next(){for(;n.length;){var e=n.pop();if(e in t)return next.value=e,next.done=!1,next}return next.done=!0,next}},n.values=values,Context.prototype={constructor:Context,reset:function reset(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(resetTryEntry),!e)for(var n in this)"t"===n.charAt(0)&&o.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function stop(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function dispatchException(e){if(this.done)throw e;var n=this;function handle(r,a){return l.type="throw",l.arg=e,n.next=r,a&&(n.method="next",n.arg=t),!!a}for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r],l=a.completion;if("root"===a.tryLoc)return handle("end");if(a.tryLoc<=this.prev){var i=o.call(a,"catchLoc"),s=o.call(a,"finallyLoc");if(i&&s){if(this.prev<a.catchLoc)return handle(a.catchLoc,!0);if(this.prev<a.finallyLoc)return handle(a.finallyLoc)}else if(i){if(this.prev<a.catchLoc)return handle(a.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return handle(a.finallyLoc)}}}},abrupt:function abrupt(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&o.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var a=r;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var l=a?a.completion:{};return l.type=e,l.arg=t,a?(this.method="next",this.next=a.finallyLoc,h):this.complete(l)},complete:function complete(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),h},finish:function finish(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),resetTryEntry(n),h}},catch:function _catch(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var a=r.arg;resetTryEntry(n)}return a}}throw new Error("illegal catch attempt")},delegateYield:function delegateYield(e,n,r){return this.delegate={iterator:values(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),h}},n}e.exports=_regeneratorRuntime,e.exports.__esModule=!0,e.exports.default=e.exports},40131:(e,t,n)=>{var r=n(17358),a=n(40608),o=n(35068),l=n(56894);e.exports=function _slicedToArray(e,t){return r(e)||a(e,t)||o(e,t)||l()},e.exports.__esModule=!0,e.exports.default=e.exports},9833:(e,t,n)=>{var r=n(34102),a=n(68),o=n(35068),l=n(91282);e.exports=function _toConsumableArray(e){return r(e)||a(e)||o(e)||l()},e.exports.__esModule=!0,e.exports.default=e.exports},56027:(e,t,n)=>{var r=n(7501).default;e.exports=function toPrimitive(e,t){if("object"!=r(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var a=n.call(e,t||"default");if("object"!=r(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)},e.exports.__esModule=!0,e.exports.default=e.exports},74040:(e,t,n)=>{var r=n(7501).default,a=n(56027);e.exports=function toPropertyKey(e){var t=a(e,"string");return"symbol"==r(t)?t:String(t)},e.exports.__esModule=!0,e.exports.default=e.exports},7501:e=>{function _typeof(t){return e.exports=_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,_typeof(t)}e.exports=_typeof,e.exports.__esModule=!0,e.exports.default=e.exports},35068:(e,t,n)=>{var r=n(98106);e.exports=function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return r(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?r(e,t):void 0}},e.exports.__esModule=!0,e.exports.default=e.exports},50824:(e,t,n)=>{var r=n(21337)();e.exports=r;try{regeneratorRuntime=r}catch(e){"object"==typeof globalThis?globalThis.regeneratorRuntime=r:Function("r","regeneratorRuntime = r")(r)}}},t={};function __webpack_require__(n){var r=t[n];if(void 0!==r)return r.exports;var a=t[n]={exports:{}};return e[n](a,a.exports,__webpack_require__),a.exports}__webpack_require__.d=(e,t)=>{for(var n in t)__webpack_require__.o(t,n)&&!__webpack_require__.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},__webpack_require__.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),__webpack_require__.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{"use strict";var e=__webpack_require__(73203),t=e(__webpack_require__(87363)),n=__webpack_require__(2234),r=e(__webpack_require__(66850)),a=__webpack_require__(18187);(0,r.default)((function(){var e=document.getElementById("elementor-element-manager-wrap");e&&(0,n.render)(t.default.createElement(a.App,null),e)}))})()})();