/*! elementor - v3.23.0 - 05-08-2024 */
(()=>{var e={61650:(e,t,r)=>{"use strict";var n,s=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(u,{QueryClient:()=>l.QueryClient,QueryClientProvider:()=>l.QueryClientProvider,createQueryClient:()=>createQueryClient,useInfiniteQuery:()=>l.useInfiniteQuery,useMutation:()=>l.useMutation,useQuery:()=>l.useQuery,useQueryClient:()=>l.useQueryClient}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))a.call(e,u)||u===r||s(e,u,{get:()=>t[u],enumerable:!(n=i(t,u))||n.enumerable});return e})(s({},"__esModule",{value:!0}),n));var c=r(39937),l=r(39937);function createQueryClient(){return new c.QueryClient({defaultOptions:{queries:{refetchOnWindowFocus:!1,refetchOnReconnect:!1}}})}},66535:(e,t,r)=>{"use strict";var n=r(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;_interopRequireWildcard(r(87363));var s=_interopRequireWildcard(r(61533)),i=r(37634);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==n(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var s={},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var a=i?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(s,o,a):s[o]=e[o]}return s.default=e,r&&r.set(e,s),s}var o={render:function render(e,t){var r;try{var n=(0,i.createRoot)(t);n.render(e),r=function unmountFunction(){n.unmount()}}catch(n){s.render(e,t),r=function unmountFunction(){s.unmountComponentAtNode(t)}}return{unmount:r}}};t.default=o},5418:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getNotifications=void 0;t.getNotifications=function getNotifications(){return function request(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return new Promise((function(r,n){elementorCommon.ajax.addRequest(e,{success:r,error:n,data:t})}))}("notifications_get")}},46730:(e,t,r)=>{"use strict";var n=r(23615),s=r(73203),i=r(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.EditorDrawer=void 0;var o=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==i(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var a=s?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(n,o,a):n[o]=e[o]}n.default=e,r&&r.set(e,n);return n}(r(87363)),a=s(r(40131)),u=r(49005);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}var c=function EditorDrawer(e){var t=e.anchorPosition,r=void 0===t?"left":t,n=(0,o.useState)(!0),s=(0,a.default)(n,2),i=s[0],c=s[1];return(0,o.useEffect)((function(){elementor.on("elementor/editor/panel/whats-new/clicked",(function(){return c(!0)}))}),[]),o.default.createElement(u.WhatsNew,{isOpen:i,setIsOpen:c,setIsRead:function setIsRead(){return document.body.classList.remove("e-has-notification")},anchorPosition:r})};t.EditorDrawer=c,c.propTypes={anchorPosition:n.oneOf(["left","top","right","bottom"])}},42722:(e,t,r)=>{"use strict";var n=r(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.editorOnButtonClicked=void 0;var s=n(r(87363)),i=n(r(66535)),o=r(46730),a=!1;t.editorOnButtonClicked=function editorOnButtonClicked(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"left";if(!a){a=!0;var t=document.createElement("div");return document.body.append(t),void i.default.render(s.default.createElement(o.EditorDrawer,{anchorPosition:e}),t)}elementor.trigger("elementor/editor/panel/whats-new/clicked")}},23066:(e,t,r)=>{"use strict";var n=r(38003).__;Object.defineProperty(t,"__esModule",{value:!0}),t.editorV1=void 0;var s=r(42722);t.editorV1=function editorV1(){elementor.on("panel:init",(function(){elementorNotifications.is_unread&&document.body.classList.add("e-has-notification"),elementor.getPanelView().getPages("menu").view.addItem({name:"notification-center",icon:"eicon-notification",title:n("What's New","elementor"),callback:s.editorOnButtonClicked},"navigate_from_page","view-page")}))}},58325:(e,t,r)=>{"use strict";var n=r(23615),s=r(73203),i=r(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.editorV2=void 0;var o=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==i(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var a=s?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(n,o,a):n[o]=e[o]}n.default=e,r&&r.set(e,n);return n}(r(87363)),a=s(r(40131)),u=r(17564),c=r(42722),l=r(36626),h=r(38003);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}var p=function IconWithBadge(e){var t=e.invisible;return o.default.createElement(l.Badge,{color:"primary",variant:"dot",invisible:t},o.default.createElement(u.GiftIcon,null))};p.propTypes={invisible:n.bool};t.editorV2=function editorV2(){window.elementorV2.editorAppBar.utilitiesMenu.registerLink({id:"app-bar-menu-item-whats-new",priority:25,useProps:function useProps(){var e=(0,o.useState)(!elementorNotifications.is_unread),t=(0,a.default)(e,2),r=t[0],n=t[1];return{title:(0,h.__)("What's New","elementor"),icon:function icon(){return o.default.createElement(p,{invisible:r})},onClick:function onClick(){elementor.editorEvents.dispatchEvent(elementor.editorEvents.config.names.topBar.whatsNew,{location:elementor.editorEvents.config.locations.topBar,secondaryLocation:elementor.editorEvents.config.secondaryLocations["whats-new"],trigger:elementor.editorEvents.config.triggers.click,element:elementor.editorEvents.config.elements.buttonIcon}),n(!0),elementorNotifications.is_unread=!1,(0,c.editorOnButtonClicked)("right")}}}})}},65059:(e,t,r)=>{"use strict";var n=r(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.WhatsNewDrawerContent=void 0;var s=n(r(87363)),i=r(61650),o=r(5418),a=r(36626),u=r(38308);t.WhatsNewDrawerContent=function WhatsNewDrawerContent(){var e=(0,i.useQuery)({queryKey:["e-notifications"],queryFn:o.getNotifications}),t=e.isPending,r=e.error,n=e.data;return t?s.default.createElement(a.Box,null,s.default.createElement(a.LinearProgress,{color:"secondary"})):r?s.default.createElement(a.Box,null,"An error has occurred: ",r):n.map((function(e,t){return s.default.createElement(u.WhatsNewItem,{key:t,item:e,itemIndex:t,itemsLength:n.length})}))}},78596:(e,t,r)=>{"use strict";var n=r(23615),s=r(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.WhatsNewItemChips=void 0;var i=s(r(87363)),o=s(r(73119)),a=r(36626),u=function WhatsNewItemChips(e){var t=e.chipPlan,r=e.chipTags,n=e.itemIndex,s=[];return t&&s.push({color:"promotion",size:"small",label:t}),r&&r.forEach((function(e){s.push({variant:"outlined",size:"small",label:e})})),s.length?i.default.createElement(a.Stack,{direction:"row",flexWrap:"wrap",gap:1,sx:{pb:1}},s.map((function(e,t){return i.default.createElement(a.Chip,(0,o.default)({key:"chip-".concat(n).concat(t)},e))}))):null};t.WhatsNewItemChips=u,u.propTypes={chipPlan:n.string,chipTags:n.array,itemIndex:n.number.isRequired}},48235:(e,t,r)=>{"use strict";var n=r(23615),s=r(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.WhatsNewItemThumbnail=void 0;var i=s(r(87363)),o=r(36626),a=r(72331),u=function WhatsNewItemThumbnail(e){var t=e.imageSrc,r=e.title,n=e.link;return i.default.createElement(o.Box,{sx:{pb:2}},i.default.createElement(a.WrapperWithLink,{link:n},i.default.createElement("img",{src:t,alt:r,style:{maxWidth:"100%"}})))};t.WhatsNewItemThumbnail=u,u.propTypes={imageSrc:n.string.isRequired,title:n.string.isRequired,link:n.string}},87691:(e,t,r)=>{"use strict";var n=r(23615),s=r(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.WhatsNewItemTopicLine=void 0;var i=s(r(87363)),o=r(36626),a=function WhatsNewItemTopicLine(e){var t=e.topic,r=e.date;return i.default.createElement(o.Stack,{direction:"row",divider:i.default.createElement(o.Divider,{orientation:"vertical",flexItem:!0}),spacing:1,color:"text.tertiary",sx:{pb:1}},t&&i.default.createElement(o.Box,null,t),r&&i.default.createElement(o.Box,null,r))};t.WhatsNewItemTopicLine=a,a.propTypes={topic:n.string,date:n.string}},38308:(e,t,r)=>{"use strict";var n=r(23615),s=r(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.WhatsNewItem=void 0;var i=s(r(87363)),o=r(36626),a=r(87691),u=r(72331),c=r(48235),l=r(78596),h=function WhatsNewItem(e){var t=e.item,r=e.itemIndex,n=e.itemsLength;return i.default.createElement(o.Box,{key:r,display:"flex",flexDirection:"column",sx:{pt:2}},(t.topic||t.date)&&i.default.createElement(a.WhatsNewItemTopicLine,{topic:t.topic,date:t.date}),i.default.createElement(u.WrapperWithLink,{link:t.link},i.default.createElement(o.Typography,{variant:"subtitle1",sx:{pb:2}},t.title)),t.imageSrc&&i.default.createElement(c.WhatsNewItemThumbnail,{imageSrc:t.imageSrc,link:t.link,title:t.title}),i.default.createElement(l.WhatsNewItemChips,{chipPlan:t.chipPlan,chipTags:t.chipTags,itemIndex:r}),t.description&&i.default.createElement(o.Typography,{variant:"body2",color:"text.secondary",sx:{pb:2}},t.description,t.readMoreText&&i.default.createElement(i.default.Fragment,null," ",i.default.createElement(o.Link,{href:t.link,color:"info.main",target:"_blank"},t.readMoreText))),t.cta&&t.ctaLink&&i.default.createElement(o.Box,{sx:{pb:2}},i.default.createElement(o.Button,{href:t.ctaLink,target:"_blank",variant:"contained",size:"small",color:"promotion"},t.cta)),r!==n-1&&i.default.createElement(o.Divider,{sx:{my:1}}))};t.WhatsNewItem=h,h.propTypes={item:n.object.isRequired,itemIndex:n.number.isRequired,itemsLength:n.number.isRequired}},41500:(e,t,r)=>{"use strict";var n=r(23615),s=r(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.WhatsNewTopBar=void 0;var i=s(r(87363)),o=r(36626),a=r(38003),u=r(64888),c=function WhatsNewTopBar(e){var t=e.setIsOpen;return i.default.createElement(i.default.Fragment,null,i.default.createElement(o.AppBar,{elevation:0,position:"sticky",sx:{backgroundColor:"background.default"}},i.default.createElement(o.Toolbar,{variant:"dense"},i.default.createElement(o.Typography,{variant:"overline",sx:{flexGrow:1}},(0,a.__)("What's New","elementor")),i.default.createElement(o.IconButton,{"aria-label":"close",size:"small",onClick:function onClick(){return t(!1)}},i.default.createElement(u.XIcon,null)))),i.default.createElement(o.Divider,null))};t.WhatsNewTopBar=c,c.propTypes={setIsOpen:n.func.isRequired}},49005:(e,t,r)=>{"use strict";var n=r(23615),s=r(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.WhatsNew=void 0;var i=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==s(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var a=i?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(n,o,a):n[o]=e[o]}n.default=e,r&&r.set(e,n);return n}(r(87363)),o=r(36626),a=r(61650),u=r(41500),c=r(65059);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}var l=new a.QueryClient({defaultOptions:{queries:{refetchOnWindowFocus:!1,retry:!1,staleTime:18e5}}}),h=function WhatsNew(e){var t,r,n=e.isOpen,s=e.setIsOpen,h=e.setIsRead,p=e.anchorPosition,d=void 0===p?"right":p;return(0,i.useEffect)((function(){n&&h(!0)}),[n,h]),i.default.createElement(i.default.Fragment,null,i.default.createElement(a.QueryClientProvider,{client:l},i.default.createElement(o.DirectionProvider,{rtl:elementorCommon.config.isRTL},i.default.createElement(o.ThemeProvider,{colorScheme:(null===(t=window.elementor)||void 0===t||null===(r=t.getPreferences)||void 0===r?void 0:r.call(t,"ui_theme"))||"auto"},i.default.createElement(o.Drawer,{anchor:d,open:n,onClose:function onClose(){return s(!1)},ModalProps:{style:{zIndex:999999}}},i.default.createElement(o.Box,{sx:{width:320,backgroundColor:"background.default"},role:"presentation"},i.default.createElement(u.WhatsNewTopBar,{setIsOpen:s}),i.default.createElement(o.Box,{sx:{padding:"16px"}},i.default.createElement(c.WhatsNewDrawerContent,null))))))))};t.WhatsNew=h,h.propTypes={isOpen:n.bool.isRequired,setIsOpen:n.func.isRequired,setIsRead:n.func.isRequired,anchorPosition:n.oneOf(["left","top","right","bottom"])}},72331:(e,t,r)=>{"use strict";var n=r(23615),s=r(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.WrapperWithLink=void 0;var i=s(r(87363)),o=r(36626),a=function WrapperWithLink(e){var t=e.link,r=e.children;return t?i.default.createElement(o.Link,{href:t,target:"_blank",underline:"none",color:"inherit",sx:{"&:hover":{color:"inherit"}}},r):r};t.WrapperWithLink=a,a.propTypes={link:n.string,children:n.any.isRequired}},17564:(e,t,r)=>{"use strict";var n=r(73203),s=r(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.GiftIcon=void 0;var i=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==s(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var a=i?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(n,o,a):n[o]=e[o]}n.default=e,r&&r.set(e,n);return n}(r(87363)),o=n(r(73119)),a=r(36626);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}var u=(0,i.forwardRef)((function(e,t){return i.default.createElement(a.SvgIcon,(0,o.default)({viewBox:"0 0 24 24"},e,{ref:t}),i.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M9.65527 4.84484C8.95951 4.07178 8.20923 3.73771 7.51306 3.74984L7.5 3.75007C7.03587 3.75007 6.59075 3.93433 6.26256 4.26252C5.93437 4.59071 5.75 5.03583 5.75 5.49995C5.75 5.96408 5.93437 6.4092 6.26256 6.73739C6.59075 7.06558 7.03587 7.24995 7.5 7.24995C7.50295 7.24995 7.5059 7.24997 7.50884 7.25001H11.0002C10.6592 6.26394 10.1939 5.44328 9.65527 4.84484ZM11.25 8.75001V11.25H4C3.86193 11.25 3.75 11.1381 3.75 11V9.00001C3.75 8.86193 3.86193 8.75001 4 8.75001H11.25ZM4.25 12.75H4C3.0335 12.75 2.25 11.9665 2.25 11V9.00001C2.25 8.03351 3.0335 7.25001 4 7.25001H4.76141C4.43004 6.73144 4.25 6.12498 4.25 5.49995C4.25 4.638 4.59241 3.81135 5.2019 3.20186C5.80984 2.59392 6.63384 2.2517 7.49342 2.24996C8.72414 2.23069 9.86213 2.83242 10.7702 3.84139C11.2484 4.37275 11.6608 5.01284 12 5.73103C12.3392 5.01284 12.7516 4.37275 13.2298 3.84139C14.1379 2.83242 15.2759 2.23069 16.5066 2.24996C17.3662 2.2517 18.1902 2.59392 18.7981 3.20186C19.4076 3.81135 19.75 4.638 19.75 5.49995C19.75 6.12498 19.57 6.73144 19.2386 7.25001H20C20.9665 7.25001 21.75 8.03351 21.75 9.00001V11C21.75 11.9665 20.9665 12.75 20 12.75H19.75V19C19.75 19.7294 19.4603 20.4288 18.9445 20.9445C18.4288 21.4603 17.7293 21.75 17 21.75H7C6.27065 21.75 5.57118 21.4603 5.05546 20.9445C4.53973 20.4288 4.25 19.7294 4.25 19V12.75ZM11.25 20.25H7C6.66848 20.25 6.35054 20.1183 6.11612 19.8839C5.8817 19.6495 5.75 19.3315 5.75 19V12.75H11.25V20.25ZM12.75 20.25H17C17.3315 20.25 17.6495 20.1183 17.8839 19.8839C18.1183 19.6495 18.25 19.3315 18.25 19V12.75H12.75V20.25ZM12.75 11.25V8.75001H20C20.1381 8.75001 20.25 8.86193 20.25 9.00001V11C20.25 11.1381 20.1381 11.25 20 11.25H12.75ZM16.4912 7.25001C16.4941 7.24997 16.497 7.24995 16.5 7.24995C16.9641 7.24995 17.4092 7.06558 17.7374 6.73739C18.0656 6.4092 18.25 5.96408 18.25 5.49995C18.25 5.03583 18.0656 4.59071 17.7374 4.26252C17.4092 3.93433 16.9641 3.74995 16.5 3.74995H16.4869C15.7908 3.73783 15.0405 4.07178 14.3447 4.84484C13.8061 5.44328 13.3408 6.26394 12.9998 7.25001H16.4912Z"}))}));t.GiftIcon=u},64888:(e,t,r)=>{"use strict";var n=r(73203),s=r(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.XIcon=void 0;var i=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==s(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var a=i?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(n,o,a):n[o]=e[o]}n.default=e,r&&r.set(e,n);return n}(r(87363)),o=n(r(73119)),a=r(36626);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}var u=(0,i.forwardRef)((function(e,t){return i.default.createElement(a.SvgIcon,(0,o.default)({viewBox:"0 0 24 24"},e,{ref:t}),i.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M18.5303 5.46967C18.8232 5.76256 18.8232 6.23744 18.5303 6.53033L6.53033 18.5303C6.23744 18.8232 5.76256 18.8232 5.46967 18.5303C5.17678 18.2374 5.17678 17.7626 5.46967 17.4697L17.4697 5.46967C17.7626 5.17678 18.2374 5.17678 18.5303 5.46967Z"}),i.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M5.46967 5.46967C5.76256 5.17678 6.23744 5.17678 6.53033 5.46967L18.5303 17.4697C18.8232 17.7626 18.8232 18.2374 18.5303 18.5303C18.2374 18.8232 17.7626 18.8232 17.4697 18.5303L5.46967 6.53033C5.17678 6.23744 5.17678 5.76256 5.46967 5.46967Z"}))}));t.XIcon=u},58772:(e,t,r)=>{"use strict";var n=r(90331);function emptyFunction(){}function emptyFunctionWithReset(){}emptyFunctionWithReset.resetWarningCache=emptyFunction,e.exports=function(){function shim(e,t,r,s,i,o){if(o!==n){var a=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw a.name="Invariant Violation",a}}function getShim(){return shim}shim.isRequired=shim;var e={array:shim,bigint:shim,bool:shim,func:shim,number:shim,object:shim,string:shim,symbol:shim,any:shim,arrayOf:getShim,element:shim,elementType:shim,instanceOf:getShim,node:shim,objectOf:getShim,oneOf:getShim,oneOfType:getShim,shape:getShim,exact:getShim,checkPropTypes:emptyFunctionWithReset,resetWarningCache:emptyFunction};return e.PropTypes=e,e}},23615:(e,t,r)=>{e.exports=r(58772)()},90331:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},37634:(e,t,r)=>{"use strict";var n=r(61533);t.createRoot=n.createRoot,t.hydrateRoot=n.hydrateRoot},87363:e=>{"use strict";e.exports=React},61533:e=>{"use strict";e.exports=ReactDOM},36626:e=>{"use strict";e.exports=elementorV2.ui},38003:e=>{"use strict";e.exports=wp.i18n},98106:e=>{e.exports=function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n},e.exports.__esModule=!0,e.exports.default=e.exports},17358:e=>{e.exports=function _arrayWithHoles(e){if(Array.isArray(e))return e},e.exports.__esModule=!0,e.exports.default=e.exports},73119:e=>{function _extends(){return e.exports=_extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,_extends.apply(this,arguments)}e.exports=_extends,e.exports.__esModule=!0,e.exports.default=e.exports},73203:e=>{e.exports=function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports},40608:e=>{e.exports=function _iterableToArrayLimit(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,s,i,o,a=[],u=!0,c=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(a.push(n.value),a.length!==t);u=!0);}catch(e){c=!0,s=e}finally{try{if(!u&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw s}}return a}},e.exports.__esModule=!0,e.exports.default=e.exports},56894:e=>{e.exports=function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports},40131:(e,t,r)=>{var n=r(17358),s=r(40608),i=r(35068),o=r(56894);e.exports=function _slicedToArray(e,t){return n(e)||s(e,t)||i(e,t)||o()},e.exports.__esModule=!0,e.exports.default=e.exports},7501:e=>{function _typeof(t){return e.exports=_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,_typeof(t)}e.exports=_typeof,e.exports.__esModule=!0,e.exports.default=e.exports},35068:(e,t,r)=>{var n=r(98106);e.exports=function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return n(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?n(e,t):void 0}},e.exports.__esModule=!0,e.exports.default=e.exports},62238:(e,t,r)=>{"use strict";var n,s=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(u,{FocusManager:()=>h,focusManager:()=>p}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))a.call(e,u)||u===r||s(e,u,{get:()=>t[u],enumerable:!(n=i(t,u))||n.enumerable});return e})(s({},"__esModule",{value:!0}),n));var c=r(33949),l=r(14525),h=class extends c.Subscribable{#e;#t;#r;constructor(){super(),this.#r=e=>{if(!l.isServer&&window.addEventListener){const listener=()=>e();return window.addEventListener("visibilitychange",listener,!1),()=>{window.removeEventListener("visibilitychange",listener)}}}}onSubscribe(){this.#t||this.setEventListener(this.#r)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#r=e,this.#t?.(),this.#t=e((e=>{"boolean"==typeof e?this.setFocused(e):this.onFocus()}))}setFocused(e){this.#e!==e&&(this.#e=e,this.onFocus())}onFocus(){this.listeners.forEach((e=>{e()}))}isFocused(){return"boolean"==typeof this.#e?this.#e:"hidden"!==globalThis.document?.visibilityState}},p=new h},13654:e=>{"use strict";var t,r=Object.defineProperty,n=Object.getOwnPropertyDescriptor,s=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,o={};function dehydrateMutation(e){return{mutationKey:e.options.mutationKey,state:e.state,...e.meta&&{meta:e.meta}}}function dehydrateQuery(e){return{state:e.state,queryKey:e.queryKey,queryHash:e.queryHash,...e.meta&&{meta:e.meta}}}function defaultShouldDehydrateMutation(e){return e.state.isPaused}function defaultShouldDehydrateQuery(e){return"success"===e.state.status}function dehydrate(e,t={}){const r=t.shouldDehydrateMutation??defaultShouldDehydrateMutation,n=e.getMutationCache().getAll().flatMap((e=>r(e)?[dehydrateMutation(e)]:[])),s=t.shouldDehydrateQuery??defaultShouldDehydrateQuery;return{mutations:n,queries:e.getQueryCache().getAll().flatMap((e=>s(e)?[dehydrateQuery(e)]:[]))}}function hydrate(e,t,r){if("object"!=typeof t||null===t)return;const n=e.getMutationCache(),s=e.getQueryCache(),i=t.mutations||[],o=t.queries||[];i.forEach((t=>{n.build(e,{...r?.defaultOptions?.mutations,mutationKey:t.mutationKey,meta:t.meta},t.state)})),o.forEach((({queryKey:t,state:n,queryHash:i,meta:o})=>{const a=s.get(i);if(a){if(a.state.dataUpdatedAt<n.dataUpdatedAt){const{fetchStatus:e,...t}=n;a.setState(t)}}else s.build(e,{...r?.defaultOptions?.queries,queryKey:t,queryHash:i,meta:o},{...n,fetchStatus:"idle"})}))}((e,t)=>{for(var n in t)r(e,n,{get:t[n],enumerable:!0})})(o,{defaultShouldDehydrateMutation:()=>defaultShouldDehydrateMutation,defaultShouldDehydrateQuery:()=>defaultShouldDehydrateQuery,dehydrate:()=>dehydrate,hydrate:()=>hydrate}),e.exports=(t=o,((e,t,o,a)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of s(t))i.call(e,u)||u===o||r(e,u,{get:()=>t[u],enumerable:!(a=n(t,u))||a.enumerable});return e})(r({},"__esModule",{value:!0}),t))},61528:(e,t,r)=>{"use strict";var n,s=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))a.call(e,u)||u===r||s(e,u,{get:()=>t[u],enumerable:!(n=i(t,u))||n.enumerable});return e},u={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(u,{CancelledError:()=>c.CancelledError,InfiniteQueryObserver:()=>f.InfiniteQueryObserver,MutationCache:()=>y.MutationCache,MutationObserver:()=>b.MutationObserver,QueriesObserver:()=>d.QueriesObserver,Query:()=>j.Query,QueryCache:()=>l.QueryCache,QueryClient:()=>h.QueryClient,QueryObserver:()=>p.QueryObserver,defaultShouldDehydrateMutation:()=>w.defaultShouldDehydrateMutation,defaultShouldDehydrateQuery:()=>w.defaultShouldDehydrateQuery,dehydrate:()=>w.dehydrate,focusManager:()=>v.focusManager,hashKey:()=>g.hashKey,hydrate:()=>w.hydrate,isCancelledError:()=>P.isCancelledError,isServer:()=>g.isServer,keepPreviousData:()=>g.keepPreviousData,matchQuery:()=>g.matchQuery,notifyManager:()=>m.notifyManager,onlineManager:()=>O.onlineManager,replaceEqualDeep:()=>g.replaceEqualDeep}),e.exports=(n=u,__copyProps(s({},"__esModule",{value:!0}),n));var c=r(71739),l=r(17029),h=r(33489),p=r(59716),d=r(65631),f=r(77698),y=r(1516),b=r(17724),m=r(27842),v=r(62238),O=r(11044),g=r(14525),P=r(71739),w=r(13654);((e,t,r)=>{__copyProps(e,t,"default"),r&&__copyProps(r,t,"default")})(u,r(53749),e.exports);var j=r(6881)},13144:(e,t,r)=>{"use strict";var n,s=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(u,{hasNextPage:()=>hasNextPage,hasPreviousPage:()=>hasPreviousPage,infiniteQueryBehavior:()=>infiniteQueryBehavior}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))a.call(e,u)||u===r||s(e,u,{get:()=>t[u],enumerable:!(n=i(t,u))||n.enumerable});return e})(s({},"__esModule",{value:!0}),n));var c=r(14525);function infiniteQueryBehavior(e){return{onFetch:(t,r)=>{const fetchFn=async()=>{const r=t.options,n=t.fetchOptions?.meta?.fetchMore?.direction,s=t.state.data?.pages||[],i=t.state.data?.pageParams||[],o={pages:[],pageParams:[]};let a=!1;const u=t.options.queryFn||(()=>Promise.reject(new Error(`Missing queryFn: '${t.options.queryHash}'`))),fetchPage=async(e,r,n)=>{if(a)return Promise.reject();if(null==r&&e.pages.length)return Promise.resolve(e);const s={queryKey:t.queryKey,pageParam:r,direction:n?"backward":"forward",meta:t.options.meta};var i;i=s,Object.defineProperty(i,"signal",{enumerable:!0,get:()=>(t.signal.aborted?a=!0:t.signal.addEventListener("abort",(()=>{a=!0})),t.signal)});const o=await u(s),{maxPages:l}=t.options,h=n?c.addToStart:c.addToEnd;return{pages:h(e.pages,o,l),pageParams:h(e.pageParams,r,l)}};let l;if(n&&s.length){const e="backward"===n,t={pages:s,pageParams:i},o=(e?getPreviousPageParam:getNextPageParam)(r,t);l=await fetchPage(t,o,e)}else{l=await fetchPage(o,i[0]??r.initialPageParam);const t=e??s.length;for(let e=1;e<t;e++){const e=getNextPageParam(r,l);l=await fetchPage(l,e)}}return l};t.options.persister?t.fetchFn=()=>t.options.persister?.(fetchFn,{queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},r):t.fetchFn=fetchFn}}}function getNextPageParam(e,{pages:t,pageParams:r}){const n=t.length-1;return e.getNextPageParam(t[n],t,r[n],r)}function getPreviousPageParam(e,{pages:t,pageParams:r}){return e.getPreviousPageParam?.(t[0],t,r[0],r)}function hasNextPage(e,t){return!!t&&null!=getNextPageParam(e,t)}function hasPreviousPage(e,t){return!(!t||!e.getPreviousPageParam)&&null!=getPreviousPageParam(e,t)}},77698:(e,t,r)=>{"use strict";var n,s=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(u,{InfiniteQueryObserver:()=>h}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))a.call(e,u)||u===r||s(e,u,{get:()=>t[u],enumerable:!(n=i(t,u))||n.enumerable});return e})(s({},"__esModule",{value:!0}),n));var c=r(59716),l=r(13144),h=class extends c.QueryObserver{constructor(e,t){super(e,t)}bindMethods(){super.bindMethods(),this.fetchNextPage=this.fetchNextPage.bind(this),this.fetchPreviousPage=this.fetchPreviousPage.bind(this)}setOptions(e,t){super.setOptions({...e,behavior:(0,l.infiniteQueryBehavior)()},t)}getOptimisticResult(e){return e.behavior=(0,l.infiniteQueryBehavior)(),super.getOptimisticResult(e)}fetchNextPage(e){return this.fetch({...e,meta:{fetchMore:{direction:"forward"}}})}fetchPreviousPage(e){return this.fetch({...e,meta:{fetchMore:{direction:"backward"}}})}createResult(e,t){const{state:r}=e,n=super.createResult(e,t),{isFetching:s,isRefetching:i}=n,o=s&&"forward"===r.fetchMeta?.fetchMore?.direction,a=s&&"backward"===r.fetchMeta?.fetchMore?.direction;return{...n,fetchNextPage:this.fetchNextPage,fetchPreviousPage:this.fetchPreviousPage,hasNextPage:(0,l.hasNextPage)(t,r.data),hasPreviousPage:(0,l.hasPreviousPage)(t,r.data),isFetchingNextPage:o,isFetchingPreviousPage:a,isRefetching:i&&!o&&!a}}}},4251:(e,t,r)=>{"use strict";var n,s=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(u,{Mutation:()=>p,getDefaultState:()=>getDefaultState}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))a.call(e,u)||u===r||s(e,u,{get:()=>t[u],enumerable:!(n=i(t,u))||n.enumerable});return e})(s({},"__esModule",{value:!0}),n));var c=r(27842),l=r(85603),h=r(71739),p=class extends l.Removable{constructor(e){super(),this.mutationId=e.mutationId,this.#n=e.defaultOptions,this.#s=e.mutationCache,this.#i=[],this.state=e.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(e.options),this.scheduleGc()}#i;#n;#s;#o;setOptions(e){this.options={...this.#n,...e},this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#i.includes(e)||(this.#i.push(e),this.clearGcTimeout(),this.#s.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#i=this.#i.filter((t=>t!==e)),this.scheduleGc(),this.#s.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#i.length||("pending"===this.state.status?this.scheduleGc():this.#s.remove(this))}continue(){return this.#o?.continue()??this.execute(this.state.variables)}async execute(e){const executeMutation=()=>(this.#o=(0,h.createRetryer)({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(new Error("No mutationFn found")),onFail:(e,t)=>{this.#a({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#a({type:"pause"})},onContinue:()=>{this.#a({type:"continue"})},retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode}),this.#o.promise),t="pending"===this.state.status;try{if(!t){this.#a({type:"pending",variables:e}),await(this.#s.config.onMutate?.(e,this));const t=await(this.options.onMutate?.(e));t!==this.state.context&&this.#a({type:"pending",context:t,variables:e})}const r=await executeMutation();return await(this.#s.config.onSuccess?.(r,e,this.state.context,this)),await(this.options.onSuccess?.(r,e,this.state.context)),await(this.#s.config.onSettled?.(r,null,this.state.variables,this.state.context,this)),await(this.options.onSettled?.(r,null,e,this.state.context)),this.#a({type:"success",data:r}),r}catch(t){try{throw await(this.#s.config.onError?.(t,e,this.state.context,this)),await(this.options.onError?.(t,e,this.state.context)),await(this.#s.config.onSettled?.(void 0,t,this.state.variables,this.state.context,this)),await(this.options.onSettled?.(void 0,t,e,this.state.context)),t}finally{this.#a({type:"error",error:t})}}}#a(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"pending":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:!(0,h.canFetch)(this.options.networkMode),status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),c.notifyManager.batch((()=>{this.#i.forEach((t=>{t.onMutationUpdate(e)})),this.#s.notify({mutation:this,type:"updated",action:e})}))}};function getDefaultState(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}},1516:(e,t,r)=>{"use strict";var n,s=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(u,{MutationCache:()=>d}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))a.call(e,u)||u===r||s(e,u,{get:()=>t[u],enumerable:!(n=i(t,u))||n.enumerable});return e})(s({},"__esModule",{value:!0}),n));var c=r(27842),l=r(4251),h=r(14525),p=r(33949),d=class extends p.Subscribable{constructor(e={}){super(),this.config=e,this.#u=[],this.#c=0}#u;#c;#l;build(e,t,r){const n=new l.Mutation({mutationCache:this,mutationId:++this.#c,options:e.defaultMutationOptions(t),state:r});return this.add(n),n}add(e){this.#u.push(e),this.notify({type:"added",mutation:e})}remove(e){this.#u=this.#u.filter((t=>t!==e)),this.notify({type:"removed",mutation:e})}clear(){c.notifyManager.batch((()=>{this.#u.forEach((e=>{this.remove(e)}))}))}getAll(){return this.#u}find(e){const t={exact:!0,...e};return this.#u.find((e=>(0,h.matchMutation)(t,e)))}findAll(e={}){return this.#u.filter((t=>(0,h.matchMutation)(e,t)))}notify(e){c.notifyManager.batch((()=>{this.listeners.forEach((t=>{t(e)}))}))}resumePausedMutations(){return this.#l=(this.#l??Promise.resolve()).then((()=>{const e=this.#u.filter((e=>e.state.isPaused));return c.notifyManager.batch((()=>e.reduce(((e,t)=>e.then((()=>t.continue().catch(h.noop)))),Promise.resolve())))})).then((()=>{this.#l=void 0})),this.#l}}},17724:(e,t,r)=>{"use strict";var n,s=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(u,{MutationObserver:()=>d}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))a.call(e,u)||u===r||s(e,u,{get:()=>t[u],enumerable:!(n=i(t,u))||n.enumerable});return e})(s({},"__esModule",{value:!0}),n));var c=r(4251),l=r(27842),h=r(33949),p=r(14525),d=class extends h.Subscribable{constructor(e,t){super(),this.#h=void 0,this.#p=e,this.setOptions(t),this.bindMethods(),this.#d()}#p;#h;#f;#y;bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){const t=this.options;this.options=this.#p.defaultMutationOptions(e),(0,p.shallowEqualObjects)(t,this.options)||this.#p.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#f,observer:this}),this.#f?.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#f?.removeObserver(this)}onMutationUpdate(e){this.#d(),this.#b(e)}getCurrentResult(){return this.#h}reset(){this.#f=void 0,this.#d(),this.#b()}mutate(e,t){return this.#y=t,this.#f?.removeObserver(this),this.#f=this.#p.getMutationCache().build(this.#p,this.options),this.#f.addObserver(this),this.#f.execute(e)}#d(){const e=this.#f?.state??(0,c.getDefaultState)();this.#h={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#b(e){l.notifyManager.batch((()=>{this.#y&&this.hasListeners()&&("success"===e?.type?(this.#y.onSuccess?.(e.data,this.#h.variables,this.#h.context),this.#y.onSettled?.(e.data,null,this.#h.variables,this.#h.context)):"error"===e?.type&&(this.#y.onError?.(e.error,this.#h.variables,this.#h.context),this.#y.onSettled?.(void 0,e.error,this.#h.variables,this.#h.context))),this.listeners.forEach((e=>{e(this.#h)}))}))}}},27842:(e,t,r)=>{"use strict";var n,s=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(u,{createNotifyManager:()=>createNotifyManager,notifyManager:()=>l}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))a.call(e,u)||u===r||s(e,u,{get:()=>t[u],enumerable:!(n=i(t,u))||n.enumerable});return e})(s({},"__esModule",{value:!0}),n));var c=r(14525);function createNotifyManager(){let e=[],t=0,notifyFn=e=>{e()},batchNotifyFn=e=>{e()};const schedule=r=>{t?e.push(r):(0,c.scheduleMicrotask)((()=>{notifyFn(r)}))},flush=()=>{const t=e;e=[],t.length&&(0,c.scheduleMicrotask)((()=>{batchNotifyFn((()=>{t.forEach((e=>{notifyFn(e)}))}))}))};return{batch:e=>{let r;t++;try{r=e()}finally{t--,t||flush()}return r},batchCalls:e=>(...t)=>{schedule((()=>{e(...t)}))},schedule,setNotifyFunction:e=>{notifyFn=e},setBatchNotifyFunction:e=>{batchNotifyFn=e}}}var l=createNotifyManager()},11044:(e,t,r)=>{"use strict";var n,s=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(u,{OnlineManager:()=>h,onlineManager:()=>p}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))a.call(e,u)||u===r||s(e,u,{get:()=>t[u],enumerable:!(n=i(t,u))||n.enumerable});return e})(s({},"__esModule",{value:!0}),n));var c=r(33949),l=r(14525),h=class extends c.Subscribable{#m=!0;#t;#r;constructor(){super(),this.#r=e=>{if(!l.isServer&&window.addEventListener){const onlineListener=()=>e(!0),offlineListener=()=>e(!1);return window.addEventListener("online",onlineListener,!1),window.addEventListener("offline",offlineListener,!1),()=>{window.removeEventListener("online",onlineListener),window.removeEventListener("offline",offlineListener)}}}}onSubscribe(){this.#t||this.setEventListener(this.#r)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#r=e,this.#t?.(),this.#t=e(this.setOnline.bind(this))}setOnline(e){this.#m!==e&&(this.#m=e,this.listeners.forEach((t=>{t(e)})))}isOnline(){return this.#m}},p=new h},65631:(e,t,r)=>{"use strict";var n,s=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(u,{QueriesObserver:()=>d}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))a.call(e,u)||u===r||s(e,u,{get:()=>t[u],enumerable:!(n=i(t,u))||n.enumerable});return e})(s({},"__esModule",{value:!0}),n));var c=r(27842),l=r(59716),h=r(33949),p=r(14525);function difference(e,t){return e.filter((e=>!t.includes(e)))}var d=class extends h.Subscribable{#p;#v;#O;#i;#g;#P;constructor(e,t,r){super(),this.#p=e,this.#O=[],this.#i=[],this.#w([]),this.setQueries(t,r)}#w(e){this.#v=e,this.#P=this.#j(e)}onSubscribe(){1===this.listeners.size&&this.#i.forEach((e=>{e.subscribe((t=>{this.#M(e,t)}))}))}onUnsubscribe(){this.listeners.size||this.destroy()}destroy(){this.listeners=new Set,this.#i.forEach((e=>{e.destroy()}))}setQueries(e,t,r){this.#O=e,this.#g=t,c.notifyManager.batch((()=>{const e=this.#i,t=this.#C(this.#O);t.forEach((e=>e.observer.setOptions(e.defaultedQueryOptions,r)));const n=t.map((e=>e.observer)),s=n.map((e=>e.getCurrentResult())),i=n.some(((t,r)=>t!==e[r]));(e.length!==n.length||i)&&(this.#i=n,this.#w(s),this.hasListeners()&&(difference(e,n).forEach((e=>{e.destroy()})),difference(n,e).forEach((e=>{e.subscribe((t=>{this.#M(e,t)}))})),this.#b()))}))}getCurrentResult(){return this.#P}getQueries(){return this.#i.map((e=>e.getCurrentQuery()))}getObservers(){return this.#i}getOptimisticResult(e){const t=this.#C(e),r=t.map((e=>e.observer.getOptimisticResult(e.defaultedQueryOptions)));return[r,e=>this.#j(e??r),()=>t.map(((e,t)=>{const n=r[t];return e.defaultedQueryOptions.notifyOnChangeProps?n:e.observer.trackResult(n)}))]}#j(e){const t=this.#g?.combine;return t?(0,p.replaceEqualDeep)(this.#P,t(e)):e}#C(e){const t=this.#i,r=new Map(t.map((e=>[e.options.queryHash,e]))),n=e.map((e=>this.#p.defaultQueryOptions(e))),s=n.flatMap((e=>{const t=r.get(e.queryHash);return null!=t?[{defaultedQueryOptions:e,observer:t}]:[]})),i=new Set(s.map((e=>e.defaultedQueryOptions.queryHash))),o=n.filter((e=>!i.has(e.queryHash))),getObserver=e=>{const t=this.#p.defaultQueryOptions(e);return this.#i.find((e=>e.options.queryHash===t.queryHash))??new l.QueryObserver(this.#p,t)},a=o.map((e=>({defaultedQueryOptions:e,observer:getObserver(e)})));return s.concat(a).sort(((e,t)=>n.indexOf(e.defaultedQueryOptions)-n.indexOf(t.defaultedQueryOptions)))}#M(e,t){const r=this.#i.indexOf(e);-1!==r&&(this.#w(function replaceAt(e,t,r){const n=e.slice(0);return n[t]=r,n}(this.#v,r,t)),this.#b())}#b(){c.notifyManager.batch((()=>{this.listeners.forEach((e=>{e(this.#v)}))}))}}},6881:(e,t,r)=>{"use strict";var n,s=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(u,{Query:()=>d}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))a.call(e,u)||u===r||s(e,u,{get:()=>t[u],enumerable:!(n=i(t,u))||n.enumerable});return e})(s({},"__esModule",{value:!0}),n));var c=r(14525),l=r(27842),h=r(71739),p=r(85603),d=class extends p.Removable{constructor(e){super(),this.#R=!1,this.#n=e.defaultOptions,this.#_(e.options),this.#i=[],this.#Q=e.cache,this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.#x=e.state||function getDefaultState(e){const t="function"==typeof e.initialData?e.initialData():e.initialData,r=void 0!==t,n=r?"function"==typeof e.initialDataUpdatedAt?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:r?n??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:r?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=this.#x,this.scheduleGc()}#x;#E;#Q;#S;#o;#i;#n;#R;get meta(){return this.options.meta}#_(e){this.options={...this.#n,...e},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.#i.length||"idle"!==this.state.fetchStatus||this.#Q.remove(this)}setData(e,t){const r=(0,c.replaceData)(this.state.data,e,this.options);return this.#a({data:r,type:"success",dataUpdatedAt:t?.updatedAt,manual:t?.manual}),r}setState(e,t){this.#a({type:"setState",state:e,setStateOptions:t})}cancel(e){const t=this.#S;return this.#o?.cancel(e),t?t.then(c.noop).catch(c.noop):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#x)}isActive(){return this.#i.some((e=>!1!==e.options.enabled))}isDisabled(){return this.getObserversCount()>0&&!this.isActive()}isStale(){return this.state.isInvalidated||!this.state.dataUpdatedAt||this.#i.some((e=>e.getCurrentResult().isStale))}isStaleByTime(e=0){return this.state.isInvalidated||!this.state.dataUpdatedAt||!(0,c.timeUntilStale)(this.state.dataUpdatedAt,e)}onFocus(){const e=this.#i.find((e=>e.shouldFetchOnWindowFocus()));e?.refetch({cancelRefetch:!1}),this.#o?.continue()}onOnline(){const e=this.#i.find((e=>e.shouldFetchOnReconnect()));e?.refetch({cancelRefetch:!1}),this.#o?.continue()}addObserver(e){this.#i.includes(e)||(this.#i.push(e),this.clearGcTimeout(),this.#Q.notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.#i.includes(e)&&(this.#i=this.#i.filter((t=>t!==e)),this.#i.length||(this.#o&&(this.#R?this.#o.cancel({revert:!0}):this.#o.cancelRetry()),this.scheduleGc()),this.#Q.notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.#i.length}invalidate(){this.state.isInvalidated||this.#a({type:"invalidate"})}fetch(e,t){if("idle"!==this.state.fetchStatus)if(this.state.dataUpdatedAt&&t?.cancelRefetch)this.cancel({silent:!0});else if(this.#S)return this.#o?.continueRetry(),this.#S;if(e&&this.#_(e),!this.options.queryFn){const e=this.#i.find((e=>e.options.queryFn));e&&this.#_(e.options)}const r=new AbortController,n={queryKey:this.queryKey,meta:this.meta},addSignalProperty=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(this.#R=!0,r.signal)})};addSignalProperty(n);const s={fetchOptions:t,options:this.options,queryKey:this.queryKey,state:this.state,fetchFn:()=>this.options.queryFn?(this.#R=!1,this.options.persister?this.options.persister(this.options.queryFn,n,this):this.options.queryFn(n)):Promise.reject(new Error(`Missing queryFn: '${this.options.queryHash}'`))};addSignalProperty(s),this.options.behavior?.onFetch(s,this),this.#E=this.state,"idle"!==this.state.fetchStatus&&this.state.fetchMeta===s.fetchOptions?.meta||this.#a({type:"fetch",meta:s.fetchOptions?.meta});const onError=e=>{(0,h.isCancelledError)(e)&&e.silent||this.#a({type:"error",error:e}),(0,h.isCancelledError)(e)||(this.#Q.config.onError?.(e,this),this.#Q.config.onSettled?.(this.state.data,e,this)),this.isFetchingOptimistic||this.scheduleGc(),this.isFetchingOptimistic=!1};return this.#o=(0,h.createRetryer)({fn:s.fetchFn,abort:r.abort.bind(r),onSuccess:e=>{void 0!==e?(this.setData(e),this.#Q.config.onSuccess?.(e,this),this.#Q.config.onSettled?.(e,this.state.error,this),this.isFetchingOptimistic||this.scheduleGc(),this.isFetchingOptimistic=!1):onError(new Error(`${this.queryHash} data is undefined`))},onError,onFail:(e,t)=>{this.#a({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#a({type:"pause"})},onContinue:()=>{this.#a({type:"continue"})},retry:s.options.retry,retryDelay:s.options.retryDelay,networkMode:s.options.networkMode}),this.#S=this.#o.promise,this.#S}#a(e){this.state=(t=>{switch(e.type){case"failed":return{...t,fetchFailureCount:e.failureCount,fetchFailureReason:e.error};case"pause":return{...t,fetchStatus:"paused"};case"continue":return{...t,fetchStatus:"fetching"};case"fetch":return{...t,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:e.meta??null,fetchStatus:(0,h.canFetch)(this.options.networkMode)?"fetching":"paused",...!t.dataUpdatedAt&&{error:null,status:"pending"}};case"success":return{...t,data:e.data,dataUpdateCount:t.dataUpdateCount+1,dataUpdatedAt:e.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const r=e.error;return(0,h.isCancelledError)(r)&&r.revert&&this.#E?{...this.#E,fetchStatus:"idle"}:{...t,error:r,errorUpdateCount:t.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:t.fetchFailureCount+1,fetchFailureReason:r,fetchStatus:"idle",status:"error"};case"invalidate":return{...t,isInvalidated:!0};case"setState":return{...t,...e.state}}})(this.state),l.notifyManager.batch((()=>{this.#i.forEach((e=>{e.onQueryUpdate()})),this.#Q.notify({query:this,type:"updated",action:e})}))}}},17029:(e,t,r)=>{"use strict";var n,s=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(u,{QueryCache:()=>d}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))a.call(e,u)||u===r||s(e,u,{get:()=>t[u],enumerable:!(n=i(t,u))||n.enumerable});return e})(s({},"__esModule",{value:!0}),n));var c=r(14525),l=r(6881),h=r(27842),p=r(33949),d=class extends p.Subscribable{constructor(e={}){super(),this.config=e,this.#O=new Map}#O;build(e,t,r){const n=t.queryKey,s=t.queryHash??(0,c.hashQueryKeyByOptions)(n,t);let i=this.get(s);return i||(i=new l.Query({cache:this,queryKey:n,queryHash:s,options:e.defaultQueryOptions(t),state:r,defaultOptions:e.getQueryDefaults(n)}),this.add(i)),i}add(e){this.#O.has(e.queryHash)||(this.#O.set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){const t=this.#O.get(e.queryHash);t&&(e.destroy(),t===e&&this.#O.delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){h.notifyManager.batch((()=>{this.getAll().forEach((e=>{this.remove(e)}))}))}get(e){return this.#O.get(e)}getAll(){return[...this.#O.values()]}find(e){const t={exact:!0,...e};return this.getAll().find((e=>(0,c.matchQuery)(t,e)))}findAll(e={}){const t=this.getAll();return Object.keys(e).length>0?t.filter((t=>(0,c.matchQuery)(e,t))):t}notify(e){h.notifyManager.batch((()=>{this.listeners.forEach((t=>{t(e)}))}))}onFocus(){h.notifyManager.batch((()=>{this.getAll().forEach((e=>{e.onFocus()}))}))}onOnline(){h.notifyManager.batch((()=>{this.getAll().forEach((e=>{e.onOnline()}))}))}}},33489:(e,t,r)=>{"use strict";var n,s=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(u,{QueryClient:()=>b}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))a.call(e,u)||u===r||s(e,u,{get:()=>t[u],enumerable:!(n=i(t,u))||n.enumerable});return e})(s({},"__esModule",{value:!0}),n));var c=r(14525),l=r(17029),h=r(1516),p=r(62238),d=r(11044),f=r(27842),y=r(13144),b=class{#q;#s;#n;#D;#I;#T;#F;#k;constructor(e={}){this.#q=e.queryCache||new l.QueryCache,this.#s=e.mutationCache||new h.MutationCache,this.#n=e.defaultOptions||{},this.#D=new Map,this.#I=new Map,this.#T=0}mount(){this.#T++,1===this.#T&&(this.#F=p.focusManager.subscribe((()=>{p.focusManager.isFocused()&&(this.resumePausedMutations(),this.#q.onFocus())})),this.#k=d.onlineManager.subscribe((()=>{d.onlineManager.isOnline()&&(this.resumePausedMutations(),this.#q.onOnline())})))}unmount(){this.#T--,0===this.#T&&(this.#F?.(),this.#F=void 0,this.#k?.(),this.#k=void 0)}isFetching(e){return this.#q.findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return this.#s.findAll({...e,status:"pending"}).length}getQueryData(e){return this.#q.find({queryKey:e})?.state.data}ensureQueryData(e){const t=this.getQueryData(e.queryKey);return void 0!==t?Promise.resolve(t):this.fetchQuery(e)}getQueriesData(e){return this.getQueryCache().findAll(e).map((({queryKey:e,state:t})=>[e,t.data]))}setQueryData(e,t,r){const n=this.#q.find({queryKey:e}),s=n?.state.data,i=(0,c.functionalUpdate)(t,s);if(void 0===i)return;const o=this.defaultQueryOptions({queryKey:e});return this.#q.build(this,o).setData(i,{...r,manual:!0})}setQueriesData(e,t,r){return f.notifyManager.batch((()=>this.getQueryCache().findAll(e).map((({queryKey:e})=>[e,this.setQueryData(e,t,r)]))))}getQueryState(e){return this.#q.find({queryKey:e})?.state}removeQueries(e){const t=this.#q;f.notifyManager.batch((()=>{t.findAll(e).forEach((e=>{t.remove(e)}))}))}resetQueries(e,t){const r=this.#q,n={type:"active",...e};return f.notifyManager.batch((()=>(r.findAll(e).forEach((e=>{e.reset()})),this.refetchQueries(n,t))))}cancelQueries(e={},t={}){const r={revert:!0,...t},n=f.notifyManager.batch((()=>this.#q.findAll(e).map((e=>e.cancel(r)))));return Promise.all(n).then(c.noop).catch(c.noop)}invalidateQueries(e={},t={}){return f.notifyManager.batch((()=>{if(this.#q.findAll(e).forEach((e=>{e.invalidate()})),"none"===e.refetchType)return Promise.resolve();const r={...e,type:e.refetchType??e.type??"active"};return this.refetchQueries(r,t)}))}refetchQueries(e={},t){const r={...t,cancelRefetch:t?.cancelRefetch??!0},n=f.notifyManager.batch((()=>this.#q.findAll(e).filter((e=>!e.isDisabled())).map((e=>{let t=e.fetch(void 0,r);return r.throwOnError||(t=t.catch(c.noop)),"paused"===e.state.fetchStatus?Promise.resolve():t}))));return Promise.all(n).then(c.noop)}fetchQuery(e){const t=this.defaultQueryOptions(e);void 0===t.retry&&(t.retry=!1);const r=this.#q.build(this,t);return r.isStaleByTime(t.staleTime)?r.fetch(t):Promise.resolve(r.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(c.noop).catch(c.noop)}fetchInfiniteQuery(e){return e.behavior=(0,y.infiniteQueryBehavior)(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(c.noop).catch(c.noop)}resumePausedMutations(){return this.#s.resumePausedMutations()}getQueryCache(){return this.#q}getMutationCache(){return this.#s}getDefaultOptions(){return this.#n}setDefaultOptions(e){this.#n=e}setQueryDefaults(e,t){this.#D.set((0,c.hashKey)(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...this.#D.values()];let r={};return t.forEach((t=>{(0,c.partialMatchKey)(e,t.queryKey)&&(r={...r,...t.defaultOptions})})),r}setMutationDefaults(e,t){this.#I.set((0,c.hashKey)(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...this.#I.values()];let r={};return t.forEach((t=>{(0,c.partialMatchKey)(e,t.mutationKey)&&(r={...r,...t.defaultOptions})})),r}defaultQueryOptions(e){if(e?._defaulted)return e;const t={...this.#n.queries,...e?.queryKey&&this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=(0,c.hashQueryKeyByOptions)(t.queryKey,t)),void 0===t.refetchOnReconnect&&(t.refetchOnReconnect="always"!==t.networkMode),void 0===t.throwOnError&&(t.throwOnError=!!t.suspense),void 0===t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t}defaultMutationOptions(e){return e?._defaulted?e:{...this.#n.mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){this.#q.clear(),this.#s.clear()}}},59716:(e,t,r)=>{"use strict";var n,s=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(u,{QueryObserver:()=>f}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))a.call(e,u)||u===r||s(e,u,{get:()=>t[u],enumerable:!(n=i(t,u))||n.enumerable});return e})(s({},"__esModule",{value:!0}),n));var c=r(14525),l=r(27842),h=r(62238),p=r(33949),d=r(71739),f=class extends p.Subscribable{constructor(e,t){super(),this.#W=void 0,this.#N=void 0,this.#h=void 0,this.#A=new Set,this.#p=e,this.options=t,this.#B=null,this.bindMethods(),this.setOptions(t)}#p;#W;#N;#h;#U;#K;#B;#L;#H;#V;#G;#z;#Z;#A;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(this.#W.addObserver(this),shouldFetchOnMount(this.#W,this.options)&&this.#$(),this.#X())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return shouldFetchOn(this.#W,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return shouldFetchOn(this.#W,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#J(),this.#Y(),this.#W.removeObserver(this)}setOptions(e,t){const r=this.options,n=this.#W;if(this.options=this.#p.defaultQueryOptions(e),(0,c.shallowEqualObjects)(r,this.options)||this.#p.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#W,observer:this}),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled)throw new Error("Expected enabled to be a boolean");this.options.queryKey||(this.options.queryKey=r.queryKey),this.#ee();const s=this.hasListeners();s&&shouldFetchOptionally(this.#W,n,this.options,r)&&this.#$(),this.updateResult(t),!s||this.#W===n&&this.options.enabled===r.enabled&&this.options.staleTime===r.staleTime||this.#te();const i=this.#re();!s||this.#W===n&&this.options.enabled===r.enabled&&i===this.#Z||this.#ne(i)}getOptimisticResult(e){const t=this.#p.getQueryCache().build(this.#p,e),r=this.createResult(t,e);return function shouldAssignObserverCurrentProperties(e,t){if(!(0,c.shallowEqualObjects)(e.getCurrentResult(),t))return!0;return!1}(this,r)&&(this.#h=r,this.#K=this.options,this.#U=this.#W.state),r}getCurrentResult(){return this.#h}trackResult(e){const t={};return Object.keys(e).forEach((r=>{Object.defineProperty(t,r,{configurable:!1,enumerable:!0,get:()=>(this.#A.add(r),e[r])})})),t}getCurrentQuery(){return this.#W}refetch({...e}={}){return this.fetch({...e})}fetchOptimistic(e){const t=this.#p.defaultQueryOptions(e),r=this.#p.getQueryCache().build(this.#p,t);return r.isFetchingOptimistic=!0,r.fetch().then((()=>this.createResult(r,t)))}fetch(e){return this.#$({...e,cancelRefetch:e.cancelRefetch??!0}).then((()=>(this.updateResult(),this.#h)))}#$(e){this.#ee();let t=this.#W.fetch(this.options,e);return e?.throwOnError||(t=t.catch(c.noop)),t}#te(){if(this.#J(),c.isServer||this.#h.isStale||!(0,c.isValidTimeout)(this.options.staleTime))return;const e=(0,c.timeUntilStale)(this.#h.dataUpdatedAt,this.options.staleTime)+1;this.#G=setTimeout((()=>{this.#h.isStale||this.updateResult()}),e)}#re(){return("function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.#W):this.options.refetchInterval)??!1}#ne(e){this.#Y(),this.#Z=e,!c.isServer&&!1!==this.options.enabled&&(0,c.isValidTimeout)(this.#Z)&&0!==this.#Z&&(this.#z=setInterval((()=>{(this.options.refetchIntervalInBackground||h.focusManager.isFocused())&&this.#$()}),this.#Z))}#X(){this.#te(),this.#ne(this.#re())}#J(){this.#G&&(clearTimeout(this.#G),this.#G=void 0)}#Y(){this.#z&&(clearInterval(this.#z),this.#z=void 0)}createResult(e,t){const r=this.#W,n=this.options,s=this.#h,i=this.#U,o=this.#K,a=e!==r?e.state:this.#N,{state:u}=e;let l,{error:h,errorUpdatedAt:p,fetchStatus:f,status:y}=u,b=!1;if(t._optimisticResults){const s=this.hasListeners(),i=!s&&shouldFetchOnMount(e,t),o=s&&shouldFetchOptionally(e,r,t,n);(i||o)&&(f=(0,d.canFetch)(e.options.networkMode)?"fetching":"paused",u.dataUpdatedAt||(y="pending")),"isRestoring"===t._optimisticResults&&(f="idle")}if(t.select&&void 0!==u.data)if(s&&u.data===i?.data&&t.select===this.#L)l=this.#H;else try{this.#L=t.select,l=t.select(u.data),l=(0,c.replaceData)(s?.data,l,t),this.#H=l,this.#B=null}catch(e){this.#B=e}else l=u.data;if(void 0!==t.placeholderData&&void 0===l&&"pending"===y){let e;if(s?.isPlaceholderData&&t.placeholderData===o?.placeholderData)e=s.data;else if(e="function"==typeof t.placeholderData?t.placeholderData(this.#V?.state.data,this.#V):t.placeholderData,t.select&&void 0!==e)try{e=t.select(e),this.#B=null}catch(e){this.#B=e}void 0!==e&&(y="success",l=(0,c.replaceData)(s?.data,e,t),b=!0)}this.#B&&(h=this.#B,l=this.#H,p=Date.now(),y="error");const m="fetching"===f,v="pending"===y,O="error"===y,g=v&&m;return{status:y,fetchStatus:f,isPending:v,isSuccess:"success"===y,isError:O,isInitialLoading:g,isLoading:g,data:l,dataUpdatedAt:u.dataUpdatedAt,error:h,errorUpdatedAt:p,failureCount:u.fetchFailureCount,failureReason:u.fetchFailureReason,errorUpdateCount:u.errorUpdateCount,isFetched:u.dataUpdateCount>0||u.errorUpdateCount>0,isFetchedAfterMount:u.dataUpdateCount>a.dataUpdateCount||u.errorUpdateCount>a.errorUpdateCount,isFetching:m,isRefetching:m&&!v,isLoadingError:O&&0===u.dataUpdatedAt,isPaused:"paused"===f,isPlaceholderData:b,isRefetchError:O&&0!==u.dataUpdatedAt,isStale:isStale(e,t),refetch:this.refetch}}updateResult(e){const t=this.#h,r=this.createResult(this.#W,this.options);if(this.#U=this.#W.state,this.#K=this.options,(0,c.shallowEqualObjects)(r,t))return;void 0!==this.#U.data&&(this.#V=this.#W),this.#h=r;const n={};!1!==e?.listeners&&(()=>{if(!t)return!0;const{notifyOnChangeProps:e}=this.options,r="function"==typeof e?e():e;if("all"===r||!r&&!this.#A.size)return!0;const n=new Set(r??this.#A);return this.options.throwOnError&&n.add("error"),Object.keys(this.#h).some((e=>{const r=e;return this.#h[r]!==t[r]&&n.has(r)}))})()&&(n.listeners=!0),this.#b({...n,...e})}#ee(){const e=this.#p.getQueryCache().build(this.#p,this.options);if(e===this.#W)return;const t=this.#W;this.#W=e,this.#N=e.state,this.hasListeners()&&(t?.removeObserver(this),e.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#X()}#b(e){l.notifyManager.batch((()=>{e.listeners&&this.listeners.forEach((e=>{e(this.#h)})),this.#p.getQueryCache().notify({query:this.#W,type:"observerResultsUpdated"})}))}};function shouldFetchOnMount(e,t){return function shouldLoadOnMount(e,t){return!(!1===t.enabled||e.state.dataUpdatedAt||"error"===e.state.status&&!1===t.retryOnMount)}(e,t)||e.state.dataUpdatedAt>0&&shouldFetchOn(e,t,t.refetchOnMount)}function shouldFetchOn(e,t,r){if(!1!==t.enabled){const n="function"==typeof r?r(e):r;return"always"===n||!1!==n&&isStale(e,t)}return!1}function shouldFetchOptionally(e,t,r,n){return!1!==r.enabled&&(e!==t||!1===n.enabled)&&(!r.suspense||"error"!==e.state.status)&&isStale(e,r)}function isStale(e,t){return e.isStaleByTime(t.staleTime)}},85603:(e,t,r)=>{"use strict";var n,s=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(u,{Removable:()=>l}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))a.call(e,u)||u===r||s(e,u,{get:()=>t[u],enumerable:!(n=i(t,u))||n.enumerable});return e})(s({},"__esModule",{value:!0}),n));var c=r(14525),l=class{#se;destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),(0,c.isValidTimeout)(this.gcTime)&&(this.#se=setTimeout((()=>{this.optionalRemove()}),this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(c.isServer?1/0:3e5))}clearGcTimeout(){this.#se&&(clearTimeout(this.#se),this.#se=void 0)}}},71739:(e,t,r)=>{"use strict";var n,s=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(u,{CancelledError:()=>p,canFetch:()=>canFetch,createRetryer:()=>createRetryer,isCancelledError:()=>isCancelledError}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))a.call(e,u)||u===r||s(e,u,{get:()=>t[u],enumerable:!(n=i(t,u))||n.enumerable});return e})(s({},"__esModule",{value:!0}),n));var c=r(62238),l=r(11044),h=r(14525);function defaultRetryDelay(e){return Math.min(1e3*2**e,3e4)}function canFetch(e){return"online"!==(e??"online")||l.onlineManager.isOnline()}var p=class{constructor(e){this.revert=e?.revert,this.silent=e?.silent}};function isCancelledError(e){return e instanceof p}function createRetryer(e){let t,r,n,s=!1,i=0,o=!1;const a=new Promise(((e,t)=>{r=e,n=t})),shouldPause=()=>!c.focusManager.isFocused()||"always"!==e.networkMode&&!l.onlineManager.isOnline(),resolve=n=>{o||(o=!0,e.onSuccess?.(n),t?.(),r(n))},reject=r=>{o||(o=!0,e.onError?.(r),t?.(),n(r))},pause=()=>new Promise((r=>{t=e=>{const t=o||!shouldPause();return t&&r(e),t},e.onPause?.()})).then((()=>{t=void 0,o||e.onContinue?.()})),run=()=>{if(o)return;let t;try{t=e.fn()}catch(e){t=Promise.reject(e)}Promise.resolve(t).then(resolve).catch((t=>{if(o)return;const r=e.retry??(h.isServer?0:3),n=e.retryDelay??defaultRetryDelay,a="function"==typeof n?n(i,t):n,u=!0===r||"number"==typeof r&&i<r||"function"==typeof r&&r(i,t);!s&&u?(i++,e.onFail?.(i,t),(0,h.sleep)(a).then((()=>{if(shouldPause())return pause()})).then((()=>{s?reject(t):run()}))):reject(t)}))};return canFetch(e.networkMode)?run():pause().then(run),{promise:a,cancel:t=>{o||(reject(new p(t)),e.abort?.())},continue:()=>{const e=t?.();return e?a:Promise.resolve()},cancelRetry:()=>{s=!0},continueRetry:()=>{s=!1}}}},33949:e=>{"use strict";var t,r=Object.defineProperty,n=Object.getOwnPropertyDescriptor,s=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,o={};((e,t)=>{for(var n in t)r(e,n,{get:t[n],enumerable:!0})})(o,{Subscribable:()=>a}),e.exports=(t=o,((e,t,o,a)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of s(t))i.call(e,u)||u===o||r(e,u,{get:()=>t[u],enumerable:!(a=n(t,u))||a.enumerable});return e})(r({},"__esModule",{value:!0}),t));var a=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}}},53749:e=>{"use strict";var t,r=Object.defineProperty,n=Object.getOwnPropertyDescriptor,s=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty;e.exports=(t={},((e,t,o,a)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of s(t))i.call(e,u)||u===o||r(e,u,{get:()=>t[u],enumerable:!(a=n(t,u))||a.enumerable});return e})(r({},"__esModule",{value:!0}),t))},14525:e=>{"use strict";var t,r=Object.defineProperty,n=Object.getOwnPropertyDescriptor,s=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,o={};((e,t)=>{for(var n in t)r(e,n,{get:t[n],enumerable:!0})})(o,{addToEnd:()=>addToEnd,addToStart:()=>addToStart,functionalUpdate:()=>functionalUpdate,hashKey:()=>hashKey,hashQueryKeyByOptions:()=>hashQueryKeyByOptions,isPlainArray:()=>isPlainArray,isPlainObject:()=>isPlainObject,isServer:()=>a,isValidTimeout:()=>isValidTimeout,keepPreviousData:()=>keepPreviousData,matchMutation:()=>matchMutation,matchQuery:()=>matchQuery,noop:()=>noop,partialMatchKey:()=>partialMatchKey,replaceData:()=>replaceData,replaceEqualDeep:()=>replaceEqualDeep,scheduleMicrotask:()=>scheduleMicrotask,shallowEqualObjects:()=>shallowEqualObjects,sleep:()=>sleep,timeUntilStale:()=>timeUntilStale}),e.exports=(t=o,((e,t,o,a)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of s(t))i.call(e,u)||u===o||r(e,u,{get:()=>t[u],enumerable:!(a=n(t,u))||a.enumerable});return e})(r({},"__esModule",{value:!0}),t));var a="undefined"==typeof window||"Deno"in window;function noop(){}function functionalUpdate(e,t){return"function"==typeof e?e(t):e}function isValidTimeout(e){return"number"==typeof e&&e>=0&&e!==1/0}function timeUntilStale(e,t){return Math.max(e+(t||0)-Date.now(),0)}function matchQuery(e,t){const{type:r="all",exact:n,fetchStatus:s,predicate:i,queryKey:o,stale:a}=e;if(o)if(n){if(t.queryHash!==hashQueryKeyByOptions(o,t.options))return!1}else if(!partialMatchKey(t.queryKey,o))return!1;if("all"!==r){const e=t.isActive();if("active"===r&&!e)return!1;if("inactive"===r&&e)return!1}return("boolean"!=typeof a||t.isStale()===a)&&((void 0===s||s===t.state.fetchStatus)&&!(i&&!i(t)))}function matchMutation(e,t){const{exact:r,status:n,predicate:s,mutationKey:i}=e;if(i){if(!t.options.mutationKey)return!1;if(r){if(hashKey(t.options.mutationKey)!==hashKey(i))return!1}else if(!partialMatchKey(t.options.mutationKey,i))return!1}return(!n||t.state.status===n)&&!(s&&!s(t))}function hashQueryKeyByOptions(e,t){return(t?.queryKeyHashFn||hashKey)(e)}function hashKey(e){return JSON.stringify(e,((e,t)=>isPlainObject(t)?Object.keys(t).sort().reduce(((e,r)=>(e[r]=t[r],e)),{}):t))}function partialMatchKey(e,t){return e===t||typeof e==typeof t&&(!(!e||!t||"object"!=typeof e||"object"!=typeof t)&&!Object.keys(t).some((r=>!partialMatchKey(e[r],t[r]))))}function replaceEqualDeep(e,t){if(e===t)return e;const r=isPlainArray(e)&&isPlainArray(t);if(r||isPlainObject(e)&&isPlainObject(t)){const n=r?e.length:Object.keys(e).length,s=r?t:Object.keys(t),i=s.length,o=r?[]:{};let a=0;for(let n=0;n<i;n++){const i=r?n:s[n];o[i]=replaceEqualDeep(e[i],t[i]),o[i]===e[i]&&a++}return n===i&&a===n?e:o}return t}function shallowEqualObjects(e,t){if(e&&!t||t&&!e)return!1;for(const r in e)if(e[r]!==t[r])return!1;return!0}function isPlainArray(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function isPlainObject(e){if(!hasObjectPrototype(e))return!1;const t=e.constructor;if(void 0===t)return!0;const r=t.prototype;return!!hasObjectPrototype(r)&&!!r.hasOwnProperty("isPrototypeOf")}function hasObjectPrototype(e){return"[object Object]"===Object.prototype.toString.call(e)}function sleep(e){return new Promise((t=>{setTimeout(t,e)}))}function scheduleMicrotask(e){sleep(0).then(e)}function replaceData(e,t,r){return"function"==typeof r.structuralSharing?r.structuralSharing(e,t):!1!==r.structuralSharing?replaceEqualDeep(e,t):t}function keepPreviousData(e){return e}function addToEnd(e,t,r=0){const n=[...e,t];return r&&n.length>r?n.slice(1):n}function addToStart(e,t,r=0){const n=[t,...e];return r&&n.length>r?n.slice(0,-1):n}},32969:(e,t,r)=>{"use strict";var n,s=Object.create,i=Object.defineProperty,o=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,u=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let s of a(t))c.call(e,s)||s===r||i(e,s,{get:()=>t[s],enumerable:!(n=o(t,s))||n.enumerable});return e},l={};((e,t)=>{for(var r in t)i(e,r,{get:t[r],enumerable:!0})})(l,{HydrationBoundary:()=>HydrationBoundary}),e.exports=(n=l,__copyProps(i({},"__esModule",{value:!0}),n));var h=((e,t,r)=>(r=null!=e?s(u(e)):{},__copyProps(!t&&e&&e.__esModule?r:i(r,"default",{value:e,enumerable:!0}),e)))(r(87363),1),p=r(61528),d=r(43653),HydrationBoundary=({children:e,options:t={},state:r,queryClient:n})=>{const s=(0,d.useQueryClient)(n),[i,o]=h.useState(),a=h.useRef(t);return a.current=t,h.useMemo((()=>{if(r){if("object"!=typeof r)return;const e=s.getQueryCache(),t=r.queries||[],n=[],u=[];for(const r of t){const t=e.get(r.queryHash);if(t){const e=r.state.dataUpdatedAt>t.state.dataUpdatedAt,n=i?.find((e=>e.queryHash===r.queryHash));e&&(!n||r.state.dataUpdatedAt>n.state.dataUpdatedAt)&&u.push(r)}else n.push(r)}n.length>0&&(0,p.hydrate)(s,{queries:n},a.current),u.length>0&&o((e=>e?[...e,...u]:u))}}),[s,i,r]),h.useEffect((()=>{i&&((0,p.hydrate)(s,{queries:i},a.current),o(void 0))}),[s,i]),e}},43653:(e,t,r)=>{"use strict";var n,s=Object.create,i=Object.defineProperty,o=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,u=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let s of a(t))c.call(e,s)||s===r||i(e,s,{get:()=>t[s],enumerable:!(n=o(t,s))||n.enumerable});return e},l={};((e,t)=>{for(var r in t)i(e,r,{get:t[r],enumerable:!0})})(l,{QueryClientContext:()=>p,QueryClientProvider:()=>QueryClientProvider,useQueryClient:()=>useQueryClient}),e.exports=(n=l,__copyProps(i({},"__esModule",{value:!0}),n));var h=((e,t,r)=>(r=null!=e?s(u(e)):{},__copyProps(!t&&e&&e.__esModule?r:i(r,"default",{value:e,enumerable:!0}),e)))(r(87363),1),p=h.createContext(void 0),useQueryClient=e=>{const t=h.useContext(p);if(e)return e;if(!t)throw new Error("No QueryClient set, use QueryClientProvider to set one");return t},QueryClientProvider=({client:e,children:t})=>(h.useEffect((()=>(e.mount(),()=>{e.unmount()})),[e]),h.createElement(p.Provider,{value:e},t))},15178:(e,t,r)=>{"use strict";var n,s=Object.create,i=Object.defineProperty,o=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,u=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let s of a(t))c.call(e,s)||s===r||i(e,s,{get:()=>t[s],enumerable:!(n=o(t,s))||n.enumerable});return e},l={};((e,t)=>{for(var r in t)i(e,r,{get:t[r],enumerable:!0})})(l,{QueryErrorResetBoundary:()=>QueryErrorResetBoundary,useQueryErrorResetBoundary:()=>useQueryErrorResetBoundary}),e.exports=(n=l,__copyProps(i({},"__esModule",{value:!0}),n));var h=((e,t,r)=>(r=null!=e?s(u(e)):{},__copyProps(!t&&e&&e.__esModule?r:i(r,"default",{value:e,enumerable:!0}),e)))(r(87363),1);function createValue(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}var p=h.createContext(createValue()),useQueryErrorResetBoundary=()=>h.useContext(p),QueryErrorResetBoundary=({children:e})=>{const[t]=h.useState((()=>createValue()));return h.createElement(p.Provider,{value:t},"function"==typeof e?e(t):e)}},94025:(e,t,r)=>{"use strict";var n,s=Object.create,i=Object.defineProperty,o=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,u=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let s of a(t))c.call(e,s)||s===r||i(e,s,{get:()=>t[s],enumerable:!(n=o(t,s))||n.enumerable});return e},l={};((e,t)=>{for(var r in t)i(e,r,{get:t[r],enumerable:!0})})(l,{ensurePreventErrorBoundaryRetry:()=>ensurePreventErrorBoundaryRetry,getHasError:()=>getHasError,useClearResetErrorBoundary:()=>useClearResetErrorBoundary}),e.exports=(n=l,__copyProps(i({},"__esModule",{value:!0}),n));var h=((e,t,r)=>(r=null!=e?s(u(e)):{},__copyProps(!t&&e&&e.__esModule?r:i(r,"default",{value:e,enumerable:!0}),e)))(r(87363),1),p=r(6609),ensurePreventErrorBoundaryRetry=(e,t)=>{(e.suspense||e.throwOnError)&&(t.isReset()||(e.retryOnMount=!1))},useClearResetErrorBoundary=e=>{h.useEffect((()=>{e.clearReset()}),[e])},getHasError=({result:e,errorResetBoundary:t,throwOnError:r,query:n})=>e.isError&&!t.isReset()&&!e.isFetching&&(0,p.shouldThrowError)(r,[e.error,n])},39937:(e,t,r)=>{"use strict";var n,s=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))a.call(e,u)||u===r||s(e,u,{get:()=>t[u],enumerable:!(n=i(t,u))||n.enumerable});return e},__reExport=(e,t,r)=>(__copyProps(e,t,"default"),r&&__copyProps(r,t,"default")),u={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(u,{HydrationBoundary:()=>m.HydrationBoundary,IsRestoringProvider:()=>j.IsRestoringProvider,QueryClientContext:()=>b.QueryClientContext,QueryClientProvider:()=>b.QueryClientProvider,QueryErrorResetBoundary:()=>v.QueryErrorResetBoundary,infiniteQueryOptions:()=>y.infiniteQueryOptions,queryOptions:()=>f.queryOptions,useInfiniteQuery:()=>w.useInfiniteQuery,useIsFetching:()=>O.useIsFetching,useIsMutating:()=>g.useIsMutating,useIsRestoring:()=>j.useIsRestoring,useMutation:()=>P.useMutation,useMutationState:()=>g.useMutationState,useQueries:()=>c.useQueries,useQuery:()=>l.useQuery,useQueryClient:()=>b.useQueryClient,useQueryErrorResetBoundary:()=>v.useQueryErrorResetBoundary,useSuspenseInfiniteQuery:()=>p.useSuspenseInfiniteQuery,useSuspenseQueries:()=>d.useSuspenseQueries,useSuspenseQuery:()=>h.useSuspenseQuery}),e.exports=(n=u,__copyProps(s({},"__esModule",{value:!0}),n)),__reExport(u,r(61528),e.exports),__reExport(u,r(429),e.exports);var c=r(25405),l=r(47313),h=r(29134),p=r(22715),d=r(15605),f=r(56701),y=r(19777),b=r(43653),m=r(32969),v=r(15178),O=r(43351),g=r(72836),P=r(89339),w=r(93217),j=r(46732)},19777:e=>{"use strict";var t,r=Object.defineProperty,n=Object.getOwnPropertyDescriptor,s=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,o={};function infiniteQueryOptions(e){return e}((e,t)=>{for(var n in t)r(e,n,{get:t[n],enumerable:!0})})(o,{infiniteQueryOptions:()=>infiniteQueryOptions}),e.exports=(t=o,((e,t,o,a)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of s(t))i.call(e,u)||u===o||r(e,u,{get:()=>t[u],enumerable:!(a=n(t,u))||a.enumerable});return e})(r({},"__esModule",{value:!0}),t))},46732:(e,t,r)=>{"use strict";var n,s=Object.create,i=Object.defineProperty,o=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,u=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let s of a(t))c.call(e,s)||s===r||i(e,s,{get:()=>t[s],enumerable:!(n=o(t,s))||n.enumerable});return e},l={};((e,t)=>{for(var r in t)i(e,r,{get:t[r],enumerable:!0})})(l,{IsRestoringProvider:()=>d,useIsRestoring:()=>useIsRestoring}),e.exports=(n=l,__copyProps(i({},"__esModule",{value:!0}),n));var h=((e,t,r)=>(r=null!=e?s(u(e)):{},__copyProps(!t&&e&&e.__esModule?r:i(r,"default",{value:e,enumerable:!0}),e)))(r(87363),1),p=h.createContext(!1),useIsRestoring=()=>h.useContext(p),d=p.Provider},56701:e=>{"use strict";var t,r=Object.defineProperty,n=Object.getOwnPropertyDescriptor,s=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,o={};function queryOptions(e){return e}((e,t)=>{for(var n in t)r(e,n,{get:t[n],enumerable:!0})})(o,{queryOptions:()=>queryOptions}),e.exports=(t=o,((e,t,o,a)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of s(t))i.call(e,u)||u===o||r(e,u,{get:()=>t[u],enumerable:!(a=n(t,u))||a.enumerable});return e})(r({},"__esModule",{value:!0}),t))},28495:e=>{"use strict";var t,r=Object.defineProperty,n=Object.getOwnPropertyDescriptor,s=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,o={};((e,t)=>{for(var n in t)r(e,n,{get:t[n],enumerable:!0})})(o,{defaultThrowOnError:()=>defaultThrowOnError,ensureStaleTime:()=>ensureStaleTime,fetchOptimistic:()=>fetchOptimistic,shouldSuspend:()=>shouldSuspend,willFetch:()=>willFetch}),e.exports=(t=o,((e,t,o,a)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of s(t))i.call(e,u)||u===o||r(e,u,{get:()=>t[u],enumerable:!(a=n(t,u))||a.enumerable});return e})(r({},"__esModule",{value:!0}),t));var defaultThrowOnError=(e,t)=>void 0===t.state.data,ensureStaleTime=e=>{e.suspense&&"number"!=typeof e.staleTime&&(e.staleTime=1e3)},willFetch=(e,t)=>e.isLoading&&e.isFetching&&!t,shouldSuspend=(e,t,r)=>e?.suspense&&willFetch(t,r),fetchOptimistic=(e,t,r)=>t.fetchOptimistic(e).catch((()=>{r.clearReset()}))},429:e=>{"use strict";var t,r=Object.defineProperty,n=Object.getOwnPropertyDescriptor,s=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty;e.exports=(t={},((e,t,o,a)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of s(t))i.call(e,u)||u===o||r(e,u,{get:()=>t[u],enumerable:!(a=n(t,u))||a.enumerable});return e})(r({},"__esModule",{value:!0}),t))},84238:(e,t,r)=>{"use strict";var n,s=Object.create,i=Object.defineProperty,o=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,u=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let s of a(t))c.call(e,s)||s===r||i(e,s,{get:()=>t[s],enumerable:!(n=o(t,s))||n.enumerable});return e},l={};((e,t)=>{for(var r in t)i(e,r,{get:t[r],enumerable:!0})})(l,{useBaseQuery:()=>useBaseQuery}),e.exports=(n=l,__copyProps(i({},"__esModule",{value:!0}),n));var h=((e,t,r)=>(r=null!=e?s(u(e)):{},__copyProps(!t&&e&&e.__esModule?r:i(r,"default",{value:e,enumerable:!0}),e)))(r(87363),1),p=r(61528),d=r(15178),f=r(43653),y=r(46732),b=r(94025),m=r(28495);function useBaseQuery(e,t,r){const n=(0,f.useQueryClient)(r),s=(0,y.useIsRestoring)(),i=(0,d.useQueryErrorResetBoundary)(),o=n.defaultQueryOptions(e);o._optimisticResults=s?"isRestoring":"optimistic",(0,m.ensureStaleTime)(o),(0,b.ensurePreventErrorBoundaryRetry)(o,i),(0,b.useClearResetErrorBoundary)(i);const[a]=h.useState((()=>new t(n,o))),u=a.getOptimisticResult(o);if(h.useSyncExternalStore(h.useCallback((e=>{const t=s?()=>{}:a.subscribe(p.notifyManager.batchCalls(e));return a.updateResult(),t}),[a,s]),(()=>a.getCurrentResult()),(()=>a.getCurrentResult())),h.useEffect((()=>{a.setOptions(o,{listeners:!1})}),[o,a]),(0,m.shouldSuspend)(o,u,s))throw(0,m.fetchOptimistic)(o,a,i);if((0,b.getHasError)({result:u,errorResetBoundary:i,throwOnError:o.throwOnError,query:a.getCurrentQuery()}))throw u.error;return o.notifyOnChangeProps?u:a.trackResult(u)}},93217:(e,t,r)=>{"use strict";var n,s=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(u,{useInfiniteQuery:()=>useInfiniteQuery}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))a.call(e,u)||u===r||s(e,u,{get:()=>t[u],enumerable:!(n=i(t,u))||n.enumerable});return e})(s({},"__esModule",{value:!0}),n));var c=r(61528),l=r(84238);function useInfiniteQuery(e,t){return(0,l.useBaseQuery)(e,c.InfiniteQueryObserver,t)}},43351:(e,t,r)=>{"use strict";var n,s=Object.create,i=Object.defineProperty,o=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,u=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let s of a(t))c.call(e,s)||s===r||i(e,s,{get:()=>t[s],enumerable:!(n=o(t,s))||n.enumerable});return e},l={};((e,t)=>{for(var r in t)i(e,r,{get:t[r],enumerable:!0})})(l,{useIsFetching:()=>useIsFetching}),e.exports=(n=l,__copyProps(i({},"__esModule",{value:!0}),n));var h=((e,t,r)=>(r=null!=e?s(u(e)):{},__copyProps(!t&&e&&e.__esModule?r:i(r,"default",{value:e,enumerable:!0}),e)))(r(87363),1),p=r(61528),d=r(43653);function useIsFetching(e,t){const r=(0,d.useQueryClient)(t),n=r.getQueryCache();return h.useSyncExternalStore(h.useCallback((e=>n.subscribe(p.notifyManager.batchCalls(e))),[n]),(()=>r.isFetching(e)),(()=>r.isFetching(e)))}},89339:(e,t,r)=>{"use strict";var n,s=Object.create,i=Object.defineProperty,o=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,u=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let s of a(t))c.call(e,s)||s===r||i(e,s,{get:()=>t[s],enumerable:!(n=o(t,s))||n.enumerable});return e},l={};((e,t)=>{for(var r in t)i(e,r,{get:t[r],enumerable:!0})})(l,{useMutation:()=>useMutation}),e.exports=(n=l,__copyProps(i({},"__esModule",{value:!0}),n));var h=((e,t,r)=>(r=null!=e?s(u(e)):{},__copyProps(!t&&e&&e.__esModule?r:i(r,"default",{value:e,enumerable:!0}),e)))(r(87363),1),p=r(61528),d=r(43653),f=r(6609);function useMutation(e,t){const r=(0,d.useQueryClient)(t),[n]=h.useState((()=>new p.MutationObserver(r,e)));h.useEffect((()=>{n.setOptions(e)}),[n,e]);const s=h.useSyncExternalStore(h.useCallback((e=>n.subscribe(p.notifyManager.batchCalls(e))),[n]),(()=>n.getCurrentResult()),(()=>n.getCurrentResult())),i=h.useCallback(((e,t)=>{n.mutate(e,t).catch(noop)}),[n]);if(s.error&&(0,f.shouldThrowError)(n.options.throwOnError,[s.error]))throw s.error;return{...s,mutate:i,mutateAsync:s.mutate}}function noop(){}},72836:(e,t,r)=>{"use strict";var n,s=Object.create,i=Object.defineProperty,o=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,u=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let s of a(t))c.call(e,s)||s===r||i(e,s,{get:()=>t[s],enumerable:!(n=o(t,s))||n.enumerable});return e},l={};((e,t)=>{for(var r in t)i(e,r,{get:t[r],enumerable:!0})})(l,{useIsMutating:()=>useIsMutating,useMutationState:()=>useMutationState}),e.exports=(n=l,__copyProps(i({},"__esModule",{value:!0}),n));var h=((e,t,r)=>(r=null!=e?s(u(e)):{},__copyProps(!t&&e&&e.__esModule?r:i(r,"default",{value:e,enumerable:!0}),e)))(r(87363),1),p=r(61528),d=r(43653);function useIsMutating(e,t){return useMutationState({filters:{...e,status:"pending"}},(0,d.useQueryClient)(t)).length}function getResult(e,t){return e.findAll(t.filters).map((e=>t.select?t.select(e):e.state))}function useMutationState(e={},t){const r=(0,d.useQueryClient)(t).getMutationCache(),n=h.useRef(e),s=h.useRef();return s.current||(s.current=getResult(r,e)),h.useEffect((()=>{n.current=e})),h.useSyncExternalStore(h.useCallback((e=>r.subscribe((()=>{const t=(0,p.replaceEqualDeep)(s.current,getResult(r,n.current));s.current!==t&&(s.current=t,p.notifyManager.schedule(e))}))),[r]),(()=>s.current),(()=>s.current))}},25405:(e,t,r)=>{"use strict";var n,s=Object.create,i=Object.defineProperty,o=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,u=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let s of a(t))c.call(e,s)||s===r||i(e,s,{get:()=>t[s],enumerable:!(n=o(t,s))||n.enumerable});return e},l={};((e,t)=>{for(var r in t)i(e,r,{get:t[r],enumerable:!0})})(l,{useQueries:()=>useQueries}),e.exports=(n=l,__copyProps(i({},"__esModule",{value:!0}),n));var h=((e,t,r)=>(r=null!=e?s(u(e)):{},__copyProps(!t&&e&&e.__esModule?r:i(r,"default",{value:e,enumerable:!0}),e)))(r(87363),1),p=r(61528),d=r(43653),f=r(46732),y=r(15178),b=r(94025),m=r(28495);function useQueries({queries:e,...t},r){const n=(0,d.useQueryClient)(r),s=(0,f.useIsRestoring)(),i=(0,y.useQueryErrorResetBoundary)(),o=h.useMemo((()=>e.map((e=>{const t=n.defaultQueryOptions(e);return t._optimisticResults=s?"isRestoring":"optimistic",t}))),[e,n,s]);o.forEach((e=>{(0,m.ensureStaleTime)(e),(0,b.ensurePreventErrorBoundaryRetry)(e,i)})),(0,b.useClearResetErrorBoundary)(i);const[a]=h.useState((()=>new p.QueriesObserver(n,o,t))),[u,c,l]=a.getOptimisticResult(o);h.useSyncExternalStore(h.useCallback((e=>s?()=>{}:a.subscribe(p.notifyManager.batchCalls(e))),[a,s]),(()=>a.getCurrentResult()),(()=>a.getCurrentResult())),h.useEffect((()=>{a.setQueries(o,t,{listeners:!1})}),[o,t,a]);const v=u.some(((e,t)=>(0,m.shouldSuspend)(o[t],e,s)))?u.flatMap(((e,t)=>{const r=o[t];if(r){const t=new p.QueryObserver(n,r);if((0,m.shouldSuspend)(r,e,s))return(0,m.fetchOptimistic)(r,t,i);(0,m.willFetch)(e,s)&&(0,m.fetchOptimistic)(r,t,i)}return[]})):[];if(v.length>0)throw Promise.all(v);const O=a.getQueries(),g=u.find(((e,t)=>(0,b.getHasError)({result:e,errorResetBoundary:i,throwOnError:o[t]?.throwOnError??!1,query:O[t]})));if(g?.error)throw g.error;return c(l())}},47313:(e,t,r)=>{"use strict";var n,s=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(u,{useQuery:()=>useQuery}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))a.call(e,u)||u===r||s(e,u,{get:()=>t[u],enumerable:!(n=i(t,u))||n.enumerable});return e})(s({},"__esModule",{value:!0}),n));var c=r(61528),l=r(84238);function useQuery(e,t){return(0,l.useBaseQuery)(e,c.QueryObserver,t)}},22715:(e,t,r)=>{"use strict";var n,s=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(u,{useSuspenseInfiniteQuery:()=>useSuspenseInfiniteQuery}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))a.call(e,u)||u===r||s(e,u,{get:()=>t[u],enumerable:!(n=i(t,u))||n.enumerable});return e})(s({},"__esModule",{value:!0}),n));var c=r(61528),l=r(84238),h=r(28495);function useSuspenseInfiniteQuery(e,t){return(0,l.useBaseQuery)({...e,enabled:!0,suspense:!0,throwOnError:h.defaultThrowOnError},c.InfiniteQueryObserver,t)}},15605:(e,t,r)=>{"use strict";var n,s=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(u,{useSuspenseQueries:()=>useSuspenseQueries}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))a.call(e,u)||u===r||s(e,u,{get:()=>t[u],enumerable:!(n=i(t,u))||n.enumerable});return e})(s({},"__esModule",{value:!0}),n));var c=r(25405),l=r(28495);function useSuspenseQueries(e,t){return(0,c.useQueries)({...e,queries:e.queries.map((e=>({...e,suspense:!0,throwOnError:l.defaultThrowOnError,enabled:!0})))},t)}},29134:(e,t,r)=>{"use strict";var n,s=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(u,{useSuspenseQuery:()=>useSuspenseQuery}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))a.call(e,u)||u===r||s(e,u,{get:()=>t[u],enumerable:!(n=i(t,u))||n.enumerable});return e})(s({},"__esModule",{value:!0}),n));var c=r(61528),l=r(84238),h=r(28495);function useSuspenseQuery(e,t){return(0,l.useBaseQuery)({...e,enabled:!0,suspense:!0,throwOnError:h.defaultThrowOnError},c.QueryObserver,t)}},6609:e=>{"use strict";var t,r=Object.defineProperty,n=Object.getOwnPropertyDescriptor,s=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,o={};function shouldThrowError(e,t){return"function"==typeof e?e(...t):!!e}((e,t)=>{for(var n in t)r(e,n,{get:t[n],enumerable:!0})})(o,{shouldThrowError:()=>shouldThrowError}),e.exports=(t=o,((e,t,o,a)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of s(t))i.call(e,u)||u===o||r(e,u,{get:()=>t[u],enumerable:!(a=n(t,u))||a.enumerable});return e})(r({},"__esModule",{value:!0}),t))}},t={};function __webpack_require__(r){var n=t[r];if(void 0!==n)return n.exports;var s=t[r]={exports:{}};return e[r](s,s.exports,__webpack_require__),s.exports}(()=>{"use strict";var e,t,r=__webpack_require__(23066),n=__webpack_require__(58325);null!==(e=window)&&void 0!==e&&null!==(t=e.elementorV2)&&void 0!==t&&t.editorAppBar?(0,n.editorV2)():(0,r.editorV1)()})()})();