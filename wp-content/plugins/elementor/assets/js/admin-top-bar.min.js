/*! elementor - v3.23.0 - 05-08-2024 */
(()=>{var e={66535:(e,t,r)=>{"use strict";var n=r(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;_interopRequireWildcard(r(87363));var o=_interopRequireWildcard(r(61533)),a=r(37634);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==n(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var o={},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var u=a?Object.getOwnPropertyDescriptor(e,i):null;u&&(u.get||u.set)?Object.defineProperty(o,i,u):o[i]=e[i]}return o.default=e,r&&r.set(e,o),o}var i={render:function render(e,t){var r;try{var n=(0,a.createRoot)(t);n.render(e),r=function unmountFunction(){n.unmount()}}catch(n){o.render(e,t),r=function unmountFunction(){o.unmountComponentAtNode(t)}}return{unmount:r}}};t.default=i},73308:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=function matchUserAgent(e){return n.indexOf(e)>=0},n=navigator.userAgent,o=!!window.opr&&!!opr.addons||!!window.opera||r(" OPR/"),a=r("Firefox"),i=/^((?!chrome|android).)*safari/i.test(n)||/constructor/i.test(window.HTMLElement)||"[object SafariRemoteNotification]"===(!window.safari||"undefined"!=typeof safari&&safari.pushNotification).toString(),u=/Trident|MSIE/.test(n)&&!!document.documentMode,c=!u&&!!window.StyleMedia||r("Edg"),l=!!window.chrome&&r("Chrome")&&!(c||o),d=r("Chrome")&&!!window.CSS,s=r("AppleWebKit")&&!d,f={isTouchDevice:"ontouchstart"in window||navigator.maxTouchPoints>0||navigator.msMaxTouchPoints>0,appleWebkit:s,blink:d,chrome:l,edge:c,firefox:a,ie:u,mac:r("Macintosh"),opera:o,safari:i,webkit:r("AppleWebKit")};t.default=f},23680:(e,t,r)=>{"use strict";var n=r(38003).__,o=r(73203),a=r(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function AdminTopBar(){var e,t,r,o=(0,i.useRef)(),a=window.elementorAdminTopBarConfig.promotion;(0,i.useEffect)((function(){document.querySelector("#e-admin-top-bar-root").classList.add("e-admin-top-bar--active")}),[]);var f=(0,d.usePageTitle)();(0,i.useEffect)((function(){document.querySelectorAll(".page-title-action").forEach((function(e){o.current.appendChild(e)}))}),[]);var p=s.default.mac?"⌘":"^",m=n("Search or do anything in Elementor","elementor")+" ".concat(p,"+E"),_=null===(e=window)||void 0===e||null===(t=e.elementorNotificationCenter)||void 0===t?void 0:t.BarButtonNotification;return i.default.createElement("div",{className:"e-admin-top-bar"},i.default.createElement("div",{className:"e-admin-top-bar__main-area"},i.default.createElement(c.default,null,f),i.default.createElement("div",{className:"e-admin-top-bar__main-area-buttons",ref:o})),i.default.createElement("div",{className:"e-admin-top-bar__secondary-area"},i.default.createElement("div",{className:"e-admin-top-bar__secondary-area-buttons"},!elementorAppConfig.hasPro&&i.default.createElement(u.default,{additionalClasses:"accent",href:a.url,target:"__blank",icon:"eicon-upgrade-crown",iconAdditionalClasses:"crown-icon"},a.text),i.default.createElement(u.default,{href:window.elementorAdminTopBarConfig.apps_url,icon:"eicon-integration"},n("Add-ons","elementor")),window.elementorAdminTopBarConfig.is_administrator?i.default.createElement(u.default,{onClick:function finderAction(){$e.route("finder")},dataInfo:m,icon:"eicon-search-bold"},n("Finder","elementor")):"",window.elementorCloudAdmin?window.elementorCloudAdmin():"",_?i.default.createElement(_,{defaultIsRead:!(null!==(r=elementorNotifications)&&void 0!==r&&r.is_unread)},n("What's New","elementor")):""),i.default.createElement(l.default,null)))};var i=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==a(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var u=o?Object.getOwnPropertyDescriptor(e,i):null;u&&(u.get||u.set)?Object.defineProperty(n,i,u):n[i]=e[i]}n.default=e,r&&r.set(e,n);return n}(r(87363)),u=o(r(50818)),c=o(r(76668)),l=o(r(79943)),d=r(30174),s=o(r(73308));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}},50818:(e,t,r)=>{"use strict";var n=r(23615),o=r(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.default=BarButton;var a=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==o(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var u=a?Object.getOwnPropertyDescriptor(e,i):null;u&&(u.get||u.set)?Object.defineProperty(n,i,u):n[i]=e[i]}n.default=e,r&&r.set(e,n);return n}(r(87363));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}function BarButton(e){return(0,a.useEffect)((function(){e.dataInfo&&jQuery(".e-admin-top-bar__bar-button[data-info]").tipsy({title:function title(){return this.getAttribute("data-info")},gravity:function gravity(){return"n"},delayIn:400,offset:1})}),[]),a.default.createElement("a",{className:"e-admin-top-bar__bar-button ".concat(e.additionalClasses),ref:e.buttonRef,onClick:e.onClick,"data-info":e.dataInfo,href:e.href,target:e.target},a.default.createElement("i",{className:"e-admin-top-bar__bar-button-icon ".concat(e.icon," ").concat(e.iconAdditionalClasses)}),a.default.createElement("span",{className:"e-admin-top-bar__bar-button-title"},e.children))}BarButton.propTypes={children:n.any,dataInfo:n.string,icon:n.any,onClick:n.func,buttonRef:n.object,href:n.string,target:n.string,additionalClasses:n.string,iconAdditionalClasses:n.string}},76668:(e,t,r)=>{"use strict";var n=r(23615),o=r(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=BarHeading;var a=o(r(87363));function BarHeading(e){return a.default.createElement("div",{className:"e-admin-top-bar__heading"},a.default.createElement("div",{className:"e-logo-wrapper"},a.default.createElement("i",{className:"eicon-elementor","aria-hidden":"true"})),a.default.createElement("span",{className:"e-admin-top-bar__heading-title"},e.children))}BarHeading.propTypes={children:n.any}},79943:(e,t,r)=>{"use strict";var n=r(38003).__,o=r(73203),a=r(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function ConnectionButton(){var e=(0,i.useRef)(),t=elementorAdminTopBarConfig.is_user_connected;(0,i.useEffect)((function(){e.current&&!t&&jQuery(e.current).elementorConnect()}),[]);var r=n("Connect your account to get access to Elementor's Template Library & more.","elementor"),o=elementorAdminTopBarConfig.connect_url,a=n("Connect Account","elementor"),c="_self";t&&(r="",o="https://go.elementor.com/wp-dash-admin-bar-account/",a=n("My Elementor","elementor"),c="_blank");return i.default.createElement(u.default,{icon:"eicon-user-circle-o",buttonRef:e,dataInfo:r,href:o,target:c},a)};var i=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==a(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var u=o?Object.getOwnPropertyDescriptor(e,i):null;u&&(u.get||u.set)?Object.defineProperty(n,i,u):n[i]=e[i]}n.default=e,r&&r.set(e,n);return n}(r(87363)),u=o(r(50818));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}},30174:(e,t,r)=>{"use strict";var n=r(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.usePageTitle=void 0;var o=n(r(40131)),a=r(87363);t.usePageTitle=function usePageTitle(){var e=(0,a.useState)("Elementor"),t=(0,o.default)(e,2),r=t[0],n=t[1];return(0,a.useEffect)((function(){var e=document.querySelector(".wp-heading-inline");e&&n(e.innerText)}),[]),r}},58772:(e,t,r)=>{"use strict";var n=r(90331);function emptyFunction(){}function emptyFunctionWithReset(){}emptyFunctionWithReset.resetWarningCache=emptyFunction,e.exports=function(){function shim(e,t,r,o,a,i){if(i!==n){var u=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw u.name="Invariant Violation",u}}function getShim(){return shim}shim.isRequired=shim;var e={array:shim,bigint:shim,bool:shim,func:shim,number:shim,object:shim,string:shim,symbol:shim,any:shim,arrayOf:getShim,element:shim,elementType:shim,instanceOf:getShim,node:shim,objectOf:getShim,oneOf:getShim,oneOfType:getShim,shape:getShim,exact:getShim,checkPropTypes:emptyFunctionWithReset,resetWarningCache:emptyFunction};return e.PropTypes=e,e}},23615:(e,t,r)=>{e.exports=r(58772)()},90331:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},37634:(e,t,r)=>{"use strict";var n=r(61533);t.createRoot=n.createRoot,t.hydrateRoot=n.hydrateRoot},87363:e=>{"use strict";e.exports=React},61533:e=>{"use strict";e.exports=ReactDOM},38003:e=>{"use strict";e.exports=wp.i18n},98106:e=>{e.exports=function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n},e.exports.__esModule=!0,e.exports.default=e.exports},17358:e=>{e.exports=function _arrayWithHoles(e){if(Array.isArray(e))return e},e.exports.__esModule=!0,e.exports.default=e.exports},73203:e=>{e.exports=function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports},40608:e=>{e.exports=function _iterableToArrayLimit(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,a,i,u=[],c=!0,l=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(e){l=!0,o=e}finally{try{if(!c&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(l)throw o}}return u}},e.exports.__esModule=!0,e.exports.default=e.exports},56894:e=>{e.exports=function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports},40131:(e,t,r)=>{var n=r(17358),o=r(40608),a=r(35068),i=r(56894);e.exports=function _slicedToArray(e,t){return n(e)||o(e,t)||a(e,t)||i()},e.exports.__esModule=!0,e.exports.default=e.exports},7501:e=>{function _typeof(t){return e.exports=_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,_typeof(t)}e.exports=_typeof,e.exports.__esModule=!0,e.exports.default=e.exports},35068:(e,t,r)=>{var n=r(98106);e.exports=function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return n(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?n(e,t):void 0}},e.exports.__esModule=!0,e.exports.default=e.exports}},t={};function __webpack_require__(r){var n=t[r];if(void 0!==n)return n.exports;var o=t[r]={exports:{}};return e[r](o,o.exports,__webpack_require__),o.exports}(()=>{"use strict";var e=__webpack_require__(73203),t=e(__webpack_require__(87363)),r=e(__webpack_require__(66535)),n=e(__webpack_require__(23680)),o=elementorCommon.config.isDebug?t.default.StrictMode:t.default.Fragment,a=document.getElementById("e-admin-top-bar-root");r.default.render(t.default.createElement(o,null,t.default.createElement(n.default,null)),a)})()})();