/*! elementor - v3.23.0 - 05-08-2024 */
(()=>{var t={85422:(t,r,o)=>{"use strict";var i=o(38003).__,a=o(73203)(o(93231)),l=o(12406);t.exports=Marionette.Behavior.extend({tagView:null,listenerAttached:!1,initialize:function initialize(){this.listenerAttached||(this.listenTo(this.view.options.container.settings,"change:external:__dynamic__",this.onAfterExternalChange),this.listenerAttached=!0)},shouldRenderTools:function shouldRenderTools(){if(this.getOption("dynamicSettings").default)return!1;var t=elementor.helpers.hasPro()&&!elementor.helpers.hasProAndNotConnected(),r=this.getOption("tags").length>0;return!t||r},renderTools:function renderTools(){var t=this;if(this.shouldRenderTools()){var r=jQuery(Marionette.Renderer.render("#tmpl-elementor-control-dynamic-switcher"));r.on("click",(function(r){return t.onDynamicSwitcherClick(r)})),this.$el.find(".elementor-control-dynamic-switcher-wrapper").append(r),this.ui.dynamicSwitcher=r,"color"===this.view.model.get("type")&&(this.view.colorPicker?this.moveDynamicSwitcherToColorPicker():setTimeout((function(){return t.moveDynamicSwitcherToColorPicker()}))),this.ui.dynamicSwitcher.tipsy({title:function title(){return this.getAttribute("data-tooltip")},gravity:"s"})}},moveDynamicSwitcherToColorPicker:function moveDynamicSwitcherToColorPicker(){var t=this.view.colorPicker.$pickerToolsContainer;this.ui.dynamicSwitcher.removeClass("elementor-control-unit-1").addClass("e-control-tool");var r=t.find(".elementor-control-element-color-picker");r.length?this.ui.dynamicSwitcher.insertBefore(r):t.append(this.ui.dynamicSwitcher)},toggleDynamicClass:function toggleDynamicClass(){this.$el.toggleClass("elementor-control-dynamic-value",this.isDynamicMode())},isDynamicMode:function isDynamicMode(){var t=this.view.container.settings.get("__dynamic__");return!(!t||!t[this.view.model.get("name")])},createTagsList:function createTagsList(){var t=_.groupBy(this.getOption("tags"),"group"),r=elementor.dynamicTags.getConfig("groups"),o=this.ui.tagsList=jQuery("<div>",{class:"elementor-tags-list"}),i=jQuery("<div>",{class:"elementor-tags-list__inner"});if(o.append(i),jQuery.each(r,(function(r){var o=t[r];if(o){var a=jQuery("<div>",{class:"elementor-tags-list__group-title"}).text(this.title);i.append(a),o.forEach((function(t){var r=jQuery("<div>",{class:"elementor-tags-list__item"});r.text(t.title).attr("data-tag-name",t.name),i.append(r)}))}})),!elementor.helpers.hasPro()&&Object.keys(t).length){var a=Marionette.Renderer.render("#tmpl-elementor-dynamic-tags-promo",{promotionUrl:elementor.config.dynamicPromotionURL.replace("%s",this.view.model.get("name"))});i.append(a)}i.on("click",".elementor-tags-list__item",this.onTagsListItemClick.bind(this)),elementorCommon.elements.$body.append(o)},getTagsList:function getTagsList(){return this.ui.tagsList||this.createTagsList(),this.ui.tagsList},toggleTagsList:function toggleTagsList(){var t=this.getTagsList();if(t.is(":visible"))t.hide();else{var r=elementorCommon.config.isRTL?"left":"right";t.show().position({my:"".concat(r," top"),at:"".concat(r," bottom+5"),of:this.ui.dynamicSwitcher})}},setTagView:function setTagView(t,r,o){this.tagView&&this.tagView.destroy();var i=this.tagView=new l({id:t,name:r,settings:o,controlName:this.view.model.get("name"),dynamicSettings:this.getOption("dynamicSettings")}),a=this.view.options.container,u=a.controls[i.options.controlName].label;i.options.container=new elementorModules.editor.Container({type:"dynamic",id:t,model:i.model,settings:i.model,view:i,parent:a,label:a.label+" "+u,controls:i.model.options.controls,renderer:a}),i.render(),this.$el.find(".elementor-control-tag-area").after(i.el),this.listenTo(i,"remove",this.onTagViewRemove.bind(this))},setDefaultTagView:function setDefaultTagView(){var t=elementor.dynamicTags.tagTextToTagData(this.getDynamicValue());this.setTagView(t.id,t.name,t.settings)},tagViewToTagText:function tagViewToTagText(){var t=this.tagView;return elementor.dynamicTags.tagDataToTagText(t.getOption("id"),t.getOption("name"),t.model)},getDynamicValue:function getDynamicValue(){return this.view.container.dynamic.get(this.view.model.get("name"))},destroyTagView:function destroyTagView(){this.tagView&&(this.tagView.destroy(),this.tagView=null)},showPromotion:function showPromotion(){var t=elementor.helpers.hasProAndNotConnected(),r={title:i("Dynamic Content","elementor"),content:i("Create more personalized and dynamic sites by populating data from various sources with dozens of dynamic tags to choose from.","elementor"),targetElement:this.ui.dynamicSwitcher,position:{blockStart:"-10"},actionButton:{url:t?elementorProEditorConfig.urls.connect:elementor.config.dynamicPromotionURL.replace("%s",this.view.model.get("name")),text:i(t?"Connect & Activate":"Upgrade","elementor")}};elementor.promotion.showDialog(r)},onRender:function onRender(){this.$el.addClass("elementor-control-dynamic"),this.renderTools(),this.toggleDynamicClass(),this.isDynamicMode()&&this.setDefaultTagView()},onDynamicSwitcherClick:function onDynamicSwitcherClick(t){t.stopPropagation(),this.getOption("tags").length?this.toggleTagsList():this.showPromotion()},onTagsListItemClick:function onTagsListItemClick(t){var r=jQuery(t.currentTarget);this.setTagView(elementorCommon.helpers.getUniqueId(),r.data("tagName"),{}),this.view.getGlobalKey()&&this.view.triggerMethod("unset:global:value"),this.isDynamicMode()?$e.run("document/dynamic/settings",{container:this.view.options.container,settings:(0,a.default)({},this.view.model.get("name"),this.tagViewToTagText())}):$e.run("document/dynamic/enable",{container:this.view.options.container,settings:(0,a.default)({},this.view.model.get("name"),this.tagViewToTagText())}),this.toggleDynamicClass(),this.toggleTagsList(),this.tagView.getTagConfig().settings_required&&this.tagView.showSettingsPopup()},onTagViewRemove:function onTagViewRemove(){$e.run("document/dynamic/disable",{container:this.view.options.container,settings:(0,a.default)({},this.view.model.get("name"),this.tagViewToTagText())}),this.toggleDynamicClass()},onAfterExternalChange:function onAfterExternalChange(){this.destroyTagView(),this.isDynamicMode()&&this.setDefaultTagView(),this.toggleDynamicClass()},onDestroy:function onDestroy(){this.destroyTagView(),this.ui.tagsList&&this.ui.tagsList.remove()}})},8061:t=>{"use strict";t.exports=Marionette.ItemView.extend({className:"elementor-tag-controls-stack-empty",template:"#tmpl-elementor-tag-controls-stack-empty"})},52099:(t,r,o)=>{"use strict";var i=o(8061);t.exports=elementorModules.editor.views.ControlsStack.extend({activeTab:"content",template:_.noop,emptyView:i,isEmpty:function isEmpty(){return this.collection.length<2},childViewOptions:function childViewOptions(){return{container:this.options.container}},getNamespaceArray:function getNamespaceArray(){var t=elementor.getPanelView().getCurrentPageView(),r=t.getNamespaceArray();return r.push(t.activeSection),r.push(this.getOption("controlName")),r.push(this.getOption("name")),r},onRenderTemplate:function onRenderTemplate(){this.activateFirstSection()}})},12406:(t,r,o)=>{"use strict";var i=o(52099);t.exports=Marionette.ItemView.extend({className:"elementor-dynamic-cover e-input-style",tagControlsStack:null,templateHelpers:function templateHelpers(){var t={};return this.model&&(t.controls=this.model.options.controls),t},ui:{remove:".elementor-dynamic-cover__remove"},events:function events(){var events={"click @ui.remove":"onRemoveClick"};return this.hasSettings()&&(events.click="onClick"),events},getTemplate:function getTemplate(){var t=this.getTagConfig(),r=Marionette.TemplateCache.get("#tmpl-elementor-control-dynamic-cover"),o=Marionette.Renderer.render(r,{hasSettings:this.hasSettings(),isRemovable:!this.getOption("dynamicSettings").default,title:t.title,content:t.panel_template});return Marionette.TemplateCache.prototype.compileTemplate(o.trim())},getTagConfig:function getTagConfig(){return elementor.dynamicTags.getConfig("tags."+this.getOption("name"))},initSettingsPopup:function initSettingsPopup(){var t={className:"elementor-tag-settings-popup",position:{my:"left top+5",at:"left bottom",of:this.$el,autoRefresh:!0},hide:{ignore:".select2-container"}},r=elementorCommon.dialogsManager.createWidget("buttons",t);this.getSettingsPopup=function(){return r}},hasSettings:function hasSettings(){return!!Object.values(this.getTagConfig().controls).length},showSettingsPopup:function showSettingsPopup(){this.tagControlsStack||this.initTagControlsStack();var t=this.getSettingsPopup();t.isVisible()||t.show()},initTagControlsStack:function initTagControlsStack(){this.tagControlsStack=new i({model:this.model,controls:this.model.controls,name:this.options.name,controlName:this.options.controlName,container:this.options.container,el:this.getSettingsPopup().getElements("message")[0]}),this.tagControlsStack.render()},initModel:function initModel(){this.model=new elementorModules.editor.elements.models.BaseSettings(this.getOption("settings"),{controls:this.getTagConfig().controls})},initialize:function initialize(){this.initModel(),this.hasSettings()&&(this.initSettingsPopup(),this.listenTo(this.model,"change",this.render))},onClick:function onClick(){this.showSettingsPopup()},onRemoveClick:function onRemoveClick(t){t.stopPropagation(),this.destroy(),this.trigger("remove")},onDestroy:function onDestroy(){this.hasSettings()&&this.getSettingsPopup().destroy(),this.tagControlsStack&&this.tagControlsStack.destroy()}})},96731:t=>{"use strict";t.exports=elementorModules.Module.extend({errors:[],__construct:function __construct(t){var r=t.customValidationMethod;r&&(this.validationMethod=r)},getDefaultSettings:function getDefaultSettings(){return{validationTerms:{}}},isValid:function isValid(){var t=this.validationMethod.apply(this,arguments);return!t.length||(this.errors=t,!1)},validationMethod:function validationMethod(t){var r=[];return this.getSettings("validationTerms").required&&((""+t).length||r.push("Required value is empty")),r}})},33273:(t,r,o)=>{"use strict";var i=o(73203);Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var a=i(o(78983)),l=i(o(42081)),u=i(o(58724)),c=i(o(71173)),d=i(o(74910));function _createSuper(t){var r=function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function _createSuperInternal(){var o,i=(0,d.default)(t);if(r){var a=(0,d.default)(this).constructor;o=Reflect.construct(i,arguments,a)}else o=i.apply(this,arguments);return(0,c.default)(this,o)}}var p=o(12161),h=function(t){(0,u.default)(BreakpointValidator,t);var r=_createSuper(BreakpointValidator);function BreakpointValidator(){return(0,a.default)(this,BreakpointValidator),r.apply(this,arguments)}return(0,l.default)(BreakpointValidator,[{key:"getDefaultSettings",value:function getDefaultSettings(){return{validationTerms:{max:5120}}}},{key:"getPanelActiveBreakpoints",value:function getPanelActiveBreakpoints(){var t=elementor.documents.currentDocument.config.settings.settings.active_breakpoints.map((function(t){return t.replace("viewport_","")})),r={};return t.forEach((function(t){r[t]=elementorFrontend.config.responsive.breakpoints[t]})),r}},{key:"initBreakpointProperties",value:function initBreakpointProperties(){var t,r,o=this.getSettings("validationTerms"),i=this.getPanelActiveBreakpoints(),a=Object.keys(i);this.breakpointIndex=a.indexOf(o.breakpointName),this.topBreakpoint=null===(t=i[a[this.breakpointIndex+1]])||void 0===t?void 0:t.value,this.bottomBreakpoint=null===(r=i[a[this.breakpointIndex-1]])||void 0===r?void 0:r.value}},{key:"validationMethod",value:function validationMethod(t){var r=this.getSettings("validationTerms"),o=p.prototype.validationMethod.call(this,t);return(_.isFinite(t)||""===t)&&(this.validateMinMaxForBreakpoint(t,r)||o.push("Value is not between the breakpoints above or under the edited breakpoint")),o}},{key:"validateMinMaxForBreakpoint",value:function validateMinMaxForBreakpoint(t,r){var o=elementorFrontend.config.responsive.breakpoints[r.breakpointName].default_value,i=!0;return this.initBreakpointProperties(),"mobile"===r.breakpointName&&320===this.bottomBreakpoint&&(this.bottomBreakpoint-=1),this.bottomBreakpoint&&(""!==t&&t<=this.bottomBreakpoint&&(i=!1),""===t&&o<=this.bottomBreakpoint&&(i=!1)),this.topBreakpoint&&(""!==t&&t>=this.topBreakpoint&&(i=!1),""===t&&o>=this.topBreakpoint&&(i=!1)),i}}]),BreakpointValidator}(p);r.default=h},12161:(t,r,o)=>{"use strict";var i=o(96731);t.exports=i.extend({validationMethod:function validationMethod(t){var r=this.getSettings("validationTerms"),o=[];return _.isFinite(t)&&(void 0!==r.min&&t<r.min&&o.push("Value is less than minimum"),void 0!==r.max&&t>r.max&&o.push("Value is greater than maximum")),o}})},53005:(t,r,o)=>{"use strict";var i=o(73203),a=i(o(40131)),l=i(o(93231)),u=i(o(33273));function _createForOfIteratorHelper(t,r){var o="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!o){if(Array.isArray(t)||(o=function _unsupportedIterableToArray(t,r){if(!t)return;if("string"==typeof t)return _arrayLikeToArray(t,r);var o=Object.prototype.toString.call(t).slice(8,-1);"Object"===o&&t.constructor&&(o=t.constructor.name);if("Map"===o||"Set"===o)return Array.from(t);if("Arguments"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o))return _arrayLikeToArray(t,r)}(t))||r&&t&&"number"==typeof t.length){o&&(t=o);var i=0,a=function F(){};return{s:a,n:function n(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function e(t){throw t},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var l,u=!0,c=!1;return{s:function s(){o=o.call(t)},n:function n(){var t=o.next();return u=t.done,t},e:function e(t){c=!0,l=t},f:function f(){try{u||null==o.return||o.return()}finally{if(c)throw l}}}}function _arrayLikeToArray(t,r){(null==r||r>t.length)&&(r=t.length);for(var o=0,i=new Array(r);o<r;o++)i[o]=t[o];return i}var c,d=o(15584),p=o(85422),h=o(96731),g=o(12161);c=d.extend({validatorTypes:{Base:h,Number:g,Breakpoint:u.default},ui:function ui(){var ui=d.prototype.ui.apply(this,arguments);return _.extend(ui,{input:'input[data-setting][type!="checkbox"][type!="radio"]',checkbox:'input[data-setting][type="checkbox"]',radio:'input[data-setting][type="radio"]',select:"select[data-setting]",textarea:"textarea[data-setting]",responsiveSwitchersSibling:"".concat(ui.controlTitle,'[data-e-responsive-switcher-sibling!="false"]'),responsiveSwitchers:".elementor-responsive-switcher",contentEditable:'[contenteditable="true"]'}),ui},templateHelpers:function templateHelpers(){var t=d.prototype.templateHelpers.apply(this,arguments);return t.data.controlValue=this.getControlValue(),t},events:function events(){return{"input @ui.input":"onBaseInputTextChange","change @ui.checkbox":"onBaseInputChange","change @ui.radio":"onBaseInputChange","input @ui.textarea":"onBaseInputTextChange","change @ui.select":"onBaseInputChange","input @ui.contentEditable":"onBaseInputTextChange","click @ui.responsiveSwitchers":"onResponsiveSwitchersClick"}},behaviors:function behaviors(){var behaviors=d.prototype.behaviors.apply(this,arguments),t=this.options.model.get("dynamic");if(t&&t.active){var r=_.filter(elementor.dynamicTags.getConfig("tags"),(function(r){return r.editable&&_.intersection(r.categories,t.categories).length}));(r.length||elementor.config.user.is_administrator)&&(behaviors.tags={behaviorClass:p,tags:r,dynamicSettings:t})}return behaviors},initialize:function initialize(){d.prototype.initialize.apply(this,arguments),this.registerValidators(),this.model.get("responsive")&&this.setPlaceholderFromParent(),void 0===this.model.get("inherit_placeholders")&&this.model.set("inherit_placeholders",!0);var t=this.container?this.container.settings:this.elementSettingsModel;this.listenTo(t,"change:external:"+this.model.get("name"),this.onAfterExternalChange)},getControlValue:function getControlValue(){return this.container.settings.get(this.model.get("name"))},getGlobalKey:function getGlobalKey(){return this.container.globals.get(this.model.get("name"))},getGlobalValue:function getGlobalValue(){return this.globalValue},getGlobalDefault:function getGlobalDefault(){var t=this.model.get("global");if(null!=t&&t.default){if(!elementor.config.globals.defaults_enabled[this.getGlobalMeta().controlType])return"";var r=$e.data.commandExtractArgs(t.default),o=r.command,i=r.args,a=$e.data.getCache($e.components.get("globals"),o,i.query);return null==a?void 0:a.value}return""},getCurrentValue:function getCurrentValue(){if(this.getGlobalKey()&&!this.globalValue)return"";if(this.globalValue)return this.globalValue;var t=this.getControlValue();return t||this.getGlobalDefault()},isGlobalActive:function isGlobalActive(){var t;return null===(t=this.options.model.get("global"))||void 0===t?void 0:t.active},setValue:function setValue(t){this.setSettingsModel(t)},setSettingsModel:function setSettingsModel(t){var r=this.model.get("name");$e.run("document/elements/settings",{container:this.options.container,settings:(0,l.default)({},r,t)}),this.triggerMethod("settings:change")},applySavedValue:function applySavedValue(){this.setInputValue('[data-setting="'+this.model.get("name")+'"]',this.getControlValue())},getEditSettings:function getEditSettings(t){var r=this.getOption("elementEditSettings").toJSON();return t?r[t]:r},setEditSetting:function setEditSetting(t,r){(this.getOption("elementEditSettings")||this.getOption("container").settings).set(t,r)},getControlPlaceholder:function getControlPlaceholder(){var t=this.model.get("placeholder");return this.model.get("responsive")&&this.model.get("inherit_placeholders")&&(t=t||this.container.placeholders[this.model.get("name")]),t},getResponsiveParentView:function getResponsiveParentView(){var t=this.model.get("parent");try{return t&&this.container.panel.getControlView(t)}catch(t){}},getResponsiveChildrenViews:function getResponsiveChildrenViews(){var t=this.model.get("inheritors"),r=[];try{var o,i=_createForOfIteratorHelper(t);try{for(i.s();!(o=i.n()).done;){var a=o.value;r.push(this.container.panel.getControlView(a))}}catch(t){i.e(t)}finally{i.f()}}catch(t){}return r},setPlaceholderFromParent:function setPlaceholderFromParent(){var t=this.getResponsiveParentView();t&&(this.container.placeholders[this.model.get("name")]=t.preparePlaceholderForChildren())},preparePlaceholderForChildren:function preparePlaceholderForChildren(){var t,r=this.getCleanControlValue(),o=null===(t=this.getResponsiveParentView())||void 0===t?void 0:t.preparePlaceholderForChildren();return r instanceof Object?Object.assign({},o,r):r||o},propagatePlaceholder:function propagatePlaceholder(){var t,r=_createForOfIteratorHelper(this.getResponsiveChildrenViews());try{for(r.s();!(t=r.n()).done;){t.value.renderWithChildren()}}catch(t){r.e(t)}finally{r.f()}},renderWithChildren:function renderWithChildren(){this.render(),this.propagatePlaceholder()},getCleanControlValue:function getCleanControlValue(){var t=this.getControlValue();return t&&t!==this.model.get("default")?t:void 0},onAfterChange:function onAfterChange(t){Object.keys(t.changed).includes(this.model.get("name"))&&this.propagatePlaceholder(),d.prototype.onAfterChange.apply(this,arguments)},getInputValue:function getInputValue(t){var r=this.$(t);if(r.is('[contenteditable="true"]'))return r.html();var o=r.val(),i=r.attr("type");return-1!==["radio","checkbox"].indexOf(i)?r.prop("checked")?o:"":"number"===i&&_.isFinite(o)?+o:("SELECT"===t.tagName&&r.prop("multiple")&&null===o&&(o=[]),o)},setInputValue:function setInputValue(t,r){var o=this.$(t),i=o.attr("type");"checkbox"===i?o.prop("checked",!!r):"radio"===i?o.filter('[value="'+r+'"]').prop("checked",!0):o.val(r)},addValidator:function addValidator(t){this.validators.push(t)},registerValidators:function registerValidators(){var t=this;this.validators=[];var r={};this.model.get("required")&&(r.required=!0),jQuery.isEmptyObject(r)||this.addValidator(new this.validatorTypes.Base({validationTerms:r}));var o=this.model.get("validators");o&&Object.entries(o).forEach((function(r){var o=(0,a.default)(r,2),i=o[0],l=o[1];t.addValidator(new t.validatorTypes[i]({validationTerms:l}))}))},onBeforeRender:function onBeforeRender(){this.setPlaceholderFromParent()},onRender:function onRender(){d.prototype.onRender.apply(this,arguments),this.model.get("responsive")&&this.renderResponsiveSwitchers(),this.applySavedValue(),this.triggerMethod("ready"),this.toggleControlVisibility(),this.addTooltip()},onBaseInputTextChange:function onBaseInputTextChange(t){this.onBaseInputChange(t)},onBaseInputChange:function onBaseInputChange(t){clearTimeout(this.correctionTimeout);var r=t.currentTarget,o=this.getInputValue(r),i=this.validators.slice(0),a=this.container.settings.validators[this.model.get("name")];if(a&&(i=i.concat(a)),i){var l=this.getControlValue(r.dataset.setting);if(!i.every((function(t){return t.isValid(o,l)})))return void(this.correctionTimeout=setTimeout(this.setInputValue.bind(this,r,l),1200))}this.updateElementModel(o,r),this.triggerMethod("input:change",t)},onResponsiveSwitchersClick:function onResponsiveSwitchersClick(t){var r=jQuery(t.currentTarget),o=r.data("device"),i=this.ui.responsiveSwitchersWrapper,a=r.index();i.toggleClass("elementor-responsive-switchers-open"),i[0].style.setProperty("--selected-option",a),this.triggerMethod("responsive:switcher:click",o),elementor.changeDeviceMode(o)},renderResponsiveSwitchers:function renderResponsiveSwitchers(){var t=Marionette.Renderer.render("#tmpl-elementor-control-responsive-switchers",this.model.attributes);this.ui.responsiveSwitchersSibling.after(t),this.ui.responsiveSwitchersWrapper=this.$el.find(".elementor-control-responsive-switchers")},onAfterExternalChange:function onAfterExternalChange(){this.hideTooltip(),this.applySavedValue()},addTooltip:function addTooltip(){this.ui.tooltipTargets=this.$el.find(".tooltip-target"),this.ui.tooltipTargets.length&&this.ui.tooltipTargets.tipsy({gravity:function gravity(){var gravity=jQuery(this).data("tooltip-pos");return void 0!==gravity?gravity:"s"},title:function title(){return this.getAttribute("data-tooltip")}})},hideTooltip:function hideTooltip(){this.ui.tooltipTargets.length&&this.ui.tooltipTargets.tipsy("hide")},updateElementModel:function updateElementModel(t){this.setValue(t)}},{getStyleValue:function getStyleValue(t,r,o){return"DEFAULT"===t?o.default:r},onPasteStyle:function onPasteStyle(){return!0}}),t.exports=c},15584:(t,r,o)=>{"use strict";var i,a=o(73203)(o(93231));function ownKeys(t,r){var o=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);r&&(i=i.filter((function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable}))),o.push.apply(o,i)}return o}function _objectSpread(t){for(var r=1;r<arguments.length;r++){var o=null!=arguments[r]?arguments[r]:{};r%2?ownKeys(Object(o),!0).forEach((function(r){(0,a.default)(t,r,o[r])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(o)):ownKeys(Object(o)).forEach((function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(o,r))}))}return t}i=Marionette.CompositeView.extend({ui:function ui(){return{controlTitle:".elementor-control-title"}},behaviors:function behaviors(){return elementor.hooks.applyFilters("controls/base/behaviors",{},this)},getBehavior:function getBehavior(t){return this._behaviors[Object.keys(this.behaviors()).indexOf(t)]},className:function className(){var t="elementor-control elementor-control-"+this.model.get("name")+" elementor-control-type-"+this.model.get("type"),r=this.model.get("classes"),o=this.model.get("responsive");(_.isEmpty(r)||(t+=" "+r),_.isEmpty(o))||(t+=" elementor-control-responsive-"+(o.max||o.min));return t},templateHelpers:function templateHelpers(){var t={_cid:this.model.cid};return{view:this,data:_.extend({},this.model.toJSON(),t)}},getTemplate:function getTemplate(){return Marionette.TemplateCache.get("#tmpl-elementor-control-"+this.model.get("type")+"-content")},initialize:function initialize(t){var r=this.model.get("label");Object.defineProperty(this,"container",{get:function get(){if(!t.container){var o=t.elementSettingsModel,i=$e.components.get("document").utils.findViewById(o.id);i&&i.getContainer?t.container=i.getContainer():(o.id||(o.id="bc-"+elementorCommon.helpers.getUniqueId()),t.container=new elementorModules.editor.Container({type:"bc-container",id:o.id,model:o,settings:o,label:r,view:!1,parent:!1,renderer:!1,controls:o.options.controls}))}return t.container}}),Object.defineProperty(this,"elementSettingsModel",{get:function get(){return elementorDevTools.deprecation.deprecated("elementSettingsModel","2.8.0","container.settings"),t.container?t.container.settings:t.elementSettingsModel}});var o=this.model.get("type"),i=jQuery.extend(!0,{},elementor.config.controls[o],this.model.attributes);this.model.set(i);var a=this.container?this.container.settings:this.elementSettingsModel;this.listenTo(a,"change",this.onAfterChange),this.model.attributes.responsive&&(this.onDeviceModeChange=this.onDeviceModeChange.bind(this),elementor.listenTo(elementor.channels.deviceMode,"change",this.onDeviceModeChange))},onDestroy:function onDestroy(){elementor.stopListening(elementor.channels.deviceMode,"change",this.onDeviceModeChange)},onDeviceModeChange:function onDeviceModeChange(){this.toggleControlVisibility()},onAfterChange:function onAfterChange(){this.toggleControlVisibility()},toggleControlVisibility:function toggleControlVisibility(){var t=this.container?this.container.settings:this.elementSettingsModel,r=elementor.helpers.isActiveControl(this.model,t.attributes,t.controls);this.$el.toggleClass("elementor-hidden-control",!r),elementor.getPanelView().updateScrollbar()},onRender:function onRender(){var t=this.model.get("label_block")?"block":"inline",r=this.model.get("show_label"),o="elementor-label-"+t;o+=" elementor-control-separator-"+this.model.get("separator"),r||(o+=" elementor-control-hidden-label"),this.$el.addClass(o),this.toggleControlVisibility()},reRoute:function reRoute(t){$e.route($e.routes.getCurrent("panel"),this.getControlInRouteArgs(t?this.getControlPath():""),{history:!1})},getControlInRouteArgs:function getControlInRouteArgs(t){return _objectSpread(_objectSpread({},$e.routes.getCurrentArgs("panel")),{},{activeControl:t})},getControlPath:function getControlPath(){for(var t=this.model.get("name"),r=this._parent;!r.$el.hasClass("elementor-controls-stack");){t=(r.model.get("name")||r.model.get("_id"))+"/"+t,r=r._parent}return t}}),t.exports=i},4073:(t,r,o)=>{"use strict";var i=o(53005);t.exports=i.extend({setInputValue:function setInputValue(t,r){this.$(t).prop("checked",this.model.get("return_value")===r)}},{onPasteStyle:function onPasteStyle(t,r){return!r||r===t.return_value}})},23933:(t,r,o)=>{"use strict";var i=o(73203);Object.defineProperty(r,"__esModule",{value:!0}),r.default=r.Enable=void 0;var a=i(o(78983)),l=i(o(42081)),u=i(o(58724)),c=i(o(71173)),d=i(o(74910));function _createSuper(t){var r=function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function _createSuperInternal(){var o,i=(0,d.default)(t);if(r){var a=(0,d.default)(this).constructor;o=Reflect.construct(i,arguments,a)}else o=i.apply(this,arguments);return(0,c.default)(this,o)}}var p=function(t){(0,u.default)(Enable,t);var r=_createSuper(Enable);function Enable(){return(0,a.default)(this,Enable),r.apply(this,arguments)}return(0,l.default)(Enable,[{key:"apply",value:function apply(t){$e.components.get("preview/styleguide").enableStyleguidePreview(t)}}]),Enable}($e.modules.CommandBase);r.Enable=p;var h=p;r.default=h},59304:(t,r,o)=>{"use strict";var i=o(73203);Object.defineProperty(r,"__esModule",{value:!0}),r.default=r.GlobalColors=void 0;var a=i(o(78983)),l=i(o(42081)),u=i(o(58724)),c=i(o(71173)),d=i(o(74910));function _createSuper(t){var r=function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function _createSuperInternal(){var o,i=(0,d.default)(t);if(r){var a=(0,d.default)(this).constructor;o=Reflect.construct(i,arguments,a)}else o=i.apply(this,arguments);return(0,c.default)(this,o)}}var p=function(t){(0,u.default)(GlobalColors,t);var r=_createSuper(GlobalColors);function GlobalColors(){return(0,a.default)(this,GlobalColors),r.apply(this,arguments)}return(0,l.default)(GlobalColors,[{key:"apply",value:function apply(){$e.components.get("preview/styleguide").showStyleguidePreview()}}]),GlobalColors}($e.modules.CommandBase);r.GlobalColors=p;var h=p;r.default=h},55520:(t,r,o)=>{"use strict";var i=o(73203);Object.defineProperty(r,"__esModule",{value:!0}),r.default=r.GlobalTypography=void 0;var a=i(o(78983)),l=i(o(42081)),u=i(o(58724)),c=i(o(71173)),d=i(o(74910));function _createSuper(t){var r=function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function _createSuperInternal(){var o,i=(0,d.default)(t);if(r){var a=(0,d.default)(this).constructor;o=Reflect.construct(i,arguments,a)}else o=i.apply(this,arguments);return(0,c.default)(this,o)}}var p=function(t){(0,u.default)(GlobalTypography,t);var r=_createSuper(GlobalTypography);function GlobalTypography(){return(0,a.default)(this,GlobalTypography),r.apply(this,arguments)}return(0,l.default)(GlobalTypography,[{key:"apply",value:function apply(){$e.components.get("preview/styleguide").showStyleguidePreview()}}]),GlobalTypography}($e.modules.CommandBase);r.GlobalTypography=p;var h=p;r.default=h},96589:(t,r,o)=>{"use strict";var i=o(73203);Object.defineProperty(r,"__esModule",{value:!0}),r.default=r.Hide=void 0;var a=i(o(78983)),l=i(o(42081)),u=i(o(58724)),c=i(o(71173)),d=i(o(74910));function _createSuper(t){var r=function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function _createSuperInternal(){var o,i=(0,d.default)(t);if(r){var a=(0,d.default)(this).constructor;o=Reflect.construct(i,arguments,a)}else o=i.apply(this,arguments);return(0,c.default)(this,o)}}var p=function(t){(0,u.default)(Hide,t);var r=_createSuper(Hide);function Hide(){return(0,a.default)(this,Hide),r.apply(this,arguments)}return(0,l.default)(Hide,[{key:"apply",value:function apply(){$e.components.get("preview/styleguide").hideStyleguidePreview()}}]),Hide}($e.modules.CommandBase);r.Hide=p;var h=p;r.default=h},42052:(t,r,o)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"Enable",{enumerable:!0,get:function get(){return i.Enable}}),Object.defineProperty(r,"GlobalColors",{enumerable:!0,get:function get(){return a.GlobalColors}}),Object.defineProperty(r,"GlobalTypography",{enumerable:!0,get:function get(){return l.GlobalTypography}}),Object.defineProperty(r,"Hide",{enumerable:!0,get:function get(){return u.Hide}}),Object.defineProperty(r,"SwitcherChange",{enumerable:!0,get:function get(){return c.SwitcherChange}});var i=o(23933),a=o(59304),l=o(55520),u=o(96589),c=o(96654)},96654:(t,r,o)=>{"use strict";var i=o(73203);Object.defineProperty(r,"__esModule",{value:!0}),r.default=r.SwitcherChange=void 0;var a=i(o(78983)),l=i(o(42081)),u=i(o(58724)),c=i(o(71173)),d=i(o(74910));function _createSuper(t){var r=function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function _createSuperInternal(){var o,i=(0,d.default)(t);if(r){var a=(0,d.default)(this).constructor;o=Reflect.construct(i,arguments,a)}else o=i.apply(this,arguments);return(0,c.default)(this,o)}}var p=function(t){(0,u.default)(SwitcherChange,t);var r=_createSuper(SwitcherChange);function SwitcherChange(){return(0,a.default)(this,SwitcherChange),r.apply(this,arguments)}return(0,l.default)(SwitcherChange,[{key:"validateArgs",value:function validateArgs(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.requireArgumentType("name","string",t),this.requireArgumentType("value","string",t)}},{key:"apply",value:function apply(t){t.name.includes("enable_styleguide_preview")&&$e.components.get("preview/styleguide").enableStyleguidePreview({value:t.value})}}]),SwitcherChange}($e.modules.CommandBase);r.SwitcherChange=p;var h=p;r.default=h},40342:(t,r,o)=>{"use strict";var i=o(73203);Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var a=i(o(78983)),l=i(o(42081)),u=i(o(51121)),c=i(o(58724)),d=i(o(71173)),p=i(o(74910)),h=i(o(4073)),g=i(o(53005));function _createSuper(t){var r=function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function _createSuperInternal(){var o,i=(0,p.default)(t);if(r){var a=(0,p.default)(this).constructor;o=Reflect.construct(i,arguments,a)}else o=i.apply(this,arguments);return(0,d.default)(this,o)}}var m=function(t){(0,c.default)(_default,t);var r=_createSuper(_default);function _default(){return(0,a.default)(this,_default),r.apply(this,arguments)}return(0,l.default)(_default,[{key:"initialize",value:function initialize(){g.default.prototype.initialize.apply(this,arguments),this.$el.addClass("elementor-control-type-switcher")}},{key:"onBeforeRender",value:function onBeforeRender(){for(var t,r=arguments.length,o=new Array(r),i=0;i<r;i++)o[i]=arguments[i];(t=(0,u.default)((0,p.default)(_default.prototype),"onBeforeRender",this)).call.apply(t,[this].concat(o));var a=elementor.getPreferences("enable_styleguide_preview");a!==this.getCurrentValue()&&this.setValue(a)}},{key:"onBaseInputChange",value:function onBaseInputChange(t){g.default.prototype.onBaseInputChange.apply(this,arguments);var r=t.currentTarget,o=this.getInputValue(r);this.model.get("on_change_command")&&this.runCommand(o),this.model.set("return_value",null)}},{key:"runCommand",value:function runCommand(t){$e.run("preview/styleguide/switcher-change",{name:this.model.get("name"),value:t})}}]),_default}(h.default);r.default=m},78006:(t,r,o)=>{"use strict";var i=o(73203),a=o(7501);Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var l=i(o(78983)),u=i(o(42081)),c=i(o(58724)),d=i(o(71173)),p=i(o(74910)),h=function _interopRequireWildcard(t,r){if(!r&&t&&t.__esModule)return t;if(null===t||"object"!==a(t)&&"function"!=typeof t)return{default:t};var o=_getRequireWildcardCache(r);if(o&&o.has(t))return o.get(t);var i={},l=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var u in t)if("default"!==u&&Object.prototype.hasOwnProperty.call(t,u)){var c=l?Object.getOwnPropertyDescriptor(t,u):null;c&&(c.get||c.set)?Object.defineProperty(i,u,c):i[u]=t[u]}i.default=t,o&&o.set(t,i);return i}(o(42052)),g=i(o(40342));function _getRequireWildcardCache(t){if("function"!=typeof WeakMap)return null;var r=new WeakMap,o=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(t){return t?o:r})(t)}function _createSuper(t){var r=function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function _createSuperInternal(){var o,i=(0,p.default)(t);if(r){var a=(0,p.default)(this).constructor;o=Reflect.construct(i,arguments,a)}else o=i.apply(this,arguments);return(0,d.default)(this,o)}}var m=function(t){(0,c.default)(_default,t);var r=_createSuper(_default);function _default(t){var o;return(0,l.default)(this,_default),o=r.call(this,t),elementor.addControlView("global-style-switcher",g.default),o.registerStyleguideDialogType(),elementor.once("preview:loaded",(function(){o.initModal()})),o}return(0,u.default)(_default,[{key:"getNamespace",value:function getNamespace(){return"preview/styleguide"}},{key:"defaultCommands",value:function defaultCommands(){return this.importCommands(h)}},{key:"registerStyleguideDialogType",value:function registerStyleguideDialogType(){DialogsManager.addWidgetType("styleguide",DialogsManager.getWidgetType("lightbox").extend("alert",{buildWidget:function buildWidget(){DialogsManager.getWidgetType("lightbox").prototype.buildWidget.apply(this,arguments);var t=this.addElement("widgetContent"),r=this.getElements();t.append(r.message),r.widget.html(t)}}))}},{key:"initModal",value:function initModal(){var t;this.getModal=function(){return t||(t=elementorCommon.dialogsManager.createWidget("styleguide",{id:"e-styleguide-preview-dialog",message:'<div class="e-styleguide-preview-root"></div>',position:{my:"center center",at:"center center"},hide:{onOutsideClick:!1,onEscKeyPress:!1,onClick:!1,onBackgroundClick:!1},container:elementor.$previewContents.find("body")}))}}},{key:"showStyleguidePreview",value:function showStyleguidePreview(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.getModal().isVisible()||!t&&!elementor.getPreferences("enable_styleguide_preview")||(this.getPreviewFrame().postMessage({name:"elementor/styleguide/preview/show"},"*"),this.getModal().show())}},{key:"hideStyleguidePreview",value:function hideStyleguidePreview(){this.getPreviewFrame().postMessage({name:"elementor/styleguide/preview/hide"},"*"),this.getModal().hide()}},{key:"enableStyleguidePreview",value:function enableStyleguidePreview(t){t.value?this.showStyleguidePreview(!0):this.hideStyleguidePreview(),$e.run("document/elements/settings",{container:elementor.settings.editorPreferences.getEditedView().getContainer(),settings:{enable_styleguide_preview:t.value},options:{external:!0}})}},{key:"isInEditor",value:function isInEditor(){return!!window.elementor}},{key:"getPreviewFrame",value:function getPreviewFrame(){return this.isInEditor()?elementor.$preview[0].contentWindow:window}}]),_default}($e.modules.ComponentBase);r.default=m},38003:t=>{"use strict";t.exports=wp.i18n},98106:t=>{t.exports=function _arrayLikeToArray(t,r){(null==r||r>t.length)&&(r=t.length);for(var o=0,i=new Array(r);o<r;o++)i[o]=t[o];return i},t.exports.__esModule=!0,t.exports.default=t.exports},17358:t=>{t.exports=function _arrayWithHoles(t){if(Array.isArray(t))return t},t.exports.__esModule=!0,t.exports.default=t.exports},77266:t=>{t.exports=function _assertThisInitialized(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t},t.exports.__esModule=!0,t.exports.default=t.exports},78983:t=>{t.exports=function _classCallCheck(t,r){if(!(t instanceof r))throw new TypeError("Cannot call a class as a function")},t.exports.__esModule=!0,t.exports.default=t.exports},42081:(t,r,o)=>{var i=o(74040);function _defineProperties(t,r){for(var o=0;o<r.length;o++){var a=r[o];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(t,i(a.key),a)}}t.exports=function _createClass(t,r,o){return r&&_defineProperties(t.prototype,r),o&&_defineProperties(t,o),Object.defineProperty(t,"prototype",{writable:!1}),t},t.exports.__esModule=!0,t.exports.default=t.exports},93231:(t,r,o)=>{var i=o(74040);t.exports=function _defineProperty(t,r,o){return(r=i(r))in t?Object.defineProperty(t,r,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[r]=o,t},t.exports.__esModule=!0,t.exports.default=t.exports},51121:(t,r,o)=>{var i=o(79443);function _get(){return"undefined"!=typeof Reflect&&Reflect.get?(t.exports=_get=Reflect.get.bind(),t.exports.__esModule=!0,t.exports.default=t.exports):(t.exports=_get=function _get(t,r,o){var a=i(t,r);if(a){var l=Object.getOwnPropertyDescriptor(a,r);return l.get?l.get.call(arguments.length<3?t:o):l.value}},t.exports.__esModule=!0,t.exports.default=t.exports),_get.apply(this,arguments)}t.exports=_get,t.exports.__esModule=!0,t.exports.default=t.exports},74910:t=>{function _getPrototypeOf(r){return t.exports=_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function _getPrototypeOf(t){return t.__proto__||Object.getPrototypeOf(t)},t.exports.__esModule=!0,t.exports.default=t.exports,_getPrototypeOf(r)}t.exports=_getPrototypeOf,t.exports.__esModule=!0,t.exports.default=t.exports},58724:(t,r,o)=>{var i=o(96196);t.exports=function _inherits(t,r){if("function"!=typeof r&&null!==r)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(r&&r.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),r&&i(t,r)},t.exports.__esModule=!0,t.exports.default=t.exports},73203:t=>{t.exports=function _interopRequireDefault(t){return t&&t.__esModule?t:{default:t}},t.exports.__esModule=!0,t.exports.default=t.exports},40608:t=>{t.exports=function _iterableToArrayLimit(t,r){var o=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=o){var i,a,l,u,c=[],d=!0,p=!1;try{if(l=(o=o.call(t)).next,0===r){if(Object(o)!==o)return;d=!1}else for(;!(d=(i=l.call(o)).done)&&(c.push(i.value),c.length!==r);d=!0);}catch(t){p=!0,a=t}finally{try{if(!d&&null!=o.return&&(u=o.return(),Object(u)!==u))return}finally{if(p)throw a}}return c}},t.exports.__esModule=!0,t.exports.default=t.exports},56894:t=>{t.exports=function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},t.exports.__esModule=!0,t.exports.default=t.exports},71173:(t,r,o)=>{var i=o(7501).default,a=o(77266);t.exports=function _possibleConstructorReturn(t,r){if(r&&("object"===i(r)||"function"==typeof r))return r;if(void 0!==r)throw new TypeError("Derived constructors may only return object or undefined");return a(t)},t.exports.__esModule=!0,t.exports.default=t.exports},96196:t=>{function _setPrototypeOf(r,o){return t.exports=_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function _setPrototypeOf(t,r){return t.__proto__=r,t},t.exports.__esModule=!0,t.exports.default=t.exports,_setPrototypeOf(r,o)}t.exports=_setPrototypeOf,t.exports.__esModule=!0,t.exports.default=t.exports},40131:(t,r,o)=>{var i=o(17358),a=o(40608),l=o(35068),u=o(56894);t.exports=function _slicedToArray(t,r){return i(t)||a(t,r)||l(t,r)||u()},t.exports.__esModule=!0,t.exports.default=t.exports},79443:(t,r,o)=>{var i=o(74910);t.exports=function _superPropBase(t,r){for(;!Object.prototype.hasOwnProperty.call(t,r)&&null!==(t=i(t)););return t},t.exports.__esModule=!0,t.exports.default=t.exports},56027:(t,r,o)=>{var i=o(7501).default;t.exports=function toPrimitive(t,r){if("object"!=i(t)||!t)return t;var o=t[Symbol.toPrimitive];if(void 0!==o){var a=o.call(t,r||"default");if("object"!=i(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(t)},t.exports.__esModule=!0,t.exports.default=t.exports},74040:(t,r,o)=>{var i=o(7501).default,a=o(56027);t.exports=function toPropertyKey(t){var r=a(t,"string");return"symbol"==i(r)?r:String(r)},t.exports.__esModule=!0,t.exports.default=t.exports},7501:t=>{function _typeof(r){return t.exports=_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t.exports.__esModule=!0,t.exports.default=t.exports,_typeof(r)}t.exports=_typeof,t.exports.__esModule=!0,t.exports.default=t.exports},35068:(t,r,o)=>{var i=o(98106);t.exports=function _unsupportedIterableToArray(t,r){if(t){if("string"==typeof t)return i(t,r);var o=Object.prototype.toString.call(t).slice(8,-1);return"Object"===o&&t.constructor&&(o=t.constructor.name),"Map"===o||"Set"===o?Array.from(t):"Arguments"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o)?i(t,r):void 0}},t.exports.__esModule=!0,t.exports.default=t.exports}},r={};function __webpack_require__(o){var i=r[o];if(void 0!==i)return i.exports;var a=r[o]={exports:{}};return t[o](a,a.exports,__webpack_require__),a.exports}(()=>{"use strict";var t=__webpack_require__(73203),r=t(__webpack_require__(78983)),o=t(__webpack_require__(42081)),i=t(__webpack_require__(58724)),a=t(__webpack_require__(71173)),l=t(__webpack_require__(74910)),u=t(__webpack_require__(78006));function _createSuper(t){var r=function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function _createSuperInternal(){var o,i=(0,l.default)(t);if(r){var u=(0,l.default)(this).constructor;o=Reflect.construct(i,arguments,u)}else o=i.apply(this,arguments);return(0,a.default)(this,o)}}new(function(t){(0,i.default)(Styleguide,t);var a=_createSuper(Styleguide);function Styleguide(){return(0,r.default)(this,Styleguide),a.apply(this,arguments)}return(0,o.default)(Styleguide,[{key:"onInit",value:function onInit(){$e.components.register(new u.default),this.addHooks()}},{key:"getGlobalRoutes",value:function getGlobalRoutes(){return{"global-colors":"panel/global/global-colors","global-typography":"panel/global/global-typography"}}},{key:"addHooks",value:function addHooks(){elementor.hooks.addAction("panel/global/tab/before-show",this.show.bind(this)),elementor.hooks.addAction("panel/global/tab/before-destroy",this.hide.bind(this))}},{key:"show",value:function show(t){t.id&&t.id in this.getGlobalRoutes()&&$e.run("preview/styleguide/".concat(t.id))}},{key:"hide",value:function hide(t){t.id&&t.id in this.getGlobalRoutes()&&(Object.values(this.getGlobalRoutes()).some((function(t){return $e.routes.current.panel===t}))||$e.run("preview/styleguide/hide"))}}]),Styleguide}(elementorModules.editor.utils.Module))})()})();