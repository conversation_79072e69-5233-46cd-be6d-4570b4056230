/*! elementor - v3.23.0 - 05-08-2024 */
(()=>{var e={97267:(e,t,o)=>{"use strict";var r=o(38003).__,n=o(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var u=n(o(78983)),i=n(o(42081)),a=n(o(58724)),s=n(o(71173)),l=n(o(74910)),c=n(o(5721));function _createSuper(e){var t=function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function _createSuperInternal(){var o,r=(0,l.default)(e);if(t){var n=(0,l.default)(this).constructor;o=Reflect.construct(r,arguments,n)}else o=r.apply(this,arguments);return(0,s.default)(this,o)}}var f=function(e){(0,a.default)(BetaTesterLayout,e);var t=_createSuper(BetaTesterLayout);function BetaTesterLayout(){return(0,u.default)(this,BetaTesterLayout),t.apply(this,arguments)}return(0,i.default)(BetaTesterLayout,[{key:"ui",value:function ui(){return{closeModal:".elementor-templates-modal__header__close",dontShowAgain:".elementor-beta-tester-do-not-show-again"}}},{key:"events",value:function events(){return{"click @ui.closeModal":this.onCloseModalClick,"click @ui.dontShowAgain":this.onDontShowAgainClick}}},{key:"getModalOptions",value:function getModalOptions(){return{id:"elementor-beta-tester-modal",hide:{onBackgroundClick:!1}}}},{key:"getLogoOptions",value:function getLogoOptions(){return{title:r("Sign Up","elementor")}}},{key:"initialize",value:function initialize(){elementorModules.common.views.modal.Layout.prototype.initialize.apply(this,arguments),this.showLogo(),this.showContentView();var e=r("Don't Show Again","elementor");this.modalHeader.currentView.ui.closeModal.after(jQuery("<div>",{class:"elementor-beta-tester-do-not-show-again"}).text(e))}},{key:"showContentView",value:function showContentView(){this.modalContent.show(new c.default)}},{key:"onDontShowAgainClick",value:function onDontShowAgainClick(){this.hideModal(),this.onCloseModalClick()}},{key:"onCloseModalClick",value:function onCloseModalClick(){elementorCommon.ajax.addRequest("introduction_viewed",{data:{introductionKey:elementorAdmin.config.beta_tester.beta_tester_signup}})}}]),BetaTesterLayout}(elementorModules.common.views.modal.Layout);t.default=f},5721:(e,t,o)=>{"use strict";var r=o(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=r(o(78983)),u=r(o(42081)),i=r(o(58724)),a=r(o(71173)),s=r(o(74910));function _createSuper(e){var t=function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function _createSuperInternal(){var o,r=(0,s.default)(e);if(t){var n=(0,s.default)(this).constructor;o=Reflect.construct(r,arguments,n)}else o=r.apply(this,arguments);return(0,a.default)(this,o)}}var l=function(e){(0,i.default)(BetaTesterView,e);var t=_createSuper(BetaTesterView);function BetaTesterView(){var e;return(0,n.default)(this,BetaTesterView),(e=t.call(this)).id="elementor-beta-tester-dialog-content",e.template="#tmpl-elementor-beta-tester",e}return(0,u.default)(BetaTesterView,[{key:"ui",value:function ui(){return{betaForm:"#elementor-beta-tester-form",betaEmail:"#elementor-beta-tester-form__email",betaButton:"#elementor-beta-tester-form__submit"}}},{key:"events",value:function events(){return{"submit @ui.betaForm":"onBetaFormSubmit"}}},{key:"onBetaFormSubmit",value:function onBetaFormSubmit(e){e.preventDefault();var t=this.ui.betaEmail.val();this.ui.betaButton.addClass("elementor-button-state"),elementorCommon.ajax.addRequest("beta_tester_signup",{data:{betaTesterEmail:t}}),elementorBetaTester.layout.hideModal()}},{key:"onRender",value:function onRender(){}}]),BetaTesterView}(Marionette.ItemView);t.default=l},38003:e=>{"use strict";e.exports=wp.i18n},77266:e=>{e.exports=function _assertThisInitialized(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e},e.exports.__esModule=!0,e.exports.default=e.exports},78983:e=>{e.exports=function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},e.exports.__esModule=!0,e.exports.default=e.exports},42081:(e,t,o)=>{var r=o(74040);function _defineProperties(e,t){for(var o=0;o<t.length;o++){var n=t[o];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,r(n.key),n)}}e.exports=function _createClass(e,t,o){return t&&_defineProperties(e.prototype,t),o&&_defineProperties(e,o),Object.defineProperty(e,"prototype",{writable:!1}),e},e.exports.__esModule=!0,e.exports.default=e.exports},74910:e=>{function _getPrototypeOf(t){return e.exports=_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function _getPrototypeOf(e){return e.__proto__||Object.getPrototypeOf(e)},e.exports.__esModule=!0,e.exports.default=e.exports,_getPrototypeOf(t)}e.exports=_getPrototypeOf,e.exports.__esModule=!0,e.exports.default=e.exports},58724:(e,t,o)=>{var r=o(96196);e.exports=function _inherits(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&r(e,t)},e.exports.__esModule=!0,e.exports.default=e.exports},73203:e=>{e.exports=function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports},71173:(e,t,o)=>{var r=o(7501).default,n=o(77266);e.exports=function _possibleConstructorReturn(e,t){if(t&&("object"===r(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return n(e)},e.exports.__esModule=!0,e.exports.default=e.exports},96196:e=>{function _setPrototypeOf(t,o){return e.exports=_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function _setPrototypeOf(e,t){return e.__proto__=t,e},e.exports.__esModule=!0,e.exports.default=e.exports,_setPrototypeOf(t,o)}e.exports=_setPrototypeOf,e.exports.__esModule=!0,e.exports.default=e.exports},56027:(e,t,o)=>{var r=o(7501).default;e.exports=function toPrimitive(e,t){if("object"!=r(e)||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var n=o.call(e,t||"default");if("object"!=r(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)},e.exports.__esModule=!0,e.exports.default=e.exports},74040:(e,t,o)=>{var r=o(7501).default,n=o(56027);e.exports=function toPropertyKey(e){var t=n(e,"string");return"symbol"==r(t)?t:String(t)},e.exports.__esModule=!0,e.exports.default=e.exports},7501:e=>{function _typeof(t){return e.exports=_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,_typeof(t)}e.exports=_typeof,e.exports.__esModule=!0,e.exports.default=e.exports}},t={};function __webpack_require__(o){var r=t[o];if(void 0!==r)return r.exports;var n=t[o]={exports:{}};return e[o](n,n.exports,__webpack_require__),n.exports}(()=>{"use strict";var e=__webpack_require__(73203),t=e(__webpack_require__(78983)),o=e(__webpack_require__(42081)),r=e(__webpack_require__(58724)),n=e(__webpack_require__(71173)),u=e(__webpack_require__(74910)),i=e(__webpack_require__(97267));function _createSuper(e){var t=function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function _createSuperInternal(){var o,r=(0,u.default)(e);if(t){var i=(0,u.default)(this).constructor;o=Reflect.construct(r,arguments,i)}else o=r.apply(this,arguments);return(0,n.default)(this,o)}}var a=function(e){(0,r.default)(BetaTesterModule,e);var n=_createSuper(BetaTesterModule);function BetaTesterModule(){return(0,t.default)(this,BetaTesterModule),n.apply(this,arguments)}return(0,o.default)(BetaTesterModule,[{key:"onInit",value:function onInit(){elementorModules.ViewModule.prototype.onInit.apply(this,arguments),this.showLayout(!1)}},{key:"showLayout",value:function showLayout(){(!(arguments.length>0&&void 0!==arguments[0])||arguments[0]||elementorAdmin.config.beta_tester.option_enabled&&!elementorAdmin.config.beta_tester.signup_dismissed&&"#tab-fontawesome4_migration"!==location.hash)&&(this.layout=new i.default,this.layout.showModal())}},{key:"getDefaultSettings",value:function getDefaultSettings(){return{selectors:{betaTesterFirstToKnow:"#beta-tester-first-to-know"}}}},{key:"getDefaultElements",value:function getDefaultElements(){var e={},t=this.getSettings("selectors");return e.$betaTesterFirstToKnow=jQuery(t.betaTesterFirstToKnow),e}},{key:"bindEvents",value:function bindEvents(){this.elements.$betaTesterFirstToKnow.on("click",this.showLayout.bind(this))}}]),BetaTesterModule}(elementorModules.ViewModule);jQuery((function(){window.elementorBetaTester=new a}))})()})();