/*! elementor - v3.23.0 - 05-08-2024 */
(()=>{var t={8074:(t,r,o)=>{"use strict";var a=o(73203);Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var u=a(o(78983)),i=a(o(42081)),c=a(o(58724)),l=a(o(71173)),d=a(o(74910));function _createSuper(t){var r=function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function _createSuperInternal(){var o,a=(0,d.default)(t);if(r){var u=(0,d.default)(this).constructor;o=Reflect.construct(a,arguments,u)}else o=a.apply(this,arguments);return(0,l.default)(this,o)}}var p=function(t){(0,c.default)(CommandContainerBase,t);var r=_createSuper(CommandContainerBase);function CommandContainerBase(){return(0,u.default)(this,CommandContainerBase),r.apply(this,arguments)}return(0,i.default)(CommandContainerBase,[{key:"requireContainer",value:function requireContainer(){var t=this,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.args;if(!r.container&&!r.containers)throw Error("container or containers are required.");if(r.container&&r.containers)throw Error("container and containers cannot go together please select one of them.");(r.containers||[r.container]).forEach((function(r){t.requireArgumentInstance("container",elementorModules.editor.Container,{container:r})}))}}],[{key:"getInstanceType",value:function getInstanceType(){return"CommandContainerBase"}}]),CommandContainerBase}(a(o(83024)).default);r.default=p},746:(t,r,o)=>{"use strict";var a=o(73203);Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var u=a(o(78983)),i=a(o(42081)),c=a(o(58724)),l=a(o(71173)),d=a(o(74910));function _createSuper(t){var r=function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function _createSuperInternal(){var o,a=(0,d.default)(t);if(r){var u=(0,d.default)(this).constructor;o=Reflect.construct(a,arguments,u)}else o=a.apply(this,arguments);return(0,l.default)(this,o)}}var p=function(t){(0,c.default)(CommandContainerInternalBase,t);var r=_createSuper(CommandContainerInternalBase);function CommandContainerInternalBase(t){return(0,u.default)(this,CommandContainerInternalBase),r.call(this,t,$e.commandsInternal)}return(0,i.default)(CommandContainerInternalBase,null,[{key:"getInstanceType",value:function getInstanceType(){return"CommandContainerInternalBase"}}]),CommandContainerInternalBase}(a(o(8074)).default);r.default=p},65917:(t,r,o)=>{"use strict";var a=o(73203);Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var u=a(o(78983)),i=a(o(42081)),c=a(o(51121)),l=a(o(58724)),d=a(o(71173)),p=a(o(74910));function _createSuper(t){var r=function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function _createSuperInternal(){var o,a=(0,p.default)(t);if(r){var u=(0,p.default)(this).constructor;o=Reflect.construct(a,arguments,u)}else o=a.apply(this,arguments);return(0,d.default)(this,o)}}var y=function(t){(0,l.default)(CommandHistoryBase,t);var r=_createSuper(CommandHistoryBase);function CommandHistoryBase(){return(0,u.default)(this,CommandHistoryBase),r.apply(this,arguments)}return(0,i.default)(CommandHistoryBase,[{key:"initialize",value:function initialize(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=t.options,o=(void 0===r?{}:r).useHistory;(void 0===o||o)&&(this.history=this.getHistory(t),this.historyId=!1)}},{key:"getHistory",value:function getHistory(){elementorModules.ForceMethodImplementation()}},{key:"isHistoryActive",value:function isHistoryActive(){return elementor.documents.getCurrent().history.getActive()}},{key:"onBeforeRun",value:function onBeforeRun(t){(0,c.default)((0,p.default)(CommandHistoryBase.prototype),"onBeforeRun",this).call(this,t),this.history&&this.isHistoryActive()&&(this.historyId=$e.internal("document/history/start-log",this.history))}},{key:"onAfterRun",value:function onAfterRun(t,r){(0,c.default)((0,p.default)(CommandHistoryBase.prototype),"onAfterRun",this).call(this,t,r),this.history&&this.isHistoryActive()&&$e.internal("document/history/end-log",{id:this.historyId})}},{key:"onAfterApply",value:function onAfterApply(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=arguments.length>1?arguments[1]:void 0;(0,c.default)((0,p.default)(CommandHistoryBase.prototype),"onAfterApply",this).call(this,t,r),this.isDataChanged()&&$e.internal("document/save/set-is-modified",{status:!0})}},{key:"onCatchApply",value:function onCatchApply(t){t instanceof $e.modules.HookBreak&&this.historyId&&$e.internal("document/history/delete-log",{id:this.historyId}),(0,c.default)((0,p.default)(CommandHistoryBase.prototype),"onCatchApply",this).call(this,t)}},{key:"isDataChanged",value:function isDataChanged(){return!0}}],[{key:"getInstanceType",value:function getInstanceType(){return"CommandHistoryBase"}}]),CommandHistoryBase}(a(o(8074)).default);r.default=y},9009:(t,r,o)=>{"use strict";var a=o(73203);Object.defineProperty(r,"__esModule",{value:!0}),r.getDefaultDebounceDelay=r.default=r.DEFAULT_DEBOUNCE_DELAY=void 0;var u=a(o(78983)),i=a(o(42081)),c=a(o(51121)),l=a(o(58724)),d=a(o(71173)),p=a(o(74910)),y=a(o(93231)),m=a(o(65917));function _createSuper(t){var r=function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function _createSuperInternal(){var o,a=(0,p.default)(t);if(r){var u=(0,p.default)(this).constructor;o=Reflect.construct(a,arguments,u)}else o=a.apply(this,arguments);return(0,d.default)(this,o)}}r.DEFAULT_DEBOUNCE_DELAY=800;var v=function getDefaultDebounceDelay(){var t=800;return elementor.config.document&&void 0!==elementor.config.document.debounceDelay&&(t=elementor.config.document.debounceDelay),t};r.getDefaultDebounceDelay=v;var h=function(t){(0,l.default)(CommandHistoryDebounceBase,t);var r=_createSuper(CommandHistoryDebounceBase);function CommandHistoryDebounceBase(){return(0,u.default)(this,CommandHistoryDebounceBase),r.apply(this,arguments)}return(0,i.default)(CommandHistoryDebounceBase,[{key:"initialize",value:function initialize(t){var r=t.options,o=void 0===r?{}:r;(0,c.default)((0,p.default)(CommandHistoryDebounceBase.prototype),"initialize",this).call(this,t),this.constructor.debounce||(this.constructor.debounce=_.debounce((function(t){return t()}),v())),(1===$e.commands.currentTrace.length||o.debounce)&&(this.isDebounceRequired=!0)}},{key:"onBeforeRun",value:function onBeforeRun(t){$e.modules.CommandBase.prototype.onBeforeRun.call(this,t),this.history&&this.isHistoryActive()&&$e.internal("document/history/add-transaction",this.history)}},{key:"onAfterRun",value:function onAfterRun(t,r){$e.modules.CommandBase.prototype.onAfterRun.call(this,t,r),this.isHistoryActive()&&(this.isDebounceRequired?this.constructor.debounce((function(){return $e.internal("document/history/end-transaction")})):$e.internal("document/history/end-transaction"))}},{key:"onCatchApply",value:function onCatchApply(t){$e.modules.CommandBase.prototype.onCatchApply.call(this,t),t instanceof $e.modules.HookBreak&&this.history&&(this.isDebounceRequired?this.constructor.debounce((function(){return $e.internal("document/history/clear-transaction")})):$e.internal("document/history/clear-transaction"))}}],[{key:"getInstanceType",value:function getInstanceType(){return"CommandHistoryDebounceBase"}}]),CommandHistoryDebounceBase}(m.default);r.default=h,(0,y.default)(h,"debounce",void 0)},90381:(t,r)=>{"use strict";function _createForOfIteratorHelper(t,r){var o="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!o){if(Array.isArray(t)||(o=function _unsupportedIterableToArray(t,r){if(!t)return;if("string"==typeof t)return _arrayLikeToArray(t,r);var o=Object.prototype.toString.call(t).slice(8,-1);"Object"===o&&t.constructor&&(o=t.constructor.name);if("Map"===o||"Set"===o)return Array.from(t);if("Arguments"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o))return _arrayLikeToArray(t,r)}(t))||r&&t&&"number"==typeof t.length){o&&(t=o);var a=0,u=function F(){};return{s:u,n:function n(){return a>=t.length?{done:!0}:{done:!1,value:t[a++]}},e:function e(t){throw t},f:u}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,c=!0,l=!1;return{s:function s(){o=o.call(t)},n:function n(){var t=o.next();return c=t.done,t},e:function e(t){l=!0,i=t},f:function f(){try{c||null==o.return||o.return()}finally{if(l)throw i}}}}function _arrayLikeToArray(t,r){(null==r||r>t.length)&&(r=t.length);for(var o=0,a=new Array(r);o<r;o++)a[o]=t[o];return a}Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;r.default=function _default(t,r){var o,a=_createForOfIteratorHelper(r=Array.isArray(r)?r:[r]);try{for(a.s();!(o=a.n()).done;){var u=o.value;if(t.constructor.name===u.prototype[Symbol.toStringTag])return!0}}catch(t){a.e(t)}finally{a.f()}return!1}},42618:(t,r,o)=>{"use strict";var a=o(73203);Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var u=a(o(7501)),i=a(o(78983)),c=a(o(42081)),l=a(o(58724)),d=a(o(71173)),p=a(o(74910)),y=a(o(27597)),m=a(o(90381));function _createSuper(t){var r=function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function _createSuperInternal(){var o,a=(0,p.default)(t);if(r){var u=(0,p.default)(this).constructor;o=Reflect.construct(a,arguments,u)}else o=a.apply(this,arguments);return(0,d.default)(this,o)}}var v=function(t){(0,l.default)(ArgsObject,t);var r=_createSuper(ArgsObject);function ArgsObject(t){var o;return(0,i.default)(this,ArgsObject),(o=r.call(this)).args=t,o}return(0,c.default)(ArgsObject,[{key:"requireArgument",value:function requireArgument(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.args;if(!Object.prototype.hasOwnProperty.call(r,t))throw Error("".concat(t," is required."))}},{key:"requireArgumentType",value:function requireArgumentType(t,r){var o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.args;if(this.requireArgument(t,o),(0,u.default)(o[t])!==r)throw Error("".concat(t," invalid type: ").concat(r,"."))}},{key:"requireArgumentInstance",value:function requireArgumentInstance(t,r){var o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.args;if(this.requireArgument(t,o),!(o[t]instanceof r||(0,m.default)(o[t],r)))throw Error("".concat(t," invalid instance."))}},{key:"requireArgumentConstructor",value:function requireArgumentConstructor(t,r){var o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.args;if(this.requireArgument(t,o),o[t].constructor.toString()!==r.prototype.constructor.toString())throw Error("".concat(t," invalid constructor type."))}}],[{key:"getInstanceType",value:function getInstanceType(){return"ArgsObject"}}]),ArgsObject}(y.default);r.default=v},27597:(t,r,o)=>{"use strict";var a=o(73203);Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var u=a(o(78983)),i=a(o(42081)),c=a(o(51121)),l=a(o(74910)),d=function(t){function InstanceType(){var t=this;(0,u.default)(this,InstanceType);for(var r=this instanceof InstanceType?this.constructor:void 0,o=[];r.__proto__&&r.__proto__.name;)o.push(r.__proto__),r=r.__proto__;o.reverse().forEach((function(r){return t instanceof r}))}return(0,i.default)(InstanceType,null,[{key:t,value:function value(t){var r=(0,c.default)((0,l.default)(InstanceType),Symbol.hasInstance,this).call(this,t);if(t&&!t.constructor.getInstanceType)return r;if(t&&(t.instanceTypes||(t.instanceTypes=[]),r||this.getInstanceType()===t.constructor.getInstanceType()&&(r=!0),r)){var o=this.getInstanceType===InstanceType.getInstanceType?"BaseInstanceType":this.getInstanceType();-1===t.instanceTypes.indexOf(o)&&t.instanceTypes.push(o)}return!r&&t&&(r=t.instanceTypes&&Array.isArray(t.instanceTypes)&&-1!==t.instanceTypes.indexOf(this.getInstanceType())),r}},{key:"getInstanceType",value:function getInstanceType(){elementorModules.ForceMethodImplementation()}}]),InstanceType}(Symbol.hasInstance);r.default=d},83024:(t,r,o)=>{"use strict";var a=o(73203);Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var u=a(o(78983)),i=a(o(42081)),c=a(o(58724)),l=a(o(71173)),d=a(o(74910)),p=a(o(74774)),y=a(o(70170));function _createSuper(t){var r=function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function _createSuperInternal(){var o,a=(0,d.default)(t);if(r){var u=(0,d.default)(this).constructor;o=Reflect.construct(a,arguments,u)}else o=a.apply(this,arguments);return(0,l.default)(this,o)}}var m=function(t){(0,c.default)(CommandBase,t);var r=_createSuper(CommandBase);function CommandBase(){return(0,u.default)(this,CommandBase),r.apply(this,arguments)}return(0,i.default)(CommandBase,[{key:"onBeforeRun",value:function onBeforeRun(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};$e.hooks.runUIBefore(this.command,t)}},{key:"onAfterRun",value:function onAfterRun(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=arguments.length>1?arguments[1]:void 0;$e.hooks.runUIAfter(this.command,t,r)}},{key:"onBeforeApply",value:function onBeforeApply(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};$e.hooks.runDataDependency(this.command,t)}},{key:"onAfterApply",value:function onAfterApply(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=arguments.length>1?arguments[1]:void 0;$e.hooks.runDataAfter(this.command,t,r)}},{key:"onCatchApply",value:function onCatchApply(t){this.runCatchHooks(t)}},{key:"runCatchHooks",value:function runCatchHooks(t){$e.hooks.runDataCatch(this.command,this.args,t),$e.hooks.runUICatch(this.command,this.args,t)}},{key:"requireContainer",value:function requireContainer(){var t=this,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.args;if(y.default.deprecated("requireContainer()","3.7.0","Extend `$e.modules.editor.CommandContainerBase` or `$e.modules.editor.CommandContainerInternalBase`"),!r.container&&!r.containers)throw Error("container or containers are required.");if(r.container&&r.containers)throw Error("container and containers cannot go together please select one of them.");(r.containers||[r.container]).forEach((function(r){t.requireArgumentInstance("container",elementorModules.editor.Container,{container:r})}))}}],[{key:"getInstanceType",value:function getInstanceType(){return"CommandBase"}}]),CommandBase}(p.default);r.default=m},74774:(t,r,o)=>{"use strict";var a=o(73203);Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var u=a(o(78983)),i=a(o(42081)),c=a(o(58724)),l=a(o(71173)),d=a(o(74910)),p=a(o(93231)),y=a(o(42618)),m=a(o(70170));function _createSuper(t){var r=function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function _createSuperInternal(){var o,a=(0,d.default)(t);if(r){var u=(0,d.default)(this).constructor;o=Reflect.construct(a,arguments,u)}else o=a.apply(this,arguments);return(0,l.default)(this,o)}}var v=function(t){(0,c.default)(CommandInfra,t);var r=_createSuper(CommandInfra);function CommandInfra(){var t,o=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if((0,u.default)(this,CommandInfra),!(t=r.call(this,o)).constructor.registerConfig)throw RangeError("Doing it wrong: Each command type should have `registerConfig`.");return t.command=t.constructor.getCommand(),t.component=t.constructor.getComponent(),t.initialize(o),o=t.args,t.validateArgs(o),t}return(0,i.default)(CommandInfra,[{key:"currentCommand",get:function get(){return m.default.deprecated("this.currentCommand","3.7.0","this.command"),this.command}},{key:"initialize",value:function initialize(){}},{key:"validateArgs",value:function validateArgs(){}},{key:"apply",value:function apply(){elementorModules.ForceMethodImplementation()}},{key:"run",value:function run(){return this.apply(this.args)}},{key:"onBeforeRun",value:function onBeforeRun(){}},{key:"onAfterRun",value:function onAfterRun(){}},{key:"onBeforeApply",value:function onBeforeApply(){}},{key:"onAfterApply",value:function onAfterApply(){}},{key:"onCatchApply",value:function onCatchApply(t){}}],[{key:"getInstanceType",value:function getInstanceType(){return"CommandInfra"}},{key:"getInfo",value:function getInfo(){return{}}},{key:"getCommand",value:function getCommand(){return this.registerConfig.command}},{key:"getComponent",value:function getComponent(){return this.registerConfig.component}},{key:"setRegisterConfig",value:function setRegisterConfig(t){this.registerConfig=Object.freeze(t)}}]),CommandInfra}(y.default);r.default=v,(0,p.default)(v,"registerConfig",null)},17341:(t,r,o)=>{"use strict";var a=o(73203);Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var u=a(o(78983)),i=a(o(42081)),c=function(){function Console(){(0,u.default)(this,Console)}return(0,i.default)(Console,null,[{key:"error",value:function error(t){$e.devTools&&$e.devTools.log.error(t),t instanceof $e.modules.HookBreak||console.error(t)}},{key:"warn",value:function warn(){for(var t,r='font-size: 12px; background-image: url("'.concat(elementorWebCliConfig.urls.assets,'images/logo-icon.png"); background-repeat: no-repeat; background-size: contain;'),o=arguments.length,a=new Array(o),u=0;u<o;u++)a[u]=arguments[u];a.unshift("%c  %c",r,""),(t=console).warn.apply(t,a)}}]),Console}();r.default=c},70170:(t,r,o)=>{"use strict";var a=o(73203);Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var u=a(o(40131)),i=a(o(78983)),c=a(o(42081)),l=a(o(17341)),d=function deprecatedMessage(t,r,o,a){var u="`".concat(r,"` is ").concat(t," deprecated since ").concat(o);a&&(u+=" - Use `".concat(a,"` instead")),l.default.warn(u)},p=function(){function Deprecation(){(0,i.default)(this,Deprecation)}return(0,c.default)(Deprecation,null,[{key:"deprecated",value:function deprecated(t,r,o){this.isHardDeprecated(r)?function hardDeprecated(t,r,o){d("hard",t,r,o)}(t,r,o):function softDeprecated(t,r,o){elementorWebCliConfig.isDebug&&d("soft",t,r,o)}(t,r,o)}},{key:"parseVersion",value:function parseVersion(t){var r=t.split(".");if(r.length<3||r.length>4)throw new RangeError("Invalid Semantic Version string provided");var o=(0,u.default)(r,4),a=o[0],i=o[1],c=o[2],l=o[3],d=void 0===l?"":l;return{major1:parseInt(a),major2:parseInt(i),minor:parseInt(c),build:d}}},{key:"getTotalMajor",value:function getTotalMajor(t){var r=parseInt("".concat(t.major1).concat(t.major2,"0"));return r=Number((r/10).toFixed(0)),t.major2>9&&(r=t.major2-9),r}},{key:"compareVersion",value:function compareVersion(t,r){var o=this;return[this.parseVersion(t),this.parseVersion(r)].map((function(t){return o.getTotalMajor(t)})).reduce((function(t,r){return t-r}))}},{key:"isSoftDeprecated",value:function isSoftDeprecated(t){return this.compareVersion(t,elementorWebCliConfig.version)<=4}},{key:"isHardDeprecated",value:function isHardDeprecated(t){var r=this.compareVersion(t,elementorWebCliConfig.version);return r<0||r>=8}}]),Deprecation}();r.default=p},98106:t=>{t.exports=function _arrayLikeToArray(t,r){(null==r||r>t.length)&&(r=t.length);for(var o=0,a=new Array(r);o<r;o++)a[o]=t[o];return a},t.exports.__esModule=!0,t.exports.default=t.exports},17358:t=>{t.exports=function _arrayWithHoles(t){if(Array.isArray(t))return t},t.exports.__esModule=!0,t.exports.default=t.exports},77266:t=>{t.exports=function _assertThisInitialized(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t},t.exports.__esModule=!0,t.exports.default=t.exports},78983:t=>{t.exports=function _classCallCheck(t,r){if(!(t instanceof r))throw new TypeError("Cannot call a class as a function")},t.exports.__esModule=!0,t.exports.default=t.exports},42081:(t,r,o)=>{var a=o(74040);function _defineProperties(t,r){for(var o=0;o<r.length;o++){var u=r[o];u.enumerable=u.enumerable||!1,u.configurable=!0,"value"in u&&(u.writable=!0),Object.defineProperty(t,a(u.key),u)}}t.exports=function _createClass(t,r,o){return r&&_defineProperties(t.prototype,r),o&&_defineProperties(t,o),Object.defineProperty(t,"prototype",{writable:!1}),t},t.exports.__esModule=!0,t.exports.default=t.exports},93231:(t,r,o)=>{var a=o(74040);t.exports=function _defineProperty(t,r,o){return(r=a(r))in t?Object.defineProperty(t,r,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[r]=o,t},t.exports.__esModule=!0,t.exports.default=t.exports},51121:(t,r,o)=>{var a=o(79443);function _get(){return"undefined"!=typeof Reflect&&Reflect.get?(t.exports=_get=Reflect.get.bind(),t.exports.__esModule=!0,t.exports.default=t.exports):(t.exports=_get=function _get(t,r,o){var u=a(t,r);if(u){var i=Object.getOwnPropertyDescriptor(u,r);return i.get?i.get.call(arguments.length<3?t:o):i.value}},t.exports.__esModule=!0,t.exports.default=t.exports),_get.apply(this,arguments)}t.exports=_get,t.exports.__esModule=!0,t.exports.default=t.exports},74910:t=>{function _getPrototypeOf(r){return t.exports=_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function _getPrototypeOf(t){return t.__proto__||Object.getPrototypeOf(t)},t.exports.__esModule=!0,t.exports.default=t.exports,_getPrototypeOf(r)}t.exports=_getPrototypeOf,t.exports.__esModule=!0,t.exports.default=t.exports},58724:(t,r,o)=>{var a=o(96196);t.exports=function _inherits(t,r){if("function"!=typeof r&&null!==r)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(r&&r.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),r&&a(t,r)},t.exports.__esModule=!0,t.exports.default=t.exports},73203:t=>{t.exports=function _interopRequireDefault(t){return t&&t.__esModule?t:{default:t}},t.exports.__esModule=!0,t.exports.default=t.exports},40608:t=>{t.exports=function _iterableToArrayLimit(t,r){var o=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=o){var a,u,i,c,l=[],d=!0,p=!1;try{if(i=(o=o.call(t)).next,0===r){if(Object(o)!==o)return;d=!1}else for(;!(d=(a=i.call(o)).done)&&(l.push(a.value),l.length!==r);d=!0);}catch(t){p=!0,u=t}finally{try{if(!d&&null!=o.return&&(c=o.return(),Object(c)!==c))return}finally{if(p)throw u}}return l}},t.exports.__esModule=!0,t.exports.default=t.exports},56894:t=>{t.exports=function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},t.exports.__esModule=!0,t.exports.default=t.exports},71173:(t,r,o)=>{var a=o(7501).default,u=o(77266);t.exports=function _possibleConstructorReturn(t,r){if(r&&("object"===a(r)||"function"==typeof r))return r;if(void 0!==r)throw new TypeError("Derived constructors may only return object or undefined");return u(t)},t.exports.__esModule=!0,t.exports.default=t.exports},96196:t=>{function _setPrototypeOf(r,o){return t.exports=_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function _setPrototypeOf(t,r){return t.__proto__=r,t},t.exports.__esModule=!0,t.exports.default=t.exports,_setPrototypeOf(r,o)}t.exports=_setPrototypeOf,t.exports.__esModule=!0,t.exports.default=t.exports},40131:(t,r,o)=>{var a=o(17358),u=o(40608),i=o(35068),c=o(56894);t.exports=function _slicedToArray(t,r){return a(t)||u(t,r)||i(t,r)||c()},t.exports.__esModule=!0,t.exports.default=t.exports},79443:(t,r,o)=>{var a=o(74910);t.exports=function _superPropBase(t,r){for(;!Object.prototype.hasOwnProperty.call(t,r)&&null!==(t=a(t)););return t},t.exports.__esModule=!0,t.exports.default=t.exports},56027:(t,r,o)=>{var a=o(7501).default;t.exports=function toPrimitive(t,r){if("object"!=a(t)||!t)return t;var o=t[Symbol.toPrimitive];if(void 0!==o){var u=o.call(t,r||"default");if("object"!=a(u))return u;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(t)},t.exports.__esModule=!0,t.exports.default=t.exports},74040:(t,r,o)=>{var a=o(7501).default,u=o(56027);t.exports=function toPropertyKey(t){var r=u(t,"string");return"symbol"==a(r)?r:String(r)},t.exports.__esModule=!0,t.exports.default=t.exports},7501:t=>{function _typeof(r){return t.exports=_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t.exports.__esModule=!0,t.exports.default=t.exports,_typeof(r)}t.exports=_typeof,t.exports.__esModule=!0,t.exports.default=t.exports},35068:(t,r,o)=>{var a=o(98106);t.exports=function _unsupportedIterableToArray(t,r){if(t){if("string"==typeof t)return a(t,r);var o=Object.prototype.toString.call(t).slice(8,-1);return"Object"===o&&t.constructor&&(o=t.constructor.name),"Map"===o||"Set"===o?Array.from(t):"Arguments"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o)?a(t,r):void 0}},t.exports.__esModule=!0,t.exports.default=t.exports}},r={};function __webpack_require__(o){var a=r[o];if(void 0!==a)return a.exports;var u=r[o]={exports:{}};return t[o](u,u.exports,__webpack_require__),u.exports}(()=>{"use strict";var t=__webpack_require__(73203),r=t(__webpack_require__(8074)),o=t(__webpack_require__(746)),a=t(__webpack_require__(65917)),u=t(__webpack_require__(9009));$e.modules.editor={CommandContainerBase:r.default,CommandContainerInternalBase:o.default,document:{CommandHistoryBase:a.default,CommandHistoryDebounceBase:u.default}},$e.modules.document={get CommandHistory(){return elementorDevTools.deprecation.deprecated("$e.modules.document.CommandHistory","3.7.0","$e.modules.editor.document.CommandHistoryBase"),$e.modules.editor.document.CommandHistoryBase},get CommandHistoryDebounce(){return elementorDevTools.deprecation.deprecated("$e.modules.CommandHistoryDebounce","3.7.0","$e.modules.editor.document.CommandHistoryDebounceBase"),$e.modules.editor.document.CommandHistoryDebounceBase}}})()})();