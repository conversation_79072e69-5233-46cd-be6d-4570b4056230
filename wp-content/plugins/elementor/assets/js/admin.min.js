/*! elementor - v3.23.0 - 05-08-2024 */
/*! For license information please see admin.min.js.LICENSE.txt */
(()=>{var e={15511:e=>{"use strict";e.exports=elementorModules.ViewModule.extend({getDefaultSettings:function getDefaultSettings(){return{selectors:{modeSelect:".elementor_maintenance_mode_mode select",maintenanceModeTable:"#tab-maintenance_mode table",maintenanceModeDescriptions:".elementor-maintenance-mode-description",excludeModeSelect:".elementor_maintenance_mode_exclude_mode select",excludeRolesArea:".elementor_maintenance_mode_exclude_roles",templateSelect:".elementor_maintenance_mode_template_id select",editTemplateButton:".elementor-edit-template",maintenanceModeError:".elementor-maintenance-mode-error"},classes:{isEnabled:"elementor-maintenance-mode-is-enabled"}}},getDefaultElements:function getDefaultElements(){var e={},t=this.getSettings("selectors");return e.$modeSelect=jQuery(t.modeSelect),e.$maintenanceModeTable=e.$modeSelect.parents(t.maintenanceModeTable),e.$excludeModeSelect=e.$maintenanceModeTable.find(t.excludeModeSelect),e.$excludeRolesArea=e.$maintenanceModeTable.find(t.excludeRolesArea),e.$templateSelect=e.$maintenanceModeTable.find(t.templateSelect),e.$editTemplateButton=e.$maintenanceModeTable.find(t.editTemplateButton),e.$maintenanceModeDescriptions=e.$maintenanceModeTable.find(t.maintenanceModeDescriptions),e.$maintenanceModeError=e.$maintenanceModeTable.find(t.maintenanceModeError),e},handleModeSelectChange:function handleModeSelectChange(){var e=this.getSettings(),t=this.elements;t.$maintenanceModeTable.toggleClass(e.classes.isEnabled,!!t.$modeSelect.val()),t.$maintenanceModeDescriptions.hide(),t.$maintenanceModeDescriptions.filter('[data-value="'+t.$modeSelect.val()+'"]').show()},handleExcludeModeSelectChange:function handleExcludeModeSelectChange(){var e=this.elements;e.$excludeRolesArea.toggle("custom"===e.$excludeModeSelect.val())},handleTemplateSelectChange:function handleTemplateSelectChange(){var e=this.elements,t=e.$templateSelect.val();if(!t)return e.$editTemplateButton.hide(),void e.$maintenanceModeError.show();var n=elementorAdmin.config.home_url+"?p="+t+"&elementor";e.$editTemplateButton.prop("href",n).show(),e.$maintenanceModeError.hide()},bindEvents:function bindEvents(){var e=this.elements;e.$modeSelect.on("change",this.handleModeSelectChange.bind(this)),e.$excludeModeSelect.on("change",this.handleExcludeModeSelectChange.bind(this)),e.$templateSelect.on("change",this.handleTemplateSelectChange.bind(this))},onAdminInit:function onAdminInit(){this.handleModeSelectChange(),this.handleExcludeModeSelectChange(),this.handleTemplateSelectChange()},onInit:function onInit(){elementorModules.ViewModule.prototype.onInit.apply(this,arguments),elementorCommon.elements.$window.on("elementor/admin/init",this.onAdminInit)}})},75389:(e,t,n)=>{"use strict";var r=n(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=r(n(78983)),a=r(n(42081)),i=r(n(51121)),l=r(n(58724)),s=r(n(71173)),u=r(n(74910));function _createSuper(e){var t=function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function _createSuperInternal(){var n,r=(0,u.default)(e);if(t){var o=(0,u.default)(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return(0,s.default)(this,n)}}var c=function(e){(0,l.default)(MenuHandler,e);var t=_createSuper(MenuHandler);function MenuHandler(){return(0,o.default)(this,MenuHandler),t.apply(this,arguments)}return(0,a.default)(MenuHandler,[{key:"getDefaultSettings",value:function getDefaultSettings(){return{selectors:{currentSubmenuItems:"#adminmenu .current"}}}},{key:"getDefaultElements",value:function getDefaultElements(){var e=this.getSettings();return{$currentSubmenuItems:jQuery(e.selectors.currentSubmenuItems),$adminPageMenuLink:jQuery('a[href="'.concat(e.path,'"]'))}}},{key:"highlightSubMenuItem",value:function highlightSubMenuItem(){var e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:null)||this.elements.$adminPageMenuLink;this.elements.$currentSubmenuItems.length&&this.elements.$currentSubmenuItems.removeClass("current"),e.addClass("current"),e.parent().addClass("current")}},{key:"highlightTopLevelMenuItem",value:function highlightTopLevelMenuItem(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n="wp-has-current-submenu wp-menu-open current";e.parent().addClass(n).removeClass("wp-not-current-submenu"),t&&t.removeClass(n)}},{key:"onInit",value:function onInit(){(0,i.default)((0,u.default)(MenuHandler.prototype),"onInit",this).call(this);var e=this.getSettings();window.location.href.includes(e.path)&&this.highlightSubMenuItem()}}]),MenuHandler}(elementorModules.ViewModule);t.default=c},48729:(e,t,n)=>{"use strict";var r=n(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=r(n(40131)),a=r(n(78983)),i=r(n(42081)),l=function(){function TemplateControls(){(0,a.default)(this,TemplateControls)}return(0,i.default)(TemplateControls,[{key:"setDynamicControlsVisibility",value:function setDynamicControlsVisibility(e,t){if(void 0!==t)for(var n=0,r=Object.entries(t);n<r.length;n++){var a=(0,o.default)(r[n],2),i=a[0],l=a[1];this.setVisibilityForControl(e,l,i)}}},{key:"setVisibilityForControl",value:function setVisibilityForControl(e,t,n){var r,o=this;Object.entries(null!==(r=t.conditions)&&void 0!==r?r:{}).forEach((function(t){o.changeVisibilityBasedOnCondition(e,t,n)}))}},{key:"changeVisibilityBasedOnCondition",value:function changeVisibilityBasedOnCondition(e,t,n){var r=(0,o.default)(t,2),a=r[0],i=r[1],l=document.getElementById(e+n+"__wrapper"),s=document.getElementById(e+a);l.classList.toggle("elementor-hidden",!s||i!==s.value)}}]),TemplateControls}();t.default=l},40548:(e,t,n)=>{"use strict";var r=n(38003).__,o=n(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=o(n(78983)),i=o(n(42081)),l=function(){function FilesUploadHandler(){(0,a.default)(this,FilesUploadHandler)}return(0,i.default)(FilesUploadHandler,null,[{key:"isUploadEnabled",value:function isUploadEnabled(e){return!["svg","application/json"].includes(e)||elementorCommon.config.filesUpload.unfilteredFiles}},{key:"setUploadTypeCaller",value:function setUploadTypeCaller(e){e.uploader.uploader.param("uploadTypeCaller","elementor-wp-media-upload")}},{key:"getUnfilteredFilesNonAdminDialog",value:function getUnfilteredFilesNonAdminDialog(){return elementorCommon.dialogsManager.createWidget("alert",{id:"e-unfiltered-files-disabled-dialog",headerMessage:r("Sorry, you can't upload that file yet","elementor"),message:r("This is because JSON files may pose a security risk.","elementor")+"<br><br>"+r("To upload them anyway, ask the site administrator to enable unfiltered file uploads.","elementor"),strings:{confirm:r("Got it","elementor")}})}},{key:"getUnfilteredFilesNotEnabledDialog",value:function getUnfilteredFilesNotEnabledDialog(e){var t=window.elementorAdmin||window.elementor;if(!t.config.user.is_administrator)return this.getUnfilteredFilesNonAdminDialog();return t.helpers.getSimpleDialog("e-enable-unfiltered-files-dialog",r("Enable Unfiltered File Uploads","elementor"),r("Before you enable unfiltered files upload, note that such files include a security risk. Elementor does run a process to remove possible malicious code, but there is still risk involved when using such files.","elementor"),r("Enable","elementor"),(function onConfirm(){elementorCommon.ajax.addRequest("enable_unfiltered_files_upload",{},!0),elementorCommon.config.filesUpload.unfilteredFiles=!0,e()}))}},{key:"getUnfilteredFilesNotEnabledImportTemplateDialog",value:function getUnfilteredFilesNotEnabledImportTemplateDialog(e){return(window.elementorAdmin||window.elementor).config.user.is_administrator?elementorCommon.dialogsManager.createWidget("confirm",{id:"e-enable-unfiltered-files-dialog-import-template",headerMessage:r("Enable Unfiltered File Uploads","elementor"),message:r("Before you enable unfiltered files upload, note that such files include a security risk. Elementor does run a process to remove possible malicious code, but there is still risk involved when using such files.","elementor")+"<br /><br />"+r("If you do not enable uploading unfiltered files, any SVG or JSON (including lottie) files used in the uploaded template will not be imported.","elementor"),position:{my:"center center",at:"center center"},strings:{confirm:r("Enable and Import","elementor"),cancel:r("Import Without Enabling","elementor")},onConfirm:function onConfirm(){elementorCommon.ajax.addRequest("enable_unfiltered_files_upload",{success:function success(){elementorCommon.config.filesUpload.unfilteredFiles=!0,e()}},!0)},onCancel:function onCancel(){return e()}}):this.getUnfilteredFilesNonAdminDialog()}}]),FilesUploadHandler}();t.default=l},36404:(e,t,n)=>{"use strict";var r=n(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.Events=void 0;var o=r(n(78983)),a=r(n(42081)),i=function(){function Events(){(0,o.default)(this,Events)}return(0,a.default)(Events,null,[{key:"dispatch",value:function dispatch(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;e=e instanceof jQuery?e[0]:e,r&&e.dispatchEvent(new CustomEvent(r,{detail:n})),e.dispatchEvent(new CustomEvent(t,{detail:n}))}}]),Events}();t.Events=i;var l=i;t.default=l},67010:(e,t,n)=>{"use strict";var r=n(38003).__,o=n(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.showJsonUploadWarningMessageIfNeeded=function showJsonUploadWarningMessageIfNeeded(e){var t=e.introductionMap,n=e.IntroductionClass,o=e.waitForSetViewed,u=void 0!==o&&o;s||(s=function createGenericWarningModal(e){var t,n,o="e-generic-warning-modal-for-json-upload",a=new e({introductionKey:l,dialogType:"confirm",dialogOptions:{id:o,headerMessage:r("Warning: JSON files may be unsafe","elementor"),message:r("Uploading JSON files from unknown sources can be harmful and put your site at risk. For maximum safety, only install JSON files from trusted sources.","elementor"),effects:{show:"fadeIn",hide:"fadeOut"},hide:{onBackgroundClick:!0,onButtonClick:!1},strings:{confirm:r("Continue","elementor"),cancel:r("Cancel","elementor")}}}),i=function createCheckboxAndLabel(e){var t="".concat(e,"-dont-show-again"),n=document.createElement("input");n.type="checkbox",n.name=t,n.id=t;var o=document.createElement("label");return o.htmlFor=t,o.textContent=r("Do not show this message again","elementor"),o.style.display="block",o.style.marginTop="20px",o.style.marginBottom="20px",o.prepend(n),{checkbox:n,label:o}}(o),s=i.checkbox,u=i.label;return a.getDialog().addElement("checkbox-dont-show-again",s),null===(t=a.getDialog().getElements("message"))||void 0===t||null===(n=t.append)||void 0===n||n.call(t,u),a}(n));if(s.setIntroductionMap(t),s.introductionViewed)return Promise.resolve();var c=s.getDialog();return new Promise((function(e,t){c.onHide=function(){t()},c.onConfirm=(0,i.default)(a.default.mark((function _callee(){return a.default.wrap((function _callee$(t){for(;;)switch(t.prev=t.next){case 0:if(!c.getElements("checkbox-dont-show-again").prop("checked")){t.next=7;break}if(!u){t.next=6;break}return t.next=4,s.setViewed();case 4:t.next=7;break;case 6:s.setViewed();case 7:e(),c.hide();case 9:case"end":return t.stop()}}),_callee)}))),c.onCancel=function(){c.hide()},s.show()}))};var a=o(n(50824)),i=o(n(10029)),l="upload_json_warning_generic_message",s=null},73308:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=function matchUserAgent(e){return r.indexOf(e)>=0},r=navigator.userAgent,o=!!window.opr&&!!opr.addons||!!window.opera||n(" OPR/"),a=n("Firefox"),i=/^((?!chrome|android).)*safari/i.test(r)||/constructor/i.test(window.HTMLElement)||"[object SafariRemoteNotification]"===(!window.safari||"undefined"!=typeof safari&&safari.pushNotification).toString(),l=/Trident|MSIE/.test(r)&&!!document.documentMode,s=!l&&!!window.StyleMedia||n("Edg"),u=!!window.chrome&&n("Chrome")&&!(s||o),c=n("Chrome")&&!!window.CSS,d=n("AppleWebKit")&&!c,f={isTouchDevice:"ontouchstart"in window||navigator.maxTouchPoints>0||navigator.msMaxTouchPoints>0,appleWebkit:d,blink:c,chrome:u,edge:s,firefox:a,ie:l,mac:n("Macintosh"),opera:o,safari:i,webkit:n("AppleWebKit")};t.default=f},13863:(e,t,n)=>{"use strict";var r=n(38003).__,o=n(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=o(n(40131)),i=o(n(78983)),l=o(n(42081)),s=o(n(93231)),u="active",c="inactive",d="default",f=function(){function ExperimentsMessages(e){var t=e.selects,n=e.submit;(0,i.default)(this,ExperimentsMessages),(0,s.default)(this,"elements",{}),this.elements={selects:t,submit:n}}return(0,l.default)(ExperimentsMessages,[{key:"bindEvents",value:function bindEvents(){var e=this;this.elements.selects.forEach((function(t){t.addEventListener("change",(function(t){return e.onExperimentStateChange(t)}))}))}},{key:"onExperimentStateChange",value:function onExperimentStateChange(e){var t=e.currentTarget.dataset.experimentId;switch(this.getExperimentActualState(t)){case u:this.shouldShowDependenciesDialog(t)&&this.showDependenciesDialog(t);break;case c:this.shouldShowDeactivationDialog(t)?this.showDeactivationDialog(t):this.deactivateDependantExperiments(t)}}},{key:"getExperimentData",value:function getExperimentData(e){return elementorAdminConfig.experiments[e]}},{key:"getExperimentDependencies",value:function getExperimentDependencies(e){var t=this;return this.getExperimentData(e).dependencies.map((function(e){return t.getExperimentData(e)}))}},{key:"getExperimentSelect",value:function getExperimentSelect(e){return this.elements.selects.find((function(t){return t.matches('[data-experiment-id="'.concat(e,'"]'))}))}},{key:"setExperimentState",value:function setExperimentState(e,t){this.getExperimentSelect(e).value=t}},{key:"getExperimentActualState",value:function getExperimentActualState(e){var t,n=null===(t=this.getExperimentSelect(e))||void 0===t?void 0:t.value;return n?n!==d?n:this.isExperimentActiveByDefault(e)?u:c:this.getExperimentData(e).state}},{key:"isExperimentActive",value:function isExperimentActive(e){return this.getExperimentActualState(e)===u}},{key:"isExperimentActiveByDefault",value:function isExperimentActiveByDefault(e){return this.getExperimentData(e).default===u}},{key:"areAllDependenciesActive",value:function areAllDependenciesActive(e){var t=this;return e.every((function(e){return t.isExperimentActive(e.name)}))}},{key:"deactivateDependantExperiments",value:function deactivateDependantExperiments(e){var t=this;Object.entries(elementorAdminConfig.experiments).forEach((function(n){var r=(0,a.default)(n,2),o=r[0],i=r[1].dependencies.includes(e),l=t.getExperimentActualState(o)===u;i&&l&&t.setExperimentState(o,c)}))}},{key:"shouldShowDependenciesDialog",value:function shouldShowDependenciesDialog(e){var t=this.getExperimentDependencies(e);return!this.areAllDependenciesActive(t)}},{key:"shouldShowDeactivationDialog",value:function shouldShowDeactivationDialog(e){var t=this.getExperimentData(e),n=t.state===u||t.state===d&&t.default===u;return!!this.getMessage(e,"on_deactivate")&&n}},{key:"showDialog",value:function showDialog(e){return elementorCommon.dialogsManager.createWidget("confirm",{id:"e-experiments-messages-dialog",headerMessage:e.headerMessage,message:e.message,position:{my:"center center",at:"center center"},strings:{confirm:e.strings.confirm,cancel:e.strings.cancel},hide:{onOutsideClick:!1,onBackgroundClick:!1,onEscKeyPress:!1},onConfirm:e.onConfirm,onCancel:e.onCancel}).show()}},{key:"getSiteLanguageCode",value:function getSiteLanguageCode(){var e=document.querySelector("html").getAttribute("lang");return null!=e?e:"en"}},{key:"formatDependenciesList",value:function formatDependenciesList(e){var t=e.map((function(e){return e.title})),n=this.getSiteLanguageCode();return new Intl.ListFormat(n).format(t)}},{key:"showDependenciesDialog",value:function showDependenciesDialog(e){var t=this,n=this.getExperimentData(e).title,o=this.formatDependenciesList(this.getExperimentDependencies(e)),a=r("In order to use %1$s, first you need to activate %2$s.","elementor").replace("%1$s","<strong>".concat(n,"</strong>")).replace("%2$s",o);this.showDialog({message:a,headerMessage:r("First, activate another experiment.","elementor"),strings:{confirm:r("Activate","elementor"),cancel:r("Cancel","elementor")},onConfirm:function onConfirm(){t.getExperimentDependencies(e).forEach((function(e){t.setExperimentState(e.name,u)})),t.elements.submit.click()},onCancel:function onCancel(){t.setExperimentState(e,c)}})}},{key:"showDeactivationDialog",value:function showDeactivationDialog(e){var t=this;this.showDialog({message:this.getMessage(e,"on_deactivate"),headerMessage:r("Are you sure?","elementor"),strings:{confirm:r("Deactivate","elementor"),cancel:r("Cancel","elementor")},onConfirm:function onConfirm(){t.setExperimentState(e,c),t.deactivateDependantExperiments(e),t.elements.submit.click()},onCancel:function onCancel(){t.setExperimentState(e,u)}})}},{key:"getMessage",value:function getMessage(e,t){var n;return null===(n=this.getExperimentData(e))||void 0===n?void 0:n.messages[t]}}]),ExperimentsMessages}();t.default=f},72104:(e,t,n)=>{"use strict";var r=n(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=r(n(78983)),a=r(n(42081)),i=r(n(51121)),l=r(n(58724)),s=r(n(71173)),u=r(n(74910)),c=r(n(13863));function _createSuper(e){var t=function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function _createSuperInternal(){var n,r=(0,u.default)(e);if(t){var o=(0,u.default)(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return(0,s.default)(this,n)}}var d=function(e){(0,l.default)(ExperimentsModule,e);var t=_createSuper(ExperimentsModule);function ExperimentsModule(){return(0,o.default)(this,ExperimentsModule),t.apply(this,arguments)}return(0,a.default)(ExperimentsModule,[{key:"getDefaultSettings",value:function getDefaultSettings(){return{selectors:{experimentIndicators:".e-experiment__title__indicator",experimentForm:"#elementor-settings-form",experimentSelects:".e-experiment__select",experimentsButtons:".e-experiment__button"}}}},{key:"getDefaultElements",value:function getDefaultElements(){var e=this.getSettings().selectors;return{$experimentIndicators:jQuery(e.experimentIndicators),$experimentForm:jQuery(e.experimentForm),$experimentSelects:jQuery(e.experimentSelects),$experimentsButtons:jQuery(e.experimentsButtons)}}},{key:"bindEvents",value:function bindEvents(){var e=this;this.elements.$experimentsButtons.on("click",(function(t){return e.onExperimentsButtonsClick(t)}))}},{key:"onExperimentsButtonsClick",value:function onExperimentsButtonsClick(e){var t=jQuery(e.currentTarget);this.elements.$experimentSelects.val(t.val()),this.elements.$experimentForm.find("#submit").trigger("click")}},{key:"addTipsy",value:function addTipsy(e){e.tipsy({gravity:"s",offset:8,title:function title(){return this.getAttribute("data-tooltip")}})}},{key:"addIndicatorsTooltips",value:function addIndicatorsTooltips(){var e=this;this.elements.$experimentIndicators.each((function(t,n){return e.addTipsy(jQuery(n))}))}},{key:"onInit",value:function onInit(){var e=this;(0,i.default)((0,u.default)(ExperimentsModule.prototype),"onInit",this).call(this),this.experimentsDependency=new c.default({selects:this.elements.$experimentSelects.toArray(),submit:this.elements.$experimentForm.find("#submit").get(0)}),this.experimentsDependency.bindEvents(),this.elements.$experimentIndicators.length&&import("".concat(elementorCommon.config.urls.assets,"lib/tipsy/tipsy.min.js?ver=1.0.0")).then((function(){return e.addIndicatorsTooltips()}))}}]),ExperimentsModule}(elementorModules.ViewModule);t.default=d},99839:(e,t,n)=>{"use strict";var r=n(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=r(n(78983)),a=r(n(42081)),i=r(n(51121)),l=r(n(58724)),s=r(n(71173)),u=r(n(74910));function _createSuper(e){var t=function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function _createSuperInternal(){var n,r=(0,u.default)(e);if(t){var o=(0,u.default)(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return(0,s.default)(this,n)}}var c=function(e){(0,l.default)(FloatingButtonsHandler,e);var t=_createSuper(FloatingButtonsHandler);function FloatingButtonsHandler(){return(0,o.default)(this,FloatingButtonsHandler),t.apply(this,arguments)}return(0,a.default)(FloatingButtonsHandler,[{key:"getDefaultSettings",value:function getDefaultSettings(){var e="e-floating-buttons",t={contactPagesTablePage:'a[href="edit.php?post_type='+e+'"]',contactPagesAddNewPage:'a[href="edit.php?post_type=elementor_library&page='+e+'"]'};return{selectors:{addButton:".page-title-action:first",templatesMenuItem:".menu-icon-elementor_library",contactPagesMenuItem:"".concat(t.contactPagesTablePage,", ").concat(t.contactPagesAddNewPage)}}}},{key:"getDefaultElements",value:function getDefaultElements(){var e=this.getSettings("selectors"),t=(0,i.default)((0,u.default)(FloatingButtonsHandler.prototype),"getDefaultElements",this).call(this);return t.$templatesMenuItem=jQuery(e.templatesMenuItem),t.$contactPagesMenuItem=jQuery(e.contactPagesMenuItem),t}},{key:"onInit",value:function onInit(){var e;(0,i.default)((0,u.default)(FloatingButtonsHandler.prototype),"onInit",this).call(this);var t=this.getSettings(),n=!!window.location.href.includes(t.paths.contactPagesTablePage),r=!!window.location.href.includes(t.paths.contactPagesTrashPage),o=!!window.location.href.includes(t.paths.contactPagesAddNewPage);null!==(e=elementorAdminConfig.urls)&&void 0!==e&&e.viewContactPageUrl&&this.elements.$templatesMenuItem.find("li.submenu-e-contact a").attr("href",elementorAdminConfig.urls.viewContactPageUrl),(n||r||o)&&(this.highlightTopLevelMenuItem(this.elements.$templatesMenuItem,this.elements.$pagesMenuItemAndLink),this.highlightSubMenuItem(this.elements.$contactPagesMenuItem),jQuery(t.selectors.addButton).attr("href",elementorAdminConfig.urls.addNewLinkUrlContact))}}]),FloatingButtonsHandler}(r(n(75389)).default);t.default=c},8371:(e,t,n)=>{"use strict";var r=n(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=r(n(78983)),a=r(n(42081)),i=r(n(58724)),l=r(n(71173)),s=r(n(74910)),u=r(n(99839));function _createSuper(e){var t=function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function _createSuperInternal(){var n,r=(0,s.default)(e);if(t){var o=(0,s.default)(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return(0,l.default)(this,n)}}var c=function(e){(0,i.default)(_default,e);var t=_createSuper(_default);function _default(){var e;return(0,o.default)(this,_default),e=t.call(this),elementorCommon.elements.$window.on("elementor/admin/init",(function(){e.runHandler()})),e}return(0,a.default)(_default,[{key:"runHandler",value:function runHandler(){var e="e-floating-buttons",t={paths:{contactPagesTablePage:"edit.php?post_type="+e,contactPagesAddNewPage:"edit.php?post_type=elementor_library&page="+e,contactPagesTrashPage:"edit.php?post_status=trash&post_type="+e}};new u.default(t)}}]),_default}(elementorModules.Module);t.default=c},70602:(e,t,n)=>{"use strict";var r=n(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=r(n(78983)),a=r(n(42081)),i=r(n(51121)),l=r(n(58724)),s=r(n(71173)),u=r(n(74910));function _createSuper(e){var t=function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function _createSuperInternal(){var n,r=(0,u.default)(e);if(t){var o=(0,u.default)(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return(0,s.default)(this,n)}}var c=function(e){(0,l.default)(LandingPagesHandler,e);var t=_createSuper(LandingPagesHandler);function LandingPagesHandler(){return(0,o.default)(this,LandingPagesHandler),t.apply(this,arguments)}return(0,a.default)(LandingPagesHandler,[{key:"getDefaultSettings",value:function getDefaultSettings(){var e="e-landing-page",t={landingPagesTablePage:'a[href="edit.php?post_type='+e+'"]',landingPagesAddNewPage:'a[href="edit.php?post_type=elementor_library&page='+e+'"]'};return{selectors:{addButton:".page-title-action:first",pagesMenuItemAndLink:"#menu-pages, #menu-pages > a",landingPagesMenuItem:"".concat(t.landingPagesTablePage,", ").concat(t.landingPagesAddNewPage),templatesMenuItem:".menu-icon-elementor_library"}}}},{key:"getDefaultElements",value:function getDefaultElements(){var e=this.getSettings("selectors"),t=(0,i.default)((0,u.default)(LandingPagesHandler.prototype),"getDefaultElements",this).call(this);return t.$landingPagesMenuItem=jQuery(e.landingPagesMenuItem),t.$templatesMenuItem=jQuery(e.templatesMenuItem),t.$pagesMenuItemAndLink=jQuery(e.pagesMenuItemAndLink),t}},{key:"onInit",value:function onInit(){(0,i.default)((0,u.default)(LandingPagesHandler.prototype),"onInit",this).call(this);var e=this.getSettings(),t=!!window.location.href.includes(e.paths.landingPagesTablePage),n=!!window.location.href.includes(e.paths.landingPagesTrashPage),r=!!window.location.href.includes(e.paths.landingPagesAddNewPage);(t||n||r||e.isLandingPageAdminEdit)&&(this.highlightTopLevelMenuItem(this.elements.$templatesMenuItem,this.elements.$pagesMenuItemAndLink),this.highlightSubMenuItem(this.elements.$landingPagesMenuItem),jQuery(e.selectors.addButton).attr("href",elementorAdminConfig.urls.addNewLandingPageUrl))}}]),LandingPagesHandler}(r(n(75389)).default);t.default=c},71466:(e,t,n)=>{"use strict";var r=n(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=r(n(78983)),a=r(n(42081)),i=r(n(58724)),l=r(n(71173)),s=r(n(74910)),u=r(n(70602));function _createSuper(e){var t=function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function _createSuperInternal(){var n,r=(0,s.default)(e);if(t){var o=(0,s.default)(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return(0,l.default)(this,n)}}var c=function(e){(0,i.default)(_default,e);var t=_createSuper(_default);function _default(){var e;return(0,o.default)(this,_default),e=t.call(this),elementorCommon.elements.$window.on("elementor/admin/init",(function(){e.runHandler()})),e}return(0,a.default)(_default,[{key:"runHandler",value:function runHandler(){var e,t,n="e-landing-page",r={landingPagesTablePage:"edit.php?post_type="+n,landingPagesAddNewPage:"edit.php?post_type=elementor_library&page="+n,landingPagesTrashPage:"edit.php?post_status=trash&post_type="+n},o={path:null!==(e=elementorAdmin.config.landingPages)&&void 0!==e&&e.landingPagesHasPages?r.landingPagesTablePage:r.landingPagesAddNewPage,isLandingPageAdminEdit:null===(t=elementorAdmin.config.landingPages)||void 0===t?void 0:t.isLandingPageAdminEdit,paths:r};new u.default(o)}}]),_default}(elementorModules.Module);t.default=c},38003:e=>{"use strict";e.exports=wp.i18n},98106:e=>{e.exports=function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r},e.exports.__esModule=!0,e.exports.default=e.exports},17358:e=>{e.exports=function _arrayWithHoles(e){if(Array.isArray(e))return e},e.exports.__esModule=!0,e.exports.default=e.exports},77266:e=>{e.exports=function _assertThisInitialized(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e},e.exports.__esModule=!0,e.exports.default=e.exports},10029:e=>{function asyncGeneratorStep(e,t,n,r,o,a,i){try{var l=e[a](i),s=l.value}catch(e){return void n(e)}l.done?t(s):Promise.resolve(s).then(r,o)}e.exports=function _asyncToGenerator(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var a=e.apply(t,n);function _next(e){asyncGeneratorStep(a,r,o,_next,_throw,"next",e)}function _throw(e){asyncGeneratorStep(a,r,o,_next,_throw,"throw",e)}_next(void 0)}))}},e.exports.__esModule=!0,e.exports.default=e.exports},78983:e=>{e.exports=function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},e.exports.__esModule=!0,e.exports.default=e.exports},42081:(e,t,n)=>{var r=n(74040);function _defineProperties(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,r(o.key),o)}}e.exports=function _createClass(e,t,n){return t&&_defineProperties(e.prototype,t),n&&_defineProperties(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e},e.exports.__esModule=!0,e.exports.default=e.exports},93231:(e,t,n)=>{var r=n(74040);e.exports=function _defineProperty(e,t,n){return(t=r(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e},e.exports.__esModule=!0,e.exports.default=e.exports},51121:(e,t,n)=>{var r=n(79443);function _get(){return"undefined"!=typeof Reflect&&Reflect.get?(e.exports=_get=Reflect.get.bind(),e.exports.__esModule=!0,e.exports.default=e.exports):(e.exports=_get=function _get(e,t,n){var o=r(e,t);if(o){var a=Object.getOwnPropertyDescriptor(o,t);return a.get?a.get.call(arguments.length<3?e:n):a.value}},e.exports.__esModule=!0,e.exports.default=e.exports),_get.apply(this,arguments)}e.exports=_get,e.exports.__esModule=!0,e.exports.default=e.exports},74910:e=>{function _getPrototypeOf(t){return e.exports=_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function _getPrototypeOf(e){return e.__proto__||Object.getPrototypeOf(e)},e.exports.__esModule=!0,e.exports.default=e.exports,_getPrototypeOf(t)}e.exports=_getPrototypeOf,e.exports.__esModule=!0,e.exports.default=e.exports},58724:(e,t,n)=>{var r=n(96196);e.exports=function _inherits(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&r(e,t)},e.exports.__esModule=!0,e.exports.default=e.exports},73203:e=>{e.exports=function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports},40608:e=>{e.exports=function _iterableToArrayLimit(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,a,i,l=[],s=!0,u=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(r=a.call(n)).done)&&(l.push(r.value),l.length!==t);s=!0);}catch(e){u=!0,o=e}finally{try{if(!s&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(u)throw o}}return l}},e.exports.__esModule=!0,e.exports.default=e.exports},56894:e=>{e.exports=function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports},71173:(e,t,n)=>{var r=n(7501).default,o=n(77266);e.exports=function _possibleConstructorReturn(e,t){if(t&&("object"===r(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return o(e)},e.exports.__esModule=!0,e.exports.default=e.exports},21337:(e,t,n)=>{var r=n(7501).default;function _regeneratorRuntime(){"use strict";e.exports=_regeneratorRuntime=function _regeneratorRuntime(){return n},e.exports.__esModule=!0,e.exports.default=e.exports;var t,n={},o=Object.prototype,a=o.hasOwnProperty,i=Object.defineProperty||function(e,t,n){e[t]=n.value},l="function"==typeof Symbol?Symbol:{},s=l.iterator||"@@iterator",u=l.asyncIterator||"@@asyncIterator",c=l.toStringTag||"@@toStringTag";function define(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{define({},"")}catch(t){define=function define(e,t,n){return e[t]=n}}function wrap(e,t,n,r){var o=t&&t.prototype instanceof Generator?t:Generator,a=Object.create(o.prototype),l=new Context(r||[]);return i(a,"_invoke",{value:makeInvokeMethod(e,n,l)}),a}function tryCatch(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}n.wrap=wrap;var d="suspendedStart",f="suspendedYield",m="executing",p="completed",g={};function Generator(){}function GeneratorFunction(){}function GeneratorFunctionPrototype(){}var h={};define(h,s,(function(){return this}));var v=Object.getPrototypeOf,y=v&&v(v(values([])));y&&y!==o&&a.call(y,s)&&(h=y);var _=GeneratorFunctionPrototype.prototype=Generator.prototype=Object.create(h);function defineIteratorMethods(e){["next","throw","return"].forEach((function(t){define(e,t,(function(e){return this._invoke(t,e)}))}))}function AsyncIterator(e,t){function invoke(n,o,i,l){var s=tryCatch(e[n],e,o);if("throw"!==s.type){var u=s.arg,c=u.value;return c&&"object"==r(c)&&a.call(c,"__await")?t.resolve(c.__await).then((function(e){invoke("next",e,i,l)}),(function(e){invoke("throw",e,i,l)})):t.resolve(c).then((function(e){u.value=e,i(u)}),(function(e){return invoke("throw",e,i,l)}))}l(s.arg)}var n;i(this,"_invoke",{value:function value(e,r){function callInvokeWithMethodAndArg(){return new t((function(t,n){invoke(e,r,t,n)}))}return n=n?n.then(callInvokeWithMethodAndArg,callInvokeWithMethodAndArg):callInvokeWithMethodAndArg()}})}function makeInvokeMethod(e,n,r){var o=d;return function(a,i){if(o===m)throw new Error("Generator is already running");if(o===p){if("throw"===a)throw i;return{value:t,done:!0}}for(r.method=a,r.arg=i;;){var l=r.delegate;if(l){var s=maybeInvokeDelegate(l,r);if(s){if(s===g)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===d)throw o=p,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=m;var u=tryCatch(e,n,r);if("normal"===u.type){if(o=r.done?p:f,u.arg===g)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(o=p,r.method="throw",r.arg=u.arg)}}}function maybeInvokeDelegate(e,n){var r=n.method,o=e.iterator[r];if(o===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,maybeInvokeDelegate(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),g;var a=tryCatch(o,e.iterator,n.arg);if("throw"===a.type)return n.method="throw",n.arg=a.arg,n.delegate=null,g;var i=a.arg;return i?i.done?(n[e.resultName]=i.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,g):i:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,g)}function pushTryEntry(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function resetTryEntry(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function Context(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(pushTryEntry,this),this.reset(!0)}function values(e){if(e||""===e){var n=e[s];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function next(){for(;++o<e.length;)if(a.call(e,o))return next.value=e[o],next.done=!1,next;return next.value=t,next.done=!0,next};return i.next=i}}throw new TypeError(r(e)+" is not iterable")}return GeneratorFunction.prototype=GeneratorFunctionPrototype,i(_,"constructor",{value:GeneratorFunctionPrototype,configurable:!0}),i(GeneratorFunctionPrototype,"constructor",{value:GeneratorFunction,configurable:!0}),GeneratorFunction.displayName=define(GeneratorFunctionPrototype,c,"GeneratorFunction"),n.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===GeneratorFunction||"GeneratorFunction"===(t.displayName||t.name))},n.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,GeneratorFunctionPrototype):(e.__proto__=GeneratorFunctionPrototype,define(e,c,"GeneratorFunction")),e.prototype=Object.create(_),e},n.awrap=function(e){return{__await:e}},defineIteratorMethods(AsyncIterator.prototype),define(AsyncIterator.prototype,u,(function(){return this})),n.AsyncIterator=AsyncIterator,n.async=function(e,t,r,o,a){void 0===a&&(a=Promise);var i=new AsyncIterator(wrap(e,t,r,o),a);return n.isGeneratorFunction(t)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},defineIteratorMethods(_),define(_,c,"Generator"),define(_,s,(function(){return this})),define(_,"toString",(function(){return"[object Generator]"})),n.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function next(){for(;n.length;){var e=n.pop();if(e in t)return next.value=e,next.done=!1,next}return next.done=!0,next}},n.values=values,Context.prototype={constructor:Context,reset:function reset(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(resetTryEntry),!e)for(var n in this)"t"===n.charAt(0)&&a.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function stop(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function dispatchException(e){if(this.done)throw e;var n=this;function handle(r,o){return i.type="throw",i.arg=e,n.next=r,o&&(n.method="next",n.arg=t),!!o}for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r],i=o.completion;if("root"===o.tryLoc)return handle("end");if(o.tryLoc<=this.prev){var l=a.call(o,"catchLoc"),s=a.call(o,"finallyLoc");if(l&&s){if(this.prev<o.catchLoc)return handle(o.catchLoc,!0);if(this.prev<o.finallyLoc)return handle(o.finallyLoc)}else if(l){if(this.prev<o.catchLoc)return handle(o.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return handle(o.finallyLoc)}}}},abrupt:function abrupt(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&a.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var o=r;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=e,i.arg=t,o?(this.method="next",this.next=o.finallyLoc,g):this.complete(i)},complete:function complete(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function finish(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),resetTryEntry(n),g}},catch:function _catch(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;resetTryEntry(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function delegateYield(e,n,r){return this.delegate={iterator:values(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),g}},n}e.exports=_regeneratorRuntime,e.exports.__esModule=!0,e.exports.default=e.exports},96196:e=>{function _setPrototypeOf(t,n){return e.exports=_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function _setPrototypeOf(e,t){return e.__proto__=t,e},e.exports.__esModule=!0,e.exports.default=e.exports,_setPrototypeOf(t,n)}e.exports=_setPrototypeOf,e.exports.__esModule=!0,e.exports.default=e.exports},40131:(e,t,n)=>{var r=n(17358),o=n(40608),a=n(35068),i=n(56894);e.exports=function _slicedToArray(e,t){return r(e)||o(e,t)||a(e,t)||i()},e.exports.__esModule=!0,e.exports.default=e.exports},79443:(e,t,n)=>{var r=n(74910);e.exports=function _superPropBase(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=r(e)););return e},e.exports.__esModule=!0,e.exports.default=e.exports},56027:(e,t,n)=>{var r=n(7501).default;e.exports=function toPrimitive(e,t){if("object"!=r(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=r(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)},e.exports.__esModule=!0,e.exports.default=e.exports},74040:(e,t,n)=>{var r=n(7501).default,o=n(56027);e.exports=function toPropertyKey(e){var t=o(e,"string");return"symbol"==r(t)?t:String(t)},e.exports.__esModule=!0,e.exports.default=e.exports},7501:e=>{function _typeof(t){return e.exports=_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,_typeof(t)}e.exports=_typeof,e.exports.__esModule=!0,e.exports.default=e.exports},35068:(e,t,n)=>{var r=n(98106);e.exports=function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return r(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?r(e,t):void 0}},e.exports.__esModule=!0,e.exports.default=e.exports},50824:(e,t,n)=>{var r=n(21337)();e.exports=r;try{regeneratorRuntime=r}catch(e){"object"==typeof globalThis?globalThis.regeneratorRuntime=r:Function("r","regeneratorRuntime = r")(r)}}},t={};function __webpack_require__(n){var r=t[n];if(void 0!==r)return r.exports;var o=t[n]={exports:{}};return e[n](o,o.exports,__webpack_require__),o.exports}(()=>{"use strict";var e,t,n=__webpack_require__(38003).__,r=__webpack_require__(73203),o=r(__webpack_require__(50824)),a=r(__webpack_require__(10029)),i=r(__webpack_require__(40131)),l=r(__webpack_require__(71466)),s=r(__webpack_require__(72104)),u=r(__webpack_require__(73308)),c=r(__webpack_require__(36404)),d=r(__webpack_require__(40548)),f=r(__webpack_require__(48729)),m=__webpack_require__(67010),p=r(__webpack_require__(8371));e=jQuery,t=elementorModules.ViewModule.extend({maintenanceMode:null,config:elementorAdminConfig,getDefaultElements:function getDefaultElements(){var t={$switchMode:e("#elementor-switch-mode"),$goToEditLink:e("#elementor-go-to-edit-page-link"),$switchModeInput:e("#elementor-switch-mode-input"),$switchModeButton:e("#elementor-switch-mode-button"),$elementorLoader:e(".elementor-loader"),$builderEditor:e("#elementor-editor"),$importButton:e("#elementor-import-template-trigger"),$importNowButton:e("#e-import-template-action"),$importArea:e("#elementor-import-template-area"),$importForm:e("#elementor-import-template-form"),$importFormFileInput:e('#elementor-import-template-form input[type="file"]'),$settingsForm:e("#elementor-settings-form"),$settingsTabsWrapper:e("#elementor-settings-tabs-wrapper"),$menuGetHelpLink:e('a[href="admin.php?page=go_knowledge_base_site"]'),$menuGoProLink:e('a[href="admin.php?page=go_elementor_pro"]'),$reMigrateGlobalsButton:e(".elementor-re-migrate-globals-button")};return t.$settingsFormPages=t.$settingsForm.find(".elementor-settings-form-page"),t.$activeSettingsPage=t.$settingsFormPages.filter(".elementor-active"),t.$settingsTabs=t.$settingsTabsWrapper.children(),t.$activeSettingsTab=t.$settingsTabs.filter(".nav-tab-active"),t},toggleStatus:function toggleStatus(){var e=this.isElementorMode();elementorCommon.elements.$body.toggleClass("elementor-editor-active",e).toggleClass("elementor-editor-inactive",!e)},bindEvents:function bindEvents(){var t=this;t.elements.$switchModeButton.on("click",(function(r){if(r.preventDefault(),t.isElementorMode())elementorCommon.dialogsManager.createWidget("confirm",{message:n("Please note that you are switching to WordPress default editor. Your current layout, design and content might break.","elementor"),headerMessage:n("Back to WordPress Editor","elementor"),strings:{confirm:n("Continue","elementor"),cancel:n("Cancel","elementor")},defaultOption:"confirm",onConfirm:function onConfirm(){t.elements.$switchModeInput.val(""),t.toggleStatus()}}).show();else{t.elements.$switchModeInput.val(!0);var o=e("#title");o.val()||o.val("Elementor #"+e("#post_ID").val()),wp.autosave&&wp.autosave.server.triggerSave(),t.animateLoader(),e(document).on("heartbeat-tick.autosave",(function(){elementorCommon.elements.$window.off("beforeunload.edit-post"),location.href=t.elements.$goToEditLink.attr("href")})),t.toggleStatus()}})),t.elements.$goToEditLink.on("click",(function(){t.animateLoader()})),e(".e-notice--dismissible").on("click",".e-notice__dismiss, .e-notice-dismiss",(function(t){t.preventDefault();var n=e(this).closest(".e-notice--dismissible");e.post(ajaxurl,{action:"elementor_set_admin_notice_viewed",notice_id:n.data("notice_id")}),n.fadeTo(100,0,(function(){n.slideUp(100,(function(){n.remove()}))}))})),e('.e-notice--cta.e-notice--dismissible[data-notice_id="plugin_image_optimization"] a.e-button--cta').on("click",(function(){elementorCommon.ajax.addRequest("elementor_image_optimization_campaign",{data:{source:"io-wp-media-library-install"}})})),e('.e-a-apps .e-a-item[data-plugin="image-optimization/image-optimization.php"] a.e-btn').on("click",(function(){elementorCommon.ajax.addRequest("elementor_image_optimization_campaign",{data:{source:"io-esetting-addons-install"}})})),e("#elementor-clear-cache-button").on("click",(function(t){t.preventDefault();var n=e(this);n.removeClass("success").addClass("loading"),e.post(ajaxurl,{action:"elementor_clear_cache",_nonce:n.data("nonce")}).done((function(){n.removeClass("loading").addClass("success")}))})),e("#elementor-library-sync-button").on("click",(function(t){t.preventDefault();var n=e(this);n.removeClass("success").addClass("loading"),e.post(ajaxurl,{action:"elementor_reset_library",_nonce:n.data("nonce")}).done((function(){n.removeClass("loading").addClass("success")}))})),e("#elementor-recreate-kit-button").on("click",(function(t){t.preventDefault();var n=e(this);n.removeClass("success error").addClass("loading").next(".e-recreate-kit-error-message").remove(),e.post(ajaxurl,{action:"elementor_recreate_kit",_nonce:n.data("nonce")}).done((function(){n.removeClass("loading").addClass("success")})).fail((function(e){var t,r=e.responseJSON;n.removeClass("loading").addClass("error"),null!==(t=r.data)&&void 0!==t&&t.message&&n.after('<div class="e-recreate-kit-error-message">'.concat(r.data.message,"</div>"))}))})),e("#elementor-replace-url-button").on("click",(function(t){t.preventDefault();var n=e(this),r=n.parents("tr"),o=r.find('[name="from"]'),a=r.find('[name="to"]');n.removeClass("success").addClass("loading"),e.post(ajaxurl,{action:"elementor_replace_url",from:o.val(),to:a.val(),_nonce:n.data("nonce")}).done((function(e){n.removeClass("loading"),e.success&&n.addClass("success"),elementorCommon.dialogsManager.createWidget("alert",{message:e.data}).show()}))})),e("#elementor_upgrade_fa_button").on("click",(function(t){t.preventDefault();var r=e(this);r.addClass("loading"),elementorCommon.dialogsManager.createWidget("confirm",{id:"confirm_fa_migration_admin_modal",message:n("I understand that by upgrading to Font Awesome 5,","elementor")+"<br>"+n("I acknowledge that some changes may affect my website and that this action cannot be undone.","elementor"),headerMessage:n("Font Awesome 5 Migration","elementor"),strings:{confirm:n("Continue","elementor"),cancel:n("Cancel","elementor")},defaultOption:"confirm",onConfirm:function onConfirm(){r.removeClass("error").addClass("loading");var t=r.data(),n=t._nonce,o=t.action,a=t.redirectUrl;e.post(ajaxurl,{action:o,_nonce:n}).done((function(t){r.removeClass("loading").addClass("success");var n=document.createElement("p");n.appendChild(document.createTextNode(t.data.message)),e("#elementor_upgrade_fa_button").parent().append(n),a?location.href=decodeURIComponent(a):history.go(-1)})).fail((function(){r.removeClass("loading").addClass("error")}))},onCancel:function onCancel(){r.removeClass("loading").addClass("error")}}).show()})),t.elements.$settingsTabs.on({click:function click(e){e.preventDefault(),e.currentTarget.focus()},focus:function focus(){var e=location.href.replace(/#.*/,"");history.pushState({},"",e+this.hash),t.goToSettingsTabFromHash()}}),e("select.elementor-rollback-select").on("change",(function(){var t=e(this),n=t.next(".elementor-rollback-button"),r=n.data("placeholder-text"),o=n.data("placeholder-url");n.html(r.replace("{VERSION}",t.val())),n.attr("href",o.replace("VERSION",t.val()))})).trigger("change"),e(".elementor-rollback-button").on("click",(function(t){t.preventDefault();var r=e(this);elementorCommon.dialogsManager.createWidget("confirm",{headerMessage:n("Rollback to Previous Version","elementor"),message:n("Are you sure you want to reinstall previous version?","elementor"),strings:{confirm:n("Continue","elementor"),cancel:n("Cancel","elementor")},onConfirm:function onConfirm(){r.addClass("loading"),location.href=r.attr("href")}}).show()})),t.elements.$reMigrateGlobalsButton.on("click",(function(t){t.preventDefault();var r=e(t.currentTarget);elementorCommon.dialogsManager.createWidget("confirm",{headerMessage:n("Migrate to v3.0","elementor"),message:n("Please note that this process will revert all changes made to Global Colors and Fonts since upgrading to v3.x.","elementor"),strings:{confirm:n("Continue","elementor"),cancel:n("Cancel","elementor")},onConfirm:function onConfirm(){r.removeClass("success").addClass("loading"),elementorCommon.ajax.addRequest("re_migrate_globals",{success:function success(){return r.removeClass("loading").addClass("success")}})}}).show()})),e(".elementor_google_font select").on("change",(function(){e(".elementor_font_display").toggle("1"===e(this).val())})).trigger("change")},onInit:function onInit(){elementorModules.ViewModule.prototype.onInit.apply(this,arguments),this.initTemplatesImport(),this.initMaintenanceMode(),this.goToSettingsTabFromHash(),this.openLinksInNewTab(),this.addUserAgentClasses(),this.roleManager.init(),elementorCommon.config.experimentalFeatures["landing-pages"]&&new l.default,elementorCommon.config.experimentalFeatures["floating-buttons"]&&new p.default,this.templateControls=new f.default,new s.default},addUserAgentClasses:function addUserAgentClasses(){var e=document.querySelector("body");Object.entries(u.default).forEach((function(t){var n=(0,i.default)(t,2),r=n[0];n[1]&&e.classList.add("e--ua-"+r)}))},openLinksInNewTab:function openLinksInNewTab(){[this.elements.$menuGetHelpLink,this.elements.$menuGoProLink].forEach((function(e){e.length&&e.attr("target","_blank")}))},initTemplatesImport:function initTemplatesImport(){var t,r;if((elementorAdminConfig.user.is_administrator||null!==(t=null===(r=elementorAdminConfig.user.restrictions)||void 0===r?void 0:r.includes("json-upload"))&&void 0!==t&&t)&&elementorCommon.elements.$body.hasClass("post-type-elementor_library")){var i=this,l=i.elements.$importForm,s=i.elements.$importButton,u=i.elements.$importArea,c=i.elements.$importNowButton,f=i.elements.$importFormFileInput;i.elements.$formAnchor=e(".wp-header-end"),e("#wpbody-content").find(".page-title-action").last().after(s),i.elements.$formAnchor.after(u),s.on("click",(function(){e("#elementor-import-template-area").toggle()}));var p={jsonUploadWarning:{shown:!1},enableUnfilteredFiles:{shown:!1}},g=c[0].value;l.on("submit",function(){var e=(0,a.default)(o.default.mark((function _callee(e){var t,r;return o.default.wrap((function _callee$(o){for(;;)switch(o.prev=o.next){case 0:if(c[0].disabled=!0,c[0].value=n("Importing...","elementor"),p.jsonUploadWarning.shown){o.next=16;break}return e.preventDefault(),o.prev=4,o.next=7,(0,m.showJsonUploadWarningMessageIfNeeded)({IntroductionClass:window.elementorModules.admin.utils.Introduction,introductionMap:window.elementorAdmin.config.user.introduction,waitForSetViewed:!0});case 7:p.jsonUploadWarning.shown=!0,l.trigger("submit"),o.next=15;break;case 11:o.prev=11,o.t0=o.catch(4),c[0].disabled=!1,c[0].value=g;case 15:return o.abrupt("return");case 16:if(t=f[0].files.length,r=elementorCommon.config.filesUpload.unfilteredFiles,!t||r||p.enableUnfilteredFiles.shown){o.next=23;break}return e.preventDefault(),d.default.getUnfilteredFilesNotEnabledImportTemplateDialog((function(){p.enableUnfilteredFiles.shown=!0,l.trigger("submit")})).show(),o.abrupt("return");case 23:p.jsonUploadWarning.shown=!1,p.enableUnfilteredFiles.shown=!1;case 25:case"end":return o.stop()}}),_callee,null,[[4,11]])})));return function(t){return e.apply(this,arguments)}}())}},initMaintenanceMode:function initMaintenanceMode(){var e=__webpack_require__(15511);this.maintenanceMode=new e},isElementorMode:function isElementorMode(){return!!this.elements.$switchModeInput.val()},animateLoader:function animateLoader(){this.elements.$goToEditLink.addClass("elementor-animate")},goToSettingsTabFromHash:function goToSettingsTabFromHash(){var e=location.hash.slice(1);e&&this.goToSettingsTab(e)},goToSettingsTab:function goToSettingsTab(e){var t=this.elements.$settingsFormPages;if(t.length){var n=t.filter("#"+e);this.elements.$activeSettingsPage.removeClass("elementor-active"),this.elements.$activeSettingsTab.removeClass("nav-tab-active");var r=this.elements.$settingsTabs.filter("#elementor-settings-"+e);n.addClass("elementor-active"),r.addClass("nav-tab-active"),this.elements.$settingsForm.attr("action","options.php#"+e),this.elements.$activeSettingsPage=n,this.elements.$activeSettingsTab=r}},translate:function translate(e,t){return elementorCommon.translate(e,null,t,this.config.i18n)},roleManager:{selectors:{body:"elementor-role-manager",row:".elementor-role-row",label:".elementor-role-label",excludedIndicator:".elementor-role-excluded-indicator",excludedField:'input[name="elementor_exclude_user_roles[]"]',controlsContainer:".elementor-role-controls",toggleHandle:".elementor-role-toggle",arrowUp:"dashicons-arrow-up",arrowDown:"dashicons-arrow-down"},toggle:function toggle(e){var t=this,n=e.closest(t.selectors.row),r=n.find(t.selectors.toggleHandle).find(".dashicons"),o=n.find(t.selectors.controlsContainer);o.toggleClass("hidden"),o.hasClass("hidden")?r.removeClass(t.selectors.arrowUp).addClass(t.selectors.arrowDown):r.removeClass(t.selectors.arrowDown).addClass(t.selectors.arrowUp),t.updateLabel(n)},updateLabel:function updateLabel(e){var t=this,n=e.find(t.selectors.excludedIndicator),r=e.find(t.selectors.excludedField).is(":checked");r?n.html(n.data("excluded-label")):n.html(""),t.setAdvancedState(e,r)},setAdvancedState:function setAdvancedState(t,n){t.find('input[type="checkbox"]').not(this.selectors.excludedField).each((function(t,r){e(r).prop("disabled",n)}))},bind:function bind(){var t=this;e(document).on("click",t.selectors.label+","+t.selectors.toggleHandle,(function(n){n.stopPropagation(),n.preventDefault(),t.toggle(e(this))})).on("change",t.selectors.excludedField,(function(){t.updateLabel(e(this).closest(t.selectors.row))}))},init:function init(){var t=this;e('body[class*="'+t.selectors.body+'"]').length&&(t.bind(),e(t.selectors.row).each((function(n,r){t.updateLabel(e(r))})))}}}),e((function(){window.elementorAdmin=new t,c.default.dispatch(elementorCommon.elements.$window,"elementor/admin/init")}))})()})();