/*! elementor - v3.14.0 - 26-06-2023 */
"use strict";
(self["webpackChunkelementor"] = self["webpackChunkelementor"] || []).push([["wp-audio"],{

/***/ "../assets/dev/js/frontend/handlers/wp-audio.js":
/*!******************************************************!*\
  !*** ../assets/dev/js/frontend/handlers/wp-audio.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, exports) => {



Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = void 0;
class WpAudio extends elementorModules.frontend.handlers.Base {
  onInit() {
    super.onInit();
    window.wp.mediaelement.initialize();
  }
}
exports["default"] = WpAudio;

/***/ })

}]);
//# sourceMappingURL=wp-audio.4368a4a260548f3c083a.bundle.js.map