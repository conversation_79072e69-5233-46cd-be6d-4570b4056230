/*! elementor - v3.23.0 - 05-08-2024 */
/*! For license information please see text-path.2bc8a9cd0e50cf1a5a9c.bundle.min.js.LICENSE.txt */
(self.webpackChunkelementor=self.webpackChunkelementor||[]).push([[48],{6468:(e,t,n)=>{"use strict";var o=n(3203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=n(6028),r=o(n(1699));class TextPathHandler extends elementorModules.frontend.handlers.Base{getDefaultSettings(){return{selectors:{pathContainer:".e-text-path",svg:".e-text-path > svg"}}}getDefaultElements(){const{selectors:e}=this.getSettings(),t=this.$element[0];return{widgetWrapper:t,pathContainer:t.querySelector(e.pathContainer),svg:t.querySelector(e.svg),textPath:t.querySelector(e.textPath)}}onInit(){this.elements=this.getDefaultElements(),this.fetchSVG().then((()=>{this.pathId=`e-path-${this.elements.widgetWrapper.dataset.id}`,this.textPathId=`e-text-path-${this.elements.widgetWrapper.dataset.id}`,this.elements.svg&&this.initTextPath()}))}fetchSVG(){const{url:e}=this.elements.pathContainer.dataset;return e&&e.endsWith(".svg")?fetch(e).then((e=>e.text())).then((e=>{this.elements.pathContainer.innerHTML=r.default.sanitize(e),this.elements=this.getDefaultElements()})):Promise.reject(e)}setOffset(e){this.elements.textPath&&(this.isRTL()&&(e=100-parseInt(e)),this.elements.textPath.setAttribute("startOffset",e+"%"))}onElementChange(e){const{start_point:t,text:n}=this.getElementSettings();switch(e){case"start_point":this.setOffset(t.size);break;case"text":this.setText(n);break;case"text_path_direction":this.setOffset(t.size),this.setText(n)}}attachIdToPath(){(this.elements.svg.querySelector("[data-path-anchor]")||this.elements.svg.querySelector("path")).id=this.pathId}initTextPath(){const{start_point:e}=this.getElementSettings(),t=this.elements.pathContainer.dataset.text;this.attachIdToPath(),this.elements.svg.innerHTML+=`\n\t\t\t<text>\n\t\t\t\t<textPath id="${this.textPathId}" href="#${this.pathId}"></textPath>\n\t\t\t</text>\n\t\t`,this.elements.textPath=this.elements.svg.querySelector(`#${this.textPathId}`),this.setOffset(e.size),this.setText(t)}setText(e){const{is_external:t,nofollow:n}=this.getElementSettings().link,{linkUrl:o}=this.elements.pathContainer.dataset,i=t?"_blank":"",l=n?"nofollow":"";o&&(e=`<a href="${(0,a.escapeHTML)(o)}" rel="${l}" target="${i}">${(0,a.escapeHTML)(e)}</a>`,e=r.default.sanitize(e,{ADD_ATTR:["target"]})),this.elements.textPath.innerHTML=e;const s=this.elements.svg.querySelector(`#${this.textPathId}-clone`);if(s&&s.remove(),this.shouldReverseText()){const t=this.elements.textPath.cloneNode();t.id+="-clone",t.classList.add("elementor-hidden"),t.textContent=e,this.elements.textPath.parentNode.appendChild(t),this.reverseToRTL()}}isRTL(){const{text_path_direction:e}=this.getElementSettings();let t=elementorFrontend.config.is_rtl;return e&&(t="rtl"===e),t}shouldReverseText(){if(!this.isRTL())return!1;if(elementorFrontend.utils.environment.firefox)return!1;return!elementorFrontend.utils.environment.blink||!this.isFixedChromiumVersion()}isFixedChromiumVersion(){return parseInt(navigator.userAgent.match(/(?:Chrom(?:e|ium)|Edg)\/([0-9]+)\./)[1])>=96}reverseToRTL(){let e=this.elements.textPath;e=e.querySelector("a")||e;e.textContent=e.textContent.replace(/([\u0591-\u07FF\u200F\u202B\u202E\uFB1D-\uFDFD\uFE70-\uFEFC\s$&+,:;=?@#|'<>.^*()%!-]+)/gi,(e=>e.split("").reverse().join(""))),e.setAttribute("aria-hidden",!0)}}t.default=TextPathHandler},1699:function(e){e.exports=function(){"use strict";const{entries:e,setPrototypeOf:t,isFrozen:n,getPrototypeOf:o,getOwnPropertyDescriptor:a}=Object;let{freeze:r,seal:i,create:l}=Object,{apply:s,construct:c}="undefined"!=typeof Reflect&&Reflect;r||(r=function freeze(e){return e}),i||(i=function seal(e){return e}),s||(s=function apply(e,t,n){return e.apply(t,n)}),c||(c=function construct(e,t){return new e(...t)});const u=unapply(Array.prototype.forEach),d=unapply(Array.prototype.pop),m=unapply(Array.prototype.push),p=unapply(String.prototype.toLowerCase),f=unapply(String.prototype.toString),h=unapply(String.prototype.match),g=unapply(String.prototype.replace),T=unapply(String.prototype.indexOf),y=unapply(String.prototype.trim),S=unapply(Object.prototype.hasOwnProperty),E=unapply(RegExp.prototype.test),_=unconstruct(TypeError);function unapply(e){return function(t){for(var n=arguments.length,o=new Array(n>1?n-1:0),a=1;a<n;a++)o[a-1]=arguments[a];return s(e,t,o)}}function unconstruct(e){return function(){for(var t=arguments.length,n=new Array(t),o=0;o<t;o++)n[o]=arguments[o];return c(e,n)}}function addToSet(e,o){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:p;t&&t(e,null);let r=o.length;for(;r--;){let t=o[r];if("string"==typeof t){const e=a(t);e!==t&&(n(o)||(o[r]=e),t=e)}e[t]=!0}return e}function cleanArray(e){for(let t=0;t<e.length;t++)S(e,t)||(e[t]=null);return e}function clone(t){const n=l(null);for(const[o,a]of e(t))S(t,o)&&(Array.isArray(a)?n[o]=cleanArray(a):a&&"object"==typeof a&&a.constructor===Object?n[o]=clone(a):n[o]=a);return n}function lookupGetter(e,t){for(;null!==e;){const n=a(e,t);if(n){if(n.get)return unapply(n.get);if("function"==typeof n.value)return unapply(n.value)}e=o(e)}function fallbackValue(){return null}return fallbackValue}const A=r(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),b=r(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),x=r(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),N=r(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),R=r(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),v=r(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),C=r(["#text"]),D=r(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","xmlns","slot"]),w=r(["accent-height","accumulate","additive","alignment-baseline","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),k=r(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),L=r(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),O=i(/\{\{[\w\W]*|[\w\W]*\}\}/gm),I=i(/<%[\w\W]*|[\w\W]*%>/gm),M=i(/\${[\w\W]*}/gm),P=i(/^data-[\-\w.\u00B7-\uFFFF]/),U=i(/^aria-[\-\w]+$/),F=i(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),H=i(/^(?:\w+script|data):/i),z=i(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),B=i(/^html$/i),G=i(/^[a-z][a-z\d]*(-[a-z\d]+)+$/i);var W=Object.freeze({__proto__:null,MUSTACHE_EXPR:O,ERB_EXPR:I,TMPLIT_EXPR:M,DATA_ATTR:P,ARIA_ATTR:U,IS_ALLOWED_URI:F,IS_SCRIPT_OR_DATA:H,ATTR_WHITESPACE:z,DOCTYPE_NAME:B,CUSTOM_ELEMENT:G});const j=function getGlobal(){return"undefined"==typeof window?null:window},Y=function _createTrustedTypesPolicy(e,t){if("object"!=typeof e||"function"!=typeof e.createPolicy)return null;let n=null;const o="data-tt-policy-suffix";t&&t.hasAttribute(o)&&(n=t.getAttribute(o));const a="dompurify"+(n?"#"+n:"");try{return e.createPolicy(a,{createHTML:e=>e,createScriptURL:e=>e})}catch(e){return console.warn("TrustedTypes policy "+a+" could not be created."),null}};function createDOMPurify(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:j();const DOMPurify=e=>createDOMPurify(e);if(DOMPurify.version="3.0.10",DOMPurify.removed=[],!t||!t.document||9!==t.document.nodeType)return DOMPurify.isSupported=!1,DOMPurify;let{document:n}=t;const o=n,a=o.currentScript,{DocumentFragment:i,HTMLTemplateElement:s,Node:c,Element:O,NodeFilter:I,NamedNodeMap:M=t.NamedNodeMap||t.MozNamedAttrMap,HTMLFormElement:P,DOMParser:U,trustedTypes:H}=t,z=O.prototype,G=lookupGetter(z,"cloneNode"),$=lookupGetter(z,"nextSibling"),q=lookupGetter(z,"childNodes"),V=lookupGetter(z,"parentNode");if("function"==typeof s){const e=n.createElement("template");e.content&&e.content.ownerDocument&&(n=e.content.ownerDocument)}let X,K="";const{implementation:Z,createNodeIterator:J,createDocumentFragment:Q,getElementsByTagName:ee}=n,{importNode:te}=o;let ne={};DOMPurify.isSupported="function"==typeof e&&"function"==typeof V&&Z&&void 0!==Z.createHTMLDocument;const{MUSTACHE_EXPR:oe,ERB_EXPR:ae,TMPLIT_EXPR:re,DATA_ATTR:ie,ARIA_ATTR:le,IS_SCRIPT_OR_DATA:se,ATTR_WHITESPACE:ce,CUSTOM_ELEMENT:ue}=W;let{IS_ALLOWED_URI:de}=W,me=null;const pe=addToSet({},[...A,...b,...x,...R,...C]);let fe=null;const he=addToSet({},[...D,...w,...k,...L]);let ge=Object.seal(l(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),Te=null,ye=null,Se=!0,Ee=!0,_e=!1,Ae=!0,be=!1,xe=!1,Ne=!1,Re=!1,ve=!1,Ce=!1,De=!1,we=!0,ke=!1;const Le="user-content-";let Oe=!0,Ie=!1,Me={},Pe=null;const Ue=addToSet({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let Fe=null;const He=addToSet({},["audio","video","img","source","image","track"]);let ze=null;const Be=addToSet({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),Ge="http://www.w3.org/1998/Math/MathML",We="http://www.w3.org/2000/svg",je="http://www.w3.org/1999/xhtml";let Ye=je,$e=!1,qe=null;const Ve=addToSet({},[Ge,We,je],f);let Xe=null;const Ke=["application/xhtml+xml","text/html"],Ze="text/html";let Je=null,Qe=null;const et=n.createElement("form"),tt=function isRegexOrFunction(e){return e instanceof RegExp||e instanceof Function},nt=function _parseConfig(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!Qe||Qe!==e){if(e&&"object"==typeof e||(e={}),e=clone(e),Xe=-1===Ke.indexOf(e.PARSER_MEDIA_TYPE)?Ze:e.PARSER_MEDIA_TYPE,Je="application/xhtml+xml"===Xe?f:p,me=S(e,"ALLOWED_TAGS")?addToSet({},e.ALLOWED_TAGS,Je):pe,fe=S(e,"ALLOWED_ATTR")?addToSet({},e.ALLOWED_ATTR,Je):he,qe=S(e,"ALLOWED_NAMESPACES")?addToSet({},e.ALLOWED_NAMESPACES,f):Ve,ze=S(e,"ADD_URI_SAFE_ATTR")?addToSet(clone(Be),e.ADD_URI_SAFE_ATTR,Je):Be,Fe=S(e,"ADD_DATA_URI_TAGS")?addToSet(clone(He),e.ADD_DATA_URI_TAGS,Je):He,Pe=S(e,"FORBID_CONTENTS")?addToSet({},e.FORBID_CONTENTS,Je):Ue,Te=S(e,"FORBID_TAGS")?addToSet({},e.FORBID_TAGS,Je):{},ye=S(e,"FORBID_ATTR")?addToSet({},e.FORBID_ATTR,Je):{},Me=!!S(e,"USE_PROFILES")&&e.USE_PROFILES,Se=!1!==e.ALLOW_ARIA_ATTR,Ee=!1!==e.ALLOW_DATA_ATTR,_e=e.ALLOW_UNKNOWN_PROTOCOLS||!1,Ae=!1!==e.ALLOW_SELF_CLOSE_IN_ATTR,be=e.SAFE_FOR_TEMPLATES||!1,xe=e.WHOLE_DOCUMENT||!1,ve=e.RETURN_DOM||!1,Ce=e.RETURN_DOM_FRAGMENT||!1,De=e.RETURN_TRUSTED_TYPE||!1,Re=e.FORCE_BODY||!1,we=!1!==e.SANITIZE_DOM,ke=e.SANITIZE_NAMED_PROPS||!1,Oe=!1!==e.KEEP_CONTENT,Ie=e.IN_PLACE||!1,de=e.ALLOWED_URI_REGEXP||F,Ye=e.NAMESPACE||je,ge=e.CUSTOM_ELEMENT_HANDLING||{},e.CUSTOM_ELEMENT_HANDLING&&tt(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(ge.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&tt(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(ge.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(ge.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),be&&(Ee=!1),Ce&&(ve=!0),Me&&(me=addToSet({},C),fe=[],!0===Me.html&&(addToSet(me,A),addToSet(fe,D)),!0===Me.svg&&(addToSet(me,b),addToSet(fe,w),addToSet(fe,L)),!0===Me.svgFilters&&(addToSet(me,x),addToSet(fe,w),addToSet(fe,L)),!0===Me.mathMl&&(addToSet(me,R),addToSet(fe,k),addToSet(fe,L))),e.ADD_TAGS&&(me===pe&&(me=clone(me)),addToSet(me,e.ADD_TAGS,Je)),e.ADD_ATTR&&(fe===he&&(fe=clone(fe)),addToSet(fe,e.ADD_ATTR,Je)),e.ADD_URI_SAFE_ATTR&&addToSet(ze,e.ADD_URI_SAFE_ATTR,Je),e.FORBID_CONTENTS&&(Pe===Ue&&(Pe=clone(Pe)),addToSet(Pe,e.FORBID_CONTENTS,Je)),Oe&&(me["#text"]=!0),xe&&addToSet(me,["html","head","body"]),me.table&&(addToSet(me,["tbody"]),delete Te.tbody),e.TRUSTED_TYPES_POLICY){if("function"!=typeof e.TRUSTED_TYPES_POLICY.createHTML)throw _('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if("function"!=typeof e.TRUSTED_TYPES_POLICY.createScriptURL)throw _('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');X=e.TRUSTED_TYPES_POLICY,K=X.createHTML("")}else void 0===X&&(X=Y(H,a)),null!==X&&"string"==typeof K&&(K=X.createHTML(""));r&&r(e),Qe=e}},ot=addToSet({},["mi","mo","mn","ms","mtext"]),at=addToSet({},["foreignobject","desc","title","annotation-xml"]),rt=addToSet({},["title","style","font","a","script"]),it=addToSet({},[...b,...x,...N]),lt=addToSet({},[...R,...v]),st=function _checkValidNamespace(e){let t=V(e);t&&t.tagName||(t={namespaceURI:Ye,tagName:"template"});const n=p(e.tagName),o=p(t.tagName);return!!qe[e.namespaceURI]&&(e.namespaceURI===We?t.namespaceURI===je?"svg"===n:t.namespaceURI===Ge?"svg"===n&&("annotation-xml"===o||ot[o]):Boolean(it[n]):e.namespaceURI===Ge?t.namespaceURI===je?"math"===n:t.namespaceURI===We?"math"===n&&at[o]:Boolean(lt[n]):e.namespaceURI===je?!(t.namespaceURI===We&&!at[o])&&!(t.namespaceURI===Ge&&!ot[o])&&!lt[n]&&(rt[n]||!it[n]):!("application/xhtml+xml"!==Xe||!qe[e.namespaceURI]))},ct=function _forceRemove(e){m(DOMPurify.removed,{element:e});try{e.parentNode.removeChild(e)}catch(t){e.remove()}},ut=function _removeAttribute(e,t){try{m(DOMPurify.removed,{attribute:t.getAttributeNode(e),from:t})}catch(e){m(DOMPurify.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e&&!fe[e])if(ve||Ce)try{ct(t)}catch(e){}else try{t.setAttribute(e,"")}catch(e){}},dt=function _initDocument(e){let t=null,o=null;if(Re)e="<remove></remove>"+e;else{const t=h(e,/^[\r\n\t ]+/);o=t&&t[0]}"application/xhtml+xml"===Xe&&Ye===je&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");const a=X?X.createHTML(e):e;if(Ye===je)try{t=(new U).parseFromString(a,Xe)}catch(e){}if(!t||!t.documentElement){t=Z.createDocument(Ye,"template",null);try{t.documentElement.innerHTML=$e?K:a}catch(e){}}const r=t.body||t.documentElement;return e&&o&&r.insertBefore(n.createTextNode(o),r.childNodes[0]||null),Ye===je?ee.call(t,xe?"html":"body")[0]:xe?t.documentElement:r},mt=function _createNodeIterator(e){return J.call(e.ownerDocument||e,e,I.SHOW_ELEMENT|I.SHOW_COMMENT|I.SHOW_TEXT|I.SHOW_PROCESSING_INSTRUCTION,null)},pt=function _isClobbered(e){return e instanceof P&&("string"!=typeof e.nodeName||"string"!=typeof e.textContent||"function"!=typeof e.removeChild||!(e.attributes instanceof M)||"function"!=typeof e.removeAttribute||"function"!=typeof e.setAttribute||"string"!=typeof e.namespaceURI||"function"!=typeof e.insertBefore||"function"!=typeof e.hasChildNodes)},ft=function _isNode(e){return"function"==typeof c&&e instanceof c},ht=function _executeHook(e,t,n){ne[e]&&u(ne[e],(e=>{e.call(DOMPurify,t,n,Qe)}))},gt=function _sanitizeElements(e){let t=null;if(ht("beforeSanitizeElements",e,null),pt(e))return ct(e),!0;const n=Je(e.nodeName);if(ht("uponSanitizeElement",e,{tagName:n,allowedTags:me}),e.hasChildNodes()&&!ft(e.firstElementChild)&&E(/<[/\w]/g,e.innerHTML)&&E(/<[/\w]/g,e.textContent))return ct(e),!0;if(!me[n]||Te[n]){if(!Te[n]&&yt(n)){if(ge.tagNameCheck instanceof RegExp&&E(ge.tagNameCheck,n))return!1;if(ge.tagNameCheck instanceof Function&&ge.tagNameCheck(n))return!1}if(Oe&&!Pe[n]){const t=V(e)||e.parentNode,n=q(e)||e.childNodes;if(n&&t)for(let o=n.length-1;o>=0;--o)t.insertBefore(G(n[o],!0),$(e))}return ct(e),!0}return e instanceof O&&!st(e)?(ct(e),!0):"noscript"!==n&&"noembed"!==n&&"noframes"!==n||!E(/<\/no(script|embed|frames)/i,e.innerHTML)?(be&&3===e.nodeType&&(t=e.textContent,u([oe,ae,re],(e=>{t=g(t,e," ")})),e.textContent!==t&&(m(DOMPurify.removed,{element:e.cloneNode()}),e.textContent=t)),ht("afterSanitizeElements",e,null),!1):(ct(e),!0)},Tt=function _isValidAttribute(e,t,o){if(we&&("id"===t||"name"===t)&&(o in n||o in et))return!1;if(Ee&&!ye[t]&&E(ie,t));else if(Se&&E(le,t));else if(!fe[t]||ye[t]){if(!(yt(e)&&(ge.tagNameCheck instanceof RegExp&&E(ge.tagNameCheck,e)||ge.tagNameCheck instanceof Function&&ge.tagNameCheck(e))&&(ge.attributeNameCheck instanceof RegExp&&E(ge.attributeNameCheck,t)||ge.attributeNameCheck instanceof Function&&ge.attributeNameCheck(t))||"is"===t&&ge.allowCustomizedBuiltInElements&&(ge.tagNameCheck instanceof RegExp&&E(ge.tagNameCheck,o)||ge.tagNameCheck instanceof Function&&ge.tagNameCheck(o))))return!1}else if(ze[t]);else if(E(de,g(o,ce,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==T(o,"data:")||!Fe[e])if(_e&&!E(se,g(o,ce,"")));else if(o)return!1;return!0},yt=function _isBasicCustomElement(e){return"annotation-xml"!==e&&h(e,ue)},St=function _sanitizeAttributes(e){ht("beforeSanitizeAttributes",e,null);const{attributes:t}=e;if(!t)return;const n={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:fe};let o=t.length;for(;o--;){const a=t[o],{name:r,namespaceURI:i,value:l}=a,s=Je(r);let c="value"===r?l:y(l);if(n.attrName=s,n.attrValue=c,n.keepAttr=!0,n.forceKeepAttr=void 0,ht("uponSanitizeAttribute",e,n),c=n.attrValue,n.forceKeepAttr)continue;if(ut(r,e),!n.keepAttr)continue;if(!Ae&&E(/\/>/i,c)){ut(r,e);continue}be&&u([oe,ae,re],(e=>{c=g(c,e," ")}));const m=Je(e.nodeName);if(Tt(m,s,c)){if(!ke||"id"!==s&&"name"!==s||(ut(r,e),c=Le+c),X&&"object"==typeof H&&"function"==typeof H.getAttributeType)if(i);else switch(H.getAttributeType(m,s)){case"TrustedHTML":c=X.createHTML(c);break;case"TrustedScriptURL":c=X.createScriptURL(c)}try{i?e.setAttributeNS(i,r,c):e.setAttribute(r,c),d(DOMPurify.removed)}catch(e){}}}ht("afterSanitizeAttributes",e,null)},Et=function _sanitizeShadowDOM(e){let t=null;const n=mt(e);for(ht("beforeSanitizeShadowDOM",e,null);t=n.nextNode();)ht("uponSanitizeShadowNode",t,null),gt(t)||(t.content instanceof i&&_sanitizeShadowDOM(t.content),St(t));ht("afterSanitizeShadowDOM",e,null)};return DOMPurify.sanitize=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=null,a=null,r=null,l=null;if($e=!e,$e&&(e="\x3c!--\x3e"),"string"!=typeof e&&!ft(e)){if("function"!=typeof e.toString)throw _("toString is not a function");if("string"!=typeof(e=e.toString()))throw _("dirty is not a string, aborting")}if(!DOMPurify.isSupported)return e;if(Ne||nt(t),DOMPurify.removed=[],"string"==typeof e&&(Ie=!1),Ie){if(e.nodeName){const t=Je(e.nodeName);if(!me[t]||Te[t])throw _("root node is forbidden and cannot be sanitized in-place")}}else if(e instanceof c)n=dt("\x3c!----\x3e"),a=n.ownerDocument.importNode(e,!0),1===a.nodeType&&"BODY"===a.nodeName||"HTML"===a.nodeName?n=a:n.appendChild(a);else{if(!ve&&!be&&!xe&&-1===e.indexOf("<"))return X&&De?X.createHTML(e):e;if(n=dt(e),!n)return ve?null:De?K:""}n&&Re&&ct(n.firstChild);const s=mt(Ie?e:n);for(;r=s.nextNode();)gt(r)||(r.content instanceof i&&Et(r.content),St(r));if(Ie)return e;if(ve){if(Ce)for(l=Q.call(n.ownerDocument);n.firstChild;)l.appendChild(n.firstChild);else l=n;return(fe.shadowroot||fe.shadowrootmode)&&(l=te.call(o,l,!0)),l}let d=xe?n.outerHTML:n.innerHTML;return xe&&me["!doctype"]&&n.ownerDocument&&n.ownerDocument.doctype&&n.ownerDocument.doctype.name&&E(B,n.ownerDocument.doctype.name)&&(d="<!DOCTYPE "+n.ownerDocument.doctype.name+">\n"+d),be&&u([oe,ae,re],(e=>{d=g(d,e," ")})),X&&De?X.createHTML(d):d},DOMPurify.setConfig=function(){nt(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}),Ne=!0},DOMPurify.clearConfig=function(){Qe=null,Ne=!1},DOMPurify.isValidAttribute=function(e,t,n){Qe||nt({});const o=Je(e),a=Je(t);return Tt(o,a,n)},DOMPurify.addHook=function(e,t){"function"==typeof t&&(ne[e]=ne[e]||[],m(ne[e],t))},DOMPurify.removeHook=function(e){if(ne[e])return d(ne[e])},DOMPurify.removeHooks=function(e){ne[e]&&(ne[e]=[])},DOMPurify.removeAllHooks=function(){ne={}},DOMPurify}return createDOMPurify()}()}}]);