/*! elementor - v3.23.0 - 05-08-2024 */
"use strict";(self.webpackChunkelementor=self.webpackChunkelementor||[]).push([[413],{8073:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;class GridContainer extends elementorModules.frontend.handlers.Base{__construct(e){super.__construct(e),this.onDeviceModeChange=this.onDeviceModeChange.bind(this),this.updateEmptyViewHeight=this.updateEmptyViewHeight.bind(this)}isActive(){return elementorFrontend.isEditMode()}getDefaultSettings(){return{selectors:{gridOutline:".e-grid-outline",directGridOverlay:":scope > .e-grid-outline",boxedContainer:":scope > .e-con-inner",emptyView:".elementor-empty-view"},classes:{outline:"e-grid-outline",outlineItem:"e-grid-outline-item"}}}getDefaultElements(){const e=this.getSettings("selectors");return{outlineParentContainer:null,gridOutline:this.findElement(e.gridOutline),directChildGridOverlay:this.findElement(e.directGridOverlay),emptyView:this.findElement(e.emptyView)[0],container:this.$element[0]}}onInit(){super.onInit(),this.initLayoutOverlay(),this.updateEmptyViewHeight(),elementor.hooks.addAction("panel/open_editor/container",this.onPanelShow)}onPanelShow(e,t){const n=t.get("settings").get("container_type"),i=e.$el.find("#elementor-panel__editor__help__link"),s="grid"===n?"https://go.elementor.com/widget-container-grid":"https://go.elementor.com/widget-container";i&&i.attr("href",s)}bindEvents(){elementorFrontend.elements.$window.on("resize",this.onDeviceModeChange),elementorFrontend.elements.$window.on("resize",this.updateEmptyViewHeight),this.addChildLifeCycleEventListeners()}unbindEvents(){this.removeChildLifeCycleEventListeners(),elementorFrontend.elements.$window.off("resize",this.onDeviceModeChange),elementorFrontend.elements.$window.off("resize",this.updateEmptyViewHeight)}initLayoutOverlay(){this.getCorrectContainer();const e=this.getSettings("selectors"),t="grid"===this.getElementSettings("container_type");this.elements.emptyView=this.findElement(e.emptyView)[0],t&&this.elements?.emptyView&&(this.elements.emptyView.style.display=this.shouldRemoveEmptyView()?"none":"block"),this.shouldDrawOutline()&&(this.removeExistingOverlay(),this.createOverlayContainer(),this.createOverlayItems())}shouldDrawOutline(){const{grid_outline:e}=this.getElementSettings();return e}getCorrectContainer(){const e=this.elements.container,t=this.getDefaultSettings(),{selectors:{boxedContainer:n}}=t;this.elements.outlineParentContainer=e.querySelector(n)||e}removeExistingOverlay(){this.elements.gridOutline?.remove()}createOverlayContainer(){const{outlineParentContainer:e}=this.elements,{classes:{outline:t}}=this.getDefaultSettings(),n=document.createElement("div");n.classList.add(t),e.appendChild(n),this.elements.gridOutline=n,this.setGridOutlineDimensions()}createOverlayItems(){const{gridOutline:e}=this.elements,{classes:{outlineItem:t}}=this.getDefaultSettings(),n=this.getMaxOutlineElementsNumber();for(let i=0;i<n;i++){const n=document.createElement("div");n.classList.add(t),e.appendChild(n)}}getDeviceGridDimensions(){const e=elementor.channels.deviceMode.request("currentMode");return{rows:this.getControlValues("grid_rows_grid",e,"grid-template-rows")||1,columns:this.getControlValues("grid_columns_grid",e,"grid-template-columns")||1}}setGridOutlineDimensions(){const{gridOutline:e}=this.elements,{rows:t,columns:n}=this.getDeviceGridDimensions();e.style.gridTemplateColumns=n.value,e.style.gridTemplateRows=t.value}getControlValues(e,t,n){const i=this.getElementSettings(),{unit:s,size:o}=i[e],{outlineParentContainer:r}=this.elements,l=elementorFrontend.utils.controls.getResponsiveControlValue(i,e,"size",t),d=this.getComputedStyle(r,n),a=d.split(" ").length;let h;return h="custom"===s&&"string"==typeof l||o<a?{value:d}:{value:`repeat(${a}, 1fr)`},h={...h,length:a},h}getComputedStyle(e,t){return window?.getComputedStyle(e,null).getPropertyValue(t)}onElementChange(e){this.isControlThatMayAffectEmptyViewHeight(e)&&this.updateEmptyViewHeight();let t=["grid_rows_grid","grid_columns_grid","grid_gaps","container_type","boxed_width","content_width","width","height","min_height","padding","grid_auto_flow"];t=this.getResponsiveControlNames(t),t.includes(e)&&this.initLayoutOverlay()}isControlThatMayAffectEmptyViewHeight(e){return 0===e.indexOf("grid_rows_grid")||0===e.indexOf("grid_columns_grid")||0===e.indexOf("grid_auto_flow")}getResponsiveControlNames(e){const t=elementorFrontend.breakpoints.getActiveBreakpointsList(),n=[];for(const i of e)for(const e of t)n.push(`${i}_${e}`);return n.push(...e),n}onDeviceModeChange(){this.initLayoutOverlay()}addChildLifeCycleEventListeners(){this.lifecycleChangeListener=this.initLayoutOverlay.bind(this),window.addEventListener("elementor/editor/element-rendered",this.lifecycleChangeListener),window.addEventListener("elementor/editor/element-destroyed",this.lifecycleChangeListener)}removeChildLifeCycleEventListeners(){window.removeEventListener("elementor/editor/element-rendered",this.lifecycleChangeListener),window.removeEventListener("elementor/editor/element-destroyed",this.lifecycleChangeListener)}updateEmptyViewHeight(){if(this.shouldUpdateEmptyViewHeight()){const{emptyView:e}=this.elements,t=elementor.channels.deviceMode.request("currentMode"),n=this.getElementSettings(),i="desktop"===t?n.grid_rows_grid:n.grid_rows_grid+"_"+t;e?.style.removeProperty("min-height"),this.hasCustomUnit(i)&&this.isNotOnlyANumber(i)&&this.sizeNotEmpty(i)&&(e.style.minHeight="auto"),e?.offsetHeight<=0&&(e.style.minHeight="100px")}}shouldUpdateEmptyViewHeight(){return!!this.elements.container.querySelector(".elementor-empty-view")}hasCustomUnit(e){return"custom"===e?.unit}sizeNotEmpty(e){return""!==e?.size?.trim()}isNotOnlyANumber(e){return!/^\d+$/.test(e?.size)}shouldRemoveEmptyView(){const e=this.elements.outlineParentContainer.querySelectorAll(":scope > .elementor-element").length;if(0===e)return!1;return this.getMaxElementsNumber()<=e&&this.isFullFilled(e)}isFullFilled(e){const t=this.getDeviceGridDimensions(),{grid_auto_flow:n}=this.getElementSettings();return 0==e%t["row"===n?"columns":"rows"].length}getMaxOutlineElementsNumber(){const e=this.elements.outlineParentContainer.querySelectorAll(":scope > .elementor-element").length,t=this.getDeviceGridDimensions(),n=this.getMaxElementsNumber(),{grid_auto_flow:i}=this.getElementSettings(),s="row"===i?"columns":"rows",o=Math.ceil(e/t[s].length)*t[s].length;return n>o?n:o}getMaxElementsNumber(){const e=this.getElementSettings(),t=elementor.channels.deviceMode.request("currentMode"),{grid_auto_flow:n}=this.getElementSettings(),i=this.getDeviceGridDimensions();if("row"===n){const n=elementorFrontend.utils.controls.getResponsiveControlValue(e,"grid_rows_grid","size",t),s=isNaN(n)?n.split(" ").length:n;return i.columns.length*s}const s=elementorFrontend.utils.controls.getResponsiveControlValue(e,"grid_columns_grid","size",t),o=isNaN(s)?rows.split(" ").length:s;return i.rows.length*o}}t.default=GridContainer},2929:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;class HandlesPosition extends elementorModules.frontend.handlers.Base{isActive(){return elementorFrontend.isEditMode()}isFirstContainer(){return this.$element[0]===document.querySelector(".elementor-edit-mode .e-con:first-child")}isOverflowHidden(){return"hidden"===this.$element.css("overflow")}getOffset(){if("body"===elementor.config.document.container)return this.$element.offset().top;const e=jQuery(elementor.config.document.container);return this.$element.offset().top-e.offset().top}setHandlesPosition(){const e=elementor.documents.getCurrent();if(!e||!e.container.isEditable())return;const t=this.isOverflowHidden();if(!t&&!this.isFirstContainer())return;const n=t?0:this.getOffset(),i=this.$element.find("> .elementor-element-overlay > .elementor-editor-section-settings"),s="e-handles-inside";n<25?(this.$element.addClass(s),n<-5?i.css("top",-n):i.css("top","")):this.$element.removeClass(s)}onInit(){this.isActive()&&(this.setHandlesPosition(),this.$element.on("mouseenter",this.setHandlesPosition.bind(this)))}}t.default=HandlesPosition},343:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;class Shapes extends elementorModules.frontend.handlers.Base{getDefaultSettings(){return{selectors:{container:"boxed"===this.getElementSettings("content_width")?"> .e-con-inner > .elementor-shape-%s":"> .elementor-shape-%s"},svgURL:elementorFrontend.config.urls.assets+"shapes/"}}getDefaultElements(){const e={},t=this.getSettings("selectors");return e.$topContainer=this.$element.find(t.container.replace("%s","top")),e.$bottomContainer=this.$element.find(t.container.replace("%s","bottom")),e}isActive(){return elementorFrontend.isEditMode()}getSvgURL(e,t){let n=this.getSettings("svgURL")+t+".svg";return elementor.config.additional_shapes&&e in elementor.config.additional_shapes&&(n=elementor.config.additional_shapes[e],-1<t.indexOf("-negative")&&(n=n.replace(".svg","-negative.svg"))),n}buildSVG(e){const t="shape_divider_"+e,n=this.getElementSettings(t),i=this.elements["$"+e+"Container"];if(i.attr("data-shape",n),!n)return void i.empty();let s=n;this.getElementSettings(t+"_negative")&&(s+="-negative");const o=this.getSvgURL(n,s);jQuery.get(o,(e=>{i.empty().append(e.childNodes[0])})),this.setNegative(e)}setNegative(e){this.elements["$"+e+"Container"].attr("data-negative",!!this.getElementSettings("shape_divider_"+e+"_negative"))}onInit(){this.isActive(this.getSettings())&&(super.onInit(...arguments),["top","bottom"].forEach((e=>{this.getElementSettings("shape_divider_"+e)&&this.buildSVG(e)})))}onElementChange(e){const t=e.match(/^shape_divider_(top|bottom)$/);if(t)return void this.buildSVG(t[1]);const n=e.match(/^shape_divider_(top|bottom)_negative$/);n&&(this.buildSVG(n[1]),this.setNegative(n[1]))}}t.default=Shapes}}]);