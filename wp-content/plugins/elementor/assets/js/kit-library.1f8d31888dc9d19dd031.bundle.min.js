/*! elementor - v3.23.0 - 05-08-2024 */
(self.webpackChunkelementor=self.webpackChunkelementor||[]).push([[5372],{99175:()=>{},4791:()=>{},88079:()=>{},70116:()=>{},39194:()=>{},12262:()=>{},43417:()=>{},41744:()=>{},47218:()=>{},39723:()=>{},91601:()=>{},85604:()=>{},98299:()=>{},3740:()=>{},23474:()=>{},37126:()=>{},53964:()=>{},84179:()=>{},69097:()=>{},87206:(e,t,r)=>{"use strict";var n=r(23615),a=r(38003).__,i=r(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=ElementorLoading;var o=i(r(87363));function ElementorLoading(e){return o.default.createElement("div",{className:"elementor-loading"},o.default.createElement("div",{className:"elementor-loader-wrapper"},o.default.createElement("div",{className:"elementor-loader"},o.default.createElement("div",{className:"elementor-loader-boxes"},o.default.createElement("div",{className:"elementor-loader-box"}),o.default.createElement("div",{className:"elementor-loader-box"}),o.default.createElement("div",{className:"elementor-loader-box"}),o.default.createElement("div",{className:"elementor-loader-box"}))),o.default.createElement("div",{className:"elementor-loading-title"},e.loadingText)))}ElementorLoading.propTypes={loadingText:n.string},ElementorLoading.defaultProps={loadingText:a("Loading","elementor")}},15368:(e,t,r)=>{"use strict";var n=r(23615),a=r(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.default=PopoverDialog;var i=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==a(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var l=i?Object.getOwnPropertyDescriptor(e,o):null;l&&(l.get||l.set)?Object.defineProperty(n,o,l):n[o]=e[o]}n.default=e,r&&r.set(e,n);return n}(r(87363));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}function PopoverDialog(e){var t=e.targetRef,r=e.offsetTop,n=e.offsetLeft,a=e.wrapperClass,o=e.trigger,l=e.hideAfter,u=(0,i.useCallback)((function(e){var a=null==t?void 0:t.current;if(a&&e){var i=function showPopover(){e.style.display="block",e.setAttribute("aria-expanded",!0);var t=a.getBoundingClientRect(),i=e.getBoundingClientRect(),o=i.width-t.width;e.style.top=t.bottom+r+"px",e.style.left=t.left-o/2-n+"px",e.style.setProperty("--popover-arrow-offset-end",(i.width-16)/2+"px")},u=function hidePopover(){e.style.display="none",e.setAttribute("aria-expanded",!1)};"hover"===o?function handlePopoverHover(){var t=!0,r=null;a.addEventListener("mouseover",(function(){t=!0,i()})),a.addEventListener("mouseleave",(function(){r=setTimeout((function(){t&&"block"===e.style.display&&u()}),l)})),e.addEventListener("mouseover",(function(){t=!1,r&&(clearTimeout(r),r=null)})),e.addEventListener("mouseleave",(function(){r=setTimeout((function(){t&&"block"===e.style.display&&u()}),l),t=!0}))}():"click"===o&&function handlePopoverClick(){var t=!1;a.addEventListener("click",(function(e){e.preventDefault(),e.stopPropagation(),t?(u(),t=!1):(i(),t=!0)})),e.addEventListener("click",(function(e){e.stopPropagation()})),document.body.addEventListener("click",(function(){t&&(u(),t=!1)}))}()}}),[t]),c="e-app__popover";return a&&(c+=" "+a),i.default.createElement("div",{className:c,ref:u},e.children)}PopoverDialog.propTypes={targetRef:n.oneOfType([n.func,n.shape({current:n.any})]).isRequired,trigger:n.string,direction:n.string,offsetTop:n.oneOfType([n.string,n.number]),offsetLeft:n.oneOfType([n.string,n.number]),wrapperClass:n.string,children:n.any,hideAfter:n.number},PopoverDialog.defaultProps={direction:"bottom",trigger:"hover",offsetTop:10,offsetLeft:0,hideAfter:300}},4859:(e,t,r)=>{"use strict";var n=r(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function App(){return a.default.createElement("div",{className:"e-kit-library"},a.default.createElement(s.QueryClientProvider,{client:m},a.default.createElement(p.SettingsProvider,{value:elementorAppConfig["kit-library"]},a.default.createElement(c.LastFilterProvider,null,a.default.createElement(f.Router,null,a.default.createElement(o.default,{path:"/"}),a.default.createElement(i.default,{path:"/favorites"}),a.default.createElement(u.default,{path:"/preview/:id"}),a.default.createElement(l.default,{path:"/overview/:id"})))),elementorCommon.config.isElementorDebug&&a.default.createElement(d.ReactQueryDevtools,{initialIsOpen:!1})))};var a=n(r(87363)),i=n(r(56005)),o=n(r(98931)),l=n(r(37971)),u=n(r(17490)),c=r(55040),s=r(56552),d=r(29029),f=r(50927),p=r(28405),m=new s.QueryClient({defaultOptions:{queries:{refetchOnWindowFocus:!1,retry:!1,staleTime:18e5}}})},29781:(e,t,r)=>{"use strict";var n=r(38003).__,a=r(23615),i=r(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.default=ApplyKitDialog;var o=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==i(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var l=a?Object.getOwnPropertyDescriptor(e,o):null;l&&(l.get||l.set)?Object.defineProperty(n,o,l):n[o]=e[o]}n.default=e,r&&r.set(e,n);return n}(r(87363)),l=r(50927),u=r(36779);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}function ApplyKitDialog(e){var t=(0,l.useNavigate)(),r=(0,o.useCallback)((function(){var r=arguments.length>0&&void 0!==arguments[0]&&arguments[0],n="/import/process"+"?id=".concat(e.id)+"&file_url=".concat(encodeURIComponent(e.downloadLink))+"&nonce=".concat(e.nonce,"&referrer=kit-library");r&&(n+="&action_type=apply-all"),t(n)}),[e.downloadLink,e.nonce]);return o.default.createElement(u.Dialog,{title:n("Apply %s?","elementor").replace("%s",e.title),text:o.default.createElement(o.default.Fragment,null,n("You can use everything in this kit, or Customize to only include some items.","elementor"),o.default.createElement("br",null),o.default.createElement("br",null),n("By applying the entire kit, you'll override any styles, settings or content already on your site.","elementor")),approveButtonText:n("Apply All","elementor"),approveButtonColor:"primary",approveButtonOnClick:function approveButtonOnClick(){return r(!0)},dismissButtonText:n("Customize","elementor"),dismissButtonOnClick:function dismissButtonOnClick(){return r(!1)},onClose:e.onClose})}ApplyKitDialog.propTypes={id:a.string.isRequired,downloadLink:a.string.isRequired,nonce:a.string.isRequired,onClose:a.func.isRequired,title:a.string},ApplyKitDialog.defaultProps={title:"Kit"}},71445:(e,t,r)=>{"use strict";var n=r(23615),a=r(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=Badge;var i=a(r(87363));function Badge(e){return i.default.createElement("span",{className:"eps-badge eps-badge--".concat(e.variant," ").concat(e.className),style:e.style},e.children)}r(99175),Badge.propTypes={children:n.node,className:n.string,style:n.object,variant:n.oneOf(["sm","md"])},Badge.defaultProps={className:"",style:{},variant:"md"}},67405:(e,t,r)=>{"use strict";var n=r(23615),a=r(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=Collapse;var i=a(r(87363));function Collapse(e){return i.default.createElement("div",{className:"eps-collapse ".concat(e.className),"data-open":e.isOpen||void 0},i.default.createElement("button",{className:"eps-collapse__title",onClick:function onClick(){var t;e.onChange((function(e){return!e})),null===(t=e.onClick)||void 0===t||t.call(e,e.isOpen,e.title)}},i.default.createElement("span",null,e.title),i.default.createElement("i",{className:"eicon-chevron-right eps-collapse__icon"})),i.default.createElement("div",{className:"eps-collapse__content"},e.children))}r(4791),Collapse.propTypes={isOpen:n.bool,onChange:n.func,className:n.string,title:n.node,onClick:n.func,children:n.oneOfType([n.node,n.arrayOf(n.node)])},Collapse.defaultProps={className:"",isOpen:!1}},79742:(e,t,r)=>{"use strict";var n=r(38003).__,a=r(23615),i=r(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=ConnectDialog;var o=i(r(87363)),l=r(36779),u=r(28405),c=o.default,s=c.useEffect,d=c.useRef;function ConnectDialog(e){var t=(0,u.useSettingsContext)().settings,r=d();return s((function(){jQuery(r.current).elementorConnect({success:function success(t,r){return e.onSuccess(r)},error:function error(){return e.onError(n("Unable to connect","elementor"))},parseUrl:function parseUrl(t){return t.replace("%%page%%",e.pageId)}})}),[]),o.default.createElement(l.Dialog,{title:n("Connect to Template Library","elementor"),text:n("Access this template and our entire library by creating a free personal account","elementor"),approveButtonText:n("Get Started","elementor"),approveButtonUrl:t.library_connect_url,approveButtonOnClick:function approveButtonOnClick(){return e.onClose()},approveButtonColor:"primary",approveButtonRef:r,dismissButtonText:n("Cancel","elementor"),dismissButtonOnClick:function dismissButtonOnClick(){return e.onClose()},onClose:function onClose(){return e.onClose()}})}ConnectDialog.propTypes={onClose:a.func.isRequired,onError:a.func.isRequired,onSuccess:a.func.isRequired,pageId:a.string}},46109:(e,t,r)=>{"use strict";var n=r(38003).__,a=r(23615),i=r(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=EnvatoPromotion;var o=i(r(87363)),l=r(36779),u=r(92602);function EnvatoPromotion(e){return o.default.createElement(l.Text,{className:"e-kit-library-promotion",variant:"xl"},n("Looking for more Kits?","elementor")," "," ",o.default.createElement(l.Button,{variant:"underlined",color:"link",url:"https://go.elementor.com/app-envato-kits/",target:"_blank",rel:"noreferrer",text:n("Check out Elementor Template Kits on ThemeForest","elementor"),onClick:function onClick(){return function eventTracking(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"click";(0,u.appsEventTrackingDispatch)(t,{page_source:"home page",element_position:"library_bottom_promotion",category:e.category&&("/favorites"===e.category?"favorites":"all kits"),event_type:r})}("kit-library/check-kits-on-theme-forest")}}))}r(88079),EnvatoPromotion.propTypes={category:a.string}},68316:(e,t,r)=>{"use strict";var n=r(23615),a=r(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=ErrorScreen;var i=a(r(87363)),o=r(36779),l=r(92602);function ErrorScreen(e){return i.default.createElement(o.Grid,{container:!0,alignItems:"center",justify:"center",direction:"column",className:"e-kit-library__error-screen"},i.default.createElement("img",{src:"".concat(elementorAppConfig.assets_url,"images/no-search-results.svg")}),i.default.createElement(o.Heading,{tag:"h3",variant:"display-1",className:"e-kit-library__error-screen-title"},e.title),i.default.createElement(o.Text,{variant:"xl",className:"e-kit-library__error-screen-description"},e.description," "," ",i.default.createElement(o.Button,{text:e.button.text,color:"link",onClick:function onClick(){(0,l.appsEventTrackingDispatch)("kit-library/go-back-to-view-kits",{page_source:"home page",element_position:"empty state",category:e.button.category&&("/favorites"===e.button.category?"favorites":"all")}),e.button.action()},url:e.button.url,target:e.button.target})))}r(70116),ErrorScreen.propTypes={title:n.string,description:n.string,button:n.shape({text:n.string,action:n.func,url:n.string,target:n.string,category:n.string})}},55499:(e,t,r)=>{"use strict";var n=r(38003).__,a=r(23615),i=r(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=FavoritesActions;var o=i(r(87363)),l=r(69187),u=r(36779),c=r(92602);function FavoritesActions(e){var t=(0,l.useKitFavoritesMutations)(),r=t.addToFavorites,a=t.removeFromFavorites,i=t.isLoading,s=i?"e-kit-library__kit-favorite-actions--loading":"",d=function eventTracking(e,t,r){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null;(0,c.appsEventTrackingDispatch)("kit-library/favorite-icon",{grid_location:n,search_term:a,kit_name:e,page_source:t&&("/"===t?"home page":"overview"),element_location:t&&"overview"===t?"app_sidebar":null,action:r})};return e.isFavorite?o.default.createElement(u.Button,{text:n("Remove from Favorites","elementor"),hideText:!0,icon:"eicon-heart",className:"e-kit-library__kit-favorite-actions e-kit-library__kit-favorite-actions--active ".concat(s),onClick:function onClick(){!i&&a.mutate(e.id),d(null==e?void 0:e.name,null==e?void 0:e.source,"uncheck")}}):o.default.createElement(u.Button,{text:n("Add to Favorites","elementor"),hideText:!0,icon:"eicon-heart-o",className:"e-kit-library__kit-favorite-actions ".concat(s),onClick:function onClick(){!i&&r.mutate(e.id),d(null==e?void 0:e.name,null==e?void 0:e.source,"check",null==e?void 0:e.index,null==e?void 0:e.queryParams)}})}r(39194),FavoritesActions.propTypes={isFavorite:a.bool,id:a.string,name:a.string,source:a.string,index:a.number,queryParams:a.string}},40133:(e,t,r)=>{"use strict";var n=r(38003).__,a=r(23615),i=r(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=FilterIndicationText;var o=i(r(87363)),l=i(r(94050)),u=i(r(71445)),c=r(38003),s=r(36779),d=r(92602);function FilterIndicationText(e){var t=(0,l.default)(e.queryParams.taxonomies),r=function eventTracking(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"click";(0,d.appsEventTrackingDispatch)("kit-library/clear-filter",{tag:e,page_source:"home page",event_type:t})};return o.default.createElement(s.Grid,{container:!0,className:"e-kit-library__filter-indication"},o.default.createElement(s.Text,{className:"e-kit-library__filter-indication-text"},(0,c.sprintf)((0,c._n)("Showing %s result for","Showing %s results for",e.resultCount,"elementor"),e.resultCount?e.resultCount:n("no","elementor"))," ",e.queryParams.search&&'"'.concat(e.queryParams.search,'"')," ",t.length>0&&o.default.createElement(o.default.Fragment,null,t.map((function(t){return o.default.createElement(u.default,{key:t,className:"e-kit-library__filter-indication-badge"},t,o.default.createElement(s.Button,{text:n("Remove","elementor"),hideText:!0,icon:"eicon-editor-close",className:"e-kit-library__filter-indication-badge-remove",onClick:function onClick(){r(t),e.onRemoveTag(t)}}))})))),o.default.createElement(s.Button,{className:"e-kit-library__filter-indication-button",text:n("Clear all","elementor"),variant:"underlined",onClick:function onClick(){r("all"),e.onClear()}}))}r(12262),FilterIndicationText.propTypes={queryParams:a.shape({search:a.string,taxonomies:a.objectOf(a.arrayOf(a.string)),favorite:a.bool}),resultCount:a.number.isRequired,onClear:a.func.isRequired,onRemoveTag:a.func.isRequired}},28938:(e,t,r)=>{"use strict";var n=r(38003).__,a=r(23615),i=r(73203),o=r(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.default=ItemHeader;var l=_interopRequireWildcard(r(87363)),u=i(r(73119)),c=i(r(9833)),s=i(r(40131)),d=i(r(2392)),f=i(r(79742)),p=i(r(57332)),m=i(r(22218)),v=i(r(41773)),y=i(r(2921)),g=_interopRequireWildcard(r(30642)),_=i(r(24637)),b=r(36779),h=r(28405),k=r(92602),O=r(56827);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==o(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var l=a?Object.getOwnPropertyDescriptor(e,i):null;l&&(l.get||l.set)?Object.defineProperty(n,i,l):n[i]=e[i]}return n.default=e,r&&r.set(e,n),n}function ItemHeader(e){var t=(0,h.useSettingsContext)().updateSettings,r=(0,l.useState)(!1),a=(0,s.default)(r,2),i=a[0],o=a[1],v=(0,l.useState)(null),C=(0,s.default)(v,2),w=C[0],P=C[1],E=(0,l.useState)(!1),j=(0,s.default)(E,2),T=j[0],x=j[1],R={kitName:e.model.title,pageId:e.pageId},S=(0,y.default)(e.model,{onSuccess:function onSuccess(e){var t=e.data;return P(t)},onError:function onError(e){if(401===e.code)return elementorCommon.config.library_connect.is_connected=!1,elementorCommon.config.library_connect.current_access_level=0,elementorCommon.config.library_connect.current_access_tier=O.TIERS.free,t({is_library_connected:!1,access_level:0,access_tier:O.TIERS.free}),void o(!0);x({code:e.code,message:n("Something went wrong.","elementor")})}}),M=S.mutate,D=S.isLoading,W=function useKitCallToActionButton(e,t){var r=t.apply,a=t.isApplyLoading,i=t.onConnect,o=t.onClick,u=(0,g.default)(e.accessTier),c=(0,s.default)(u,2),d=c[0],f=c[1].subscriptionPlan,p=(0,_.default)(f.promotion_url,e.id,e.title),m=(0,h.useSettingsContext)().settings;return(0,l.useMemo)((function(){return d===g.TYPE_CONNECT?{id:"connect",text:n("Apply Kit","elementor"),hideText:!1,variant:"contained",color:"primary",size:"sm",onClick:function onClick(e){i(e),null==o||o(e)},includeHeaderBtnClass:!1}:d===g.TYPE_PROMOTION&&f?{id:"promotion",text:m.is_pro?"Upgrade":"Go ".concat(f.label),hideText:!1,variant:"contained",color:"cta",size:"sm",url:p,target:"_blank",includeHeaderBtnClass:!1}:{id:"apply",text:n("Apply Kit","elementor"),className:"e-kit-library__apply-button",icon:a?"eicon-loading eicon-animation-spin":"",hideText:!1,variant:"contained",color:a?"disabled":"primary",size:"sm",onClick:function onClick(e){a||r(e),null==o||o(e)},includeHeaderBtnClass:!1}}),[d,f,a,r])}(e.model,{onConnect:function onConnect(){return o(!0)},apply:M,isApplyLoading:D,onClick:function onClick(){return(0,k.appsEventTrackingDispatch)("kit-library/apply-kit",{kit_name:e.model.title,element_position:"app_header",page_source:e.pageId,event_type:"click"})}}),q=(0,l.useMemo)((function(){return[W].concat((0,c.default)(e.buttons))}),[e.buttons,W]);return l.default.createElement(l.default.Fragment,null,T&&l.default.createElement(b.Dialog,{title:T.message,text:n("Go to the pages screen to make sure your kit pages have been imported successfully. If not, try again.","elementor"),approveButtonText:n("Go to pages","elementor"),approveButtonColor:"primary",approveButtonUrl:elementorAppConfig.admin_url+"edit.php?post_type=page",approveButtonOnClick:function approveButtonOnClick(){return x(!1)},dismissButtonText:n("Got it","elementor"),dismissButtonOnClick:function dismissButtonOnClick(){return x(!1)},onClose:function onClose(){return x(!1)}}),w&&l.default.createElement(d.default,{id:e.model.id,downloadLinkData:w,onClose:function onClose(){return P(null)}}),i&&l.default.createElement(f.default,{pageId:e.pageId,onClose:function onClose(){return o(!1)},onSuccess:function onSuccess(r){var n=r.kits_access_level||r.access_level||0,a=r.access_tier;elementorCommon.config.library_connect.is_connected=!0,elementorCommon.config.library_connect.current_access_level=n,elementorCommon.config.library_connect.current_access_tier=a,t({is_library_connected:!0,access_level:n,access_tier:a}),r.access_level<e.model.accessLevel||(0,O.isTierAtLeast)(a,e.model.accessTier)&&M()},onError:function onError(e){return x({message:e})}}),l.default.createElement(p.default,(0,u.default)({startColumn:l.default.createElement(m.default,R),centerColumn:e.centerColumn,buttons:q},R)))}r(43417),ItemHeader.propTypes={model:a.instanceOf(v.default).isRequired,centerColumn:a.node,buttons:a.arrayOf(a.object),pageId:a.string}},57378:(e,t,r)=>{"use strict";var n=r(38003).__,a=r(23615),i=r(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=KitAlreadyAppliedDialog;var o=i(r(87363)),l=r(36779);function KitAlreadyAppliedDialog(e){return o.default.createElement(l.Dialog,{title:n("You've already applied a Kit.","elementor"),text:o.default.createElement(o.default.Fragment,null,n("Applying two Kits on the same website will mix global styles and colors and hurt your site's performance.","elementor"),o.default.createElement("br",null),o.default.createElement("br",null),n("Remove the existing Kit before applying a new one.","elementor")),approveButtonText:n("Remove existing Kit","elementor"),approveButtonColor:"primary",approveButtonOnClick:function approveButtonOnClick(){return location.href=function getRemoveKitUrl(){var t=elementorAppConfig["import-export"].tools_url,r=new URL(t);return r.searchParams.append("referrer_kit",e.id),r.hash="tab-import-export-kit",r.toString()}()},dismissButtonText:n("Apply anyway","elementor"),dismissButtonOnClick:e.dismissButtonOnClick,onClose:e.onClose})}KitAlreadyAppliedDialog.propTypes={id:a.string.isRequired,dismissButtonOnClick:a.func.isRequired,onClose:a.func.isRequired}},2392:(e,t,r)=>{"use strict";var n=r(23615),a=r(73203),i=r(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.default=KitDialog;var o=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==i(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var l=a?Object.getOwnPropertyDescriptor(e,o):null;l&&(l.get||l.set)?Object.defineProperty(n,o,l):n[o]=e[o]}n.default=e,r&&r.set(e,n);return n}(r(87363)),l=a(r(40131)),u=a(r(57378)),c=a(r(29781));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}function KitDialog(e){var t=(0,o.useState)(!1),r=(0,l.default)(t,2),n=r[0],a=r[1];return!!elementorAppConfig["import-export"].lastImportedSession.session_id&&!n?o.default.createElement(u.default,{id:e.id,dismissButtonOnClick:function dismissButtonOnClick(){return a(!0)},onClose:e.onClose}):o.default.createElement(c.default,{id:e.id,downloadLink:e.downloadLinkData.data.download_link,nonce:e.downloadLinkData.meta.nonce,onClose:e.onClose})}KitDialog.propTypes={id:n.string.isRequired,downloadLinkData:n.object.isRequired,onClose:n.func.isRequired}},12283:(e,t,r)=>{"use strict";var n=r(38003).__,a=r(23615),i=r(73203),o=r(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var l=i(r(87363)),u=i(r(40131)),c=i(r(71445)),s=i(r(55499)),d=i(r(41773)),f=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==o(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var l=a?Object.getOwnPropertyDescriptor(e,i):null;l&&(l.get||l.set)?Object.defineProperty(n,i,l):n[i]=e[i]}n.default=e,r&&r.set(e,n);return n}(r(30642)),p=i(r(24637)),m=r(36779),v=r(92602),y=r(28405);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}r(41744);var g=function KitListItem(e){var t=(0,f.default)(e.model.accessTier),r=(0,u.default)(t,2),a=r[0],i=r[1],o=i.subscriptionPlan,d=i.badgeLabel,g=(0,y.useSettingsContext)().settings,_=(0,p.default)(o.promotion_url,e.model.id,e.model.title),b=g.is_pro?"Upgrade":"Go ".concat((null==o?void 0:o.label)||""),h=f.TYPE_PROMOTION===a;return l.default.createElement(m.Card,{className:"e-kit-library__kit-item"},l.default.createElement(m.CardHeader,null,l.default.createElement(m.Heading,{tag:"h3",title:e.model.title,variant:"h5",className:"eps-card__headline"},e.model.title),l.default.createElement(s.default,{id:e.model.id,isFavorite:e.model.isFavorite,index:e.index,name:e.model.title,queryParams:e.queryParams,source:e.source})),l.default.createElement(m.CardBody,null,l.default.createElement(m.CardImage,{alt:e.model.title,src:e.model.thumbnailUrl||""},h&&l.default.createElement(c.default,{variant:"sm",className:"e-kit-library__kit-item-subscription-plan-badge",style:{"--e-a-color-brand":o.color}},d),l.default.createElement(m.CardOverlay,null,l.default.createElement(m.Grid,{container:!0,direction:"column",className:"e-kit-library__kit-item-overlay"},l.default.createElement(m.Button,{className:"e-kit-library__kit-item-overlay-overview-button",text:n("View Demo","elementor"),icon:"eicon-preview-medium",url:"/kit-library/preview/".concat(e.model.id),onClick:function onClick(){return function eventTracking(t){(0,v.appsEventTrackingDispatch)(t,{kit_name:e.model.title,grid_location:e.index,search_term:e.queryParams,page_source:e.source&&"/"===e.source?"all kits":"favorites"})}("kit-library/check-out-kit")}}),h&&l.default.createElement(m.Button,{className:"e-kit-library__kit-item-overlay-promotion-button",text:b,icon:"eicon-external-link-square",url:_,target:"_blank"}))))))};g.propTypes={model:a.instanceOf(d.default).isRequired,index:a.number,queryParams:a.string,source:a.string};var _=l.default.memo(g);t.default=_},53772:(e,t,r)=>{"use strict";var n=r(23615),a=r(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=KitList;var i=a(r(87363)),o=r(50927),l=a(r(41773)),u=a(r(12283)),c=a(r(83601)),s=r(36779);function KitList(e){var t,r=(0,o.useLocation)(),n=new URLSearchParams(null===(t=r.pathname.split("?"))||void 0===t?void 0:t[1]).get("referrer");return i.default.createElement(s.CssGrid,{spacing:24,colMinWidth:290},"onboarding"===n&&i.default.createElement(c.default,null),e.data.map((function(t,r){var n;return i.default.createElement(u.default,{key:t.id,model:t,index:r+1,queryParams:null===(n=e.queryParams)||void 0===n?void 0:n.search,source:e.source})})))}KitList.propTypes={data:n.arrayOf(n.instanceOf(l.default)),queryParams:n.shape({search:n.string}),source:n.string}},22218:(e,t,r)=>{"use strict";var n=r(38003).__,a=r(23615),i=r(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=HeaderBackButton;var o=i(r(87363)),l=r(36779),u=r(55040),c=r(50927),s=r(92602);function HeaderBackButton(e){var t=(0,c.useNavigate)(),r=(0,u.useLastFilterContext)().lastFilter;return o.default.createElement("div",{className:"e-kit-library__header-back-container"},o.default.createElement(l.Button,{className:"e-kit-library__header-back",icon:"eicon-chevron-left",text:n("Back to Library","elementor"),onClick:function onClick(){!function eventTracking(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"click";(0,s.appsEventTrackingDispatch)(t,{page_source:e.pageId,kit_name:e.kitName,element_position:"app_header",event_type:r})}("kit-library/back-to-library"),t(wp.url.addQueryArgs("/kit-library",r))}}))}r(47218),HeaderBackButton.propTypes={pageId:a.string.isRequired,kitName:a.string.isRequired}},57332:(e,t,r)=>{"use strict";var n=r(38003).__,a=r(23615),i=r(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=Header;var o=i(r(87363)),l=r(36779),u=i(r(78419)),c=r(92602);function Header(e){var t=function eventTracking(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"home page",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"click";return(0,c.appsEventTrackingDispatch)(e,{page_source:t,element_position:"app_header",kit_name:r,event_type:n})};return o.default.createElement(l.Grid,{container:!0,alignItems:"center",justify:"space-between",className:"eps-app__header"},e.startColumn||o.default.createElement("a",{className:"eps-app__logo-title-wrapper",href:"#/kit-library",onClick:function onClick(){return t("kit-library/logo")}},o.default.createElement("i",{className:"eps-app__logo eicon-elementor"}),o.default.createElement("h1",{className:"eps-app__title"},n("Kit Library","elementor"))),e.centerColumn||o.default.createElement("span",null),e.endColumn||o.default.createElement("div",{style:{flex:1}},o.default.createElement(u.default,{buttons:e.buttons,onClose:function onClose(){t("kit-library/close",null==e?void 0:e.pageId,null==e?void 0:e.kitName),window.top.location=elementorAppConfig.admin_url}})))}Header.propTypes={startColumn:a.node,endColumn:a.node,centerColumn:a.node,buttons:a.arrayOf(a.object),kitName:a.string,pageId:a.string}},6115:(e,t,r)=>{"use strict";var n=r(23615),a=r(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=Index;var i=a(r(87363)),o=a(r(22595));function Index(e){return i.default.createElement("div",{className:"eps-app__lightbox"},i.default.createElement("div",{className:"eps-app"},e.header,i.default.createElement("div",{className:"eps-app__main"},e.sidebar&&i.default.createElement(o.default,null,e.sidebar),e.children)))}Index.propTypes={header:n.node,sidebar:n.node,children:n.node}},8589:(e,t,r)=>{"use strict";var n=r(23615),a=r(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=PageLoader;var i=a(r(87363)),o=r(36779);function PageLoader(e){return i.default.createElement("div",{className:"e-kit-library__page-loader ".concat(e.className)},i.default.createElement(o.Icon,{className:"eicon-loading eicon-animation-spin"}))}r(39723),PageLoader.propTypes={className:n.string},PageLoader.defaultProps={className:""}},266:(e,t,r)=>{"use strict";var n=r(38003).__,a=r(23615),i=r(73203),o=r(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.default=SearchInput;var l=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==o(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var l=a?Object.getOwnPropertyDescriptor(e,i):null;l&&(l.get||l.set)?Object.defineProperty(n,i,l):n[i]=e[i]}n.default=e,r&&r.set(e,n);return n}(r(87363)),u=i(r(40131)),c=i(r(70118)),s=r(36779);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}function SearchInput(e){var t=(0,l.useState)(e.value||""),r=(0,u.default)(t,2),a=r[0],i=r[1],o=(0,c.default)((function(t){return e.onChange(t)}),e.debounceTimeout);return(0,l.useEffect)((function(){e.value!==a&&i(e.value)}),[e.value]),l.default.createElement("div",{className:"eps-search-input__container ".concat(e.className)},l.default.createElement("input",{className:"eps-search-input eps-search-input--".concat(e.size),placeholder:e.placeholder,value:a,onChange:function onChange(e){i(e.target.value),o(e.target.value)}}),l.default.createElement(s.Icon,{className:"eicon-search-bold eps-search-input__icon eps-search-input__icon--".concat(e.size)}),e.value&&l.default.createElement(s.Button,{text:n("Clear","elementor"),hideText:!0,className:"eicon-close-circle eps-search-input__clear-icon eps-search-input__clear-icon--".concat(e.size),onClick:function onClick(){return e.onChange("")}}))}r(91601),SearchInput.propTypes={placeholder:a.string,value:a.string.isRequired,onChange:a.func.isRequired,className:a.string,size:a.oneOf(["md","sm"]),debounceTimeout:a.number},SearchInput.defaultProps={className:"",size:"md",debounceTimeout:300}},20638:(e,t,r)=>{"use strict";var n=r(38003).__,a=r(23615),i=r(73203),o=r(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.default=SortSelect;var l=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==o(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var l=a?Object.getOwnPropertyDescriptor(e,i):null;l&&(l.get||l.set)?Object.defineProperty(n,i,l):n[i]=e[i]}n.default=e,r&&r.set(e,n);return n}(r(87363)),u=i(r(40131)),c=r(36779);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}function SortSelect(e){var t=function getSelectedOptionDetails(t){return e.options.find((function(e){return e.value===t}))},r=(0,l.useState)(t(e.value.by)),a=(0,u.default)(r,2),i=a[0],o=a[1];return(0,l.useEffect)((function(){var t;e.onChange({by:i.value,direction:null!==(t=i.defaultOrder)&&void 0!==t?t:e.value.direction})}),[i]),l.default.createElement("div",{className:"eps-sort-select"},l.default.createElement("div",{className:"eps-sort-select__select-wrapper"},l.default.createElement(c.Select,{options:e.options,value:e.value.by,onChange:function onChange(r){var n,a=r.target.value;o(t(a)),null===(n=e.onChangeSortValue)||void 0===n||n.call(e,a)},className:"eps-sort-select__select",onClick:function onClick(){var t;e.onChange({by:e.value.by,direction:e.value.direction}),null===(t=e.onSortSelectOpen)||void 0===t||t.call(e)}})),!i.orderDisabled&&l.default.createElement(c.Button,{text:"asc"===e.value.direction?n("Sort Descending","elementor"):n("Sort Ascending","elementor"),hideText:!0,icon:"asc"===e.value.direction?"eicon-arrow-up":"eicon-arrow-down",className:"eps-sort-select__button",onClick:function onClick(){var t=e.value.direction&&"asc"===e.value.direction?"desc":"asc";e.onChangeSortDirection&&e.onChangeSortDirection(t),e.onChange({by:e.value.by,direction:t})}}))}r(85604),SortSelect.propTypes={options:a.arrayOf(a.shape({label:a.string.isRequired,value:a.oneOfType([a.string,a.number]).isRequired})).isRequired,value:a.shape({direction:a.oneOf(["asc","desc"]).isRequired,by:a.string.isRequired}).isRequired,onChange:a.func.isRequired,onChangeSortValue:a.func,onSortSelectOpen:a.func,onChangeSortDirection:a.func}},11035:(e,t,r)=>{"use strict";var n=r(38003).__,a=r(23615),i=r(73203),o=r(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var l=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==o(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var l=a?Object.getOwnPropertyDescriptor(e,i):null;l&&(l.get||l.set)?Object.defineProperty(n,i,l):n[i]=e[i]}n.default=e,r&&r.set(e,n);return n}(r(87363)),u=i(r(9833)),c=i(r(40131)),s=i(r(83080)),d=i(r(67405)),f=i(r(266)),p=r(36779),m=r(38003),v=r(92602);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}var y=function TaxonomiesFilterList(e){var t=(0,l.useState)(e.taxonomiesByType.isOpenByDefault),r=(0,c.default)(t,2),a=r[0],i=r[1],o=(0,l.useState)(""),s=(0,c.default)(o,2),y=s[0],g=s[1],_=(0,l.useMemo)((function(){if(!y)return e.taxonomiesByType.data;var t=y.toLowerCase();return e.taxonomiesByType.data.filter((function(e){return e.text.toLowerCase().includes(t)}))}),[e.taxonomiesByType.data,y]);return l.default.createElement(d.default,{className:"e-kit-library__tags-filter-list",title:e.taxonomiesByType.label,isOpen:a,onChange:i,onClick:function onClick(t,r){var n;null===(n=e.onCollapseChange)||void 0===n||n.call(e,t,r)}},e.taxonomiesByType.data.length>=15&&l.default.createElement(f.default,{size:"sm",className:"e-kit-library__tags-filter-list-search",placeholder:(0,m.sprintf)(n("Search %s...","elementor"),e.taxonomiesByType.label),value:y,onChange:function onChange(t){var r;(g(t),t)&&(null===(r=e.onChange)||void 0===r||r.call(e,t))}}),l.default.createElement("div",{className:"e-kit-library__tags-filter-list-container"},0===_.length&&l.default.createElement(p.Text,null,n("No Results Found","elementor")),_.map((function(t){var r;return l.default.createElement("label",{key:t.text,className:"e-kit-library__tags-filter-list-item"},l.default.createElement(p.Checkbox,{checked:(null===(r=e.selected[t.type])||void 0===r?void 0:r.includes(t.text))||!1,onChange:function onChange(r){var n=r.target.checked;!function eventTracking(t,r,n,a){var i=e.category&&("/favorites"===e.category?"favorites":"all kits");(0,v.appsEventTrackingDispatch)(t,{page_source:"home page",element_location:"app_sidebar",category:i,section:r,item:a,action:n?"checked":"unchecked"})}("kit-library/filter",t.type,n,t.text),e.onSelect(t.type,(function(e){return n?[].concat((0,u.default)(e),[t.text]):e.filter((function(e){return e!==t.text}))}))}}),t.text)}))))};y.propTypes={taxonomiesByType:a.shape({key:a.string,label:a.string,data:a.arrayOf(a.instanceOf(s.default)),isOpenByDefault:a.bool}),selected:a.objectOf(a.arrayOf(a.string)),onSelect:a.func,onCollapseChange:a.func,category:a.string,onChange:a.func};var g=l.default.memo(y);t.default=g},1572:(e,t,r)=>{"use strict";var n=r(23615),a=r(73203),i=r(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.default=TaxonomiesFilter;var o=a(r(87363)),l=a(r(93231)),u=a(r(11035)),c=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==i(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var l=a?Object.getOwnPropertyDescriptor(e,o):null;l&&(l.get||l.set)?Object.defineProperty(n,o,l):n[o]=e[o]}n.default=e,r&&r.set(e,n);return n}(r(83080)),s=r(92602);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}function ownKeys(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(r),!0).forEach((function(t){(0,l.default)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ownKeys(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}r(98299);var d=o.default.useMemo;function TaxonomiesFilter(e){var t=d((function(){return e.taxonomies?c.taxonomyType.map((function(t){return _objectSpread(_objectSpread({},t),{},{data:e.taxonomies.filter((function(e){return e.type===t.key}))})})).filter((function(e){return e.data.length>0})):[]}),[e.taxonomies]),r=function eventTracking(t,r,n){var a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"click";return(0,s.appsEventTrackingDispatch)(t,{page_source:"home page",element_location:"app_sidebar",category:e.category&&("/favorites"===e.category?"favorites":"all kits"),section:n,search_term:r,event_type:a})};return o.default.createElement("div",{className:"e-kit-library__tags-filter"},t.map((function(t){return o.default.createElement(u.default,{key:t.key,taxonomiesByType:t,selected:e.selected,onSelect:e.onSelect,onCollapseChange:function onCollapseChange(e,t){r(e?"kit-library/collapse":"kit-library/expand",null,t)},onChange:function onChange(e){r("kit-library/filter",e,t.label,"search")},category:e.category})})))}TaxonomiesFilter.propTypes={selected:n.objectOf(n.arrayOf(n.string)),onSelect:n.func,taxonomies:n.arrayOf(n.instanceOf(c.default)),category:n.string}},55040:(e,t,r)=>{"use strict";var n=r(23615),a=r(73203),i=r(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.LastFilterProvider=LastFilterProvider,t.useLastFilterContext=function useLastFilterContext(){return(0,o.useContext)(u)};var o=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==i(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var l=a?Object.getOwnPropertyDescriptor(e,o):null;l&&(l.get||l.set)?Object.defineProperty(n,o,l):n[o]=e[o]}n.default=e,r&&r.set(e,n);return n}(r(87363)),l=a(r(40131));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}var u=(0,o.createContext)({});function LastFilterProvider(e){var t=(0,o.useState)({}),r=(0,l.default)(t,2),n=r[0],a=r[1];return o.default.createElement(u.Provider,{value:{lastFilter:n,setLastFilter:a}},e.children)}LastFilterProvider.propTypes={children:n.any}},28405:(e,t,r)=>{"use strict";var n=r(23615),a=r(73203),i=r(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.SettingsProvider=SettingsProvider,t.useSettingsContext=function useSettingsContext(){return(0,o.useContext)(c)};var o=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==i(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var l=a?Object.getOwnPropertyDescriptor(e,o):null;l&&(l.get||l.set)?Object.defineProperty(n,o,l):n[o]=e[o]}n.default=e,r&&r.set(e,n);return n}(r(87363)),l=a(r(93231)),u=a(r(40131));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}function ownKeys(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(r),!0).forEach((function(t){(0,l.default)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ownKeys(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var c=(0,o.createContext)({});function SettingsProvider(e){var t=(0,o.useState)({}),r=(0,u.default)(t,2),n=r[0],a=r[1],i=(0,o.useCallback)((function(e){a((function(t){return _objectSpread(_objectSpread({},t),e)}))}),[a]);return(0,o.useEffect)((function(){a(e.value)}),[a]),o.default.createElement(c.Provider,{value:{settings:n,setSettings:a,updateSettings:i}},e.children)}SettingsProvider.propTypes={children:n.any,value:n.object.isRequired}},24637:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function useAddKitPromotionUTM(e,t,r){if(!e)return"";var n;try{n=new URL(e)}catch(e){return""}if(r&&"string"==typeof r){var a=r.trim().replace(/\s+/g,"-").replace(/[^\w-]/g,"").toLowerCase();n.searchParams.set("utm_term",a)}t&&"string"==typeof t&&n.searchParams.set("utm_content",t);return n.toString()}},271:(e,t,r)=>{"use strict";var n=r(38003).__,a=r(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.KEY=void 0,t.default=function useContentTypes(){var e=(0,l.useSettingsContext)().settings;return(0,o.useQuery)([c,e],(function(){return function fetchContentTypes(e){var t=[{id:"page",label:n("Pages","elementor"),doc_types:["wp-page"],order:0},{id:"site-parts",label:n("Site Parts","elementor"),doc_types:["archive","error-404","footer","header","search-results","single-page","single-post","product","product-archive","404","single"],order:1}],r=e.access_tier,a=e.is_pro&&e.is_library_connected;a&&r===u.TIERS.free&&(r=u.TIERS["essential-oct2023"]);var o=u.TIERS["essential-oct2023"];(0,u.isTierAtLeast)(r,o)&&t.push({id:"popup",label:n("Popups","elementor"),doc_types:["popup"],order:2});return Promise.resolve(t).then((function(e){return e.map((function(e){return i.default.createFromResponse(e)}))}))}(e)}))};var i=a(r(79262)),o=r(56552),l=r(28405),u=r(56827),c="content-types";t.KEY=c},70118:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function useDebouncedCallback(e,t){var r=(0,n.useRef)();return(0,n.useCallback)((function(){for(var n=arguments.length,a=new Array(n),i=0;i<n;i++)a[i]=arguments[i];clearTimeout(r.current),r.current=setTimeout((function later(){clearTimeout(r.current),e.apply(void 0,a)}),t)}),[e,t])};var n=r(87363)},2921:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function useDownloadLinkMutation(e,t){var r=t.onError,i=t.onSuccess,o=(0,n.useCallback)((function(){return $e.data.get("kits/download-link",{id:e.id},{refresh:!0})}),[e]);return(0,a.useMutation)(o,{onSuccess:i,onError:r})};var n=r(87363),a=r(56552)},30642:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.TYPE_PROMOTION=t.TYPE_CONNECT=t.TYPE_APPLY=void 0,t.default=function useKitCallToAction(e){var t,r=(0,a.useSettingsContext)().settings,c=r.access_tier;r.is_pro&&r.is_library_connected&&c===i.TIERS.free&&(c=i.TIERS["essential-oct2023"]);var s=(0,n.useMemo)((function(){var t;return null===(t=r.subscription_plans)||void 0===t?void 0:t[e]}),[r,e]),d=c===i.TIERS.free?null===(t=r.subscription_plans)||void 0===t?void 0:t.essential.label:null==s?void 0:s.label;return[(0,n.useMemo)((function(){var t=(0,i.isTierAtLeast)(c,e);return r.is_library_connected||!r.is_pro&&!t?t?u:l:o}),[r,e]),{subscriptionPlan:s,badgeLabel:d}]};var n=r(87363),a=r(28405),i=r(56827),o="connect";t.TYPE_CONNECT=o;var l="promotion";t.TYPE_PROMOTION=l;var u="apply";t.TYPE_APPLY=u},80843:(e,t,r)=>{"use strict";var n=r(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function useKitDocumentByType(e){var t=(0,i.default)(),r=(0,o.useMemo)((function(){return e&&t.data?e.getDocumentsByTypes(t.data).sort((function(e,t){return e.order-t.order})):[]}),[e,t.data]);return _objectSpread(_objectSpread({},t),{},{data:r})};var a=n(r(93231)),i=n(r(271)),o=r(87363);function ownKeys(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(r),!0).forEach((function(t){(0,a.default)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ownKeys(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}},69187:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.useKitFavoritesMutations=function useKitFavoritesMutations(){var e=(0,o.useQueryClient)(),t=(0,n.useCallback)((function(t){var r=t.data,n=r.data.id,o=r.data.is_favorite;e.getQueryData([a.KEY])&&e.setQueryData([a.KEY],(function(e){return e?e.map((function(e){return e.id===n?(e.isFavorite=o,e.clone()):e})):e})),e.getQueryData([i.KEY,n])&&e.setQueryData([i.KEY,n],(function(e){return e.isFavorite=o,e.clone()}))}),[e]),r=(0,o.useMutation)((function(e){return $e.data.create("kits/favorites",{},{id:e})}),{onSuccess:t}),l=(0,o.useMutation)((function(e){return $e.data.delete("kits/favorites",{id:e})}),{onSuccess:t});return{addToFavorites:r,removeFromFavorites:l,isLoading:r.isLoading||l.isLoading}};var n=r(87363),a=r(37790),i=r(39279),o=r(56552)},39279:(e,t,r)=>{"use strict";var n=r(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.KEY=void 0,t.default=function useKit(e){var t=function usePlaceholderDataCallback(e){var t=(0,u.useQueryClient)();return(0,l.useCallback)((function(){var r,n=null===(r=t.getQueryData(o.KEY))||void 0===r?void 0:r.find((function(t){return t.id===e}));if(n)return n}),[t,e])}(e);return(0,u.useQuery)([c,e],fetchKitItem,{placeholderData:t})};var a=n(r(40131)),i=n(r(41773)),o=r(37790),l=r(87363),u=r(56552),c="kit";function fetchKitItem(e){var t=(0,a.default)(e.queryKey,2),r=(t[0],t[1]);return $e.data.get("kits/index",{id:r},{refresh:!0}).then((function(e){return e.data})).then((function(e){var t=e.data;return i.default.createFromResponse(t)}))}t.KEY=c},37790:(e,t,r)=>{"use strict";var n=r(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.KEY=void 0,t.default=function useKits(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=(0,d.useState)(!1),r=(0,a.default)(t,2),n=r[0],o=r[1],c=(0,d.useState)((function(){return _objectSpread(_objectSpread({ready:!1},p),e)})),v=(0,a.default)(c,2),y=v[0],g=v[1],_=(0,d.useCallback)((function(){return o(!0)}),[o]),b=(0,d.useCallback)((function(){return g(_objectSpread(_objectSpread({ready:!0},p),e))}),[g]),h=(0,s.useQuery)([f],(function(){return function fetchKits(e){return $e.data.get("kits/index",{force:e?1:void 0},{refresh:!0}).then((function(e){return e.data})).then((function(e){return e.data.map((function(e){return l.default.createFromResponse(e)}))}))}(n)})),k=(0,d.useMemo)((function(){return h.data?pipe.apply(void 0,(0,i.default)(Object.values(m)))((0,i.default)(h.data),y):[]}),[h.data,y]),O=(0,u.default)(y.taxonomies),C=(0,d.useMemo)((function(){return!!y.search||!!O.length}),[y]);return(0,d.useEffect)((function(){n&&h.refetch().then((function(){return o(!1)}))}),[n]),_objectSpread(_objectSpread({},h),{},{data:k,queryParams:y,setQueryParams:g,clearQueryParams:b,forceRefetch:_,isFilterActive:C})},t.defaultQueryParams=void 0;var a=n(r(40131)),i=n(r(9833)),o=n(r(93231)),l=n(r(41773)),u=n(r(94050)),c=r(83080),s=r(56552),d=r(87363);function ownKeys(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(r),!0).forEach((function(t){(0,o.default)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ownKeys(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var f="kits";t.KEY=f;var p={favorite:!1,search:"",taxonomies:c.taxonomyType.reduce((function(e,t){var r=t.key;return _objectSpread(_objectSpread({},e),{},(0,o.default)({},r,[]))}),{}),order:{direction:"asc",by:"featuredIndex"},referrer:null};t.defaultQueryParams=p;var m={favoriteFilter:function favoriteFilter(e,t){return t.favorite?e.filter((function(e){return e.isFavorite})):e},searchFilter:function searchFilter(e,t){return t.search?e.filter((function(e){var r=[].concat((0,i.default)(e.keywords),(0,i.default)(e.taxonomies),[e.title]),n=t.search.toLowerCase();return r.some((function(e){return e.toLowerCase().includes(n)}))})):e},taxonomiesFilter:function taxonomiesFilter(e,t){return Object.values(t.taxonomies).filter((function(e){return e.length})).reduce((function(e,t){return e.filter((function(e){return t.some((function(t){return e.taxonomies.some((function(e){return t===e}))}))}))}),e)},sort:function sort(e,t){var r=t.order;return e.sort((function(e,t){return"asc"===r.direction?e[r.by]-t[r.by]:t[r.by]-e[r.by]}))}};function pipe(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return function(e){for(var r=arguments.length,n=new Array(r>1?r-1:0),a=1;a<r;a++)n[a-1]=arguments[a];return t.reduce((function(e,t){return t.apply(void 0,[e].concat(n))}),e)}}},94050:(e,t,r)=>{"use strict";var n=r(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function useSelectedTaxonomies(e){return(0,i.useMemo)((function(){return Object.values(e).reduce((function(e,t){return[].concat((0,a.default)(e),(0,a.default)(t))}))}),[e])};var a=n(r(9833)),i=r(87363)},68109:(e,t,r)=>{"use strict";var n=r(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.KEY=void 0,t.default=function useTaxonomies(){var e=(0,u.useState)(!1),t=(0,i.default)(e,2),r=t[0],n=t[1],a=(0,u.useCallback)((function(){return n(!0)}),[n]),s=(0,l.useQuery)([c],(function(){return function fetchTaxonomies(e){return $e.data.get("kit-taxonomies/index",{force:e?1:void 0},{refresh:!0}).then((function(e){return e.data})).then((function(e){return e.data.map((function(e){return o.default.createFromResponse(e)}))}))}(r)}));return(0,u.useEffect)((function(){r&&s.refetch().then((function(){return n(!1)}))}),[r]),_objectSpread(_objectSpread({},s),{},{forceRefetch:a})};var a=n(r(93231)),i=n(r(40131)),o=n(r(83080)),l=r(56552),u=r(87363);function ownKeys(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(r),!0).forEach((function(t){(0,a.default)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ownKeys(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var c="tags";t.KEY=c},58139:(e,t,r)=>{"use strict";var n=r(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=n(r(40131)),i=n(r(78983)),o=n(r(42081)),l=function(){function BaseModel(){(0,i.default)(this,BaseModel)}return(0,o.default)(BaseModel,[{key:"clone",value:function clone(){var e=this,t=new this.constructor;return Object.keys(this).forEach((function(r){t[r]=e[r]})),t}},{key:"init",value:function init(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object.entries(t).forEach((function(t){var r=(0,a.default)(t,2),n=r[0],i=r[1];e[n]=i})),this}}]),BaseModel}();t.default=l},79262:(e,t,r)=>{"use strict";var n=r(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=n(r(78983)),i=n(r(42081)),o=n(r(77266)),l=n(r(58724)),u=n(r(71173)),c=n(r(74910)),s=n(r(93231));function _createSuper(e){var t=function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function _createSuperInternal(){var r,n=(0,c.default)(e);if(t){var a=(0,c.default)(this).constructor;r=Reflect.construct(n,arguments,a)}else r=n.apply(this,arguments);return(0,u.default)(this,r)}}var d=function(e){(0,l.default)(ContentType,e);var t=_createSuper(ContentType);function ContentType(){var e;(0,a.default)(this,ContentType);for(var r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];return e=t.call.apply(t,[this].concat(n)),(0,s.default)((0,o.default)(e),"id",""),(0,s.default)((0,o.default)(e),"label",""),(0,s.default)((0,o.default)(e),"documentTypes",[]),(0,s.default)((0,o.default)(e),"documents",[]),(0,s.default)((0,o.default)(e),"order",0),e}return(0,i.default)(ContentType,null,[{key:"createFromResponse",value:function createFromResponse(e){return(new ContentType).init({id:e.id,label:e.label,documentTypes:e.doc_types,order:e.order,documents:[]})}}]),ContentType}(n(r(58139)).default);t.default=d},6325:(e,t,r)=>{"use strict";var n=r(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=n(r(78983)),i=n(r(42081)),o=n(r(77266)),l=n(r(58724)),u=n(r(71173)),c=n(r(74910)),s=n(r(93231));function _createSuper(e){var t=function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function _createSuperInternal(){var r,n=(0,c.default)(e);if(t){var a=(0,c.default)(this).constructor;r=Reflect.construct(n,arguments,a)}else r=n.apply(this,arguments);return(0,u.default)(this,r)}}var d=function(e){(0,l.default)(Document,e);var t=_createSuper(Document);function Document(){var e;(0,a.default)(this,Document);for(var r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];return e=t.call.apply(t,[this].concat(n)),(0,s.default)((0,o.default)(e),"id",""),(0,s.default)((0,o.default)(e),"title",""),(0,s.default)((0,o.default)(e),"documentType",""),(0,s.default)((0,o.default)(e),"thumbnailUrl",""),(0,s.default)((0,o.default)(e),"previewUrl",""),e}return(0,i.default)(Document,null,[{key:"createFromResponse",value:function createFromResponse(e){return(new Document).init({id:e.id,title:e.title,documentType:e.doc_type,thumbnailUrl:e.thumbnail_url,previewUrl:e.preview_url})}}]),Document}(n(r(58139)).default);t.default=d},41773:(e,t,r)=>{"use strict";var n=r(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=n(r(78983)),i=n(r(42081)),o=n(r(77266)),l=n(r(58724)),u=n(r(71173)),c=n(r(74910)),s=n(r(93231)),d=n(r(58139)),f=n(r(6325));function _createSuper(e){var t=function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function _createSuperInternal(){var r,n=(0,c.default)(e);if(t){var a=(0,c.default)(this).constructor;r=Reflect.construct(n,arguments,a)}else r=n.apply(this,arguments);return(0,u.default)(this,r)}}var p=function(e){(0,l.default)(Kit,e);var t=_createSuper(Kit);function Kit(){var e;(0,a.default)(this,Kit);for(var r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];return e=t.call.apply(t,[this].concat(n)),(0,s.default)((0,o.default)(e),"id",""),(0,s.default)((0,o.default)(e),"title",""),(0,s.default)((0,o.default)(e),"description",""),(0,s.default)((0,o.default)(e),"isFavorite",!1),(0,s.default)((0,o.default)(e),"thumbnailUrl",null),(0,s.default)((0,o.default)(e),"previewUrl",""),(0,s.default)((0,o.default)(e),"accessLevel",0),(0,s.default)((0,o.default)(e),"trendIndex",null),(0,s.default)((0,o.default)(e),"popularityIndex",null),(0,s.default)((0,o.default)(e),"featuredIndex",null),(0,s.default)((0,o.default)(e),"createdAt",null),(0,s.default)((0,o.default)(e),"updatedAt",null),(0,s.default)((0,o.default)(e),"keywords",[]),(0,s.default)((0,o.default)(e),"taxonomies",[]),(0,s.default)((0,o.default)(e),"documents",[]),e}return(0,i.default)(Kit,[{key:"getDocumentsByTypes",value:function getDocumentsByTypes(e){var t=this;return e.map((function(e){return(e=e.clone()).documents=t.documents.filter((function(t){return e.documentTypes.includes(t.documentType)})),e}))}}],[{key:"createFromResponse",value:function createFromResponse(e){return(new Kit).init({id:e.id,title:e.title,description:e.description,isFavorite:e.is_favorite,thumbnailUrl:e.thumbnail_url,previewUrl:e.preview_url,accessLevel:e.access_level,accessTier:e.access_tier,trendIndex:e.trend_index,popularityIndex:e.popularity_index,featuredIndex:e.featured_index,createdAt:e.created_at?new Date(e.created_at):null,updatedAt:e.updated_at?new Date(e.updated_at):null,keywords:e.keywords,taxonomies:e.taxonomies,documents:e.documents?e.documents.map((function(e){return f.default.createFromResponse(e)})):[]})}}]),Kit}(d.default);t.default=p},83080:(e,t,r)=>{"use strict";var n=r(38003).__,a=r(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.taxonomyType=t.default=void 0;var i=a(r(78983)),o=a(r(42081)),l=a(r(77266)),u=a(r(58724)),c=a(r(71173)),s=a(r(74910)),d=a(r(93231)),f=a(r(58139));function _createSuper(e){var t=function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function _createSuperInternal(){var r,n=(0,s.default)(e);if(t){var a=(0,s.default)(this).constructor;r=Reflect.construct(n,arguments,a)}else r=n.apply(this,arguments);return(0,c.default)(this,r)}}var p=[{key:"categories",label:n("Categories","elementor"),isOpenByDefault:!0},{key:"tags",label:n("Tags","elementor")},{key:"features",label:n("Features","elementor")}];t.taxonomyType=p,elementorAppConfig.hasPro||p.push({key:"subscription_plans",label:n("Kits by plan","elementor")});var m=function(e){(0,u.default)(Taxonomy,e);var t=_createSuper(Taxonomy);function Taxonomy(){var e;(0,i.default)(this,Taxonomy);for(var r=arguments.length,n=new Array(r),a=0;a<r;a++)n[a]=arguments[a];return e=t.call.apply(t,[this].concat(n)),(0,d.default)((0,l.default)(e),"text",""),(0,d.default)((0,l.default)(e),"type","tag"),e}return(0,o.default)(Taxonomy,null,[{key:"createFromResponse",value:function createFromResponse(e){return(new Taxonomy).init({text:e.text,type:e.type})}}]),Taxonomy}(f.default);t.default=m},56005:(e,t,r)=>{"use strict";var n=r(38003).__,a=r(23615),i=r(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=Favorites;var o=i(r(87363)),l=i(r(98931)),u=i(r(68316)),c=r(50927);function Favorites(e){var t=(0,c.useNavigate)(),r=o.default.createElement(u.default,{title:n("No favorites here yet...","elementor"),description:n("Use the heart icon to save kits that inspire you. You'll be able to find them here.","elementor"),button:{text:n("Continue browsing.","elementor"),action:function action(){return t("/kit-library")}}});return o.default.createElement(l.default,{path:e.path,initialQueryParams:{favorite:!0},renderNoResultsComponent:function renderNoResultsComponent(e){var t=e.defaultComponent;return e.isFilterActive?t:r}})}Favorites.propTypes={path:a.string}},93568:(e,t,r)=>{"use strict";var n=r(38003).__,a=r(23615),i=r(73203),o=r(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.default=IndexHeader;var l=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==o(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var l=a?Object.getOwnPropertyDescriptor(e,i):null;l&&(l.get||l.set)?Object.defineProperty(n,i,l):n[i]=e[i]}n.default=e,r&&r.set(e,n);return n}(r(87363)),u=i(r(40131)),c=i(r(57332)),s=r(36779),d=r(50927),f=i(r(15368)),p=r(92602);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}function IndexHeader(e){var t,r,a=(0,d.useNavigate)(),i=(0,l.useState)(!1),o=(0,u.default)(i,2),m=o[0],v=o[1],y=(0,l.useRef)(),g=function eventTracking(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"click",n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;(0,p.appsEventTrackingDispatch)(e,{element:t,event_type:r,page_source:"home page",element_position:"app_header",modal_type:n})},_=elementorAppConfig.user.is_administrator||null!==(t=null===(r=elementorAppConfig.user.restrictions)||void 0===r?void 0:r.includes("json-upload"))&&void 0!==t&&t,b=(0,l.useMemo)((function(){return[{id:"info",text:n("Info","elementor"),hideText:!0,icon:"eicon-info-circle-o",onClick:function onClick(){g("kit-library/seek-more-info"),v(!0)}},{id:"refetch",text:n("Refetch","elementor"),hideText:!0,icon:"eicon-sync ".concat(e.isFetching?"eicon-animation-spin":""),onClick:function onClick(){g("kit-library/refetch"),e.refetch()}},_&&{id:"import",text:n("Import","elementor"),hideText:!0,icon:"eicon-upload-circle-o",elRef:y,onClick:function onClick(){g("kit-library/kit-import"),a("/import?referrer=kit-library")}}]}),[e.isFetching,e.refetch,_]);return l.default.createElement(l.default.Fragment,null,l.default.createElement(c.default,{buttons:b}),l.default.createElement(f.default,{targetRef:y,wrapperClass:"e-kit-library__tooltip"},n("Import Kit","elementor")),l.default.createElement(s.ModalProvider,{title:n("Welcome to the Library","elementor"),show:m,setShow:v,onOpen:function onOpen(){return g("kit-library/modal-open",null,"load","info")},onClose:function onClose(e){return function onClose(e){var t=e.target.classList.contains("eps-modal__overlay")?"overlay":"x";g("kit-library/modal-close",t,null,"info")}(e)}},l.default.createElement("div",{className:"e-kit-library-header-info-modal-container"},l.default.createElement(s.Heading,{tag:"h3",variant:"h3"},n("What's a Website Kit?","elementor")),l.default.createElement(s.Text,null,n("A Website Kit is full, ready-made design that you can apply to your site. It includes all the pages, parts, settings and content that you'd expect in a fully functional website.","elementor"))),l.default.createElement("div",{className:"e-kit-library-header-info-modal-container"},l.default.createElement(s.Heading,{tag:"h3",variant:"h3"},n("What's going on in the Kit Library?","elementor")),l.default.createElement(s.Text,null,n("Search & filter for kits by category and tags, or browse through individual kits to see what's inside.","elementor"),l.default.createElement("br",null),n("Once you've picked a winner, apply it to your site!","elementor"))),l.default.createElement("div",null,l.default.createElement(s.Heading,{tag:"h3",variant:"h3"},n("Happy browsing!","elementor")),l.default.createElement(s.Text,null,l.default.createElement(s.Button,{url:"https://go.elementor.com/app-kit-library-how-to-use-kits/",target:"_blank",rel:"noreferrer",text:n("Learn more","elementor"),color:"link",onClick:function onClick(){g("kit-library/seek-more-info","text link",null,"info")}})," ",n("about using templates","elementor")))))}r(3740),IndexHeader.propTypes={refetch:a.func.isRequired,isFetching:a.bool}},16876:(e,t,r)=>{"use strict";var n=r(23615),a=r(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=IndexSidebar;var i=a(r(87363)),o=r(36779),l=r(92602);function IndexSidebar(e){return i.default.createElement(i.default.Fragment,null,e.menuItems.map((function(e){return i.default.createElement(o.MenuItem,{key:e.label,text:e.label,className:"eps-menu-item__link ".concat(e.isActive?"eps-menu-item--active":""),icon:e.icon,url:e.url,onClick:function onClick(){return function eventTracking(e,t,r){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"click";return(0,l.appsEventTrackingDispatch)(e,{category:t,source:r,element_location:"app_sidebar",event_type:n})}(e.trackEventData.command,e.trackEventData.category,"home page")}})})),e.tagsFilterSlot)}IndexSidebar.propTypes={tagsFilterSlot:n.node,menuItems:n.arrayOf(n.shape({label:n.string,icon:n.string,isActive:n.bool,url:n.string}))}},98931:(e,t,r)=>{"use strict";var n=r(38003).__,a=r(23615),i=r(73203),o=r(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.default=Index;var l=_interopRequireWildcard(r(87363)),u=i(r(9833)),c=i(r(40131)),s=i(r(93231)),d=i(r(88138)),f=i(r(46109)),p=i(r(68316)),m=i(r(40133)),v=i(r(93568)),y=i(r(16876)),g=i(r(53772)),_=i(r(6115)),b=i(r(8589)),h=i(r(266)),k=i(r(20638)),O=i(r(1572)),C=_interopRequireWildcard(r(37790)),w=i(r(78845)),P=i(r(68109)),E=r(36779),j=r(55040),T=r(50927),x=r(92602);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==o(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var l=a?Object.getOwnPropertyDescriptor(e,i):null;l&&(l.get||l.set)?Object.defineProperty(n,i,l):n[i]=e[i]}return n.default=e,r&&r.set(e,n),n}function ownKeys(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(r),!0).forEach((function(t){(0,s.default)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ownKeys(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Index(e){(0,w.default)({title:n("Kit Library","elementor")});var t=function useMenuItems(e){return(0,l.useMemo)((function(){var t=e.replace("/","");return[{label:n("All Website Kits","elementor"),icon:"eicon-filter",isActive:!t,url:"/kit-library",trackEventData:{command:"kit-library/select-organizing-category",category:"all"}},{label:n("Favorites","elementor"),icon:"eicon-heart-o",isActive:"favorites"===t,url:"/kit-library/favorites",trackEventData:{command:"kit-library/select-organizing-category",category:"favorites"}}]}),[e])}(e.path),r=(0,C.default)(e.initialQueryParams),a=r.data,i=r.isSuccess,o=r.isLoading,R=r.isFetching,S=r.isError,M=r.queryParams,D=r.setQueryParams,W=r.clearQueryParams,q=r.forceRefetch,N=r.isFilterActive;!function useRouterQueryParams(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],n=(0,T.useLocation)(),a=(0,j.useLastFilterContext)().setLastFilter;(0,l.useEffect)((function(){var t=Object.fromEntries(Object.entries(e).filter((function(e){var t=(0,c.default)(e,2),n=t[0],a=t[1];return!r.includes(n)&&a})));a(t),history.replaceState(null,"",decodeURI("#".concat(wp.url.addQueryArgs(n.pathname.split("?")[0]||"/",t))))}),[e]),(0,l.useEffect)((function(){var e=Object.keys(C.defaultQueryParams).reduce((function(e,t){var r=wp.url.getQueryArg(n.pathname,t);return r?_objectSpread(_objectSpread({},e),{},(0,s.default)({},t,r)):e}),{});t((function(t){return _objectSpread(_objectSpread(_objectSpread({},t),e),{},{taxonomies:_objectSpread(_objectSpread({},t.taxonomies),e.taxonomies),ready:!0})}))}),[])}(M,D,["ready"].concat((0,u.default)(Object.keys(e.initialQueryParams))));var I=(0,P.default)(),B=I.data,L=I.forceRefetch,F=I.isFetching,K=function useTaxonomiesSelection(e){return[(0,l.useCallback)((function(t,r){return e((function(e){var n=_objectSpread({},e.taxonomies);return n[t]=r(e.taxonomies[t]),_objectSpread(_objectSpread({},e),{},{taxonomies:n})}))}),[e]),(0,l.useCallback)((function(t){return e((function(e){var r=Object.entries(e.taxonomies).reduce((function(e,r){var n=(0,c.default)(r,2),a=n[0],i=n[1];return _objectSpread(_objectSpread({},e),{},(0,s.default)({},a,i.filter((function(e){return e!==t}))))}),{});return _objectSpread(_objectSpread({},e),{},{taxonomies:r})}))}),[e])]}(D),A=(0,c.default)(K,2),U=A[0],H=A[1],Q=function eventTracking(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,i=arguments.length>5&&void 0!==arguments[5]?arguments[5]:null,o=arguments.length>6&&void 0!==arguments[6]?arguments[6]:"click";(0,x.appsEventTrackingDispatch)(e,{page_source:"home page",element_position:t,search_term:r,sort_direction:n,sort_type:a,event_type:o,action:i})};return l.default.createElement(_.default,{sidebar:l.default.createElement(y.default,{tagsFilterSlot:l.default.createElement(O.default,{selected:M.taxonomies,onSelect:U,taxonomies:B,category:e.path}),menuItems:t}),header:l.default.createElement(v.default,{refetch:function refetch(){q(),L()},isFetching:R||F})},l.default.createElement("div",{className:"e-kit-library__index-layout-container"},l.default.createElement(E.Grid,{container:!0,className:"e-kit-library__index-layout-heading"},l.default.createElement(E.Grid,{item:!0,className:"e-kit-library__index-layout-heading-search"},l.default.createElement(h.default,{placeholder:n("Search all Website Kits...","elementor"),value:M.search,onChange:function onChange(e){D((function(t){return _objectSpread(_objectSpread({},t),{},{search:e})})),Q("kit-library/kit-free-search","top_area_search",e,null,null,null,"search")}}),N&&l.default.createElement(m.default,{queryParams:M,resultCount:a.length||0,onClear:W,onRemoveTag:H})),l.default.createElement(E.Grid,{item:!0,className:"e-kit-library__index-layout-heading-sort"},l.default.createElement(k.default,{options:[{label:n("Featured","elementor"),value:"featuredIndex",defaultOrder:"asc",orderDisabled:!0},{label:n("New","elementor"),value:"createdAt",defaultOrder:"desc"},{label:n("Popular","elementor"),value:"popularityIndex",defaultOrder:"desc"},{label:n("Trending","elementor"),value:"trendIndex",defaultOrder:"desc"}],value:M.order,onChange:function onChange(e){return D((function(t){return _objectSpread(_objectSpread({},t),{},{order:e})}))},onChangeSortDirection:function onChangeSortDirection(e){return Q("kit-library/change-sort-direction","top_area_sort",null,e)},onChangeSortValue:function onChangeSortValue(e){return Q("kit-library/change-sort-value","top_area_sort",null,null,e)},onSortSelectOpen:function onSortSelectOpen(){return Q("kit-library/change-sort-type","top_area_sort",null,null,null,"expand")}}))),l.default.createElement(d.default,{className:"e-kit-library__index-layout-main"},l.default.createElement(l.default.Fragment,null,o&&l.default.createElement(b.default,null),S&&l.default.createElement(p.default,{title:n("Something went wrong.","elementor"),description:n("Nothing to worry about, use 🔄 on the top corner to try again. If the problem continues, head over to the Help Center.","elementor"),button:{text:n("Learn More","elementor"),url:"https://go.elementor.com/app-kit-library-error/",target:"_blank"}}),i&&0<a.length&&M.ready&&l.default.createElement(g.default,{data:a,queryParams:M,source:e.path}),i&&0===a.length&&M.ready&&e.renderNoResultsComponent({defaultComponent:l.default.createElement(p.default,{title:n("No results matched your search.","elementor"),description:n("Try different keywords or ","elementor"),button:{text:n("Continue browsing.","elementor"),action:W,category:e.path}}),isFilterActive:N}),l.default.createElement(f.default,{category:e.path})))))}r(23474),Index.propTypes={path:a.string,initialQueryParams:a.object,renderNoResultsComponent:a.func},Index.defaultProps={initialQueryParams:{},renderNoResultsComponent:function renderNoResultsComponent(e){return e.defaultComponent}}},79141:(e,t,r)=>{"use strict";var n=r(38003).__,a=r(23615),i=r(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=OverviewContentGroupItem;var o=i(r(87363)),l=i(r(6325)),u=r(36779),c=r(92602);function OverviewContentGroupItem(e){return o.default.createElement(u.Card,null,o.default.createElement(u.CardHeader,null,o.default.createElement(u.Heading,{tag:"h3",title:e.document.title,variant:"h5",className:"eps-card__headline"},e.document.title)),o.default.createElement(u.CardBody,null,o.default.createElement(u.CardImage,{alt:e.document.title,src:e.document.thumbnailUrl||""},e.document.previewUrl&&o.default.createElement(u.CardOverlay,null,o.default.createElement(u.Button,{className:"e-kit-library__kit-item-overlay-overview-button",text:n("View Demo","elementor"),icon:"eicon-preview-medium",url:"/kit-library/preview/".concat(e.kitId,"?document_id=").concat(e.document.id),onClick:function onClick(){return function eventTracking(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"click";(0,c.appsEventTrackingDispatch)(t,{kit_name:e.kitTitle,document_type:e.groupData.id,document_name:"".concat(e.groupData.label,"-").concat(e.document.title),page_source:"overview",element_position:"content_overview",event_type:r})}("kit-library/view-demo-part")}})))))}OverviewContentGroupItem.propTypes={document:a.instanceOf(l.default).isRequired,kitId:a.string.isRequired,kitTitle:a.string.isRequired,groupData:a.shape({label:a.string,id:a.string}).isRequired}},40448:(e,t,r)=>{"use strict";var n=r(23615),a=r(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=OverviewContentGroup;var i=a(r(87363)),o=a(r(79262)),l=a(r(79141)),u=r(36779);function OverviewContentGroup(e){var t,r;return(null===(t=e.contentType)||void 0===t||null===(r=t.documents)||void 0===r?void 0:r.length)<=0?"":i.default.createElement("div",{className:"e-kit-library__content-overview-group-item"},i.default.createElement(u.Heading,{tag:"h3",variant:"h3",className:"e-kit-library__content-overview-group-title"},e.contentType.label),i.default.createElement(u.CssGrid,{spacing:24,colMinWidth:250},e.contentType.documents.map((function(t){return i.default.createElement(l.default,{key:t.id,document:t,kitId:e.kitId,kitTitle:e.kitTitle,groupData:e.contentType})}))))}OverviewContentGroup.propTypes={contentType:n.instanceOf(o.default),kitId:n.string.isRequired,kitTitle:n.string.isRequired}},86969:(e,t,r)=>{"use strict";var n=r(38003).__,a=r(23615),i=r(73203),o=r(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.default=OverviewSidebar;var l=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==o(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var l=a?Object.getOwnPropertyDescriptor(e,i):null;l&&(l.get||l.set)?Object.defineProperty(n,i,l):n[i]=e[i]}n.default=e,r&&r.set(e,n);return n}(r(87363)),u=i(r(40131)),c=i(r(67405)),s=i(r(79262)),d=i(r(55499)),f=i(r(41773)),p=r(36779),m=r(92602);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}function OverviewSidebar(e){var t,r=(0,l.useState)(!0),a=(0,u.default)(r,2),i=a[0],o=a[1];return l.default.createElement("div",{className:"e-kit-library__item-sidebar"},l.default.createElement("div",{className:"e-kit-library__item-sidebar-header"},l.default.createElement(p.Heading,{tag:"h1",variant:"h5",className:"e-kit-library__item-sidebar-header-title"},e.model.title),l.default.createElement(d.default,{isFavorite:e.model.isFavorite,id:e.model.id})),l.default.createElement(p.CardImage,{className:"e-kit-library__item-sidebar-thumbnail",alt:e.model.title,src:e.model.thumbnailUrl||""}),l.default.createElement(p.Text,{className:"e-kit-library__item-sidebar-description"},e.model.description||""),(null===(t=e.groupedKitContent)||void 0===t?void 0:t.length)>0&&e.model.documents.length>0&&l.default.createElement(c.default,{isOpen:i,onChange:o,title:n("WHAT'S INSIDE","elementor"),className:"e-kit-library__item-sidebar-collapse-info",onClick:function onClick(e,t){!function eventTracking(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,i=arguments.length>5&&void 0!==arguments[5]?arguments[5]:"click";"boolean"==typeof a&&(e="kit-library/".concat(a&&a?"collapse":"expand")),(0,m.appsEventTrackingDispatch)(e,{page_source:"overview",element_location:"app_sidebar",kit_name:r,tag:n,section:t,event_type:i})}(null,t,null,null,e)}},e.groupedKitContent.map((function(e){return e.documents<=0?"":l.default.createElement(p.Text,{className:"e-kit-library__item-information-text",key:e.id},e.documents.length," ",e.label)}))))}r(37126),OverviewSidebar.propTypes={model:a.instanceOf(f.default).isRequired,index:a.number,groupedKitContent:a.arrayOf(a.instanceOf(s.default))}},37971:(e,t,r)=>{"use strict";var n=r(38003).__,a=r(23615),i=r(73203),o=r(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.default=Overview;var l=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==o(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var l=a?Object.getOwnPropertyDescriptor(e,i):null;l&&(l.get||l.set)?Object.defineProperty(n,i,l):n[i]=e[i]}n.default=e,r&&r.set(e,n);return n}(r(87363)),u=i(r(88138)),c=i(r(87206)),s=i(r(28938)),d=i(r(6115)),f=i(r(40448)),p=i(r(86969)),m=i(r(39279)),v=i(r(80843)),y=i(r(78845)),g=r(50927),_=r(92602);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}function Overview(e){var t=(0,m.default)(e.id),r=t.data,a=t.isError,i=t.isLoading,o=(0,v.default)(r).data,b=function useHeaderButtons(e,t){var r=(0,g.useNavigate)();return(0,l.useMemo)((function(){return[{id:"view-demo",text:n("View Demo","elementor"),hideText:!1,variant:"outlined",color:"secondary",size:"sm",onClick:function onClick(){(0,_.appsEventTrackingDispatch)("kit-library/view-demo-page",{kit_name:t,page_source:"overview",element_position:"app_header",view_type_clicked:"demo"}),r("/kit-library/preview/".concat(e))},includeHeaderBtnClass:!1}]}),[e])}(e.id,r&&r.title);if((0,y.default)({title:r?"".concat(n("Kit Library","elementor")," | ").concat(r.title):n("Loading...","elementor")}),a)throw new Error;return i?l.default.createElement(c.default,null):l.default.createElement(d.default,{header:l.default.createElement(s.default,{model:r,buttons:b,pageId:"overview"}),sidebar:l.default.createElement(p.default,{model:r,groupedKitContent:o})},o.length>0&&l.default.createElement(u.default,null,o.map((function(t){return l.default.createElement(f.default,{key:t.id,contentType:t,kitId:e.id,kitTitle:r.title})}))))}r(53964),Overview.propTypes={id:a.string}},24067:(e,t,r)=>{"use strict";var n=r(23615),a=r(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.PreviewIframe=PreviewIframe;var i=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==a(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var l=i?Object.getOwnPropertyDescriptor(e,o):null;l&&(l.get||l.set)?Object.defineProperty(n,o,l):n[o]=e[o]}n.default=e,r&&r.set(e,n);return n}(r(87363)),o=r(36779);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}function PreviewIframe(e){var t=(0,i.useRef)();return(0,i.useEffect)((function(){if(t.current){var r=function listener(){return e.onLoaded()};return t.current.addEventListener("load",r),function(){return t.current&&t.current.removeEventListener("load",r)}}}),[t.current,e.previewUrl]),i.default.createElement(o.Grid,{container:!0,justify:"center",className:"e-kit-library__preview-iframe-container"},i.default.createElement("iframe",{className:"e-kit-library__preview-iframe",src:e.previewUrl,style:e.style,ref:t}))}PreviewIframe.propTypes={previewUrl:n.string.isRequired,style:n.object,onLoaded:n.func},PreviewIframe.defaultProps={style:{width:"100%",height:"100%"},onLoaded:function onLoaded(){}}},30769:(e,t,r)=>{"use strict";var n=r(23615),a=r(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=PreviewResponsiveControls;var i=a(r(87363)),o=r(17490),l=r(36779);function PreviewResponsiveControls(e){return i.default.createElement(l.Grid,{container:!0,alignItems:"center",justify:"center",className:"e-kit-library__preview-responsive-controls"},o.breakpoints.map((function(t){var r=t.label,n=t.value,a="e-kit-library__preview-responsive-controls-item";return e.active===n&&(a+=" e-kit-library__preview-responsive-controls-item--active"),i.default.createElement(l.Button,{key:n,text:r,hideText:!0,className:a,icon:"eicon-device-".concat(n),onClick:function onClick(){return e.onChange(n)}})})))}r(84179),PreviewResponsiveControls.propTypes={active:n.string,onChange:n.func.isRequired},PreviewResponsiveControls.defaultProps={active:"desktop"}},17490:(e,t,r)=>{"use strict";var n=r(38003).__,a=r(23615),i=r(73203),o=r(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.breakpoints=void 0,t.default=Preview;var l=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==o(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var l=a?Object.getOwnPropertyDescriptor(e,i):null;l&&(l.get||l.set)?Object.defineProperty(n,i,l):n[i]=e[i]}n.default=e,r&&r.set(e,n);return n}(r(87363)),u=i(r(40131)),c=i(r(87206)),s=i(r(28938)),d=i(r(6115)),f=i(r(8589)),p=i(r(30769)),m=i(r(39279)),v=i(r(78845)),y=r(24067),g=r(50927),_=r(92602);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}r(69097);var b=[{value:"desktop",label:n("Desktop","elementor"),style:{width:"100%",height:"100%"}},{value:"tablet",label:n("Tablet","elementor"),style:{marginBlockStart:"30px",marginBlockEnd:"30px",width:"768px",height:"1024px"}},{value:"mobile",label:n("Mobile","elementor"),style:{marginBlockStart:"30px",marginBlockEnd:"30px",width:"375px",height:"667px"}}];function useHeaderButtons(e,t){var r=(0,g.useNavigate)();return(0,l.useMemo)((function(){return[{id:"overview",text:n("Overview","elementor"),hideText:!1,variant:"outlined",color:"secondary",size:"sm",onClick:function onClick(){!function eventTracking(e,r){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"click";(0,_.appsEventTrackingDispatch)(e,{kit_name:t,element_position:"app_header",page_source:"view demo",view_type_clicked:r,event_type:n})}("kit-library/view-overview-page","overview"),r("/kit-library/overview/".concat(e))},includeHeaderBtnClass:!1}]}),[e])}function Preview(e){var t=(0,m.default)(e.id),r=t.data,a=t.isError,i=t.isLoading,o=(0,l.useState)(!0),h=(0,u.default)(o,2),k=h[0],O=h[1],C=useHeaderButtons(e.id,r&&r.title),w=function usePreviewUrl(e){var t=(0,g.useLocation)();return(0,l.useMemo)((function(){var r,n;if(!e)return null;var a=new URLSearchParams(null===(r=t.pathname.split("?"))||void 0===r?void 0:r[1]).get("document_id"),i="?utm_source=kit-library&utm_medium=wp-dash&utm_campaign=preview",o=e.previewUrl?e.previewUrl+i:e.previewUrl;if(!a)return o;var l=(null===(n=e.documents.find((function(e){return e.id===parseInt(a)})))||void 0===n?void 0:n.previewUrl)||o;return l?l+i:l}),[t,e])}(r),P=(0,l.useState)("desktop"),E=(0,u.default)(P,2),j=E[0],T=E[1],x=(0,l.useMemo)((function(){return b.find((function(e){return e.value===j})).style}),[j]),R=function onChange(e){T(e),function eventTracking(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"click";(0,_.appsEventTrackingDispatch)(e,{kit_name:r.title,page_source:"view demo",layout:t,element_position:n,event_type:a})}("kit-library/responsive-controls",e,"app_header")};if((0,v.default)({title:r?"".concat(n("Kit Library","elementor")," | ").concat(r.title):n("Loading...","elementor")}),a)throw new Error;return i?l.default.createElement(c.default,null):l.default.createElement(d.default,{header:l.default.createElement(s.default,{model:r,buttons:C,centerColumn:l.default.createElement(p.default,{active:j,onChange:function onChange(e){return R(e)},kitName:r.title}),pageId:"demo"})},k&&l.default.createElement(f.default,{className:"e-kit-library__preview-loader"}),w&&l.default.createElement(y.PreviewIframe,{previewUrl:w,style:x,onLoaded:function onLoaded(){return O(!1)}}))}t.breakpoints=b,Preview.propTypes={id:a.string}},83601:(e,t,r)=>{"use strict";var n=r(38003).__,a=r(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=a(r(87363)),o=r(36779);r(41744);var l=i.default.memo((function NewPageKitListItem(){return i.default.createElement(o.Card,{className:"e-onboarding__kit-library-card e-kit-library__kit-item"},i.default.createElement(o.CardHeader,null,i.default.createElement(o.Heading,{tag:"h3",title:n("Blank Canvas","elementor"),variant:"h5",className:"eps-card__headline"},n("Blank Canvas","elementor"))),i.default.createElement(o.CardBody,null,i.default.createElement(o.CardImage,{alt:n("Blank Canvas","elementor"),src:elementorCommon.config.urls.assets+"images/app/onboarding/Blank_Preview.jpg"||0},i.default.createElement(o.CardOverlay,null,i.default.createElement(o.Grid,{container:!0,direction:"column",className:"e-kit-library__kit-item-overlay"},i.default.createElement(o.Button,{className:"e-kit-library__kit-item-overlay-overview-button",text:n("Create New Elementor Page","elementor"),icon:"eicon-single-page",url:elementorAppConfig.onboarding.urls.createNewPage}))))))}));t.default=l},56827:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isTierAtLeast=t.TIERS_PRIORITY=t.TIERS=void 0;var r=Object.freeze(["free","essential","essential-oct2023","advanced","expert","agency"]);t.TIERS_PRIORITY=r;var n=Object.freeze(r.reduce((function(e,t){return e[t]=t,e}),{}));t.TIERS=n;t.isTierAtLeast=function isTierAtLeast(e,t){var n=r.indexOf(e),a=r.indexOf(t);return-1!==n&&-1!==a&&n>=a}}}]);