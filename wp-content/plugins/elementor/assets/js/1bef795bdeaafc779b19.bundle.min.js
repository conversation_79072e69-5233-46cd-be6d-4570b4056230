/*! elementor - v3.14.0 - 26-06-2023 */
(self.webpackChunkelementor=self.webpackChunkelementor||[]).push([[13],{29029:t=>{t.exports={ReactQueryDevtools:function(){return null},ReactQueryDevtoolsPanel:function(){return null}}},85123:(t,e,n)=>{"use strict";n.d(e,{j:()=>u});var r=n(88863),i=n(90270),s=n(12860),u=new(function(t){function FocusManager(){var e;return(e=t.call(this)||this).setup=function(t){var e;if(!s.sk&&(null==(e=window)?void 0:e.addEventListener)){var n=function listener(){return t()};return window.addEventListener("visibilitychange",n,!1),window.addEventListener("focus",n,!1),function(){window.removeEventListener("visibilitychange",n),window.removeEventListener("focus",n)}}},e}(0,r.Z)(FocusManager,t);var e=FocusManager.prototype;return e.onSubscribe=function onSubscribe(){this.cleanup||this.setEventListener(this.setup)},e.onUnsubscribe=function onUnsubscribe(){var t;this.hasListeners()||(null==(t=this.cleanup)||t.call(this),this.cleanup=void 0)},e.setEventListener=function setEventListener(t){var e,n=this;this.setup=t,null==(e=this.cleanup)||e.call(this),this.cleanup=t((function(t){"boolean"==typeof t?n.setFocused(t):n.onFocus()}))},e.setFocused=function setFocused(t){this.focused=t,t&&this.onFocus()},e.onFocus=function onFocus(){this.listeners.forEach((function(t){t()}))},e.isFocused=function isFocused(){return"boolean"==typeof this.focused?this.focused:"undefined"==typeof document||[void 0,"visible","prerender"].includes(document.visibilityState)},FocusManager}(i.l))},9975:(t,e,n)=>{"use strict";n.d(e,{D:()=>dehydrate,Z:()=>hydrate});var r=n(25773);function defaultShouldDehydrateMutation(t){return t.state.isPaused}function defaultShouldDehydrateQuery(t){return"success"===t.state.status}function dehydrate(t,e){var n,r,i=[],s=[];if(!1!==(null==(n=e=e||{})?void 0:n.dehydrateMutations)){var u=e.shouldDehydrateMutation||defaultShouldDehydrateMutation;t.getMutationCache().getAll().forEach((function(t){u(t)&&i.push(function dehydrateMutation(t){return{mutationKey:t.options.mutationKey,state:t.state}}(t))}))}if(!1!==(null==(r=e)?void 0:r.dehydrateQueries)){var o=e.shouldDehydrateQuery||defaultShouldDehydrateQuery;t.getQueryCache().getAll().forEach((function(t){o(t)&&s.push(function dehydrateQuery(t){return{state:t.state,queryKey:t.queryKey,queryHash:t.queryHash}}(t))}))}return{mutations:i,queries:s}}function hydrate(t,e,n){if("object"==typeof e&&null!==e){var i=t.getMutationCache(),s=t.getQueryCache(),u=e.mutations||[],o=e.queries||[];u.forEach((function(e){var s;i.build(t,(0,r.Z)({},null==n||null==(s=n.defaultOptions)?void 0:s.mutations,{mutationKey:e.mutationKey}),e.state)})),o.forEach((function(e){var i,u=s.get(e.queryHash);u?u.state.dataUpdatedAt<e.state.dataUpdatedAt&&u.setState(e.state):s.build(t,(0,r.Z)({},null==n||null==(i=n.defaultOptions)?void 0:i.queries,{queryKey:e.queryKey,queryHash:e.queryHash}),e.state)}))}}},30795:(t,e,n)=>{"use strict";n.r(e),n.d(e,{CancelledError:()=>r.p8,InfiniteQueryObserver:()=>a.c,MutationCache:()=>c.L,MutationObserver:()=>l.X,QueriesObserver:()=>o.y,QueryCache:()=>i.t,QueryClient:()=>s.S,QueryObserver:()=>u.z,dehydrate:()=>p.D,focusManager:()=>d.j,hashQueryKey:()=>y.yF,hydrate:()=>p.Z,isCancelledError:()=>r.DV,isError:()=>y.VZ,notifyManager:()=>f.V,onlineManager:()=>v.N,setLogger:()=>h.E});var r=n(98973),i=n(89157),s=n(37710),u=n(87978),o=n(27282),a=n(15830),c=n(79560),l=n(40424),h=n(16129),f=n(87226),d=n(85123),v=n(19240),y=n(12860),p=n(9975),b=n(46503),m={};for(const t in b)["default","CancelledError","QueryCache","QueryClient","QueryObserver","QueriesObserver","InfiniteQueryObserver","MutationCache","MutationObserver","setLogger","notifyManager","focusManager","onlineManager","hashQueryKey","isError","isCancelledError","dehydrate","hydrate"].indexOf(t)<0&&(m[t]=()=>b[t]);n.d(e,m)},47798:(t,e,n)=>{"use strict";n.d(e,{Gm:()=>infiniteQueryBehavior,Qy:()=>hasNextPage,ZF:()=>hasPreviousPage});var r=n(98973),i=n(12860);function infiniteQueryBehavior(){return{onFetch:function onFetch(t){t.fetchFn=function(){var e,n,s,u,o,a,c,l=null==(e=t.fetchOptions)||null==(n=e.meta)?void 0:n.refetchPage,h=null==(s=t.fetchOptions)||null==(u=s.meta)?void 0:u.fetchMore,f=null==h?void 0:h.pageParam,d="forward"===(null==h?void 0:h.direction),v="backward"===(null==h?void 0:h.direction),y=(null==(o=t.state.data)?void 0:o.pages)||[],p=(null==(a=t.state.data)?void 0:a.pageParams)||[],b=(0,i.G9)(),m=null==b?void 0:b.signal,g=p,O=!1,Q=t.options.queryFn||function(){return Promise.reject("Missing queryFn")},C=function buildNewPages(t,e,n,r){return g=r?[e].concat(g):[].concat(g,[e]),r?[n].concat(t):[].concat(t,[n])},P=function fetchPage(e,n,i,s){if(O)return Promise.reject("Cancelled");if(void 0===i&&!n&&e.length)return Promise.resolve(e);var u={queryKey:t.queryKey,signal:m,pageParam:i,meta:t.meta},o=Q(u),a=Promise.resolve(o).then((function(t){return C(e,i,t,s)}));(0,r.LE)(o)&&(a.cancel=o.cancel);return a};if(y.length)if(d){var R=void 0!==f,M=R?f:getNextPageParam(t.options,y);c=P(y,R,M)}else if(v){var F=void 0!==f,E=F?f:getPreviousPageParam(t.options,y);c=P(y,F,E,!0)}else!function(){g=[];var e=void 0===t.options.getNextPageParam,n=!l||!y[0]||l(y[0],0,y);c=n?P([],e,p[0]):Promise.resolve(C([],p[0],y[0]));for(var r=function _loop(n){c=c.then((function(r){if(!l||!y[n]||l(y[n],n,y)){var i=e?p[n]:getNextPageParam(t.options,r);return P(r,e,i)}return Promise.resolve(C(r,p[n],y[n]))}))},i=1;i<y.length;i++)r(i)}();else c=P([]);var S=c.then((function(t){return{pages:t,pageParams:g}}));return S.cancel=function(){O=!0,null==b||b.abort(),(0,r.LE)(c)&&c.cancel()},S}}}}function getNextPageParam(t,e){return null==t.getNextPageParam?void 0:t.getNextPageParam(e[e.length-1],e)}function getPreviousPageParam(t,e){return null==t.getPreviousPageParam?void 0:t.getPreviousPageParam(e[0],e)}function hasNextPage(t,e){if(t.getNextPageParam&&Array.isArray(e)){var n=getNextPageParam(t,e);return null!=n&&!1!==n}}function hasPreviousPage(t,e){if(t.getPreviousPageParam&&Array.isArray(e)){var n=getPreviousPageParam(t,e);return null!=n&&!1!==n}}},15830:(t,e,n)=>{"use strict";n.d(e,{c:()=>o});var r=n(25773),i=n(88863),s=n(87978),u=n(47798),o=function(t){function InfiniteQueryObserver(e,n){return t.call(this,e,n)||this}(0,i.Z)(InfiniteQueryObserver,t);var e=InfiniteQueryObserver.prototype;return e.bindMethods=function bindMethods(){t.prototype.bindMethods.call(this),this.fetchNextPage=this.fetchNextPage.bind(this),this.fetchPreviousPage=this.fetchPreviousPage.bind(this)},e.setOptions=function setOptions(e,n){t.prototype.setOptions.call(this,(0,r.Z)({},e,{behavior:(0,u.Gm)()}),n)},e.getOptimisticResult=function getOptimisticResult(e){return e.behavior=(0,u.Gm)(),t.prototype.getOptimisticResult.call(this,e)},e.fetchNextPage=function fetchNextPage(t){var e;return this.fetch({cancelRefetch:null==(e=null==t?void 0:t.cancelRefetch)||e,throwOnError:null==t?void 0:t.throwOnError,meta:{fetchMore:{direction:"forward",pageParam:null==t?void 0:t.pageParam}}})},e.fetchPreviousPage=function fetchPreviousPage(t){var e;return this.fetch({cancelRefetch:null==(e=null==t?void 0:t.cancelRefetch)||e,throwOnError:null==t?void 0:t.throwOnError,meta:{fetchMore:{direction:"backward",pageParam:null==t?void 0:t.pageParam}}})},e.createResult=function createResult(e,n){var i,s,o,a,c,l,h=e.state,f=t.prototype.createResult.call(this,e,n);return(0,r.Z)({},f,{fetchNextPage:this.fetchNextPage,fetchPreviousPage:this.fetchPreviousPage,hasNextPage:(0,u.Qy)(n,null==(i=h.data)?void 0:i.pages),hasPreviousPage:(0,u.ZF)(n,null==(s=h.data)?void 0:s.pages),isFetchingNextPage:h.isFetching&&"forward"===(null==(o=h.fetchMeta)||null==(a=o.fetchMore)?void 0:a.direction),isFetchingPreviousPage:h.isFetching&&"backward"===(null==(c=h.fetchMeta)||null==(l=c.fetchMore)?void 0:l.direction)})},InfiniteQueryObserver}(s.z)},16129:(t,e,n)=>{"use strict";n.d(e,{E:()=>setLogger,j:()=>getLogger});var r=console;function getLogger(){return r}function setLogger(t){r=t}},12790:(t,e,n)=>{"use strict";n.d(e,{R:()=>getDefaultState,m:()=>a});var r=n(25773),i=n(16129),s=n(87226),u=n(98973),o=n(12860),a=function(){function Mutation(t){this.options=(0,r.Z)({},t.defaultOptions,t.options),this.mutationId=t.mutationId,this.mutationCache=t.mutationCache,this.observers=[],this.state=t.state||getDefaultState(),this.meta=t.meta}var t=Mutation.prototype;return t.setState=function setState(t){this.dispatch({type:"setState",state:t})},t.addObserver=function addObserver(t){-1===this.observers.indexOf(t)&&this.observers.push(t)},t.removeObserver=function removeObserver(t){this.observers=this.observers.filter((function(e){return e!==t}))},t.cancel=function cancel(){return this.retryer?(this.retryer.cancel(),this.retryer.promise.then(o.ZT).catch(o.ZT)):Promise.resolve()},t.continue=function _continue(){return this.retryer?(this.retryer.continue(),this.retryer.promise):this.execute()},t.execute=function execute(){var t,e=this,n="loading"===this.state.status,r=Promise.resolve();return n||(this.dispatch({type:"loading",variables:this.options.variables}),r=r.then((function(){null==e.mutationCache.config.onMutate||e.mutationCache.config.onMutate(e.state.variables,e)})).then((function(){return null==e.options.onMutate?void 0:e.options.onMutate(e.state.variables)})).then((function(t){t!==e.state.context&&e.dispatch({type:"loading",context:t,variables:e.state.variables})}))),r.then((function(){return e.executeMutation()})).then((function(n){t=n,null==e.mutationCache.config.onSuccess||e.mutationCache.config.onSuccess(t,e.state.variables,e.state.context,e)})).then((function(){return null==e.options.onSuccess?void 0:e.options.onSuccess(t,e.state.variables,e.state.context)})).then((function(){return null==e.options.onSettled?void 0:e.options.onSettled(t,null,e.state.variables,e.state.context)})).then((function(){return e.dispatch({type:"success",data:t}),t})).catch((function(t){return null==e.mutationCache.config.onError||e.mutationCache.config.onError(t,e.state.variables,e.state.context,e),(0,i.j)().error(t),Promise.resolve().then((function(){return null==e.options.onError?void 0:e.options.onError(t,e.state.variables,e.state.context)})).then((function(){return null==e.options.onSettled?void 0:e.options.onSettled(void 0,t,e.state.variables,e.state.context)})).then((function(){throw e.dispatch({type:"error",error:t}),t}))}))},t.executeMutation=function executeMutation(){var t,e=this;return this.retryer=new u.m4({fn:function fn(){return e.options.mutationFn?e.options.mutationFn(e.state.variables):Promise.reject("No mutationFn found")},onFail:function onFail(){e.dispatch({type:"failed"})},onPause:function onPause(){e.dispatch({type:"pause"})},onContinue:function onContinue(){e.dispatch({type:"continue"})},retry:null!=(t=this.options.retry)?t:0,retryDelay:this.options.retryDelay}),this.retryer.promise},t.dispatch=function dispatch(t){var e=this;this.state=function reducer(t,e){switch(e.type){case"failed":return(0,r.Z)({},t,{failureCount:t.failureCount+1});case"pause":return(0,r.Z)({},t,{isPaused:!0});case"continue":return(0,r.Z)({},t,{isPaused:!1});case"loading":return(0,r.Z)({},t,{context:e.context,data:void 0,error:null,isPaused:!1,status:"loading",variables:e.variables});case"success":return(0,r.Z)({},t,{data:e.data,error:null,status:"success",isPaused:!1});case"error":return(0,r.Z)({},t,{data:void 0,error:e.error,failureCount:t.failureCount+1,isPaused:!1,status:"error"});case"setState":return(0,r.Z)({},t,e.state);default:return t}}(this.state,t),s.V.batch((function(){e.observers.forEach((function(e){e.onMutationUpdate(t)})),e.mutationCache.notify(e)}))},Mutation}();function getDefaultState(){return{context:void 0,data:void 0,error:null,failureCount:0,isPaused:!1,status:"idle",variables:void 0}}},79560:(t,e,n)=>{"use strict";n.d(e,{L:()=>o});var r=n(88863),i=n(87226),s=n(12790),u=n(12860),o=function(t){function MutationCache(e){var n;return(n=t.call(this)||this).config=e||{},n.mutations=[],n.mutationId=0,n}(0,r.Z)(MutationCache,t);var e=MutationCache.prototype;return e.build=function build(t,e,n){var r=new s.m({mutationCache:this,mutationId:++this.mutationId,options:t.defaultMutationOptions(e),state:n,defaultOptions:e.mutationKey?t.getMutationDefaults(e.mutationKey):void 0,meta:e.meta});return this.add(r),r},e.add=function add(t){this.mutations.push(t),this.notify(t)},e.remove=function remove(t){this.mutations=this.mutations.filter((function(e){return e!==t})),t.cancel(),this.notify(t)},e.clear=function clear(){var t=this;i.V.batch((function(){t.mutations.forEach((function(e){t.remove(e)}))}))},e.getAll=function getAll(){return this.mutations},e.find=function find(t){return void 0===t.exact&&(t.exact=!0),this.mutations.find((function(e){return(0,u.X7)(t,e)}))},e.findAll=function findAll(t){return this.mutations.filter((function(e){return(0,u.X7)(t,e)}))},e.notify=function notify(t){var e=this;i.V.batch((function(){e.listeners.forEach((function(e){e(t)}))}))},e.onFocus=function onFocus(){this.resumePausedMutations()},e.onOnline=function onOnline(){this.resumePausedMutations()},e.resumePausedMutations=function resumePausedMutations(){var t=this.mutations.filter((function(t){return t.state.isPaused}));return i.V.batch((function(){return t.reduce((function(t,e){return t.then((function(){return e.continue().catch(u.ZT)}))}),Promise.resolve())}))},MutationCache}(n(90270).l)},40424:(t,e,n)=>{"use strict";n.d(e,{X:()=>o});var r=n(25773),i=n(88863),s=n(12790),u=n(87226),o=function(t){function MutationObserver(e,n){var r;return(r=t.call(this)||this).client=e,r.setOptions(n),r.bindMethods(),r.updateResult(),r}(0,i.Z)(MutationObserver,t);var e=MutationObserver.prototype;return e.bindMethods=function bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)},e.setOptions=function setOptions(t){this.options=this.client.defaultMutationOptions(t)},e.onUnsubscribe=function onUnsubscribe(){var t;this.listeners.length||(null==(t=this.currentMutation)||t.removeObserver(this))},e.onMutationUpdate=function onMutationUpdate(t){this.updateResult();var e={listeners:!0};"success"===t.type?e.onSuccess=!0:"error"===t.type&&(e.onError=!0),this.notify(e)},e.getCurrentResult=function getCurrentResult(){return this.currentResult},e.reset=function reset(){this.currentMutation=void 0,this.updateResult(),this.notify({listeners:!0})},e.mutate=function mutate(t,e){return this.mutateOptions=e,this.currentMutation&&this.currentMutation.removeObserver(this),this.currentMutation=this.client.getMutationCache().build(this.client,(0,r.Z)({},this.options,{variables:void 0!==t?t:this.options.variables})),this.currentMutation.addObserver(this),this.currentMutation.execute()},e.updateResult=function updateResult(){var t=this.currentMutation?this.currentMutation.state:(0,s.R)(),e=(0,r.Z)({},t,{isLoading:"loading"===t.status,isSuccess:"success"===t.status,isError:"error"===t.status,isIdle:"idle"===t.status,mutate:this.mutate,reset:this.reset});this.currentResult=e},e.notify=function notify(t){var e=this;u.V.batch((function(){e.mutateOptions&&(t.onSuccess?(null==e.mutateOptions.onSuccess||e.mutateOptions.onSuccess(e.currentResult.data,e.currentResult.variables,e.currentResult.context),null==e.mutateOptions.onSettled||e.mutateOptions.onSettled(e.currentResult.data,null,e.currentResult.variables,e.currentResult.context)):t.onError&&(null==e.mutateOptions.onError||e.mutateOptions.onError(e.currentResult.error,e.currentResult.variables,e.currentResult.context),null==e.mutateOptions.onSettled||e.mutateOptions.onSettled(void 0,e.currentResult.error,e.currentResult.variables,e.currentResult.context))),t.listeners&&e.listeners.forEach((function(t){t(e.currentResult)}))}))},MutationObserver}(n(90270).l)},87226:(t,e,n)=>{"use strict";n.d(e,{V:()=>i});var r=n(12860),i=new(function(){function NotifyManager(){this.queue=[],this.transactions=0,this.notifyFn=function(t){t()},this.batchNotifyFn=function(t){t()}}var t=NotifyManager.prototype;return t.batch=function batch(t){var e;this.transactions++;try{e=t()}finally{this.transactions--,this.transactions||this.flush()}return e},t.schedule=function schedule(t){var e=this;this.transactions?this.queue.push(t):(0,r.A4)((function(){e.notifyFn(t)}))},t.batchCalls=function batchCalls(t){var e=this;return function(){for(var n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];e.schedule((function(){t.apply(void 0,r)}))}},t.flush=function flush(){var t=this,e=this.queue;this.queue=[],e.length&&(0,r.A4)((function(){t.batchNotifyFn((function(){e.forEach((function(e){t.notifyFn(e)}))}))}))},t.setNotifyFunction=function setNotifyFunction(t){this.notifyFn=t},t.setBatchNotifyFunction=function setBatchNotifyFunction(t){this.batchNotifyFn=t},NotifyManager}())},19240:(t,e,n)=>{"use strict";n.d(e,{N:()=>u});var r=n(88863),i=n(90270),s=n(12860),u=new(function(t){function OnlineManager(){var e;return(e=t.call(this)||this).setup=function(t){var e;if(!s.sk&&(null==(e=window)?void 0:e.addEventListener)){var n=function listener(){return t()};return window.addEventListener("online",n,!1),window.addEventListener("offline",n,!1),function(){window.removeEventListener("online",n),window.removeEventListener("offline",n)}}},e}(0,r.Z)(OnlineManager,t);var e=OnlineManager.prototype;return e.onSubscribe=function onSubscribe(){this.cleanup||this.setEventListener(this.setup)},e.onUnsubscribe=function onUnsubscribe(){var t;this.hasListeners()||(null==(t=this.cleanup)||t.call(this),this.cleanup=void 0)},e.setEventListener=function setEventListener(t){var e,n=this;this.setup=t,null==(e=this.cleanup)||e.call(this),this.cleanup=t((function(t){"boolean"==typeof t?n.setOnline(t):n.onOnline()}))},e.setOnline=function setOnline(t){this.online=t,t&&this.onOnline()},e.onOnline=function onOnline(){this.listeners.forEach((function(t){t()}))},e.isOnline=function isOnline(){return"boolean"==typeof this.online?this.online:"undefined"==typeof navigator||void 0===navigator.onLine||navigator.onLine},OnlineManager}(i.l))},27282:(t,e,n)=>{"use strict";n.d(e,{y:()=>o});var r=n(88863),i=n(12860),s=n(87226),u=n(87978),o=function(t){function QueriesObserver(e,n){var r;return(r=t.call(this)||this).client=e,r.queries=[],r.result=[],r.observers=[],r.observersMap={},n&&r.setQueries(n),r}(0,r.Z)(QueriesObserver,t);var e=QueriesObserver.prototype;return e.onSubscribe=function onSubscribe(){var t=this;1===this.listeners.length&&this.observers.forEach((function(e){e.subscribe((function(n){t.onUpdate(e,n)}))}))},e.onUnsubscribe=function onUnsubscribe(){this.listeners.length||this.destroy()},e.destroy=function destroy(){this.listeners=[],this.observers.forEach((function(t){t.destroy()}))},e.setQueries=function setQueries(t,e){this.queries=t,this.updateObservers(e)},e.getCurrentResult=function getCurrentResult(){return this.result},e.getOptimisticResult=function getOptimisticResult(t){return this.findMatchingObservers(t).map((function(t){return t.observer.getOptimisticResult(t.defaultedQueryOptions)}))},e.findMatchingObservers=function findMatchingObservers(t){var e=this,n=this.observers,r=t.map((function(t){return e.client.defaultQueryObserverOptions(t)})),i=r.flatMap((function(t){var e=n.find((function(e){return e.options.queryHash===t.queryHash}));return null!=e?[{defaultedQueryOptions:t,observer:e}]:[]})),s=i.map((function(t){return t.defaultedQueryOptions.queryHash})),u=r.filter((function(t){return!s.includes(t.queryHash)})),o=n.filter((function(t){return!i.some((function(e){return e.observer===t}))})),a=u.map((function(t,n){if(t.keepPreviousData){var r=o[n];if(void 0!==r)return{defaultedQueryOptions:t,observer:r}}return{defaultedQueryOptions:t,observer:e.getObserver(t)}}));return i.concat(a).sort((function sortMatchesByOrderOfQueries(t,e){return r.indexOf(t.defaultedQueryOptions)-r.indexOf(e.defaultedQueryOptions)}))},e.getObserver=function getObserver(t){var e=this.client.defaultQueryObserverOptions(t),n=this.observersMap[e.queryHash];return null!=n?n:new u.z(this.client,e)},e.updateObservers=function updateObservers(t){var e=this;s.V.batch((function(){var n=e.observers,r=e.findMatchingObservers(e.queries);r.forEach((function(e){return e.observer.setOptions(e.defaultedQueryOptions,t)}));var s=r.map((function(t){return t.observer})),u=Object.fromEntries(s.map((function(t){return[t.options.queryHash,t]}))),o=s.map((function(t){return t.getCurrentResult()})),a=s.some((function(t,e){return t!==n[e]}));(n.length!==s.length||a)&&(e.observers=s,e.observersMap=u,e.result=o,e.hasListeners()&&((0,i.e5)(n,s).forEach((function(t){t.destroy()})),(0,i.e5)(s,n).forEach((function(t){t.subscribe((function(n){e.onUpdate(t,n)}))})),e.notify()))}))},e.onUpdate=function onUpdate(t,e){var n=this.observers.indexOf(t);-1!==n&&(this.result=(0,i.Rc)(this.result,n,e),this.notify())},e.notify=function notify(){var t=this;s.V.batch((function(){t.listeners.forEach((function(e){e(t.result)}))}))},QueriesObserver}(n(90270).l)},89157:(t,e,n)=>{"use strict";n.d(e,{t:()=>l});var r=n(88863),i=n(12860),s=n(25773),u=n(87226),o=n(16129),a=n(98973),c=function(){function Query(t){this.abortSignalConsumed=!1,this.hadObservers=!1,this.defaultOptions=t.defaultOptions,this.setOptions(t.options),this.observers=[],this.cache=t.cache,this.queryKey=t.queryKey,this.queryHash=t.queryHash,this.initialState=t.state||this.getDefaultState(this.options),this.state=this.initialState,this.meta=t.meta,this.scheduleGc()}var t=Query.prototype;return t.setOptions=function setOptions(t){var e;this.options=(0,s.Z)({},this.defaultOptions,t),this.meta=null==t?void 0:t.meta,this.cacheTime=Math.max(this.cacheTime||0,null!=(e=this.options.cacheTime)?e:3e5)},t.setDefaultOptions=function setDefaultOptions(t){this.defaultOptions=t},t.scheduleGc=function scheduleGc(){var t=this;this.clearGcTimeout(),(0,i.PN)(this.cacheTime)&&(this.gcTimeout=setTimeout((function(){t.optionalRemove()}),this.cacheTime))},t.clearGcTimeout=function clearGcTimeout(){this.gcTimeout&&(clearTimeout(this.gcTimeout),this.gcTimeout=void 0)},t.optionalRemove=function optionalRemove(){this.observers.length||(this.state.isFetching?this.hadObservers&&this.scheduleGc():this.cache.remove(this))},t.setData=function setData(t,e){var n,r,s=this.state.data,u=(0,i.SE)(t,s);return(null==(n=(r=this.options).isDataEqual)?void 0:n.call(r,s,u))?u=s:!1!==this.options.structuralSharing&&(u=(0,i.Q$)(s,u)),this.dispatch({data:u,type:"success",dataUpdatedAt:null==e?void 0:e.updatedAt}),u},t.setState=function setState(t,e){this.dispatch({type:"setState",state:t,setStateOptions:e})},t.cancel=function cancel(t){var e,n=this.promise;return null==(e=this.retryer)||e.cancel(t),n?n.then(i.ZT).catch(i.ZT):Promise.resolve()},t.destroy=function destroy(){this.clearGcTimeout(),this.cancel({silent:!0})},t.reset=function reset(){this.destroy(),this.setState(this.initialState)},t.isActive=function isActive(){return this.observers.some((function(t){return!1!==t.options.enabled}))},t.isFetching=function isFetching(){return this.state.isFetching},t.isStale=function isStale(){return this.state.isInvalidated||!this.state.dataUpdatedAt||this.observers.some((function(t){return t.getCurrentResult().isStale}))},t.isStaleByTime=function isStaleByTime(t){return void 0===t&&(t=0),this.state.isInvalidated||!this.state.dataUpdatedAt||!(0,i.Kp)(this.state.dataUpdatedAt,t)},t.onFocus=function onFocus(){var t,e=this.observers.find((function(t){return t.shouldFetchOnWindowFocus()}));e&&e.refetch(),null==(t=this.retryer)||t.continue()},t.onOnline=function onOnline(){var t,e=this.observers.find((function(t){return t.shouldFetchOnReconnect()}));e&&e.refetch(),null==(t=this.retryer)||t.continue()},t.addObserver=function addObserver(t){-1===this.observers.indexOf(t)&&(this.observers.push(t),this.hadObservers=!0,this.clearGcTimeout(),this.cache.notify({type:"observerAdded",query:this,observer:t}))},t.removeObserver=function removeObserver(t){-1!==this.observers.indexOf(t)&&(this.observers=this.observers.filter((function(e){return e!==t})),this.observers.length||(this.retryer&&(this.retryer.isTransportCancelable||this.abortSignalConsumed?this.retryer.cancel({revert:!0}):this.retryer.cancelRetry()),this.cacheTime?this.scheduleGc():this.cache.remove(this)),this.cache.notify({type:"observerRemoved",query:this,observer:t}))},t.getObserversCount=function getObserversCount(){return this.observers.length},t.invalidate=function invalidate(){this.state.isInvalidated||this.dispatch({type:"invalidate"})},t.fetch=function fetch(t,e){var n,r,s,u=this;if(this.state.isFetching)if(this.state.dataUpdatedAt&&(null==e?void 0:e.cancelRefetch))this.cancel({silent:!0});else if(this.promise){var c;return null==(c=this.retryer)||c.continueRetry(),this.promise}if(t&&this.setOptions(t),!this.options.queryFn){var l=this.observers.find((function(t){return t.options.queryFn}));l&&this.setOptions(l.options)}var h=(0,i.mc)(this.queryKey),f=(0,i.G9)(),d={queryKey:h,pageParam:void 0,meta:this.meta};Object.defineProperty(d,"signal",{enumerable:!0,get:function get(){if(f)return u.abortSignalConsumed=!0,f.signal}});var v,y,p={fetchOptions:e,options:this.options,queryKey:h,state:this.state,fetchFn:function fetchFn(){return u.options.queryFn?(u.abortSignalConsumed=!1,u.options.queryFn(d)):Promise.reject("Missing queryFn")},meta:this.meta};(null==(n=this.options.behavior)?void 0:n.onFetch)&&(null==(v=this.options.behavior)||v.onFetch(p));(this.revertState=this.state,this.state.isFetching&&this.state.fetchMeta===(null==(r=p.fetchOptions)?void 0:r.meta))||this.dispatch({type:"fetch",meta:null==(y=p.fetchOptions)?void 0:y.meta});return this.retryer=new a.m4({fn:p.fetchFn,abort:null==f||null==(s=f.abort)?void 0:s.bind(f),onSuccess:function onSuccess(t){u.setData(t),null==u.cache.config.onSuccess||u.cache.config.onSuccess(t,u),0===u.cacheTime&&u.optionalRemove()},onError:function onError(t){(0,a.DV)(t)&&t.silent||u.dispatch({type:"error",error:t}),(0,a.DV)(t)||(null==u.cache.config.onError||u.cache.config.onError(t,u),(0,o.j)().error(t)),0===u.cacheTime&&u.optionalRemove()},onFail:function onFail(){u.dispatch({type:"failed"})},onPause:function onPause(){u.dispatch({type:"pause"})},onContinue:function onContinue(){u.dispatch({type:"continue"})},retry:p.options.retry,retryDelay:p.options.retryDelay}),this.promise=this.retryer.promise,this.promise},t.dispatch=function dispatch(t){var e=this;this.state=this.reducer(this.state,t),u.V.batch((function(){e.observers.forEach((function(e){e.onQueryUpdate(t)})),e.cache.notify({query:e,type:"queryUpdated",action:t})}))},t.getDefaultState=function getDefaultState(t){var e="function"==typeof t.initialData?t.initialData():t.initialData,n=void 0!==t.initialData?"function"==typeof t.initialDataUpdatedAt?t.initialDataUpdatedAt():t.initialDataUpdatedAt:0,r=void 0!==e;return{data:e,dataUpdateCount:0,dataUpdatedAt:r?null!=n?n:Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchMeta:null,isFetching:!1,isInvalidated:!1,isPaused:!1,status:r?"success":"idle"}},t.reducer=function reducer(t,e){var n,r;switch(e.type){case"failed":return(0,s.Z)({},t,{fetchFailureCount:t.fetchFailureCount+1});case"pause":return(0,s.Z)({},t,{isPaused:!0});case"continue":return(0,s.Z)({},t,{isPaused:!1});case"fetch":return(0,s.Z)({},t,{fetchFailureCount:0,fetchMeta:null!=(n=e.meta)?n:null,isFetching:!0,isPaused:!1},!t.dataUpdatedAt&&{error:null,status:"loading"});case"success":return(0,s.Z)({},t,{data:e.data,dataUpdateCount:t.dataUpdateCount+1,dataUpdatedAt:null!=(r=e.dataUpdatedAt)?r:Date.now(),error:null,fetchFailureCount:0,isFetching:!1,isInvalidated:!1,isPaused:!1,status:"success"});case"error":var i=e.error;return(0,a.DV)(i)&&i.revert&&this.revertState?(0,s.Z)({},this.revertState):(0,s.Z)({},t,{error:i,errorUpdateCount:t.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:t.fetchFailureCount+1,isFetching:!1,isPaused:!1,status:"error"});case"invalidate":return(0,s.Z)({},t,{isInvalidated:!0});case"setState":return(0,s.Z)({},t,e.state);default:return t}},Query}(),l=function(t){function QueryCache(e){var n;return(n=t.call(this)||this).config=e||{},n.queries=[],n.queriesMap={},n}(0,r.Z)(QueryCache,t);var e=QueryCache.prototype;return e.build=function build(t,e,n){var r,s=e.queryKey,u=null!=(r=e.queryHash)?r:(0,i.Rm)(s,e),o=this.get(u);return o||(o=new c({cache:this,queryKey:s,queryHash:u,options:t.defaultQueryOptions(e),state:n,defaultOptions:t.getQueryDefaults(s),meta:e.meta}),this.add(o)),o},e.add=function add(t){this.queriesMap[t.queryHash]||(this.queriesMap[t.queryHash]=t,this.queries.push(t),this.notify({type:"queryAdded",query:t}))},e.remove=function remove(t){var e=this.queriesMap[t.queryHash];e&&(t.destroy(),this.queries=this.queries.filter((function(e){return e!==t})),e===t&&delete this.queriesMap[t.queryHash],this.notify({type:"queryRemoved",query:t}))},e.clear=function clear(){var t=this;u.V.batch((function(){t.queries.forEach((function(e){t.remove(e)}))}))},e.get=function get(t){return this.queriesMap[t]},e.getAll=function getAll(){return this.queries},e.find=function find(t,e){var n=(0,i.I6)(t,e)[0];return void 0===n.exact&&(n.exact=!0),this.queries.find((function(t){return(0,i._x)(n,t)}))},e.findAll=function findAll(t,e){var n=(0,i.I6)(t,e)[0];return Object.keys(n).length>0?this.queries.filter((function(t){return(0,i._x)(n,t)})):this.queries},e.notify=function notify(t){var e=this;u.V.batch((function(){e.listeners.forEach((function(e){e(t)}))}))},e.onFocus=function onFocus(){var t=this;u.V.batch((function(){t.queries.forEach((function(t){t.onFocus()}))}))},e.onOnline=function onOnline(){var t=this;u.V.batch((function(){t.queries.forEach((function(t){t.onOnline()}))}))},QueryCache}(n(90270).l)},37710:(t,e,n)=>{"use strict";n.d(e,{S:()=>h});var r=n(25773),i=n(12860),s=n(89157),u=n(79560),o=n(85123),a=n(19240),c=n(87226),l=n(47798),h=function(){function QueryClient(t){void 0===t&&(t={}),this.queryCache=t.queryCache||new s.t,this.mutationCache=t.mutationCache||new u.L,this.defaultOptions=t.defaultOptions||{},this.queryDefaults=[],this.mutationDefaults=[]}var t=QueryClient.prototype;return t.mount=function mount(){var t=this;this.unsubscribeFocus=o.j.subscribe((function(){o.j.isFocused()&&a.N.isOnline()&&(t.mutationCache.onFocus(),t.queryCache.onFocus())})),this.unsubscribeOnline=a.N.subscribe((function(){o.j.isFocused()&&a.N.isOnline()&&(t.mutationCache.onOnline(),t.queryCache.onOnline())}))},t.unmount=function unmount(){var t,e;null==(t=this.unsubscribeFocus)||t.call(this),null==(e=this.unsubscribeOnline)||e.call(this)},t.isFetching=function isFetching(t,e){var n=(0,i.I6)(t,e)[0];return n.fetching=!0,this.queryCache.findAll(n).length},t.isMutating=function isMutating(t){return this.mutationCache.findAll((0,r.Z)({},t,{fetching:!0})).length},t.getQueryData=function getQueryData(t,e){var n;return null==(n=this.queryCache.find(t,e))?void 0:n.state.data},t.getQueriesData=function getQueriesData(t){return this.getQueryCache().findAll(t).map((function(t){return[t.queryKey,t.state.data]}))},t.setQueryData=function setQueryData(t,e,n){var r=(0,i._v)(t),s=this.defaultQueryOptions(r);return this.queryCache.build(this,s).setData(e,n)},t.setQueriesData=function setQueriesData(t,e,n){var r=this;return c.V.batch((function(){return r.getQueryCache().findAll(t).map((function(t){var i=t.queryKey;return[i,r.setQueryData(i,e,n)]}))}))},t.getQueryState=function getQueryState(t,e){var n;return null==(n=this.queryCache.find(t,e))?void 0:n.state},t.removeQueries=function removeQueries(t,e){var n=(0,i.I6)(t,e)[0],r=this.queryCache;c.V.batch((function(){r.findAll(n).forEach((function(t){r.remove(t)}))}))},t.resetQueries=function resetQueries(t,e,n){var s=this,u=(0,i.I6)(t,e,n),o=u[0],a=u[1],l=this.queryCache,h=(0,r.Z)({},o,{active:!0});return c.V.batch((function(){return l.findAll(o).forEach((function(t){t.reset()})),s.refetchQueries(h,a)}))},t.cancelQueries=function cancelQueries(t,e,n){var r=this,s=(0,i.I6)(t,e,n),u=s[0],o=s[1],a=void 0===o?{}:o;void 0===a.revert&&(a.revert=!0);var l=c.V.batch((function(){return r.queryCache.findAll(u).map((function(t){return t.cancel(a)}))}));return Promise.all(l).then(i.ZT).catch(i.ZT)},t.invalidateQueries=function invalidateQueries(t,e,n){var s,u,o,a=this,l=(0,i.I6)(t,e,n),h=l[0],f=l[1],d=(0,r.Z)({},h,{active:null==(s=null!=(u=h.refetchActive)?u:h.active)||s,inactive:null!=(o=h.refetchInactive)&&o});return c.V.batch((function(){return a.queryCache.findAll(h).forEach((function(t){t.invalidate()})),a.refetchQueries(d,f)}))},t.refetchQueries=function refetchQueries(t,e,n){var s=this,u=(0,i.I6)(t,e,n),o=u[0],a=u[1],l=c.V.batch((function(){return s.queryCache.findAll(o).map((function(t){return t.fetch(void 0,(0,r.Z)({},a,{meta:{refetchPage:null==o?void 0:o.refetchPage}}))}))})),h=Promise.all(l).then(i.ZT);return(null==a?void 0:a.throwOnError)||(h=h.catch(i.ZT)),h},t.fetchQuery=function fetchQuery(t,e,n){var r=(0,i._v)(t,e,n),s=this.defaultQueryOptions(r);void 0===s.retry&&(s.retry=!1);var u=this.queryCache.build(this,s);return u.isStaleByTime(s.staleTime)?u.fetch(s):Promise.resolve(u.state.data)},t.prefetchQuery=function prefetchQuery(t,e,n){return this.fetchQuery(t,e,n).then(i.ZT).catch(i.ZT)},t.fetchInfiniteQuery=function fetchInfiniteQuery(t,e,n){var r=(0,i._v)(t,e,n);return r.behavior=(0,l.Gm)(),this.fetchQuery(r)},t.prefetchInfiniteQuery=function prefetchInfiniteQuery(t,e,n){return this.fetchInfiniteQuery(t,e,n).then(i.ZT).catch(i.ZT)},t.cancelMutations=function cancelMutations(){var t=this,e=c.V.batch((function(){return t.mutationCache.getAll().map((function(t){return t.cancel()}))}));return Promise.all(e).then(i.ZT).catch(i.ZT)},t.resumePausedMutations=function resumePausedMutations(){return this.getMutationCache().resumePausedMutations()},t.executeMutation=function executeMutation(t){return this.mutationCache.build(this,t).execute()},t.getQueryCache=function getQueryCache(){return this.queryCache},t.getMutationCache=function getMutationCache(){return this.mutationCache},t.getDefaultOptions=function getDefaultOptions(){return this.defaultOptions},t.setDefaultOptions=function setDefaultOptions(t){this.defaultOptions=t},t.setQueryDefaults=function setQueryDefaults(t,e){var n=this.queryDefaults.find((function(e){return(0,i.yF)(t)===(0,i.yF)(e.queryKey)}));n?n.defaultOptions=e:this.queryDefaults.push({queryKey:t,defaultOptions:e})},t.getQueryDefaults=function getQueryDefaults(t){var e;return t?null==(e=this.queryDefaults.find((function(e){return(0,i.to)(t,e.queryKey)})))?void 0:e.defaultOptions:void 0},t.setMutationDefaults=function setMutationDefaults(t,e){var n=this.mutationDefaults.find((function(e){return(0,i.yF)(t)===(0,i.yF)(e.mutationKey)}));n?n.defaultOptions=e:this.mutationDefaults.push({mutationKey:t,defaultOptions:e})},t.getMutationDefaults=function getMutationDefaults(t){var e;return t?null==(e=this.mutationDefaults.find((function(e){return(0,i.to)(t,e.mutationKey)})))?void 0:e.defaultOptions:void 0},t.defaultQueryOptions=function defaultQueryOptions(t){if(null==t?void 0:t._defaulted)return t;var e=(0,r.Z)({},this.defaultOptions.queries,this.getQueryDefaults(null==t?void 0:t.queryKey),t,{_defaulted:!0});return!e.queryHash&&e.queryKey&&(e.queryHash=(0,i.Rm)(e.queryKey,e)),e},t.defaultQueryObserverOptions=function defaultQueryObserverOptions(t){return this.defaultQueryOptions(t)},t.defaultMutationOptions=function defaultMutationOptions(t){return(null==t?void 0:t._defaulted)?t:(0,r.Z)({},this.defaultOptions.mutations,this.getMutationDefaults(null==t?void 0:t.mutationKey),t,{_defaulted:!0})},t.clear=function clear(){this.queryCache.clear(),this.mutationCache.clear()},QueryClient}()},87978:(t,e,n)=>{"use strict";n.d(e,{z:()=>h});var r=n(25773),i=n(88863),s=n(12860),u=n(87226),o=n(85123),a=n(90270),c=n(16129),l=n(98973),h=function(t){function QueryObserver(e,n){var r;return(r=t.call(this)||this).client=e,r.options=n,r.trackedProps=[],r.selectError=null,r.bindMethods(),r.setOptions(n),r}(0,i.Z)(QueryObserver,t);var e=QueryObserver.prototype;return e.bindMethods=function bindMethods(){this.remove=this.remove.bind(this),this.refetch=this.refetch.bind(this)},e.onSubscribe=function onSubscribe(){1===this.listeners.length&&(this.currentQuery.addObserver(this),shouldFetchOnMount(this.currentQuery,this.options)&&this.executeFetch(),this.updateTimers())},e.onUnsubscribe=function onUnsubscribe(){this.listeners.length||this.destroy()},e.shouldFetchOnReconnect=function shouldFetchOnReconnect(){return shouldFetchOn(this.currentQuery,this.options,this.options.refetchOnReconnect)},e.shouldFetchOnWindowFocus=function shouldFetchOnWindowFocus(){return shouldFetchOn(this.currentQuery,this.options,this.options.refetchOnWindowFocus)},e.destroy=function destroy(){this.listeners=[],this.clearTimers(),this.currentQuery.removeObserver(this)},e.setOptions=function setOptions(t,e){var n=this.options,r=this.currentQuery;if(this.options=this.client.defaultQueryObserverOptions(t),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled)throw new Error("Expected enabled to be a boolean");this.options.queryKey||(this.options.queryKey=n.queryKey),this.updateQuery();var i=this.hasListeners();i&&shouldFetchOptionally(this.currentQuery,r,this.options,n)&&this.executeFetch(),this.updateResult(e),!i||this.currentQuery===r&&this.options.enabled===n.enabled&&this.options.staleTime===n.staleTime||this.updateStaleTimeout();var s=this.computeRefetchInterval();!i||this.currentQuery===r&&this.options.enabled===n.enabled&&s===this.currentRefetchInterval||this.updateRefetchInterval(s)},e.getOptimisticResult=function getOptimisticResult(t){var e=this.client.defaultQueryObserverOptions(t),n=this.client.getQueryCache().build(this.client,e);return this.createResult(n,e)},e.getCurrentResult=function getCurrentResult(){return this.currentResult},e.trackResult=function trackResult(t,e){var n=this,r={},i=function trackProp(t){n.trackedProps.includes(t)||n.trackedProps.push(t)};return Object.keys(t).forEach((function(e){Object.defineProperty(r,e,{configurable:!1,enumerable:!0,get:function get(){return i(e),t[e]}})})),(e.useErrorBoundary||e.suspense)&&i("error"),r},e.getNextResult=function getNextResult(t){var e=this;return new Promise((function(n,r){var i=e.subscribe((function(e){e.isFetching||(i(),e.isError&&(null==t?void 0:t.throwOnError)?r(e.error):n(e))}))}))},e.getCurrentQuery=function getCurrentQuery(){return this.currentQuery},e.remove=function remove(){this.client.getQueryCache().remove(this.currentQuery)},e.refetch=function refetch(t){return this.fetch((0,r.Z)({},t,{meta:{refetchPage:null==t?void 0:t.refetchPage}}))},e.fetchOptimistic=function fetchOptimistic(t){var e=this,n=this.client.defaultQueryObserverOptions(t),r=this.client.getQueryCache().build(this.client,n);return r.fetch().then((function(){return e.createResult(r,n)}))},e.fetch=function fetch(t){var e=this;return this.executeFetch(t).then((function(){return e.updateResult(),e.currentResult}))},e.executeFetch=function executeFetch(t){this.updateQuery();var e=this.currentQuery.fetch(this.options,t);return(null==t?void 0:t.throwOnError)||(e=e.catch(s.ZT)),e},e.updateStaleTimeout=function updateStaleTimeout(){var t=this;if(this.clearStaleTimeout(),!s.sk&&!this.currentResult.isStale&&(0,s.PN)(this.options.staleTime)){var e=(0,s.Kp)(this.currentResult.dataUpdatedAt,this.options.staleTime)+1;this.staleTimeoutId=setTimeout((function(){t.currentResult.isStale||t.updateResult()}),e)}},e.computeRefetchInterval=function computeRefetchInterval(){var t;return"function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.currentResult.data,this.currentQuery):null!=(t=this.options.refetchInterval)&&t},e.updateRefetchInterval=function updateRefetchInterval(t){var e=this;this.clearRefetchInterval(),this.currentRefetchInterval=t,!s.sk&&!1!==this.options.enabled&&(0,s.PN)(this.currentRefetchInterval)&&0!==this.currentRefetchInterval&&(this.refetchIntervalId=setInterval((function(){(e.options.refetchIntervalInBackground||o.j.isFocused())&&e.executeFetch()}),this.currentRefetchInterval))},e.updateTimers=function updateTimers(){this.updateStaleTimeout(),this.updateRefetchInterval(this.computeRefetchInterval())},e.clearTimers=function clearTimers(){this.clearStaleTimeout(),this.clearRefetchInterval()},e.clearStaleTimeout=function clearStaleTimeout(){this.staleTimeoutId&&(clearTimeout(this.staleTimeoutId),this.staleTimeoutId=void 0)},e.clearRefetchInterval=function clearRefetchInterval(){this.refetchIntervalId&&(clearInterval(this.refetchIntervalId),this.refetchIntervalId=void 0)},e.createResult=function createResult(t,e){var n,r=this.currentQuery,i=this.options,u=this.currentResult,o=this.currentResultState,a=this.currentResultOptions,l=t!==r,h=l?t.state:this.currentQueryInitialState,f=l?this.currentResult:this.previousQueryResult,d=t.state,v=d.dataUpdatedAt,y=d.error,p=d.errorUpdatedAt,b=d.isFetching,m=d.status,g=!1,O=!1;if(e.optimisticResults){var Q=this.hasListeners(),C=!Q&&shouldFetchOnMount(t,e),P=Q&&shouldFetchOptionally(t,r,e,i);(C||P)&&(b=!0,v||(m="loading"))}if(e.keepPreviousData&&!d.dataUpdateCount&&(null==f?void 0:f.isSuccess)&&"error"!==m)n=f.data,v=f.dataUpdatedAt,m=f.status,g=!0;else if(e.select&&void 0!==d.data)if(u&&d.data===(null==o?void 0:o.data)&&e.select===this.selectFn)n=this.selectResult;else try{this.selectFn=e.select,n=e.select(d.data),!1!==e.structuralSharing&&(n=(0,s.Q$)(null==u?void 0:u.data,n)),this.selectResult=n,this.selectError=null}catch(t){(0,c.j)().error(t),this.selectError=t}else n=d.data;if(void 0!==e.placeholderData&&void 0===n&&("loading"===m||"idle"===m)){var R;if((null==u?void 0:u.isPlaceholderData)&&e.placeholderData===(null==a?void 0:a.placeholderData))R=u.data;else if(R="function"==typeof e.placeholderData?e.placeholderData():e.placeholderData,e.select&&void 0!==R)try{R=e.select(R),!1!==e.structuralSharing&&(R=(0,s.Q$)(null==u?void 0:u.data,R)),this.selectError=null}catch(t){(0,c.j)().error(t),this.selectError=t}void 0!==R&&(m="success",n=R,O=!0)}return this.selectError&&(y=this.selectError,n=this.selectResult,p=Date.now(),m="error"),{status:m,isLoading:"loading"===m,isSuccess:"success"===m,isError:"error"===m,isIdle:"idle"===m,data:n,dataUpdatedAt:v,error:y,errorUpdatedAt:p,failureCount:d.fetchFailureCount,errorUpdateCount:d.errorUpdateCount,isFetched:d.dataUpdateCount>0||d.errorUpdateCount>0,isFetchedAfterMount:d.dataUpdateCount>h.dataUpdateCount||d.errorUpdateCount>h.errorUpdateCount,isFetching:b,isRefetching:b&&"loading"!==m,isLoadingError:"error"===m&&0===d.dataUpdatedAt,isPlaceholderData:O,isPreviousData:g,isRefetchError:"error"===m&&0!==d.dataUpdatedAt,isStale:isStale(t,e),refetch:this.refetch,remove:this.remove}},e.shouldNotifyListeners=function shouldNotifyListeners(t,e){if(!e)return!0;var n=this.options,r=n.notifyOnChangeProps,i=n.notifyOnChangePropsExclusions;if(!r&&!i)return!0;if("tracked"===r&&!this.trackedProps.length)return!0;var s="tracked"===r?this.trackedProps:r;return Object.keys(t).some((function(n){var r=n,u=t[r]!==e[r],o=null==s?void 0:s.some((function(t){return t===n})),a=null==i?void 0:i.some((function(t){return t===n}));return u&&!a&&(!s||o)}))},e.updateResult=function updateResult(t){var e=this.currentResult;if(this.currentResult=this.createResult(this.currentQuery,this.options),this.currentResultState=this.currentQuery.state,this.currentResultOptions=this.options,!(0,s.VS)(this.currentResult,e)){var n={cache:!0};!1!==(null==t?void 0:t.listeners)&&this.shouldNotifyListeners(this.currentResult,e)&&(n.listeners=!0),this.notify((0,r.Z)({},n,t))}},e.updateQuery=function updateQuery(){var t=this.client.getQueryCache().build(this.client,this.options);if(t!==this.currentQuery){var e=this.currentQuery;this.currentQuery=t,this.currentQueryInitialState=t.state,this.previousQueryResult=this.currentResult,this.hasListeners()&&(null==e||e.removeObserver(this),t.addObserver(this))}},e.onQueryUpdate=function onQueryUpdate(t){var e={};"success"===t.type?e.onSuccess=!0:"error"!==t.type||(0,l.DV)(t.error)||(e.onError=!0),this.updateResult(e),this.hasListeners()&&this.updateTimers()},e.notify=function notify(t){var e=this;u.V.batch((function(){t.onSuccess?(null==e.options.onSuccess||e.options.onSuccess(e.currentResult.data),null==e.options.onSettled||e.options.onSettled(e.currentResult.data,null)):t.onError&&(null==e.options.onError||e.options.onError(e.currentResult.error),null==e.options.onSettled||e.options.onSettled(void 0,e.currentResult.error)),t.listeners&&e.listeners.forEach((function(t){t(e.currentResult)})),t.cache&&e.client.getQueryCache().notify({query:e.currentQuery,type:"observerResultsUpdated"})}))},QueryObserver}(a.l);function shouldFetchOnMount(t,e){return function shouldLoadOnMount(t,e){return!(!1===e.enabled||t.state.dataUpdatedAt||"error"===t.state.status&&!1===e.retryOnMount)}(t,e)||t.state.dataUpdatedAt>0&&shouldFetchOn(t,e,e.refetchOnMount)}function shouldFetchOn(t,e,n){if(!1!==e.enabled){var r="function"==typeof n?n(t):n;return"always"===r||!1!==r&&isStale(t,e)}return!1}function shouldFetchOptionally(t,e,n,r){return!1!==n.enabled&&(t!==e||!1===r.enabled)&&(!n.suspense||"error"!==t.state.status)&&isStale(t,n)}function isStale(t,e){return t.isStaleByTime(e.staleTime)}},98973:(t,e,n)=>{"use strict";n.d(e,{DV:()=>isCancelledError,LE:()=>isCancelable,m4:()=>o,p8:()=>u});var r=n(85123),i=n(19240),s=n(12860);function defaultRetryDelay(t){return Math.min(1e3*Math.pow(2,t),3e4)}function isCancelable(t){return"function"==typeof(null==t?void 0:t.cancel)}var u=function CancelledError(t){this.revert=null==t?void 0:t.revert,this.silent=null==t?void 0:t.silent};function isCancelledError(t){return t instanceof u}var o=function Retryer(t){var e,n,o,a,c=this,l=!1;this.abort=t.abort,this.cancel=function(t){return null==e?void 0:e(t)},this.cancelRetry=function(){l=!0},this.continueRetry=function(){l=!1},this.continue=function(){return null==n?void 0:n()},this.failureCount=0,this.isPaused=!1,this.isResolved=!1,this.isTransportCancelable=!1,this.promise=new Promise((function(t,e){o=t,a=e}));var h=function resolve(e){c.isResolved||(c.isResolved=!0,null==t.onSuccess||t.onSuccess(e),null==n||n(),o(e))},f=function reject(e){c.isResolved||(c.isResolved=!0,null==t.onError||t.onError(e),null==n||n(),a(e))};!function run(){if(!c.isResolved){var o;try{o=t.fn()}catch(t){o=Promise.reject(t)}e=function cancelFn(t){if(!c.isResolved&&(f(new u(t)),null==c.abort||c.abort(),isCancelable(o)))try{o.cancel()}catch(t){}},c.isTransportCancelable=isCancelable(o),Promise.resolve(o).then(h).catch((function(e){var u,o;if(!c.isResolved){var a=null!=(u=t.retry)?u:3,h=null!=(o=t.retryDelay)?o:defaultRetryDelay,d="function"==typeof h?h(c.failureCount,e):h,v=!0===a||"number"==typeof a&&c.failureCount<a||"function"==typeof a&&a(c.failureCount,e);!l&&v?(c.failureCount++,null==t.onFail||t.onFail(c.failureCount,e),(0,s.Gh)(d).then((function(){if(!r.j.isFocused()||!i.N.isOnline())return function pause(){return new Promise((function(e){n=e,c.isPaused=!0,null==t.onPause||t.onPause()})).then((function(){n=void 0,c.isPaused=!1,null==t.onContinue||t.onContinue()}))}()})).then((function(){l?f(e):run()}))):f(e)}}))}}()}},90270:(t,e,n)=>{"use strict";n.d(e,{l:()=>r});var r=function(){function Subscribable(){this.listeners=[]}var t=Subscribable.prototype;return t.subscribe=function subscribe(t){var e=this,n=t||function(){};return this.listeners.push(n),this.onSubscribe(),function(){e.listeners=e.listeners.filter((function(t){return t!==n})),e.onUnsubscribe()}},t.hasListeners=function hasListeners(){return this.listeners.length>0},t.onSubscribe=function onSubscribe(){},t.onUnsubscribe=function onUnsubscribe(){},Subscribable}()},46503:()=>{},12860:(t,e,n)=>{"use strict";n.d(e,{A4:()=>scheduleMicrotask,G9:()=>getAbortController,Gh:()=>sleep,I6:()=>parseFilterArgs,Kp:()=>timeUntilStale,PN:()=>isValidTimeout,Q$:()=>replaceEqualDeep,Rc:()=>replaceAt,Rm:()=>hashQueryKeyByOptions,SE:()=>functionalUpdate,VS:()=>shallowEqualObjects,VZ:()=>isError,X7:()=>matchMutation,ZT:()=>noop,_v:()=>parseQueryArgs,_x:()=>matchQuery,cb:()=>parseMutationFilterArgs,e5:()=>difference,lV:()=>parseMutationArgs,mc:()=>ensureQueryKeyArray,sk:()=>i,to:()=>partialMatchKey,yF:()=>hashQueryKey});var r=n(25773),i="undefined"==typeof window;function noop(){}function functionalUpdate(t,e){return"function"==typeof t?t(e):t}function isValidTimeout(t){return"number"==typeof t&&t>=0&&t!==1/0}function ensureQueryKeyArray(t){return Array.isArray(t)?t:[t]}function difference(t,e){return t.filter((function(t){return-1===e.indexOf(t)}))}function replaceAt(t,e,n){var r=t.slice(0);return r[e]=n,r}function timeUntilStale(t,e){return Math.max(t+(e||0)-Date.now(),0)}function parseQueryArgs(t,e,n){return isQueryKey(t)?"function"==typeof e?(0,r.Z)({},n,{queryKey:t,queryFn:e}):(0,r.Z)({},e,{queryKey:t}):t}function parseMutationArgs(t,e,n){return isQueryKey(t)?"function"==typeof e?(0,r.Z)({},n,{mutationKey:t,mutationFn:e}):(0,r.Z)({},e,{mutationKey:t}):"function"==typeof t?(0,r.Z)({},e,{mutationFn:t}):(0,r.Z)({},t)}function parseFilterArgs(t,e,n){return isQueryKey(t)?[(0,r.Z)({},e,{queryKey:t}),n]:[t||{},e]}function parseMutationFilterArgs(t,e){return isQueryKey(t)?(0,r.Z)({},e,{mutationKey:t}):t}function matchQuery(t,e){var n=t.active,r=t.exact,i=t.fetching,s=t.inactive,u=t.predicate,o=t.queryKey,a=t.stale;if(isQueryKey(o))if(r){if(e.queryHash!==hashQueryKeyByOptions(o,e.options))return!1}else if(!partialMatchKey(e.queryKey,o))return!1;var c=function mapQueryStatusFilter(t,e){return!0===t&&!0===e||null==t&&null==e?"all":!1===t&&!1===e?"none":(null!=t?t:!e)?"active":"inactive"}(n,s);if("none"===c)return!1;if("all"!==c){var l=e.isActive();if("active"===c&&!l)return!1;if("inactive"===c&&l)return!1}return("boolean"!=typeof a||e.isStale()===a)&&(("boolean"!=typeof i||e.isFetching()===i)&&!(u&&!u(e)))}function matchMutation(t,e){var n=t.exact,r=t.fetching,i=t.predicate,s=t.mutationKey;if(isQueryKey(s)){if(!e.options.mutationKey)return!1;if(n){if(hashQueryKey(e.options.mutationKey)!==hashQueryKey(s))return!1}else if(!partialMatchKey(e.options.mutationKey,s))return!1}return("boolean"!=typeof r||"loading"===e.state.status===r)&&!(i&&!i(e))}function hashQueryKeyByOptions(t,e){return((null==e?void 0:e.queryKeyHashFn)||hashQueryKey)(t)}function hashQueryKey(t){return function stableValueHash(t){return JSON.stringify(t,(function(t,e){return isPlainObject(e)?Object.keys(e).sort().reduce((function(t,n){return t[n]=e[n],t}),{}):e}))}(ensureQueryKeyArray(t))}function partialMatchKey(t,e){return partialDeepEqual(ensureQueryKeyArray(t),ensureQueryKeyArray(e))}function partialDeepEqual(t,e){return t===e||typeof t==typeof e&&(!(!t||!e||"object"!=typeof t||"object"!=typeof e)&&!Object.keys(e).some((function(n){return!partialDeepEqual(t[n],e[n])})))}function replaceEqualDeep(t,e){if(t===e)return t;var n=Array.isArray(t)&&Array.isArray(e);if(n||isPlainObject(t)&&isPlainObject(e)){for(var r=n?t.length:Object.keys(t).length,i=n?e:Object.keys(e),s=i.length,u=n?[]:{},o=0,a=0;a<s;a++){var c=n?a:i[a];u[c]=replaceEqualDeep(t[c],e[c]),u[c]===t[c]&&o++}return r===s&&o===r?t:u}return e}function shallowEqualObjects(t,e){if(t&&!e||e&&!t)return!1;for(var n in t)if(t[n]!==e[n])return!1;return!0}function isPlainObject(t){if(!hasObjectPrototype(t))return!1;var e=t.constructor;if(void 0===e)return!0;var n=e.prototype;return!!hasObjectPrototype(n)&&!!n.hasOwnProperty("isPrototypeOf")}function hasObjectPrototype(t){return"[object Object]"===Object.prototype.toString.call(t)}function isQueryKey(t){return"string"==typeof t||Array.isArray(t)}function isError(t){return t instanceof Error}function sleep(t){return new Promise((function(e){setTimeout(e,t)}))}function scheduleMicrotask(t){Promise.resolve().then(t).catch((function(t){return setTimeout((function(){throw t}))}))}function getAbortController(){if("function"==typeof AbortController)return new AbortController}},56552:(t,e,n)=>{"use strict";n.r(e);var r=n(30795),i={};for(const t in r)"default"!==t&&(i[t]=()=>r[t]);n.d(e,i);var s=n(43606);i={};for(const t in s)["default","CancelledError","QueryCache","QueryClient","QueryObserver","QueriesObserver","InfiniteQueryObserver","MutationCache","MutationObserver","setLogger","notifyManager","focusManager","onlineManager","hashQueryKey","isError","isCancelledError","dehydrate","hydrate"].indexOf(t)<0&&(i[t]=()=>s[t]);n.d(e,i)},61280:(t,e,n)=>{"use strict";n.d(e,{p:()=>o,s:()=>useHydrate});var r=n(87363),i=n.n(r),s=n(9975),u=n(4667);function useHydrate(t,e){var n=(0,u.N)(),r=i().useRef(e);r.current=e,i().useMemo((function(){t&&(0,s.Z)(n,t,r.current)}),[n,t])}var o=function Hydrate(t){var e=t.children,n=t.options;return useHydrate(t.state,n),e}},4667:(t,e,n)=>{"use strict";n.d(e,{N:()=>o,a:()=>a});var r=n(87363),i=n.n(r),s=i().createContext(void 0),u=i().createContext(!1);function getQueryClientContext(t){return t&&"undefined"!=typeof window?(window.ReactQueryClientContext||(window.ReactQueryClientContext=s),window.ReactQueryClientContext):s}var o=function useQueryClient(){var t=i().useContext(getQueryClientContext(i().useContext(u)));if(!t)throw new Error("No QueryClient set, use QueryClientProvider to set one");return t},a=function QueryClientProvider(t){var e=t.client,n=t.contextSharing,r=void 0!==n&&n,s=t.children;i().useEffect((function(){return e.mount(),function(){e.unmount()}}),[e]);var o=getQueryClientContext(r);return i().createElement(u.Provider,{value:r},i().createElement(o.Provider,{value:e},s))}},39877:(t,e,n)=>{"use strict";n.d(e,{_:()=>u,k:()=>o});var r=n(87363),i=n.n(r);function createValue(){var t=!1;return{clearReset:function clearReset(){t=!1},reset:function reset(){t=!0},isReset:function isReset(){return t}}}var s=i().createContext(createValue()),u=function useQueryErrorResetBoundary(){return i().useContext(s)},o=function QueryErrorResetBoundary(t){var e=t.children,n=i().useMemo((function(){return createValue()}),[]);return i().createElement(s.Provider,{value:n},"function"==typeof e?e(n):e)}},43606:(t,e,n)=>{"use strict";n.r(e),n.d(e,{Hydrate:()=>h.p,QueryClientProvider:()=>r.a,QueryErrorResetBoundary:()=>i.k,useHydrate:()=>h.s,useInfiniteQuery:()=>l.N,useIsFetching:()=>s.y,useIsMutating:()=>u.B,useMutation:()=>o.D,useQueries:()=>c.h,useQuery:()=>a.a,useQueryClient:()=>r.N,useQueryErrorResetBoundary:()=>i._});n(28861),n(24902);var r=n(4667),i=n(39877),s=n(37074),u=n(54321),o=n(68544),a=n(3988),c=n(96467),l=n(65469),h=n(61280),f=n(68412),d={};for(const t in f)["default","QueryClientProvider","useQueryClient","QueryErrorResetBoundary","useQueryErrorResetBoundary","useIsFetching","useIsMutating","useMutation","useQuery","useQueries","useInfiniteQuery","useHydrate","Hydrate"].indexOf(t)<0&&(d[t]=()=>f[t]);n.d(e,d)},28861:(t,e,n)=>{"use strict";var r=n(87226),i=n(61533),s=n.n(i)().unstable_batchedUpdates;r.V.setBatchNotifyFunction(s)},24902:(t,e,n)=>{"use strict";var r=n(16129),i=console;(0,r.E)(i)},68412:()=>{},85055:(t,e,n)=>{"use strict";n.d(e,{r:()=>useBaseQuery});var r=n(87363),i=n.n(r),s=n(87226),u=n(39877),o=n(4667),a=n(75108);function useBaseQuery(t,e){var n=i().useRef(!1),r=i().useState(0)[1],c=(0,o.N)(),l=(0,u._)(),h=c.defaultQueryObserverOptions(t);h.optimisticResults=!0,h.onError&&(h.onError=s.V.batchCalls(h.onError)),h.onSuccess&&(h.onSuccess=s.V.batchCalls(h.onSuccess)),h.onSettled&&(h.onSettled=s.V.batchCalls(h.onSettled)),h.suspense&&("number"!=typeof h.staleTime&&(h.staleTime=1e3),0===h.cacheTime&&(h.cacheTime=1)),(h.suspense||h.useErrorBoundary)&&(l.isReset()||(h.retryOnMount=!1));var f=i().useState((function(){return new e(c,h)}))[0],d=f.getOptimisticResult(h);if(i().useEffect((function(){n.current=!0,l.clearReset();var t=f.subscribe(s.V.batchCalls((function(){n.current&&r((function(t){return t+1}))})));return f.updateResult(),function(){n.current=!1,t()}}),[l,f]),i().useEffect((function(){f.setOptions(h,{listeners:!1})}),[h,f]),h.suspense&&d.isLoading)throw f.fetchOptimistic(h).then((function(t){var e=t.data;null==h.onSuccess||h.onSuccess(e),null==h.onSettled||h.onSettled(e,null)})).catch((function(t){l.clearReset(),null==h.onError||h.onError(t),null==h.onSettled||h.onSettled(void 0,t)}));if(d.isError&&!l.isReset()&&!d.isFetching&&(0,a.L)(h.suspense,h.useErrorBoundary,[d.error,f.getCurrentQuery()]))throw d.error;return"tracked"===h.notifyOnChangeProps&&(d=f.trackResult(d,h)),d}},65469:(t,e,n)=>{"use strict";n.d(e,{N:()=>useInfiniteQuery});var r=n(15830),i=n(12860),s=n(85055);function useInfiniteQuery(t,e,n){var u=(0,i._v)(t,e,n);return(0,s.r)(u,r.c)}},37074:(t,e,n)=>{"use strict";n.d(e,{y:()=>useIsFetching});var r=n(87363),i=n.n(r),s=n(87226),u=n(12860),o=n(4667),a=function checkIsFetching(t,e,n,r){var i=t.isFetching(e);n!==i&&r(i)};function useIsFetching(t,e){var n=i().useRef(!1),r=(0,o.N)(),c=(0,u.I6)(t,e)[0],l=i().useState(r.isFetching(c)),h=l[0],f=l[1],d=i().useRef(c);d.current=c;var v=i().useRef(h);return v.current=h,i().useEffect((function(){n.current=!0,a(r,d.current,v.current,f);var t=r.getQueryCache().subscribe(s.V.batchCalls((function(){n.current&&a(r,d.current,v.current,f)})));return function(){n.current=!1,t()}}),[r]),h}},54321:(t,e,n)=>{"use strict";n.d(e,{B:()=>useIsMutating});var r=n(87363),i=n.n(r),s=n(87226),u=n(12860),o=n(4667);function useIsMutating(t,e){var n=i().useRef(!1),r=(0,u.cb)(t,e),a=(0,o.N)(),c=i().useState(a.isMutating(r)),l=c[0],h=c[1],f=i().useRef(r);f.current=r;var d=i().useRef(l);return d.current=l,i().useEffect((function(){n.current=!0;var t=a.getMutationCache().subscribe(s.V.batchCalls((function(){if(n.current){var t=a.isMutating(f.current);d.current!==t&&h(t)}})));return function(){n.current=!1,t()}}),[a]),l}},68544:(t,e,n)=>{"use strict";n.d(e,{D:()=>useMutation});var r=n(25773),i=n(87363),s=n.n(i),u=n(87226),o=n(12860),a=n(40424),c=n(4667),l=n(75108);function useMutation(t,e,n){var i=s().useRef(!1),h=s().useState(0)[1],f=(0,o.lV)(t,e,n),d=(0,c.N)(),v=s().useRef();v.current?v.current.setOptions(f):v.current=new a.X(d,f);var y=v.current.getCurrentResult();s().useEffect((function(){i.current=!0;var t=v.current.subscribe(u.V.batchCalls((function(){i.current&&h((function(t){return t+1}))})));return function(){i.current=!1,t()}}),[]);var p=s().useCallback((function(t,e){v.current.mutate(t,e).catch(o.ZT)}),[]);if(y.error&&(0,l.L)(void 0,v.current.options.useErrorBoundary,[y.error]))throw y.error;return(0,r.Z)({},y,{mutate:p,mutateAsync:y.mutate})}},96467:(t,e,n)=>{"use strict";n.d(e,{h:()=>useQueries});var r=n(87363),i=n.n(r),s=n(87226),u=n(27282),o=n(4667);function useQueries(t){var e=i().useRef(!1),n=i().useState(0)[1],a=(0,o.N)(),c=(0,r.useMemo)((function(){return t.map((function(t){var e=a.defaultQueryObserverOptions(t);return e.optimisticResults=!0,e}))}),[t,a]),l=i().useState((function(){return new u.y(a,c)}))[0],h=l.getOptimisticResult(c);return i().useEffect((function(){e.current=!0;var t=l.subscribe(s.V.batchCalls((function(){e.current&&n((function(t){return t+1}))})));return function(){e.current=!1,t()}}),[l]),i().useEffect((function(){l.setQueries(c,{listeners:!1})}),[c,l]),h}},3988:(t,e,n)=>{"use strict";n.d(e,{a:()=>useQuery});var r=n(87978),i=n(12860),s=n(85055);function useQuery(t,e,n){var u=(0,i._v)(t,e,n);return(0,s.r)(u,r.z)}},75108:(t,e,n)=>{"use strict";function shouldThrowError(t,e,n){return"function"==typeof e?e.apply(void 0,n):"boolean"==typeof e?e:!!t}n.d(e,{L:()=>shouldThrowError})},25773:(t,e,n)=>{"use strict";function _extends(){return _extends=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},_extends.apply(this,arguments)}n.d(e,{Z:()=>_extends})},88863:(t,e,n)=>{"use strict";function _setPrototypeOf(t,e){return _setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function _setPrototypeOf(t,e){return t.__proto__=e,t},_setPrototypeOf(t,e)}function _inheritsLoose(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t,_setPrototypeOf(t,e)}n.d(e,{Z:()=>_inheritsLoose})}}]);