!function(){"use strict";var e={d:function(t,n){for(var r in n)e.o(n,r)&&!e.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:n[r]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{documentOptionsMenu:function(){return C},injectIntoPageIndication:function(){return h},injectIntoPrimaryAction:function(){return A},injectIntoResponsive:function(){return P},mainMenu:function(){return T},toolsMenu:function(){return M},utilitiesMenu:function(){return w}});var n=window.React,r=window.__UNSTABLE__elementorPackages.locations,o=window.__UNSTABLE__elementorPackages.ui,i=window.__UNSTABLE__elementorPackages.icons,l=window.wp.i18n,c=window.__UNSTABLE__elementorPackages.editor,a=window.__UNSTABLE__elementorPackages.editorV1Adapters,s=window.__UNSTABLE__elementorPackages.editorDocuments,u=(0,n.createContext)({type:"toolbar"});function m({type:e,children:t}){return n.createElement(u.Provider,{value:{type:e}},t)}function p(){return(0,n.useContext)(u)}function d({title:e,...t}){return n.createElement(o.Tooltip,{title:e},n.createElement(o.Box,{component:"span","aria-label":void 0},n.createElement(o.IconButton,{...t,"aria-label":e,size:"small"})))}var g=(0,o.withDirection)(i.ArrowUpRightIcon);function v({text:e,icon:t,onClick:r,href:i,target:l,disabled:c,...a}){const s=i&&"_blank"===l;return n.createElement(o.MenuItem,{...a,disabled:c,onClick:r,component:i?"a":"div",href:i,target:l},n.createElement(o.ListItemIcon,null,t),n.createElement(o.ListItemText,{primary:e}),s&&n.createElement(g,null))}function E({icon:e,title:t,visible:r=!0,...o}){const{type:i}=p();return r?"toolbar"===i?n.createElement(d,{title:t,...o},n.createElement(e,null)):n.createElement(v,{...o,text:t,icon:n.createElement(e,null)}):null}function f({title:e,onClick:t,...r}){return n.createElement(o.Tooltip,{title:e},n.createElement(o.Box,{component:"span","aria-label":void 0},n.createElement(o.ToggleButton,{...r,onChange:t,"aria-label":e,size:"small"})))}function y({icon:e,title:t,value:r,visible:o=!0,...i}){const{type:l}=p();return o?"toolbar"===l?n.createElement(f,{value:r||t,title:t,...i},n.createElement(e,null)):n.createElement(v,{...i,text:t,icon:n.createElement(e,null)}):null}function _({icon:e,title:t,visible:r=!0,...o}){const{type:i}=p();return r?"toolbar"===i?n.createElement(d,{title:t,...o},n.createElement(e,null)):n.createElement(v,{...o,text:t,icon:n.createElement(e,null)}):null}function b(e=[]){const t=[...e,"default"],o=t.reduce(((e,t)=>({...e,[t]:(0,r.createLocation)()})),{}),[i,l,c]=[E,y,_].map((e=>function({locations:e,menuGroups:t,component:r}){return({group:o="default",id:i,overwrite:l,priority:c,...a})=>{if(!t.includes(o))return;const s="props"in a?()=>a.props:a.useProps,u=r;e[o].inject({id:i,filler:e=>{const t=s();return n.createElement(u,{...e,...t})},options:{priority:c,overwrite:l}})}}({locations:o,menuGroups:t,component:e}))),a=function(e){return()=>(0,n.useMemo)((()=>Object.entries(e).reduce(((e,[t,n])=>({...e,[t]:n.getInjections().map((e=>({id:e.id,MenuItem:e.filler})))})),{})),[])}(o);return{registerAction:i,registerToggleAction:l,registerLink:c,useMenuItems:a}}var{inject:h,Slot:k}=(0,r.createLocation)(),{inject:P,Slot:S}=(0,r.createLocation)(),{inject:A,Slot:I}=(0,r.createLocation)(),T=b(["exits"]),M=b(),w=b(),C=b(["save"]);function x({children:e,...t}){return n.createElement(m,{type:"popover"},n.createElement(o.Menu,{PaperProps:{sx:{mt:4}},...t,MenuListProps:{component:"div"}},e))}var B=(0,o.styled)(o.ToggleButton)((()=>({padding:0,"&.MuiToggleButton-root:hover":{backgroundColor:"initial"},"&.MuiToggleButton-root.Mui-selected":{backgroundColor:"initial"}}))),D=(0,o.styled)((e=>n.createElement(o.SvgIcon,{viewBox:"0 0 32 32",...e},n.createElement("g",null,n.createElement("circle",{cx:"16",cy:"16",r:"16"}),n.createElement("path",{d:"M11.7 9H9V22.3H11.7V9Z"}),n.createElement("path",{d:"M22.4 9H9V11.7H22.4V9Z"}),n.createElement("path",{d:"M22.4 14.4004H9V17.1004H22.4V14.4004Z"}),n.createElement("path",{d:"M22.4 19.6992H9V22.3992H22.4V19.6992Z"})))),{shouldForwardProp:e=>"showMenuIcon"!==e})((({theme:e,showMenuIcon:t})=>({width:"auto",height:"100%","& path":{fill:"initial",transition:"all 0.2s linear",transformOrigin:"bottom left","&:first-of-type":{transitionDelay:!t&&"0.2s",transform:t&&"translateY(-9px) scaleY(0)"},"&:not(:first-of-type)":{transform:!t&&`translateX(${"rtl"===e.direction?"4":"9"}px) scaleX(0.6)`},"&:nth-of-type(2)":{transitionDelay:t?"0":"0.2s"},"&:nth-of-type(3)":{transitionDelay:"0.1s"},"&:nth-of-type(4)":{transitionDelay:t?"0.2s":"0"}}})));function L(e){const[t,r]=(0,n.useState)(!1),o=e.selected||t;return n.createElement(B,{...e,value:"selected",size:"small",onMouseEnter:()=>r(!0),onMouseLeave:()=>r(!1)},n.createElement(D,{titleAccess:(0,l.__)("Elementor Logo","elementor"),showMenuIcon:o}))}var{useMenuItems:R}=T;function O(){const e=R(),t=[e.default,e.exits],r=(0,o.usePopupState)({variant:"popover",popupId:"elementor-v2-app-bar-main-menu"});return n.createElement(o.Stack,{sx:{paddingInlineStart:4},direction:"row",alignItems:"center"},n.createElement(L,{...(0,o.bindTrigger)(r),selected:r.isOpen}),n.createElement(x,{onClick:r.close,...(0,o.bindMenu)(r),PaperProps:{sx:{mt:4,marginInlineStart:-2}}},t.filter((e=>e.length)).map(((e,t)=>[t>0?n.createElement(o.Divider,{key:t,orientation:"horizontal"}):null,...e.map((({MenuItem:e,id:t})=>n.createElement(e,{key:t})))]))))}function j({children:e,...t}){return n.createElement(m,{type:"toolbar"},n.createElement(o.Stack,{sx:{px:4},spacing:4,direction:"row",alignItems:"center",...t},e))}function H({children:e,id:t}){const r=(0,o.usePopupState)({variant:"popover",popupId:t});return n.createElement(n.Fragment,null,n.createElement(d,{...(0,o.bindTrigger)(r),title:(0,l.__)("More","elementor")},n.createElement(i.DotsVerticalIcon,null)),n.createElement(x,{onClick:r.close,...(0,o.bindMenu)(r)},e))}var V=5,{useMenuItems:z}=M;function U(){const e=z(),t=e.default.slice(0,V),r=e.default.slice(V);return n.createElement(j,null,t.map((({MenuItem:e,id:t})=>n.createElement(e,{key:t}))),r.length>0&&n.createElement(H,{id:"elementor-editor-app-bar-tools-more"},r.map((({MenuItem:e,id:t})=>n.createElement(e,{key:t})))))}var N=3,{useMenuItems:F}=w;function G(){const e=F(),t=e.default.slice(0,N),r=e.default.slice(N);return n.createElement(j,null,t.map((({MenuItem:e,id:t},r)=>n.createElement(n.Fragment,{key:t},0===r&&n.createElement(o.Divider,{orientation:"vertical"}),n.createElement(e,null)))),r.length>0&&n.createElement(H,{id:"elementor-editor-app-bar-utilities-more"},r.map((({MenuItem:e,id:t})=>n.createElement(e,{key:t})))))}function K(){return n.createElement(I,null)}function Z(){return n.createElement(k,null)}function W(){return n.createElement(S,null)}function q(){const e=(0,s.useActiveDocument)(),t=(0,s.useHostDocument)(),r=e&&"kit"!==e.type.value?e:t,{isActive:c,isBlocked:u}=(0,a.useRouteStatus)("panel/page-settings");if(!r)return null;const m=(0,l.__)("%s Settings","elementor").replace("%s",r.type.label);return n.createElement(o.Tooltip,{title:m},n.createElement(o.Box,{component:"span","aria-label":void 0},n.createElement(o.ToggleButton,{value:"document-settings",selected:c,disabled:u,onChange:()=>(0,a.openRoute)("panel/page-settings/settings"),"aria-label":m,size:"small"},n.createElement(i.SettingsIcon,null))))}function X(){const e=(0,s.useActiveDocument)();return{icon:i.EyeIcon,title:(0,l.__)("Preview Changes","elementor"),onClick:()=>e&&(0,a.runCommand)("editor/documents/preview",{id:e.id,force:!0})}}function Y(){const e=(0,s.useActiveDocument)(),{saveDraft:t}=(0,s.useActiveDocumentActions)();return{icon:i.FileReportIcon,title:(0,l.__)("Save Draft","elementor"),onClick:t,disabled:!e||e.isSaving||e.isSavingDraft||!e.isDirty}}function $(){const{saveTemplate:e}=(0,s.useActiveDocumentActions)();return{icon:i.FolderIcon,title:(0,l.__)("Save as Template","elementor"),onClick:e}}var{useMenuItems:J}=C,Q=(0,o.styled)(x)`
	& > .MuiPopover-paper > .MuiList-root > .MuiDivider-root {
		&:only-child, /* A divider is being rendered lonely */
		&:last-child, /* The last group renders empty but renders a divider */
		& + .MuiDivider-root /* Multiple dividers due to multiple empty groups */ {
			display: none;
		}
	}
`;function ee(e){const{save:t,default:r}=J();return n.createElement(Q,{...e,anchorOrigin:{vertical:"bottom",horizontal:"right"},transformOrigin:{vertical:"top",horizontal:"right"},PaperProps:{sx:{mt:2,ml:3}}},t.map((({MenuItem:e,id:t},r)=>[r>0&&n.createElement(o.Divider,{key:`${t}-divider`}),n.createElement(e,{key:t})])),r.length>0&&n.createElement(o.Divider,null),r.map((({MenuItem:e,id:t})=>n.createElement(e,{key:t}))))}function te(){const e=(0,s.useActiveDocument)(),{save:t}=(0,s.useActiveDocumentActions)(),r=(0,o.usePopupState)({variant:"popover",popupId:"document-save-options"});if(!e)return null;const c=!function(e){return"kit"!==e.type.value&&(e.isDirty||"draft"===e.status.value)}(e),a=e.isSaving&&!c;return n.createElement(n.Fragment,null,n.createElement(o.ButtonGroup,{size:"large",variant:"contained"},n.createElement(o.Button,{onClick:()=>!e.isSaving&&t(),sx:{width:"120px"},disabled:c},a?n.createElement(o.CircularProgress,null):function(e){return e.userCan.publish?(0,l.__)("Publish","elementor"):(0,l.__)("Submit","elementor")}(e)),n.createElement(o.Tooltip,{title:(0,l.__)("Save Options","elementor"),PopperProps:{sx:{"&.MuiTooltip-popper .MuiTooltip-tooltip.MuiTooltip-tooltipPlacementBottom":{mt:3,mr:1}}}},n.createElement(o.Box,{component:"span","aria-label":void 0},n.createElement(o.Button,{...(0,o.bindTrigger)(r),sx:{px:0},disabled:"kit"===e.type.value,"aria-label":(0,l.__)("Save Options","elementor")},n.createElement(i.ChevronDownIcon,null))))),n.createElement(ee,{...(0,o.bindMenu)(r),onClick:r.close}))}function ne(e){window.elementor?.getPanelView?.()?.getHeaderView?.()?.setTitle?.(e)}function re(e){const t=document.querySelector('.elementor-component-tab[data-tab="categories"]');t&&(t.textContent=e)}function oe(){const{isActive:e,isBlocked:t}=(0,a.useRouteStatus)("panel/elements");return{title:(0,l.__)("Add Element","elementor"),icon:i.PlusIcon,onClick:()=>(0,a.openRoute)("panel/elements/categories"),selected:e,disabled:t}}function ie(){const{isActive:e,isBlocked:t}=(0,a.useRouteStatus)("finder",{blockOnKitRoutes:!1,blockOnPreviewMode:!1});return{title:(0,l.__)("Finder","elementor"),icon:i.SearchIcon,onClick:()=>(0,a.runCommand)("finder/toggle"),selected:e,disabled:t}}function le(){const{isActive:e,isBlocked:t}=(0,a.useRouteStatus)("panel/history");return{title:(0,l.__)("History","elementor"),icon:i.HistoryIcon,onClick:()=>(0,a.openRoute)("panel/history/actions"),selected:e,disabled:t}}function ce(){return{icon:i.KeyboardIcon,title:(0,l.__)("Keyboard Shortcuts","elementor"),onClick:()=>(0,a.runCommand)("shortcuts/open")}}function ae(e){const t=(0,a.useListenTo)([(0,a.routeOpenEvent)("panel/global"),(0,a.routeCloseEvent)("panel/global")],se);return t.current?n.createElement(o.Portal,{container:t.current,...e}):null}function se(){return(0,a.isRouteActive)("panel/global")?{current:document.querySelector("#elementor-panel-inner")}:{current:null}}function ue(){const e=(0,s.useActiveDocument)(),{save:t}=(0,s.useActiveDocumentActions)();return n.createElement(o.Paper,{sx:{px:5,py:4,borderTop:1,borderColor:"divider"}},n.createElement(o.Button,{variant:"contained",disabled:!e||!e.isDirty,size:"medium",sx:{width:"100%"},onClick:()=>e&&!e.isSaving?t():null},e?.isSaving?n.createElement(o.CircularProgress,null):(0,l.__)("Save Changes","elementor")))}function me(){return n.createElement(ae,null,n.createElement(ue,null))}function pe(){const{isActive:e,isBlocked:t}=(0,a.useRouteStatus)("panel/global",{blockOnKitRoutes:!1});return{title:(0,l.__)("Site Settings","elementor"),icon:i.AdjustmentsHorizontalIcon,onClick:()=>e?(0,a.runCommand)("panel/global/close"):(0,a.runCommand)("panel/global/open"),selected:e,disabled:t}}function de(){const{isActive:e,isBlocked:t}=(0,a.useRouteStatus)("navigator");return{title:(0,l.__)("Structure","elementor"),icon:i.StructureIcon,onClick:()=>(0,a.runCommand)("navigator/toggle"),selected:e,disabled:t}}function ge(){return{icon:i.ThemeBuilderIcon,title:(0,l.__)("Theme Builder","elementor"),onClick:()=>(0,a.runCommand)("app/open")}}function ve(){const{isActive:e,isBlocked:t}=(0,a.useRouteStatus)("panel/editor-preferences");return{icon:i.ToggleRightIcon,title:(0,l.__)("User Preferences","elementor"),onClick:()=>(0,a.openRoute)("panel/editor-preferences"),selected:e,disabled:t}}(0,a.listenTo)((0,a.routeOpenEvent)("panel/menu"),(()=>{(0,a.openRoute)("panel/elements/categories")})),h({id:"document-settings-button",filler:q,options:{priority:20}}),w.registerAction({id:"document-preview-button",priority:30,useProps:X}),A({id:"document-primary-action",filler:te}),C.registerAction({group:"save",id:"document-save-draft",priority:10,useProps:Y}),C.registerAction({group:"save",id:"document-save-as-template",priority:20,useProps:$}),function(){const e=(0,l.__)("Elements","elementor"),t=(0,l.__)("Widgets","elementor");(0,a.listenTo)((0,a.routeOpenEvent)("panel/elements"),(()=>{ne(e),re(t)})),(0,a.listenTo)((0,a.v1ReadyEvent)(),(()=>{(0,a.isRouteActive)("panel/elements")&&(ne(e),re(t))}))}(),M.registerToggleAction({id:"open-elements-panel",priority:1,useProps:oe}),w.registerToggleAction({id:"toggle-finder",priority:10,useProps:ie}),w.registerLink({id:"open-help-center",priority:20,useProps:()=>({title:(0,l.__)("Help","elementor"),href:"https://go.elementor.com/editor-top-bar-learn/",icon:i.HelpIcon,target:"_blank"})}),T.registerToggleAction({id:"open-history",priority:20,useProps:le}),T.registerAction({id:"open-keyboard-shortcuts",group:"default",priority:40,useProps:ce}),(0,c.injectIntoTop)({id:"site-settings-primary-action-portal",filler:me}),M.registerToggleAction({id:"toggle-site-settings",priority:2,useProps:pe}),M.registerToggleAction({id:"toggle-structure-view",priority:3,useProps:de}),T.registerAction({id:"open-theme-builder",useProps:ge}),T.registerToggleAction({id:"open-user-preferences",priority:30,useProps:ve}),T.registerLink({id:"exit-to-wordpress",group:"exits",useProps:()=>{const e=(0,s.useActiveDocument)();return{title:(0,l.__)("Exit to WordPress","elementor"),href:e?.links?.platformEdit,icon:i.WordpressIcon}}}),(0,c.injectIntoTop)({id:"app-bar",filler:function(){return n.createElement(o.ThemeProvider,{colorScheme:"dark"},n.createElement(o.AppBar,{position:"sticky"},n.createElement(o.Box,{display:"grid",gridTemplateColumns:"repeat(3, 1fr)"},n.createElement(o.Grid,{container:!0},n.createElement(O,null),n.createElement(U,null)),n.createElement(o.Grid,{container:!0,justifyContent:"center"},n.createElement(j,{spacing:3},n.createElement(o.Divider,{orientation:"vertical"}),n.createElement(Z,null),n.createElement(o.Divider,{orientation:"vertical"}),n.createElement(W,null),n.createElement(o.Divider,{orientation:"vertical"}))),n.createElement(o.Grid,{container:!0,justifyContent:"flex-end"},n.createElement(G,null),n.createElement(K,null)))))}}),(window.__UNSTABLE__elementorPackages=window.__UNSTABLE__elementorPackages||{}).editorAppBar=t}();