!function(){"use strict";var e={d:function(t,n){for(var r in n)e.o(n,r)&&!e.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:n[r]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{__privateAppBar:function(){return Q},__privateCreateMenu:function(){return w},__privateInjectIntoPageIndication:function(){return S},__privateInjectIntoPrimaryAction:function(){return T},__privateInjectIntoResponsive:function(){return k},__privateIntegrationsMenu:function(){return V},__privateMainMenu:function(){return j},__privatePopoverMenu:function(){return M},__privateToolsMenu:function(){return B},__privateUtilitiesMenu:function(){return D}});var n=window.React,r=window.elementorV2.locations,o=window.elementorV2.ui,i=window.elementorV2.icons,l=window.wp.i18n,a=window.elementorV2.editorDocuments,c=(0,n.createContext)({type:"toolbar"});function u({type:e,popupState:t,children:r}){return n.createElement(c.Provider,{value:{type:e,popupState:t}},r)}function m(){return(0,n.useContext)(c)}function s({title:e,...t}){return n.createElement(p,{title:e},n.createElement(o.Box,{component:"span","aria-label":void 0},n.createElement(o.IconButton,{...t,"aria-label":e,size:"medium",sx:{"& svg":{fontSize:"1.25rem",height:"1em",width:"1em"},"&:hover":{color:"text.primary"}}})))}function p(e){return n.createElement(o.Tooltip,{PopperProps:{sx:{"&.MuiTooltip-popper .MuiTooltip-tooltip.MuiTooltip-tooltipPlacementBottom":{mt:2}}},...e})}var d=(0,o.withDirection)(i.ArrowUpRightIcon),E=(0,o.withDirection)(i.ChevronRightIcon);function g({text:e,icon:t,onClick:r,href:i,target:l,disabled:a,isGroupParent:c,...u}){const m=i&&"_blank"===l;return n.createElement(o.MenuItem,{...u,disabled:a,onClick:r,component:i?"a":"div",href:i,target:l,sx:{"&:hover":{color:"text.primary"}}},n.createElement(o.ListItemIcon,null,t),n.createElement(o.ListItemText,{primary:e}),m&&n.createElement(d,null),c&&n.createElement(E,null))}function f({icon:e,title:t,visible:r=!0,...o}){const{type:i}=m();return r?"toolbar"===i?n.createElement(s,{title:t,...o},n.createElement(e,null)):n.createElement(g,{...o,text:t,icon:n.createElement(e,null)}):null}function v({title:e,onClick:t,...r}){return n.createElement(o.Tooltip,{title:e},n.createElement(o.Box,{component:"span","aria-label":void 0},n.createElement(o.ToggleButton,{...r,onChange:t,"aria-label":e,size:"small",sx:{border:0,"&.Mui-disabled":{border:0},"& svg":{fontSize:"1.25rem",height:"1em",width:"1em"}}})))}function h({icon:e,title:t,value:r,visible:o=!0,...i}){const{type:l}=m();return o?"toolbar"===l?n.createElement(v,{value:r||t,title:t,...i},n.createElement(e,null)):n.createElement(g,{...i,text:t,icon:n.createElement(e,null)}):null}function b({icon:e,title:t,visible:r=!0,...o}){const{type:i}=m();return r?"toolbar"===i?n.createElement(s,{title:t,...o},n.createElement(e,null)):n.createElement(g,{...o,text:t,icon:n.createElement(e,null)}):null}function M({children:e,popupState:t,...r}){return n.createElement(u,{type:"popover",popupState:t},n.createElement(o.Menu,{PaperProps:{sx:{mt:1.5}},...r,MenuListProps:{component:"div",dense:!0}},e))}function y({children:e,...t}){const r="rtl"===(0,o.useTheme)().direction;return n.createElement(M,{sx:{pointerEvents:"none"},PaperProps:{sx:{...r?{marginInlineEnd:-1}:{marginInlineStart:1},pointerEvents:"auto"}},anchorOrigin:{vertical:"center",horizontal:r?"left":"right"},transformOrigin:{vertical:"center",horizontal:r?"right":"left"},...t},e)}function I({icon:e,title:t,visible:r=!0,items:i,...l}){const a=(0,n.useId)(),{type:c,popupState:u}=m(),p=(0,o.usePopupState)({parentPopupState:u,variant:"popover",popupId:"elementor-v2-app-bar-actions-group-"+a});return r?"toolbar"===c?n.createElement(n.Fragment,null,n.createElement(s,{...(0,o.bindTrigger)(p),title:t,...l},n.createElement(e,null)),n.createElement(M,{onClick:p.close,...(0,o.bindMenu)(p),marginThreshold:8,open:p.isOpen,popupState:p},i.map((({MenuItem:e,id:t})=>n.createElement(e,{key:t}))))):n.createElement(n.Fragment,null,n.createElement(g,{...l,...(0,o.bindHover)(p),...(0,o.bindFocus)(p),text:t,icon:n.createElement(e,null),isGroupParent:!0}),n.createElement(y,{...l,...(0,o.bindPopover)(p),popupState:p},i.map((({MenuItem:e,id:t})=>n.createElement(e,{key:t}))))):null}function w(e=[]){const t=[...e,"default"],o=t.reduce(((e,t)=>({...e,[t]:(0,r.createLocation)()})),{}),[i,l,a]=[f,h,b].map((e=>x({locations:o,menuGroups:t,component:e}))),c=function(e){return()=>(0,n.useMemo)((()=>Object.entries(e).reduce(((e,[t,n])=>({...e,[t]:n.getInjections().map((e=>({id:e.id,MenuItem:e.component})))})),{})),[])}(o),u=function({locations:e,menuGroups:t}){return n=>{const r=w();return x({locations:e,menuGroups:t,component:I,useMenuItems:()=>r.useMenuItems().default})(n),r}}({locations:o,menuGroups:t});return{registerAction:i,registerToggleAction:l,registerLink:a,registerSubMenu:u,useMenuItems:c}}function x({locations:e,menuGroups:t,component:r,useMenuItems:o}){return({group:i="default",id:l,overwrite:a,priority:c,...u})=>{if(!t.includes(i))return;const m="props"in u?()=>u.props:u.useProps,s=r;e[i].inject({id:l,component:e=>{const t=m(),r=o?.();return r?.length?n.createElement(s,{...e,...t,items:r}):n.createElement(s,{...e,...t})},options:{priority:c,overwrite:a}})}}var{inject:S,Slot:_}=(0,r.createLocation)(),{inject:k,Slot:P}=(0,r.createLocation)(),{inject:T,Slot:C}=(0,r.createLocation)(),j=w(["exits"]),B=w(),D=w(),V=w(),L=(0,o.styled)(o.ToggleButton)((({theme:e})=>({padding:0,border:0,color:e.palette.text.primary,"&.MuiToggleButton-root:hover":{backgroundColor:"initial"},"&.MuiToggleButton-root.Mui-selected":{backgroundColor:"initial"}}))),G=(0,o.styled)((e=>n.createElement(o.SvgIcon,{viewBox:"0 0 32 32",...e},n.createElement("g",null,n.createElement("circle",{cx:"16",cy:"16",r:"16"}),n.createElement("path",{d:"M11.7 9H9V22.3H11.7V9Z"}),n.createElement("path",{d:"M22.4 9H9V11.7H22.4V9Z"}),n.createElement("path",{d:"M22.4 14.4004H9V17.1004H22.4V14.4004Z"}),n.createElement("path",{d:"M22.4 19.6992H9V22.3992H22.4V19.6992Z"})))),{shouldForwardProp:e=>"showMenuIcon"!==e})((({theme:e,showMenuIcon:t})=>({"& path":{fill:e.palette.background.default,transition:"all 0.2s linear",transformOrigin:"bottom left","&:first-of-type":{transitionDelay:!t&&"0.2s",transform:t&&"translateY(-9px) scaleY(0)"},"&:not(:first-of-type)":{transform:!t&&`translateX(${"rtl"===e.direction?"4":"9"}px) scaleX(0.6)`},"&:nth-of-type(2)":{transitionDelay:t?"0":"0.2s"},"&:nth-of-type(3)":{transitionDelay:"0.1s"},"&:nth-of-type(4)":{transitionDelay:t?"0.2s":"0"}}})));function O(e){const[t,r]=(0,n.useState)(!1),o=e.selected||t;return n.createElement(L,{...e,value:"selected",size:"large",onMouseEnter:()=>r(!0),onMouseLeave:()=>r(!1)},n.createElement(G,{fontSize:"large",showMenuIcon:o,titleAccess:(0,l.__)("Elementor Logo","elementor")}))}var{useMenuItems:A}=j;function H(){const e=A(),t=(0,o.usePopupState)({variant:"popover",popupId:"elementor-v2-app-bar-main-menu"}),r=(0,o.bindTrigger)(t);return n.createElement(o.Stack,{sx:{paddingInlineStart:3},direction:"row",alignItems:"center"},n.createElement(O,{...r,onClick:e=>{const t=window,n=t?.elementor?.editorEvents?.config;n&&t.elementor.editorEvents.dispatchEvent(n.names.topBar.elementorLogoDropdown,{location:n.locations.topBar,secondaryLocation:n.secondaryLocations.elementorLogo,trigger:n.triggers.dropdownClick,element:n.elements.buttonIcon}),r.onClick(e)},selected:t.isOpen}),n.createElement(M,{onClick:t.close,...(0,o.bindMenu)(t),marginThreshold:8},e.default.map((({MenuItem:e,id:t})=>n.createElement(e,{key:t}))),e.exits.length>0&&n.createElement(o.Divider,null),e.exits.map((({MenuItem:e,id:t})=>n.createElement(e,{key:t})))))}function z({children:e,...t}){return n.createElement(u,{type:"toolbar"},n.createElement(o.Stack,{sx:{px:1.5},spacing:1.5,direction:"row",alignItems:"center",...t},e))}function F({children:e,id:t}){const r=(0,o.usePopupState)({variant:"popover",popupId:t});return n.createElement(n.Fragment,null,n.createElement(s,{...(0,o.bindTrigger)(r),title:(0,l.__)("More","elementor")},n.createElement(i.DotsVerticalIcon,null)),n.createElement(M,{onClick:r.close,...(0,o.bindMenu)(r)},e))}var{useMenuItems:R}=V;function Z(){const e=R(),t=(0,o.usePopupState)({variant:"popover",popupId:"elementor-v2-app-bar-integrations"});return 0===e.default.length?null:n.createElement(n.Fragment,null,n.createElement(s,{...(0,o.bindTrigger)(t),title:(0,l.__)("Integrations","elementor")},n.createElement(i.PlugIcon,null)),n.createElement(M,{onClick:t.close,...(0,o.bindMenu)(t),marginThreshold:8,open:t.isOpen},e.default.map((({MenuItem:e,id:t})=>n.createElement(e,{key:t})))))}var U=5,{useMenuItems:X}=B;function Y(){const e=X(),t=e.default.slice(0,U),r=e.default.slice(U);return n.createElement(z,null,t.map((({MenuItem:e,id:t})=>n.createElement(e,{key:t}))),n.createElement(Z,null),r.length>0&&n.createElement(F,{id:"elementor-editor-app-bar-tools-more"},r.map((({MenuItem:e,id:t})=>n.createElement(e,{key:t})))))}var W=4,{useMenuItems:$}=D;function q(){const e=$(),t=e.default.slice(0,W),r=e.default.slice(W);return n.createElement(z,null,t.map((({MenuItem:e,id:t},r)=>n.createElement(n.Fragment,{key:t},0===r&&n.createElement(o.Divider,{orientation:"vertical"}),n.createElement(e,null)))),r.length>0&&n.createElement(F,{id:"elementor-editor-app-bar-utilities-more"},r.map((({MenuItem:e,id:t})=>n.createElement(e,{key:t})))))}function J(){return n.createElement(C,null)}function K(){return n.createElement(_,null)}function N(){return n.createElement(P,null)}function Q(){const e=(0,a.__useActiveDocument)();return n.createElement(o.ThemeProvider,{colorScheme:"dark"},n.createElement(o.AppBar,{position:"sticky"},n.createElement(o.Toolbar,{disableGutters:!0,variant:"dense"},n.createElement(o.Box,{display:"grid",gridTemplateColumns:"repeat(3, 1fr)",flexGrow:1},n.createElement(o.Grid,{container:!0},n.createElement(H,null),e?.permissions?.allowAddingWidgets&&n.createElement(Y,null)),n.createElement(o.Grid,{container:!0,justifyContent:"center"},n.createElement(z,{spacing:1.5},n.createElement(o.Divider,{orientation:"vertical"}),n.createElement(K,null),n.createElement(o.Divider,{orientation:"vertical"}),n.createElement(N,null),n.createElement(o.Divider,{orientation:"vertical"}))),n.createElement(o.Grid,{container:!0,justifyContent:"flex-end"},n.createElement(q,null),n.createElement(J,null))))))}(window.elementorV2=window.elementorV2||{}).editorAppBarUi=t}();