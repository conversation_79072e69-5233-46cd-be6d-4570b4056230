/*! For license information please see editor-app-bar-ui.js.LICENSE.txt */
!function(){"use strict";var e={react:function(e){e.exports=window.React},"@elementor/editor-documents":function(e){e.exports=window.elementorV2.editorDocuments},"@elementor/icons":function(e){e.exports=window.elementorV2.icons},"@elementor/locations":function(e){e.exports=window.elementorV2.locations},"@elementor/ui":function(e){e.exports=window.elementorV2.ui},"@wordpress/i18n":function(e){e.exports=window.wp.i18n}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var i=t[r]={exports:{}};return e[r](i,i.exports,n),i.exports}n.d=function(e,t){for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};!function(){n.r(r),n.d(r,{__privateAppBar:function(){return Q},__privateCreateMenu:function(){return x},__privateInjectIntoPageIndication:function(){return S},__privateInjectIntoPrimaryAction:function(){return T},__privateInjectIntoResponsive:function(){return k},__privateIntegrationsMenu:function(){return V},__privateMainMenu:function(){return j},__privatePopoverMenu:function(){return M},__privateToolsMenu:function(){return B},__privateUtilitiesMenu:function(){return D}});var e=n("react"),t=n("@elementor/locations"),o=n("@elementor/ui"),i=n("@elementor/icons"),l=n("@wordpress/i18n"),a=n("@elementor/editor-documents"),c=(0,e.createContext)({type:"toolbar"});function u({type:t,popupState:n,children:r}){return e.createElement(c.Provider,{value:{type:t,popupState:n}},r)}function m(){return(0,e.useContext)(c)}function s({title:t,...n}){return e.createElement(p,{title:t},e.createElement(o.Box,{component:"span","aria-label":void 0},e.createElement(o.IconButton,{...n,"aria-label":t,size:"medium",sx:{"& svg":{fontSize:"1.25rem",height:"1em",width:"1em"},"&:hover":{color:"text.primary"}}})))}function p(t){return e.createElement(o.Tooltip,{PopperProps:{sx:{"&.MuiTooltip-popper .MuiTooltip-tooltip.MuiTooltip-tooltipPlacementBottom":{mt:2}}},...t})}var d=(0,o.withDirection)(i.ArrowUpRightIcon),E=(0,o.withDirection)(i.ChevronRightIcon);function f({text:t,icon:n,onClick:r,href:i,target:l,disabled:a,isGroupParent:c,...u}){const m=i&&"_blank"===l;return e.createElement(o.MenuItem,{...u,disabled:a,onClick:r,component:i?"a":"div",href:i,target:l,sx:{"&:hover":{color:"text.primary"}}},e.createElement(o.ListItemIcon,null,n),e.createElement(o.ListItemText,{primary:t}),m&&e.createElement(d,null),c&&e.createElement(E,null))}function g({icon:t,title:n,visible:r=!0,...o}){const{type:i}=m();return r?"toolbar"===i?e.createElement(s,{title:n,...o},e.createElement(t,null)):e.createElement(f,{...o,text:n,icon:e.createElement(t,null)}):null}function v({title:t,onClick:n,...r}){return e.createElement(o.Tooltip,{title:t},e.createElement(o.Box,{component:"span","aria-label":void 0},e.createElement(o.ToggleButton,{...r,onChange:n,"aria-label":t,size:"small",sx:{border:0,"&.Mui-disabled":{border:0},"& svg":{fontSize:"1.25rem",height:"1em",width:"1em"}}})))}function h({icon:t,title:n,value:r,visible:o=!0,...i}){const{type:l}=m();return o?"toolbar"===l?e.createElement(v,{value:r||n,title:n,...i},e.createElement(t,null)):e.createElement(f,{...i,text:n,icon:e.createElement(t,null)}):null}function b({icon:t,title:n,visible:r=!0,...o}){const{type:i}=m();return r?"toolbar"===i?e.createElement(s,{title:n,...o},e.createElement(t,null)):e.createElement(f,{...o,text:n,icon:e.createElement(t,null)}):null}function M({children:t,popupState:n,...r}){return e.createElement(u,{type:"popover",popupState:n},e.createElement(o.Menu,{PaperProps:{sx:{mt:1.5}},...r,MenuListProps:{component:"div",dense:!0}},t))}function y({children:t,...n}){const r="rtl"===(0,o.useTheme)().direction;return e.createElement(M,{sx:{pointerEvents:"none"},PaperProps:{sx:{...r?{marginInlineEnd:-1}:{marginInlineStart:1},pointerEvents:"auto"}},anchorOrigin:{vertical:"center",horizontal:r?"left":"right"},transformOrigin:{vertical:"center",horizontal:r?"right":"left"},...n},t)}function I({icon:t,title:n,visible:r=!0,items:i,...l}){const a=(0,e.useId)(),{type:c,popupState:u}=m(),p=(0,o.usePopupState)({parentPopupState:u,variant:"popover",popupId:"elementor-v2-app-bar-actions-group-"+a});return r?"toolbar"===c?e.createElement(e.Fragment,null,e.createElement(s,{...(0,o.bindTrigger)(p),title:n,...l},e.createElement(t,null)),e.createElement(M,{onClick:p.close,...(0,o.bindMenu)(p),marginThreshold:8,open:p.isOpen,popupState:p},i.map((({MenuItem:t,id:n})=>e.createElement(t,{key:n}))))):e.createElement(e.Fragment,null,e.createElement(f,{...l,...(0,o.bindHover)(p),...(0,o.bindFocus)(p),text:n,icon:e.createElement(t,null),isGroupParent:!0}),e.createElement(y,{...l,...(0,o.bindPopover)(p),popupState:p},i.map((({MenuItem:t,id:n})=>e.createElement(t,{key:n}))))):null}function x(n=[]){const r=[...n,"default"],o=r.reduce(((e,n)=>({...e,[n]:(0,t.createLocation)()})),{}),[i,l,a]=[g,h,b].map((e=>w({locations:o,menuGroups:r,component:e}))),c=function(t){return()=>(0,e.useMemo)((()=>Object.entries(t).reduce(((e,[t,n])=>({...e,[t]:n.getInjections().map((e=>({id:e.id,MenuItem:e.component})))})),{})),[])}(o),u=function({locations:e,menuGroups:t}){return n=>{const r=x();return w({locations:e,menuGroups:t,component:I,useMenuItems:()=>r.useMenuItems().default})(n),r}}({locations:o,menuGroups:r});return{registerAction:i,registerToggleAction:l,registerLink:a,registerSubMenu:u,useMenuItems:c}}function w({locations:t,menuGroups:n,component:r,useMenuItems:o}){return({group:i="default",id:l,overwrite:a,priority:c,...u})=>{if(!n.includes(i))return;const m="props"in u?()=>u.props:u.useProps,s=r;t[i].inject({id:l,component:t=>{const n=m(),r=o?.();return r?.length?e.createElement(s,{...t,...n,items:r}):e.createElement(s,{...t,...n})},options:{priority:c,overwrite:a}})}}var{inject:S,Slot:_}=(0,t.createLocation)(),{inject:k,Slot:P}=(0,t.createLocation)(),{inject:T,Slot:C}=(0,t.createLocation)(),j=x(["exits"]),B=x(),D=x(),V=x(),L=(0,o.styled)(o.ToggleButton)((({theme:e})=>({padding:0,border:0,color:e.palette.text.primary,"&.MuiToggleButton-root:hover":{backgroundColor:"initial"},"&.MuiToggleButton-root.Mui-selected":{backgroundColor:"initial"}}))),G=(0,o.styled)((t=>e.createElement(o.SvgIcon,{viewBox:"0 0 32 32",...t},e.createElement("g",null,e.createElement("circle",{cx:"16",cy:"16",r:"16"}),e.createElement("path",{d:"M11.7 9H9V22.3H11.7V9Z"}),e.createElement("path",{d:"M22.4 9H9V11.7H22.4V9Z"}),e.createElement("path",{d:"M22.4 14.4004H9V17.1004H22.4V14.4004Z"}),e.createElement("path",{d:"M22.4 19.6992H9V22.3992H22.4V19.6992Z"})))),{shouldForwardProp:e=>"showMenuIcon"!==e})((({theme:e,showMenuIcon:t})=>({"& path":{fill:e.palette.background.default,transition:"all 0.2s linear",transformOrigin:"bottom left","&:first-of-type":{transitionDelay:!t&&"0.2s",transform:t&&"translateY(-9px) scaleY(0)"},"&:not(:first-of-type)":{transform:!t&&`translateX(${"rtl"===e.direction?"4":"9"}px) scaleX(0.6)`},"&:nth-of-type(2)":{transitionDelay:t?"0":"0.2s"},"&:nth-of-type(3)":{transitionDelay:"0.1s"},"&:nth-of-type(4)":{transitionDelay:t?"0.2s":"0"}}})));function O(t){const[n,r]=(0,e.useState)(!1),o=t.selected||n;return e.createElement(L,{...t,value:"selected",size:"large",onMouseEnter:()=>r(!0),onMouseLeave:()=>r(!1)},e.createElement(G,{fontSize:"large",showMenuIcon:o,titleAccess:(0,l.__)("Elementor Logo","elementor")}))}var{useMenuItems:A}=j;function H(){const t=A(),n=(0,o.usePopupState)({variant:"popover",popupId:"elementor-v2-app-bar-main-menu"}),r=(0,o.bindTrigger)(n);return e.createElement(o.Stack,{sx:{paddingInlineStart:3},direction:"row",alignItems:"center"},e.createElement(O,{...r,onClick:e=>{const t=window,n=t?.elementor?.editorEvents?.config;n&&t.elementor.editorEvents.dispatchEvent(n.names.topBar.elementorLogoDropdown,{location:n.locations.topBar,secondaryLocation:n.secondaryLocations.elementorLogo,trigger:n.triggers.dropdownClick,element:n.elements.buttonIcon}),r.onClick(e)},selected:n.isOpen}),e.createElement(M,{onClick:n.close,...(0,o.bindMenu)(n),marginThreshold:8},t.default.map((({MenuItem:t,id:n})=>e.createElement(t,{key:n}))),t.exits.length>0&&e.createElement(o.Divider,null),t.exits.map((({MenuItem:t,id:n})=>e.createElement(t,{key:n})))))}function z({children:t,...n}){return e.createElement(u,{type:"toolbar"},e.createElement(o.Stack,{sx:{px:1.5},spacing:1.5,direction:"row",alignItems:"center",...n},t))}function F({children:t,id:n}){const r=(0,o.usePopupState)({variant:"popover",popupId:n});return e.createElement(e.Fragment,null,e.createElement(s,{...(0,o.bindTrigger)(r),title:(0,l.__)("More","elementor")},e.createElement(i.DotsVerticalIcon,null)),e.createElement(M,{onClick:r.close,...(0,o.bindMenu)(r)},t))}var{useMenuItems:R}=V;function Z(){const t=R(),n=(0,o.usePopupState)({variant:"popover",popupId:"elementor-v2-app-bar-integrations"});return 0===t.default.length?null:e.createElement(e.Fragment,null,e.createElement(s,{...(0,o.bindTrigger)(n),title:(0,l.__)("Integrations","elementor")},e.createElement(i.PlugIcon,null)),e.createElement(M,{onClick:n.close,...(0,o.bindMenu)(n),marginThreshold:8,open:n.isOpen},t.default.map((({MenuItem:t,id:n})=>e.createElement(t,{key:n})))))}var U=5,{useMenuItems:X}=B;function Y(){const t=X(),n=t.default.slice(0,U),r=t.default.slice(U);return e.createElement(z,null,n.map((({MenuItem:t,id:n})=>e.createElement(t,{key:n}))),e.createElement(Z,null),r.length>0&&e.createElement(F,{id:"elementor-editor-app-bar-tools-more"},r.map((({MenuItem:t,id:n})=>e.createElement(t,{key:n})))))}var W=4,{useMenuItems:$}=D;function q(){const t=$(),n=t.default.slice(0,W),r=t.default.slice(W);return e.createElement(z,null,n.map((({MenuItem:t,id:n},r)=>e.createElement(e.Fragment,{key:n},0===r&&e.createElement(o.Divider,{orientation:"vertical"}),e.createElement(t,null)))),r.length>0&&e.createElement(F,{id:"elementor-editor-app-bar-utilities-more"},r.map((({MenuItem:t,id:n})=>e.createElement(t,{key:n})))))}function J(){return e.createElement(C,null)}function K(){return e.createElement(_,null)}function N(){return e.createElement(P,null)}function Q(){const t=(0,a.__useActiveDocument)();return e.createElement(o.ThemeProvider,{colorScheme:"dark"},e.createElement(o.AppBar,{position:"sticky"},e.createElement(o.Toolbar,{disableGutters:!0,variant:"dense"},e.createElement(o.Box,{display:"grid",gridTemplateColumns:"repeat(3, 1fr)",flexGrow:1},e.createElement(o.Grid,{container:!0},e.createElement(H,null),t?.permissions?.allowAddingWidgets&&e.createElement(Y,null)),e.createElement(o.Grid,{container:!0,justifyContent:"center"},e.createElement(z,{spacing:1.5},e.createElement(o.Divider,{orientation:"vertical"}),e.createElement(K,null),e.createElement(o.Divider,{orientation:"vertical"}),e.createElement(N,null),e.createElement(o.Divider,{orientation:"vertical"}))),e.createElement(o.Grid,{container:!0,justifyContent:"flex-end"},e.createElement(q,null),e.createElement(J,null))))))}}(),(window.elementorV2=window.elementorV2||{}).editorAppBarUi=r}();