!function(){"use strict";var e={d:function(t,n){for(var r in n)e.o(n,r)&&!e.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:n[r]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{__flushAllInjections:function(){return l},createLocation:function(){return a}});var n=window.React,r=class extends n.Component{state={hasError:!1};static getDerivedStateFromError(){return{hasError:!0}}render(){return this.state.hasError?this.props.fallback:this.props.children}};function o({children:e}){return n.createElement(r,{fallback:null},n.createElement(n.Suspense,{fallback:null},e))}var i=10,c=[];function a(){const e=new Map,t=function(e){return()=>[...e.values()].sort(((e,t)=>e.priority-t.priority))}(e),r=function(e){return()=>(0,n.useMemo)((()=>e()),[])}(t),a=function(e){return t=>{const r=e();return n.createElement(n.Fragment,null,r.map((({id:e,component:r})=>n.createElement(r,{...t,key:e}))))}}(r),l=function(e){return({component:t,id:r,options:c={}})=>{var a;!e.has(r)||c?.overwrite?e.set(r,{id:r,component:(a=t,e=>n.createElement(o,null,n.createElement(a,{...e}))),priority:c.priority??i}):console.warn(`An injection with the id "${r}" already exists. Did you mean to use "options.overwrite"?`)}}(e);return c.push((()=>e.clear())),{inject:l,getInjections:t,useInjections:r,Slot:a}}function l(){c.forEach((e=>e()))}(window.elementorV2=window.elementorV2||{}).locations=t}();