!function(){"use strict";var e={};(function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})})(e);var t=window.__UNSTABLE__elementorPackages.store,i=window.__UNSTABLE__elementorPackages.editorV1Adapters,n=window.wp.i18n,o=window.__UNSTABLE__elementorPackages.editorAppBar,a=window.React,l=window.__UNSTABLE__elementorPackages.ui,r=window.__UNSTABLE__elementorPackages.icons;function c(){const{breakpoints:e}=window.elementor?.config?.responsive||{};if(!e)return[];const t=Object.entries(e).filter((([,e])=>e.is_enabled)).map((([e,{value:t,direction:i,label:n}])=>({id:e,label:n,width:t,type:"min"===i?"min-width":"max-width"})));return t.push({id:"desktop",label:(0,n.__)("Desktop","elementor")}),t}function d(){const e=window;return e.elementor?.channels?.deviceMode?.request?.("currentMode")||null}var s=e=>e.breakpoints.entities,p=(0,t.createSelector)(s,(e=>e.breakpoints.activeId),((e,t)=>t&&e[t]?e[t]:null)),u=(0,t.createSelector)(s,(e=>{const t=(e,t)=>e.width&&t.width?t.width-e.width:0,i=Object.values(e),n=i.filter((e=>!e.width)),o=i.filter((e=>"min-width"===e.type)),a=i.filter((e=>"max-width"===e.type));return[...o.sort(t),...n,...a.sort(t)]}));function m(e){return a.createElement(l.Tooltip,{PopperProps:{sx:{"&.MuiTooltip-popper .MuiTooltip-tooltip.MuiTooltip-tooltipPlacementBottom":{mt:7}}},...e})}var w,_={widescreen:r.WidescreenIcon,desktop:r.DesktopIcon,laptop:r.LaptopIcon,tablet_extra:r.TabletLandscapeIcon,tablet:r.TabletPortraitIcon,mobile_extra:r.MobileLandscapeIcon,mobile:r.MobilePortraitIcon},v={default:"%s","min-width":(0,n.__)("%s (%dpx and up)","elementor"),"max-width":(0,n.__)("%s (up to %dpx)","elementor")};(function(e){const{init:n}=e.actions;(0,i.listenTo)((0,i.v1ReadyEvent)(),(()=>{(0,t.dispatch)(n({entities:c(),activeId:d()}))}))})(w=(0,t.addSlice)({name:"breakpoints",initialState:{entities:{},activeId:null},reducers:{init(e,t){e.activeId=t.payload.activeId,e.entities=t.payload.entities.reduce(((e,t)=>({...e,[t.id]:t})),{})},activateBreakpoint(e,t){e.entities[t.payload]&&(e.activeId=t.payload)}}})),function(e){const{activateBreakpoint:n}=e.actions;(0,i.listenTo)((0,i.windowEvent)("elementor/device-mode/change"),(()=>{const e=d();(0,t.dispatch)(n(e))}))}(w),(0,o.injectIntoResponsive)({id:"responsive-breakpoints-switcher",filler:function(){const{all:e,active:o}={all:(0,t.useSelector)(u),active:(0,t.useSelector)(p)},{activate:r}={activate:(0,a.useCallback)((e=>(0,i.runCommand)("panel/change-device-mode",{device:e})),[])};return e.length&&o?a.createElement(l.Tabs,{value:o.id,onChange:(e,t)=>r(t),"aria-label":(0,n.__)("Switch Device","elementor")},e.map((({id:e,label:t,type:i,width:n})=>{const o=_[e],r=v[i||"default"].replace("%s",t).replace("%d",n?.toString()||"");return a.createElement(l.Tab,{value:e,key:e,"aria-label":r,icon:a.createElement(m,{title:r},a.createElement(o,null))})}))):null},options:{priority:20}}),(window.__UNSTABLE__elementorPackages=window.__UNSTABLE__elementorPackages||{}).editorResponsive=e}();