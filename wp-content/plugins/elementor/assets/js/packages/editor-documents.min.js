!function(){"use strict";var t={d:function(e,n){for(var i in n)t.o(n,i)&&!t.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:n[i]})},o:function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r:function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})}},e={};t.r(e),t.d(e,{useActiveDocument:function(){return v},useActiveDocumentActions:function(){return m},useHostDocument:function(){return g},useNavigateToDocument:function(){return f}});var n=window.__UNSTABLE__elementorPackages.store,i=window.__UNSTABLE__elementorPackages.editorV1Adapters,a=window.React;function o(){const t=window.elementor?.documents;if(!t)throw new Error("Elementor Editor V1 documents manager not found");return t}function s(t){const e=t.config.revisions.current_id!==t.id;return{id:t.id,title:t.container.settings.get("post_title"),type:{value:t.config.type,label:t.config.panel.title},status:{value:t.config.status.value,label:t.config.status.label},links:{platformEdit:t.config.urls.exit_to_dashboard},isDirty:t.editor.isChanged||e,isSaving:t.editor.isSaving,isSavingDraft:!1,userCan:{publish:t.config.user.can_publish}}}function c(t){return!(!t.activeId||!t.entities[t.activeId])}var r,d=t=>t.documents.entities,u=(0,n.createSelector)(d,(t=>t.documents.activeId),((t,e)=>e&&t[e]?t[e]:null)),l=(0,n.createSelector)(d,(t=>t.documents.hostId),((t,e)=>e&&t[e]?t[e]:null));function v(){return(0,n.useSelector)(u)}function m(){return{save:(0,a.useCallback)((()=>(0,i.runCommand)("document/save/default")),[]),saveDraft:(0,a.useCallback)((()=>(0,i.runCommand)("document/save/draft")),[]),saveTemplate:(0,a.useCallback)((()=>(0,i.openRoute)("library/save-template")),[])}}function g(){return(0,n.useSelector)(l)}function f(){return(0,a.useCallback)((t=>(0,i.runCommand)("editor/documents/switch",{id:t,setAsInitial:!0})),[])}(function(t){const{init:e}=t.actions;(0,i.listenTo)((0,i.v1ReadyEvent)(),(()=>{const t=o(),i=Object.entries(t.documents).reduce(((t,[e,n])=>(t[e]=s(n),t)),{});(0,n.dispatch)(e({entities:i,hostId:t.getInitialId(),activeId:t.getCurrentId()}))}))})(r=(0,n.addSlice)({name:"documents",initialState:{entities:{},activeId:null,hostId:null},reducers:{init(t,{payload:e}){t.entities=e.entities,t.hostId=e.hostId,t.activeId=e.activeId},activateDocument(t,e){t.entities[e.payload.id]=e.payload,t.activeId=e.payload.id},setAsHost(t,e){t.hostId=e.payload},updateActiveDocument(t,e){c(t)&&(t.entities[t.activeId]={...t.entities[t.activeId],...e.payload})},startSaving(t){c(t)&&(t.entities[t.activeId].isSaving=!0)},endSaving(t,e){c(t)&&(t.entities[t.activeId]={...e.payload,isSaving:!1})},startSavingDraft:t=>{c(t)&&(t.entities[t.activeId].isSavingDraft=!0)},endSavingDraft(t,e){c(t)&&(t.entities[t.activeId]={...e.payload,isSavingDraft:!1})},markAsDirty(t){c(t)&&(t.entities[t.activeId].isDirty=!0)},markAsPristine(t){c(t)&&(t.entities[t.activeId].isDirty=!1)}}})),function(t){const{activateDocument:e,setAsHost:a}=t.actions;(0,i.listenTo)((0,i.commandEndEvent)("editor/documents/open"),(()=>{const t=o(),i=s(t.getCurrent());(0,n.dispatch)(e(i)),t.getInitialId()===i.id&&(0,n.dispatch)(a(i.id))}))}(r),function(t){const{startSaving:e,endSaving:a,startSavingDraft:c,endSavingDraft:r}=t.actions,d=t=>{const e=t;return"autosave"===e.args?.status};(0,i.listenTo)((0,i.commandStartEvent)("document/save/save"),(t=>{d(t)?(0,n.dispatch)(c()):(0,n.dispatch)(e())})),(0,i.listenTo)((0,i.commandEndEvent)("document/save/save"),(t=>{const e=s(o().getCurrent());d(t)?(0,n.dispatch)(r(e)):(0,n.dispatch)(a(e))}))}(r),function(t){const{updateActiveDocument:e}=t.actions,a=function(t,i){let a;return(...t)=>{clearTimeout(a),a=setTimeout((()=>{(t=>{const i=t;if(!("post_title"in i.args?.settings))return;const a=o().getCurrent().container.settings.get("post_title");(0,n.dispatch)(e({title:a}))})(...t)}),400)}}();(0,i.listenTo)((0,i.commandEndEvent)("document/elements/settings"),a)}(r),function(t){const{markAsDirty:e,markAsPristine:a}=t.actions;(0,i.listenTo)((0,i.commandEndEvent)("document/save/set-is-modified"),(()=>{o().getCurrent().editor.isChanged?(0,n.dispatch)(e()):(0,n.dispatch)(a())}))}(r),(window.__UNSTABLE__elementorPackages=window.__UNSTABLE__elementorPackages||{}).editorDocuments=e}();