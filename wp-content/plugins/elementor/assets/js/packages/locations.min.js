!function(){"use strict";var e={d:function(t,r){for(var n in r)e.o(r,n)&&!e.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:r[n]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{createLocation:function(){return l},flushAllInjections:function(){return c}});var r=window.React,n=class extends r.Component{state={hasError:!1};static getDerivedStateFromError(){return{hasError:!0}}render(){return this.state.hasError?this.props.fallback:this.props.children}};function o({children:e}){return r.createElement(n,{fallback:null},r.createElement(r.Suspense,{fallback:null},e))}var i=10,a=[];function l(){const e=new Map,t=function(e){return()=>[...e.values()].sort(((e,t)=>e.priority-t.priority))}(e),n=function(e){return()=>(0,r.useMemo)((()=>e()),[])}(t),l=function(e){return t=>{const n=e();return r.createElement(r.Fragment,null,n.map((({id:e,filler:n})=>r.createElement(n,{...t,key:e}))))}}(n),c=function(e){return({filler:t,id:n,options:a={}})=>{var l;!e.has(n)||a?.overwrite?e.set(n,{id:n,filler:(l=t,e=>r.createElement(o,null,r.createElement(l,{...e}))),priority:a.priority??i}):console.error(`An injection with the id "${n}" already exists. Did you mean to use "options.overwrite"?`)}}(e);return a.push((()=>e.clear())),{inject:c,getInjections:t,useInjections:n,Slot:l}}function c(){a.forEach((e=>e()))}(window.__UNSTABLE__elementorPackages=window.__UNSTABLE__elementorPackages||{}).locations=t}();