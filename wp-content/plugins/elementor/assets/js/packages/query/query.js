/*! For license information please see query.js.LICENSE.txt */
!function(){"use strict";var e={react:function(e){e.exports=window.React},"./node_modules/@tanstack/query-core/build/modern/focusManager.js":function(e,t,r){r.r(t),r.d(t,{FocusManager:function(){return i},focusManager:function(){return o}});var s=r("./node_modules/@tanstack/query-core/build/modern/subscribable.js"),n=r("./node_modules/@tanstack/query-core/build/modern/utils.js"),i=class extends s.Subscribable{#e;#t;#r;constructor(){super(),this.#r=e=>{if(!n.isServer&&window.addEventListener){const t=()=>e();return window.addEventListener("visibilitychange",t,!1),()=>{window.removeEventListener("visibilitychange",t)}}}}onSubscribe(){this.#t||this.setEventListener(this.#r)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#r=e,this.#t?.(),this.#t=e((e=>{"boolean"==typeof e?this.setFocused(e):this.onFocus()}))}setFocused(e){this.#e!==e&&(this.#e=e,this.onFocus())}onFocus(){this.listeners.forEach((e=>{e()}))}isFocused(){return"boolean"==typeof this.#e?this.#e:"hidden"!==globalThis.document?.visibilityState}},o=new i},"./node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js":function(e,t,r){r.r(t),r.d(t,{hasNextPage:function(){return u},hasPreviousPage:function(){return a},infiniteQueryBehavior:function(){return n}});var s=r("./node_modules/@tanstack/query-core/build/modern/utils.js");function n(e){return{onFetch:(t,r)=>{const n=async()=>{const r=t.options,n=t.fetchOptions?.meta?.fetchMore?.direction,u=t.state.data?.pages||[],a=t.state.data?.pageParams||[],c={pages:[],pageParams:[]};let h=!1;const d=t.options.queryFn||(()=>Promise.reject(new Error(`Missing queryFn: '${t.options.queryHash}'`))),l=async(e,r,n)=>{if(h)return Promise.reject();if(null==r&&e.pages.length)return Promise.resolve(e);const i={queryKey:t.queryKey,pageParam:r,direction:n?"backward":"forward",meta:t.options.meta};var o;o=i,Object.defineProperty(o,"signal",{enumerable:!0,get:()=>(t.signal.aborted?h=!0:t.signal.addEventListener("abort",(()=>{h=!0})),t.signal)});const u=await d(i),{maxPages:a}=t.options,c=n?s.addToStart:s.addToEnd;return{pages:c(e.pages,u,a),pageParams:c(e.pageParams,r,a)}};let f;if(n&&u.length){const e="backward"===n,t={pages:u,pageParams:a},s=(e?o:i)(r,t);f=await l(t,s,e)}else{f=await l(c,a[0]??r.initialPageParam);const t=e??u.length;for(let e=1;e<t;e++){const e=i(r,f);f=await l(f,e)}}return f};t.options.persister?t.fetchFn=()=>t.options.persister?.(n,{queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},r):t.fetchFn=n}}}function i(e,{pages:t,pageParams:r}){const s=t.length-1;return e.getNextPageParam(t[s],t,r[s],r)}function o(e,{pages:t,pageParams:r}){return e.getPreviousPageParam?.(t[0],t,r[0],r)}function u(e,t){return!!t&&null!=i(e,t)}function a(e,t){return!(!t||!e.getPreviousPageParam)&&null!=o(e,t)}},"./node_modules/@tanstack/query-core/build/modern/infiniteQueryObserver.js":function(e,t,r){r.r(t),r.d(t,{InfiniteQueryObserver:function(){return i}});var s=r("./node_modules/@tanstack/query-core/build/modern/queryObserver.js"),n=r("./node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js"),i=class extends s.QueryObserver{constructor(e,t){super(e,t)}bindMethods(){super.bindMethods(),this.fetchNextPage=this.fetchNextPage.bind(this),this.fetchPreviousPage=this.fetchPreviousPage.bind(this)}setOptions(e,t){super.setOptions({...e,behavior:(0,n.infiniteQueryBehavior)()},t)}getOptimisticResult(e){return e.behavior=(0,n.infiniteQueryBehavior)(),super.getOptimisticResult(e)}fetchNextPage(e){return this.fetch({...e,meta:{fetchMore:{direction:"forward"}}})}fetchPreviousPage(e){return this.fetch({...e,meta:{fetchMore:{direction:"backward"}}})}createResult(e,t){const{state:r}=e,s=super.createResult(e,t),{isFetching:i,isRefetching:o}=s,u=i&&"forward"===r.fetchMeta?.fetchMore?.direction,a=i&&"backward"===r.fetchMeta?.fetchMore?.direction;return{...s,fetchNextPage:this.fetchNextPage,fetchPreviousPage:this.fetchPreviousPage,hasNextPage:(0,n.hasNextPage)(t,r.data),hasPreviousPage:(0,n.hasPreviousPage)(t,r.data),isFetchingNextPage:u,isFetchingPreviousPage:a,isRefetching:o&&!u&&!a}}}},"./node_modules/@tanstack/query-core/build/modern/mutation.js":function(e,t,r){r.r(t),r.d(t,{Mutation:function(){return o},getDefaultState:function(){return u}});var s=r("./node_modules/@tanstack/query-core/build/modern/notifyManager.js"),n=r("./node_modules/@tanstack/query-core/build/modern/removable.js"),i=r("./node_modules/@tanstack/query-core/build/modern/retryer.js"),o=class extends n.Removable{constructor(e){super(),this.mutationId=e.mutationId,this.#s=e.defaultOptions,this.#n=e.mutationCache,this.#i=[],this.state=e.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(e.options),this.scheduleGc()}#i;#s;#n;#o;setOptions(e){this.options={...this.#s,...e},this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#i.includes(e)||(this.#i.push(e),this.clearGcTimeout(),this.#n.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#i=this.#i.filter((t=>t!==e)),this.scheduleGc(),this.#n.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#i.length||("pending"===this.state.status?this.scheduleGc():this.#n.remove(this))}continue(){return this.#o?.continue()??this.execute(this.state.variables)}async execute(e){const t=()=>(this.#o=(0,i.createRetryer)({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(new Error("No mutationFn found")),onFail:(e,t)=>{this.#u({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#u({type:"pause"})},onContinue:()=>{this.#u({type:"continue"})},retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode}),this.#o.promise),r="pending"===this.state.status;try{if(!r){this.#u({type:"pending",variables:e}),await(this.#n.config.onMutate?.(e,this));const t=await(this.options.onMutate?.(e));t!==this.state.context&&this.#u({type:"pending",context:t,variables:e})}const s=await t();return await(this.#n.config.onSuccess?.(s,e,this.state.context,this)),await(this.options.onSuccess?.(s,e,this.state.context)),await(this.#n.config.onSettled?.(s,null,this.state.variables,this.state.context,this)),await(this.options.onSettled?.(s,null,e,this.state.context)),this.#u({type:"success",data:s}),s}catch(t){try{throw await(this.#n.config.onError?.(t,e,this.state.context,this)),await(this.options.onError?.(t,e,this.state.context)),await(this.#n.config.onSettled?.(void 0,t,this.state.variables,this.state.context,this)),await(this.options.onSettled?.(void 0,t,e,this.state.context)),t}finally{this.#u({type:"error",error:t})}}}#u(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"pending":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:!(0,i.canFetch)(this.options.networkMode),status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),s.notifyManager.batch((()=>{this.#i.forEach((t=>{t.onMutationUpdate(e)})),this.#n.notify({mutation:this,type:"updated",action:e})}))}};function u(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}},"./node_modules/@tanstack/query-core/build/modern/mutationCache.js":function(e,t,r){r.r(t),r.d(t,{MutationCache:function(){return u}});var s=r("./node_modules/@tanstack/query-core/build/modern/notifyManager.js"),n=r("./node_modules/@tanstack/query-core/build/modern/mutation.js"),i=r("./node_modules/@tanstack/query-core/build/modern/utils.js"),o=r("./node_modules/@tanstack/query-core/build/modern/subscribable.js"),u=class extends o.Subscribable{constructor(e={}){super(),this.config=e,this.#a=[],this.#c=0}#a;#c;#h;build(e,t,r){const s=new n.Mutation({mutationCache:this,mutationId:++this.#c,options:e.defaultMutationOptions(t),state:r});return this.add(s),s}add(e){this.#a.push(e),this.notify({type:"added",mutation:e})}remove(e){this.#a=this.#a.filter((t=>t!==e)),this.notify({type:"removed",mutation:e})}clear(){s.notifyManager.batch((()=>{this.#a.forEach((e=>{this.remove(e)}))}))}getAll(){return this.#a}find(e){const t={exact:!0,...e};return this.#a.find((e=>(0,i.matchMutation)(t,e)))}findAll(e={}){return this.#a.filter((t=>(0,i.matchMutation)(e,t)))}notify(e){s.notifyManager.batch((()=>{this.listeners.forEach((t=>{t(e)}))}))}resumePausedMutations(){return this.#h=(this.#h??Promise.resolve()).then((()=>{const e=this.#a.filter((e=>e.state.isPaused));return s.notifyManager.batch((()=>e.reduce(((e,t)=>e.then((()=>t.continue().catch(i.noop)))),Promise.resolve())))})).then((()=>{this.#h=void 0})),this.#h}}},"./node_modules/@tanstack/query-core/build/modern/mutationObserver.js":function(e,t,r){r.r(t),r.d(t,{MutationObserver:function(){return u}});var s=r("./node_modules/@tanstack/query-core/build/modern/mutation.js"),n=r("./node_modules/@tanstack/query-core/build/modern/notifyManager.js"),i=r("./node_modules/@tanstack/query-core/build/modern/subscribable.js"),o=r("./node_modules/@tanstack/query-core/build/modern/utils.js"),u=class extends i.Subscribable{constructor(e,t){super(),this.#d=void 0,this.#l=e,this.setOptions(t),this.bindMethods(),this.#f()}#l;#d;#y;#p;bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){const t=this.options;this.options=this.#l.defaultMutationOptions(e),(0,o.shallowEqualObjects)(t,this.options)||this.#l.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#y,observer:this}),this.#y?.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#y?.removeObserver(this)}onMutationUpdate(e){this.#f(),this.#m(e)}getCurrentResult(){return this.#d}reset(){this.#y=void 0,this.#f(),this.#m()}mutate(e,t){return this.#p=t,this.#y?.removeObserver(this),this.#y=this.#l.getMutationCache().build(this.#l,this.options),this.#y.addObserver(this),this.#y.execute(e)}#f(){const e=this.#y?.state??(0,s.getDefaultState)();this.#d={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#m(e){n.notifyManager.batch((()=>{this.#p&&this.hasListeners()&&("success"===e?.type?(this.#p.onSuccess?.(e.data,this.#d.variables,this.#d.context),this.#p.onSettled?.(e.data,null,this.#d.variables,this.#d.context)):"error"===e?.type&&(this.#p.onError?.(e.error,this.#d.variables,this.#d.context),this.#p.onSettled?.(void 0,e.error,this.#d.variables,this.#d.context))),this.listeners.forEach((e=>{e(this.#d)}))}))}}},"./node_modules/@tanstack/query-core/build/modern/notifyManager.js":function(e,t,r){r.r(t),r.d(t,{createNotifyManager:function(){return n},notifyManager:function(){return i}});var s=r("./node_modules/@tanstack/query-core/build/modern/utils.js");function n(){let e=[],t=0,r=e=>{e()},n=e=>{e()};const i=n=>{t?e.push(n):(0,s.scheduleMicrotask)((()=>{r(n)}))};return{batch:i=>{let o;t++;try{o=i()}finally{t--,t||(()=>{const t=e;e=[],t.length&&(0,s.scheduleMicrotask)((()=>{n((()=>{t.forEach((e=>{r(e)}))}))}))})()}return o},batchCalls:e=>(...t)=>{i((()=>{e(...t)}))},schedule:i,setNotifyFunction:e=>{r=e},setBatchNotifyFunction:e=>{n=e}}}var i=n()},"./node_modules/@tanstack/query-core/build/modern/onlineManager.js":function(e,t,r){r.r(t),r.d(t,{OnlineManager:function(){return i},onlineManager:function(){return o}});var s=r("./node_modules/@tanstack/query-core/build/modern/subscribable.js"),n=r("./node_modules/@tanstack/query-core/build/modern/utils.js"),i=class extends s.Subscribable{#b=!0;#t;#r;constructor(){super(),this.#r=e=>{if(!n.isServer&&window.addEventListener){const t=()=>e(!0),r=()=>e(!1);return window.addEventListener("online",t,!1),window.addEventListener("offline",r,!1),()=>{window.removeEventListener("online",t),window.removeEventListener("offline",r)}}}}onSubscribe(){this.#t||this.setEventListener(this.#r)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#r=e,this.#t?.(),this.#t=e(this.setOnline.bind(this))}setOnline(e){this.#b!==e&&(this.#b=e,this.listeners.forEach((t=>{t(e)})))}isOnline(){return this.#b}},o=new i},"./node_modules/@tanstack/query-core/build/modern/query.js":function(e,t,r){r.r(t),r.d(t,{Query:function(){return u}});var s=r("./node_modules/@tanstack/query-core/build/modern/utils.js"),n=r("./node_modules/@tanstack/query-core/build/modern/notifyManager.js"),i=r("./node_modules/@tanstack/query-core/build/modern/retryer.js"),o=r("./node_modules/@tanstack/query-core/build/modern/removable.js"),u=class extends o.Removable{constructor(e){super(),this.#v=!1,this.#s=e.defaultOptions,this.#g(e.options),this.#i=[],this.#q=e.cache,this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.#O=e.state||function(e){const t="function"==typeof e.initialData?e.initialData():e.initialData,r=void 0!==t,s=r?"function"==typeof e.initialDataUpdatedAt?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:r?s??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:r?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=this.#O,this.scheduleGc()}#O;#C;#q;#R;#o;#i;#s;#v;get meta(){return this.options.meta}#g(e){this.options={...this.#s,...e},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.#i.length||"idle"!==this.state.fetchStatus||this.#q.remove(this)}setData(e,t){const r=(0,s.replaceData)(this.state.data,e,this.options);return this.#u({data:r,type:"success",dataUpdatedAt:t?.updatedAt,manual:t?.manual}),r}setState(e,t){this.#u({type:"setState",state:e,setStateOptions:t})}cancel(e){const t=this.#R;return this.#o?.cancel(e),t?t.then(s.noop).catch(s.noop):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#O)}isActive(){return this.#i.some((e=>!1!==e.options.enabled))}isDisabled(){return this.getObserversCount()>0&&!this.isActive()}isStale(){return this.state.isInvalidated||!this.state.dataUpdatedAt||this.#i.some((e=>e.getCurrentResult().isStale))}isStaleByTime(e=0){return this.state.isInvalidated||!this.state.dataUpdatedAt||!(0,s.timeUntilStale)(this.state.dataUpdatedAt,e)}onFocus(){const e=this.#i.find((e=>e.shouldFetchOnWindowFocus()));e?.refetch({cancelRefetch:!1}),this.#o?.continue()}onOnline(){const e=this.#i.find((e=>e.shouldFetchOnReconnect()));e?.refetch({cancelRefetch:!1}),this.#o?.continue()}addObserver(e){this.#i.includes(e)||(this.#i.push(e),this.clearGcTimeout(),this.#q.notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.#i.includes(e)&&(this.#i=this.#i.filter((t=>t!==e)),this.#i.length||(this.#o&&(this.#v?this.#o.cancel({revert:!0}):this.#o.cancelRetry()),this.scheduleGc()),this.#q.notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.#i.length}invalidate(){this.state.isInvalidated||this.#u({type:"invalidate"})}fetch(e,t){if("idle"!==this.state.fetchStatus)if(this.state.dataUpdatedAt&&t?.cancelRefetch)this.cancel({silent:!0});else if(this.#R)return this.#o?.continueRetry(),this.#R;if(e&&this.#g(e),!this.options.queryFn){const e=this.#i.find((e=>e.options.queryFn));e&&this.#g(e.options)}Array.isArray(this.options.queryKey)||console.error("As of v4, queryKey needs to be an Array. If you are using a string like 'repoData', please change it to an Array, e.g. ['repoData']");const r=new AbortController,s={queryKey:this.queryKey,meta:this.meta},n=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(this.#v=!0,r.signal)})};n(s);const o={fetchOptions:t,options:this.options,queryKey:this.queryKey,state:this.state,fetchFn:()=>this.options.queryFn?(this.#v=!1,this.options.persister?this.options.persister(this.options.queryFn,s,this):this.options.queryFn(s)):Promise.reject(new Error(`Missing queryFn: '${this.options.queryHash}'`))};n(o),this.options.behavior?.onFetch(o,this),this.#C=this.state,"idle"!==this.state.fetchStatus&&this.state.fetchMeta===o.fetchOptions?.meta||this.#u({type:"fetch",meta:o.fetchOptions?.meta});const u=e=>{(0,i.isCancelledError)(e)&&e.silent||this.#u({type:"error",error:e}),(0,i.isCancelledError)(e)||(this.#q.config.onError?.(e,this),this.#q.config.onSettled?.(this.state.data,e,this)),this.isFetchingOptimistic||this.scheduleGc(),this.isFetchingOptimistic=!1};return this.#o=(0,i.createRetryer)({fn:o.fetchFn,abort:r.abort.bind(r),onSuccess:e=>{if(void 0===e)return console.error(`Query data cannot be undefined. Please make sure to return a value other than undefined from your query function. Affected query key: ${this.queryHash}`),void u(new Error(`${this.queryHash} data is undefined`));this.setData(e),this.#q.config.onSuccess?.(e,this),this.#q.config.onSettled?.(e,this.state.error,this),this.isFetchingOptimistic||this.scheduleGc(),this.isFetchingOptimistic=!1},onError:u,onFail:(e,t)=>{this.#u({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#u({type:"pause"})},onContinue:()=>{this.#u({type:"continue"})},retry:o.options.retry,retryDelay:o.options.retryDelay,networkMode:o.options.networkMode}),this.#R=this.#o.promise,this.#R}#u(e){this.state=(t=>{switch(e.type){case"failed":return{...t,fetchFailureCount:e.failureCount,fetchFailureReason:e.error};case"pause":return{...t,fetchStatus:"paused"};case"continue":return{...t,fetchStatus:"fetching"};case"fetch":return{...t,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:e.meta??null,fetchStatus:(0,i.canFetch)(this.options.networkMode)?"fetching":"paused",...!t.dataUpdatedAt&&{error:null,status:"pending"}};case"success":return{...t,data:e.data,dataUpdateCount:t.dataUpdateCount+1,dataUpdatedAt:e.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const r=e.error;return(0,i.isCancelledError)(r)&&r.revert&&this.#C?{...this.#C,fetchStatus:"idle"}:{...t,error:r,errorUpdateCount:t.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:t.fetchFailureCount+1,fetchFailureReason:r,fetchStatus:"idle",status:"error"};case"invalidate":return{...t,isInvalidated:!0};case"setState":return{...t,...e.state}}})(this.state),n.notifyManager.batch((()=>{this.#i.forEach((e=>{e.onQueryUpdate()})),this.#q.notify({query:this,type:"updated",action:e})}))}}},"./node_modules/@tanstack/query-core/build/modern/queryCache.js":function(e,t,r){r.r(t),r.d(t,{QueryCache:function(){return u}});var s=r("./node_modules/@tanstack/query-core/build/modern/utils.js"),n=r("./node_modules/@tanstack/query-core/build/modern/query.js"),i=r("./node_modules/@tanstack/query-core/build/modern/notifyManager.js"),o=r("./node_modules/@tanstack/query-core/build/modern/subscribable.js"),u=class extends o.Subscribable{constructor(e={}){super(),this.config=e,this.#Q=new Map}#Q;build(e,t,r){const i=t.queryKey,o=t.queryHash??(0,s.hashQueryKeyByOptions)(i,t);let u=this.get(o);return u||(u=new n.Query({cache:this,queryKey:i,queryHash:o,options:e.defaultQueryOptions(t),state:r,defaultOptions:e.getQueryDefaults(i)}),this.add(u)),u}add(e){this.#Q.has(e.queryHash)||(this.#Q.set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){const t=this.#Q.get(e.queryHash);t&&(e.destroy(),t===e&&this.#Q.delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){i.notifyManager.batch((()=>{this.getAll().forEach((e=>{this.remove(e)}))}))}get(e){return this.#Q.get(e)}getAll(){return[...this.#Q.values()]}find(e){const t={exact:!0,...e};return this.getAll().find((e=>(0,s.matchQuery)(t,e)))}findAll(e={}){const t=this.getAll();return Object.keys(e).length>0?t.filter((t=>(0,s.matchQuery)(e,t))):t}notify(e){i.notifyManager.batch((()=>{this.listeners.forEach((t=>{t(e)}))}))}onFocus(){i.notifyManager.batch((()=>{this.getAll().forEach((e=>{e.onFocus()}))}))}onOnline(){i.notifyManager.batch((()=>{this.getAll().forEach((e=>{e.onOnline()}))}))}}},"./node_modules/@tanstack/query-core/build/modern/queryClient.js":function(e,t,r){r.r(t),r.d(t,{QueryClient:function(){return h}});var s=r("./node_modules/@tanstack/query-core/build/modern/utils.js"),n=r("./node_modules/@tanstack/query-core/build/modern/queryCache.js"),i=r("./node_modules/@tanstack/query-core/build/modern/mutationCache.js"),o=r("./node_modules/@tanstack/query-core/build/modern/focusManager.js"),u=r("./node_modules/@tanstack/query-core/build/modern/onlineManager.js"),a=r("./node_modules/@tanstack/query-core/build/modern/notifyManager.js"),c=r("./node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js"),h=class{#M;#n;#s;#w;#k;#S;#j;#P;constructor(e={}){this.#M=e.queryCache||new n.QueryCache,this.#n=e.mutationCache||new i.MutationCache,this.#s=e.defaultOptions||{},this.#w=new Map,this.#k=new Map,this.#S=0}mount(){this.#S++,1===this.#S&&(this.#j=o.focusManager.subscribe((()=>{o.focusManager.isFocused()&&(this.resumePausedMutations(),this.#M.onFocus())})),this.#P=u.onlineManager.subscribe((()=>{u.onlineManager.isOnline()&&(this.resumePausedMutations(),this.#M.onOnline())})))}unmount(){this.#S--,0===this.#S&&(this.#j?.(),this.#j=void 0,this.#P?.(),this.#P=void 0)}isFetching(e){return this.#M.findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return this.#n.findAll({...e,status:"pending"}).length}getQueryData(e){return this.#M.find({queryKey:e})?.state.data}ensureQueryData(e){const t=this.getQueryData(e.queryKey);return void 0!==t?Promise.resolve(t):this.fetchQuery(e)}getQueriesData(e){return this.getQueryCache().findAll(e).map((({queryKey:e,state:t})=>[e,t.data]))}setQueryData(e,t,r){const n=this.#M.find({queryKey:e}),i=n?.state.data,o=(0,s.functionalUpdate)(t,i);if(void 0===o)return;const u=this.defaultQueryOptions({queryKey:e});return this.#M.build(this,u).setData(o,{...r,manual:!0})}setQueriesData(e,t,r){return a.notifyManager.batch((()=>this.getQueryCache().findAll(e).map((({queryKey:e})=>[e,this.setQueryData(e,t,r)]))))}getQueryState(e){return this.#M.find({queryKey:e})?.state}removeQueries(e){const t=this.#M;a.notifyManager.batch((()=>{t.findAll(e).forEach((e=>{t.remove(e)}))}))}resetQueries(e,t){const r=this.#M,s={type:"active",...e};return a.notifyManager.batch((()=>(r.findAll(e).forEach((e=>{e.reset()})),this.refetchQueries(s,t))))}cancelQueries(e={},t={}){const r={revert:!0,...t},n=a.notifyManager.batch((()=>this.#M.findAll(e).map((e=>e.cancel(r)))));return Promise.all(n).then(s.noop).catch(s.noop)}invalidateQueries(e={},t={}){return a.notifyManager.batch((()=>{if(this.#M.findAll(e).forEach((e=>{e.invalidate()})),"none"===e.refetchType)return Promise.resolve();const r={...e,type:e.refetchType??e.type??"active"};return this.refetchQueries(r,t)}))}refetchQueries(e={},t){const r={...t,cancelRefetch:t?.cancelRefetch??!0},n=a.notifyManager.batch((()=>this.#M.findAll(e).filter((e=>!e.isDisabled())).map((e=>{let t=e.fetch(void 0,r);return r.throwOnError||(t=t.catch(s.noop)),"paused"===e.state.fetchStatus?Promise.resolve():t}))));return Promise.all(n).then(s.noop)}fetchQuery(e){const t=this.defaultQueryOptions(e);void 0===t.retry&&(t.retry=!1);const r=this.#M.build(this,t);return r.isStaleByTime(t.staleTime)?r.fetch(t):Promise.resolve(r.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(s.noop).catch(s.noop)}fetchInfiniteQuery(e){return e.behavior=(0,c.infiniteQueryBehavior)(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(s.noop).catch(s.noop)}resumePausedMutations(){return this.#n.resumePausedMutations()}getQueryCache(){return this.#M}getMutationCache(){return this.#n}getDefaultOptions(){return this.#s}setDefaultOptions(e){this.#s=e}setQueryDefaults(e,t){this.#w.set((0,s.hashKey)(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...this.#w.values()];let r={};return t.forEach((t=>{(0,s.partialMatchKey)(e,t.queryKey)&&(r={...r,...t.defaultOptions})})),r}setMutationDefaults(e,t){this.#k.set((0,s.hashKey)(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...this.#k.values()];let r={};return t.forEach((t=>{(0,s.partialMatchKey)(e,t.mutationKey)&&(r={...r,...t.defaultOptions})})),r}defaultQueryOptions(e){if(e?._defaulted)return e;const t={...this.#s.queries,...e?.queryKey&&this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=(0,s.hashQueryKeyByOptions)(t.queryKey,t)),void 0===t.refetchOnReconnect&&(t.refetchOnReconnect="always"!==t.networkMode),void 0===t.throwOnError&&(t.throwOnError=!!t.suspense),void 0===t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t}defaultMutationOptions(e){return e?._defaulted?e:{...this.#s.mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){this.#M.clear(),this.#n.clear()}}},"./node_modules/@tanstack/query-core/build/modern/queryObserver.js":function(e,t,r){r.r(t),r.d(t,{QueryObserver:function(){return a}});var s=r("./node_modules/@tanstack/query-core/build/modern/utils.js"),n=r("./node_modules/@tanstack/query-core/build/modern/notifyManager.js"),i=r("./node_modules/@tanstack/query-core/build/modern/focusManager.js"),o=r("./node_modules/@tanstack/query-core/build/modern/subscribable.js"),u=r("./node_modules/@tanstack/query-core/build/modern/retryer.js"),a=class extends o.Subscribable{constructor(e,t){super(),this.#E=void 0,this.#_=void 0,this.#d=void 0,this.#F=new Set,this.#l=e,this.options=t,this.#D=null,this.bindMethods(),this.setOptions(t)}#l;#E;#_;#d;#I;#T;#D;#x;#A;#U;#K;#B;#L;#F;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(this.#E.addObserver(this),c(this.#E,this.options)&&this.#H(),this.#G())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return h(this.#E,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return h(this.#E,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#N(),this.#W(),this.#E.removeObserver(this)}setOptions(e,t){const r=this.options,n=this.#E;if(this.options=this.#l.defaultQueryOptions(e),(0,s.shallowEqualObjects)(r,this.options)||this.#l.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#E,observer:this}),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled)throw new Error("Expected enabled to be a boolean");this.options.queryKey||(this.options.queryKey=r.queryKey),this.#V();const i=this.hasListeners();i&&d(this.#E,n,this.options,r)&&this.#H(),this.updateResult(t),!i||this.#E===n&&this.options.enabled===r.enabled&&this.options.staleTime===r.staleTime||this.#$();const o=this.#z();!i||this.#E===n&&this.options.enabled===r.enabled&&o===this.#L||this.#J(o)}getOptimisticResult(e){const t=this.#l.getQueryCache().build(this.#l,e),r=this.createResult(t,e);return n=this,i=r,!(0,s.shallowEqualObjects)(n.getCurrentResult(),i)&&(this.#d=r,this.#T=this.options,this.#I=this.#E.state),r;var n,i}getCurrentResult(){return this.#d}trackResult(e){const t={};return Object.keys(e).forEach((r=>{Object.defineProperty(t,r,{configurable:!1,enumerable:!0,get:()=>(this.#F.add(r),e[r])})})),t}getCurrentQuery(){return this.#E}refetch({...e}={}){return this.fetch({...e})}fetchOptimistic(e){const t=this.#l.defaultQueryOptions(e),r=this.#l.getQueryCache().build(this.#l,t);return r.isFetchingOptimistic=!0,r.fetch().then((()=>this.createResult(r,t)))}fetch(e){return this.#H({...e,cancelRefetch:e.cancelRefetch??!0}).then((()=>(this.updateResult(),this.#d)))}#H(e){this.#V();let t=this.#E.fetch(this.options,e);return e?.throwOnError||(t=t.catch(s.noop)),t}#$(){if(this.#N(),s.isServer||this.#d.isStale||!(0,s.isValidTimeout)(this.options.staleTime))return;const e=(0,s.timeUntilStale)(this.#d.dataUpdatedAt,this.options.staleTime)+1;this.#K=setTimeout((()=>{this.#d.isStale||this.updateResult()}),e)}#z(){return("function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.#E):this.options.refetchInterval)??!1}#J(e){this.#W(),this.#L=e,!s.isServer&&!1!==this.options.enabled&&(0,s.isValidTimeout)(this.#L)&&0!==this.#L&&(this.#B=setInterval((()=>{(this.options.refetchIntervalInBackground||i.focusManager.isFocused())&&this.#H()}),this.#L))}#G(){this.#$(),this.#J(this.#z())}#N(){this.#K&&(clearTimeout(this.#K),this.#K=void 0)}#W(){this.#B&&(clearInterval(this.#B),this.#B=void 0)}createResult(e,t){const r=this.#E,n=this.options,i=this.#d,o=this.#I,a=this.#T,h=e!==r?e.state:this.#_,{state:f}=e;let y,{error:p,errorUpdatedAt:m,fetchStatus:b,status:v}=f,g=!1;if(t._optimisticResults){const s=this.hasListeners(),i=!s&&c(e,t),o=s&&d(e,r,t,n);(i||o)&&(b=(0,u.canFetch)(e.options.networkMode)?"fetching":"paused",f.dataUpdatedAt||(v="pending")),"isRestoring"===t._optimisticResults&&(b="idle")}if(t.select&&void 0!==f.data)if(i&&f.data===o?.data&&t.select===this.#x)y=this.#A;else try{this.#x=t.select,y=t.select(f.data),y=(0,s.replaceData)(i?.data,y,t),this.#A=y,this.#D=null}catch(e){this.#D=e}else y=f.data;if(void 0!==t.placeholderData&&void 0===y&&"pending"===v){let e;if(i?.isPlaceholderData&&t.placeholderData===a?.placeholderData)e=i.data;else if(e="function"==typeof t.placeholderData?t.placeholderData(this.#U?.state.data,this.#U):t.placeholderData,t.select&&void 0!==e)try{e=t.select(e),this.#D=null}catch(e){this.#D=e}void 0!==e&&(v="success",y=(0,s.replaceData)(i?.data,e,t),g=!0)}this.#D&&(p=this.#D,y=this.#A,m=Date.now(),v="error");const q="fetching"===b,O="pending"===v,C="error"===v,R=O&&q;return{status:v,fetchStatus:b,isPending:O,isSuccess:"success"===v,isError:C,isInitialLoading:R,isLoading:R,data:y,dataUpdatedAt:f.dataUpdatedAt,error:p,errorUpdatedAt:m,failureCount:f.fetchFailureCount,failureReason:f.fetchFailureReason,errorUpdateCount:f.errorUpdateCount,isFetched:f.dataUpdateCount>0||f.errorUpdateCount>0,isFetchedAfterMount:f.dataUpdateCount>h.dataUpdateCount||f.errorUpdateCount>h.errorUpdateCount,isFetching:q,isRefetching:q&&!O,isLoadingError:C&&0===f.dataUpdatedAt,isPaused:"paused"===b,isPlaceholderData:g,isRefetchError:C&&0!==f.dataUpdatedAt,isStale:l(e,t),refetch:this.refetch}}updateResult(e){const t=this.#d,r=this.createResult(this.#E,this.options);if(this.#I=this.#E.state,this.#T=this.options,(0,s.shallowEqualObjects)(r,t))return;void 0!==this.#I.data&&(this.#U=this.#E),this.#d=r;const n={};!1!==e?.listeners&&(()=>{if(!t)return!0;const{notifyOnChangeProps:e}=this.options,r="function"==typeof e?e():e;if("all"===r||!r&&!this.#F.size)return!0;const s=new Set(r??this.#F);return this.options.throwOnError&&s.add("error"),Object.keys(this.#d).some((e=>{const r=e;return this.#d[r]!==t[r]&&s.has(r)}))})()&&(n.listeners=!0),this.#m({...n,...e})}#V(){const e=this.#l.getQueryCache().build(this.#l,this.options);if(e===this.#E)return;const t=this.#E;this.#E=e,this.#_=e.state,this.hasListeners()&&(t?.removeObserver(this),e.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#G()}#m(e){n.notifyManager.batch((()=>{e.listeners&&this.listeners.forEach((e=>{e(this.#d)})),this.#l.getQueryCache().notify({query:this.#E,type:"observerResultsUpdated"})}))}};function c(e,t){return function(e,t){return!(!1===t.enabled||e.state.dataUpdatedAt||"error"===e.state.status&&!1===t.retryOnMount)}(e,t)||e.state.dataUpdatedAt>0&&h(e,t,t.refetchOnMount)}function h(e,t,r){if(!1!==t.enabled){const s="function"==typeof r?r(e):r;return"always"===s||!1!==s&&l(e,t)}return!1}function d(e,t,r,s){return!1!==r.enabled&&(e!==t||!1===s.enabled)&&(!r.suspense||"error"!==e.state.status)&&l(e,r)}function l(e,t){return e.isStaleByTime(t.staleTime)}},"./node_modules/@tanstack/query-core/build/modern/removable.js":function(e,t,r){r.r(t),r.d(t,{Removable:function(){return n}});var s=r("./node_modules/@tanstack/query-core/build/modern/utils.js"),n=class{#X;destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),(0,s.isValidTimeout)(this.gcTime)&&(this.#X=setTimeout((()=>{this.optionalRemove()}),this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(s.isServer?1/0:3e5))}clearGcTimeout(){this.#X&&(clearTimeout(this.#X),this.#X=void 0)}}},"./node_modules/@tanstack/query-core/build/modern/retryer.js":function(e,t,r){r.r(t),r.d(t,{CancelledError:function(){return a},canFetch:function(){return u},createRetryer:function(){return h},isCancelledError:function(){return c}});var s=r("./node_modules/@tanstack/query-core/build/modern/focusManager.js"),n=r("./node_modules/@tanstack/query-core/build/modern/onlineManager.js"),i=r("./node_modules/@tanstack/query-core/build/modern/utils.js");function o(e){return Math.min(1e3*2**e,3e4)}function u(e){return"online"!==(e??"online")||n.onlineManager.isOnline()}var a=class{constructor(e){this.revert=e?.revert,this.silent=e?.silent}};function c(e){return e instanceof a}function h(e){let t,r,c,h=!1,d=0,l=!1;const f=new Promise(((e,t)=>{r=e,c=t})),y=()=>!s.focusManager.isFocused()||"always"!==e.networkMode&&!n.onlineManager.isOnline(),p=s=>{l||(l=!0,e.onSuccess?.(s),t?.(),r(s))},m=r=>{l||(l=!0,e.onError?.(r),t?.(),c(r))},b=()=>new Promise((r=>{t=e=>{const t=l||!y();return t&&r(e),t},e.onPause?.()})).then((()=>{t=void 0,l||e.onContinue?.()})),v=()=>{if(l)return;let t;try{t=e.fn()}catch(e){t=Promise.reject(e)}Promise.resolve(t).then(p).catch((t=>{if(l)return;const r=e.retry??(i.isServer?0:3),s=e.retryDelay??o,n="function"==typeof s?s(d,t):s,u=!0===r||"number"==typeof r&&d<r||"function"==typeof r&&r(d,t);!h&&u?(d++,e.onFail?.(d,t),(0,i.sleep)(n).then((()=>{if(y())return b()})).then((()=>{h?m(t):v()}))):m(t)}))};return u(e.networkMode)?v():b().then(v),{promise:f,cancel:t=>{l||(m(new a(t)),e.abort?.())},continue:()=>{const e=t?.();return e?f:Promise.resolve()},cancelRetry:()=>{h=!0},continueRetry:()=>{h=!1}}}},"./node_modules/@tanstack/query-core/build/modern/subscribable.js":function(e,t,r){r.r(t),r.d(t,{Subscribable:function(){return s}});var s=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}}},"./node_modules/@tanstack/query-core/build/modern/utils.js":function(e,t,r){r.r(t),r.d(t,{addToEnd:function(){return C},addToStart:function(){return R},functionalUpdate:function(){return i},hashKey:function(){return d},hashQueryKeyByOptions:function(){return h},isPlainArray:function(){return p},isPlainObject:function(){return m},isServer:function(){return s},isValidTimeout:function(){return o},keepPreviousData:function(){return O},matchMutation:function(){return c},matchQuery:function(){return a},noop:function(){return n},partialMatchKey:function(){return l},replaceData:function(){return q},replaceEqualDeep:function(){return f},scheduleMicrotask:function(){return g},shallowEqualObjects:function(){return y},sleep:function(){return v},timeUntilStale:function(){return u}});var s="undefined"==typeof window||"Deno"in window;function n(){}function i(e,t){return"function"==typeof e?e(t):e}function o(e){return"number"==typeof e&&e>=0&&e!==1/0}function u(e,t){return Math.max(e+(t||0)-Date.now(),0)}function a(e,t){const{type:r="all",exact:s,fetchStatus:n,predicate:i,queryKey:o,stale:u}=e;if(o)if(s){if(t.queryHash!==h(o,t.options))return!1}else if(!l(t.queryKey,o))return!1;if("all"!==r){const e=t.isActive();if("active"===r&&!e)return!1;if("inactive"===r&&e)return!1}return!("boolean"==typeof u&&t.isStale()!==u||void 0!==n&&n!==t.state.fetchStatus||i&&!i(t))}function c(e,t){const{exact:r,status:s,predicate:n,mutationKey:i}=e;if(i){if(!t.options.mutationKey)return!1;if(r){if(d(t.options.mutationKey)!==d(i))return!1}else if(!l(t.options.mutationKey,i))return!1}return!(s&&t.state.status!==s||n&&!n(t))}function h(e,t){return(t?.queryKeyHashFn||d)(e)}function d(e){return JSON.stringify(e,((e,t)=>m(t)?Object.keys(t).sort().reduce(((e,r)=>(e[r]=t[r],e)),{}):t))}function l(e,t){return e===t||typeof e==typeof t&&!(!e||!t||"object"!=typeof e||"object"!=typeof t)&&!Object.keys(t).some((r=>!l(e[r],t[r])))}function f(e,t){if(e===t)return e;const r=p(e)&&p(t);if(r||m(e)&&m(t)){const s=r?e.length:Object.keys(e).length,n=r?t:Object.keys(t),i=n.length,o=r?[]:{};let u=0;for(let s=0;s<i;s++){const i=r?s:n[s];o[i]=f(e[i],t[i]),o[i]===e[i]&&u++}return s===i&&u===s?e:o}return t}function y(e,t){if(e&&!t||t&&!e)return!1;for(const r in e)if(e[r]!==t[r])return!1;return!0}function p(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function m(e){if(!b(e))return!1;const t=e.constructor;if(void 0===t)return!0;const r=t.prototype;return!!b(r)&&!!r.hasOwnProperty("isPrototypeOf")}function b(e){return"[object Object]"===Object.prototype.toString.call(e)}function v(e){return new Promise((t=>{setTimeout(t,e)}))}function g(e){v(0).then(e)}function q(e,t,r){return"function"==typeof r.structuralSharing?r.structuralSharing(e,t):!1!==r.structuralSharing?f(e,t):t}function O(e){return e}function C(e,t,r=0){const s=[...e,t];return r&&s.length>r?s.slice(1):s}function R(e,t,r=0){const s=[t,...e];return r&&s.length>r?s.slice(0,-1):s}},"./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js":function(e,t,r){r.r(t),r.d(t,{QueryClientContext:function(){return n},QueryClientProvider:function(){return o},useQueryClient:function(){return i}});var s=r("react"),n=s.createContext(void 0),i=e=>{const t=s.useContext(n);if(e)return e;if(!t)throw new Error("No QueryClient set, use QueryClientProvider to set one");return t},o=({client:e,children:t})=>(s.useEffect((()=>(e.mount(),()=>{e.unmount()})),[e]),s.createElement(n.Provider,{value:e},t))},"./node_modules/@tanstack/react-query/build/modern/QueryErrorResetBoundary.js":function(e,t,r){r.r(t),r.d(t,{QueryErrorResetBoundary:function(){return u},useQueryErrorResetBoundary:function(){return o}});var s=r("react");function n(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}var i=s.createContext(n()),o=()=>s.useContext(i),u=({children:e})=>{const[t]=s.useState((()=>n()));return s.createElement(i.Provider,{value:t},"function"==typeof e?e(t):e)}},"./node_modules/@tanstack/react-query/build/modern/errorBoundaryUtils.js":function(e,t,r){r.r(t),r.d(t,{ensurePreventErrorBoundaryRetry:function(){return i},getHasError:function(){return u},useClearResetErrorBoundary:function(){return o}});var s=r("react"),n=r("./node_modules/@tanstack/react-query/build/modern/utils.js"),i=(e,t)=>{(e.suspense||e.throwOnError)&&(t.isReset()||(e.retryOnMount=!1))},o=e=>{s.useEffect((()=>{e.clearReset()}),[e])},u=({result:e,errorResetBoundary:t,throwOnError:r,query:s})=>e.isError&&!t.isReset()&&!e.isFetching&&(0,n.shouldThrowError)(r,[e.error,s])},"./node_modules/@tanstack/react-query/build/modern/isRestoring.js":function(e,t,r){r.r(t),r.d(t,{IsRestoringProvider:function(){return o},useIsRestoring:function(){return i}});var s=r("react"),n=s.createContext(!1),i=()=>s.useContext(n),o=n.Provider},"./node_modules/@tanstack/react-query/build/modern/suspense.js":function(e,t,r){r.r(t),r.d(t,{defaultThrowOnError:function(){return s},ensureStaleTime:function(){return n},fetchOptimistic:function(){return u},shouldSuspend:function(){return o},willFetch:function(){return i}});var s=(e,t)=>void 0===t.state.data,n=e=>{e.suspense&&"number"!=typeof e.staleTime&&(e.staleTime=1e3)},i=(e,t)=>e.isLoading&&e.isFetching&&!t,o=(e,t,r)=>e?.suspense&&i(t,r),u=(e,t,r)=>t.fetchOptimistic(e).catch((()=>{r.clearReset()}))},"./node_modules/@tanstack/react-query/build/modern/useBaseQuery.js":function(e,t,r){r.r(t),r.d(t,{useBaseQuery:function(){return h}});var s=r("react"),n=r("./node_modules/@tanstack/query-core/build/modern/notifyManager.js"),i=r("./node_modules/@tanstack/react-query/build/modern/QueryErrorResetBoundary.js"),o=r("./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js"),u=r("./node_modules/@tanstack/react-query/build/modern/isRestoring.js"),a=r("./node_modules/@tanstack/react-query/build/modern/errorBoundaryUtils.js"),c=r("./node_modules/@tanstack/react-query/build/modern/suspense.js");function h(e,t,r){if("object"!=typeof e||Array.isArray(e))throw new Error('Bad argument type. Starting with v5, only the "Object" form is allowed when calling query related functions. Please use the error stack to find the culprit call. More info here: https://tanstack.com/query/latest/docs/react/guides/migrating-to-v5#supports-a-single-signature-one-object');const h=(0,o.useQueryClient)(r),d=(0,u.useIsRestoring)(),l=(0,i.useQueryErrorResetBoundary)(),f=h.defaultQueryOptions(e);f._optimisticResults=d?"isRestoring":"optimistic",(0,c.ensureStaleTime)(f),(0,a.ensurePreventErrorBoundaryRetry)(f,l),(0,a.useClearResetErrorBoundary)(l);const[y]=s.useState((()=>new t(h,f))),p=y.getOptimisticResult(f);if(s.useSyncExternalStore(s.useCallback((e=>{const t=d?()=>{}:y.subscribe(n.notifyManager.batchCalls(e));return y.updateResult(),t}),[y,d]),(()=>y.getCurrentResult()),(()=>y.getCurrentResult())),s.useEffect((()=>{y.setOptions(f,{listeners:!1})}),[f,y]),(0,c.shouldSuspend)(f,p,d))throw(0,c.fetchOptimistic)(f,y,l);if((0,a.getHasError)({result:p,errorResetBoundary:l,throwOnError:f.throwOnError,query:y.getCurrentQuery()}))throw p.error;return f.notifyOnChangeProps?p:y.trackResult(p)}},"./node_modules/@tanstack/react-query/build/modern/useInfiniteQuery.js":function(e,t,r){r.r(t),r.d(t,{useInfiniteQuery:function(){return i}});var s=r("./node_modules/@tanstack/query-core/build/modern/infiniteQueryObserver.js"),n=r("./node_modules/@tanstack/react-query/build/modern/useBaseQuery.js");function i(e,t){return(0,n.useBaseQuery)(e,s.InfiniteQueryObserver,t)}},"./node_modules/@tanstack/react-query/build/modern/useMutation.js":function(e,t,r){r.r(t),r.d(t,{useMutation:function(){return a}});var s=r("react"),n=r("./node_modules/@tanstack/query-core/build/modern/mutationObserver.js"),i=r("./node_modules/@tanstack/query-core/build/modern/notifyManager.js"),o=r("./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js"),u=r("./node_modules/@tanstack/react-query/build/modern/utils.js");function a(e,t){const r=(0,o.useQueryClient)(t),[a]=s.useState((()=>new n.MutationObserver(r,e)));s.useEffect((()=>{a.setOptions(e)}),[a,e]);const h=s.useSyncExternalStore(s.useCallback((e=>a.subscribe(i.notifyManager.batchCalls(e))),[a]),(()=>a.getCurrentResult()),(()=>a.getCurrentResult())),d=s.useCallback(((e,t)=>{a.mutate(e,t).catch(c)}),[a]);if(h.error&&(0,u.shouldThrowError)(a.options.throwOnError,[h.error]))throw h.error;return{...h,mutate:d,mutateAsync:h.mutate}}function c(){}},"./node_modules/@tanstack/react-query/build/modern/useQuery.js":function(e,t,r){r.r(t),r.d(t,{useQuery:function(){return i}});var s=r("./node_modules/@tanstack/query-core/build/modern/queryObserver.js"),n=r("./node_modules/@tanstack/react-query/build/modern/useBaseQuery.js");function i(e,t){return(0,n.useBaseQuery)(e,s.QueryObserver,t)}},"./node_modules/@tanstack/react-query/build/modern/utils.js":function(e,t,r){function s(e,t){return"function"==typeof e?e(...t):!!e}r.r(t),r.d(t,{shouldThrowError:function(){return s}})}},t={};function r(s){var n=t[s];if(void 0!==n)return n.exports;var i=t[s]={exports:{}};return e[s](i,i.exports,r),i.exports}r.d=function(e,t){for(var s in t)r.o(t,s)&&!r.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:t[s]})},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var s={};!function(){r.r(s),r.d(s,{QueryClient:function(){return e.QueryClient},QueryClientProvider:function(){return t.QueryClientProvider},createQueryClient:function(){return u},useInfiniteQuery:function(){return n.useInfiniteQuery},useMutation:function(){return i.useMutation},useQuery:function(){return o.useQuery},useQueryClient:function(){return t.useQueryClient}});var e=r("./node_modules/@tanstack/query-core/build/modern/queryClient.js"),t=r("./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js"),n=r("./node_modules/@tanstack/react-query/build/modern/useInfiniteQuery.js"),i=r("./node_modules/@tanstack/react-query/build/modern/useMutation.js"),o=r("./node_modules/@tanstack/react-query/build/modern/useQuery.js");function u(){return new e.QueryClient({defaultOptions:{queries:{refetchOnWindowFocus:!1,refetchOnReconnect:!1}}})}}(),(window.elementorV2=window.elementorV2||{}).query=s}();