!function(){"use strict";var t={d:function(e,s){for(var i in s)t.o(s,i)&&!t.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:s[i]})},o:function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r:function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})}},e={};t.r(e),t.d(e,{QueryClient:function(){return j},QueryClientProvider:function(){return _},createQueryClient:function(){return pt},useInfiniteQuery:function(){return ct},useMutation:function(){return lt},useQuery:function(){return ft},useQueryClient:function(){return N}});var s="undefined"==typeof window||"Deno"in window;function i(){}function r(t){return"number"==typeof t&&t>=0&&t!==1/0}function n(t,e){return Math.max(t+(e||0)-Date.now(),0)}function a(t,e){const{type:s="all",exact:i,fetchStatus:r,predicate:n,queryKey:a,stale:o}=t;if(a)if(i){if(e.queryHash!==u(a,e.options))return!1}else if(!h(e.queryKey,a))return!1;if("all"!==s){const t=e.isActive();if("active"===s&&!t)return!1;if("inactive"===s&&t)return!1}return!("boolean"==typeof o&&e.isStale()!==o||void 0!==r&&r!==e.state.fetchStatus||n&&!n(e))}function o(t,e){const{exact:s,status:i,predicate:r,mutationKey:n}=t;if(n){if(!e.options.mutationKey)return!1;if(s){if(c(e.options.mutationKey)!==c(n))return!1}else if(!h(e.options.mutationKey,n))return!1}return!(i&&e.state.status!==i||r&&!r(e))}function u(t,e){return(e?.queryKeyHashFn||c)(t)}function c(t){return JSON.stringify(t,((t,e)=>p(e)?Object.keys(e).sort().reduce(((t,s)=>(t[s]=e[s],t)),{}):e))}function h(t,e){return t===e||typeof t==typeof e&&!(!t||!e||"object"!=typeof t||"object"!=typeof e)&&!Object.keys(e).some((s=>!h(t[s],e[s])))}function l(t,e){if(t===e)return t;const s=f(t)&&f(e);if(s||p(t)&&p(e)){const i=s?t.length:Object.keys(t).length,r=s?e:Object.keys(e),n=r.length,a=s?[]:{};let o=0;for(let i=0;i<n;i++){const n=s?i:r[i];a[n]=l(t[n],e[n]),a[n]===t[n]&&o++}return i===n&&o===i?t:a}return e}function d(t,e){if(t&&!e||e&&!t)return!1;for(const s in t)if(t[s]!==e[s])return!1;return!0}function f(t){return Array.isArray(t)&&t.length===Object.keys(t).length}function p(t){if(!y(t))return!1;const e=t.constructor;if(void 0===e)return!0;const s=e.prototype;return!!y(s)&&!!s.hasOwnProperty("isPrototypeOf")}function y(t){return"[object Object]"===Object.prototype.toString.call(t)}function m(t){return new Promise((e=>{setTimeout(e,t)}))}function v(t){m(0).then(t)}function b(t,e,s){return"function"==typeof s.structuralSharing?s.structuralSharing(t,e):!1!==s.structuralSharing?l(t,e):e}function g(t,e,s=0){const i=[...t,e];return s&&i.length>s?i.slice(1):i}function O(t,e,s=0){const i=[e,...t];return s&&i.length>s?i.slice(0,-1):i}var R=function(){let t=[],e=0,s=t=>{t()},i=t=>{t()};const r=i=>{e?t.push(i):v((()=>{s(i)}))};return{batch:r=>{let n;e++;try{n=r()}finally{e--,e||(()=>{const e=t;t=[],e.length&&v((()=>{i((()=>{e.forEach((t=>{s(t)}))}))}))})()}return n},batchCalls:t=>(...e)=>{r((()=>{t(...e)}))},schedule:r,setNotifyFunction:t=>{s=t},setBatchNotifyFunction:t=>{i=t}}}(),C=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(t){return this.listeners.add(t),this.onSubscribe(),()=>{this.listeners.delete(t),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},w=new class extends C{#t;#e;#s;constructor(){super(),this.#s=t=>{if(!s&&window.addEventListener){const e=()=>t();return window.addEventListener("visibilitychange",e,!1),()=>{window.removeEventListener("visibilitychange",e)}}}}onSubscribe(){this.#e||this.setEventListener(this.#s)}onUnsubscribe(){this.hasListeners()||(this.#e?.(),this.#e=void 0)}setEventListener(t){this.#s=t,this.#e?.(),this.#e=t((t=>{"boolean"==typeof t?this.setFocused(t):this.onFocus()}))}setFocused(t){this.#t!==t&&(this.#t=t,this.onFocus())}onFocus(){this.listeners.forEach((t=>{t()}))}isFocused(){return"boolean"==typeof this.#t?this.#t:"hidden"!==globalThis.document?.visibilityState}},P=new class extends C{#i=!0;#e;#s;constructor(){super(),this.#s=t=>{if(!s&&window.addEventListener){const e=()=>t(!0),s=()=>t(!1);return window.addEventListener("online",e,!1),window.addEventListener("offline",s,!1),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",s)}}}}onSubscribe(){this.#e||this.setEventListener(this.#s)}onUnsubscribe(){this.hasListeners()||(this.#e?.(),this.#e=void 0)}setEventListener(t){this.#s=t,this.#e?.(),this.#e=t(this.setOnline.bind(this))}setOnline(t){this.#i!==t&&(this.#i=t,this.listeners.forEach((e=>{e(t)})))}isOnline(){return this.#i}};function S(t){return Math.min(1e3*2**t,3e4)}function q(t){return"online"!==(t??"online")||P.isOnline()}var Q=class{constructor(t){this.revert=t?.revert,this.silent=t?.silent}};function F(t){return t instanceof Q}function E(t){let e,i,r,n=!1,a=0,o=!1;const u=new Promise(((t,e)=>{i=t,r=e})),c=()=>!w.isFocused()||"always"!==t.networkMode&&!P.isOnline(),h=s=>{o||(o=!0,t.onSuccess?.(s),e?.(),i(s))},l=s=>{o||(o=!0,t.onError?.(s),e?.(),r(s))},d=()=>new Promise((s=>{e=t=>{const e=o||!c();return e&&s(t),e},t.onPause?.()})).then((()=>{e=void 0,o||t.onContinue?.()})),f=()=>{if(o)return;let e;try{e=t.fn()}catch(t){e=Promise.reject(t)}Promise.resolve(e).then(h).catch((e=>{if(o)return;const i=t.retry??(s?0:3),r=t.retryDelay??S,u="function"==typeof r?r(a,e):r,h=!0===i||"number"==typeof i&&a<i||"function"==typeof i&&i(a,e);!n&&h?(a++,t.onFail?.(a,e),m(u).then((()=>{if(c())return d()})).then((()=>{n?l(e):f()}))):l(e)}))};return q(t.networkMode)?f():d().then(f),{promise:u,cancel:e=>{o||(l(new Q(e)),t.abort?.())},continue:()=>{const t=e?.();return t?u:Promise.resolve()},cancelRetry:()=>{n=!0},continueRetry:()=>{n=!1}}}var M=class{#r;destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),r(this.gcTime)&&(this.#r=setTimeout((()=>{this.optionalRemove()}),this.gcTime))}updateGcTime(t){this.gcTime=Math.max(this.gcTime||0,t??(s?1/0:3e5))}clearGcTimeout(){this.#r&&(clearTimeout(this.#r),this.#r=void 0)}},D=class extends M{constructor(t){super(),this.#n=!1,this.#a=t.defaultOptions,this.#o(t.options),this.#u=[],this.#c=t.cache,this.queryKey=t.queryKey,this.queryHash=t.queryHash,this.#h=t.state||function(t){const e="function"==typeof t.initialData?t.initialData():t.initialData,s=void 0!==e,i=s?"function"==typeof t.initialDataUpdatedAt?t.initialDataUpdatedAt():t.initialDataUpdatedAt:0;return{data:e,dataUpdateCount:0,dataUpdatedAt:s?i??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:s?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=this.#h,this.scheduleGc()}#h;#l;#c;#d;#f;#u;#a;#n;get meta(){return this.options.meta}#o(t){this.options={...this.#a,...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.#u.length||"idle"!==this.state.fetchStatus||this.#c.remove(this)}setData(t,e){const s=b(this.state.data,t,this.options);return this.#p({data:s,type:"success",dataUpdatedAt:e?.updatedAt,manual:e?.manual}),s}setState(t,e){this.#p({type:"setState",state:t,setStateOptions:e})}cancel(t){const e=this.#d;return this.#f?.cancel(t),e?e.then(i).catch(i):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#h)}isActive(){return this.#u.some((t=>!1!==t.options.enabled))}isDisabled(){return this.getObserversCount()>0&&!this.isActive()}isStale(){return this.state.isInvalidated||!this.state.dataUpdatedAt||this.#u.some((t=>t.getCurrentResult().isStale))}isStaleByTime(t=0){return this.state.isInvalidated||!this.state.dataUpdatedAt||!n(this.state.dataUpdatedAt,t)}onFocus(){const t=this.#u.find((t=>t.shouldFetchOnWindowFocus()));t?.refetch({cancelRefetch:!1}),this.#f?.continue()}onOnline(){const t=this.#u.find((t=>t.shouldFetchOnReconnect()));t?.refetch({cancelRefetch:!1}),this.#f?.continue()}addObserver(t){this.#u.includes(t)||(this.#u.push(t),this.clearGcTimeout(),this.#c.notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.#u.includes(t)&&(this.#u=this.#u.filter((e=>e!==t)),this.#u.length||(this.#f&&(this.#n?this.#f.cancel({revert:!0}):this.#f.cancelRetry()),this.scheduleGc()),this.#c.notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.#u.length}invalidate(){this.state.isInvalidated||this.#p({type:"invalidate"})}fetch(t,e){if("idle"!==this.state.fetchStatus)if(this.state.dataUpdatedAt&&e?.cancelRefetch)this.cancel({silent:!0});else if(this.#d)return this.#f?.continueRetry(),this.#d;if(t&&this.#o(t),!this.options.queryFn){const t=this.#u.find((t=>t.options.queryFn));t&&this.#o(t.options)}const s=new AbortController,i={queryKey:this.queryKey,meta:this.meta},r=t=>{Object.defineProperty(t,"signal",{enumerable:!0,get:()=>(this.#n=!0,s.signal)})};r(i);const n={fetchOptions:e,options:this.options,queryKey:this.queryKey,state:this.state,fetchFn:()=>this.options.queryFn?(this.#n=!1,this.options.persister?this.options.persister(this.options.queryFn,i,this):this.options.queryFn(i)):Promise.reject(new Error(`Missing queryFn: '${this.options.queryHash}'`))};r(n),this.options.behavior?.onFetch(n,this),this.#l=this.state,"idle"!==this.state.fetchStatus&&this.state.fetchMeta===n.fetchOptions?.meta||this.#p({type:"fetch",meta:n.fetchOptions?.meta});const a=t=>{F(t)&&t.silent||this.#p({type:"error",error:t}),F(t)||(this.#c.config.onError?.(t,this),this.#c.config.onSettled?.(this.state.data,t,this)),this.isFetchingOptimistic||this.scheduleGc(),this.isFetchingOptimistic=!1};return this.#f=E({fn:n.fetchFn,abort:s.abort.bind(s),onSuccess:t=>{void 0!==t?(this.setData(t),this.#c.config.onSuccess?.(t,this),this.#c.config.onSettled?.(t,this.state.error,this),this.isFetchingOptimistic||this.scheduleGc(),this.isFetchingOptimistic=!1):a(new Error(`${this.queryHash} data is undefined`))},onError:a,onFail:(t,e)=>{this.#p({type:"failed",failureCount:t,error:e})},onPause:()=>{this.#p({type:"pause"})},onContinue:()=>{this.#p({type:"continue"})},retry:n.options.retry,retryDelay:n.options.retryDelay,networkMode:n.options.networkMode}),this.#d=this.#f.promise,this.#d}#p(t){this.state=(e=>{switch(t.type){case"failed":return{...e,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...e,fetchStatus:"paused"};case"continue":return{...e,fetchStatus:"fetching"};case"fetch":return{...e,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:t.meta??null,fetchStatus:q(this.options.networkMode)?"fetching":"paused",...!e.dataUpdatedAt&&{error:null,status:"pending"}};case"success":return{...e,data:t.data,dataUpdateCount:e.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const s=t.error;return F(s)&&s.revert&&this.#l?{...this.#l,fetchStatus:"idle"}:{...e,error:s,errorUpdateCount:e.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:e.fetchFailureCount+1,fetchFailureReason:s,fetchStatus:"idle",status:"error"};case"invalidate":return{...e,isInvalidated:!0};case"setState":return{...e,...t.state}}})(this.state),R.batch((()=>{this.#u.forEach((t=>{t.onQueryUpdate()})),this.#c.notify({query:this,type:"updated",action:t})}))}},x=class extends C{constructor(t={}){super(),this.config=t,this.#y=new Map}#y;build(t,e,s){const i=e.queryKey,r=e.queryHash??u(i,e);let n=this.get(r);return n||(n=new D({cache:this,queryKey:i,queryHash:r,options:t.defaultQueryOptions(e),state:s,defaultOptions:t.getQueryDefaults(i)}),this.add(n)),n}add(t){this.#y.has(t.queryHash)||(this.#y.set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const e=this.#y.get(t.queryHash);e&&(t.destroy(),e===t&&this.#y.delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){R.batch((()=>{this.getAll().forEach((t=>{this.remove(t)}))}))}get(t){return this.#y.get(t)}getAll(){return[...this.#y.values()]}find(t){const e={exact:!0,...t};return this.getAll().find((t=>a(e,t)))}findAll(t={}){const e=this.getAll();return Object.keys(t).length>0?e.filter((e=>a(t,e))):e}notify(t){R.batch((()=>{this.listeners.forEach((e=>{e(t)}))}))}onFocus(){R.batch((()=>{this.getAll().forEach((t=>{t.onFocus()}))}))}onOnline(){R.batch((()=>{this.getAll().forEach((t=>{t.onOnline()}))}))}},I=class extends M{constructor(t){super(),this.mutationId=t.mutationId,this.#a=t.defaultOptions,this.#m=t.mutationCache,this.#u=[],this.state=t.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(t.options),this.scheduleGc()}#u;#a;#m;#f;setOptions(t){this.options={...this.#a,...t},this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){this.#u.includes(t)||(this.#u.push(t),this.clearGcTimeout(),this.#m.notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){this.#u=this.#u.filter((e=>e!==t)),this.scheduleGc(),this.#m.notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){this.#u.length||("pending"===this.state.status?this.scheduleGc():this.#m.remove(this))}continue(){return this.#f?.continue()??this.execute(this.state.variables)}async execute(t){const e=()=>(this.#f=E({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(t,e)=>{this.#p({type:"failed",failureCount:t,error:e})},onPause:()=>{this.#p({type:"pause"})},onContinue:()=>{this.#p({type:"continue"})},retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode}),this.#f.promise),s="pending"===this.state.status;try{if(!s){this.#p({type:"pending",variables:t}),await(this.#m.config.onMutate?.(t,this));const e=await(this.options.onMutate?.(t));e!==this.state.context&&this.#p({type:"pending",context:e,variables:t})}const i=await e();return await(this.#m.config.onSuccess?.(i,t,this.state.context,this)),await(this.options.onSuccess?.(i,t,this.state.context)),await(this.#m.config.onSettled?.(i,null,this.state.variables,this.state.context,this)),await(this.options.onSettled?.(i,null,t,this.state.context)),this.#p({type:"success",data:i}),i}catch(e){try{throw await(this.#m.config.onError?.(e,t,this.state.context,this)),await(this.options.onError?.(e,t,this.state.context)),await(this.#m.config.onSettled?.(void 0,e,this.state.variables,this.state.context,this)),await(this.options.onSettled?.(void 0,e,t,this.state.context)),e}finally{this.#p({type:"error",error:e})}}}#p(t){this.state=(e=>{switch(t.type){case"failed":return{...e,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...e,isPaused:!0};case"continue":return{...e,isPaused:!1};case"pending":return{...e,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:!q(this.options.networkMode),status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...e,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...e,data:void 0,error:t.error,failureCount:e.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}})(this.state),R.batch((()=>{this.#u.forEach((e=>{e.onMutationUpdate(t)})),this.#m.notify({mutation:this,type:"updated",action:t})}))}},A=class extends C{constructor(t={}){super(),this.config=t,this.#v=[],this.#b=0}#v;#b;#g;build(t,e,s){const i=new I({mutationCache:this,mutationId:++this.#b,options:t.defaultMutationOptions(e),state:s});return this.add(i),i}add(t){this.#v.push(t),this.notify({type:"added",mutation:t})}remove(t){this.#v=this.#v.filter((e=>e!==t)),this.notify({type:"removed",mutation:t})}clear(){R.batch((()=>{this.#v.forEach((t=>{this.remove(t)}))}))}getAll(){return this.#v}find(t){const e={exact:!0,...t};return this.#v.find((t=>o(e,t)))}findAll(t={}){return this.#v.filter((e=>o(t,e)))}notify(t){R.batch((()=>{this.listeners.forEach((e=>{e(t)}))}))}resumePausedMutations(){return this.#g=(this.#g??Promise.resolve()).then((()=>{const t=this.#v.filter((t=>t.state.isPaused));return R.batch((()=>t.reduce(((t,e)=>t.then((()=>e.continue().catch(i)))),Promise.resolve())))})).then((()=>{this.#g=void 0})),this.#g}};function T(t){return{onFetch:(e,s)=>{const i=async()=>{const s=e.options,i=e.fetchOptions?.meta?.fetchMore?.direction,r=e.state.data?.pages||[],n=e.state.data?.pageParams||[],a={pages:[],pageParams:[]};let o=!1;const u=e.options.queryFn||(()=>Promise.reject(new Error(`Missing queryFn: '${e.options.queryHash}'`))),c=async(t,s,i)=>{if(o)return Promise.reject();if(null==s&&t.pages.length)return Promise.resolve(t);const r={queryKey:e.queryKey,pageParam:s,direction:i?"backward":"forward",meta:e.options.meta};var n;n=r,Object.defineProperty(n,"signal",{enumerable:!0,get:()=>(e.signal.aborted?o=!0:e.signal.addEventListener("abort",(()=>{o=!0})),e.signal)});const a=await u(r),{maxPages:c}=e.options,h=i?O:g;return{pages:h(t.pages,a,c),pageParams:h(t.pageParams,s,c)}};let h;if(i&&r.length){const t="backward"===i,e={pages:r,pageParams:n},a=(t?K:U)(s,e);h=await c(e,a,t)}else{h=await c(a,n[0]??s.initialPageParam);const e=t??r.length;for(let t=1;t<e;t++){const t=U(s,h);h=await c(h,t)}}return h};e.options.persister?e.fetchFn=()=>e.options.persister?.(i,{queryKey:e.queryKey,meta:e.options.meta,signal:e.signal},s):e.fetchFn=i}}}function U(t,{pages:e,pageParams:s}){const i=e.length-1;return t.getNextPageParam(e[i],e,s[i],s)}function K(t,{pages:e,pageParams:s}){return t.getPreviousPageParam?.(e[0],e,s[0],s)}function k(t,e){return!!e&&null!=U(t,e)}function L(t,e){return!(!e||!t.getPreviousPageParam)&&null!=K(t,e)}var j=class{#O;#m;#a;#R;#C;#w;#P;#S;constructor(t={}){this.#O=t.queryCache||new x,this.#m=t.mutationCache||new A,this.#a=t.defaultOptions||{},this.#R=new Map,this.#C=new Map,this.#w=0}mount(){this.#w++,1===this.#w&&(this.#P=w.subscribe((()=>{w.isFocused()&&(this.resumePausedMutations(),this.#O.onFocus())})),this.#S=P.subscribe((()=>{P.isOnline()&&(this.resumePausedMutations(),this.#O.onOnline())})))}unmount(){this.#w--,0===this.#w&&(this.#P?.(),this.#P=void 0,this.#S?.(),this.#S=void 0)}isFetching(t){return this.#O.findAll({...t,fetchStatus:"fetching"}).length}isMutating(t){return this.#m.findAll({...t,status:"pending"}).length}getQueryData(t){return this.#O.find({queryKey:t})?.state.data}ensureQueryData(t){const e=this.getQueryData(t.queryKey);return void 0!==e?Promise.resolve(e):this.fetchQuery(t)}getQueriesData(t){return this.getQueryCache().findAll(t).map((({queryKey:t,state:e})=>[t,e.data]))}setQueryData(t,e,s){const i=this.#O.find({queryKey:t}),r=i?.state.data,n=function(t,e){return"function"==typeof t?t(e):t}(e,r);if(void 0===n)return;const a=this.defaultQueryOptions({queryKey:t});return this.#O.build(this,a).setData(n,{...s,manual:!0})}setQueriesData(t,e,s){return R.batch((()=>this.getQueryCache().findAll(t).map((({queryKey:t})=>[t,this.setQueryData(t,e,s)]))))}getQueryState(t){return this.#O.find({queryKey:t})?.state}removeQueries(t){const e=this.#O;R.batch((()=>{e.findAll(t).forEach((t=>{e.remove(t)}))}))}resetQueries(t,e){const s=this.#O,i={type:"active",...t};return R.batch((()=>(s.findAll(t).forEach((t=>{t.reset()})),this.refetchQueries(i,e))))}cancelQueries(t={},e={}){const s={revert:!0,...e},r=R.batch((()=>this.#O.findAll(t).map((t=>t.cancel(s)))));return Promise.all(r).then(i).catch(i)}invalidateQueries(t={},e={}){return R.batch((()=>{if(this.#O.findAll(t).forEach((t=>{t.invalidate()})),"none"===t.refetchType)return Promise.resolve();const s={...t,type:t.refetchType??t.type??"active"};return this.refetchQueries(s,e)}))}refetchQueries(t={},e){const s={...e,cancelRefetch:e?.cancelRefetch??!0},r=R.batch((()=>this.#O.findAll(t).filter((t=>!t.isDisabled())).map((t=>{let e=t.fetch(void 0,s);return s.throwOnError||(e=e.catch(i)),"paused"===t.state.fetchStatus?Promise.resolve():e}))));return Promise.all(r).then(i)}fetchQuery(t){const e=this.defaultQueryOptions(t);void 0===e.retry&&(e.retry=!1);const s=this.#O.build(this,e);return s.isStaleByTime(e.staleTime)?s.fetch(e):Promise.resolve(s.state.data)}prefetchQuery(t){return this.fetchQuery(t).then(i).catch(i)}fetchInfiniteQuery(t){return t.behavior=T(t.pages),this.fetchQuery(t)}prefetchInfiniteQuery(t){return this.fetchInfiniteQuery(t).then(i).catch(i)}resumePausedMutations(){return this.#m.resumePausedMutations()}getQueryCache(){return this.#O}getMutationCache(){return this.#m}getDefaultOptions(){return this.#a}setDefaultOptions(t){this.#a=t}setQueryDefaults(t,e){this.#R.set(c(t),{queryKey:t,defaultOptions:e})}getQueryDefaults(t){const e=[...this.#R.values()];let s={};return e.forEach((e=>{h(t,e.queryKey)&&(s={...s,...e.defaultOptions})})),s}setMutationDefaults(t,e){this.#C.set(c(t),{mutationKey:t,defaultOptions:e})}getMutationDefaults(t){const e=[...this.#C.values()];let s={};return e.forEach((e=>{h(t,e.mutationKey)&&(s={...s,...e.defaultOptions})})),s}defaultQueryOptions(t){if(t?._defaulted)return t;const e={...this.#a.queries,...t?.queryKey&&this.getQueryDefaults(t.queryKey),...t,_defaulted:!0};return e.queryHash||(e.queryHash=u(e.queryKey,e)),void 0===e.refetchOnReconnect&&(e.refetchOnReconnect="always"!==e.networkMode),void 0===e.throwOnError&&(e.throwOnError=!!e.suspense),void 0===e.networkMode&&e.persister&&(e.networkMode="offlineFirst"),e}defaultMutationOptions(t){return t?._defaulted?t:{...this.#a.mutations,...t?.mutationKey&&this.getMutationDefaults(t.mutationKey),...t,_defaulted:!0}}clear(){this.#O.clear(),this.#m.clear()}},G=window.React,H=G.createContext(void 0),N=t=>{const e=G.useContext(H);if(t)return t;if(!e)throw new Error("No QueryClient set, use QueryClientProvider to set one");return e},_=({client:t,children:e})=>(G.useEffect((()=>(t.mount(),()=>{t.unmount()})),[t]),G.createElement(H.Provider,{value:t},e)),W=class extends C{constructor(t,e){super(),this.#q=void 0,this.#Q=void 0,this.#F=void 0,this.#E=new Set,this.#M=t,this.options=e,this.#D=null,this.bindMethods(),this.setOptions(e)}#M;#q;#Q;#F;#x;#I;#D;#A;#T;#U;#K;#k;#L;#E;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(this.#q.addObserver(this),B(this.#q,this.options)&&this.#j(),this.#G())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return z(this.#q,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return z(this.#q,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#H(),this.#N(),this.#q.removeObserver(this)}setOptions(t,e){const s=this.options,i=this.#q;if(this.options=this.#M.defaultQueryOptions(t),d(s,this.options)||this.#M.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#q,observer:this}),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled)throw new Error("Expected enabled to be a boolean");this.options.queryKey||(this.options.queryKey=s.queryKey),this.#_();const r=this.hasListeners();r&&$(this.#q,i,this.options,s)&&this.#j(),this.updateResult(e),!r||this.#q===i&&this.options.enabled===s.enabled&&this.options.staleTime===s.staleTime||this.#W();const n=this.#B();!r||this.#q===i&&this.options.enabled===s.enabled&&n===this.#L||this.#z(n)}getOptimisticResult(t){const e=this.#M.getQueryCache().build(this.#M,t),s=this.createResult(e,t);return i=s,!d(this.getCurrentResult(),i)&&(this.#F=s,this.#I=this.options,this.#x=this.#q.state),s;var i}getCurrentResult(){return this.#F}trackResult(t){const e={};return Object.keys(t).forEach((s=>{Object.defineProperty(e,s,{configurable:!1,enumerable:!0,get:()=>(this.#E.add(s),t[s])})})),e}getCurrentQuery(){return this.#q}refetch({...t}={}){return this.fetch({...t})}fetchOptimistic(t){const e=this.#M.defaultQueryOptions(t),s=this.#M.getQueryCache().build(this.#M,e);return s.isFetchingOptimistic=!0,s.fetch().then((()=>this.createResult(s,e)))}fetch(t){return this.#j({...t,cancelRefetch:t.cancelRefetch??!0}).then((()=>(this.updateResult(),this.#F)))}#j(t){this.#_();let e=this.#q.fetch(this.options,t);return t?.throwOnError||(e=e.catch(i)),e}#W(){if(this.#H(),s||this.#F.isStale||!r(this.options.staleTime))return;const t=n(this.#F.dataUpdatedAt,this.options.staleTime)+1;this.#K=setTimeout((()=>{this.#F.isStale||this.updateResult()}),t)}#B(){return("function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.#q):this.options.refetchInterval)??!1}#z(t){this.#N(),this.#L=t,!s&&!1!==this.options.enabled&&r(this.#L)&&0!==this.#L&&(this.#k=setInterval((()=>{(this.options.refetchIntervalInBackground||w.isFocused())&&this.#j()}),this.#L))}#G(){this.#W(),this.#z(this.#B())}#H(){this.#K&&(clearTimeout(this.#K),this.#K=void 0)}#N(){this.#k&&(clearInterval(this.#k),this.#k=void 0)}createResult(t,e){const s=this.#q,i=this.options,r=this.#F,n=this.#x,a=this.#I,o=t!==s?t.state:this.#Q,{state:u}=t;let c,{error:h,errorUpdatedAt:l,fetchStatus:d,status:f}=u,p=!1;if(e._optimisticResults){const r=this.hasListeners(),n=!r&&B(t,e),a=r&&$(t,s,e,i);(n||a)&&(d=q(t.options.networkMode)?"fetching":"paused",u.dataUpdatedAt||(f="pending")),"isRestoring"===e._optimisticResults&&(d="idle")}if(e.select&&void 0!==u.data)if(r&&u.data===n?.data&&e.select===this.#A)c=this.#T;else try{this.#A=e.select,c=e.select(u.data),c=b(r?.data,c,e),this.#T=c,this.#D=null}catch(t){this.#D=t}else c=u.data;if(void 0!==e.placeholderData&&void 0===c&&"pending"===f){let t;if(r?.isPlaceholderData&&e.placeholderData===a?.placeholderData)t=r.data;else if(t="function"==typeof e.placeholderData?e.placeholderData(this.#U?.state.data,this.#U):e.placeholderData,e.select&&void 0!==t)try{t=e.select(t),this.#D=null}catch(t){this.#D=t}void 0!==t&&(f="success",c=b(r?.data,t,e),p=!0)}this.#D&&(h=this.#D,c=this.#T,l=Date.now(),f="error");const y="fetching"===d,m="pending"===f,v="error"===f,g=m&&y;return{status:f,fetchStatus:d,isPending:m,isSuccess:"success"===f,isError:v,isInitialLoading:g,isLoading:g,data:c,dataUpdatedAt:u.dataUpdatedAt,error:h,errorUpdatedAt:l,failureCount:u.fetchFailureCount,failureReason:u.fetchFailureReason,errorUpdateCount:u.errorUpdateCount,isFetched:u.dataUpdateCount>0||u.errorUpdateCount>0,isFetchedAfterMount:u.dataUpdateCount>o.dataUpdateCount||u.errorUpdateCount>o.errorUpdateCount,isFetching:y,isRefetching:y&&!m,isLoadingError:v&&0===u.dataUpdatedAt,isPaused:"paused"===d,isPlaceholderData:p,isRefetchError:v&&0!==u.dataUpdatedAt,isStale:V(t,e),refetch:this.refetch}}updateResult(t){const e=this.#F,s=this.createResult(this.#q,this.options);if(this.#x=this.#q.state,this.#I=this.options,d(s,e))return;void 0!==this.#x.data&&(this.#U=this.#q),this.#F=s;const i={};!1!==t?.listeners&&(()=>{if(!e)return!0;const{notifyOnChangeProps:t}=this.options,s="function"==typeof t?t():t;if("all"===s||!s&&!this.#E.size)return!0;const i=new Set(s??this.#E);return this.options.throwOnError&&i.add("error"),Object.keys(this.#F).some((t=>{const s=t;return this.#F[s]!==e[s]&&i.has(s)}))})()&&(i.listeners=!0),this.#$({...i,...t})}#_(){const t=this.#M.getQueryCache().build(this.#M,this.options);if(t===this.#q)return;const e=this.#q;this.#q=t,this.#Q=t.state,this.hasListeners()&&(e?.removeObserver(this),t.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#G()}#$(t){R.batch((()=>{t.listeners&&this.listeners.forEach((t=>{t(this.#F)})),this.#M.getQueryCache().notify({query:this.#q,type:"observerResultsUpdated"})}))}};function B(t,e){return function(t,e){return!(!1===e.enabled||t.state.dataUpdatedAt||"error"===t.state.status&&!1===e.retryOnMount)}(t,e)||t.state.dataUpdatedAt>0&&z(t,e,e.refetchOnMount)}function z(t,e,s){if(!1!==e.enabled){const i="function"==typeof s?s(t):s;return"always"===i||!1!==i&&V(t,e)}return!1}function $(t,e,s,i){return!1!==s.enabled&&(t!==e||!1===i.enabled)&&(!s.suspense||"error"!==t.state.status)&&V(t,s)}function V(t,e){return t.isStaleByTime(e.staleTime)}var J=class extends W{constructor(t,e){super(t,e)}bindMethods(){super.bindMethods(),this.fetchNextPage=this.fetchNextPage.bind(this),this.fetchPreviousPage=this.fetchPreviousPage.bind(this)}setOptions(t,e){super.setOptions({...t,behavior:T()},e)}getOptimisticResult(t){return t.behavior=T(),super.getOptimisticResult(t)}fetchNextPage(t){return this.fetch({...t,meta:{fetchMore:{direction:"forward"}}})}fetchPreviousPage(t){return this.fetch({...t,meta:{fetchMore:{direction:"backward"}}})}createResult(t,e){const{state:s}=t,i=super.createResult(t,e),{isFetching:r,isRefetching:n}=i,a=r&&"forward"===s.fetchMeta?.fetchMore?.direction,o=r&&"backward"===s.fetchMeta?.fetchMore?.direction;return{...i,fetchNextPage:this.fetchNextPage,fetchPreviousPage:this.fetchPreviousPage,hasNextPage:k(e,s.data),hasPreviousPage:L(e,s.data),isFetchingNextPage:a,isFetchingPreviousPage:o,isRefetching:n&&!a&&!o}}};var X=G.createContext(function(){let t=!1;return{clearReset:()=>{t=!1},reset:()=>{t=!0},isReset:()=>t}}()),Y=()=>G.useContext(X),Z=G.createContext(!1),tt=()=>G.useContext(Z);function et(t,e){return"function"==typeof t?t(...e):!!t}Z.Provider;var st=(t,e)=>{(t.suspense||t.throwOnError)&&(e.isReset()||(t.retryOnMount=!1))},it=t=>{G.useEffect((()=>{t.clearReset()}),[t])},rt=({result:t,errorResetBoundary:e,throwOnError:s,query:i})=>t.isError&&!e.isReset()&&!t.isFetching&&et(s,[t.error,i]),nt=t=>{t.suspense&&"number"!=typeof t.staleTime&&(t.staleTime=1e3)},at=(t,e,s)=>t?.suspense&&((t,e)=>t.isLoading&&t.isFetching&&!e)(e,s),ot=(t,e,s)=>e.fetchOptimistic(t).catch((()=>{s.clearReset()}));function ut(t,e,s){const i=N(s),r=tt(),n=Y(),a=i.defaultQueryOptions(t);a._optimisticResults=r?"isRestoring":"optimistic",nt(a),st(a,n),it(n);const[o]=G.useState((()=>new e(i,a))),u=o.getOptimisticResult(a);if(G.useSyncExternalStore(G.useCallback((t=>{const e=r?()=>{}:o.subscribe(R.batchCalls(t));return o.updateResult(),e}),[o,r]),(()=>o.getCurrentResult()),(()=>o.getCurrentResult())),G.useEffect((()=>{o.setOptions(a,{listeners:!1})}),[a,o]),at(a,u,r))throw ot(a,o,n);if(rt({result:u,errorResetBoundary:n,throwOnError:a.throwOnError,query:o.getCurrentQuery()}))throw u.error;return a.notifyOnChangeProps?u:o.trackResult(u)}function ct(t,e){return ut(t,J,e)}var ht=class extends C{constructor(t,e){super(),this.#F=void 0,this.#M=t,this.setOptions(e),this.bindMethods(),this.#V()}#M;#F;#J;#X;bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(t){const e=this.options;this.options=this.#M.defaultMutationOptions(t),d(e,this.options)||this.#M.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#J,observer:this}),this.#J?.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#J?.removeObserver(this)}onMutationUpdate(t){this.#V(),this.#$(t)}getCurrentResult(){return this.#F}reset(){this.#J=void 0,this.#V(),this.#$()}mutate(t,e){return this.#X=e,this.#J?.removeObserver(this),this.#J=this.#M.getMutationCache().build(this.#M,this.options),this.#J.addObserver(this),this.#J.execute(t)}#V(){const t=this.#J?.state??{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0};this.#F={...t,isPending:"pending"===t.status,isSuccess:"success"===t.status,isError:"error"===t.status,isIdle:"idle"===t.status,mutate:this.mutate,reset:this.reset}}#$(t){R.batch((()=>{this.#X&&this.hasListeners()&&("success"===t?.type?(this.#X.onSuccess?.(t.data,this.#F.variables,this.#F.context),this.#X.onSettled?.(t.data,null,this.#F.variables,this.#F.context)):"error"===t?.type&&(this.#X.onError?.(t.error,this.#F.variables,this.#F.context),this.#X.onSettled?.(void 0,t.error,this.#F.variables,this.#F.context))),this.listeners.forEach((t=>{t(this.#F)}))}))}};function lt(t,e){const s=N(e),[i]=G.useState((()=>new ht(s,t)));G.useEffect((()=>{i.setOptions(t)}),[i,t]);const r=G.useSyncExternalStore(G.useCallback((t=>i.subscribe(R.batchCalls(t))),[i]),(()=>i.getCurrentResult()),(()=>i.getCurrentResult())),n=G.useCallback(((t,e)=>{i.mutate(t,e).catch(dt)}),[i]);if(r.error&&et(i.options.throwOnError,[r.error]))throw r.error;return{...r,mutate:n,mutateAsync:r.mutate}}function dt(){}function ft(t,e){return ut(t,W,e)}function pt(){return new j({defaultOptions:{queries:{refetchOnWindowFocus:!1,refetchOnReconnect:!1}}})}(window.elementorV2=window.elementorV2||{}).query=e}();