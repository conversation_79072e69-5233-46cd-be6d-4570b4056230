!function(){"use strict";var t={d:function(e,n){for(var i in n)t.o(n,i)&&!t.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:n[i]})},o:function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r:function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})}},e={};t.r(e),t.d(e,{__useActiveDocument:function(){return f},__useActiveDocumentActions:function(){return g},__useHostDocument:function(){return h},__useNavigateToDocument:function(){return y},slice:function(){return s}});var n=window.elementorV2.store,i=window.elementorV2.editorV1Adapters,a=window.React;function o(t){return!(!t.activeId||!t.entities[t.activeId])}var s=(0,n.__createSlice)({name:"documents",initialState:{entities:{},activeId:null,hostId:null},reducers:{init(t,{payload:e}){t.entities=e.entities,t.hostId=e.hostId,t.activeId=e.activeId},activateDocument(t,e){t.entities[e.payload.id]=e.payload,t.activeId=e.payload.id},setAsHost(t,e){t.hostId=e.payload},updateActiveDocument(t,e){o(t)&&(t.entities[t.activeId]={...t.entities[t.activeId],...e.payload})},startSaving(t){o(t)&&(t.entities[t.activeId].isSaving=!0)},endSaving(t,e){o(t)&&(t.entities[t.activeId]={...e.payload,isSaving:!1})},startSavingDraft:t=>{o(t)&&(t.entities[t.activeId].isSavingDraft=!0)},endSavingDraft(t,e){o(t)&&(t.entities[t.activeId]={...e.payload,isSavingDraft:!1})},markAsDirty(t){o(t)&&(t.entities[t.activeId].isDirty=!0)},markAsPristine(t){o(t)&&(t.entities[t.activeId].isDirty=!1)}}});function r(){const t=window.elementor?.documents;if(!t)throw new Error("Elementor Editor V1 documents manager not found");return t}function c(t){switch(window.elementor?.getPreferences?.("exit_to")||"this_post"){case"dashboard":return t.config.urls.main_dashboard;case"all_posts":return t.config.urls.all_post_type;default:return t.config.urls.exit_to_dashboard}}function d(t){return t?.config?.panel?.show_copy_and_share??!1}function u(t){return t.config.urls.permalink??""}function l(t){const e=t.config.revisions.current_id!==t.id,n=c(t);return{id:t.id,title:t.container.settings.get("post_title"),type:{value:t.config.type,label:t.config.panel.title},status:{value:t.config.status.value,label:t.config.status.label},links:{permalink:u(t),platformEdit:n},isDirty:t.editor.isChanged||e,isSaving:t.editor.isSaving,isSavingDraft:!1,permissions:{allowAddingWidgets:t.config.panel?.allow_adding_widgets??!0,showCopyAndShare:d(t)},userCan:{publish:t.config.user.can_publish}}}function _(t,e){let n;return(...i)=>{clearTimeout(n),n=setTimeout((()=>{t(...i)}),e)}}var v=t=>t.documents.entities,m=(0,n.__createSelector)(v,(t=>t.documents.activeId),((t,e)=>e&&t[e]?t[e]:null)),p=(0,n.__createSelector)(v,(t=>t.documents.hostId),((t,e)=>e&&t[e]?t[e]:null));function f(){return(0,n.__useSelector)(m)}function g(){const t=f(),e=t?.links?.permalink??"";return{save:(0,a.useCallback)((()=>(0,i.__privateRunCommand)("document/save/default")),[]),saveDraft:(0,a.useCallback)((()=>(0,i.__privateRunCommand)("document/save/draft")),[]),saveTemplate:(0,a.useCallback)((()=>(0,i.__privateOpenRoute)("library/save-template")),[]),copyAndShare:(0,a.useCallback)((()=>{navigator.clipboard.writeText(e)}),[e])}}function h(){return(0,n.__useSelector)(p)}function y(){return(0,a.useCallback)((async t=>{await(0,i.__privateRunCommand)("editor/documents/switch",{id:t,setAsInitial:!0});const e=new URL(window.location.href);e.searchParams.set("post",t.toString()),e.searchParams.delete("active-document"),history.replaceState({},"",e)}),[])}(0,n.__registerSlice)(s),function(){const{init:t}=s.actions;(0,i.__privateListenTo)((0,i.v1ReadyEvent)(),(()=>{const e=r(),i=Object.entries(e.documents).reduce(((t,[e,n])=>(t[e]=l(n),t)),{});(0,n.__dispatch)(t({entities:i,hostId:e.getInitialId(),activeId:e.getCurrentId()}))}))}(),function(){const{activateDocument:t,setAsHost:e}=s.actions;(0,i.__privateListenTo)((0,i.commandEndEvent)("editor/documents/open"),(()=>{const i=r(),a=l(i.getCurrent());(0,n.__dispatch)(t(a)),i.getInitialId()===a.id&&(0,n.__dispatch)(e(a.id))}))}(),function(){const{startSaving:t,endSaving:e,startSavingDraft:a,endSavingDraft:o}=s.actions,c=t=>{const e=t;return"autosave"===e.args?.status};(0,i.__privateListenTo)((0,i.commandStartEvent)("document/save/save"),(e=>{c(e)?(0,n.__dispatch)(a()):(0,n.__dispatch)(t())})),(0,i.__privateListenTo)((0,i.commandEndEvent)("document/save/save"),(t=>{const i=l(r().getCurrent());c(t)?(0,n.__dispatch)(o(i)):(0,n.__dispatch)(e(i))}))}(),function(){const{updateActiveDocument:t}=s.actions,e=_((e=>{const i=e;if(!("post_title"in i.args?.settings))return;const a=r().getCurrent().container.settings.get("post_title");(0,n.__dispatch)(t({title:a}))}),400);(0,i.__privateListenTo)((0,i.commandEndEvent)("document/elements/settings"),e)}(),function(){const{markAsDirty:t,markAsPristine:e}=s.actions;(0,i.__privateListenTo)((0,i.commandEndEvent)("document/save/set-is-modified"),(()=>{r().getCurrent().editor.isChanged?(0,n.__dispatch)(t()):(0,n.__dispatch)(e())}))}(),function(){const{updateActiveDocument:t}=s.actions,e=_((e=>{const i=e;if(!("exit_to"in i.args?.settings))return;const a=r().getCurrent(),o=c(a),s=u(a);(0,n.__dispatch)(t({links:{platformEdit:o,permalink:s}}))}),400);(0,i.__privateListenTo)((0,i.commandEndEvent)("document/elements/settings"),e)}(),(window.elementorV2=window.elementorV2||{}).editorDocuments=e}();