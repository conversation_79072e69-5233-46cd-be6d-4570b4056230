!function(){"use strict";var e={d:function(t,n){for(var r in n)e.o(n,r)&&!e.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:n[r]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{__privateDispatchReadyEvent:function(){return p},__privateFlushListeners:function(){return g},__privateGetCurrentEditMode:function(){return P},__privateIsRouteActive:function(){return b},__privateListenTo:function(){return y},__privateOpenRoute:function(){return o},__privateRegisterRoute:function(){return i},__privateRunCommand:function(){return r},__privateSetReady:function(){return v},__privateUseIsPreviewMode:function(){return j},__privateUseIsRouteActive:function(){return O},__privateUseListenTo:function(){return h},__privateUseRouteStatus:function(){return $},commandEndEvent:function(){return a},commandStartEvent:function(){return u},editModeChangeEvent:function(){return l},routeCloseEvent:function(){return s},routeOpenEvent:function(){return c},v1ReadyEvent:function(){return f},windowEvent:function(){return d}});var n=window.React;function r(e,t){const n=window;if(!n.$e?.run)return Promise.reject("`$e.run()` is not available");const r=n.$e.run(e,t);return r instanceof Promise?r:(i=r)&&"object"==typeof i&&Object.hasOwn(i,"promise")&&Object.hasOwn(i,"then")&&Object.hasOwn(i,"fail")?(o=r,new Promise(((e,t)=>{o.then(e,t)}))):Promise.resolve(r);var o,i}function o(e){const t=window;if(!t.$e?.route)return Promise.reject("`$e.route()` is not available");try{return Promise.resolve(t.$e.route(e))}catch(e){return Promise.reject(e)}}function i(e){const t=window;if(!t.$e?.routes?.register)return Promise.reject("`$e.routes.register()` is not available");const n=e.split("/");if(n.length<2)return Promise.reject(`\`${e}\` is an invalid route`);const r=n.pop(),o=n.join("/");try{return Promise.resolve(t.$e.routes.register(o,r,(()=>null)))}catch(e){return Promise.reject(e)}}var u=e=>({type:"command",name:e,state:"before"}),a=e=>({type:"command",name:e,state:"after"}),c=e=>({type:"route",name:e,state:"open"}),s=e=>({type:"route",name:e,state:"close"}),d=e=>({type:"window-event",name:e}),f=()=>d("elementor/initialized"),l=()=>d("elementor/edit-mode/change"),m=!1;function v(e){m=e}function p(){return function(){const e=window.__elementorEditorV1LoadingPromise;return e||Promise.reject("Elementor Editor V1 is not loaded")}().then((()=>{v(!0),window.dispatchEvent(new CustomEvent("elementor/initialized"))}))}var w=new Map,_=new AbortController;function y(e,t){Array.isArray(e)||(e=[e]);const n=e.map((e=>{const{type:n,name:r}=e;switch(n){case"command":return function(e,t,n){return E(`elementor/commands/run/${t}`,(t=>{"command"===t.type&&t.command===e&&n(t)}))}(r,e.state,t);case"route":return function(e,t,n){return E(`elementor/routes/${t}`,(t=>{"route"===t.type&&t.route.startsWith(e)&&n(t)}))}(r,e.state,t);case"window-event":return E(r,t)}}));return()=>{n.forEach((e=>e()))}}function g(){_.abort(),w.clear(),v(!1),_=new AbortController}function E(e,t){return!w.has(e)&&(w.set(e,[]),function(e){window.addEventListener(e,function(e){return t=>{if(!m)return;const n=function(e){return e instanceof CustomEvent&&e.detail?.command?{type:"command",command:e.detail.command,args:e.detail.args,originalEvent:e}:e instanceof CustomEvent&&e.detail?.route?{type:"route",route:e.detail.route,originalEvent:e}:{type:"window-event",event:e.type,originalEvent:e}}(t);w.get(e)?.forEach((e=>{e(n)}))}}(e),{signal:_.signal})}(e)),w.get(e)?.push(t),()=>{const n=w.get(e);if(!n?.length)return;const r=n.filter((e=>e!==t));w.set(e,r)}}function h(e,t,r=[]){const[o,i]=(0,n.useState)((()=>t()));return(0,n.useEffect)((()=>{const n=()=>i(t());return n(),y(e,n)}),r),o}function b(e){const t=window;return!!t.$e?.routes?.isPartOf(e)}function P(){const e=window;return e.elementor?.channels?.dataEditMode?.request?.("activeMode")}function j(){return h(l(),(()=>"preview"===P()))}function O(e){return h([c(e),s(e)],(()=>b(e)),[e])}function $(e,{blockOnKitRoutes:t=!0,blockOnPreviewMode:n=!0}={}){const r=O(e),o=O("panel/global"),i=j();return{isActive:r&&!(n&&i),isBlocked:n&&i||t&&o}}(window.elementorV2=window.elementorV2||{}).editorV1Adapters=t}();