!function(){"use strict";var e={d:function(t,n){for(var r in n)e.o(n,r)&&!e.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:n[r]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{commandEndEvent:function(){return u},commandStartEvent:function(){return i},dispatchReadyEvent:function(){return v},editModeChangeEvent:function(){return f},flushListeners:function(){return g},getCurrentEditMode:function(){return O},isReady:function(){return l},isRouteActive:function(){return P},listenTo:function(){return E},openRoute:function(){return o},routeCloseEvent:function(){return a},routeOpenEvent:function(){return c},runCommand:function(){return r},setReady:function(){return w},useIsPreviewMode:function(){return j},useIsRouteActive:function(){return _},useListenTo:function(){return h},useRouteStatus:function(){return A},v1ReadyEvent:function(){return d},windowEvent:function(){return s}});var n=window.React;function r(e,t){const n=window;if(!n.$e?.run)return Promise.reject("`$e.run()` is not available");const r=n.$e.run(e,t);return r instanceof Promise?r:(i=r)&&"object"==typeof i&&Object.hasOwn(i,"promise")&&Object.hasOwn(i,"then")&&Object.hasOwn(i,"fail")?(o=r,new Promise(((e,t)=>{o.then(e,t)}))):Promise.resolve(r);var o,i}function o(e){const t=window;if(!t.$e?.route)return Promise.reject("`$e.route()` is not available");try{return Promise.resolve(t.$e.route(e))}catch(e){return Promise.reject(e)}}var i=e=>({type:"command",name:e,state:"before"}),u=e=>({type:"command",name:e,state:"after"}),c=e=>({type:"route",name:e,state:"open"}),a=e=>({type:"route",name:e,state:"close"}),s=e=>({type:"window-event",name:e}),d=()=>s("elementor/initialized"),f=()=>s("elementor/edit-mode/change"),m=!1;function l(){return m}function w(e){m=e}function v(){return function(){const e=window.__elementorEditorV1LoadingPromise;return e||Promise.reject("Elementor Editor V1 is not loaded")}().then((()=>{w(!0),window.dispatchEvent(new CustomEvent("elementor/initialized"))}))}var p=new Map,y=new AbortController;function E(e,t){Array.isArray(e)||(e=[e]);const n=e.map((e=>{const{type:n,name:r}=e;switch(n){case"command":return function(e,t,n){return b(`elementor/commands/run/${t}`,(t=>{"command"===t.type&&t.command===e&&n(t)}))}(r,e.state,t);case"route":return function(e,t,n){return b(`elementor/routes/${t}`,(t=>{"route"===t.type&&t.route.startsWith(e)&&n(t)}))}(r,e.state,t);case"window-event":return b(r,t)}}));return()=>{n.forEach((e=>e()))}}function g(){y.abort(),p.clear(),w(!1),y=new AbortController}function b(e,t){return!p.has(e)&&(p.set(e,[]),function(e){window.addEventListener(e,function(e){return t=>{if(!l())return;const n=function(e){return e instanceof CustomEvent&&e.detail?.command?{type:"command",command:e.detail.command,args:e.detail.args,originalEvent:e}:e instanceof CustomEvent&&e.detail?.route?{type:"route",route:e.detail.route,originalEvent:e}:{type:"window-event",event:e.type,originalEvent:e}}(t);p.get(e)?.forEach((e=>{e(n)}))}}(e),{signal:y.signal})}(e)),p.get(e)?.push(t),()=>{const n=p.get(e);if(!n?.length)return;const r=n.filter((e=>e!==t));p.set(e,r)}}function h(e,t,r=[]){const[o,i]=(0,n.useState)((()=>t()));return(0,n.useEffect)((()=>{const n=()=>i(t());return n(),E(e,n)}),r),o}function P(e){const t=window;return!!t.$e?.routes?.isPartOf(e)}function O(){const e=window;return e.elementor?.channels?.dataEditMode?.request?.("activeMode")}function j(){return h(f(),(()=>"preview"===O()))}function _(e){return h([c(e),a(e)],(()=>P(e)),[e])}function A(e,{blockOnKitRoutes:t=!0,blockOnPreviewMode:n=!0}={}){const r=_(e),o=_("panel/global"),i=j();return{isActive:r&&!(n&&i),isBlocked:n&&i||t&&o}}(window.__UNSTABLE__elementorPackages=window.__UNSTABLE__elementorPackages||{}).editorV1Adapters=t}();