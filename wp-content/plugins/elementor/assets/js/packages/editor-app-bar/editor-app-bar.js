/*! For license information please see editor-app-bar.js.LICENSE.txt */
!function(){"use strict";var e={react:function(e){e.exports=window.React},"@elementor/editor":function(e){e.exports=window.elementorV2.editor},"@elementor/editor-app-bar-ui":function(e){e.exports=window.elementorV2.editorAppBarUi},"@elementor/editor-documents":function(e){e.exports=window.elementorV2.editorDocuments},"@elementor/editor-v1-adapters":function(e){e.exports=window.elementorV2.editorV1Adapters},"@elementor/icons":function(e){e.exports=window.elementorV2.icons},"@elementor/ui":function(e){e.exports=window.elementorV2.ui},"@wordpress/i18n":function(e){e.exports=window.wp.i18n}},t={};function n(o){var i=t[o];if(void 0!==i)return i.exports;var r=t[o]={exports:{}};return e[o](r,r.exports,n),r.exports}n.d=function(e,t){for(var o in t)n.o(t,o)&&!n.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var o={};!function(){n.r(o),n.d(o,{documentOptionsMenu:function(){return u},injectIntoPageIndication:function(){return e.__privateInjectIntoPageIndication},injectIntoPrimaryAction:function(){return e.__privateInjectIntoPrimaryAction},injectIntoResponsive:function(){return e.__privateInjectIntoResponsive},integrationsMenu:function(){return e.__privateIntegrationsMenu},mainMenu:function(){return e.__privateMainMenu},toolsMenu:function(){return e.__privateToolsMenu},utilitiesMenu:function(){return e.__privateUtilitiesMenu}});var e=n("@elementor/editor-app-bar-ui"),t=n("@elementor/editor"),i=n("@elementor/editor-v1-adapters"),r=n("react"),s=n("@elementor/ui"),a=n("@wordpress/i18n"),c=n("@elementor/icons"),l=n("@elementor/editor-documents"),u=(0,e.__privateCreateMenu)(["save"]);function p(){const e=(0,l.__useActiveDocument)(),t=(0,l.__useHostDocument)(),n=e&&"kit"!==e.type.value?e:t,{isActive:o,isBlocked:u}=(0,i.__privateUseRouteStatus)("panel/page-settings");if(!n)return null;const p=(0,a.__)("%s Settings","elementor").replace("%s",n.type.label);return r.createElement(d,{title:p},r.createElement(s.Box,{component:"span","aria-label":void 0},r.createElement(s.ToggleButton,{value:"document-settings",selected:o,disabled:u,onChange:()=>{const e=window,t=e?.elementor?.editorEvents?.config;t&&e.elementor.editorEvents.dispatchEvent(t.names.topBar.documentSettings,{location:t.locations.topBar,secondaryLocation:t.secondaryLocations["document-settings"],trigger:t.triggers.click,element:t.elements.buttonIcon}),(0,i.__privateOpenRoute)("panel/page-settings/settings")},"aria-label":p,size:"small",sx:{border:0,"&.Mui-disabled":{border:0}}},r.createElement(c.SettingsIcon,{fontSize:"small"}))))}function d(e){return r.createElement(s.Tooltip,{PopperProps:{sx:{"&.MuiTooltip-popper .MuiTooltip-tooltip.MuiTooltip-tooltipPlacementBottom":{mt:1.7}}},...e})}function m(){const e=(0,l.__useActiveDocument)();return{icon:c.EyeIcon,title:(0,a.__)("Preview Changes","elementor"),onClick:()=>{const t=window,n=t?.elementor?.editorEvents?.config;n&&t.elementor.editorEvents.dispatchEvent(n.names.topBar.previewPage,{location:n.locations.topBar,secondaryLocation:n.secondaryLocations["preview-page"],trigger:n.triggers.click,element:n.elements.buttonIcon}),e&&(0,i.__privateRunCommand)("editor/documents/preview",{id:e.id,force:!0})}}}function g(){const e=(0,l.__useActiveDocument)(),{saveDraft:t}=(0,l.__useActiveDocumentActions)();return{icon:c.FileReportIcon,title:(0,a.__)("Save Draft","elementor"),onClick:t,disabled:!e||e.isSaving||e.isSavingDraft||!e.isDirty}}function v(){const{saveTemplate:e}=(0,l.__useActiveDocumentActions)();return{icon:c.FolderIcon,title:(0,a.__)("Save as Template","elementor"),onClick:e}}function _(){const e=(0,l.__useActiveDocument)();return{icon:c.EyeIcon,title:(0,a.__)("View Page","elementor"),onClick:()=>e?.id&&(0,i.__privateRunCommand)("editor/documents/view",{id:e.id})}}var{useMenuItems:f}=u,y=(0,s.styled)(e.__privatePopoverMenu)`
	& > .MuiPopover-paper > .MuiList-root {
		& > .MuiDivider-root {
			display: none;
		}

		& > *:not(.MuiDivider-root):not(:last-of-type) + .MuiDivider-root {
			display: block;
		}
	}
`;function E(e){const{save:t,default:n}=f();return r.createElement(y,{...e,anchorOrigin:{vertical:"bottom",horizontal:"right"},transformOrigin:{vertical:"top",horizontal:"right"},marginThreshold:4,PaperProps:{sx:{mt:.5}}},t.map((({MenuItem:e,id:t},n)=>[n>0&&r.createElement(s.Divider,{key:`${t}-divider`}),r.createElement(e,{key:t})])),t.length>0&&n.length>0&&r.createElement(s.Divider,null),n.map((({MenuItem:e,id:t},n)=>[n>0&&r.createElement(s.Divider,{key:`${t}-divider`}),r.createElement(e,{key:t})])))}function b(){const e=(0,l.__useActiveDocument)(),{save:t}=(0,l.__useActiveDocumentActions)(),n=(0,i.__privateUseIsPreviewMode)(),o=(0,s.usePopupState)({variant:"popover",popupId:"document-save-options"});if(!e)return null;const u=n||!function(e){return"kit"!==e.type.value&&(e.isDirty||"draft"===e.status.value)}(e),p=n||"kit"===e.type.value,d=e.isSaving&&!u;return r.createElement(r.Fragment,null,r.createElement(s.ButtonGroup,{size:"large",variant:"contained"},r.createElement(s.Button,{onClick:()=>{const n=window,o=n?.elementor?.editorEvents?.config;o&&n.elementor.editorEvents.dispatchEvent(o.names.topBar.publishButton,{location:o.locations.topBar,secondaryLocation:o.secondaryLocations["publish-button"],trigger:o.triggers.click,element:o.elements.mainCta}),e.isSaving||t()},sx:{height:"100%",borderRadius:0,maxWidth:"158px","&.MuiButtonBase-root.MuiButtonGroup-grouped":{minWidth:"110px"}},disabled:u},d?r.createElement(s.CircularProgress,{color:"inherit",size:"1.5em"}):function(e){return e.userCan.publish?(0,a.__)("Publish","elementor"):(0,a.__)("Submit","elementor")}(e)),r.createElement(s.Tooltip,{title:(0,a.__)("Save Options","elementor"),PopperProps:{sx:{"&.MuiTooltip-popper .MuiTooltip-tooltip.MuiTooltip-tooltipPlacementBottom":{mt:1,mr:.25}}}},r.createElement(s.Box,{component:"span","aria-label":void 0},r.createElement(s.Button,{size:"small",...(0,s.bindTrigger)(o),sx:{px:0,height:"100%",borderRadius:0},disabled:p,"aria-label":(0,a.__)("Save Options","elementor")},r.createElement(c.ChevronDownIcon,null))))),r.createElement(E,{...(0,s.bindMenu)(o),onClick:o.close}))}function w(){const e=(0,l.__useActiveDocument)(),{copyAndShare:t}=(0,l.__useActiveDocumentActions)();return{icon:c.LinkIcon,title:(0,a.__)("Copy and Share","elementor"),onClick:t,disabled:!e||e.isSaving||e.isSavingDraft||!("publish"===e.status.value),visible:e?.permissions?.showCopyAndShare}}function h(e){window.elementor?.getPanelView?.()?.getHeaderView?.()?.setTitle?.(e)}function k(e){const t=document.querySelector('.elementor-component-tab[data-tab="categories"]');t&&(t.textContent=e)}function B(){const{isActive:e,isBlocked:t}=(0,i.__privateUseRouteStatus)("panel/elements");return{title:(0,a.__)("Add Element","elementor"),icon:c.PlusIcon,onClick:()=>{const e=window,t=e?.elementor?.editorEvents?.config;t&&e.elementor.editorEvents.dispatchEvent(t.names.topBar.widgetPanel,{location:t.locations.topBar,secondaryLocation:t.secondaryLocations["widget-panel"],trigger:t.triggers.toggleClick,element:t.elements.buttonIcon}),(0,i.__privateOpenRoute)("panel/elements/categories")},selected:e,disabled:t}}function P(){const{isBlocked:e}=(0,i.__privateUseRouteStatus)("finder",{blockOnKitRoutes:!1,blockOnPreviewMode:!1});return{title:(0,a.__)("Finder","elementor"),icon:c.SearchIcon,onClick:()=>{const e=window,t=e?.elementor?.editorEvents?.config;t&&e.elementor.editorEvents.dispatchEvent(t.names.topBar.finder,{location:t.locations.topBar,secondaryLocation:t.secondaryLocations.finder,trigger:t.triggers.toggleClick,element:t.elements.buttonIcon}),(0,i.__privateRunCommand)("finder/toggle")},disabled:e}}function A(){const{isActive:e,isBlocked:t}=(0,i.__privateUseRouteStatus)("panel/history");return{title:(0,a.__)("History","elementor"),icon:c.HistoryIcon,onClick:()=>{const e=window,t=e?.elementor?.editorEvents?.config;t&&e.elementor.editorEvents.dispatchEvent(t.names.topBar.history,{location:t.locations.topBar,secondaryLocation:t.secondaryLocations.elementorLogo,trigger:t.triggers.click,element:t.elements.link}),(0,i.__privateOpenRoute)("panel/history/actions")},selected:e,disabled:t}}function I(){return{icon:c.KeyboardIcon,title:(0,a.__)("Keyboard Shortcuts","elementor"),onClick:()=>{const e=window,t=e?.elementor?.editorEvents?.config;t&&e.elementor.editorEvents.dispatchEvent(t.names.topBar.keyboardShortcuts,{location:t.locations.topBar,secondaryLocation:t.secondaryLocations.elementorLogo,trigger:t.triggers.click,element:t.elements.link}),(0,i.__privateRunCommand)("shortcuts/open")}}}function M(e){const t=(0,i.__privateUseListenTo)([(0,i.routeOpenEvent)("panel/global"),(0,i.routeCloseEvent)("panel/global")],S);return t.current?r.createElement(s.Portal,{container:t.current,...e}):null}function S(){return(0,i.__privateIsRouteActive)("panel/global")?{current:document.querySelector("#elementor-panel-inner")}:{current:null}}function C(){const e=(0,l.__useActiveDocument)(),{save:t}=(0,l.__useActiveDocumentActions)();return r.createElement(s.Paper,{sx:{px:5,py:4,borderTop:1,borderColor:"divider"}},r.createElement(s.Button,{variant:"contained",disabled:!e||!e.isDirty,size:"medium",sx:{width:"100%"},onClick:()=>e&&!e.isSaving?t():null},e?.isSaving?r.createElement(s.CircularProgress,null):(0,a.__)("Save Changes","elementor")))}function L(){return r.createElement(M,null,r.createElement(C,null))}function T(){const{isActive:e,isBlocked:t}=(0,i.__privateUseRouteStatus)("panel/global",{blockOnKitRoutes:!1});return{title:(0,a.__)("Site Settings","elementor"),icon:c.AdjustmentsHorizontalIcon,onClick:()=>{const t=window,n=t?.elementor?.editorEvents?.config;n&&t.elementor.editorEvents.dispatchEvent(n.names.topBar.siteSettings,{location:n.locations.topBar,secondaryLocation:n.secondaryLocations.siteSettings,trigger:n.triggers.toggleClick,element:n.elements.buttonIcon}),e?(0,i.__privateRunCommand)("panel/global/close"):(0,i.__privateRunCommand)("panel/global/open")},selected:e,disabled:t}}function x(){const{isActive:e,isBlocked:t}=(0,i.__privateUseRouteStatus)("navigator");return{title:(0,a.__)("Structure","elementor"),icon:c.StructureIcon,onClick:()=>{const e=window,t=e?.elementor?.editorEvents?.config;t&&e.elementor.editorEvents.dispatchEvent(t.names.topBar.structure,{location:t.locations.topBar,secondaryLocation:t.secondaryLocations.structure,trigger:t.triggers.toggleClick,element:t.elements.buttonIcon}),(0,i.__privateRunCommand)("navigator/toggle")},selected:e,disabled:t}}function R(){return{icon:c.ThemeBuilderIcon,title:(0,a.__)("Theme Builder","elementor"),onClick:()=>{const e=window,t=e?.elementor?.editorEvents?.config;t&&e.elementor.editorEvents.dispatchEvent(t.names.topBar.themeBuilder,{location:t.locations.topBar,secondaryLocation:t.secondaryLocations.elementorLogo,trigger:t.triggers.click,element:t.elements.link}),(0,i.__privateRunCommand)("app/open")}}}function D(){const{isActive:e,isBlocked:t}=(0,i.__privateUseRouteStatus)("panel/editor-preferences");return{icon:c.ToggleRightIcon,title:(0,a.__)("User Preferences","elementor"),onClick:()=>{const e=window,t=e?.elementor?.editorEvents?.config;t&&e.elementor.editorEvents.dispatchEvent(t.names.topBar.userPreferences,{location:t.locations.topBar,secondaryLocation:t.secondaryLocations.elementorLogo,trigger:t.triggers.click,element:t.elements.link}),(0,i.__privateOpenRoute)("panel/editor-preferences")},selected:e,disabled:t}}(0,i.__privateListenTo)((0,i.routeOpenEvent)("panel/menu"),(()=>{(0,i.__privateOpenRoute)("panel/elements/categories")})),(0,e.__privateInjectIntoPageIndication)({id:"document-settings-button",component:p,options:{priority:20}}),e.__privateUtilitiesMenu.registerAction({id:"document-preview-button",priority:30,useProps:m}),(0,e.__privateInjectIntoPrimaryAction)({id:"document-primary-action",component:b}),u.registerAction({group:"save",id:"document-save-draft",priority:10,useProps:g}),u.registerAction({group:"save",id:"document-save-as-template",priority:20,useProps:v}),u.registerAction({id:"document-copy-and-share",priority:10,useProps:w}),u.registerAction({id:"document-view-page",priority:50,useProps:_}),function(){const e=(0,a.__)("Elements","elementor"),t=(0,a.__)("Widgets","elementor");(0,i.__privateListenTo)((0,i.routeOpenEvent)("panel/elements"),(()=>{h(e),k(t)})),(0,i.__privateListenTo)((0,i.v1ReadyEvent)(),(()=>{(0,i.__privateIsRouteActive)("panel/elements")&&(h(e),k(t))}))}(),e.__privateToolsMenu.registerToggleAction({id:"open-elements-panel",priority:1,useProps:B}),e.__privateUtilitiesMenu.registerAction({id:"toggle-finder",priority:10,useProps:P}),e.__privateUtilitiesMenu.registerLink({id:"open-help-center",priority:20,useProps:()=>({title:(0,a.__)("Help","elementor"),href:"https://go.elementor.com/editor-top-bar-learn/",icon:c.HelpIcon,target:"_blank",onClick:()=>{const e=window,t=e?.elementor?.editorEvents?.config;t&&e.elementor.editorEvents.dispatchEvent(t.names.topBar.help,{location:t.locations.topBar,secondaryLocation:t.secondaryLocations.help,trigger:t.triggers.click,element:t.elements.buttonIcon})}})}),e.__privateMainMenu.registerToggleAction({id:"open-history",priority:20,useProps:A}),e.__privateMainMenu.registerAction({id:"open-keyboard-shortcuts",group:"default",priority:40,useProps:I}),(0,t.injectIntoTop)({id:"site-settings-primary-action-portal",component:L}),e.__privateToolsMenu.registerToggleAction({id:"toggle-site-settings",priority:2,useProps:T}),e.__privateToolsMenu.registerToggleAction({id:"toggle-structure-view",priority:3,useProps:x}),e.__privateMainMenu.registerAction({id:"open-theme-builder",useProps:R}),e.__privateMainMenu.registerToggleAction({id:"open-user-preferences",priority:30,useProps:D}),e.__privateMainMenu.registerLink({id:"exit-to-wordpress",group:"exits",useProps:()=>{const e=(0,l.__useActiveDocument)();return{title:(0,a.__)("Exit to WordPress","elementor"),href:e?.links?.platformEdit,icon:c.WordpressIcon,onClick:()=>{const e=window,t=e?.elementor?.editorEvents?.config;t&&e.elementor.editorEvents.dispatchEvent(t.names.topBar.exitToWordpress,{location:t.locations.topBar,secondaryLocation:t.secondaryLocations.elementorLogo,trigger:t.triggers.click,element:t.elements.link})}}}}),(0,t.injectIntoTop)({id:"app-bar",component:e.__privateAppBar})}(),(window.elementorV2=window.elementorV2||{}).editorAppBar=o}();