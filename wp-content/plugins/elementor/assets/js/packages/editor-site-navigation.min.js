!function(){"use strict";var e={d:function(t,n){for(var o in n)e.o(n,o)&&!e.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:n[o]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{extendIconsMap:function(){return u}});var n=window.__UNSTABLE__elementorPackages.icons,o=window.React,a=window.__UNSTABLE__elementorPackages.ui,r=window.__UNSTABLE__elementorPackages.editorDocuments,i=window.wp.apiFetch,c=window.wp.url,l=window.wp.i18n,s=window.__UNSTABLE__elementorPackages.editorAppBar,p={page:n.PageTemplateIcon,section:n.SectionTemplateIcon,container:n.ContainerTemplateIcon,"wp-page":n.PageTypeIcon,"wp-post":n.PostTypeIcon};function u(e){Object.assign(p,e)}function m({title:e,status:t}){return o.createElement(d,{title:e},o.createElement(a.Stack,{direction:"row",alignItems:"center",spacing:2},o.createElement(a.Typography,{variant:"body2",sx:{maxWidth:"120px"},noWrap:!0},e),"publish"!==t.value&&o.createElement(a.Typography,{variant:"body2",sx:{fontStyle:"italic"}},"(",t.label,")")))}function d(e){return o.createElement(a.Tooltip,{PopperProps:{sx:{"&.MuiTooltip-popper .MuiTooltip-tooltip.MuiTooltip-tooltipPlacementBottom":{mt:7}}},...e})}var y="/elementor/v1/site-navigation/recent-posts";var g=p;function _({postType:e,docType:t,label:r}){const i="elementor_library"===e?"global":"primary",c=g[t]||n.PostTypeIcon;return o.createElement(a.Chip,{size:"medium",variant:"standard",label:r,color:i,icon:o.createElement(c,null),sx:{ml:3}})}function v(e=""){return(0,o.useMemo)((()=>{const t=document.createElement("textarea");t.innerHTML=e;const{value:n}=t;return t.remove(),n}),[e])}function w({post:e,closePopup:t}){const n=(0,r.useNavigateToDocument)(),i=v(e.title);return o.createElement(a.MenuItem,{dense:!0,sx:{width:"100%"},onClick:()=>{t(),n(e.id)}},i,o.createElement(_,{postType:e.type.post_type,docType:e.type.doc_type,label:e.type.label}))}var f="/elementor/v1/site-navigation/add-new-post";function E({closePopup:e}){const{create:t,isLoading:c}=function(){const[e,t]=(0,o.useState)(!1);return{create:()=>(t(!0),async function(){return await i({path:f,method:"POST",data:{post_type:"page"}})}().then((e=>e)).finally((()=>t(!1)))),isLoading:e}}(),s=(0,r.useNavigateToDocument)();return o.createElement(a.MenuItem,{dense:!0,size:"small",color:"inherit",component:"div",onClick:async()=>{const{id:n}=await t();e(),s(n)}},o.createElement(a.ListItemIcon,null,c?o.createElement(a.CircularProgress,null):o.createElement(n.PlusIcon,null)),(0,l.__)("Add new page","elementor"))}(0,s.injectIntoPageIndication)({id:"document-recently-edited",filler:function(){const e=(0,r.useActiveDocument)(),t=(0,r.useHostDocument)(),s=e&&"kit"!==e.type.value?e:t,{recentPosts:p}=function(e){const[t,n]=(0,o.useState)([]),[a,r]=(0,o.useState)(!1);return(0,o.useEffect)((()=>{e&&(r(!0),async function(e){const t={posts_per_page:5,post__not_in:e};return await i({path:(0,c.addQueryArgs)(y,t)}).then((e=>e)).catch((()=>[]))}(e).then((e=>{n(e),r(!1)})))}),[e]),{isLoading:a,recentPosts:t}}(s?.id),u=(0,a.usePopupState)({variant:"popover",popupId:"elementor-v2-top-bar-recently-edited"}),d=v(s?.title);return s?o.createElement(a.Box,{sx:{cursor:"default"}},o.createElement(a.Button,{color:"inherit",size:"small",endIcon:o.createElement(n.ChevronDownIcon,{fontSize:"small"}),...(0,a.bindTrigger)(u)},o.createElement(m,{title:d,status:s.status})),o.createElement(a.Menu,{MenuListProps:{component:"div"},PaperProps:{sx:{mt:4,minWidth:314}},...(0,a.bindMenu)(u)},o.createElement(a.ListSubheader,{sx:{fontSize:12,fontStyle:"italic",pl:4},component:"div",id:"nested-list-subheader"},(0,l.__)("Recent","elementor")),p.map((e=>o.createElement(w,{key:e.id,post:e,closePopup:u.close}))),0===p.length&&o.createElement(a.Typography,{variant:"caption",sx:{color:"grey.500",fontStyle:"italic",p:4},component:"div","aria-label":void 0},(0,l.__)("There are no other pages or templates on this site yet.","elementor")),o.createElement(a.Divider,null),o.createElement(E,{closePopup:u.close}))):null}}),(window.__UNSTABLE__elementorPackages=window.__UNSTABLE__elementorPackages||{}).editorSiteNavigation=t}();