/*! For license information please see editor-responsive.js.LICENSE.txt */
!function(){"use strict";var e={react:function(e){e.exports=window.React},"@elementor/editor-app-bar":function(e){e.exports=window.elementorV2.editorAppBar},"@elementor/editor-v1-adapters":function(e){e.exports=window.elementorV2.editorV1Adapters},"@elementor/icons":function(e){e.exports=window.elementorV2.icons},"@elementor/store":function(e){e.exports=window.elementorV2.store},"@elementor/ui":function(e){e.exports=window.elementorV2.ui},"@wordpress/i18n":function(e){e.exports=window.wp.i18n}},t={};function o(n){var i=t[n];if(void 0!==i)return i.exports;var r=t[n]={exports:{}};return e[n](r,r.exports,o),r.exports}o.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};!function(){o.r(n);var e=o("@elementor/store"),t=o("@elementor/editor-v1-adapters"),i=o("@wordpress/i18n"),r=o("@elementor/editor-app-bar"),a=o("react"),l=o("@elementor/ui"),c=o("@elementor/icons"),s=(0,e.__createSlice)({name:"breakpoints",initialState:{entities:{},activeId:null},reducers:{init(e,t){e.activeId=t.payload.activeId,e.entities=t.payload.entities.reduce(((e,t)=>({...e,[t.id]:t})),{})},activateBreakpoint(e,t){e.entities[t.payload]&&(e.activeId=t.payload)}}});function d(){const{breakpoints:e}=window.elementor?.config?.responsive||{};if(!e)return[];const t=Object.entries(e).filter((([,e])=>e.is_enabled)).map((([e,{value:t,direction:o,label:n}])=>({id:e,label:n,width:t,type:"min"===o?"min-width":"max-width"})));return t.push({id:"desktop",label:(0,i.__)("Desktop","elementor")}),t}function p(){const e=window;return e.elementor?.channels?.deviceMode?.request?.("currentMode")||null}var u=e=>e.breakpoints.entities,m=(0,e.__createSelector)(u,(e=>e.breakpoints.activeId),((e,t)=>t&&e[t]?e[t]:null)),v=(0,e.__createSelector)(u,(e=>{const t=(e,t)=>e.width&&t.width?t.width-e.width:0,o=Object.values(e),n=o.filter((e=>!e.width)),i=o.filter((e=>"min-width"===e.type)),r=o.filter((e=>"max-width"===e.type));return[...i.sort(t),...n,...r.sort(t)]}));function w(e){return a.createElement(l.Tooltip,{PopperProps:{sx:{"&.MuiTooltip-popper .MuiTooltip-tooltip.MuiTooltip-tooltipPlacementBottom":{mt:2.5}}},...e})}var b={widescreen:c.WidescreenIcon,desktop:c.DesktopIcon,laptop:c.LaptopIcon,tablet_extra:c.TabletLandscapeIcon,tablet:c.TabletPortraitIcon,mobile_extra:c.MobileLandscapeIcon,mobile:c.MobilePortraitIcon},_={default:"%s","min-width":(0,i.__)("%s (%dpx and up)","elementor"),"max-width":(0,i.__)("%s (up to %dpx)","elementor")};(0,e.__registerSlice)(s),function(){const{init:o}=s.actions;(0,t.__privateListenTo)((0,t.v1ReadyEvent)(),(()=>{(0,e.__dispatch)(o({entities:d(),activeId:p()}))}))}(),function(){const{activateBreakpoint:o}=s.actions;(0,t.__privateListenTo)((0,t.windowEvent)("elementor/device-mode/change"),(()=>{const t=p();(0,e.__dispatch)(o(t))}))}(),(0,r.injectIntoResponsive)({id:"responsive-breakpoints-switcher",component:function(){const{all:o,active:n}={all:(0,e.__useSelector)(v),active:(0,e.__useSelector)(m)},{activate:r}={activate:(0,a.useCallback)((e=>(0,t.__privateRunCommand)("panel/change-device-mode",{device:e})),[])};return o.length&&n?a.createElement(l.Tabs,{textColor:"inherit",indicatorColor:"secondary",value:n.id,onChange:(e,t)=>{const o=window,n=o?.elementor?.editorEvents?.config;n&&o.elementor.editorEvents.dispatchEvent(n.names.topBar.responsiveControls,{location:n.locations.topBar,secondaryLocation:n.secondaryLocations.responsiveControls,trigger:n.triggers.click,element:n.elements.buttonIcon,mode:t}),r(t)},"aria-label":(0,i.__)("Switch Device","elementor"),sx:{"& .MuiTabs-indicator":{backgroundColor:"text.primary"}}},o.map((({id:e,label:t,type:o,width:n})=>{const i=b[e],r=_[o||"default"].replace("%s",t).replace("%d",n?.toString()||"");return a.createElement(l.Tab,{value:e,key:e,"aria-label":r,icon:a.createElement(w,{title:r},a.createElement(i,null)),sx:{minWidth:"auto"}})}))):null},options:{priority:20}})}(),(window.elementorV2=window.elementorV2||{}).editorResponsive=n}();