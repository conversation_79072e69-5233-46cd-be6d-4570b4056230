!function(){"use strict";var e={};(function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})})(e);var t=window.elementorV2.store,i=window.elementorV2.editorV1Adapters,o=window.wp.i18n,n=window.elementorV2.editorAppBar,a=window.React,r=window.elementorV2.ui,l=window.elementorV2.icons,c=(0,t.__createSlice)({name:"breakpoints",initialState:{entities:{},activeId:null},reducers:{init(e,t){e.activeId=t.payload.activeId,e.entities=t.payload.entities.reduce(((e,t)=>({...e,[t.id]:t})),{})},activateBreakpoint(e,t){e.entities[t.payload]&&(e.activeId=t.payload)}}});function d(){const{breakpoints:e}=window.elementor?.config?.responsive||{};if(!e)return[];const t=Object.entries(e).filter((([,e])=>e.is_enabled)).map((([e,{value:t,direction:i,label:o}])=>({id:e,label:o,width:t,type:"min"===i?"min-width":"max-width"})));return t.push({id:"desktop",label:(0,o.__)("Desktop","elementor")}),t}function s(){const e=window;return e.elementor?.channels?.deviceMode?.request?.("currentMode")||null}var p=e=>e.breakpoints.entities,u=(0,t.__createSelector)(p,(e=>e.breakpoints.activeId),((e,t)=>t&&e[t]?e[t]:null)),m=(0,t.__createSelector)(p,(e=>{const t=(e,t)=>e.width&&t.width?t.width-e.width:0,i=Object.values(e),o=i.filter((e=>!e.width)),n=i.filter((e=>"min-width"===e.type)),a=i.filter((e=>"max-width"===e.type));return[...n.sort(t),...o,...a.sort(t)]}));function w(e){return a.createElement(r.Tooltip,{PopperProps:{sx:{"&.MuiTooltip-popper .MuiTooltip-tooltip.MuiTooltip-tooltipPlacementBottom":{mt:2.5}}},...e})}var v={widescreen:l.WidescreenIcon,desktop:l.DesktopIcon,laptop:l.LaptopIcon,tablet_extra:l.TabletLandscapeIcon,tablet:l.TabletPortraitIcon,mobile_extra:l.MobileLandscapeIcon,mobile:l.MobilePortraitIcon},_={default:"%s","min-width":(0,o.__)("%s (%dpx and up)","elementor"),"max-width":(0,o.__)("%s (up to %dpx)","elementor")};(0,t.__registerSlice)(c),function(){const{init:e}=c.actions;(0,i.__privateListenTo)((0,i.v1ReadyEvent)(),(()=>{(0,t.__dispatch)(e({entities:d(),activeId:s()}))}))}(),function(){const{activateBreakpoint:e}=c.actions;(0,i.__privateListenTo)((0,i.windowEvent)("elementor/device-mode/change"),(()=>{const i=s();(0,t.__dispatch)(e(i))}))}(),(0,n.injectIntoResponsive)({id:"responsive-breakpoints-switcher",component:function(){const{all:e,active:n}={all:(0,t.__useSelector)(m),active:(0,t.__useSelector)(u)},{activate:l}={activate:(0,a.useCallback)((e=>(0,i.__privateRunCommand)("panel/change-device-mode",{device:e})),[])};return e.length&&n?a.createElement(r.Tabs,{textColor:"inherit",indicatorColor:"secondary",value:n.id,onChange:(e,t)=>{const i=window,o=i?.elementor?.editorEvents?.config;o&&i.elementor.editorEvents.dispatchEvent(o.names.topBar.responsiveControls,{location:o.locations.topBar,secondaryLocation:o.secondaryLocations.responsiveControls,trigger:o.triggers.click,element:o.elements.buttonIcon,mode:t}),l(t)},"aria-label":(0,o.__)("Switch Device","elementor"),sx:{"& .MuiTabs-indicator":{backgroundColor:"text.primary"}}},e.map((({id:e,label:t,type:i,width:o})=>{const n=v[e],l=_[i||"default"].replace("%s",t).replace("%d",o?.toString()||"");return a.createElement(r.Tab,{value:e,key:e,"aria-label":l,icon:a.createElement(w,{title:l},a.createElement(n,null)),sx:{minWidth:"auto"}})}))):null},options:{priority:20}}),(window.elementorV2=window.elementorV2||{}).editorResponsive=e}();