!function(){"use strict";var C={d:function(e,n){for(var t in n)C.o(n,t)&&!C.o(e,t)&&Object.defineProperty(e,t,{enumerable:!0,get:n[t]})},o:function(C,e){return Object.prototype.hasOwnProperty.call(C,e)},r:function(C){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(C,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(C,"__esModule",{value:!0})}},e={};C.r(e),C.d(e,{AIIcon:function(){return r},AdjustmentsHorizontalIcon:function(){return l},ArchiveTemplateIcon:function(){return o},ArrowUpRightIcon:function(){return d},CheckedCircleIcon:function(){return c},ChevronDownIcon:function(){return L},ChevronRightIcon:function(){return a},ContainerTemplateIcon:function(){return f},CopyIcon:function(){return i},DesktopIcon:function(){return H},DotsHorizontalIcon:function(){return v},DotsVerticalIcon:function(){return u},EraseIcon:function(){return V},Error404TemplateIcon:function(){return M},ExpandIcon:function(){return Z},EyeIcon:function(){return R},FileReportIcon:function(){return p},FolderIcon:function(){return m},FooterTemplateIcon:function(){return E},HeaderTemplateIcon:function(){return w},HelpIcon:function(){return I},HistoryIcon:function(){return h},HomeIcon:function(){return g},KeyboardIcon:function(){return S},LandingPageTemplateIcon:function(){return x},LaptopIcon:function(){return B},LinkIcon:function(){return s},LoopItemTemplateIcon:function(){return T},MessageIcon:function(){return b},MobileLandscapeIcon:function(){return y},MobilePortraitIcon:function(){return P},PageTemplateIcon:function(){return j},PageTypeIcon:function(){return O},PagesIcon:function(){return k},PlugIcon:function(){return F},PlusIcon:function(){return z},PopupTemplateIcon:function(){return A},PostTypeIcon:function(){return D},RefreshIcon:function(){return W},SearchIcon:function(){return _},SearchResultsTemplateIcon:function(){return U},SectionTemplateIcon:function(){return K},SettingsIcon:function(){return X},ShrinkIcon:function(){return q},StructureIcon:function(){return G},TabletLandscapeIcon:function(){return J},TabletPortraitIcon:function(){return N},ThemeBuilderIcon:function(){return Q},ToggleRightIcon:function(){return Y},TrashIcon:function(){return $},UpgradeIcon:function(){return CC},WidescreenIcon:function(){return eC},WordpressIcon:function(){return nC},XIcon:function(){return tC}});var n=window.React,t=window.elementorV2.ui,l=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M14 4.75C13.3096 4.75 12.75 5.30964 12.75 6C12.75 6.69036 13.3096 7.25 14 7.25C14.6904 7.25 15.25 6.69036 15.25 6C15.25 5.30964 14.6904 4.75 14 4.75ZM11.3535 5.25C11.68 4.09575 12.7412 3.25 14 3.25C15.2588 3.25 16.32 4.09575 16.6465 5.25H20C20.4142 5.25 20.75 5.58579 20.75 6C20.75 6.41421 20.4142 6.75 20 6.75H16.6465C16.32 7.90425 15.2588 8.75 14 8.75C12.7412 8.75 11.68 7.90425 11.3535 6.75H4C3.58579 6.75 3.25 6.41421 3.25 6C3.25 5.58579 3.58579 5.25 4 5.25H11.3535ZM8 10.75C7.30964 10.75 6.75 11.3096 6.75 12C6.75 12.6904 7.30964 13.25 8 13.25C8.69036 13.25 9.25 12.6904 9.25 12C9.25 11.3096 8.69036 10.75 8 10.75ZM5.35352 11.25C5.67998 10.0957 6.74122 9.25 8 9.25C9.25878 9.25 10.32 10.0957 10.6465 11.25H20C20.4142 11.25 20.75 11.5858 20.75 12C20.75 12.4142 20.4142 12.75 20 12.75H10.6465C10.32 13.9043 9.25878 14.75 8 14.75C6.74122 14.75 5.67998 13.9043 5.35352 12.75H4C3.58579 12.75 3.25 12.4142 3.25 12C3.25 11.5858 3.58579 11.25 4 11.25H5.35352ZM17 16.75C16.3096 16.75 15.75 17.3096 15.75 18C15.75 18.6904 16.3096 19.25 17 19.25C17.6904 19.25 18.25 18.6904 18.25 18C18.25 17.3096 17.6904 16.75 17 16.75ZM14.3535 17.25C14.68 16.0957 15.7412 15.25 17 15.25C18.2588 15.25 19.32 16.0957 19.6465 17.25H20C20.4142 17.25 20.75 17.5858 20.75 18C20.75 18.4142 20.4142 18.75 20 18.75H19.6465C19.32 19.9043 18.2588 20.75 17 20.75C15.7412 20.75 14.68 19.9043 14.3535 18.75H4C3.58579 18.75 3.25 18.4142 3.25 18C3.25 17.5858 3.58579 17.25 4 17.25H14.3535Z"})))),o=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.25 4.5C3.25 4.08579 3.58579 3.75 4 3.75H10C10.4142 3.75 10.75 4.08579 10.75 4.5V12C10.75 12.4142 10.4142 12.75 10 12.75H4C3.58579 12.75 3.25 12.4142 3.25 12V4.5ZM4.75 5.25V11.25H9.25V5.25H4.75ZM13.25 4.5C13.25 4.08579 13.5858 3.75 14 3.75H20C20.4142 3.75 20.75 4.08579 20.75 4.5V12C20.75 12.4142 20.4142 12.75 20 12.75H14C13.5858 12.75 13.25 12.4142 13.25 12V4.5ZM14.75 5.25V11.25H19.25V5.25H14.75ZM3.25 16C3.25 15.5858 3.58579 15.25 4 15.25H10C10.4142 15.25 10.75 15.5858 10.75 16C10.75 16.4142 10.4142 16.75 10 16.75H4C3.58579 16.75 3.25 16.4142 3.25 16ZM13.25 16C13.25 15.5858 13.5858 15.25 14 15.25H20C20.4142 15.25 20.75 15.5858 20.75 16C20.75 16.4142 20.4142 16.75 20 16.75H14C13.5858 16.75 13.25 16.4142 13.25 16ZM3.25 20C3.25 19.5858 3.58579 19.25 4 19.25H10C10.4142 19.25 10.75 19.5858 10.75 20C10.75 20.4142 10.4142 20.75 10 20.75H4C3.58579 20.75 3.25 20.4142 3.25 20ZM13.25 20C13.25 19.5858 13.5858 19.25 14 19.25H20C20.4142 19.25 20.75 19.5858 20.75 20C20.75 20.4142 20.4142 20.75 20 20.75H14C13.5858 20.75 13.25 20.4142 13.25 20Z"})))),r=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M18.25 3.25C18.6642 3.25 19 3.58579 19 4C19 4.33152 19.1317 4.64946 19.3661 4.88388C19.6005 5.1183 19.9185 5.25 20.25 5.25C20.6642 5.25 21 5.58579 21 6C21 6.41421 20.6642 6.75 20.25 6.75C19.9185 6.75 19.6005 6.8817 19.3661 7.11612C19.1317 7.35054 19 7.66848 19 8C19 8.41421 18.6642 8.75 18.25 8.75C17.8358 8.75 17.5 8.41421 17.5 8C17.5 7.66848 17.3683 7.35054 17.1339 7.11612C16.8995 6.8817 16.5815 6.75 16.25 6.75C15.8358 6.75 15.5 6.41421 15.5 6C15.5 5.58579 15.8358 5.25 16.25 5.25C16.5815 5.25 16.8995 5.1183 17.1339 4.88388C17.3683 4.64946 17.5 4.33152 17.5 4C17.5 3.58579 17.8358 3.25 18.25 3.25ZM18.25 5.88746C18.2318 5.90673 18.2133 5.92576 18.1945 5.94454C18.1758 5.96333 18.1567 5.98182 18.1375 6C18.1567 6.01819 18.1758 6.03667 18.1945 6.05546C18.2133 6.07424 18.2318 6.09327 18.25 6.11254C18.2682 6.09327 18.2867 6.07424 18.3055 6.05546C18.3242 6.03667 18.3433 6.01819 18.3625 6C18.3433 5.98182 18.3242 5.96333 18.3055 5.94454C18.2867 5.92576 18.2682 5.90673 18.25 5.88746ZM9.25 5.25C9.66421 5.25 10 5.58579 10 6C10 7.39239 10.5531 8.72774 11.5377 9.71231C12.5223 10.6969 13.8576 11.25 15.25 11.25C15.6642 11.25 16 11.5858 16 12C16 12.4142 15.6642 12.75 15.25 12.75C13.8576 12.75 12.5223 13.3031 11.5377 14.2877C10.5531 15.2723 10 16.6076 10 18C10 18.4142 9.66421 18.75 9.25 18.75C8.83579 18.75 8.5 18.4142 8.5 18C8.5 16.6076 7.94688 15.2723 6.96231 14.2877C5.97774 13.3031 4.64239 12.75 3.25 12.75C2.83579 12.75 2.5 12.4142 2.5 12C2.5 11.5858 2.83579 11.25 3.25 11.25C4.64239 11.25 5.97774 10.6969 6.96231 9.71231C7.94688 8.72774 8.5 7.39239 8.5 6C8.5 5.58579 8.83579 5.25 9.25 5.25ZM9.25 9.09234C8.93321 9.70704 8.52103 10.2749 8.02297 10.773C7.52491 11.271 6.95704 11.6832 6.34234 12C6.95704 12.3168 7.52491 12.729 8.02297 13.227C8.52103 13.7251 8.93321 14.293 9.25 14.9077C9.56679 14.293 9.97897 13.7251 10.477 13.227C10.9751 12.729 11.543 12.3168 12.1577 12C11.543 11.6832 10.9751 11.271 10.477 10.773C9.97897 10.2749 9.56679 9.70704 9.25 9.09234ZM18.25 15.25C18.6642 15.25 19 15.5858 19 16C19 16.3315 19.1317 16.6495 19.3661 16.8839C19.6005 17.1183 19.9185 17.25 20.25 17.25C20.6642 17.25 21 17.5858 21 18C21 18.4142 20.6642 18.75 20.25 18.75C19.9185 18.75 19.6005 18.8817 19.3661 19.1161C19.1317 19.3505 19 19.6685 19 20C19 20.4142 18.6642 20.75 18.25 20.75C17.8358 20.75 17.5 20.4142 17.5 20C17.5 19.6685 17.3683 19.3505 17.1339 19.1161C16.8995 18.8817 16.5815 18.75 16.25 18.75C15.8358 18.75 15.5 18.4142 15.5 18C15.5 17.5858 15.8358 17.25 16.25 17.25C16.5815 17.25 16.8995 17.1183 17.1339 16.8839C17.3683 16.6495 17.5 16.3315 17.5 16C17.5 15.5858 17.8358 15.25 18.25 15.25ZM18.25 17.8875C18.2318 17.9067 18.2133 17.9258 18.1945 17.9445C18.1758 17.9633 18.1567 17.9818 18.1375 18C18.1567 18.0182 18.1758 18.0367 18.1945 18.0555C18.2133 18.0742 18.2318 18.0933 18.25 18.1125C18.2682 18.0933 18.2867 18.0742 18.3055 18.0555C18.3242 18.0367 18.3433 18.0182 18.3625 18C18.3433 17.9818 18.3242 17.9633 18.3055 17.9445C18.2867 17.9258 18.2682 17.9067 18.25 17.8875Z"})))),d=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e,sx:{stroke:"currentColor",...C.sx}},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7.25 7C7.25 6.58579 7.58579 6.25 8 6.25H17C17.4142 6.25 17.75 6.58579 17.75 7V16C17.75 16.4142 17.4142 16.75 17 16.75C16.5858 16.75 16.25 16.4142 16.25 16V8.81066L7.53033 17.5303C7.23744 17.8232 6.76256 17.8232 6.46967 17.5303C6.17678 17.2374 6.17678 16.7626 6.46967 16.4697L15.1893 7.75H8C7.58579 7.75 7.25 7.41421 7.25 7Z"})))),c=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M8.26884 2.99217C9.45176 2.50219 10.7196 2.25 12 2.25C13.2804 2.25 14.5482 2.50219 15.7312 2.99217C16.9141 3.48216 17.9889 4.20034 18.8943 5.10571C19.7997 6.01108 20.5178 7.08591 21.0078 8.26884C21.4978 9.45176 21.75 10.7196 21.75 12C21.75 13.2804 21.4978 14.5482 21.0078 15.7312C20.5178 16.9141 19.7997 17.9889 18.8943 18.8943C17.9889 19.7997 16.9141 20.5178 15.7312 21.0078C14.5482 21.4978 13.2804 21.75 12 21.75C10.7196 21.75 9.45176 21.4978 8.26884 21.0078C7.08591 20.5178 6.01108 19.7997 5.10571 18.8943C4.20034 17.9889 3.48216 16.9141 2.99217 15.7312C2.50219 14.5482 2.25 13.2804 2.25 12C2.25 10.7196 2.50219 9.45176 2.99217 8.26884C3.48216 7.08591 4.20034 6.01108 5.10571 5.10571C6.01108 4.20034 7.08591 3.48216 8.26884 2.99217ZM12 3.75C10.9166 3.75 9.8438 3.96339 8.84286 4.37799C7.84193 4.7926 6.93245 5.40029 6.16637 6.16637C5.40029 6.93245 4.79259 7.84193 4.37799 8.84286C3.96339 9.8438 3.75 10.9166 3.75 12C3.75 13.0834 3.96339 14.1562 4.37799 15.1571C4.79259 16.1581 5.40029 17.0675 6.16637 17.8336C6.93245 18.5997 7.84193 19.2074 8.84286 19.622C9.8438 20.0366 10.9166 20.25 12 20.25C13.0834 20.25 14.1562 20.0366 15.1571 19.622C16.1581 19.2074 17.0675 18.5997 17.8336 17.8336C18.5997 17.0675 19.2074 16.1581 19.622 15.1571C20.0366 14.1562 20.25 13.0834 20.25 12C20.25 10.9166 20.0366 9.8438 19.622 8.84286C19.2074 7.84193 18.5997 6.93245 17.8336 6.16637C17.0675 5.40029 16.1581 4.7926 15.1571 4.37799C14.1562 3.96339 13.0834 3.75 12 3.75Z"}),n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M16.2414 8.99563C16.5343 9.28852 16.5343 9.7634 16.2414 10.0563L11.2933 15.0044C11.0004 15.2973 10.5255 15.2973 10.2326 15.0044L7.75861 12.5303C7.46572 12.2374 7.46572 11.7626 7.75861 11.4697C8.0515 11.1768 8.52638 11.1768 8.81927 11.4697L10.763 13.4134L15.1807 8.99563C15.4736 8.70274 15.9485 8.70274 16.2414 8.99563Z"})))),L=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M5.46967 9.21967C5.76256 8.92678 6.23744 8.92678 6.53033 9.21967L12 14.6893L17.4697 9.21967C17.7626 8.92678 18.2374 8.92678 18.5303 9.21967C18.8232 9.51256 18.8232 9.98744 18.5303 10.2803L12.5303 16.2803C12.2374 16.5732 11.7626 16.5732 11.4697 16.2803L5.46967 10.2803C5.17678 9.98744 5.17678 9.51256 5.46967 9.21967Z"})))),a=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M9.46967 7.46967C9.17678 7.76256 9.17678 8.23744 9.46967 8.53033L12.9393 12L9.46967 15.4697C9.17678 15.7626 9.17678 16.2374 9.46967 16.5303C9.76256 16.8232 10.2374 16.8232 10.5303 16.5303L14.5303 12.5303C14.8232 12.2374 14.8232 11.7626 14.5303 11.4697L10.5303 7.46967C10.2374 7.17678 9.76256 7.17678 9.46967 7.46967Z"})))),f=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4.75 5.25C4.05964 5.25 3.5 5.80964 3.5 6.5V17.5C3.5 18.1904 4.05964 18.75 4.75 18.75H7.75C8.16421 18.75 8.5 19.0858 8.5 19.5C8.5 19.9142 8.16421 20.25 7.75 20.25H4.75C3.23122 20.25 2 19.0188 2 17.5V6.5C2 4.98122 3.23122 3.75 4.75 3.75H16.75C18.2688 3.75 19.5 4.98122 19.5 6.5V8C19.5 8.41421 19.1642 8.75 18.75 8.75C18.3358 8.75 18 8.41421 18 8V6.5C18 5.80964 17.4404 5.25 16.75 5.25H4.75ZM12.75 13.25C12.6676 13.25 12.5982 13.281 12.5546 13.3217C12.5128 13.3607 12.5 13.4021 12.5 13.4333V18.5667C12.5 18.5979 12.5128 18.6393 12.5546 18.6783C12.5982 18.719 12.6676 18.75 12.75 18.75H19.75C19.8324 18.75 19.9018 18.719 19.9454 18.6783C19.9872 18.6393 20 18.5979 20 18.5667V14.8333C20 14.8021 19.9872 14.7607 19.9454 14.7217C19.9018 14.681 19.8324 14.65 19.75 14.65H16.25C16.06 14.65 15.8771 14.5779 15.7383 14.4483L14.4544 13.25H12.75ZM11.5312 12.2251C11.8627 11.9156 12.3019 11.75 12.75 11.75H14.75C14.94 11.75 15.1229 11.8221 15.2617 11.9517L16.5456 13.15H19.75C20.1981 13.15 20.6373 13.3156 20.9688 13.6251C21.3021 13.9361 21.5 14.3695 21.5 14.8333V18.5667C21.5 19.0305 21.3021 19.4639 20.9688 19.7749C20.6373 20.0844 20.1981 20.25 19.75 20.25H12.75C12.3019 20.25 11.8627 20.0844 11.5312 19.7749C11.1979 19.4639 11 19.0305 11 18.5667V13.4333C11 12.9695 11.1979 12.5361 11.5312 12.2251Z"})))),i=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4.05546 4.05546C4.57118 3.53973 5.27065 3.25 6 3.25H14C14.7293 3.25 15.4288 3.53973 15.9445 4.05546C16.4603 4.57118 16.75 5.27065 16.75 6V7.25H18C19.5188 7.25 20.75 8.48122 20.75 10V18C20.75 19.5188 19.5188 20.75 18 20.75H10C8.48122 20.75 7.25 19.5188 7.25 18V16.75H6C5.27065 16.75 4.57118 16.4603 4.05546 15.9445C3.53973 15.4288 3.25 14.7293 3.25 14V6C3.25 5.27065 3.53973 4.57118 4.05546 4.05546ZM8.75 18C8.75 18.6904 9.30964 19.25 10 19.25H18C18.6904 19.25 19.25 18.6904 19.25 18V10C19.25 9.30964 18.6904 8.75 18 8.75H10C9.30964 8.75 8.75 9.30964 8.75 10V18ZM15.25 7.25H10C8.48122 7.25 7.25 8.48122 7.25 10V15.25H6C5.66848 15.25 5.35054 15.1183 5.11612 14.8839C4.8817 14.6495 4.75 14.3315 4.75 14V6C4.75 5.66848 4.8817 5.35054 5.11612 5.11612C5.35054 4.8817 5.66848 4.75 6 4.75H14C14.3315 4.75 14.6495 4.8817 14.8839 5.11612C15.1183 5.35054 15.25 5.66848 15.25 6V7.25Z"})))),H=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4.82091 5.29117C4.7847 5.3319 4.75 5.40356 4.75 5.5V15.5C4.75 15.5964 4.7847 15.6681 4.82091 15.7088C4.85589 15.7482 4.88124 15.75 4.88889 15.75H19.1111C19.1188 15.75 19.1441 15.7482 19.1791 15.7088C19.2153 15.6681 19.25 15.5964 19.25 15.5V5.5C19.25 5.40356 19.2153 5.3319 19.1791 5.29117C19.1441 5.25181 19.1188 5.25 19.1111 5.25H4.88889C4.88124 5.25 4.85589 5.25181 4.82091 5.29117ZM3.25 5.5C3.25 4.61899 3.90315 3.75 4.88889 3.75H19.1111C20.0968 3.75 20.75 4.61899 20.75 5.5V15.5C20.75 16.381 20.0968 17.25 19.1111 17.25H4.88889C3.90315 17.25 3.25 16.381 3.25 15.5V5.5ZM6.25 19.5C6.25 19.0858 6.58579 18.75 7 18.75H17C17.4142 18.75 17.75 19.0858 17.75 19.5C17.75 19.9142 17.4142 20.25 17 20.25H7C6.58579 20.25 6.25 19.9142 6.25 19.5Z"})))),u=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e},n.createElement("path",{d:"M11.9999 6.90002C11.1715 6.90002 10.4999 6.22845 10.4999 5.40002C10.4999 4.5716 11.1715 3.90002 11.9999 3.90002C12.8283 3.90002 13.4999 4.5716 13.4999 5.40002C13.4999 6.22845 12.8283 6.90002 11.9999 6.90002Z"}),n.createElement("path",{d:"M11.9999 13.5C11.1715 13.5 10.4999 12.8285 10.4999 12C10.4999 11.1716 11.1715 10.5 11.9999 10.5C12.8283 10.5 13.4999 11.1716 13.4999 12C13.4999 12.8285 12.8283 13.5 11.9999 13.5Z"}),n.createElement("path",{d:"M11.9999 20.1C11.1714 20.1 10.4999 19.4285 10.4999 18.6C10.4999 17.7716 11.1715 17.1 11.9999 17.1C12.8283 17.1 13.4999 17.7716 13.4999 18.6C13.4999 19.4285 12.8283 20.1 11.9999 20.1Z"})))),v=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e},n.createElement("path",{d:"M6.90002 11.75C6.90002 12.5784 6.22845 13.25 5.40002 13.25C4.5716 13.25 3.90002 12.5784 3.90002 11.75C3.90002 10.9216 4.5716 10.25 5.40002 10.25C6.22845 10.25 6.90002 10.9216 6.90002 11.75Z"}),n.createElement("path",{d:"M13.5 11.75C13.5 12.5784 12.8285 13.25 12 13.25C11.1716 13.25 10.5 12.5784 10.5 11.75C10.5 10.9216 11.1716 10.25 12 10.25C12.8285 10.25 13.5 10.9216 13.5 11.75Z"}),n.createElement("path",{d:"M20.1 11.75C20.1 12.5784 19.4285 13.25 18.6 13.25C17.7716 13.25 17.1 12.5784 17.1 11.75C17.1 10.9216 17.7716 10.25 18.6 10.25C19.4285 10.25 20.1 10.9216 20.1 11.75Z"})))),V=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M14.8188 4.82172C14.8187 4.82187 14.819 4.82157 14.8188 4.82172L12.7008 6.93978L18.0615 12.3006L20.1669 10.1724L20.1682 10.1711C20.2147 10.1242 20.2409 10.0609 20.2409 9.99482C20.2409 9.92902 20.2149 9.86589 20.1687 9.8191C20.1685 9.81892 20.1689 9.81928 20.1687 9.8191L15.1713 4.82172C15.1712 4.82157 15.1715 4.82188 15.1713 4.82172C15.1245 4.77545 15.0609 4.74902 14.9951 4.74902C14.9292 4.74902 14.8656 4.77544 14.8188 4.82172ZM17.0066 13.367L11.6401 8.00044L4.82197 14.8186C4.82181 14.8187 4.82212 14.8184 4.82197 14.8186C4.77569 14.8654 4.74927 14.929 4.74927 14.9948C4.74927 15.0609 4.7754 15.1242 4.82197 15.1711L4.82598 15.1751L8.81538 19.2498H11.187L17.0066 13.367ZM11.5009 20.7498H19.0001C19.4143 20.7498 19.7501 20.414 19.7501 19.9998C19.7501 19.5856 19.4143 19.2498 19.0001 19.2498H13.297L21.232 11.2286C21.2322 11.2283 21.2325 11.228 21.2328 11.2277C21.5582 10.8999 21.7409 10.4568 21.7409 9.99482C21.7409 9.53249 21.5579 9.08895 21.232 8.76106L16.2304 3.75948L16.2288 3.75791C15.9009 3.43197 15.4574 3.24902 14.9951 3.24902C14.5327 3.24902 14.0892 3.43197 13.7613 3.75791L13.7597 3.75949L3.75973 13.7595L3.75815 13.7611C3.43222 14.089 3.24927 14.5325 3.24927 14.9948C3.24927 15.4561 3.43139 15.8987 3.75594 16.2263C3.75668 16.2271 3.75742 16.2278 3.75815 16.2286L7.96415 20.5245C8.10523 20.6686 8.2984 20.7498 8.50006 20.7498H11.4991C11.4997 20.7498 11.5003 20.7498 11.5009 20.7498Z"})))),M=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7 3.75C6.66848 3.75 6.35054 3.8817 6.11612 4.11612C5.8817 4.35054 5.75 4.66848 5.75 5V19C5.75 19.3315 5.8817 19.6495 6.11612 19.8839C6.35054 20.1183 6.66848 20.25 7 20.25H17C17.3315 20.25 17.6495 20.1183 17.8839 19.8839C18.1183 19.6495 18.25 19.3315 18.25 19V8.75H15C14.5359 8.75 14.0908 8.56563 13.7626 8.23744C13.4344 7.90925 13.25 7.46413 13.25 7V3.75H7ZM14.75 4.81066L17.1893 7.25H15C14.9337 7.25 14.8701 7.22366 14.8232 7.17678C14.7763 7.12989 14.75 7.0663 14.75 7V4.81066ZM5.05546 3.05546C5.57118 2.53973 6.27065 2.25 7 2.25H14C14.1989 2.25 14.3897 2.32902 14.5303 2.46967L19.5303 7.46967C19.671 7.61032 19.75 7.80109 19.75 8V19C19.75 19.7293 19.4603 20.4288 18.9445 20.9445C18.4288 21.4603 17.7293 21.75 17 21.75H7C6.27065 21.75 5.57118 21.4603 5.05546 20.9445C4.53973 20.4288 4.25 19.7293 4.25 19V5C4.25 4.27065 4.53973 3.57118 5.05546 3.05546ZM12 10.25C12.4142 10.25 12.75 10.5858 12.75 11V14C12.75 14.4142 12.4142 14.75 12 14.75C11.5858 14.75 11.25 14.4142 11.25 14V11C11.25 10.5858 11.5858 10.25 12 10.25ZM11.25 17C11.25 16.5858 11.5858 16.25 12 16.25H12.01C12.4242 16.25 12.76 16.5858 12.76 17C12.76 17.4142 12.4242 17.75 12.01 17.75H12C11.5858 17.75 11.25 17.4142 11.25 17Z"})))),Z=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4.53033 8.46967C4.82322 8.76256 4.82322 9.23744 4.53033 9.53033L2.06066 12L4.53033 14.4697C4.82322 14.7626 4.82322 15.2374 4.53033 15.5303C4.23744 15.8232 3.76256 15.8232 3.46967 15.5303L0.46967 12.5303C0.176777 12.2374 0.176777 11.7626 0.46967 11.4697L3.46967 8.46967C3.76256 8.17678 4.23744 8.17678 4.53033 8.46967Z"}),n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M19.4697 8.46967C19.7626 8.17678 20.2374 8.17678 20.5303 8.46967L23.5303 11.4697C23.8232 11.7626 23.8232 12.2374 23.5303 12.5303L20.5303 15.5303C20.2374 15.8232 19.7626 15.8232 19.4697 15.5303C19.1768 15.2374 19.1768 14.7626 19.4697 14.4697L21.9393 12L19.4697 9.53033C19.1768 9.23744 19.1768 8.76256 19.4697 8.46967Z"}),n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M0.25 12C0.25 11.5858 0.585786 11.25 1 11.25H10C10.4142 11.25 10.75 11.5858 10.75 12C10.75 12.4142 10.4142 12.75 10 12.75H1C0.585786 12.75 0.25 12.4142 0.25 12Z"}),n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M13.25 12C13.25 11.5858 13.5858 11.25 14 11.25L23 11.25C23.4142 11.25 23.75 11.5858 23.75 12C23.75 12.4142 23.4142 12.75 23 12.75L14 12.75C13.5858 12.75 13.25 12.4142 13.25 12Z"})))),R=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2.86829 12C5.41108 16.2677 8.46131 18.25 12 18.25C15.5387 18.25 18.5889 16.2677 21.1317 12C18.5889 7.73232 15.5387 5.75 12 5.75C8.46131 5.75 5.41108 7.73232 2.86829 12ZM1.34883 11.6279C4.09715 6.81857 7.63999 4.25 12 4.25C16.36 4.25 19.9028 6.81857 22.6512 11.6279C22.7829 11.8585 22.7829 12.1415 22.6512 12.3721C19.9028 17.1814 16.36 19.75 12 19.75C7.63999 19.75 4.09715 17.1814 1.34883 12.3721C1.21706 12.1415 1.21706 11.8585 1.34883 11.6279ZM12 10.75C11.3096 10.75 10.75 11.3096 10.75 12C10.75 12.6904 11.3096 13.25 12 13.25C12.6904 13.25 13.25 12.6904 13.25 12C13.25 11.3096 12.6904 10.75 12 10.75ZM9.25 12C9.25 10.4812 10.4812 9.25 12 9.25C13.5188 9.25 14.75 10.4812 14.75 12C14.75 13.5188 13.5188 14.75 12 14.75C10.4812 14.75 9.25 13.5188 9.25 12Z"})))),p=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6 3.75C5.66848 3.75 5.35054 3.8817 5.11612 4.11612C4.8817 4.35054 4.75 4.66848 4.75 5V19C4.75 19.3315 4.8817 19.6495 5.11612 19.8839C5.35054 20.1183 5.66848 20.25 6 20.25H13C13.4142 20.25 13.75 20.5858 13.75 21C13.75 21.4142 13.4142 21.75 13 21.75H6C5.27065 21.75 4.57118 21.4603 4.05546 20.9445C3.53973 20.4288 3.25 19.7293 3.25 19V5C3.25 4.27065 3.53973 3.57118 4.05546 3.05546C4.57118 2.53973 5.27065 2.25 6 2.25H13C13.1989 2.25 13.3897 2.32902 13.5303 2.46967L18.5303 7.46967C18.671 7.61032 18.75 7.80109 18.75 8V11C18.75 11.4142 18.4142 11.75 18 11.75C17.5858 11.75 17.25 11.4142 17.25 11V8.75H14C13.5359 8.75 13.0908 8.56563 12.7626 8.23744C12.4344 7.90925 12.25 7.46413 12.25 7V3.75H6ZM13.75 4.81066L16.1893 7.25H14C13.9337 7.25 13.8701 7.22366 13.8232 7.17678C13.7763 7.12989 13.75 7.0663 13.75 7V4.81066ZM18.5 14.75C16.9812 14.75 15.75 15.9812 15.75 17.5C15.75 19.0188 16.9812 20.25 18.5 20.25C20.0188 20.25 21.25 19.0188 21.25 17.5C21.25 15.9812 20.0188 14.75 18.5 14.75ZM14.25 17.5C14.25 15.1528 16.1528 13.25 18.5 13.25C20.8472 13.25 22.75 15.1528 22.75 17.5C22.75 19.8472 20.8472 21.75 18.5 21.75C16.1528 21.75 14.25 19.8472 14.25 17.5ZM18.1111 15.5833C18.5253 15.5833 18.8611 15.9191 18.8611 16.3333V17.1389H19.6667C20.0809 17.1389 20.4167 17.4747 20.4167 17.8889C20.4167 18.3031 20.0809 18.6389 19.6667 18.6389H18.1111C17.6969 18.6389 17.3611 18.3031 17.3611 17.8889V16.3333C17.3611 15.9191 17.6969 15.5833 18.1111 15.5833Z"})))),m=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M5 4.75C4.66848 4.75 4.35054 4.8817 4.11612 5.11612C3.8817 5.35054 3.75 5.66848 3.75 6V17C3.75 17.3315 3.8817 17.6495 4.11612 17.8839C4.35054 18.1183 4.66848 18.25 5 18.25H19C19.3315 18.25 19.6495 18.1183 19.8839 17.8839C20.1183 17.6495 20.25 17.3315 20.25 17V9C20.25 8.66848 20.1183 8.35054 19.8839 8.11612C19.6495 7.8817 19.3315 7.75 19 7.75H12C11.8011 7.75 11.6103 7.67098 11.4697 7.53033L8.68934 4.75H5ZM3.05546 4.05546C3.57118 3.53973 4.27065 3.25 5 3.25H9C9.19891 3.25 9.38968 3.32902 9.53033 3.46967L12.3107 6.25H19C19.7293 6.25 20.4288 6.53973 20.9445 7.05546C21.4603 7.57118 21.75 8.27065 21.75 9V17C21.75 17.7293 21.4603 18.4288 20.9445 18.9445C20.4288 19.4603 19.7293 19.75 19 19.75H5C4.27065 19.75 3.57118 19.4603 3.05546 18.9445C2.53973 18.4288 2.25 17.7293 2.25 17V6C2.25 5.27065 2.53973 4.57118 3.05546 4.05546Z"})))),E=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M19 19.25C19.1381 19.25 19.25 19.1381 19.25 19L19.25 16.75L4.75 16.75L4.75 19C4.75 19.1381 4.86193 19.25 5 19.25L19 19.25ZM3.25 19C3.25 19.9665 4.0335 20.75 5 20.75L19 20.75C19.9665 20.75 20.75 19.9665 20.75 19L20.75 5C20.75 4.0335 19.9665 3.25 19 3.25L5 3.25C4.0335 3.25 3.25 4.0335 3.25 5L3.25 19ZM4.75 15.25L19.25 15.25L19.25 5C19.25 4.86193 19.1381 4.75 19 4.75L5 4.75C4.86193 4.75 4.75 4.86193 4.75 5L4.75 15.25Z"})))),w=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M5 4.75C4.86193 4.75 4.75 4.86193 4.75 5V7.25H19.25V5C19.25 4.86193 19.1381 4.75 19 4.75H5ZM20.75 5C20.75 4.0335 19.9665 3.25 19 3.25H5C4.0335 3.25 3.25 4.0335 3.25 5V19C3.25 19.9665 4.0335 20.75 5 20.75H19C19.9665 20.75 20.75 19.9665 20.75 19V5ZM19.25 8.75H4.75V19C4.75 19.1381 4.86193 19.25 5 19.25H19C19.1381 19.25 19.25 19.1381 19.25 19V8.75Z"})))),I=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 3.75C7.44365 3.75 3.75 7.44365 3.75 12C3.75 16.5563 7.44365 20.25 12 20.25C16.5563 20.25 20.25 16.5563 20.25 12C20.25 7.44365 16.5563 3.75 12 3.75ZM2.25 12C2.25 6.61522 6.61522 2.25 12 2.25C17.3848 2.25 21.75 6.61522 21.75 12C21.75 17.3848 17.3848 21.75 12 21.75C6.61522 21.75 2.25 17.3848 2.25 12ZM11.4346 6.31004C12.1055 6.17314 12.8016 6.27204 13.4089 6.58932L13.4116 6.59074C14.0173 6.91037 14.4974 7.42629 14.7778 8.05316C15.0582 8.6798 15.1241 9.38318 14.9657 10.0516C14.8073 10.7201 14.4329 11.3179 13.8992 11.7478C13.5634 12.0182 13.1769 12.2121 12.766 12.3194L12.766 13C12.766 13.4142 12.4302 13.75 12.016 13.75C11.6018 13.75 11.266 13.4142 11.266 13L11.266 11.6666C11.266 11.2533 11.6003 10.9179 12.0136 10.9166C12.3547 10.9155 12.6874 10.7978 12.9583 10.5796C13.2296 10.3611 13.4236 10.054 13.5061 9.7057C13.5887 9.35728 13.5541 8.99081 13.4087 8.66579C13.2635 8.34144 13.0175 8.07918 12.7129 7.91806C12.4103 7.76042 12.0658 7.71214 11.7345 7.77976C11.4024 7.84752 11.0997 8.02843 10.8772 8.29658C10.6126 8.61532 10.1398 8.65925 9.82106 8.39471C9.50232 8.13018 9.45839 7.65734 9.72293 7.3386C10.1611 6.81066 10.7638 6.44691 11.4346 6.31004ZM12 15.25C12.4142 15.25 12.75 15.5858 12.75 16V16.04C12.75 16.4542 12.4142 16.79 12 16.79C11.5858 16.79 11.25 16.4542 11.25 16.04V16C11.25 15.5858 11.5858 15.25 12 15.25Z"})))),h=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M8.08961 4.0956C10.0932 3.02269 12.4216 2.72496 14.6307 3.25921C16.8397 3.79346 18.7748 5.1223 20.0667 6.99219C21.3585 8.86208 21.9168 11.1421 21.6349 13.3973C21.353 15.6525 20.2507 17.725 18.5383 19.2194C16.8259 20.7137 14.6233 21.5254 12.3506 21.4994C10.078 21.4734 7.89454 20.6117 6.21673 19.0786C4.53891 17.5456 3.48423 15.4484 3.25392 13.1874C3.21194 12.7753 3.51197 12.4072 3.92405 12.3652C4.33614 12.3233 4.70422 12.6233 4.7462 13.0354C4.93916 14.9298 5.82281 16.6868 7.22855 17.9713C8.63428 19.2558 10.4637 19.9777 12.3678 19.9995C14.2719 20.0212 16.1173 19.3412 17.552 18.0892C18.9867 16.8372 19.9103 15.1008 20.1464 13.2113C20.3826 11.3218 19.9149 9.41147 18.8325 7.84481C17.7502 6.27814 16.1289 5.16479 14.2781 4.71718C12.4272 4.26956 10.4764 4.51901 8.79772 5.41794C7.44561 6.14199 6.34633 7.24658 5.62839 8.58361H8.72228C9.13649 8.58361 9.47228 8.91939 9.47228 9.33361C9.47228 9.74782 9.13649 10.0836 8.72228 10.0836H4.48963C4.47805 10.0839 4.46644 10.0839 4.4548 10.0836H4.00006C3.58584 10.0836 3.25006 9.74782 3.25006 9.33361V4.61139C3.25006 4.19717 3.58584 3.86139 4.00006 3.86139C4.41427 3.86139 4.75006 4.19717 4.75006 4.61139V7.1337C5.58912 5.86995 6.73269 4.82222 8.08961 4.0956ZM12.4528 8.27753C12.867 8.27753 13.2028 8.61332 13.2028 9.02753V12.4946L14.872 14.1639C15.1649 14.4568 15.1649 14.9316 14.872 15.2245C14.5792 15.5174 14.1043 15.5174 13.8114 15.2245L11.9225 13.3356C11.7818 13.195 11.7028 13.0042 11.7028 12.8053V9.02753C11.7028 8.61332 12.0386 8.27753 12.4528 8.27753Z"})))),g=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M11.4697 2.46967C11.7626 2.17678 12.2375 2.17678 12.5304 2.46967L21.5304 11.4697C21.7449 11.6842 21.809 12.0068 21.6929 12.287C21.5768 12.5673 21.3034 12.75 21 12.75H19.75V19C19.75 19.7293 19.4603 20.4288 18.9446 20.9445C18.4288 21.4603 17.7294 21.75 17 21.75H7.00002C6.27068 21.75 5.5712 21.4603 5.05548 20.9445C4.53975 20.4288 4.25002 19.7293 4.25002 19V12.75H3.00002C2.69668 12.75 2.4232 12.5673 2.30711 12.287C2.19103 12.0068 2.25519 11.6842 2.46969 11.4697L11.4697 2.46967ZM9.75002 20.25H14.25V15C14.25 14.6685 14.1183 14.3505 13.8839 14.1161C13.6495 13.8817 13.3315 13.75 13 13.75H11C10.6685 13.75 10.3506 13.8817 10.1161 14.1161C9.88172 14.3505 9.75002 14.6685 9.75002 15V20.25ZM15.75 20.25V15C15.75 14.2707 15.4603 13.5712 14.9446 13.0555C14.4288 12.5397 13.7294 12.25 13 12.25H11C10.2707 12.25 9.5712 12.5397 9.05548 13.0555C8.53975 13.5712 8.25002 14.2707 8.25002 15V20.25H7.00002C6.6685 20.25 6.35056 20.1183 6.11614 19.8839C5.88172 19.6495 5.75002 19.3315 5.75002 19V12C5.75002 11.5858 5.41424 11.25 5.00002 11.25H4.81068L12 4.06066L19.1894 11.25H19C18.5858 11.25 18.25 11.5858 18.25 12V19C18.25 19.3315 18.1183 19.6495 17.8839 19.8839C17.6495 20.1183 17.3315 20.25 17 20.25H15.75Z"})))),S=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4 6.75C3.30964 6.75 2.75 7.30964 2.75 8V16C2.75 16.6904 3.30964 17.25 4 17.25H20C20.6904 17.25 21.25 16.6904 21.25 16V8C21.25 7.30964 20.6904 6.75 20 6.75H4ZM1.25 8C1.25 6.48122 2.48122 5.25 4 5.25H20C21.5188 5.25 22.75 6.48122 22.75 8V16C22.75 17.5188 21.5188 18.75 20 18.75H4C2.48122 18.75 1.25 17.5188 1.25 16V8ZM6 9.25C6.41421 9.25 6.75 9.58579 6.75 10V10.01C6.75 10.4242 6.41421 10.76 6 10.76C5.58579 10.76 5.25 10.4242 5.25 10.01V10C5.25 9.58579 5.58579 9.25 6 9.25ZM10 9.25C10.4142 9.25 10.75 9.58579 10.75 10V10.01C10.75 10.4242 10.4142 10.76 10 10.76C9.58579 10.76 9.25 10.4242 9.25 10.01V10C9.25 9.58579 9.58579 9.25 10 9.25ZM14 9.25C14.4142 9.25 14.75 9.58579 14.75 10V10.01C14.75 10.4242 14.4142 10.76 14 10.76C13.5858 10.76 13.25 10.4242 13.25 10.01V10C13.25 9.58579 13.5858 9.25 14 9.25ZM18 9.25C18.4142 9.25 18.75 9.58579 18.75 10V10.01C18.75 10.4242 18.4142 10.76 18 10.76C17.5858 10.76 17.25 10.4242 17.25 10.01V10C17.25 9.58579 17.5858 9.25 18 9.25ZM6 13.25C6.41421 13.25 6.75 13.5858 6.75 14V14.01C6.75 14.4242 6.41421 14.76 6 14.76C5.58579 14.76 5.25 14.4242 5.25 14.01V14C5.25 13.5858 5.58579 13.25 6 13.25ZM9.25 14C9.25 13.5858 9.58579 13.25 10 13.25H14C14.4142 13.25 14.75 13.5858 14.75 14C14.75 14.4142 14.4142 14.75 14 14.75H10C9.58579 14.75 9.25 14.4142 9.25 14ZM18 13.25C18.4142 13.25 18.75 13.5858 18.75 14V14.01C18.75 14.4242 18.4142 14.76 18 14.76C17.5858 14.76 17.25 14.4242 17.25 14.01V14C17.25 13.5858 17.5858 13.25 18 13.25Z"})))),x=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7 3.5C6.66848 3.5 6.35054 3.6317 6.11612 3.86612C5.8817 4.10054 5.75 4.41848 5.75 4.75V9.25C5.75 9.66421 5.41421 10 5 10C4.58579 10 4.25 9.66421 4.25 9.25V4.75C4.25 4.02065 4.53973 3.32118 5.05546 2.80546C5.57118 2.28973 6.27065 2 7 2H14C14.1989 2 14.3897 2.07902 14.5303 2.21967L19.5303 7.21967C19.671 7.36032 19.75 7.55109 19.75 7.75V9.25C19.75 9.66421 19.4142 10 19 10C18.5858 10 18.25 9.66421 18.25 9.25V8.5H15C14.5359 8.5 14.0908 8.31563 13.7626 7.98744C13.4344 7.65925 13.25 7.21413 13.25 6.75V3.5H7ZM14.75 4.56066L17.1893 7H15C14.9337 7 14.8701 6.97366 14.8232 6.92678C14.7763 6.87989 14.75 6.8163 14.75 6.75V4.56066ZM5 11C5.41421 11 5.75 11.3358 5.75 11.75V12.25C5.75 12.6642 5.41421 13 5 13C4.58579 13 4.25 12.6642 4.25 12.25V11.75C4.25 11.3358 4.58579 11 5 11ZM18.25 12.25V11.75C18.25 11.3358 18.5858 11 19 11C19.4142 11 19.75 11.3358 19.75 11.75V12.25C19.75 12.6642 19.4142 13 19 13C18.5858 13 18.25 12.6642 18.25 12.25ZM5 14.5C5.41421 14.5 5.75 14.8358 5.75 15.25V15.75C5.75 16.1642 5.41421 16.5 5 16.5C4.58579 16.5 4.25 16.1642 4.25 15.75V15.25C4.25 14.8358 4.58579 14.5 5 14.5ZM18.25 15.75V15.25C18.25 14.8358 18.5858 14.5 19 14.5C19.4142 14.5 19.75 14.8358 19.75 15.25V15.75C19.75 16.1642 19.4142 16.5 19 16.5C18.5858 16.5 18.25 16.1642 18.25 15.75ZM5 18C5.41421 18 5.75 18.3358 5.75 18.75C5.75 19.0815 5.8817 19.3995 6.11612 19.6339C6.35054 19.8683 6.66848 20 7 20H17C17.3315 20 17.6495 19.8683 17.8839 19.6339C18.1183 19.3995 18.25 19.0815 18.25 18.75C18.25 18.3358 18.5858 18 19 18C19.4142 18 19.75 18.3358 19.75 18.75C19.75 19.4793 19.4603 20.1788 18.9445 20.6945C18.4288 21.2103 17.7293 21.5 17 21.5H7C6.27065 21.5 5.57118 21.2103 5.05546 20.6945C4.53973 20.1788 4.25 19.4793 4.25 18.75C4.25 18.3358 4.58579 18 5 18Z"})))),B=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6 6.75C5.9337 6.75 5.87011 6.77634 5.82322 6.82322C5.77634 6.87011 5.75 6.9337 5.75 7V15C5.75 15.0663 5.77634 15.1299 5.82322 15.1768C5.87011 15.2237 5.9337 15.25 6 15.25H18C18.0663 15.25 18.1299 15.2237 18.1768 15.1768C18.2237 15.1299 18.25 15.0663 18.25 15V7C18.25 6.93369 18.2237 6.87011 18.1768 6.82322C18.1299 6.77634 18.0663 6.75 18 6.75H6ZM4.76256 5.76256C5.09075 5.43438 5.53587 5.25 6 5.25H18C18.4641 5.25 18.9092 5.43437 19.2374 5.76256C19.5656 6.09075 19.75 6.53587 19.75 7V15C19.75 15.4641 19.5656 15.9092 19.2374 16.2374C18.9092 16.5656 18.4641 16.75 18 16.75H6C5.53587 16.75 5.09075 16.5656 4.76256 16.2374C4.43437 15.9092 4.25 15.4641 4.25 15V7C4.25 6.53587 4.43437 6.09075 4.76256 5.76256ZM2.25 19C2.25 18.5858 2.58579 18.25 3 18.25H21C21.4142 18.25 21.75 18.5858 21.75 19C21.75 19.4142 21.4142 19.75 21 19.75H3C2.58579 19.75 2.25 19.4142 2.25 19Z"})))),s=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{width:"16",height:"17",viewBox:"0 0 16 17",fill:"none",xmlns:"http://www.w3.org/2000/svg",...C,ref:e},n.createElement("g",{id:"Link"},n.createElement("g",{id:"Vector"},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10.3537 5.74682C10.549 5.94208 10.549 6.25866 10.3537 6.45393L6.35374 10.4539C6.15848 10.6492 5.84189 10.6492 5.64663 10.4539C5.45137 10.2587 5.45137 9.94208 5.64663 9.74682L9.64663 5.74682C9.8419 5.55156 10.1585 5.55156 10.3537 5.74682Z",fill:"white"}),n.createElement("path",{d:"M12.0682 4.29488L11.7792 4.00588C10.7538 2.98046 9.09124 2.98046 8.06581 4.00589L7.37936 4.69234C7.1841 4.8876 6.86751 4.8876 6.67225 4.69234C6.47699 4.49708 6.47699 4.18049 6.67225 3.98523L7.3587 3.29878C8.77465 1.88283 11.0704 1.88283 12.4863 3.29878L12.7753 3.58778C14.1913 5.00373 14.1913 7.29943 12.7753 8.71538L12.0889 9.40183C11.8936 9.5971 11.577 9.5971 11.3818 9.40184C11.1865 9.20657 11.1865 8.88999 11.3818 8.69473L12.0682 8.00828C13.0936 6.98285 13.0936 5.32031 12.0682 4.29488Z",fill:"white"}),n.createElement("path",{d:"M3.90579 8.16591C2.88036 9.19133 2.88036 10.8539 3.90579 11.8793L4.19479 12.1683C5.22021 13.1937 6.88276 13.1937 7.90818 12.1683L8.59463 11.4818C8.7899 11.2866 9.10648 11.2866 9.30174 11.4818C9.497 11.6771 9.497 11.9937 9.30174 12.189L8.61529 12.8754C7.19934 14.2914 4.90363 14.2914 3.48768 12.8754L3.19868 12.5864C1.78273 11.1705 1.78273 8.87475 3.19868 7.4588L3.88513 6.77235C4.0804 6.57709 4.39698 6.57709 4.59224 6.77235C4.7875 6.96761 4.7875 7.28419 4.59224 7.47946L3.90579 8.16591Z",fill:"white"})))))),T=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.25003 3C3.25003 2.58578 3.58581 2.25 4.00003 2.25H12.1795C12.3784 2.25 12.5692 2.32902 12.7099 2.46967C12.8505 2.61032 12.9295 2.80109 12.9295 3L12.9295 10.5C12.9295 10.9142 12.5937 11.25 12.1795 11.25H4C3.80109 11.25 3.61032 11.171 3.46967 11.0303C3.32902 10.8897 3.25 10.6989 3.25 10.5L3.25003 3ZM4.75002 3.75L4.75 9.75H11.4295L11.4295 3.75H4.75002ZM3.25003 13.5C3.25003 13.0858 3.58581 12.75 4.00003 12.75H12.1795C12.5937 12.75 12.9295 13.0858 12.9295 13.5C12.9295 13.9142 12.5937 14.25 12.1795 14.25H4.00003C3.58581 14.25 3.25003 13.9142 3.25003 13.5Z"}),n.createElement("path",{d:"M20.4919 6.96052L20.4757 7.02512L20.4514 7.08164L18.8002 9.14069L18.7516 9.19721C18.6059 9.31833 18.3874 9.32641 18.2336 9.20529C18.0555 9.06802 18.0312 8.8177 18.1688 8.64006L19.2616 7.27543H15.6515L15.5787 7.26736C15.3925 7.23506 15.2468 7.07357 15.2468 6.8717C15.2468 6.64561 15.4249 6.46796 15.6515 6.46796H19.2616L18.1607 5.10334L18.1203 5.03874C18.0312 4.86917 18.0717 4.65115 18.2255 4.53003C18.3955 4.39276 18.6545 4.41699 18.7921 4.59463L20.411 6.61331L20.4514 6.67791L20.4757 6.72635L20.5 6.81518V6.88785L20.4919 6.96052Z"}),n.createElement("path",{d:"M7.96771 21.4919L7.90296 21.4758L7.8463 21.4516L5.78226 19.8043L5.72559 19.7559C5.60418 19.6105 5.59609 19.3925 5.7175 19.2391C5.8551 19.0614 6.10603 19.0372 6.2841 19.1745L7.65204 20.2646V16.6633L7.66013 16.5906C7.69251 16.4049 7.85439 16.2595 8.05675 16.2595C8.28339 16.2595 8.46147 16.4372 8.46147 16.6633V20.2646L9.8294 19.1664L9.89415 19.126C10.0641 19.0372 10.2827 19.0776 10.4041 19.231C10.5417 19.4006 10.5174 19.659 10.3393 19.7962L8.31577 21.4112L8.25101 21.4516L8.20245 21.4758L8.11341 21.5H8.04056L7.96771 21.4919Z"}),n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M18.9883 4.43971L20.606 6.4569C20.6121 6.46446 20.6177 6.47237 20.6228 6.48058L20.6633 6.54518C20.6675 6.55189 20.6714 6.5588 20.6749 6.56588L20.6992 6.61433C20.7066 6.62908 20.7125 6.64452 20.7169 6.66043L20.7411 6.74925C20.747 6.77073 20.75 6.7929 20.75 6.81518V6.88785C20.75 6.89709 20.7495 6.90633 20.7485 6.91552L20.7404 6.9882C20.7391 6.99935 20.7371 7.0104 20.7344 7.02129L20.7182 7.08589C20.715 7.09885 20.7107 7.11153 20.7054 7.1238L20.6811 7.18032C20.6722 7.20104 20.6606 7.22046 20.6465 7.23805L18.9899 9.30371L18.9413 9.36014C18.9322 9.37073 18.9222 9.38053 18.9115 9.38946C18.6782 9.58335 18.3292 9.59806 18.0799 9.40245C17.7863 9.17522 17.7547 8.76653 17.9712 8.48697L17.9737 8.48378L18.7411 7.52543H15.6515C15.6423 7.52543 15.6331 7.52493 15.624 7.52391L15.5511 7.51584C15.546 7.51527 15.541 7.51455 15.5359 7.51368C15.2339 7.46128 14.9968 7.1991 14.9968 6.8717C14.9968 6.50696 15.2874 6.21796 15.6515 6.21796H18.7387L17.9662 5.2603C17.9599 5.25258 17.9542 5.24448 17.9489 5.23607L17.9084 5.17147C17.9051 5.16609 17.9019 5.16058 17.8989 5.15496C17.7566 4.88388 17.8181 4.53362 18.0697 4.33455C18.3447 4.11356 18.7623 4.15006 18.9883 4.43971ZM18.5945 4.74773C18.5452 4.68419 18.447 4.67254 18.3826 4.72453L18.3802 4.72645C18.3269 4.7684 18.3074 4.84949 18.3384 4.91599L18.3647 4.95798L19.4562 6.311C19.5166 6.38594 19.5287 6.48894 19.4871 6.5758C19.4456 6.66267 19.3579 6.71796 19.2616 6.71796H15.6515C15.5624 6.71796 15.4968 6.78425 15.4968 6.8717C15.4968 6.94594 15.5482 7.0055 15.6157 7.01993L15.6653 7.02543H19.2616C19.3577 7.02543 19.4453 7.08054 19.4869 7.16719C19.5285 7.25383 19.5168 7.35667 19.4567 7.4317L18.3655 8.79442C18.3083 8.86955 18.325 8.96008 18.3862 9.00729L18.3883 9.00889C18.4423 9.05143 18.5216 9.05306 18.5789 9.0146L18.6078 8.98098L20.2351 6.95174L20.2381 6.94488L20.2453 6.9161L20.25 6.87397V6.84873L20.2411 6.8162L20.2333 6.80056L20.2068 6.75836L18.5945 4.74773ZM7.41385 16.5476C7.46655 16.2453 7.72979 16.0095 8.05675 16.0095C8.42089 16.0095 8.71147 16.2985 8.71147 16.6633V19.7433L9.6729 18.9715C9.68063 18.9653 9.68872 18.9595 9.69713 18.9543L9.76189 18.9139C9.76726 18.9105 9.77276 18.9074 9.77837 18.9045C10.0493 18.7629 10.3996 18.8237 10.5991 19.0746C10.8212 19.3496 10.7843 19.7673 10.4938 19.9928L8.47171 21.6066C8.46415 21.6126 8.45625 21.6182 8.44804 21.6233L8.38329 21.6637C8.37658 21.6679 8.36967 21.6717 8.3626 21.6753L8.31403 21.6995C8.29933 21.7068 8.28394 21.7127 8.26808 21.717L8.17904 21.7412C8.15765 21.7471 8.13558 21.75 8.11341 21.75H8.04056C8.03136 21.75 8.02216 21.7495 8.01302 21.7485L7.94017 21.7404C7.92907 21.7392 7.91806 21.7372 7.90722 21.7345L7.84246 21.7184C7.82955 21.7151 7.81692 21.7109 7.80468 21.7057L7.74802 21.6814C7.72734 21.6726 7.70794 21.661 7.69036 21.647L5.61969 19.9944L5.56313 19.9459C5.55251 19.9368 5.54268 19.9269 5.53373 19.9161C5.33908 19.6831 5.32427 19.3341 5.52071 19.0849C5.7484 18.7922 6.15709 18.7609 6.43673 18.9765L6.43992 18.979L7.40204 19.7457V16.6633C7.40204 16.654 7.40255 16.6448 7.40357 16.6356L7.41167 16.5629C7.41224 16.5578 7.41296 16.5527 7.41385 16.5476ZM7.90753 16.6278L7.90204 16.6771V20.2646C7.90204 20.3606 7.84705 20.4481 7.76055 20.4898C7.67405 20.5315 7.57132 20.5199 7.49623 20.4601L6.13019 19.3715C6.05427 19.314 5.96256 19.331 5.91514 19.3922L5.91354 19.3942C5.87123 19.4477 5.86954 19.5259 5.90781 19.5827L5.94151 19.6116L7.97575 21.235L7.98285 21.238L8.01194 21.2453L8.05438 21.25H8.08001L8.11297 21.241L8.12882 21.2331L8.17117 21.2067L10.1867 19.5982C10.2503 19.5492 10.2616 19.4522 10.21 19.3885L10.208 19.3862C10.166 19.333 10.0839 19.313 10.0165 19.3444L9.9743 19.3707L8.61797 20.4595C8.54297 20.5197 8.44008 20.5316 8.35336 20.49C8.26664 20.4484 8.21147 20.3608 8.21147 20.2646V16.6633C8.21147 16.5758 8.14589 16.5095 8.05675 16.5095C7.98116 16.5095 7.92184 16.5614 7.90753 16.6278Z"})))),b=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7 5C6.40326 5 5.83097 5.23705 5.40901 5.65901C4.98705 6.08097 4.75 6.65326 4.75 7.25V18.4393L7.46967 15.7197C7.61032 15.579 7.80109 15.5 8 15.5H17C17.5967 15.5 18.169 15.2629 18.591 14.841C19.0129 14.419 19.25 13.8467 19.25 13.25V7.25C19.25 6.65326 19.0129 6.08097 18.591 5.65901C18.169 5.23705 17.5967 5 17 5H7ZM4.34835 4.59835C5.05161 3.89509 6.00544 3.5 7 3.5H17C17.9946 3.5 18.9484 3.89509 19.6516 4.59835C20.3549 5.30161 20.75 6.25544 20.75 7.25V13.25C20.75 14.2446 20.3549 15.1984 19.6516 15.9017C18.9484 16.6049 17.9946 17 17 17H8.31066L4.53033 20.7803C4.31583 20.9948 3.99324 21.059 3.71299 20.9429C3.43273 20.8268 3.25 20.5533 3.25 20.25V7.25C3.25 6.25544 3.64509 5.30161 4.34835 4.59835Z"})))),y=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M5.38889 7.75C4.66893 7.75 4.25 8.24587 4.25 8.66667L4.25 15.3333C4.25 15.7541 4.66893 16.25 5.38889 16.25L18.6111 16.25C19.3311 16.25 19.75 15.7541 19.75 15.3333V13.7073C19.6718 13.735 19.5877 13.75 19.5 13.75C19.0858 13.75 18.75 13.4142 18.75 13V11C18.75 10.5858 19.0858 10.25 19.5 10.25C19.5877 10.25 19.6718 10.265 19.75 10.2927V8.66667C19.75 8.24587 19.3311 7.75 18.6111 7.75L5.38889 7.75ZM2.75 8.66667C2.75 7.24652 4.02244 6.25 5.38889 6.25L18.6111 6.25C19.9776 6.25 21.25 7.24652 21.25 8.66667V15.3333C21.25 16.7535 19.9776 17.75 18.6111 17.75L5.38889 17.75C4.02244 17.75 2.75 16.7535 2.75 15.3333L2.75 8.66667Z"})))),P=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M8.66667 4.25C8.24587 4.25 7.75 4.66893 7.75 5.38889V18.6111C7.75 19.3311 8.24587 19.75 8.66667 19.75H15.3333C15.7541 19.75 16.25 19.3311 16.25 18.6111V5.38889C16.25 4.66893 15.7541 4.25 15.3333 4.25H13.7073C13.735 4.32819 13.75 4.41234 13.75 4.5C13.75 4.91421 13.4142 5.25 13 5.25H11C10.5858 5.25 10.25 4.91421 10.25 4.5C10.25 4.41234 10.265 4.32819 10.2927 4.25H8.66667ZM6.25 5.38889C6.25 4.02244 7.24652 2.75 8.66667 2.75H15.3333C16.7535 2.75 17.75 4.02244 17.75 5.38889V18.6111C17.75 19.9776 16.7535 21.25 15.3333 21.25H8.66667C7.24652 21.25 6.25 19.9776 6.25 18.6111V5.38889Z"})))),k=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6.75 2C6.02066 2 5.32118 2.28973 4.80546 2.80546C4.28973 3.32118 4 4.02065 4 4.75V18.75C4 19.4793 4.28973 20.1788 4.80546 20.6945C5.32118 21.2103 6.02065 21.5 6.75 21.5H16.75C17.4793 21.5 18.1788 21.2103 18.6945 20.6945C19.2103 20.1788 19.5 19.4793 19.5 18.75V7.75C19.5 7.55109 19.421 7.36032 19.2803 7.21967L14.2803 2.21967C14.1397 2.07902 13.9489 2 13.75 2H6.75ZM5.86612 3.86612C6.10054 3.6317 6.41848 3.5 6.75 3.5H13V6.75C13 7.21413 13.1844 7.65925 13.5126 7.98744C13.8408 8.31563 14.2859 8.5 14.75 8.5H18V18.75C18 19.0815 17.8683 19.3995 17.6339 19.6339C17.3995 19.8683 17.0815 20 16.75 20H6.75C6.41848 20 6.10054 19.8683 5.86612 19.6339C5.6317 19.3995 5.5 19.0815 5.5 18.75V4.75C5.5 4.41848 5.6317 4.10054 5.86612 3.86612ZM16.9393 7L14.5 4.56066V6.75C14.5 6.8163 14.5263 6.87989 14.5732 6.92678C14.6201 6.97366 14.6837 7 14.75 7H16.9393Z"}),n.createElement("path",{d:"M8.5 12.25C8.08579 12.25 7.75 12.5858 7.75 13C7.75 13.4142 8.08579 13.75 8.5 13.75H15C15.4142 13.75 15.75 13.4142 15.75 13C15.75 12.5858 15.4142 12.25 15 12.25H8.5Z"}),n.createElement("path",{d:"M8.5 16.25C8.08579 16.25 7.75 16.5858 7.75 17C7.75 17.4142 8.08579 17.75 8.5 17.75H15C15.4142 17.75 15.75 17.4142 15.75 17C15.75 16.5858 15.4142 16.25 15 16.25H8.5Z"})))),j=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6 3.75C5.66848 3.75 5.35054 3.8817 5.11612 4.11612C4.8817 4.35054 4.75 4.66848 4.75 5V19C4.75 19.3315 4.8817 19.6495 5.11612 19.8839C5.35054 20.1183 5.66848 20.25 6 20.25H8C8.41421 20.25 8.75 20.5858 8.75 21C8.75 21.4142 8.41421 21.75 8 21.75H6C5.27065 21.75 4.57118 21.4603 4.05546 20.9445C3.53973 20.4288 3.25 19.7293 3.25 19V5C3.25 4.27065 3.53973 3.57118 4.05546 3.05546C4.57118 2.53973 5.27065 2.25 6 2.25H13C13.1989 2.25 13.3897 2.32902 13.5303 2.46967L18.5303 7.46967C18.671 7.61032 18.75 7.80109 18.75 8V12C18.75 12.4142 18.4142 12.75 18 12.75C17.5858 12.75 17.25 12.4142 17.25 12V8.75H14C13.5359 8.75 13.0908 8.56563 12.7626 8.23744C12.4344 7.90925 12.25 7.46413 12.25 7V3.75H6ZM13.75 4.81066L16.1893 7.25H14C13.9337 7.25 13.8701 7.22366 13.8232 7.17678C13.7763 7.12989 13.75 7.0663 13.75 7V4.81066ZM12 14.75C11.9176 14.75 11.8482 14.781 11.8046 14.8217C11.7628 14.8607 11.75 14.9021 11.75 14.9333V20.0667C11.75 20.0979 11.7628 20.1393 11.8046 20.1783C11.8482 20.219 11.9176 20.25 12 20.25H19C19.0824 20.25 19.1518 20.219 19.1954 20.1783C19.2372 20.1393 19.25 20.0979 19.25 20.0667V16.3333C19.25 16.3021 19.2372 16.2607 19.1954 16.2217C19.1518 16.181 19.0824 16.15 19 16.15H15.5C15.31 16.15 15.1271 16.0779 14.9883 15.9483L13.7044 14.75H12ZM10.7812 13.7251C11.1127 13.4156 11.5519 13.25 12 13.25H14C14.19 13.25 14.3729 13.3221 14.5117 13.4517L15.7956 14.65H19C19.4481 14.65 19.8873 14.8156 20.2188 15.1251C20.5521 15.4361 20.75 15.8695 20.75 16.3333V20.0667C20.75 20.5305 20.5521 20.9639 20.2188 21.2749C19.8873 21.5844 19.4481 21.75 19 21.75H12C11.5519 21.75 11.1127 21.5844 10.7812 21.2749C10.4479 20.9639 10.25 20.5305 10.25 20.0667V14.9333C10.25 14.4695 10.4479 14.0361 10.7812 13.7251Z"})))),O=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7 3.75C6.66848 3.75 6.35054 3.8817 6.11612 4.11612C5.8817 4.35054 5.75 4.66848 5.75 5V19C5.75 19.3315 5.8817 19.6495 6.11612 19.8839C6.35054 20.1183 6.66848 20.25 7 20.25H17C17.3315 20.25 17.6495 20.1183 17.8839 19.8839C18.1183 19.6495 18.25 19.3315 18.25 19V8.75H15C14.5359 8.75 14.0908 8.56563 13.7626 8.23744C13.4344 7.90925 13.25 7.46413 13.25 7V3.75H7ZM14.75 4.81066L17.1893 7.25H15C14.9337 7.25 14.8701 7.22366 14.8232 7.17678C14.7763 7.12989 14.75 7.0663 14.75 7V4.81066ZM5.05546 3.05546C5.57118 2.53973 6.27065 2.25 7 2.25H14C14.1989 2.25 14.3897 2.32902 14.5303 2.46967L19.5303 7.46967C19.671 7.61032 19.75 7.80109 19.75 8V19C19.75 19.7293 19.4603 20.4288 18.9445 20.9445C18.4288 21.4603 17.7293 21.75 17 21.75H7C6.27065 21.75 5.57118 21.4603 5.05546 20.9445C4.53973 20.4288 4.25 19.7293 4.25 19V5C4.25 4.27065 4.53973 3.57118 5.05546 3.05546Z"})))),A=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M19 19.25C19.1381 19.25 19.25 19.1381 19.25 19V4.75L4.75 4.75L4.75 19.25L19 19.25ZM19 20.75C19.9665 20.75 20.75 19.9665 20.75 19L20.75 5C20.75 4.0335 19.9665 3.25 19 3.25L5 3.25C4.0335 3.25 3.25 4.0335 3.25 5L3.25 19C3.25 19.9665 4.0335 20.75 5 20.75L19 20.75ZM4.75 19.25L4.75 4.75L4.75 19C4.75 19.1381 4.86193 19.25 5 19.25H4.75ZM4.75 4.75L19.25 4.75L19.25 5C19.25 4.86193 19.1381 4.75 19 4.75L5 4.75C4.86193 4.75 4.75 4.86193 4.75 5V4.75Z"}),n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M11.5911 7.46967C11.884 7.17678 12.3588 7.17678 12.6517 7.46967L16.6519 11.4698C16.9448 11.7627 16.9448 12.2376 16.6519 12.5305C16.359 12.8234 15.8841 12.8234 15.5912 12.5305L11.5911 8.53033C11.2982 8.23744 11.2982 7.76256 11.5911 7.46967Z"}),n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M16.6514 7.46967C16.9443 7.76256 16.9443 8.23744 16.6514 8.53033L12.6513 12.5305C12.3584 12.8234 11.8835 12.8234 11.5906 12.5305C11.2977 12.2376 11.2977 11.7627 11.5906 11.4698L15.5908 7.46967C15.8837 7.17678 16.3585 7.17678 16.6514 7.46967Z"})))),D=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7 3.75C6.30964 3.75 5.75 4.30964 5.75 5V19C5.75 19.6904 6.30964 20.25 7 20.25H17C17.6904 20.25 18.25 19.6904 18.25 19V5C18.25 4.30964 17.6904 3.75 17 3.75H7ZM4.25 5C4.25 3.48122 5.48122 2.25 7 2.25H17C18.5188 2.25 19.75 3.48122 19.75 5V19C19.75 20.5188 18.5188 21.75 17 21.75H7C5.48122 21.75 4.25 20.5188 4.25 19V5ZM8.25 7C8.25 6.58579 8.58579 6.25 9 6.25H15C15.4142 6.25 15.75 6.58579 15.75 7C15.75 7.41421 15.4142 7.75 15 7.75H9C8.58579 7.75 8.25 7.41421 8.25 7ZM8.25 11C8.25 10.5858 8.58579 10.25 9 10.25H15C15.4142 10.25 15.75 10.5858 15.75 11C15.75 11.4142 15.4142 11.75 15 11.75H9C8.58579 11.75 8.25 11.4142 8.25 11ZM8.25 15C8.25 14.5858 8.58579 14.25 9 14.25H13C13.4142 14.25 13.75 14.5858 13.75 15C13.75 15.4142 13.4142 15.75 13 15.75H9C8.58579 15.75 8.25 15.4142 8.25 15Z"})))),F=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M15.5303 3.46967C15.8232 3.76256 15.8232 4.23744 15.5303 4.53033L12.4532 7.60746L16.3925 11.5468L19.4697 8.46967C19.7626 8.17678 20.2374 8.17678 20.5303 8.46967C20.8232 8.76256 20.8232 9.23744 20.5303 9.53033L17.4532 12.6075L18.5304 13.6847C18.6711 13.8253 18.7501 14.0161 18.7501 14.215C18.7501 14.4139 18.6711 14.6047 18.5304 14.7453L16.4815 16.7943C15.8779 17.4205 15.1559 17.9205 14.3573 18.2651C13.5564 18.6108 12.6946 18.7932 11.8223 18.8018C10.9501 18.8103 10.0849 18.6448 9.27735 18.315C8.70228 18.0801 8.16469 17.7652 7.68008 17.3806L4.53033 20.5303C4.23744 20.8232 3.76256 20.8232 3.46967 20.5303C3.17678 20.2374 3.17678 19.7626 3.46967 19.4697L6.61943 16.3199C6.23487 15.8353 5.92 15.2978 5.68511 14.7227C5.35525 13.9152 5.18976 13.05 5.19831 12.1777C5.20687 11.3055 5.3893 10.4437 5.73494 9.64279C6.07957 8.84421 6.57956 8.12216 7.20579 7.51863L9.25475 5.46967C9.54765 5.17678 10.0225 5.17678 10.3154 5.46967L11.3925 6.5468L14.4697 3.46967C14.7626 3.17678 15.2374 3.17678 15.5303 3.46967ZM9.78508 7.06066L8.26141 8.58433L8.25091 8.59463C7.7659 9.06098 7.37876 9.61937 7.11216 10.2371C6.84556 10.8549 6.70484 11.5196 6.69824 12.1925C6.69164 12.8653 6.81929 13.5326 7.07373 14.1555C7.32816 14.7784 7.70427 15.3443 8.18004 15.82C8.65581 16.2958 9.22169 16.6719 9.84457 16.9264C10.4674 17.1808 11.1348 17.3084 11.8076 17.3018C12.4804 17.2952 13.1452 17.1545 13.7629 16.8879C14.3807 16.6213 14.9391 16.2342 15.4055 15.7492L15.4158 15.7387L16.9394 14.215L9.78508 7.06066Z"})))),z=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M11 4.75C11 4.33579 11.3358 4 11.75 4C12.1642 4 12.5 4.33579 12.5 4.75V11H18.75C19.1642 11 19.5 11.3358 19.5 11.75C19.5 12.1642 19.1642 12.5 18.75 12.5H12.5V18.75C12.5 19.1642 12.1642 19.5 11.75 19.5C11.3358 19.5 11 19.1642 11 18.75V12.5H4.75C4.33579 12.5 4 12.1642 4 11.75C4 11.3358 4.33579 11 4.75 11H11V4.75Z"})))),U=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7 3.75C6.66848 3.75 6.35054 3.8817 6.11612 4.11612C5.8817 4.35054 5.75 4.66848 5.75 5V19C5.75 19.3315 5.8817 19.6495 6.11612 19.8839C6.35054 20.1183 6.66848 20.25 7 20.25H12C12.4142 20.25 12.75 20.5858 12.75 21C12.75 21.4142 12.4142 21.75 12 21.75H7C6.27065 21.75 5.57118 21.4603 5.05546 20.9445C4.53973 20.4288 4.25 19.7293 4.25 19V5C4.25 4.27065 4.53973 3.57118 5.05546 3.05546C5.57118 2.53973 6.27065 2.25 7 2.25H14C14.1989 2.25 14.3897 2.32902 14.5303 2.46967L19.5303 7.46967C19.671 7.61032 19.75 7.80109 19.75 8V12.5C19.75 12.9142 19.4142 13.25 19 13.25C18.5858 13.25 18.25 12.9142 18.25 12.5V8.75H15C14.5359 8.75 14.0908 8.56563 13.7626 8.23744C13.4344 7.90925 13.25 7.46413 13.25 7V3.75H7ZM14.75 4.81066L17.1893 7.25H15C14.9337 7.25 14.8701 7.22366 14.8232 7.17678C14.7763 7.12989 14.75 7.0663 14.75 7V4.81066ZM16.5 15.75C15.5335 15.75 14.75 16.5335 14.75 17.5C14.75 18.4665 15.5335 19.25 16.5 19.25C17.4665 19.25 18.25 18.4665 18.25 17.5C18.25 16.5335 17.4665 15.75 16.5 15.75ZM13.25 17.5C13.25 15.7051 14.7051 14.25 16.5 14.25C18.2949 14.25 19.75 15.7051 19.75 17.5C19.75 18.1257 19.5732 18.7102 19.2667 19.2061L21.5303 21.4697C21.8232 21.7626 21.8232 22.2374 21.5303 22.5303C21.2374 22.8232 20.7626 22.8232 20.4697 22.5303L18.2061 20.2667C17.7102 20.5732 17.1257 20.75 16.5 20.75C14.7051 20.75 13.25 19.2949 13.25 17.5Z"})))),W=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7.55012 4.45178C9.23098 3.48072 11.1845 3.08925 13.1097 3.33767C15.035 3.58609 16.8251 4.46061 18.2045 5.82653C19.5838 7.19245 20.4757 8.97399 20.743 10.8967C20.8 11.307 20.5136 11.6858 20.1033 11.7428C19.6931 11.7998 19.3142 11.5135 19.2572 11.1032C19.0353 9.50635 18.2945 8.02677 17.149 6.89236C16.0035 5.75795 14.5167 5.03165 12.9178 4.82534C11.3189 4.61902 9.69644 4.94414 8.30047 5.75061C7.24361 6.36117 6.36093 7.22198 5.72541 8.24995H8.00009C8.41431 8.24995 8.75009 8.58574 8.75009 8.99995C8.75009 9.41417 8.41431 9.74995 8.00009 9.74995H4.51686C4.5055 9.75021 4.49412 9.75021 4.48272 9.74995H4.00009C3.58588 9.74995 3.25009 9.41417 3.25009 8.99995V4.99995C3.25009 4.58574 3.58588 4.24995 4.00009 4.24995C4.41431 4.24995 4.75009 4.58574 4.75009 4.99995V7.00691C5.48358 5.96916 6.43655 5.0951 7.55012 4.45178Z",fill:"black"}),n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.89686 12.2571C4.30713 12.2001 4.68594 12.4864 4.74295 12.8967C4.96487 14.4936 5.70565 15.9731 6.85119 17.1075C7.99673 18.242 9.48347 18.9683 11.0824 19.1746C12.6813 19.3809 14.3037 19.0558 15.6997 18.2493C16.7566 17.6387 17.6393 16.7779 18.2748 15.75H16.0001C15.5859 15.75 15.2501 15.4142 15.2501 15C15.2501 14.5857 15.5859 14.25 16.0001 14.25H19.4833C19.4947 14.2497 19.5061 14.2497 19.5175 14.25H20.0001C20.4143 14.25 20.7501 14.5857 20.7501 15V19C20.7501 19.4142 20.4143 19.75 20.0001 19.75C19.5859 19.75 19.2501 19.4142 19.2501 19V16.993C18.5166 18.0307 17.5636 18.9048 16.4501 19.5481C14.7692 20.5192 12.8157 20.9107 10.8904 20.6622C8.9652 20.4138 7.17504 19.5393 5.79572 18.1734C4.4164 16.8074 3.52443 15.0259 3.25723 13.1032C3.20022 12.6929 3.48658 12.3141 3.89686 12.2571Z",fill:"black"})))),_=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10 3.75C6.54822 3.75 3.75 6.54822 3.75 10C3.75 13.4518 6.54822 16.25 10 16.25C13.4518 16.25 16.25 13.4518 16.25 10C16.25 6.54822 13.4518 3.75 10 3.75ZM2.25 10C2.25 5.71979 5.71979 2.25 10 2.25C14.2802 2.25 17.75 5.71979 17.75 10C17.75 11.87 17.0877 13.5853 15.9848 14.9242L21.5303 20.4697C21.8232 20.7626 21.8232 21.2374 21.5303 21.5303C21.2374 21.8232 20.7626 21.8232 20.4697 21.5303L14.9242 15.9848C13.5853 17.0877 11.87 17.75 10 17.75C5.71979 17.75 2.25 14.2802 2.25 10Z"})))),K=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.75 5.25C3.6837 5.25 3.62011 5.27634 3.57322 5.32322C3.52634 5.37011 3.5 5.4337 3.5 5.5V6.5C3.5 6.91421 3.16421 7.25 2.75 7.25C2.33579 7.25 2 6.91421 2 6.5V5.5C2 5.03587 2.18438 4.59075 2.51256 4.26256C2.84075 3.93438 3.28587 3.75 3.75 3.75H4.75C5.16421 3.75 5.5 4.08579 5.5 4.5C5.5 4.91421 5.16421 5.25 4.75 5.25H3.75ZM8.5 4.5C8.5 4.08579 8.83579 3.75 9.25 3.75H12.25C12.6642 3.75 13 4.08579 13 4.5C13 4.91421 12.6642 5.25 12.25 5.25H9.25C8.83579 5.25 8.5 4.91421 8.5 4.5ZM16 4.5C16 4.08579 16.3358 3.75 16.75 3.75H17.75C18.2141 3.75 18.6592 3.93437 18.9874 4.26256C19.3156 4.59075 19.5 5.03587 19.5 5.5V6.5C19.5 6.91421 19.1642 7.25 18.75 7.25C18.3358 7.25 18 6.91421 18 6.5V5.5C18 5.43369 17.9737 5.37011 17.9268 5.32322C17.8799 5.27634 17.8163 5.25 17.75 5.25H16.75C16.3358 5.25 16 4.91421 16 4.5ZM2.75 9.75C3.16421 9.75 3.5 10.0858 3.5 10.5V13.5C3.5 13.9142 3.16421 14.25 2.75 14.25C2.33579 14.25 2 13.9142 2 13.5V10.5C2 10.0858 2.33579 9.75 2.75 9.75ZM12.75 13.25C12.6676 13.25 12.5982 13.281 12.5546 13.3217C12.5128 13.3607 12.5 13.4021 12.5 13.4333V18.5667C12.5 18.5979 12.5128 18.6393 12.5546 18.6783C12.5982 18.719 12.6676 18.75 12.75 18.75H19.75C19.8324 18.75 19.9018 18.719 19.9454 18.6783C19.9872 18.6393 20 18.5979 20 18.5667V14.8333C20 14.8021 19.9872 14.7607 19.9454 14.7217C19.9018 14.681 19.8324 14.65 19.75 14.65H16.25C16.06 14.65 15.8771 14.5779 15.7383 14.4483L14.4544 13.25H12.75ZM11.5312 12.2251C11.8627 11.9156 12.3019 11.75 12.75 11.75H14.75C14.94 11.75 15.1229 11.8221 15.2617 11.9517L16.5456 13.15H19.75C20.1981 13.15 20.6373 13.3156 20.9688 13.6251C21.3021 13.9361 21.5 14.3695 21.5 14.8333V18.5667C21.5 19.0305 21.3021 19.4639 20.9688 19.7749C20.6373 20.0844 20.1981 20.25 19.75 20.25H12.75C12.3019 20.25 11.8627 20.0844 11.5312 19.7749C11.1979 19.4639 11 19.0305 11 18.5667V13.4333C11 12.9695 11.1979 12.5361 11.5312 12.2251ZM2.75 16.75C3.16421 16.75 3.5 17.0858 3.5 17.5V18.5C3.5 18.5663 3.52634 18.6299 3.57322 18.6768C3.62011 18.7237 3.68369 18.75 3.75 18.75H4.75C5.16421 18.75 5.5 19.0858 5.5 19.5C5.5 19.9142 5.16421 20.25 4.75 20.25H3.75C3.28587 20.25 2.84075 20.0656 2.51256 19.7374C2.18437 19.4092 2 18.9641 2 18.5V17.5C2 17.0858 2.33579 16.75 2.75 16.75Z"})))),X=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12.9461 4.49382C12.7055 3.50206 11.2945 3.50206 11.0539 4.49382L11.0538 4.49421C10.6578 6.12252 8.79686 6.89441 7.36336 6.02285L7.36299 6.02262C6.49035 5.49135 5.49253 6.49022 6.0235 7.3618C6.22619 7.69432 6.34752 8.06998 6.37762 8.45824C6.40773 8.84659 6.34572 9.23656 6.19663 9.59641C6.04755 9.95627 5.8156 10.2758 5.51966 10.5291C5.22378 10.7823 4.8723 10.9621 4.49382 11.0539C3.50206 11.2945 3.50206 12.7055 4.49382 12.9461L4.49422 12.9462C4.87244 13.0382 5.22363 13.2181 5.51923 13.4714C5.81483 13.7246 6.0465 14.0441 6.19542 14.4037C6.34433 14.7633 6.40629 15.153 6.37625 15.5411C6.34621 15.9292 6.22502 16.3047 6.02253 16.6371C5.49145 17.5098 6.49026 18.5074 7.3618 17.9765C7.69431 17.7738 8.06998 17.6525 8.45824 17.6224C8.84659 17.5923 9.23656 17.6543 9.59641 17.8034C9.95627 17.9525 10.2758 18.1844 10.5291 18.4803C10.7823 18.7762 10.9621 19.1277 11.0539 19.5062C11.2945 20.4979 12.7055 20.4979 12.9461 19.5062L12.9462 19.5058C13.0382 19.1276 13.2181 18.7764 13.4714 18.4808C13.7246 18.1852 14.0441 17.9535 14.4037 17.8046C14.7633 17.6557 15.153 17.5937 15.5411 17.6238C15.9292 17.6538 16.3047 17.775 16.6371 17.9775C17.5097 18.5085 18.5074 17.5097 17.9765 16.6382C17.7738 16.3057 17.6525 15.93 17.6224 15.5418C17.5923 15.1534 17.6543 14.7634 17.8034 14.4036C17.9525 14.0437 18.1844 13.7242 18.4803 13.4709C18.7762 13.2177 19.1277 13.0379 19.5062 12.9461C20.4979 12.7055 20.4979 11.2945 19.5062 11.0539L19.5058 11.0538C19.1276 10.9618 18.7764 10.7819 18.4808 10.5286C18.1852 10.2754 17.9535 9.95594 17.8046 9.59631C17.6557 9.23668 17.5937 8.84698 17.6238 8.45889C17.6538 8.07081 17.775 7.69528 17.9775 7.36285C18.5085 6.49025 17.5097 5.49256 16.6382 6.0235C16.3057 6.22619 15.93 6.34752 15.5418 6.37762C15.1534 6.40773 14.7634 6.34572 14.4036 6.19663C14.0437 6.04755 13.7242 5.8156 13.4709 5.51966C13.2177 5.22378 13.0379 4.8723 12.9461 4.49382ZM9.59624 4.13979C10.2079 1.61994 13.7925 1.62007 14.4039 4.14018L14.4039 4.14039C14.44 4.28943 14.5108 4.42783 14.6105 4.54434C14.7102 4.66085 14.836 4.75216 14.9777 4.81086C15.1194 4.86955 15.2729 4.89397 15.4258 4.88211C15.5787 4.87026 15.7266 4.82247 15.8576 4.74264L15.8578 4.7425C18.0722 3.39347 20.6074 5.92764 19.2586 8.14301L19.2585 8.14315C19.1788 8.27403 19.1311 8.42187 19.1193 8.57465C19.1075 8.72744 19.1318 8.88086 19.1905 9.02245C19.2491 9.16404 19.3403 9.28979 19.4567 9.38949C19.573 9.4891 19.7111 9.5599 19.8598 9.59614C22.3801 10.2075 22.3801 13.7925 19.8598 14.4039L19.8596 14.4039C19.7106 14.44 19.5722 14.5108 19.4557 14.6105C19.3392 14.7102 19.2478 14.836 19.1891 14.9777C19.1304 15.1194 19.106 15.2729 19.1179 15.4258C19.1297 15.5787 19.1775 15.7266 19.2574 15.8576L19.2575 15.8578C20.6065 18.0722 18.0724 20.6074 15.857 19.2586L15.8569 19.2585C15.726 19.1788 15.5781 19.1311 15.4253 19.1193C15.2726 19.1075 15.1191 19.1318 14.9776 19.1905C14.836 19.2491 14.7102 19.3403 14.6105 19.4567C14.5109 19.573 14.4401 19.7111 14.4039 19.8598C13.7925 22.3801 10.2075 22.3801 9.59614 19.8598L9.59609 19.8596C9.55998 19.7106 9.48919 19.5722 9.38948 19.4557C9.28977 19.3392 9.16396 19.2478 9.02228 19.1891C8.88061 19.1304 8.72708 19.106 8.57419 19.1179C8.4213 19.1297 8.27337 19.1775 8.14244 19.2574L8.1422 19.2575C5.92778 20.6065 3.39265 18.0724 4.74138 15.857L4.74147 15.8569C4.82118 15.726 4.86889 15.5781 4.88072 15.4253C4.89255 15.2726 4.86816 15.1191 4.80953 14.9776C4.7509 14.836 4.65969 14.7102 4.54332 14.6105C4.42705 14.5109 4.28893 14.4401 4.14018 14.4039C1.61994 13.7925 1.61994 10.2075 4.14018 9.59614L4.14039 9.59609C4.28943 9.55998 4.42783 9.48919 4.54434 9.38948C4.66085 9.28977 4.75216 9.16396 4.81086 9.02228C4.86955 8.88061 4.89397 8.72708 4.88211 8.57419C4.87026 8.4213 4.82247 8.27337 4.74264 8.14244L4.7425 8.1422C3.39354 5.92791 5.92736 3.39294 8.14263 4.74115C8.70903 5.08552 9.4399 4.7816 9.59614 4.14018M12 9.75C10.7574 9.75 9.75 10.7574 9.75 12C9.75 13.2426 10.7574 14.25 12 14.25C13.2426 14.25 14.25 13.2426 14.25 12C14.25 10.7574 13.2426 9.75 12 9.75ZM8.25 12C8.25 9.92893 9.92893 8.25 12 8.25C14.0711 8.25 15.75 9.92893 15.75 12C15.75 14.0711 14.0711 15.75 12 15.75C9.92893 15.75 8.25 14.0711 8.25 12Z"})))),q=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6.46967 15.5303C6.17678 15.2374 6.17678 14.7626 6.46967 14.4697L8.93934 12L6.46967 9.53033C6.17678 9.23744 6.17678 8.76256 6.46967 8.46967C6.76256 8.17678 7.23744 8.17678 7.53033 8.46967L10.5303 11.4697C10.8232 11.7626 10.8232 12.2374 10.5303 12.5303L7.53033 15.5303C7.23744 15.8232 6.76256 15.8232 6.46967 15.5303Z"}),n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M17.5303 15.5303C17.2374 15.8232 16.7626 15.8232 16.4697 15.5303L13.4697 12.5303C13.1768 12.2374 13.1768 11.7626 13.4697 11.4697L16.4697 8.46967C16.7626 8.17678 17.2374 8.17678 17.5303 8.46967C17.8232 8.76256 17.8232 9.23744 17.5303 9.53033L15.0607 12L17.5303 14.4697C17.8232 14.7626 17.8232 15.2374 17.5303 15.5303Z"}),n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10.75 12C10.75 12.4142 10.4142 12.75 10 12.75L1 12.75C0.585787 12.75 0.25 12.4142 0.25 12C0.25 11.5858 0.585787 11.25 1 11.25L10 11.25C10.4142 11.25 10.75 11.5858 10.75 12Z"}),n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M23.75 12C23.75 12.4142 23.4142 12.75 23 12.75H14C13.5858 12.75 13.25 12.4142 13.25 12C13.25 11.5858 13.5858 11.25 14 11.25H23C23.4142 11.25 23.75 11.5858 23.75 12Z"}),n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M17.0303 9.21967C17.3232 9.51256 17.3232 9.98744 17.0303 10.2803L14.5607 12.75L17.0303 15.2197C17.3232 15.5126 17.3232 15.9874 17.0303 16.2803C16.7374 16.5732 16.2626 16.5732 15.9697 16.2803L12.9697 13.2803C12.6768 12.9874 12.6768 12.5126 12.9697 12.2197L15.9697 9.21967C16.2626 8.92678 16.7374 8.92678 17.0303 9.21967Z"}),n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M5.96967 9.21967C6.26256 8.92678 6.73744 8.92678 7.03033 9.21967L10.0303 12.2197C10.3232 12.5126 10.3232 12.9874 10.0303 13.2803L7.03033 16.2803C6.73744 16.5732 6.26256 16.5732 5.96967 16.2803C5.67678 15.9874 5.67678 15.5126 5.96967 15.2197L8.43934 12.75L5.96967 10.2803C5.67678 9.98744 5.67678 9.51256 5.96967 9.21967Z"}),n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12.75 12.75C12.75 12.3358 13.0858 12 13.5 12H22.25C22.6642 12 23 12.3358 23 12.75C23 13.1642 22.6642 13.5 22.25 13.5H13.5C13.0858 13.5 12.75 13.1642 12.75 12.75Z"}),n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M0 12.75C3.62117e-08 12.3358 0.335786 12 0.75 12L9.5 12C9.91421 12 10.25 12.3358 10.25 12.75C10.25 13.1642 9.91421 13.5 9.5 13.5L0.75 13.5C0.335786 13.5 -3.62117e-08 13.1642 0 12.75Z"})))),G=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M11.6645 3.32918C11.8757 3.22361 12.1242 3.22361 12.3353 3.32918L20.3353 7.32918C20.5894 7.45622 20.7499 7.71592 20.7499 8C20.7499 8.28408 20.5894 8.54378 20.3353 8.67082L12.3353 12.6708C12.1242 12.7764 11.8757 12.7764 11.6645 12.6708L3.66451 8.67082C3.41042 8.54378 3.24992 8.28408 3.24992 8C3.24992 7.71592 3.41042 7.45622 3.66451 7.32918L11.6645 3.32918ZM5.67697 8L11.9999 11.1615L18.3229 8L11.9999 4.83853L5.67697 8ZM3.3291 11.6646C3.51434 11.2941 3.96485 11.1439 4.33533 11.3292L11.9999 15.1615L19.6645 11.3292C20.035 11.1439 20.4855 11.2941 20.6707 11.6646C20.856 12.0351 20.7058 12.4856 20.3353 12.6708L12.3353 16.6708C12.1242 16.7764 11.8757 16.7764 11.6645 16.6708L3.66451 12.6708C3.29403 12.4856 3.14386 12.0351 3.3291 11.6646ZM3.3291 15.6646C3.51434 15.2941 3.96485 15.1439 4.33533 15.3292L11.9999 19.1615L19.6645 15.3292C20.035 15.1439 20.4855 15.2941 20.6707 15.6646C20.856 16.0351 20.7058 16.4856 20.3353 16.6708L12.3353 20.6708C12.1242 20.7764 11.8757 20.7764 11.6645 20.6708L3.66451 16.6708C3.29403 16.4856 3.14386 16.0351 3.3291 15.6646Z"})))),J=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4.61111 5.75C3.92825 5.75 3.25 6.3865 3.25 7.33333L3.25 16.6667C3.25 17.6135 3.92825 18.25 4.61111 18.25L19.3889 18.25C20.0718 18.25 20.75 17.6135 20.75 16.6667V13.7073C20.6718 13.735 20.5877 13.75 20.5 13.75C20.0858 13.75 19.75 13.4142 19.75 13V11C19.75 10.5858 20.0858 10.25 20.5 10.25C20.5877 10.25 20.6718 10.265 20.75 10.2927V7.33333C20.75 6.3865 20.0718 5.75 19.3889 5.75L4.61111 5.75ZM1.75 7.33333C1.75 5.70284 2.96211 4.25 4.61111 4.25L19.3889 4.25C21.0379 4.25 22.25 5.70284 22.25 7.33333V16.6667C22.25 18.2972 21.0379 19.75 19.3889 19.75L4.61111 19.75C2.96211 19.75 1.75 18.2972 1.75 16.6667L1.75 7.33333Z"})))),N=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7.33333 3.25C6.3865 3.25 5.75 3.92825 5.75 4.61111V19.3889C5.75 20.0718 6.3865 20.75 7.33333 20.75H16.6667C17.6135 20.75 18.25 20.0718 18.25 19.3889V4.61111C18.25 3.92825 17.6135 3.25 16.6667 3.25H13.7073C13.735 3.32819 13.75 3.41234 13.75 3.5C13.75 3.91421 13.4142 4.25 13 4.25H11C10.5858 4.25 10.25 3.91421 10.25 3.5C10.25 3.41234 10.265 3.32819 10.2927 3.25H7.33333ZM4.25 4.61111C4.25 2.96211 5.70284 1.75 7.33333 1.75H16.6667C18.2972 1.75 19.75 2.96211 19.75 4.61111V19.3889C19.75 21.0379 18.2972 22.25 16.6667 22.25H7.33333C5.70284 22.25 4.25 21.0379 4.25 19.3889V4.61111Z"})))),Q=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M5 4.75C4.86193 4.75 4.75 4.86193 4.75 5V7C4.75 7.13807 4.86193 7.25 5 7.25H19C19.1381 7.25 19.25 7.13807 19.25 7V5C19.25 4.86193 19.1381 4.75 19 4.75H5ZM3.25 5C3.25 4.0335 4.0335 3.25 5 3.25H19C19.9665 3.25 20.75 4.0335 20.75 5V7C20.75 7.9665 19.9665 8.75 19 8.75H5C4.0335 8.75 3.25 7.9665 3.25 7V5ZM5 12.75C4.86193 12.75 4.75 12.8619 4.75 13V19C4.75 19.1381 4.86193 19.25 5 19.25H9C9.13807 19.25 9.25 19.1381 9.25 19V13C9.25 12.8619 9.13807 12.75 9 12.75H5ZM3.25 13C3.25 12.0335 4.0335 11.25 5 11.25H9C9.9665 11.25 10.75 12.0335 10.75 13V19C10.75 19.9665 9.9665 20.75 9 20.75H5C4.0335 20.75 3.25 19.9665 3.25 19V13ZM13.25 12C13.25 11.5858 13.5858 11.25 14 11.25H20C20.4142 11.25 20.75 11.5858 20.75 12C20.75 12.4142 20.4142 12.75 20 12.75H14C13.5858 12.75 13.25 12.4142 13.25 12ZM13.25 16C13.25 15.5858 13.5858 15.25 14 15.25H20C20.4142 15.25 20.75 15.5858 20.75 16C20.75 16.4142 20.4142 16.75 20 16.75H14C13.5858 16.75 13.25 16.4142 13.25 16ZM13.25 20C13.25 19.5858 13.5858 19.25 14 19.25H20C20.4142 19.25 20.75 19.5858 20.75 20C20.75 20.4142 20.4142 20.75 20 20.75H14C13.5858 20.75 13.25 20.4142 13.25 20Z"})))),Y=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M8 6.75C5.10051 6.75 2.75 9.10051 2.75 12C2.75 14.8995 5.10051 17.25 8 17.25H16C18.8995 17.25 21.25 14.8995 21.25 12C21.25 9.1005 18.8995 6.75 16 6.75H8ZM1.25 12C1.25 8.27208 4.27208 5.25 8 5.25H16C19.7279 5.25 22.75 8.27208 22.75 12C22.75 15.7279 19.7279 18.75 16 18.75H8C4.27208 18.75 1.25 15.7279 1.25 12ZM16 10.75C15.3096 10.75 14.75 11.3096 14.75 12C14.75 12.6904 15.3096 13.25 16 13.25C16.6904 13.25 17.25 12.6904 17.25 12C17.25 11.3096 16.6904 10.75 16 10.75ZM13.25 12C13.25 10.4812 14.4812 9.25 16 9.25C17.5188 9.25 18.75 10.4812 18.75 12C18.75 13.5188 17.5188 14.75 16 14.75C14.4812 14.75 13.25 13.5188 13.25 12Z"})))),$=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10 3.75C9.9337 3.75 9.87011 3.77634 9.82322 3.82322C9.77634 3.87011 9.75 3.9337 9.75 4V6.25H14.25V4C14.25 3.9337 14.2237 3.87011 14.1768 3.82322C14.1299 3.77634 14.0663 3.75 14 3.75H10ZM15.75 6.25V4C15.75 3.53587 15.5656 3.09075 15.2374 2.76256C14.9092 2.43437 14.4641 2.25 14 2.25H10C9.53587 2.25 9.09075 2.43437 8.76256 2.76256C8.43437 3.09075 8.25 3.53587 8.25 4V6.25H5.00877C5.00349 6.24994 4.9982 6.24994 4.9929 6.25H4C3.58579 6.25 3.25 6.58579 3.25 7C3.25 7.41421 3.58579 7.75 4 7.75H4.3099L5.25021 19.0337C5.25898 19.7508 5.54767 20.4368 6.05546 20.9445C6.57118 21.4603 7.27065 21.75 8 21.75H16C16.7293 21.75 17.4288 21.4603 17.9445 20.9445C18.4523 20.4368 18.741 19.7508 18.7498 19.0337L19.6901 7.75H20C20.4142 7.75 20.75 7.41421 20.75 7C20.75 6.58579 20.4142 6.25 20 6.25H19.0071C19.0018 6.24994 18.9965 6.24994 18.9912 6.25H15.75ZM5.8151 7.75L6.74741 18.9377C6.74914 18.9584 6.75 18.9792 6.75 19C6.75 19.3315 6.8817 19.6495 7.11612 19.8839C7.35054 20.1183 7.66848 20.25 8 20.25H16C16.3315 20.25 16.6495 20.1183 16.8839 19.8839C17.1183 19.6495 17.25 19.3315 17.25 19C17.25 18.9792 17.2509 18.9584 17.2526 18.9377L18.1849 7.75H5.8151ZM10 10.25C10.4142 10.25 10.75 10.5858 10.75 11V17C10.75 17.4142 10.4142 17.75 10 17.75C9.58579 17.75 9.25 17.4142 9.25 17V11C9.25 10.5858 9.58579 10.25 10 10.25ZM14 10.25C14.4142 10.25 14.75 10.5858 14.75 11V17C14.75 17.4142 14.4142 17.75 14 17.75C13.5858 17.75 13.25 17.4142 13.25 17V11C13.25 10.5858 13.5858 10.25 14 10.25Z"})))),CC=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 5.25C12.2508 5.25 12.485 5.37533 12.6241 5.58397L16.1703 10.9033L20.5315 7.41435C20.7777 7.21743 21.1207 7.19544 21.39 7.35933C21.6592 7.52321 21.7973 7.83798 21.7355 8.14709L19.7355 18.1471C19.6654 18.4977 19.3576 18.75 19 18.75H5.00004C4.64253 18.75 4.33472 18.4977 4.26461 18.1471L2.2646 8.14709C2.20278 7.83798 2.34084 7.52321 2.61012 7.35933C2.8794 7.19544 3.22241 7.21743 3.46856 7.41435L7.82977 10.9033L11.376 5.58397C11.5151 5.37533 11.7493 5.25 12 5.25ZM12 7.35208L8.62408 12.416C8.50748 12.5909 8.32282 12.7089 8.1151 12.7411C7.90738 12.7734 7.69566 12.717 7.53152 12.5857L4.13926 9.87185L5.61489 17.25H18.3852L19.8608 9.87185L16.4686 12.5857C16.3044 12.717 16.0927 12.7734 15.885 12.7411C15.6773 12.7089 15.4926 12.5909 15.376 12.416L12 7.35208Z"})))),eC=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3 5.25C2.86193 5.25 2.75 5.36193 2.75 5.5V15.5C2.75 15.6381 2.86193 15.75 3 15.75H21C21.1381 15.75 21.25 15.6381 21.25 15.5V5.5C21.25 5.36193 21.1381 5.25 21 5.25H3ZM1.25 5.5C1.25 4.5335 2.0335 3.75 3 3.75H21C21.9665 3.75 22.75 4.5335 22.75 5.5V15.5C22.75 16.4665 21.9665 17.25 21 17.25H3C2.0335 17.25 1.25 16.4665 1.25 15.5V5.5ZM6.25 19.5C6.25 19.0858 6.58579 18.75 7 18.75H17C17.4142 18.75 17.75 19.0858 17.75 19.5C17.75 19.9142 17.4142 20.25 17 20.25H7C6.58579 20.25 6.25 19.9142 6.25 19.5Z"})))),nC=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12.0004 2.01562C6.49444 2.01562 2.01562 6.49404 2.01562 11.9996C2.01562 17.5053 6.49444 21.9844 12.0004 21.9844C17.5056 21.9844 21.9844 17.5053 21.9844 11.9996C21.9844 6.49466 17.5056 2.01562 12.0004 2.01562ZM3.16156 11.9996C3.16156 10.7184 3.43668 9.5017 3.92703 8.40311L8.14311 19.9539C5.19483 18.5215 3.16156 15.4984 3.16156 11.9996ZM12.0004 20.8387C11.1327 20.8387 10.2954 20.7106 9.50324 20.4785L12.1549 12.7731L14.8725 20.2154C14.8898 20.2589 14.9115 20.2992 14.9353 20.3372C14.0167 20.6607 13.0292 20.8387 12.0004 20.8387ZM13.218 7.85596C13.7501 7.82787 14.2293 7.77149 14.2293 7.77149C14.7058 7.71531 14.65 7.01576 14.1733 7.04385C14.1733 7.04385 12.7415 7.156 11.8176 7.156C10.9495 7.156 9.4894 7.04385 9.4894 7.04385C9.0133 7.01576 8.95794 7.74402 9.43363 7.77149C9.43363 7.77149 9.88452 7.82767 10.3602 7.85596L11.7373 11.6286L9.80335 17.4297L6.58511 7.85638C7.1178 7.82829 7.59679 7.77211 7.59679 7.77211C8.07247 7.71593 8.01691 7.01596 7.53999 7.04446C7.53999 7.04446 6.10881 7.15641 5.18429 7.15641C5.01782 7.15641 4.82304 7.15207 4.61566 7.14567C6.19535 4.74588 8.9123 3.16171 12.0004 3.16171C14.3018 3.16171 16.3964 4.04157 17.9689 5.48157C17.9302 5.47971 17.8937 5.47476 17.854 5.47476C16.9861 5.47476 16.3695 6.2309 16.3695 7.04343C16.3695 7.77149 16.789 8.38801 17.2377 9.11586C17.5741 9.70512 17.9662 10.4613 17.9662 11.5537C17.9662 12.3102 17.6758 13.1882 17.2936 14.4107L16.4121 17.3566L13.218 7.85596ZM16.4435 19.6389L19.1431 11.8337C19.6481 10.573 19.8152 9.56469 19.8152 8.66789C19.8152 8.343 19.7937 8.04042 19.7557 7.75911C20.4466 9.01797 20.8391 10.4629 20.8386 11.9998C20.8386 15.2602 19.0708 18.1068 16.4435 19.6389Z"})))),tC=n.forwardRef(((C,e)=>n.createElement(t.SvgIcon,{viewBox:"0 0 24 24",...C,ref:e},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M18.5303 5.46967C18.8232 5.76256 18.8232 6.23744 18.5303 6.53033L6.53033 18.5303C6.23744 18.8232 5.76256 18.8232 5.46967 18.5303C5.17678 18.2374 5.17678 17.7626 5.46967 17.4697L17.4697 5.46967C17.7626 5.17678 18.2374 5.17678 18.5303 5.46967Z"}),n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M5.46967 5.46967C5.76256 5.17678 6.23744 5.17678 6.53033 5.46967L18.5303 17.4697C18.8232 17.7626 18.8232 18.2374 18.5303 18.5303C18.2374 18.8232 17.7626 18.8232 17.4697 18.5303L5.46967 6.53033C5.17678 6.23744 5.17678 5.76256 5.46967 5.46967Z"}))));(window.elementorV2=window.elementorV2||{}).icons=e}();