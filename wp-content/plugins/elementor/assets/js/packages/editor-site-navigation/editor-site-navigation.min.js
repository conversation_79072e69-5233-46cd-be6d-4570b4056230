!function(){"use strict";var e={d:function(t,n){for(var o in n)e.o(n,o)&&!e.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:n[o]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{extendIconsMap:function(){return y}});var n=window.elementorV2.icons,o=window.React,a=window.elementorV2.ui,r=window.elementorV2.editorDocuments,i=window.elementorV2.query,l=window.wp.apiFetch,s=window.wp.i18n,c=window.elementorV2.editorAppBar,u=window.elementorV2.editorPanels,m=window.elementorV2.editorV1Adapters,d=window.elementorV2.env,p={page:n.PageTemplateIcon,section:n.SectionTemplateIcon,container:n.ContainerTemplateIcon,"wp-page":n.PageTypeIcon,"wp-post":n.PostTypeIcon};function y(e){Object.assign(p,e)}function g({title:e,status:t}){return o.createElement(E,{title:e},o.createElement(a.Stack,{component:"span",direction:"row",alignItems:"center",spacing:.5},o.createElement(a.Typography,{component:"span",variant:"body2",sx:{maxWidth:"120px"},noWrap:!0},e),"publish"!==t.value&&o.createElement(a.Typography,{component:"span",variant:"body2",sx:{fontStyle:"italic"}},"(",t.label,")")))}function E(e){return o.createElement(a.Tooltip,{PopperProps:{sx:{"&.MuiTooltip-popper .MuiTooltip-tooltip.MuiTooltip-tooltipPlacementBottom":{mt:2.7}}},...e})}var f=["site-navigation","recent-posts"],h=p;function v({postType:e,docType:t,label:r}){const i="elementor_library"===e?"global":"primary",l=h[t]||n.PostTypeIcon;return o.createElement(a.Chip,{component:"span",size:"small",variant:"outlined",label:r,"data-value":t,color:i,icon:o.createElement(l,null),sx:{ml:1,cursor:"inherit"}})}function _(e=""){return(0,o.useMemo)((()=>{const t=document.createElement("textarea");t.innerHTML=e;const{value:n}=t;return t.remove(),n}),[e])}function P({post:e,closePopup:t,...n}){const i=(0,r.__useNavigateToDocument)(),l=_(e.title);return o.createElement(a.MenuItem,{disabled:!e.user_can.edit,onClick:async()=>{t(),await i(e.id)},...n},o.createElement(a.ListItemText,{sx:{flexGrow:0},primaryTypographyProps:{variant:"body2",noWrap:!0},primary:l}),o.createElement(v,{postType:e.type.post_type,docType:e.type.doc_type,label:e.type.label}))}var w="/elementor/v1/site-navigation/add-new-post";var x=()=>{const e="/wp/v2/users/me?"+new URLSearchParams({_fields:["capabilities"].join(","),context:"edit"}).toString();return l({path:e})},b=()=>["site-navigation","user"];function I(){return(0,i.useQuery)({queryKey:b(),queryFn:()=>x()})}function T({closePopup:e,...t}){const{create:i,isLoading:c}=function(){const[e,t]=(0,o.useState)(!1);return{create:()=>(t(!0),async function(){return await l({path:w,method:"POST",data:{post_type:"page"}})}().then((e=>e)).finally((()=>t(!1)))),isLoading:e}}(),u=(0,r.__useNavigateToDocument)(),{data:m}=I();return o.createElement(a.MenuItem,{disabled:c||!m?.capabilities?.edit_pages,onClick:async()=>{const{id:t}=await i();e(),await u(t)},...t},o.createElement(a.ListItemIcon,null,c?o.createElement(a.CircularProgress,{size:"1.25rem"}):o.createElement(n.PlusIcon,{fontSize:"small"})),o.createElement(a.ListItemText,{primaryTypographyProps:{variant:"body2"},primary:(0,s.__)("Add new page","elementor")}))}var S={page:{labels:{singular_name:(0,s.__)("Page","elementor"),plural_name:(0,s.__)("Pages","elementor")},rest_base:"pages"}},M=(e,t)=>{const n=`/wp/v2/${S[e].rest_base}`;return l({path:n,method:"POST",data:t})},C=(e,t)=>{const n=`/wp/v2/${S[e].rest_base}`,{id:o,...a}=t;return l({path:`${n}/${o}`,method:"POST",data:a})},D=(e,t)=>{const n=`/wp/v2/${S[e].rest_base}`;return l({path:`${n}/${t}`,method:"DELETE"})},k=e=>l({path:"/elementor/v1/site-navigation/duplicate-post",method:"POST",data:{post_id:e.id,title:e.title}}),L=e=>["site-navigation","posts",e],B=e=>{if(!e)return e;const t=[];return e.pages.forEach((e=>{t.push(...e.data)})),t},A={type:"page",editMode:{mode:"none",details:{}},setEditMode:()=>null,resetEditMode:()=>null,setError:()=>null},O=(0,o.createContext)(A),F=({type:e,setError:t,children:n})=>{const[a,r]=(0,o.useState)(A.editMode);return o.createElement(O.Provider,{value:{type:e,editMode:a,setEditMode:r,resetEditMode:()=>{r(A.editMode)},setError:t}},n)};function N(){const e=(0,o.useContext)(O);if(!e)throw new Error("The `usePostListContext()` hook must be used within an `<PostListContextProvider />`");return e}var z=(0,a.styled)(n.ChevronDownIcon,{shouldForwardProp:e=>"isOpen"!==e})((({theme:e,isOpen:t})=>({transform:t?"rotate(0deg)":"rotate(-90deg)",transition:e.transitions.create("transform",{duration:e.transitions.duration.standard})}))),j=(0,a.styled)(a.ListItemIcon)((({theme:e})=>({minWidth:e.spacing(4)})));function V({label:e,Icon:t,isOpenByDefault:n=!1,children:r}){const[i,l]=(0,o.useState)(n);return o.createElement(o.Fragment,null,o.createElement(a.ListItem,null,o.createElement(j,{sx:{color:"text.secondary"}},o.createElement(a.IconButton,{onClick:()=>l((e=>!e)),size:"small",sx:{color:"inherit"}},o.createElement(z,{fontSize:"small",isOpen:i}))),o.createElement(j,{size:"small",sx:{color:"inherit"}},o.createElement(t,{fontSize:"small"})),o.createElement(a.ListItemText,{primaryTypographyProps:{variant:"subtitle2",component:"span"},primary:e})),o.createElement(a.Collapse,{in:i,timeout:"auto",unmountOnExit:!0},o.createElement(a.List,{dense:!0},r)),o.createElement(a.Divider,{sx:{mt:1}}))}function q(e){const t=function(e){const t=(0,i.useQueryClient)();return(n={})=>{const o=L(e);return t.invalidateQueries({queryKey:f},n),t.invalidateQueries({queryKey:o},n)}}(e),n=()=>t({exact:!0});return{createPost:(0,i.useMutation)({mutationFn:t=>M(e,t),onSuccess:n}),updatePost:(0,i.useMutation)({mutationFn:t=>C(e,t),onSuccess:n}),deletePost:(0,i.useMutation)({mutationFn:t=>D(e,t),onSuccess:n}),duplicatePost:(0,i.useMutation)({mutationFn:e=>k(e),onSuccess:n})}}function $({postTitle:e,isLoading:t,callback:n}){const[r,i]=(0,o.useState)(e),[l,c]=(0,o.useState)(!1),[u,m]=(0,o.useState)(null),d=(0,o.useRef)(),p=e=>""!==e.trim(),y=()=>{p(r)&&n(r)};return o.createElement(o.Fragment,null,o.createElement(a.ListItem,{secondaryAction:o.createElement(H,{isLoading:t,closeButton:d})},o.createElement(a.Box,{width:"100%",component:"form",onSubmit:e=>{e.preventDefault(),y()}},o.createElement(a.TextField,{autoFocus:!0,fullWidth:!0,value:r,onChange:e=>{l||c(!0);const t=e.target.value;p(t)?m(null):m((0,s.__)("Name is required","elementor")),i(t)},disabled:t,error:!!u,onBlur:e=>{d.current!==e.relatedTarget&&y()},variant:"outlined",color:"secondary",size:"small"}))),u&&o.createElement(a.ListItem,null,o.createElement(a.ListItemText,{sx:{color:"error.main"}},u)))}function H({isLoading:e,closeButton:t}){const{resetEditMode:r}=N();return o.createElement(a.IconButton,{size:"small",color:"secondary",onClick:r,ref:t,disabled:e},e?o.createElement(a.CircularProgress,null):o.createElement(n.XIcon,{fontSize:"small"}))}function W({post:e}){const{type:t,resetEditMode:n}=N(),{updatePost:a}=q(t),{setError:i}=N(),l=(0,r.__useActiveDocument)(),s=async e=>{const t=function(){const e=window.elementor?.documents;if(!e)throw new Error("Elementor Editor V1 documents manager not found");return e}().getCurrent().container;await(0,m.__privateRunCommand)("document/elements/settings",{container:t,settings:{post_title:e}})},c=l?.id===e.id,u=c?l?.title:e.title.rendered;return o.createElement($,{postTitle:u,isLoading:a.isPending,callback:async t=>{t===u&&n();try{c?await s(t):await a.mutateAsync({id:e.id,title:t})}catch(e){i()}finally{n()}}})}function Q(){const{type:e,resetEditMode:t}=N(),{createPost:n}=q(e),a=(0,r.__useNavigateToDocument)(),{setError:i}=N();return o.createElement($,{postTitle:(0,s.__)("New Page","elementor"),isLoading:n.isPending,callback:async e=>{try{const{id:t}=await n.mutateAsync({title:e,status:"draft"});a(t)}catch(e){i()}finally{t()}}})}function R(){const{type:e,editMode:t,resetEditMode:n}=N(),a=(0,r.__useNavigateToDocument)(),{duplicatePost:i}=q(e),{setError:l}=N();return"duplicate"!==t.mode?null:o.createElement($,{postTitle:`${t.details.title} ${(0,s.__)("copy","elementor")}`,isLoading:i.isPending,callback:async e=>{try{const{post_id:n}=await i.mutateAsync({id:t.details.postId,title:e});a(n)}catch(e){l()}finally{n()}}})}var K=({status:e})=>"publish"===e?null:o.createElement(a.Typography,{component:"span",variant:"body2",color:"text.secondary",sx:{textTransform:"capitalize",fontStyle:"italic",whiteSpace:"nowrap",flexBasis:"content"}},"(",e,")"),U=({title:e})=>{const t=_(e);return o.createElement(a.Typography,{component:"span",variant:"body2",color:"text.secondary",noWrap:!0,sx:{flexBasis:"auto"}},t)};function G({title:e,status:t}){return o.createElement(a.Box,{display:"flex"},o.createElement(U,{title:e})," ",o.createElement(K,{status:t}))}function X({title:e,icon:t,MenuItemProps:n}){return o.createElement(a.MenuItem,{...n},o.createElement(a.ListItemIcon,{sx:{color:"inherit"}},o.createElement(t,null)),o.createElement(a.ListItemText,{primary:e}))}function Y({post:e}){const{setEditMode:t}=N();return o.createElement(X,{title:(0,s.__)("Rename","elementor"),icon:n.EraseIcon,MenuItemProps:{disabled:!e.user_can.edit,onClick:()=>{t({mode:"rename",details:{postId:e.id}})}}})}function J({post:e,popupState:t}){const{setEditMode:a}=N(),{data:r}=I(),i=!r?.capabilities?.edit_pages;return o.createElement(X,{title:(0,s.__)("Duplicate","elementor"),icon:n.CopyIcon,MenuItemProps:{disabled:i,onClick:()=>{t.close(),a({mode:"duplicate",details:{postId:e.id,title:e.title.rendered}})}}})}function Z({post:e}){const[t,a]=(0,o.useState)(!1),i=(0,r.__useActiveDocument)(),l=i?.id===e.id,c=!e.user_can.delete||e.isHome||l;return o.createElement(o.Fragment,null,o.createElement(X,{title:(0,s.__)("Delete","elementor"),icon:n.TrashIcon,MenuItemProps:{disabled:c,onClick:()=>a(!0),sx:{"&:hover":{color:"error.main"}}}}),t&&o.createElement(ee,{post:e,setIsDialogOpen:a}))}function ee({post:e,setIsDialogOpen:t}){const{type:n}=N(),{deletePost:r}=q(n),{setError:i}=N(),l=(0,s.sprintf)((0,s.__)('Delete "%s"?',"elementor"),e.title.rendered),c=()=>{r.isPending||t(!1)};return o.createElement(a.Dialog,{open:!0,onClose:c,"aria-labelledby":"delete-dialog"},o.createElement(a.DialogTitle,{noWrap:!0},l),o.createElement(a.Divider,null),o.createElement(a.DialogContent,null,o.createElement(a.DialogContentText,null,(0,s.__)("The page and its content will be deleted forever and we won’t be able to recover them.","elementor"))),o.createElement(a.DialogActions,null,o.createElement(a.Button,{variant:"contained",color:"secondary",onClick:c,disabled:r.isPending},(0,s.__)("Cancel","elementor")),o.createElement(a.Button,{variant:"contained",color:"error",onClick:async()=>{try{await r.mutateAsync(e.id)}catch(e){i(),t(!1)}},disabled:r.isPending},r.isPending?o.createElement(a.CircularProgress,null):(0,s.__)("Delete","elementor"))))}function te({post:e}){const{type:t}=N(),a=(0,s.__)("View %s","elementor").replace("%s",S[t].labels.singular_name);return o.createElement(X,{title:a,icon:n.EyeIcon,MenuItemProps:{onClick:()=>window.open(e.link,"_blank")}})}var ne=e=>l({path:"/wp/v2/settings",method:"POST",data:e}),oe=()=>["site-navigation","homepage"];function ae({post:e,closeMenu:t}){const{updateSettingsMutation:r}=function(){const e=function(){const e=(0,i.useQueryClient)();return(t={})=>{const n=oe();return e.invalidateQueries({queryKey:n},t)}}();return{updateSettingsMutation:(0,i.useMutation)({mutationFn:e=>ne(e),onSuccess:async()=>e({exact:!0})})}}(),{setError:l}=N(),{data:c}=I(),u=!!c?.capabilities?.manage_options,m="publish"===e.status,d=!!e.isHome,p=!u||d||!m||r.isPending;return o.createElement(X,{title:(0,s.__)("Set as homepage","elementor"),icon:r.isPending?a.CircularProgress:n.HomeIcon,MenuItemProps:{disabled:p,onClick:async()=>{try{await r.mutateAsync({show_on_front:"page",page_on_front:e.id})}catch(e){l()}finally{t()}}}})}var re=({children:e,isDisabled:t})=>{if(t){const t=o.createElement(a.Typography,{variant:"caption"},"You cannot edit this page.",o.createElement("br",null),"To edit it directly, contact the site owner");return o.createElement(a.Tooltip,{title:t,placement:"bottom",arrow:!1},e)}return o.createElement(o.Fragment,null,e)};function ie({post:e}){const t=(0,r.__useActiveDocument)(),i=(0,r.__useNavigateToDocument)(),l=(0,a.usePopupState)({variant:"popover",popupId:"post-actions",disableAutoFocus:!0}),c=t?.id===e.id,u=c?t?.status.value:e.status,m=c?t?.title:e.title.rendered,d=!e.user_can.edit;return o.createElement(o.Fragment,null,o.createElement(re,{isDisabled:d},o.createElement(a.ListItem,{disablePadding:!0,secondaryAction:o.createElement(a.IconButton,{value:!0,size:"small",...(0,a.bindTrigger)(l)},o.createElement(n.DotsVerticalIcon,{fontSize:"small"}))},o.createElement(a.ListItemButton,{selected:c,disabled:d,onClick:()=>{c||i(e.id)},dense:!0},o.createElement(a.ListItemText,{disableTypography:!0},o.createElement(G,{title:m,status:u})),e.isHome&&o.createElement(n.HomeIcon,{titleAccess:(0,s.__)("Homepage","elementor"),color:"disabled"})))),o.createElement(a.Menu,{PaperProps:{sx:{mt:2,width:200}},MenuListProps:{dense:!0},...(0,a.bindMenu)(l)},o.createElement(Y,{post:e}),o.createElement(J,{post:e,popupState:l}),o.createElement(Z,{post:e}),o.createElement(te,{post:e}),o.createElement(a.Divider,null),o.createElement(ae,{post:e,closeMenu:()=>l.close()})))}function le({post:e}){const{editMode:t}=N();return"rename"===t.mode&&e?.id&&e?.id===t.details.postId?o.createElement(W,{post:e}):"create"!==t.mode||e?"duplicate"!==t.mode||e?e?o.createElement(ie,{post:e}):null:o.createElement(R,null):o.createElement(Q,null)}function se(){const{setEditMode:e}=N(),{data:t}=I();return o.createElement(a.Button,{size:"small",startIcon:o.createElement(n.PlusIcon,null),disabled:!t?.capabilities?.edit_pages,onClick:()=>{e({mode:"create",details:{}})},sx:{px:1.5}},(0,s.__)("Add New","elementor"))}function ce(){return o.createElement(a.Box,{sx:{display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",pt:"40px",gap:"16px"}},o.createElement(n.Error404TemplateIcon,null),o.createElement(a.Box,{sx:{display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",gap:"8px"}},o.createElement(a.Typography,{variant:"body1",color:"text.primary"},(0,s.__)("We couldn’t display your pages.","elementor")),o.createElement(a.Box,null,o.createElement(a.Typography,{variant:"body2",color:"text.primary",sx:{textAlign:"center"}},(0,s.__)("It’s probably a temporary issue.","elementor")),o.createElement(a.Typography,{variant:"body2",color:"text.primary",sx:{textAlign:"center"}},(0,s.__)("If the problem persists,","elementor")," ",o.createElement(a.Link,{target:"_blank",href:"https://go.elementor.com/wp-editor-support-open-ticket/"},"Notify support")))))}function ue({isOpenByDefault:e=!1}){const{type:t,editMode:r}=N(),{data:{posts:s,total:c},isLoading:u,isError:m,fetchNextPage:d,hasNextPage:p,isFetchingNextPage:y}=function(e){const t=(0,i.useInfiniteQuery)({queryKey:L(e),queryFn:({pageParam:t=1})=>(async(e,t)=>{const n=`/wp/v2/${S[e].rest_base}?`+new URLSearchParams({status:"any",order:"asc",page:t.toString(),per_page:10..toString(),_fields:["id","type","title","link","status","user_can"].join(",")}).toString(),o=await l({path:n,parse:!1});return{data:await o.json(),totalPages:Number(o.headers.get("x-wp-totalpages")),totalPosts:Number(o.headers.get("x-wp-total")),currentPage:t}})(e,t),initialPageParam:1,getNextPageParam:e=>e.currentPage<e.totalPages?e.currentPage+1:void 0});return{...t,data:{posts:B(t.data),total:t.data?.pages[0]?.totalPosts??0}}}(t),{data:g}=(0,i.useQuery)({queryKey:oe(),queryFn:()=>l({path:"/elementor/v1/site-navigation/homepage"})});if(m)return o.createElement(ce,null);if(!s||u)return o.createElement(a.Box,{sx:{px:5}},o.createElement(a.Box,{display:"flex",justifyContent:"flex-end",alignItems:"center"},o.createElement(a.Skeleton,{sx:{my:4},animation:"wave",variant:"rounded",width:"110px",height:"28px"})),o.createElement(a.Box,null,o.createElement(a.Skeleton,{sx:{my:3},animation:"wave",variant:"rounded",width:"100%",height:"24px"}),o.createElement(a.Skeleton,{sx:{my:3},animation:"wave",variant:"rounded",width:"70%",height:"24px"}),o.createElement(a.Skeleton,{sx:{my:3},animation:"wave",variant:"rounded",width:"70%",height:"24px"}),o.createElement(a.Skeleton,{sx:{my:3},animation:"wave",variant:"rounded",width:"70%",height:"24px"})));const E=`${S[t].labels.plural_name} (${c.toString()})`,f=s.map((e=>e.id===g?{...e,isHome:!0}:e)).sort(((e,t)=>e.id===g?-1:t.id===g?1:0));return o.createElement(o.Fragment,null,o.createElement(a.Box,{display:"flex",justifyContent:"flex-end",alignItems:"center",sx:{py:1,px:2}},o.createElement(se,null)),o.createElement(a.List,{dense:!0},o.createElement(V,{label:E,Icon:n.PageTypeIcon,isOpenByDefault:e||!1},f.map((e=>o.createElement(le,{key:e.id,post:e}))),["duplicate","create"].includes(r.mode)&&o.createElement(le,null),p&&o.createElement(a.Box,{sx:{display:"flex",justifyContent:"center"}},o.createElement(a.Button,{onClick:d,color:"secondary"},y?o.createElement(a.CircularProgress,null):"Load More")))))}var me=({open:e,onClose:t})=>o.createElement(a.Snackbar,{open:e,onClose:t,anchorOrigin:{vertical:"bottom",horizontal:"left"}},o.createElement(a.Alert,{onClose:t,severity:"error",sx:{width:"100%"}},o.createElement(a.Typography,{component:"span",sx:{fontWeight:"bold"}},"We couldn’t complete the action.")," ","Please try again")),{panel:de,usePanelStatus:pe,usePanelActions:ye}=(0,u.__createPanel)({id:"site-navigation-panel",component:()=>{const[e,t]=o.useState(!1);return o.createElement(u.Panel,null,o.createElement(u.PanelHeader,null,o.createElement(u.PanelHeaderTitle,null,(0,s.__)("Pages","elementor"))),o.createElement(u.PanelBody,null,o.createElement(F,{type:"page",setError:()=>t(!0)},o.createElement(ue,{isOpenByDefault:!0})),o.createElement(me,{open:e,onClose:()=>t(!1)})))}});var{env:ge,validateEnv:Ee}=(0,d.parseEnv)("@elementor/editor-site-navigation",(e=>e));(0,c.injectIntoPageIndication)({id:"document-recently-edited",component:function(){const e=(0,r.__useActiveDocument)(),t=(0,r.__useHostDocument)(),c=e&&"kit"!==e.type.value?e:t,{data:u}=(0,i.useQuery)({queryKey:f,queryFn:()=>(()=>{const e=`/elementor/v1/site-navigation/recent-posts?${new URLSearchParams({posts_per_page:"6"}).toString()}`;return l({path:e})})()}),m=u?u.filter((e=>e.id!==c?.id)).splice(0,5):[],d=(0,a.usePopupState)({variant:"popover",popupId:"elementor-v2-top-bar-recently-edited"}),p=_(c?.title);if(!c)return null;const y=(0,a.bindTrigger)(d);return o.createElement(o.Fragment,null,o.createElement(a.Button,{color:"inherit",size:"small",endIcon:o.createElement(n.ChevronDownIcon,{fontSize:"small"}),...y,onClick:e=>{const t=window,n=t?.elementor?.editorEvents?.config;n&&t.elementor.editorEvents.dispatchEvent(n.names.topBar.documentNameDropdown,{location:n.locations.topBar,secondaryLocation:n.secondaryLocations.documentNameDropdown,trigger:n.triggers.dropdownClick,element:n.elements.dropdown}),y.onClick(e)}},o.createElement(g,{title:p,status:c.status})),o.createElement(a.Menu,{MenuListProps:{subheader:o.createElement(a.ListSubheader,{color:"primary",sx:{fontStyle:"italic",fontWeight:"300"}},(0,s.__)("Recent","elementor"))},PaperProps:{sx:{mt:2.5,width:320}},...(0,a.bindMenu)(d)},m.map((e=>o.createElement(P,{key:e.id,post:e,closePopup:d.close}))),0===m.length&&o.createElement(a.MenuItem,{disabled:!0},o.createElement(a.ListItemText,{primaryTypographyProps:{variant:"caption",fontStyle:"italic"},primary:(0,s.__)("There are no other pages or templates on this site yet.","elementor")})),o.createElement(a.Divider,{disabled:0===m.length}),o.createElement(T,{closePopup:d.close})))}}),ge.is_pages_panel_active&&((0,u.__registerPanel)(de),c.toolsMenu.registerToggleAction({id:"toggle-site-navigation-panel",priority:2,useProps:function(){const{isOpen:e,isBlocked:t}=pe(),{open:o,close:a}=ye();return{title:(0,s.__)("Pages","elementor"),icon:n.PagesIcon,onClick:()=>e?a():o(),selected:e,disabled:t}}})),(window.elementorV2=window.elementorV2||{}).editorSiteNavigation=t}();