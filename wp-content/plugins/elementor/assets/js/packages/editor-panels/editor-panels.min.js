!function(){"use strict";var e={d:function(n,t){for(var o in t)e.o(t,o)&&!e.o(n,o)&&Object.defineProperty(n,o,{enumerable:!0,get:t[o]})},o:function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},n={};e.r(n),e.d(n,{Panel:function(){return y},PanelBody:function(){return S},PanelHeader:function(){return b},PanelHeaderTitle:function(){return P},__createPanel:function(){return h},__registerPanel:function(){return w}});var t=window.elementorV2.editor,o=window.elementorV2.store,r=window.React,i=window.elementorV2.locations,a=window.elementorV2.ui,l=window.elementorV2.editorV1Adapters,{inject:c,useInjections:u}=(0,i.createLocation)(),s=e=>e.panels.openId,p=(0,o.__createSlice)({name:"panels",initialState:{openId:null},reducers:{open(e,n){e.openId=n.payload},close(e,n){n.payload&&e.openId!==n.payload||(e.openId=null)}}}),d="panel/v2";function m(){return document.querySelector("#elementor-panel-inner")}function _(){const e=["#elementor-panel-header-wrapper","#elementor-panel-content-wrapper","#elementor-panel-state-loading","#elementor-panel-footer"].join(", ");return document.querySelectorAll(e)}function f({on:e,when:n,callback:t}){let r;(0,o.__subscribe)((()=>{const i=e((0,o.__getState)());n({prev:r,current:i})&&t({prev:r,current:i}),r=i}))}function v(e){const n=(0,r.useRef)(m);return n.current?r.createElement(a.Portal,{container:n.current,...e}):null}function h({id:e,component:n}){const t=function(e){return()=>{const n=(0,o.__useSelector)(s),t=(0,l.__privateUseRouteStatus)(d,{blockOnKitRoutes:!0,blockOnPreviewMode:!0});return{isOpen:n===e&&t.isActive,isBlocked:t.isBlocked}}}(e),r=function(e,n){return()=>{const t=(0,o.__useDispatch)(),{isBlocked:r}=n();return{open:async()=>{r||t(p.actions.open(e))},close:async()=>{r||t(p.actions.close(e))}}}}(e,t);return{panel:{id:e,component:n},usePanelStatus:t,usePanelActions:r}}function w({id:e,component:n}){c({id:e,component:n})}function y({children:e,sx:n,...t}){return r.createElement(a.Drawer,{open:!0,variant:"persistent",anchor:"left",PaperProps:{sx:{position:"relative",width:"100%",bgcolor:"background.default",border:"none"}},sx:{height:"100%",...n},...t},e)}var g=(0,a.styled)(a.Box)((({theme:e})=>({height:e?.spacing(6)||"48px",display:"flex",alignItems:"center",justifyContent:"center"})));function b({children:e,...n}){return r.createElement(r.Fragment,null,r.createElement(g,{component:"header",...n},e),r.createElement(a.Divider,null))}var E=(0,a.styled)(a.Typography)((({theme:e,variant:n="body1"})=>"inherit"===n?{}:{"&.MuiTypography-root":{...e.typography[n]}}));function P({children:e,...n}){return r.createElement(E,{component:"h2",variant:"subtitle1",...n},e)}function S({children:e,sx:n,...t}){return r.createElement(a.Box,{component:"main",sx:{overflowY:"auto",height:"100%",...n},...t},e)}(0,l.__privateListenTo)((0,l.windowEvent)("elementor/panel/init"),(()=>(0,l.__privateRegisterRoute)(d))),(0,l.__privateListenTo)((0,l.routeOpenEvent)(d),(()=>{_().forEach((e=>{e.setAttribute("hidden","hidden"),e.setAttribute("aria-hidden","true")}))})),(0,l.__privateListenTo)((0,l.routeCloseEvent)(d),(()=>s((0,o.__getState)())&&(0,o.__dispatch)(p.actions.close()))),(0,l.__privateListenTo)((0,l.routeCloseEvent)(d),(()=>{_().forEach((e=>{e.removeAttribute("hidden"),e.removeAttribute("aria-hidden")}))})),(0,l.__privateListenTo)((0,l.windowEvent)("elementor/panel/init"),(()=>f({on:e=>s(e),when:({prev:e,current:n})=>!(e||!n),callback:()=>(0,l.__privateOpenRoute)(d)}))),(0,l.__privateListenTo)((0,l.windowEvent)("elementor/panel/init"),(()=>f({on:e=>s(e),when:({prev:e,current:n})=>!(n||!e),callback:()=>(0,l.__privateIsRouteActive)(d)&&(0,l.__privateOpenRoute)(function(){const e=window?.elementor?.documents?.getCurrent?.()?.config?.panel?.default_route;return e||"panel/elements/categories"}())}))),(0,o.__registerSlice)(p),(0,t.injectIntoTop)({id:"panels",component:function(){const e=function(){const e=u(),n=(0,o.__useSelector)(s);return(0,r.useMemo)((()=>e.find((e=>n===e.id))),[e,n])}(),n=e?.component??null;return n?r.createElement(v,null,r.createElement(n,null)):null}}),(window.elementorV2=window.elementorV2||{}).editorPanels=n}();