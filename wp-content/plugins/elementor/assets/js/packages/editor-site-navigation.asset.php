<?php
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}
/**
 * This file is generated by Webpack, do not edit it directly.
 */
return [
	'handle' => 'elementor-packages-editor-site-navigation',
	'src' => plugins_url( '/', __FILE__ ) . 'editor-site-navigation{{MIN_SUFFIX}}.js',
	'i18n' => [
		'domain' => 'elementor',
		'replace_requested_file' => false,
	],
	'type' => 'extension',
	'deps' => [
		'elementor-packages-editor-app-bar',
		'elementor-packages-editor-documents',
		'elementor-packages-icons',
		'elementor-packages-ui',
		'react',
		'wp-api-fetch',
		'wp-i18n',
		'wp-url',
	],
];
