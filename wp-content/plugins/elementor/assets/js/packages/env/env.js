/*! For license information please see env.js.LICENSE.txt */
!function(){"use strict";var e={d:function(n,t){for(var r in t)e.o(t,r)&&!e.o(n,r)&&Object.defineProperty(n,r,{enumerable:!0,get:t[r]})},o:function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},n={};e.r(n),e.d(n,{InvalidEnvError:function(){return u},__resetEnv:function(){return o},initEnv:function(){return r},parseEnv:function(){return i}});var t=null;function r(e){t=e}function o(){t=null}function i(e,n=(e=>e)){let r={},o=!1;const i=new Proxy(r,{get(e,n){return o||c(),r[n]},ownKeys(){return o||c(),Reflect.ownKeys(r)},getOwnPropertyDescriptor(){return{configurable:!0,enumerable:!0}}}),c=()=>{try{const o=t?.[e];if(!o)throw new u("Settings object not found");if("object"!=typeof o)throw new u(`Expected settings to be \`object\`, but got \`${typeof o}\``);r=n(o)}catch(n){if(!(n instanceof u))throw n;console.warn(`${e} - ${n.message}`),r={}}finally{o=!0}};return{validateEnv:c,env:i}}var u=class extends Error{};(window.elementorV2=window.elementorV2||{}).env=n}();