/*! elementor - v3.14.0 - 26-06-2023 */
"use strict";(self.webpackChunkelementor=self.webpackChunkelementor||[]).push([[343],{87206:(e,t,n)=>{var o=n(23615),r=n(38003).__,a=n(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=ElementorLoading;var i=a(n(87363));function ElementorLoading(e){return i.default.createElement("div",{className:"elementor-loading"},i.default.createElement("div",{className:"elementor-loader-wrapper"},i.default.createElement("div",{className:"elementor-loader"},i.default.createElement("div",{className:"elementor-loader-boxes"},i.default.createElement("div",{className:"elementor-loader-box"}),i.default.createElement("div",{className:"elementor-loader-box"}),i.default.createElement("div",{className:"elementor-loader-box"}),i.default.createElement("div",{className:"elementor-loader-box"}))),i.default.createElement("div",{className:"elementor-loading-title"},e.loadingText)))}ElementorLoading.propTypes={loadingText:o.string},ElementorLoading.defaultProps={loadingText:r("Loading","elementor")}},15368:(e,t,n)=>{var o=n(23615),r=n(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.default=PopoverDialog;var a=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==r(e)&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var o={},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var l=a?Object.getOwnPropertyDescriptor(e,i):null;l&&(l.get||l.set)?Object.defineProperty(o,i,l):o[i]=e[i]}o.default=e,n&&n.set(e,o);return o}(n(87363));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?n:t})(e)}function PopoverDialog(e){var t=e.targetRef,n=e.offsetTop,o=e.offsetLeft,r=e.wrapperClass,i=e.trigger,l=e.hideAfter,c=(0,a.useCallback)((function(e){var r=null==t?void 0:t.current;if(r&&e){var a=function showPopover(){e.style.display="block",e.setAttribute("aria-expanded",!0);var t=r.getBoundingClientRect(),a=e.getBoundingClientRect(),i=a.width-t.width;e.style.top=t.bottom+n+"px",e.style.left=t.left-i/2-o+"px",e.style.setProperty("--popover-arrow-offset-end",(a.width-16)/2+"px")},c=function hidePopover(){e.style.display="none",e.setAttribute("aria-expanded",!1)};"hover"===i?function handlePopoverHover(){var t=!0,n=null;r.addEventListener("mouseover",(function(){t=!0,a()})),r.addEventListener("mouseleave",(function(){n=setTimeout((function(){t&&"block"===e.style.display&&c()}),l)})),e.addEventListener("mouseover",(function(){t=!1,n&&(clearTimeout(n),n=null)})),e.addEventListener("mouseleave",(function(){n=setTimeout((function(){t&&"block"===e.style.display&&c()}),l),t=!0}))}():"click"===i&&function handlePopoverClick(){var t=!1;r.addEventListener("click",(function(e){e.preventDefault(),e.stopPropagation(),t?(c(),t=!1):(a(),t=!0)})),e.addEventListener("click",(function(e){e.stopPropagation()})),document.body.addEventListener("click",(function(){t&&(c(),t=!1)}))}()}}),[t]),u="e-app__popover";return r&&(u+=" "+r),a.default.createElement("div",{className:u,ref:c},e.children)}PopoverDialog.propTypes={targetRef:o.oneOfType([o.func,o.shape({current:o.any})]).isRequired,trigger:o.string,direction:o.string,offsetTop:o.oneOfType([o.string,o.number]),offsetLeft:o.oneOfType([o.string,o.number]),wrapperClass:o.string,children:o.any,hideAfter:o.number},PopoverDialog.defaultProps={direction:"bottom",trigger:"hover",offsetTop:10,offsetLeft:0,hideAfter:300}},60458:(e,t,n)=>{var o=n(73203),r=n(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function App(){return(0,a.useEffect)((function(){var e="eps-theme-dark",t=document.body.classList.contains(e);if(t&&document.body.classList.remove(e),!elementorAppConfig.onboarding.onboardingAlreadyRan){var n=new FormData;n.append("_nonce",elementorCommon.config.ajax.nonce),n.append("action","elementor_update_onboarding_option"),fetch(elementorCommon.config.ajax.url,{method:"POST",body:n})}return elementorAppConfig.return_url=elementorAppConfig.admin_url,function(){t&&document.body.classList.add(e)}}),[]),a.default.createElement(c.ContextProvider,null,a.default.createElement(i.LocationProvider,{history:l.default.appHistory},a.default.createElement(i.Router,null,a.default.createElement(u.default,{default:!0}),a.default.createElement(s.default,{path:"hello"}),a.default.createElement(d.default,{path:"siteName"}),a.default.createElement(p.default,{path:"siteLogo"}),a.default.createElement(f.default,{path:"goodToGo"}),a.default.createElement(m.default,{path:"uploadAndInstallPro"}))))};var a=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==r(e)&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var o={},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var l=a?Object.getOwnPropertyDescriptor(e,i):null;l&&(l.get||l.set)?Object.defineProperty(o,i,l):o[i]=e[i]}o.default=e,n&&n.set(e,o);return o}(n(87363)),i=n(50927),l=o(n(3869)),c=n(33959),u=o(n(1103)),s=o(n(92728)),d=o(n(78270)),p=o(n(90013)),f=o(n(79914)),m=o(n(3902));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?n:t})(e)}},36608:(e,t,n)=>{var o=n(23615),r=n(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=Button;var a=r(n(87363));function Button(e){var t=e.buttonSettings,n=e.type,o="e-onboarding__button";return n&&(o+=" e-onboarding__button-".concat(n)),t.className?t.className+=" "+o:t.className=o,t.href?a.default.createElement("a",t,t.text):a.default.createElement("div",t,t.text)}Button.propTypes={buttonSettings:o.object.isRequired,type:o.string}},32389:(e,t,n)=>{var o=n(23615),r=n(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=Card;var a=r(n(87363));function Card(e){var t=e.image,n=e.imageAlt,o=e.text,r=e.link,i=e.name,l=e.clickAction;return a.default.createElement("a",{className:"e-onboarding__card",href:r,onClick:function onClick(){elementorCommon.events.dispatchEvent({event:"starting canvas click",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,selection:i}}),l&&l()}},a.default.createElement("img",{className:"e-onboarding__card-image",src:t,alt:n}),a.default.createElement("div",{className:"e-onboarding__card-text"},o))}Card.propTypes={image:o.string.isRequired,imageAlt:o.string.isRequired,text:o.string.isRequired,link:o.string.isRequired,name:o.string.isRequired,clickAction:o.func}},74233:(e,t,n)=>{var o=n(23615),r=n(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=ChecklistItem;var a=r(n(87363));function ChecklistItem(e){return a.default.createElement("li",{className:"e-onboarding__checklist-item"},a.default.createElement("i",{className:"eicon-check-circle"}),e.children)}ChecklistItem.propTypes={children:o.string}},51224:(e,t,n)=>{var o=n(23615),r=n(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=Checklist;var a=r(n(87363));function Checklist(e){return a.default.createElement("ul",{className:"e-onboarding__checklist"},e.children)}Checklist.propTypes={children:o.any.isRequired}},47707:(e,t,n)=>{var o=n(38003).__,r=n(23615),a=n(73203),i=n(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.default=GoProPopover;var l=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==i(e)&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var o={},r=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var l=r?Object.getOwnPropertyDescriptor(e,a):null;l&&(l.get||l.set)?Object.defineProperty(o,a,l):o[a]=e[a]}o.default=e,n&&n.set(e,o);return o}(n(87363)),c=n(33959),u=a(n(15368)),s=a(n(51224)),d=a(n(74233)),p=a(n(36608));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?n:t})(e)}function GoProPopover(e){var t=(0,l.useContext)(c.OnboardingContext),n=t.state,r=t.updateState,a=(0,l.useCallback)((function(e){e&&e.addEventListener("click",(function(t){t.preventDefault(),elementorCommon.events.dispatchEvent({event:"already have pro",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:n.currentStep}}),window.open(e.href+"&mode=popup","elementorUploadPro","toolbar=no, menubar=no, width=728, height=531, top=100, left=100"),elementorCommon.elements.$body.on("elementor/upload-and-install-pro/success",(function(){r({hasPro:!0,proNotice:{type:"success",icon:"eicon-check-circle-o",message:o("Elementor Pro has been successfully installed.","elementor")}})}))}))}),[]),i=e.buttonsConfig.find((function(e){return"go-pro"===e.id})),f={text:o("Upgrade Now","elementor"),className:"e-onboarding__go-pro-cta",target:"_blank",href:"https://elementor.com/pro/?utm_source=onboarding-wizard&utm_campaign=gopro&utm_medium=wp-dash&utm_content=top-bar-dropdown&utm_term="+elementorAppConfig.onboarding.onboardingVersion,tabIndex:0,onClick:function onClick(){elementorCommon.events.dispatchEvent({event:"get elementor pro",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:n.currentStep}})}};return l.default.createElement(u.default,{targetRef:i.elRef,wrapperClass:"e-onboarding__go-pro"},l.default.createElement("div",{className:"e-onboarding__go-pro-content"},l.default.createElement("h2",{className:"e-onboarding__go-pro-title"},o("Ready to Get Elementor Pro?","elementor")),l.default.createElement(s.default,null,l.default.createElement(d.default,null,o("90+ Basic & Pro widgets","elementor")),l.default.createElement(d.default,null,o("300+ Basic & Pro templates","elementor")),l.default.createElement(d.default,null,o("Premium Support","elementor"))),l.default.createElement("div",{className:"e-onboarding__go-pro-paragraph"},o("And so much more!","elementor")),l.default.createElement("div",{className:"e-onboarding__go-pro-paragraph"},l.default.createElement(p.default,{buttonSettings:f})),l.default.createElement("div",{className:"e-onboarding__go-pro-paragraph"},l.default.createElement("a",{tabIndex:"0",className:"e-onboarding__go-pro-already-have",ref:a,href:elementorAppConfig.onboarding.urls.uploadPro,rel:"opener"},o("Already have Elementor Pro?","elementor")))))}GoProPopover.propTypes={buttonsConfig:r.array.isRequired}},3e4:(e,t,n)=>{var o=n(23615),r=n(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=FooterButtons;var a=r(n(87363)),i=r(n(67096)),l=r(n(36608)),c=r(n(63878));function FooterButtons(e){var t=e.actionButton,n=e.skipButton,o=e.className,r="e-onboarding__footer";return o&&(r+=" "+o),a.default.createElement(i.default,{container:!0,alignItems:"center",justify:"space-between",className:r},t&&a.default.createElement(l.default,{buttonSettings:t,type:"action"}),n&&a.default.createElement(c.default,{button:n}))}FooterButtons.propTypes={actionButton:o.object,skipButton:o.object,className:o.string}},55020:(e,t,n)=>{var o=n(38003).__,r=n(23615),a=n(73203),i=n(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.default=Header;var l=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==i(e)&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var o={},r=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var l=r?Object.getOwnPropertyDescriptor(e,a):null;l&&(l.get||l.set)?Object.defineProperty(o,a,l):o[a]=e[a]}o.default=e,n&&n.set(e,o);return o}(n(87363)),c=n(33959),u=a(n(67096)),s=a(n(47707)),d=a(n(78419)),p=a(n(78845));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?n:t})(e)}function Header(e){(0,p.default)({title:e.title});var t=(0,l.useContext)(c.OnboardingContext).state;return l.default.createElement(u.default,{container:!0,alignItems:"center",justify:"space-between",className:"eps-app__header e-onboarding__header"},l.default.createElement("div",{className:"eps-app__logo-title-wrapper e-onboarding__header-logo"},l.default.createElement("i",{className:"eps-app__logo eicon-elementor"}),l.default.createElement("img",{src:elementorCommon.config.urls.assets+"images/logo-platform.svg",alt:o("Elementor Logo","elementor")})),l.default.createElement(d.default,{buttons:e.buttons,onClose:function onClose(){elementorCommon.events.dispatchEvent({event:"close modal",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:t.currentStep}}),window.top.location=elementorAppConfig.admin_url}}),!t.hasPro&&l.default.createElement(s.default,{buttonsConfig:e.buttons}))}Header.propTypes={title:r.string,buttons:r.arrayOf(r.object)},Header.defaultProps={buttons:[]}},8915:(e,t,n)=>{var o=n(38003).__,r=n(23615),a=n(73203),i=n(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.default=Layout;var l=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==i(e)&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var o={},r=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var l=r?Object.getOwnPropertyDescriptor(e,a):null;l&&(l.get||l.set)?Object.defineProperty(o,a,l):o[a]=e[a]}o.default=e,n&&n.set(e,o);return o}(n(87363)),c=n(33959),u=a(n(55020)),s=a(n(4069)),d=a(n(88138)),p=a(n(94170));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?n:t})(e)}function Layout(e){(0,l.useEffect)((function(){elementorCommon.events.dispatchEvent({event:"modal load",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:e.pageId,user_state:elementorCommon.config.library_connect.is_connected?"logged":"anon"}}),r({currentStep:e.pageId,nextStep:e.nextStep||"",proNotice:null})}),[e.pageId]);var t=(0,l.useContext)(c.OnboardingContext),n=t.state,r=t.updateState,a=[],i=(0,l.useRef)(),f={id:"create-account",text:o("Create Account","elementor-pro"),hideText:!1,elRef:(0,l.useRef)(),url:elementorAppConfig.onboarding.urls.signUp+elementorAppConfig.onboarding.utms.connectTopBar,target:"_blank",rel:"opener",onClick:function onClick(){elementorCommon.events.dispatchEvent({event:"create account",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:n.currentStep,source:"header"}})}};return n.isLibraryConnected?a.push({id:"my-elementor",text:o("My Elementor","elementor-pro"),hideText:!1,icon:"eicon-user-circle-o",url:"https://my.elementor.com/websites/?utm_source=onboarding-wizard&utm_medium=wp-dash&utm_campaign=my-account&utm_content=top-bar&utm_term="+elementorAppConfig.onboarding.onboardingVersion,target:"_blank",onClick:function onClick(){elementorCommon.events.dispatchEvent({event:"my elementor click",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:n.currentStep,source:"header"}})}}):a.push(f),n.hasPro||a.push({id:"go-pro",text:o("Upgrade","elementor"),hideText:!1,className:"eps-button__go-pro-btn",url:"https://elementor.com/pro/?utm_source=onboarding-wizard&utm_campaign=gopro&utm_medium=wp-dash&utm_content=top-bar&utm_term="+elementorAppConfig.onboarding.onboardingVersion,target:"_blank",elRef:i,onClick:function onClick(){elementorCommon.events.dispatchEvent({event:"go pro",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:n.currentStep}})}}),l.default.createElement("div",{className:"eps-app__lightbox"},l.default.createElement("div",{className:"eps-app e-onboarding"},!n.isLibraryConnected&&l.default.createElement(p.default,{buttonRef:f.elRef}),l.default.createElement(u.default,{title:o("Getting Started","elementor"),buttons:a}),l.default.createElement("div",{className:"eps-app__main e-onboarding__page-"+e.pageId},l.default.createElement(d.default,{className:"e-onboarding__content"},l.default.createElement(s.default,null),e.children))))}Layout.propTypes={pageId:r.string.isRequired,nextStep:r.string,className:r.string,children:r.any.isRequired}},61961:(e,t,n)=>{var o=n(23615),r=n(73203),a=n(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.default=PageContentLayout;var i=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==a(e)&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var o={},r=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var l=r?Object.getOwnPropertyDescriptor(e,i):null;l&&(l.get||l.set)?Object.defineProperty(o,i,l):o[i]=e[i]}o.default=e,n&&n.set(e,o);return o}(n(87363)),l=n(33959),c=r(n(67096)),u=r(n(54041)),s=r(n(3e4));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?n:t})(e)}function PageContentLayout(e){var t=(0,i.useContext)(l.OnboardingContext).state;return i.default.createElement(i.default.Fragment,null,i.default.createElement(c.default,{container:!0,alignItems:"center",justify:"space-between",className:"e-onboarding__page-content"},i.default.createElement("div",{className:"e-onboarding__page-content-start"},i.default.createElement("h1",{className:"e-onboarding__page-content-section-title"},e.title),i.default.createElement("div",{className:"e-onboarding__page-content-section-text"},e.children)),i.default.createElement("div",{className:"e-onboarding__page-content-end"},i.default.createElement("img",{src:e.image,alt:"Information"}))),i.default.createElement("div",{className:"e-onboarding__notice-container"},e.noticeState||t.proNotice?function printNotices(){return i.default.createElement(i.default.Fragment,null,e.noticeState&&i.default.createElement(u.default,{noticeState:e.noticeState}),t.proNotice&&i.default.createElement(u.default,{noticeState:t.proNotice}))}():i.default.createElement("div",{className:"e-onboarding__notice-empty-spacer"})),i.default.createElement(s.default,{actionButton:e.actionButton,skipButton:e.skipButton}))}PageContentLayout.propTypes={title:o.string,children:o.any,image:o.string,actionButton:o.object,skipButton:o.object,noticeState:o.any}},54041:(e,t,n)=>{var o=n(23615),r=n(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=Notice;var a=r(n(87363));function Notice(e){return a.default.createElement("div",{className:"e-onboarding__notice e-onboarding__notice--".concat(e.noticeState.type)},a.default.createElement("i",{className:e.noticeState.icon}),a.default.createElement("span",{className:"e-onboarding__notice-text"},e.noticeState.message))}Notice.propTypes={noticeState:o.object}},18662:(e,t,n)=>{var o=n(23615),r=n(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.default=ProgressBarItem;var a=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==r(e)&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var o={},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var l=a?Object.getOwnPropertyDescriptor(e,i):null;l&&(l.get||l.set)?Object.defineProperty(o,i,l):o[i]=e[i]}o.default=e,n&&n.set(e,o);return o}(n(87363)),i=n(33959);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?n:t})(e)}function ProgressBarItem(e){var t=(0,a.useContext)(i.OnboardingContext).state,n="completed"===t.steps[e.id],o="skipped"===t.steps[e.id],r="e-onboarding__progress-bar-item";return e.id===t.currentStep?r+=" e-onboarding__progress-bar-item--active":n?r+=" e-onboarding__progress-bar-item--completed":o&&(r+=" e-onboarding__progress-bar-item--skipped"),a.default.createElement("div",{onClick:e.onClick,className:r},a.default.createElement("div",{className:"e-onboarding__progress-bar-item-icon"},n?a.default.createElement("i",{className:"eicon-check"}):e.index+1),e.title)}ProgressBarItem.propTypes={index:o.number.isRequired,id:o.string.isRequired,title:o.string.isRequired,route:o.string,onClick:o.func}},4069:(e,t,n)=>{var o=n(38003).__,r=n(73203),a=n(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function ProgressBar(){var e=(0,i.useContext)(c.OnboardingContext).state,t=(0,u.useNavigate)(),n=[{id:"account",title:o("Elementor Account","elementor"),route:"account"}];elementorAppConfig.onboarding.helloActivated||n.push({id:"hello",title:o("Hello Theme","elementor"),route:"hello"});n.push({id:"siteName",title:o("Site Name","elementor"),route:"site-name"},{id:"siteLogo",title:o("Site Logo","elementor"),route:"site-logo"},{id:"goodToGo",title:o("Good to Go","elementor"),route:"good-to-go"});var r=n.map((function(n,o){return n.index=o,e.steps[n.id]&&(n.onClick=function(){elementorCommon.events.dispatchEvent({event:"step click",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:e.currentStep,next_step:n.id}}),t("/onboarding/"+n.id)}),i.default.createElement(s.default,(0,l.default)({key:n.id},n))}));return i.default.createElement("div",{className:"e-onboarding__progress-bar"},r)};var i=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==a(e)&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var o={},r=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var l=r?Object.getOwnPropertyDescriptor(e,i):null;l&&(l.get||l.set)?Object.defineProperty(o,i,l):o[i]=e[i]}o.default=e,n&&n.set(e,o);return o}(n(87363)),l=r(n(73119)),c=n(33959),u=n(50927),s=r(n(18662));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?n:t})(e)}},63878:(e,t,n)=>{var o=n(23615),r=n(73203),a=n(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.default=SkipButton;var i=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==a(e)&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var o={},r=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var l=r?Object.getOwnPropertyDescriptor(e,i):null;l&&(l.get||l.set)?Object.defineProperty(o,i,l):o[i]=e[i]}o.default=e,n&&n.set(e,o);return o}(n(87363)),l=n(33959),c=n(50927),u=r(n(36608));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?n:t})(e)}function SkipButton(e){var t=e.button,n=e.className,o=(0,i.useContext)(l.OnboardingContext),r=o.state,a=o.updateState,s=(0,c.useNavigate)(),d=t.action||function skipStep(){var e=JSON.parse(JSON.stringify(r));e.steps[r.currentStep]="skipped",a(e),r.nextStep&&s("onboarding/"+r.nextStep)};return delete t.action,t.onClick=function(){elementorCommon.events.dispatchEvent({event:"skip",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:r.currentStep}}),t.href||d()},i.default.createElement(u.default,{buttonSettings:t,className:n,type:"skip"})}SkipButton.propTypes={button:o.object.isRequired,className:o.string}},33959:(e,t,n)=>{var o=n(23615),r=n(73203),a=n(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.ContextProvider=ContextProvider,t.OnboardingContext=void 0;var i=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==a(e)&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var o={},r=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var l=r?Object.getOwnPropertyDescriptor(e,i):null;l&&(l.get||l.set)?Object.defineProperty(o,i,l):o[i]=e[i]}o.default=e,n&&n.set(e,o);return o}(n(87363)),l=r(n(93231)),c=r(n(40131));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?n:t})(e)}function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach((function(t){(0,l.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var u=(0,i.createContext)({});function ContextProvider(e){var t=elementorAppConfig.onboarding,n={hasPro:elementorAppConfig.hasPro,isLibraryConnected:t.isLibraryConnected,isHelloThemeInstalled:t.helloInstalled,isHelloThemeActivated:t.helloActivated,siteName:t.siteName,siteLogo:t.siteLogo,proNotice:"",currentStep:"",nextStep:"",steps:{account:!1,hello:!1,siteName:!1,siteLogo:!1,goodToGo:!1}},o=(0,i.useState)(n),r=(0,c.default)(o,2),a=r[0],l=r[1],s=(0,i.useCallback)((function(e){l((function(t){return _objectSpread(_objectSpread({},t),e)}))}),[l]);return i.default.createElement(u.Provider,{value:{state:a,setState:l,updateState:s,getStateObjectToUpdate:function getStateObjectToUpdate(e,t,n,o){var r=JSON.parse(JSON.stringify(e));return r[t][n]=o,r}}},e.children)}t.OnboardingContext=u,ContextProvider.propTypes={children:o.any}},1103:(e,t,n)=>{var o=n(38003).__,r=n(73203),a=n(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function Account(){var e,t=(0,i.useContext)(u.OnboardingContext),n=t.state,r=t.updateState,a=t.getStateObjectToUpdate,f=(0,i.useState)(null),m=(0,l.default)(f,2),g=m[0],v=m[1],b=(0,c.useNavigate)(),_="account",h=n.isHelloThemeActivated?"siteName":"hello",y=(0,i.useRef)(),C=(0,i.useRef)();"completed"!==n.steps[_]&&(e={text:o("Skip","elementor")});var E={};E=n.isLibraryConnected?{firstLine:o("To get the most out of Elementor, we'll help you take your first steps:","elementor"),listItems:[o("Set your site's theme","elementor"),o("Give your site a name & logo","elementor"),o("Choose how to start creating","elementor")]}:{firstLine:o("To get the most out of Elementor, we’ll connect your account.","elementor")+" "+o("Then you can:","elementor"),listItems:[o("Choose from countless professional templates","elementor"),o("Manage your site with our handy dashboard","elementor"),o("Take part in the community forum, share & grow together","elementor")]};var O={role:"button"};n.isLibraryConnected?(O.text=o("Let’s do it","elementor"),O.onClick=function(){elementorCommon.events.dispatchEvent({event:"next",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:n.currentStep}}),r(a(n,"steps",_,"completed")),b("onboarding/"+h)}):(O.text=o("Create my account","elementor"),O.href=elementorAppConfig.onboarding.urls.signUp+elementorAppConfig.onboarding.utms.connectCta,O.ref=y,O.onClick=function(){elementorCommon.events.dispatchEvent({event:"create account",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,source:"cta"}})});var P=function connectSuccessCallback(e){var t=a(n,"steps",_,"completed");t.isLibraryConnected=!0,elementorCommon.config.library_connect.is_connected=!0,elementorCommon.config.library_connect.current_access_level=e.kits_access_level||e.access_level||0,r(t),elementorCommon.events.dispatchEvent({event:"indication prompt",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:n.currentStep,action_state:"success",action:"connect account"}}),v({type:"success",icon:"eicon-check-circle-o",message:"Alrighty - your account is connected."}),b("onboarding/"+h)},k=function connectFailureCallback(){elementorCommon.events.dispatchEvent({event:"indication prompt",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:n.currentStep,action_state:"failure",action:"connect account"}}),v({type:"error",icon:"eicon-warning",message:o("Oops, the connection failed. Try again.","elementor")}),b("onboarding/"+h)};return i.default.createElement(d.default,{pageId:_,nextStep:h},i.default.createElement(p.default,{image:elementorCommon.config.urls.assets+"images/app/onboarding/Illustration_Account.svg",title:o("You're here! Let's set things up.","elementor"),actionButton:O,skipButton:e,noticeState:g},O.ref&&!n.isLibraryConnected&&i.default.createElement(s.default,{buttonRef:O.ref,successCallback:function successCallback(e){return P(e)},errorCallback:k}),i.default.createElement("span",null,E.firstLine),i.default.createElement("ul",null,E.listItems.map((function(e,t){return i.default.createElement("li",{key:"listItem"+t},e)})))),!n.isLibraryConnected&&i.default.createElement("div",{className:"e-onboarding__footnote"},i.default.createElement("p",null,o("Already have one?","elementor")+" ",i.default.createElement("a",{ref:C,href:elementorAppConfig.onboarding.urls.connect+elementorAppConfig.onboarding.utms.connectCtaLink,onClick:function onClick(){elementorCommon.events.dispatchEvent({event:"connect account",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement}})}},o("Connect your account","elementor"))),i.default.createElement(s.default,{buttonRef:C,successCallback:P,errorCallback:k})))};var i=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==a(e)&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var o={},r=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var l=r?Object.getOwnPropertyDescriptor(e,i):null;l&&(l.get||l.set)?Object.defineProperty(o,i,l):o[i]=e[i]}o.default=e,n&&n.set(e,o);return o}(n(87363)),l=r(n(40131)),c=n(50927),u=n(33959),s=r(n(94170)),d=r(n(8915)),p=r(n(61961));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?n:t})(e)}},79914:(e,t,n)=>{var o=n(38003).__,r=n(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function GoodToGo(){var e={text:o("Skip","elementor"),href:elementorAppConfig.onboarding.urls.createNewPage},t=elementorAppConfig.onboarding.urls.kitLibrary+"&referrer=onboarding";return a.default.createElement(l.default,{pageId:"goodToGo"},a.default.createElement("h1",{className:"e-onboarding__page-content-section-title"},o("That's a wrap! What's next?","elementor")),a.default.createElement("div",{className:"e-onboarding__page-content-section-text"},o("There are two ways to get started with Elementor:","elementor")),a.default.createElement(i.default,{container:!0,alignItems:"center",justify:"space-between",className:"e-onboarding__cards-grid e-onboarding__page-content"},a.default.createElement(c.default,{name:"blank",image:elementorCommon.config.urls.assets+"images/app/onboarding/Blank_Canvas.svg",imageAlt:o("Click here to create a new page and open it in Elementor Editor","elementor"),text:o("Edit a blank canvas with the Elementor Editor","elementor"),link:elementorAppConfig.onboarding.urls.createNewPage}),a.default.createElement(c.default,{name:"template",image:elementorCommon.config.urls.assets+"images/app/onboarding/Library.svg",imageAlt:o("Click here to go to Elementor's Kit Library","elementor"),text:o("Browse from +100 templates or import your own","elementor"),link:t,clickAction:function clickAction(){location.href=t,location.reload()}})),a.default.createElement(u.default,{skipButton:e,className:"e-onboarding__good-to-go-footer"}))};var a=r(n(87363)),i=r(n(67096)),l=r(n(8915)),c=r(n(32389)),u=r(n(3e4))},92728:(e,t,n)=>{var o=n(38003).__,r=n(73203),a=n(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function HelloTheme(){var e=(0,i.useContext)(c.OnboardingContext),t=e.state,n=e.updateState,r=e.getStateObjectToUpdate,a=(0,s.default)(),f=a.ajaxState,m=a.setAjax,g=(0,i.useState)(!1),v=(0,l.default)(g,2),b=v[0],_=v[1],h=(0,i.useState)(!1),y=(0,l.default)(h,2),C=y[0],E=y[1],O={type:"success",icon:"eicon-check-circle-o",message:o("Your site’s got Hello theme. High-five!","elementor")},P=(0,i.useState)(t.isHelloThemeActivated?O:null),k=(0,l.default)(P,2),w=k[0],j=k[1],S=(0,i.useState)([]),N=(0,l.default)(S,2),x=N[0],W=N[1],R=t.isHelloThemeActivated?o("Next","elementor"):o("Continue with Hello Theme","elementor"),T=(0,i.useState)(R),A=(0,l.default)(T,2),M=A[0],q=A[1],L=(0,u.useNavigate)(),D="hello",I="siteName",B=function goToNextScreen(){return L("onboarding/"+I)};(0,i.useEffect)((function(){if(!b&&t.isHelloThemeActivated){var e=r(t,"steps",D,"completed");n(e),B()}}),[]);var H,U=function resetScreenContent(){x.forEach((function(e){return clearTimeout(e)})),W([]),E(!1),q(R)},F=(0,i.useCallback)((function(){E(!1),elementorCommon.events.dispatchEvent({event:"indication prompt",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:t.currentStep,action_state:"success",action:"hello theme activation"}}),j(O),q(o("Next","elementor"));var e=r(t,"steps",D,"completed");e.isHelloThemeActivated=!0,n(e),_(!0),B()}),[]),G=function activateHelloTheme(){E(!0),n({isHelloThemeInstalled:!0}),m({data:{action:"elementor_activate_hello_theme"}})},z=function installHelloTheme(){C||E(!0),wp.updates.ajax("install-theme",{slug:"hello-elementor",success:function success(){return G()},error:function error(){return function onErrorInstallHelloTheme(){elementorCommon.events.dispatchEvent({event:"indication prompt",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:t.currentStep,action_state:"failure",action:"hello theme install"}}),j({type:"error",icon:"eicon-warning",message:o("There was a problem installing Hello Theme.","elementor")}),U()}()}})},V=function sendNextButtonEvent(){elementorCommon.events.dispatchEvent({event:"next",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:t.currentStep}})},J={text:M,role:"button"};C&&(J.className="e-onboarding__button--processing");t.isHelloThemeActivated?J.onClick=function(){V(),B()}:J.onClick=function(){V(),t.isHelloThemeInstalled&&!t.isHelloThemeActivated?G():t.isHelloThemeInstalled?B():z()};"completed"!==t.steps[D]&&(H={text:o("Skip","elementor")});return(0,i.useEffect)((function(){C&&q(i.default.createElement(i.default.Fragment,null,i.default.createElement("i",{className:"eicon-loading eicon-animation-spin","aria-hidden":"true"})));var e=[],t=setTimeout((function(){C&&q(i.default.createElement(i.default.Fragment,null,i.default.createElement("i",{className:"eicon-loading eicon-animation-spin","aria-hidden":"true"}),i.default.createElement("span",{className:"e-onboarding__action-button-text"},o("Hold on, this can take a minute...","elementor"))))}),4e3);e.push(t);var n=setTimeout((function(){C&&q(i.default.createElement(i.default.Fragment,null,i.default.createElement("i",{className:"eicon-loading eicon-animation-spin","aria-hidden":"true"}),i.default.createElement("span",{className:"e-onboarding__action-button-text"},o("Okay, now we're really close...","elementor"))))}),3e4);e.push(n),W(e)}),[C]),(0,i.useEffect)((function(){var e;"initial"!==f.status&&("success"===f.status&&null!==(e=f.response)&&void 0!==e&&e.helloThemeActivated?F():"error"===f.status&&(elementorCommon.events.dispatchEvent({event:"indication prompt",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:t.currentStep,action_state:"failure",action:"hello theme activation"}}),j({type:"error",icon:"eicon-warning",message:o("There was a problem activating Hello Theme.","elementor")}),U()))}),[f.status]),i.default.createElement(d.default,{pageId:D,nextStep:I},i.default.createElement(p.default,{image:elementorCommon.config.urls.assets+"images/app/onboarding/Illustration_Hello.svg",title:o("Every site starts with a theme.","elementor"),actionButton:J,skipButton:H,noticeState:w},i.default.createElement("p",null,o("Hello is Elementor's official blank canvas theme optimized to build your website exactly the way you want.","elementor")),i.default.createElement("p",null,o("Here's why:","elementor")),i.default.createElement("ul",{className:"e-onboarding__feature-list"},i.default.createElement("li",null,o("Light-weight and fast loading","elementor")),i.default.createElement("li",null,o("Great for SEO","elementor")),i.default.createElement("li",null,o("Already being used by 1M+ web creators","elementor")))),i.default.createElement("div",{className:"e-onboarding__footnote"},"* "+o("You can switch your theme later on","elementor")))};var i=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==a(e)&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var o={},r=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var l=r?Object.getOwnPropertyDescriptor(e,i):null;l&&(l.get||l.set)?Object.defineProperty(o,i,l):o[i]=e[i]}o.default=e,n&&n.set(e,o);return o}(n(87363)),l=r(n(40131)),c=n(33959),u=n(50927),s=r(n(33105)),d=r(n(8915)),p=r(n(61961));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?n:t})(e)}},90013:(e,t,n)=>{var o=n(38003).__,r=n(73203),a=n(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function SiteLogo(){var e,t=(0,i.useContext)(c.OnboardingContext),n=t.state,r=t.updateState,a=t.getStateObjectToUpdate,g=(0,i.useState)(n.siteLogo.id?n.siteLogo:null),v=(0,l.default)(g,2),b=v[0],_=v[1],h=(0,i.useState)(!1),y=(0,l.default)(h,2),C=y[0],E=y[1],O=(0,i.useState)(!1),P=(0,l.default)(O,2),k=P[0],w=P[1],j=(0,i.useState)(),S=(0,l.default)(j,2),N=S[0],x=S[1],W=(0,i.useState)(null),R=(0,l.default)(W,2),T=R[0],A=R[1],M=(0,s.default)(),q=M.ajaxState,L=M.setAjax,D=(0,s.default)(),I=D.ajaxState,B=D.setAjax,H="siteLogo",U="goodToGo",F=(0,u.useNavigate)(),G={role:"button",onClick:function onClick(){if(elementorCommon.events.dispatchEvent({event:"next",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:n.currentStep}}),b.id)if(b.id!==n.siteLogo.id)z();else{var e=a(n,"steps",H,"completed");r(e),F("onboarding/"+U)}}};"completed"!==n.steps[H]&&(e={text:o("Skip","elementor")});G.text=C?i.default.createElement(i.default.Fragment,null,i.default.createElement("i",{className:"eicon-loading eicon-animation-spin","aria-hidden":"true"})):o("Next","elementor");b||(G.className="e-onboarding__button--disabled");var z=(0,i.useCallback)((function(){E(!0),L({data:{action:"elementor_update_site_logo",data:JSON.stringify({attachmentId:b.id})}})}),[b]),V=function uploadSiteLogo(e){E(!0),B({data:{action:"elementor_upload_site_logo",fileToUpload:e}})},J=function dismissUnfilteredFilesCallback(){E(!1),_(null),w(!1)};return(0,i.useEffect)((function(){var e,t;"initial"!==I.status&&("success"===I.status&&null!==(e=I.response)&&void 0!==e&&null!==(t=e.imageAttachment)&&void 0!==t&&t.id?(elementorCommon.events.dispatchEvent({event:"logo image uploaded",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,source:N}}),E(!1),_(I.response.imageAttachment),T&&A(null)):"error"===I.status&&(E(!1),_(null),elementorCommon.events.dispatchEvent({event:"indication prompt",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,action_state:"failure",action:"logo image upload"}}),A({type:"error",icon:"eicon-warning",message:"That didn't work. Try uploading your file again."})))}),[I.status]),(0,i.useEffect)((function(){var e;if("initial"!==q.status)if("success"===q.status&&null!==(e=q.response)&&void 0!==e&&e.siteLogoUpdated){elementorCommon.events.dispatchEvent({event:"logo image updated",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,source:N}}),E(!1),T&&A(null);var t=a(n,"steps",H,"completed");t.siteLogo={id:b.id,url:b.url},r(t),F("onboarding/"+U)}else"error"===q.status&&(E(!1),elementorCommon.events.dispatchEvent({event:"indication prompt",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:n.currentStep,action_state:"failure",action:"update site logo"}}),A({type:"error",icon:"eicon-warning",message:"That didn't work. Try uploading your file again."}))}),[q.status]),i.default.createElement(f.default,{pageId:H,nextStep:U},i.default.createElement(m.default,{image:elementorCommon.config.urls.assets+"images/app/onboarding/Illustration_Setup.svg",title:o("Have a logo? Add it here.","elementor"),actionButton:G,skipButton:e,noticeState:T},i.default.createElement("span",null,o("Otherwise, you can skip this and add one later.","elementor")),b&&!k?i.default.createElement("div",{className:"e-onboarding__logo-container"+(C?" e-onboarding__is-uploading":"")},i.default.createElement("div",{className:"e-onboarding__logo-remove",onClick:function onClick(){return function onImageRemoveClick(){elementorCommon.events.dispatchEvent({event:"remove selected logo",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement}}),_(null)}()}},i.default.createElement("i",{className:"eicon-trash-o"})),i.default.createElement("img",{src:b.url,alt:o("Potential Site Logo","elementor")})):i.default.createElement(i.default.Fragment,null,i.default.createElement(d.default,{className:"e-onboarding__drop-zone",heading:o("Drop image here","elementor"),secondaryText:o("or","elementor"),buttonText:o("Open Media Library","elementor"),buttonVariant:"outlined",buttonColor:"cta",icon:"",type:"wp-media",filetypes:["jpg","jpeg","png","svg"],onFileSelect:function onFileSelect(e){return function onFileSelect(e){x("drop"),"image/svg+xml"!==e.type||elementorAppConfig.onboarding.isUnfilteredFilesEnabled?(_(e),A(null),V(e)):(_(e),E(!0),w(!0))}(e)},onWpMediaSelect:function onWpMediaSelect(e){var t=e.state().get("selection").first().toJSON();x("browse"),_(t),A(null)},onButtonClick:function onButtonClick(){elementorCommon.events.dispatchEvent({event:"browse file click",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:n.currentStep}})},onError:function onError(e){"file_not_allowed"===e.id&&(elementorCommon.events.dispatchEvent({event:"indication prompt",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:n.currentStep,action_state:"failure",action:"logo upload format"}}),A({type:"error",icon:"eicon-warning",message:o("This file type is not supported. Try a different type of file","elementor")}))}})),i.default.createElement(p.default,{show:k,setShow:w,confirmModalText:o("This allows Elementor to scan your SVGs for malicious content. If you do not wish to allow this, use a different image format.","elementor"),errorModalText:o("There was a problem with enabling SVG uploads. Try again, or use another image format.","elementor"),onReady:function onReady(){w(!1),elementorAppConfig.onboarding.isUnfilteredFilesEnabled=!0,V(b)},onDismiss:function onDismiss(){return J()},onCancel:function onCancel(){return J()}})))};var i=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==a(e)&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var o={},r=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var l=r?Object.getOwnPropertyDescriptor(e,i):null;l&&(l.get||l.set)?Object.defineProperty(o,i,l):o[i]=e[i]}o.default=e,n&&n.set(e,o);return o}(n(87363)),l=r(n(40131)),c=n(33959),u=n(50927),s=r(n(33105)),d=r(n(46218)),p=r(n(31794)),f=r(n(8915)),m=r(n(61961));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?n:t})(e)}},78270:(e,t,n)=>{var o=n(38003).__,r=n(73203),a=n(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function SiteName(){var e,t=(0,i.useContext)(c.OnboardingContext),n=t.state,r=t.updateState,a=t.getStateObjectToUpdate,f=(0,s.default)(),m=f.ajaxState,g=f.setAjax,v=(0,i.useState)(null),b=(0,l.default)(v,2),_=b[0],h=b[1],y=(0,i.useState)(n.siteName),C=(0,l.default)(y,2),E=C[0],O=C[1],P="siteName",k="siteLogo",w=(0,u.useNavigate)(),j=(0,i.useRef)(),S={text:o("Next","elementor"),onClick:function onClick(){if(elementorCommon.events.dispatchEvent({event:"next",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:n.currentStep}}),j.current.value!==n.siteName&&""!==j.current.value)g({data:{action:"elementor_update_site_name",data:JSON.stringify({siteName:j.current.value})}});else if(j.current.value===n.siteName){var e=a(n,"steps",P,"completed");r(e),w("onboarding/"+k)}else{var t=a(n,"steps",P,"skipped");r(t),w("onboarding/"+k)}}};"completed"!==n.steps[P]&&(e={text:o("Skip","elementor")});E||(S.className="e-onboarding__button--disabled");return(0,i.useEffect)((function(){var e;if("initial"!==m.status)if("success"===m.status&&null!==(e=m.response)&&void 0!==e&&e.siteNameUpdated){var t=a(n,"steps",P,"completed");t.siteName=j.current.value,r(t),w("onboarding/"+k)}else"error"===m.status&&(elementorCommon.events.dispatchEvent({event:"indication prompt",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:n.currentStep,action_state:"failure",action:"site name update"}}),h({type:"error",icon:"eicon-warning",message:o("Sorry, the name wasn't saved. Try again, or skip for now.","elementor")}))}),[m.status]),i.default.createElement(d.default,{pageId:P,nextStep:k},i.default.createElement(p.default,{image:elementorCommon.config.urls.assets+"images/app/onboarding/Illustration_Setup.svg",title:o("Now, let's give your site a name.","elementor"),actionButton:S,skipButton:e,noticeState:_},i.default.createElement("p",null,o("This is what your site is called on the WP dashboard, and can be changed later from the general settings - it's not your website's URL.","elementor")),i.default.createElement("input",{className:"e-onboarding__text-input e-onboarding__site-name-input",type:"text",placeholder:"e.g. Eric's Space Shuttles",defaultValue:n.siteName||"",ref:j,onChange:function onChange(e){return O(e.target.value)}})))};var i=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==a(e)&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var o={},r=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var l=r?Object.getOwnPropertyDescriptor(e,i):null;l&&(l.get||l.set)?Object.defineProperty(o,i,l):o[i]=e[i]}o.default=e,n&&n.set(e,o);return o}(n(87363)),l=r(n(40131)),c=n(33959),u=n(50927),s=r(n(33105)),d=r(n(8915)),p=r(n(61961));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?n:t})(e)}},3902:(e,t,n)=>{var o=n(38003).__,r=n(73203),a=n(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function UploadAndInstallPro(){(0,u.default)({title:o("Upload and Install Elementor Pro","elementor")});var e=(0,i.useContext)(f.OnboardingContext).state,t=(0,c.default)(),n=t.ajaxState,r=t.setAjax,a=(0,i.useState)(null),g=(0,l.default)(a,2),v=g[0],b=g[1],_=(0,i.useState)(!1),h=(0,l.default)(_,2),y=h[0],C=h[1],E=(0,i.useState)(),O=(0,l.default)(E,2),P=O[0],k=O[1],w=(0,i.useCallback)((function(e){C(!0),r({data:{action:"elementor_upload_and_install_pro",fileToUpload:e}})}),[]),j=function setErrorNotice(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"upload",o=(null==t?void 0:t.message)||"That didn't work. Try uploading your file again.";elementorCommon.events.dispatchEvent({event:"indication prompt",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:e.currentStep,action_state:"failure",action:n+" pro",source:P}}),b({type:"error",icon:"eicon-warning",message:o})};(0,i.useEffect)((function(){var t;"initial"!==n.status&&(C(!1),"success"===n.status&&null!==(t=n.response)&&void 0!==t&&t.elementorProInstalled?(elementorCommon.events.dispatchEvent({event:"pro uploaded",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:e.currentStep,source:P}}),opener&&opener!==window&&(opener.jQuery("body").trigger("elementor/upload-and-install-pro/success"),window.close(),opener.focus())):"error"===n.status&&j("install"))}),[n.status]);if(y)return i.default.createElement(m.default,{loadingText:o("Uploading","elementor")});return i.default.createElement("div",{className:"eps-app e-onboarding__upload-pro"},i.default.createElement(s.default,null,i.default.createElement(d.default,{className:"e-onboarding__upload-pro-drop-zone",onFileSelect:function onFileSelect(e,t,n){k(n),w(e)},onError:function onError(e){return j(e,"upload")},filetypes:["zip"],buttonColor:"cta",buttonVariant:"contained",heading:o("Import your Elementor Pro plugin file","elementor"),text:o("Drag & Drop your .zip file here","elementor"),secondaryText:o("or","elementor"),buttonText:o("Browse","elementor")}),v&&i.default.createElement(p.default,{noticeState:v}),i.default.createElement("div",{className:"e-onboarding__upload-pro-get-file"},o("Don't know where to get the file from?","elementor")+" ",i.default.createElement("a",{onClick:function onClick(){return function onProUploadHelpLinkClick(){elementorCommon.events.dispatchEvent({event:"pro plugin upload help",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:e.currentStep}})}()},href:"https://my.elementor.com/subscriptions/"+elementorAppConfig.onboarding.utms.downloadPro,target:"_blank"},o("Click here","elementor")))))};var i=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==a(e)&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var o={},r=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var l=r?Object.getOwnPropertyDescriptor(e,i):null;l&&(l.get||l.set)?Object.defineProperty(o,i,l):o[i]=e[i]}o.default=e,n&&n.set(e,o);return o}(n(87363)),l=r(n(40131)),c=r(n(33105)),u=r(n(78845)),s=r(n(88138)),d=r(n(46218)),p=r(n(54041)),f=n(33959),m=r(n(87206));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?n:t})(e)}},94170:(e,t,n)=>{var o=n(23615);Object.defineProperty(t,"__esModule",{value:!0}),t.default=Connect;var r=n(87363),a=n(33959);function Connect(e){var t=(0,r.useContext)(a.OnboardingContext),n=t.state,o=t.updateState,i=t.getStateObjectToUpdate;return(0,r.useEffect)((function(){jQuery(e.buttonRef.current).elementorConnect({success:function success(t){return e.successCallback?e.successCallback(t):function connectSuccessCallback(e){var t=i(n,"steps","account","completed");elementorCommon.config.library_connect.is_connected=!0,elementorCommon.config.library_connect.current_access_level=e.kits_access_level||e.access_level||0,t.isLibraryConnected=!0,o(t)}(t)},error:function error(){e.errorCallback&&e.errorCallback()},popup:{width:726,height:534}})}),[]),null}Connect.propTypes={buttonRef:o.object.isRequired,successCallback:o.func,errorCallback:o.func}}}]);