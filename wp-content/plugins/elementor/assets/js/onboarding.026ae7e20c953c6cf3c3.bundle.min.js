/*! elementor - v3.23.0 - 05-08-2024 */
"use strict";(self.webpackChunkelementor=self.webpackChunkelementor||[]).push([[2343],{87206:(e,t,n)=>{var r=n(23615),o=n(38003).__,a=n(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=ElementorLoading;var l=a(n(87363));function ElementorLoading(e){return l.default.createElement("div",{className:"elementor-loading"},l.default.createElement("div",{className:"elementor-loader-wrapper"},l.default.createElement("div",{className:"elementor-loader"},l.default.createElement("div",{className:"elementor-loader-boxes"},l.default.createElement("div",{className:"elementor-loader-box"}),l.default.createElement("div",{className:"elementor-loader-box"}),l.default.createElement("div",{className:"elementor-loader-box"}),l.default.createElement("div",{className:"elementor-loader-box"}))),l.default.createElement("div",{className:"elementor-loading-title"},e.loadingText)))}ElementorLoading.propTypes={loadingText:r.string},ElementorLoading.defaultProps={loadingText:o("Loading","elementor")}},15368:(e,t,n)=>{var r=n(23615),o=n(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.default=PopoverDialog;var a=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==o(e)&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var r={},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var l in e)if("default"!==l&&Object.prototype.hasOwnProperty.call(e,l)){var i=a?Object.getOwnPropertyDescriptor(e,l):null;i&&(i.get||i.set)?Object.defineProperty(r,l,i):r[l]=e[l]}r.default=e,n&&n.set(e,r);return r}(n(87363));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?n:t})(e)}function PopoverDialog(e){var t=e.targetRef,n=e.offsetTop,r=e.offsetLeft,o=e.wrapperClass,l=e.trigger,i=e.hideAfter,c=(0,a.useCallback)((function(e){var o=null==t?void 0:t.current;if(o&&e){var a=function showPopover(){e.style.display="block",e.setAttribute("aria-expanded",!0);var t=o.getBoundingClientRect(),a=e.getBoundingClientRect(),l=a.width-t.width;e.style.top=t.bottom+n+"px",e.style.left=t.left-l/2-r+"px",e.style.setProperty("--popover-arrow-offset-end",(a.width-16)/2+"px")},c=function hidePopover(){e.style.display="none",e.setAttribute("aria-expanded",!1)};"hover"===l?function handlePopoverHover(){var t=!0,n=null;o.addEventListener("mouseover",(function(){t=!0,a()})),o.addEventListener("mouseleave",(function(){n=setTimeout((function(){t&&"block"===e.style.display&&c()}),i)})),e.addEventListener("mouseover",(function(){t=!1,n&&(clearTimeout(n),n=null)})),e.addEventListener("mouseleave",(function(){n=setTimeout((function(){t&&"block"===e.style.display&&c()}),i),t=!0}))}():"click"===l&&function handlePopoverClick(){var t=!1;o.addEventListener("click",(function(e){e.preventDefault(),e.stopPropagation(),t?(c(),t=!1):(a(),t=!0)})),e.addEventListener("click",(function(e){e.stopPropagation()})),document.body.addEventListener("click",(function(){t&&(c(),t=!1)}))}()}}),[t]),u="e-app__popover";return o&&(u+=" "+o),a.default.createElement("div",{className:u,ref:c},e.children)}PopoverDialog.propTypes={targetRef:r.oneOfType([r.func,r.shape({current:r.any})]).isRequired,trigger:r.string,direction:r.string,offsetTop:r.oneOfType([r.string,r.number]),offsetLeft:r.oneOfType([r.string,r.number]),wrapperClass:r.string,children:r.any,hideAfter:r.number},PopoverDialog.defaultProps={direction:"bottom",trigger:"hover",offsetTop:10,offsetLeft:0,hideAfter:300}},60458:(e,t,n)=>{var r=n(73203),o=n(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function App(){return(0,a.useEffect)((function(){var e="eps-theme-dark",t=document.body.classList.contains(e);if(t&&document.body.classList.remove(e),!elementorAppConfig.onboarding.onboardingAlreadyRan){var n=new FormData;n.append("_nonce",elementorCommon.config.ajax.nonce),n.append("action","elementor_update_onboarding_option"),fetch(elementorCommon.config.ajax.url,{method:"POST",body:n})}return elementorAppConfig.return_url=elementorAppConfig.admin_url,function(){t&&document.body.classList.add(e)}}),[]),a.default.createElement(c.ContextProvider,null,a.default.createElement(l.LocationProvider,{history:i.default.appHistory},a.default.createElement(l.Router,null,a.default.createElement(u.default,{default:!0}),a.default.createElement(s.default,{path:"hello"}),a.default.createElement(g.default,{path:"chooseFeatures"}),a.default.createElement(d.default,{path:"siteName"}),a.default.createElement(p.default,{path:"siteLogo"}),a.default.createElement(f.default,{path:"goodToGo"}),a.default.createElement(m.default,{path:"uploadAndInstallPro"}))))};var a=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==o(e)&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var r={},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var l in e)if("default"!==l&&Object.prototype.hasOwnProperty.call(e,l)){var i=a?Object.getOwnPropertyDescriptor(e,l):null;i&&(i.get||i.set)?Object.defineProperty(r,l,i):r[l]=e[l]}r.default=e,n&&n.set(e,r);return r}(n(87363)),l=n(50927),i=r(n(3869)),c=n(33959),u=r(n(1103)),s=r(n(92728)),d=r(n(78270)),p=r(n(90013)),f=r(n(79914)),m=r(n(3902)),g=r(n(40088));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?n:t})(e)}},36608:(e,t,n)=>{var r=n(23615),o=n(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=Button;var a=o(n(87363));function Button(e){var t=e.buttonSettings,n=e.type,r="e-onboarding__button";return n&&(r+=" e-onboarding__button-".concat(n)),t.className?t.className+=" "+r:t.className=r,t.href?a.default.createElement("a",t,t.text):a.default.createElement("div",t,t.text)}Button.propTypes={buttonSettings:r.object.isRequired,type:r.string}},32389:(e,t,n)=>{var r=n(23615),o=n(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=Card;var a=o(n(87363));function Card(e){var t=e.image,n=e.imageAlt,r=e.text,o=e.link,l=e.name,i=e.clickAction;return a.default.createElement("a",{target:"_self",className:"e-onboarding__card",href:o,onClick:function onClick(){elementorCommon.events.dispatchEvent({event:"starting canvas click",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,selection:l}}),i&&i()}},a.default.createElement("img",{className:"e-onboarding__card-image",src:t,alt:n}),a.default.createElement("div",{className:"e-onboarding__card-text"},r))}Card.propTypes={image:r.string.isRequired,imageAlt:r.string.isRequired,text:r.string.isRequired,link:r.string.isRequired,name:r.string.isRequired,clickAction:r.func}},74233:(e,t,n)=>{var r=n(23615),o=n(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=ChecklistItem;var a=o(n(87363));function ChecklistItem(e){return a.default.createElement("li",{className:"e-onboarding__checklist-item"},a.default.createElement("i",{className:"eicon-check-circle"}),e.children)}ChecklistItem.propTypes={children:r.string}},51224:(e,t,n)=>{var r=n(23615),o=n(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=Checklist;var a=o(n(87363));function Checklist(e){return a.default.createElement("ul",{className:"e-onboarding__checklist"},e.children)}Checklist.propTypes={children:r.any.isRequired}},47707:(e,t,n)=>{var r=n(38003).__,o=n(23615),a=n(73203),l=n(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.default=GoProPopover;var i=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==l(e)&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var r={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var i=o?Object.getOwnPropertyDescriptor(e,a):null;i&&(i.get||i.set)?Object.defineProperty(r,a,i):r[a]=e[a]}r.default=e,n&&n.set(e,r);return r}(n(87363)),c=n(33959),u=a(n(15368)),s=a(n(51224)),d=a(n(74233)),p=a(n(36608));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?n:t})(e)}function GoProPopover(e){var t=(0,i.useContext)(c.OnboardingContext),n=t.state,o=t.updateState,a=(0,i.useCallback)((function(e){e&&e.addEventListener("click",(function(t){t.preventDefault(),elementorCommon.events.dispatchEvent({event:"already have pro",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:n.currentStep}}),window.open(e.href+"&mode=popup","elementorUploadPro","toolbar=no, menubar=no, width=728, height=531, top=100, left=100"),elementorCommon.elements.$body.on("elementor/upload-and-install-pro/success",(function(){o({hasPro:!0,proNotice:{type:"success",icon:"eicon-check-circle-o",message:r("Elementor Pro has been successfully installed.","elementor")}})}))}))}),[]),l=e.buttonsConfig.find((function(e){return"go-pro"===e.id})),f={text:elementorAppConfig.onboarding.experiment?r("Upgrade now","elementor"):r("Upgrade Now","elementor"),className:"e-onboarding__go-pro-cta",target:"_blank",href:"https://elementor.com/pro/?utm_source=onboarding-wizard&utm_campaign=gopro&utm_medium=wp-dash&utm_content=top-bar-dropdown&utm_term="+elementorAppConfig.onboarding.onboardingVersion,tabIndex:0,onClick:function onClick(){elementorCommon.events.dispatchEvent({event:"get elementor pro",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:n.currentStep}})}};return i.default.createElement(u.default,{targetRef:l.elRef,wrapperClass:"e-onboarding__go-pro"},i.default.createElement("div",{className:"e-onboarding__go-pro-content"},i.default.createElement("h2",{className:"e-onboarding__go-pro-title"},r("Ready to Get Elementor Pro?","elementor")),i.default.createElement(s.default,null,i.default.createElement(d.default,null,r("90+ Basic & Pro widgets","elementor")),i.default.createElement(d.default,null,r("300+ Basic & Pro templates","elementor")),i.default.createElement(d.default,null,r("Premium Support","elementor"))),i.default.createElement("div",{className:"e-onboarding__go-pro-paragraph"},r("And so much more!","elementor")),i.default.createElement("div",{className:"e-onboarding__go-pro-paragraph"},i.default.createElement(p.default,{buttonSettings:f})),i.default.createElement("div",{className:"e-onboarding__go-pro-paragraph"},i.default.createElement("a",{tabIndex:"0",className:"e-onboarding__go-pro-already-have",ref:a,href:elementorAppConfig.onboarding.urls.uploadPro,rel:"opener"},r("Already have Elementor Pro?","elementor")))))}GoProPopover.propTypes={buttonsConfig:o.array.isRequired}},3e4:(e,t,n)=>{var r=n(23615),o=n(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=FooterButtons;var a=o(n(87363)),l=o(n(67096)),i=o(n(36608)),c=o(n(63878));function FooterButtons(e){var t=e.actionButton,n=e.skipButton,r=e.className,o="e-onboarding__footer";return r&&(o+=" "+r),a.default.createElement(l.default,{container:!0,alignItems:"center",justify:"space-between",className:o},t&&a.default.createElement(i.default,{buttonSettings:t,type:"action"}),n&&a.default.createElement(c.default,{button:n}))}FooterButtons.propTypes={actionButton:r.object,skipButton:r.object,className:r.string}},55020:(e,t,n)=>{var r=n(38003).__,o=n(23615),a=n(73203),l=n(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.default=Header;var i=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==l(e)&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var r={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var i=o?Object.getOwnPropertyDescriptor(e,a):null;i&&(i.get||i.set)?Object.defineProperty(r,a,i):r[a]=e[a]}r.default=e,n&&n.set(e,r);return r}(n(87363)),c=n(33959),u=a(n(67096)),s=a(n(47707)),d=a(n(78419)),p=a(n(78845));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?n:t})(e)}function Header(e){(0,p.default)({title:e.title});var t=(0,i.useContext)(c.OnboardingContext).state;return i.default.createElement(u.default,{container:!0,alignItems:"center",justify:"space-between",className:"eps-app__header e-onboarding__header"},i.default.createElement("div",{className:"eps-app__logo-title-wrapper e-onboarding__header-logo"},i.default.createElement("i",{className:"eps-app__logo eicon-elementor"}),i.default.createElement("img",{src:elementorCommon.config.urls.assets+"images/logo-platform.svg",alt:r("Elementor Logo","elementor")})),i.default.createElement(d.default,{buttons:e.buttons,onClose:function onClose(){elementorCommon.events.dispatchEvent({event:"close modal",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:t.currentStep}}),window.top.location=elementorAppConfig.admin_url}}),!t.hasPro&&i.default.createElement(s.default,{buttonsConfig:e.buttons}))}Header.propTypes={title:o.string,buttons:o.arrayOf(o.object)},Header.defaultProps={buttons:[]}},8915:(e,t,n)=>{var r=n(38003).__,o=n(23615),a=n(73203),l=n(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.default=Layout;var i=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==l(e)&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var r={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var i=o?Object.getOwnPropertyDescriptor(e,a):null;i&&(i.get||i.set)?Object.defineProperty(r,a,i):r[a]=e[a]}r.default=e,n&&n.set(e,r);return r}(n(87363)),c=n(33959),u=a(n(55020)),s=a(n(4069)),d=a(n(88138)),p=a(n(94170));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?n:t})(e)}function Layout(e){(0,i.useEffect)((function(){elementorCommon.events.dispatchEvent({event:"modal load",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:e.pageId,user_state:elementorCommon.config.library_connect.is_connected?"logged":"anon"}}),o({currentStep:e.pageId,nextStep:e.nextStep||"",proNotice:null})}),[e.pageId]);var t=(0,i.useContext)(c.OnboardingContext),n=t.state,o=t.updateState,a=[],l=(0,i.useRef)(),f={id:"create-account",text:r("Create Account","elementor"),hideText:!1,elRef:(0,i.useRef)(),url:elementorAppConfig.onboarding.urls.signUp+elementorAppConfig.onboarding.utms.connectTopBar,target:"_blank",rel:"opener",onClick:function onClick(){elementorCommon.events.dispatchEvent({event:"create account",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:n.currentStep,source:"header"}})}};return n.isLibraryConnected?a.push({id:"my-elementor",text:r("My Elementor","elementor"),hideText:!1,icon:"eicon-user-circle-o",url:"https://my.elementor.com/websites/?utm_source=onboarding-wizard&utm_medium=wp-dash&utm_campaign=my-account&utm_content=top-bar&utm_term="+elementorAppConfig.onboarding.onboardingVersion,target:"_blank",onClick:function onClick(){elementorCommon.events.dispatchEvent({event:"my elementor click",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:n.currentStep,source:"header"}})}}):a.push(f),n.hasPro||a.push({id:"go-pro",text:r("Upgrade","elementor"),hideText:!1,className:"eps-button__go-pro-btn",url:"https://elementor.com/pro/?utm_source=onboarding-wizard&utm_campaign=gopro&utm_medium=wp-dash&utm_content=top-bar&utm_term="+elementorAppConfig.onboarding.onboardingVersion,target:"_blank",elRef:l,onClick:function onClick(){elementorCommon.events.dispatchEvent({event:"go pro",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:n.currentStep}})}}),i.default.createElement("div",{className:"eps-app__lightbox"},i.default.createElement("div",{className:"eps-app e-onboarding"},!n.isLibraryConnected&&i.default.createElement(p.default,{buttonRef:f.elRef}),i.default.createElement(u.default,{title:r("Getting Started","elementor"),buttons:a}),i.default.createElement("div",{className:"eps-app__main e-onboarding__page-"+e.pageId},i.default.createElement(d.default,{className:"e-onboarding__content"},i.default.createElement(s.default,null),e.children))))}Layout.propTypes={pageId:o.string.isRequired,nextStep:o.string,className:o.string,children:o.any.isRequired}},61961:(e,t,n)=>{var r=n(23615),o=n(73203),a=n(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.default=PageContentLayout;var l=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==a(e)&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var r={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var l in e)if("default"!==l&&Object.prototype.hasOwnProperty.call(e,l)){var i=o?Object.getOwnPropertyDescriptor(e,l):null;i&&(i.get||i.set)?Object.defineProperty(r,l,i):r[l]=e[l]}r.default=e,n&&n.set(e,r);return r}(n(87363)),i=n(33959),c=o(n(67096)),u=o(n(54041)),s=o(n(3e4));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?n:t})(e)}function PageContentLayout(e){var t=(0,l.useContext)(i.OnboardingContext).state;return l.default.createElement(l.default.Fragment,null,l.default.createElement(c.default,{container:!0,alignItems:"center",justify:"space-between",className:"e-onboarding__page-content"},l.default.createElement("div",{className:"e-onboarding__page-content-start"},l.default.createElement("h1",{className:"e-onboarding__page-content-section-title"},e.title,e.secondLineTitle&&l.default.createElement(l.default.Fragment,null,l.default.createElement("br",null),e.secondLineTitle)),l.default.createElement("div",{className:"e-onboarding__page-content-section-text"},e.children)),l.default.createElement("div",{className:"e-onboarding__page-content-end"},l.default.createElement("img",{src:e.image,alt:"Information"}))),e.noticeState&&l.default.createElement("div",{className:"e-onboarding__notice-container"},e.noticeState||t.proNotice?function printNotices(){return l.default.createElement(l.default.Fragment,null,e.noticeState&&l.default.createElement(u.default,{noticeState:e.noticeState}),t.proNotice&&l.default.createElement(u.default,{noticeState:t.proNotice}))}():l.default.createElement("div",{className:"e-onboarding__notice-empty-spacer"})),l.default.createElement(s.default,{actionButton:e.actionButton,skipButton:e.skipButton}))}PageContentLayout.propTypes={title:r.string,secondLineTitle:r.string,children:r.any,image:r.string,actionButton:r.object,skipButton:r.object,noticeState:r.any}},14118:(e,t,n)=>{var r=n(38003).__,o=n(23615),a=n(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=Message;var l=a(n(87363)),i=a(n(40131));function Message(e){var t=e.tier,n=r("Based on the features you chose, we recommend the %s plan, or higher","elementor").split("%s"),o=(0,i.default)(n,2),a=o[0],c=o[1];return l.default.createElement(l.default.Fragment,null,a,l.default.createElement("strong",null,t),c)}Message.propTypes={tier:o.string.isRequired}},54041:(e,t,n)=>{var r=n(23615),o=n(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=Notice;var a=o(n(87363));function Notice(e){return a.default.createElement("div",{className:"e-onboarding__notice e-onboarding__notice--".concat(e.noticeState.type)},a.default.createElement("i",{className:e.noticeState.icon}),a.default.createElement("span",{className:"e-onboarding__notice-text"},e.noticeState.message))}Notice.propTypes={noticeState:r.object}},18662:(e,t,n)=>{var r=n(23615),o=n(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.default=ProgressBarItem;var a=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==o(e)&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var r={},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var l in e)if("default"!==l&&Object.prototype.hasOwnProperty.call(e,l)){var i=a?Object.getOwnPropertyDescriptor(e,l):null;i&&(i.get||i.set)?Object.defineProperty(r,l,i):r[l]=e[l]}r.default=e,n&&n.set(e,r);return r}(n(87363)),l=n(33959);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?n:t})(e)}function ProgressBarItem(e){var t=(0,a.useContext)(l.OnboardingContext).state,n="completed"===t.steps[e.id],r="skipped"===t.steps[e.id],o="e-onboarding__progress-bar-item";return e.id===t.currentStep?o+=" e-onboarding__progress-bar-item--active":n?o+=" e-onboarding__progress-bar-item--completed":r&&(o+=" e-onboarding__progress-bar-item--skipped"),a.default.createElement("div",{onClick:e.onClick,className:o},a.default.createElement("div",{className:"e-onboarding__progress-bar-item-icon"},n?a.default.createElement("i",{className:"eicon-check"}):e.index+1),e.title)}ProgressBarItem.propTypes={index:r.number.isRequired,id:r.string.isRequired,title:r.string.isRequired,route:r.string,onClick:r.func}},4069:(e,t,n)=>{var r=n(38003).__,o=n(73203),a=n(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function ProgressBar(){var e=(0,l.useContext)(c.OnboardingContext).state,t=(0,u.useNavigate)(),n=[{id:"account",title:r("Elementor Account","elementor"),route:"account"}];elementorAppConfig.onboarding.helloActivated||n.push({id:"hello",title:r("Hello Theme","elementor"),route:"hello"});elementorAppConfig.onboarding.experiment?n.push({id:"chooseFeatures",title:r("Choose Features","elementor"),route:"chooseFeatures"}):n.push({id:"siteName",title:r("Site Name","elementor"),route:"site-name"},{id:"siteLogo",title:r("Site Logo","elementor"),route:"site-logo"});n.push({id:"goodToGo",title:r("Good to Go","elementor"),route:"good-to-go"});var o=n.map((function(n,r){return n.index=r,e.steps[n.id]&&(n.onClick=function(){elementorCommon.events.dispatchEvent({event:"step click",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:e.currentStep,next_step:n.id}}),t("/onboarding/"+n.id)}),l.default.createElement(s.default,(0,i.default)({key:n.id},n))}));return l.default.createElement("div",{className:"e-onboarding__progress-bar"},o)};var l=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==a(e)&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var r={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var l in e)if("default"!==l&&Object.prototype.hasOwnProperty.call(e,l)){var i=o?Object.getOwnPropertyDescriptor(e,l):null;i&&(i.get||i.set)?Object.defineProperty(r,l,i):r[l]=e[l]}r.default=e,n&&n.set(e,r);return r}(n(87363)),i=o(n(73119)),c=n(33959),u=n(50927),s=o(n(18662));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?n:t})(e)}},63878:(e,t,n)=>{var r=n(23615),o=n(73203),a=n(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.default=SkipButton;var l=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==a(e)&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var r={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var l in e)if("default"!==l&&Object.prototype.hasOwnProperty.call(e,l)){var i=o?Object.getOwnPropertyDescriptor(e,l):null;i&&(i.get||i.set)?Object.defineProperty(r,l,i):r[l]=e[l]}r.default=e,n&&n.set(e,r);return r}(n(87363)),i=n(33959),c=n(50927),u=o(n(36608));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?n:t})(e)}function SkipButton(e){var t=e.button,n=e.className,r=(0,l.useContext)(i.OnboardingContext),o=r.state,a=r.updateState,s=(0,c.useNavigate)(),d=t.action||function skipStep(){var e=JSON.parse(JSON.stringify(o));e.steps[o.currentStep]="skipped",a(e),o.nextStep&&s("onboarding/"+o.nextStep)};return delete t.action,t.onClick=function(){elementorCommon.events.dispatchEvent({event:"skip",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:o.currentStep}}),t.href||d()},l.default.createElement(u.default,{buttonSettings:t,className:n,type:"skip"})}SkipButton.propTypes={button:r.object.isRequired,className:r.string}},33959:(e,t,n)=>{var r=n(23615),o=n(73203),a=n(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.ContextProvider=ContextProvider,t.OnboardingContext=void 0;var l=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==a(e)&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var r={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var l in e)if("default"!==l&&Object.prototype.hasOwnProperty.call(e,l)){var i=o?Object.getOwnPropertyDescriptor(e,l):null;i&&(i.get||i.set)?Object.defineProperty(r,l,i):r[l]=e[l]}r.default=e,n&&n.set(e,r);return r}(n(87363)),i=o(n(93231)),c=o(n(40131));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?n:t})(e)}function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach((function(t){(0,i.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var u=(0,l.createContext)({});function ContextProvider(e){var t=elementorAppConfig.onboarding,n={hasPro:elementorAppConfig.hasPro,isLibraryConnected:t.isLibraryConnected,isHelloThemeInstalled:t.helloInstalled,isHelloThemeActivated:t.helloActivated,siteName:t.siteName,siteLogo:t.siteLogo,proNotice:"",currentStep:"",nextStep:"",steps:{account:!1,hello:!1,chooseFeatures:!1,siteName:!1,siteLogo:!1,goodToGo:!1}},r=(0,l.useState)(n),o=(0,c.default)(r,2),a=o[0],i=o[1],s=(0,l.useCallback)((function(e){i((function(t){return _objectSpread(_objectSpread({},t),e)}))}),[i]);return l.default.createElement(u.Provider,{value:{state:a,setState:i,updateState:s,getStateObjectToUpdate:function getStateObjectToUpdate(e,t,n,r){var o=JSON.parse(JSON.stringify(e));return o[t][n]=r,o}}},e.children)}t.OnboardingContext=u,ContextProvider.propTypes={children:r.any}},1103:(e,t,n)=>{var r=n(38003).__,o=n(73203),a=n(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function Account(){var e,t=(0,l.useContext)(u.OnboardingContext),n=t.state,o=t.updateState,a=t.getStateObjectToUpdate,f=(0,l.useState)(null),m=(0,i.default)(f,2),g=m[0],v=m[1],b=function getNextStep(){if(!n.isHelloThemeActivated)return"hello";return elementorAppConfig.onboarding.experiment?"chooseFeatures":"siteName"}(),_=(0,c.useNavigate)(),h="account",y=(0,l.useRef)(),C=(0,l.useRef)();"completed"!==n.steps[h]&&(e={text:r("Skip","elementor")});var E={};E=n.isLibraryConnected?{firstLine:l.default.createElement(l.default.Fragment,null,r("To get the most out of Elementor, we'll help you take your","elementor")," ",l.default.createElement("br",null)," ",r("first steps:","elementor")),listItems:elementorAppConfig.onboarding.experiment?[r("Set your site's theme","elementor"),r("Choose additional features","elementor"),r("Choose how to start creating","elementor")]:[r("Set your site's theme","elementor"),r("Give your site a name & logo","elementor"),r("Choose how to start creating","elementor")]}:elementorAppConfig.onboarding.experiment?{firstLine:r("Once you connect your Elementor account, you can choose from dozens of professional templates and manage your site with the My Elementor dashboard.","elementor"),listItems:[]}:{firstLine:r("To get the most out of Elementor, we’ll connect your account.","elementor")+" "+r("Then you can:","elementor"),listItems:[r("Choose from countless professional templates","elementor"),r("Manage your site with our handy dashboard","elementor"),r("Take part in the community forum, share & grow together","elementor")]};var O={role:"button"};n.isLibraryConnected?(O.text=r("Let’s do it","elementor"),O.onClick=function(){elementorCommon.events.dispatchEvent({event:"next",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:n.currentStep}}),o(a(n,"steps",h,"completed")),_("onboarding/"+b)}):(O.text=r("Create my account","elementor"),O.href=elementorAppConfig.onboarding.urls.signUp+elementorAppConfig.onboarding.utms.connectCta,O.ref=y,O.onClick=function(){elementorCommon.events.dispatchEvent({event:"create account",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,source:"cta"}})});var P=function connectSuccessCallback(e){var t=a(n,"steps",h,"completed");t.isLibraryConnected=!0,elementorCommon.config.library_connect.is_connected=!0,elementorCommon.config.library_connect.current_access_level=e.kits_access_level||e.access_level||0,elementorCommon.config.library_connect.current_access_tier=e.access_tier,o(t),elementorCommon.events.dispatchEvent({event:"indication prompt",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:n.currentStep,action_state:"success",action:"connect account"}}),v({type:"success",icon:"eicon-check-circle-o",message:"Alrighty - your account is connected."}),_("onboarding/"+b)};var w=function connectFailureCallback(){elementorCommon.events.dispatchEvent({event:"indication prompt",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:n.currentStep,action_state:"failure",action:"connect account"}}),v({type:"error",icon:"eicon-warning",message:r("Oops, the connection failed. Try again.","elementor")}),_("onboarding/"+b)};return l.default.createElement(d.default,{pageId:h,nextStep:b},l.default.createElement(p.default,{image:elementorCommon.config.urls.assets+"images/app/onboarding/Illustration_Account.svg",title:elementorAppConfig.onboarding.experiment?r("You're here!","elementor"):r("You're here! Let's set things up.","elementor"),secondLineTitle:elementorAppConfig.onboarding.experiment?r(" Let's get connected.","elementor"):"",actionButton:O,skipButton:e,noticeState:g},O.ref&&!n.isLibraryConnected&&l.default.createElement(s.default,{buttonRef:O.ref,successCallback:function successCallback(e){return P(e)},errorCallback:w}),l.default.createElement("span",null,E.firstLine),l.default.createElement("ul",null,E.listItems.map((function(e,t){return l.default.createElement("li",{key:"listItem"+t},e)})))),!n.isLibraryConnected&&l.default.createElement("div",{className:"e-onboarding__footnote"},l.default.createElement("p",null,r("Already have one?","elementor")+" ",l.default.createElement("a",{ref:C,href:elementorAppConfig.onboarding.urls.connect+elementorAppConfig.onboarding.utms.connectCtaLink,onClick:function onClick(){elementorCommon.events.dispatchEvent({event:"connect account",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement}})}},r("Connect your account","elementor"))),l.default.createElement(s.default,{buttonRef:C,successCallback:P,errorCallback:w})))};var l=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==a(e)&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var r={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var l in e)if("default"!==l&&Object.prototype.hasOwnProperty.call(e,l)){var i=o?Object.getOwnPropertyDescriptor(e,l):null;i&&(i.get||i.set)?Object.defineProperty(r,l,i):r[l]=e[l]}r.default=e,n&&n.set(e,r);return r}(n(87363)),i=o(n(40131)),c=n(50927),u=n(33959),s=o(n(94170)),d=o(n(8915)),p=o(n(61961));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?n:t})(e)}},40088:(e,t,n)=>{var r=n(38003).__,o=n(73203),a=n(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function ChooseFeatures(){var e,t=(0,l.useContext)(c.OnboardingContext).state,n={advanced:r("Advanced","elementor"),essential:r("Essential","elementor")},o=(0,l.useState)({essential:[],advanced:[]}),a=(0,i.default)(o,2),f=a[0],m=a[1],g=(0,l.useState)(n.essential),v=(0,i.default)(g,2),b=v[0],_=v[1],h="chooseFeatures",y={text:r("Upgrade Now","elementor"),href:elementorAppConfig.onboarding.urls.upgrade,target:"_blank",onClick:function onClick(){elementorCommon.events.dispatchEvent({event:"next",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:t.currentStep}})}};"completed"!==t.steps[h]&&(e={text:r("Skip","elementor")});isFeatureSelected(f)||(y.className="e-onboarding__button--disabled");function isFeatureSelected(e){return!!e.advanced.length||!!e.essential.length}return(0,l.useEffect)((function(){f.advanced.length>0?_(n.advanced):_(n.essential)}),[f]),l.default.createElement(d.default,{pageId:h,nextStep:"goodToGo"},l.default.createElement(p.default,{image:elementorCommon.config.urls.assets+"images/app/onboarding/Illustration_Setup.svg",title:r("Elevate your website with additional Pro features.","elementor"),actionButton:y,skipButton:e},l.default.createElement("p",null,r("Which Elementor Pro features do you need to bring your creative vision to life?","elementor")),l.default.createElement("form",{className:"e-onboarding__choose-features-section"},s.options.map((function(e,t){var n="".concat(e.plan,"-").concat(t);return l.default.createElement("label",{key:n,className:"e-onboarding__choose-features-section__label",htmlFor:n},l.default.createElement("input",{className:"e-onboarding__choose-features-section__checkbox",type:"checkbox",onChange:function onChange(t){return(0,s.setSelectedFeatureList)({checked:t.currentTarget.checked,id:t.target.value,text:e.text,selectedFeatures:f,setSelectedFeatures:m})},id:n,value:n}),e.text)}))),l.default.createElement("p",{className:"e-onboarding__choose-features-section__message"},isFeatureSelected(f)&&l.default.createElement(u.default,{tier:b}))))};var l=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==a(e)&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var r={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var l in e)if("default"!==l&&Object.prototype.hasOwnProperty.call(e,l)){var i=o?Object.getOwnPropertyDescriptor(e,l):null;i&&(i.get||i.set)?Object.defineProperty(r,l,i):r[l]=e[l]}r.default=e,n&&n.set(e,r);return r}(n(87363)),i=o(n(40131)),c=n(33959),u=o(n(14118)),s=n(7043),d=o(n(8915)),p=o(n(61961));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?n:t})(e)}},79914:(e,t,n)=>{var r=n(38003).__,o=n(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function GoodToGo(){var e={text:r("Skip","elementor"),href:elementorAppConfig.onboarding.urls.createNewPage},t=elementorAppConfig.onboarding.urls.kitLibrary+"&referrer=onboarding";return a.default.createElement(c.default,{pageId:"goodToGo"},a.default.createElement("h1",{className:"e-onboarding__page-content-section-title"},elementorAppConfig.onboarding.experiment?r("Welcome aboard! What's next?","elementor"):r("That's a wrap! What's next?","elementor")),a.default.createElement("div",{className:"e-onboarding__page-content-section-text"},r("There are two ways to get started with Elementor:","elementor")),a.default.createElement(i.default,{container:!0,alignItems:"center",justify:"space-between",className:"e-onboarding__cards-grid e-onboarding__page-content"},a.default.createElement(u.default,{name:"blank",image:elementorCommon.config.urls.assets+"images/app/onboarding/Blank_Canvas.svg",imageAlt:r("Click here to create a new page and open it in Elementor Editor","elementor"),text:r("Edit a blank canvas with the Elementor Editor","elementor"),link:elementorAppConfig.onboarding.urls.createNewPage}),a.default.createElement(u.default,{name:"template",image:elementorCommon.config.urls.assets+"images/app/onboarding/Library.svg",imageAlt:r("Click here to go to Elementor's Kit Library","elementor"),text:r("Choose a professionally-designed template or import your own","elementor"),link:t,clickAction:function clickAction(){location.href=t,location.reload()}})),a.default.createElement(s.default,{skipButton:_objectSpread(_objectSpread({},e),{},{target:"_self"}),className:"e-onboarding__good-to-go-footer"}))};var a=o(n(87363)),l=o(n(93231)),i=o(n(67096)),c=o(n(8915)),u=o(n(32389)),s=o(n(3e4));function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach((function(t){(0,l.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}},92728:(e,t,n)=>{var r=n(38003).__,o=n(73203),a=n(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function HelloTheme(){var e=(0,l.useContext)(c.OnboardingContext),t=e.state,n=e.updateState,o=e.getStateObjectToUpdate,a=(0,s.default)(),f=a.ajaxState,m=a.setAjax,g=(0,l.useState)(!1),v=(0,i.default)(g,2),b=v[0],_=v[1],h=(0,l.useState)(!1),y=(0,i.default)(h,2),C=y[0],E=y[1],O={type:"success",icon:"eicon-check-circle-o",message:r("Your site’s got Hello theme. High-five!","elementor")},P=(0,l.useState)(t.isHelloThemeActivated?O:null),w=(0,i.default)(P,2),k=w[0],j=w[1],S=(0,l.useState)([]),x=(0,i.default)(S,2),N=x[0],W=x[1],R=t.isHelloThemeActivated?r("Next","elementor"):r("Continue with Hello Theme","elementor"),A=(0,l.useState)(R),T=(0,i.default)(A,2),M=T[0],q=T[1],L=(0,u.useNavigate)(),D="hello",B=elementorAppConfig.onboarding.experiment?"chooseFeatures":"siteName",I=function goToNextScreen(){return L("onboarding/"+B)};(0,l.useEffect)((function(){if(!b&&t.isHelloThemeActivated){var e=o(t,"steps",D,"completed");n(e),I()}}),[]);var F,H=function resetScreenContent(){N.forEach((function(e){return clearTimeout(e)})),W([]),E(!1),q(R)},U=(0,l.useCallback)((function(){E(!1),elementorCommon.events.dispatchEvent({event:"indication prompt",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:t.currentStep,action_state:"success",action:"hello theme activation"}}),j(O),q(r("Next","elementor"));var e=o(t,"steps",D,"completed");e.isHelloThemeActivated=!0,n(e),_(!0),I()}),[]),G=function activateHelloTheme(){E(!0),n({isHelloThemeInstalled:!0}),m({data:{action:"elementor_activate_hello_theme"}})},K=function installHelloTheme(){C||E(!0),wp.updates.ajax("install-theme",{slug:"hello-elementor",success:function success(){return G()},error:function error(){return function onErrorInstallHelloTheme(){elementorCommon.events.dispatchEvent({event:"indication prompt",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:t.currentStep,action_state:"failure",action:"hello theme install"}}),j({type:"error",icon:"eicon-warning",message:r("There was a problem installing Hello Theme.","elementor")}),H()}()}})},z=function sendNextButtonEvent(){elementorCommon.events.dispatchEvent({event:"next",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:t.currentStep}})},V={text:M,role:"button"};C&&(V.className="e-onboarding__button--processing");t.isHelloThemeActivated?V.onClick=function(){z(),I()}:V.onClick=function(){z(),t.isHelloThemeInstalled&&!t.isHelloThemeActivated?G():t.isHelloThemeInstalled?I():K()};"completed"!==t.steps[D]&&(F={text:r("Skip","elementor")});return(0,l.useEffect)((function(){C&&q(l.default.createElement(l.default.Fragment,null,l.default.createElement("i",{className:"eicon-loading eicon-animation-spin","aria-hidden":"true"})));var e=[],t=setTimeout((function(){C&&q(l.default.createElement(l.default.Fragment,null,l.default.createElement("i",{className:"eicon-loading eicon-animation-spin","aria-hidden":"true"}),l.default.createElement("span",{className:"e-onboarding__action-button-text"},r("Hold on, this can take a minute...","elementor"))))}),4e3);e.push(t);var n=setTimeout((function(){C&&q(l.default.createElement(l.default.Fragment,null,l.default.createElement("i",{className:"eicon-loading eicon-animation-spin","aria-hidden":"true"}),l.default.createElement("span",{className:"e-onboarding__action-button-text"},r("Okay, now we're really close...","elementor"))))}),3e4);e.push(n),W(e)}),[C]),(0,l.useEffect)((function(){var e;"initial"!==f.status&&("success"===f.status&&null!==(e=f.response)&&void 0!==e&&e.helloThemeActivated?U():"error"===f.status&&(elementorCommon.events.dispatchEvent({event:"indication prompt",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:t.currentStep,action_state:"failure",action:"hello theme activation"}}),j({type:"error",icon:"eicon-warning",message:r("There was a problem activating Hello Theme.","elementor")}),H()))}),[f.status]),l.default.createElement(d.default,{pageId:D,nextStep:B},l.default.createElement(p.default,{image:elementorCommon.config.urls.assets+"images/app/onboarding/Illustration_Hello.svg",title:r("Every site starts with a theme.","elementor"),actionButton:V,skipButton:F,noticeState:k},l.default.createElement("p",null,r("Hello is Elementor's official blank canvas theme optimized to build your website exactly the way you want.","elementor")),!elementorAppConfig.onboarding.experiment&&l.default.createElement("p",null,r("Here's why:","elementor")),l.default.createElement("ul",{className:"e-onboarding__feature-list"},l.default.createElement("li",null,r("Light-weight and fast loading","elementor")),l.default.createElement("li",null,r("Great for SEO","elementor")),l.default.createElement("li",null,r("Already being used by 1M+ web creators","elementor")))),l.default.createElement("div",{className:"e-onboarding__footnote"},"* "+r("You can switch your theme later on","elementor")))};var l=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==a(e)&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var r={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var l in e)if("default"!==l&&Object.prototype.hasOwnProperty.call(e,l)){var i=o?Object.getOwnPropertyDescriptor(e,l):null;i&&(i.get||i.set)?Object.defineProperty(r,l,i):r[l]=e[l]}r.default=e,n&&n.set(e,r);return r}(n(87363)),i=o(n(40131)),c=n(33959),u=n(50927),s=o(n(33105)),d=o(n(8915)),p=o(n(61961));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?n:t})(e)}},90013:(e,t,n)=>{var r=n(38003).__,o=n(73203),a=n(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function SiteLogo(){var e,t=(0,l.useContext)(c.OnboardingContext),n=t.state,o=t.updateState,a=t.getStateObjectToUpdate,g=(0,l.useState)(n.siteLogo.id?n.siteLogo:null),v=(0,i.default)(g,2),b=v[0],_=v[1],h=(0,l.useState)(!1),y=(0,i.default)(h,2),C=y[0],E=y[1],O=(0,l.useState)(!1),P=(0,i.default)(O,2),w=P[0],k=P[1],j=(0,l.useState)(),S=(0,i.default)(j,2),x=S[0],N=S[1],W=(0,l.useState)(null),R=(0,i.default)(W,2),A=R[0],T=R[1],M=(0,s.default)(),q=M.ajaxState,L=M.setAjax,D=(0,s.default)(),B=D.ajaxState,I=D.setAjax,F="siteLogo",H="goodToGo",U=(0,u.useNavigate)(),G={role:"button",onClick:function onClick(){if(elementorCommon.events.dispatchEvent({event:"next",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:n.currentStep}}),b.id)if(b.id!==n.siteLogo.id)K();else{var e=a(n,"steps",F,"completed");o(e),U("onboarding/"+H)}}};"completed"!==n.steps[F]&&(e={text:r("Skip","elementor")});G.text=C?l.default.createElement(l.default.Fragment,null,l.default.createElement("i",{className:"eicon-loading eicon-animation-spin","aria-hidden":"true"})):r("Next","elementor");b||(G.className="e-onboarding__button--disabled");var K=(0,l.useCallback)((function(){E(!0),L({data:{action:"elementor_update_site_logo",data:JSON.stringify({attachmentId:b.id})}})}),[b]),z=function uploadSiteLogo(e){E(!0),I({data:{action:"elementor_upload_site_logo",fileToUpload:e}})},V=function dismissUnfilteredFilesCallback(){E(!1),_(null),k(!1)};return(0,l.useEffect)((function(){var e,t;"initial"!==B.status&&("success"===B.status&&null!==(e=B.response)&&void 0!==e&&null!==(t=e.imageAttachment)&&void 0!==t&&t.id?(elementorCommon.events.dispatchEvent({event:"logo image uploaded",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,source:x}}),E(!1),_(B.response.imageAttachment),A&&T(null)):"error"===B.status&&(E(!1),_(null),elementorCommon.events.dispatchEvent({event:"indication prompt",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,action_state:"failure",action:"logo image upload"}}),T({type:"error",icon:"eicon-warning",message:"That didn't work. Try uploading your file again."})))}),[B.status]),(0,l.useEffect)((function(){var e;if("initial"!==q.status)if("success"===q.status&&null!==(e=q.response)&&void 0!==e&&e.siteLogoUpdated){elementorCommon.events.dispatchEvent({event:"logo image updated",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,source:x}}),E(!1),A&&T(null);var t=a(n,"steps",F,"completed");t.siteLogo={id:b.id,url:b.url},o(t),U("onboarding/"+H)}else"error"===q.status&&(E(!1),elementorCommon.events.dispatchEvent({event:"indication prompt",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:n.currentStep,action_state:"failure",action:"update site logo"}}),T({type:"error",icon:"eicon-warning",message:"That didn't work. Try uploading your file again."}))}),[q.status]),l.default.createElement(f.default,{pageId:F,nextStep:H},l.default.createElement(m.default,{image:elementorCommon.config.urls.assets+"images/app/onboarding/Illustration_Setup.svg",title:r("Have a logo? Add it here.","elementor"),actionButton:G,skipButton:e,noticeState:A},l.default.createElement("span",null,r("Otherwise, you can skip this and add one later.","elementor")),b&&!w?l.default.createElement("div",{className:"e-onboarding__logo-container"+(C?" e-onboarding__is-uploading":"")},l.default.createElement("div",{className:"e-onboarding__logo-remove",onClick:function onClick(){return function onImageRemoveClick(){elementorCommon.events.dispatchEvent({event:"remove selected logo",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement}}),_(null)}()}},l.default.createElement("i",{className:"eicon-trash-o"})),l.default.createElement("img",{src:b.url,alt:r("Potential Site Logo","elementor")})):l.default.createElement(l.default.Fragment,null,l.default.createElement(d.default,{className:"e-onboarding__drop-zone",heading:r("Drop image here","elementor"),secondaryText:r("or","elementor"),buttonText:r("Open Media Library","elementor"),buttonVariant:"outlined",buttonColor:"cta",icon:"",type:"wp-media",filetypes:["jpg","jpeg","png","svg"],onFileSelect:function onFileSelect(e){return function onFileSelect(e){N("drop"),"image/svg+xml"!==e.type||elementorAppConfig.onboarding.isUnfilteredFilesEnabled?(_(e),T(null),z(e)):(_(e),E(!0),k(!0))}(e)},onWpMediaSelect:function onWpMediaSelect(e){var t=e.state().get("selection").first().toJSON();N("browse"),_(t),T(null)},onButtonClick:function onButtonClick(){elementorCommon.events.dispatchEvent({event:"browse file click",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:n.currentStep}})},onError:function onError(e){"file_not_allowed"===e.id&&(elementorCommon.events.dispatchEvent({event:"indication prompt",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:n.currentStep,action_state:"failure",action:"logo upload format"}}),T({type:"error",icon:"eicon-warning",message:r("This file type is not supported. Try a different type of file","elementor")}))}})),l.default.createElement(p.default,{show:w,setShow:k,confirmModalText:r("This allows Elementor to scan your SVGs for malicious content. If you do not wish to allow this, use a different image format.","elementor"),errorModalText:r("There was a problem with enabling SVG uploads. Try again, or use another image format.","elementor"),onReady:function onReady(){k(!1),elementorAppConfig.onboarding.isUnfilteredFilesEnabled=!0,z(b)},onDismiss:function onDismiss(){return V()},onCancel:function onCancel(){return V()}})))};var l=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==a(e)&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var r={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var l in e)if("default"!==l&&Object.prototype.hasOwnProperty.call(e,l)){var i=o?Object.getOwnPropertyDescriptor(e,l):null;i&&(i.get||i.set)?Object.defineProperty(r,l,i):r[l]=e[l]}r.default=e,n&&n.set(e,r);return r}(n(87363)),i=o(n(40131)),c=n(33959),u=n(50927),s=o(n(33105)),d=o(n(46218)),p=o(n(31794)),f=o(n(8915)),m=o(n(61961));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?n:t})(e)}},78270:(e,t,n)=>{var r=n(38003).__,o=n(73203),a=n(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function SiteName(){var e,t=(0,l.useContext)(c.OnboardingContext),n=t.state,o=t.updateState,a=t.getStateObjectToUpdate,f=(0,s.default)(),m=f.ajaxState,g=f.setAjax,v=(0,l.useState)(null),b=(0,i.default)(v,2),_=b[0],h=b[1],y=(0,l.useState)(n.siteName),C=(0,i.default)(y,2),E=C[0],O=C[1],P="siteName",w="siteLogo",k=(0,u.useNavigate)(),j=(0,l.useRef)(),S={text:r("Next","elementor"),onClick:function onClick(){if(elementorCommon.events.dispatchEvent({event:"next",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:n.currentStep}}),j.current.value!==n.siteName&&""!==j.current.value)g({data:{action:"elementor_update_site_name",data:JSON.stringify({siteName:j.current.value})}});else if(j.current.value===n.siteName){var e=a(n,"steps",P,"completed");o(e),k("onboarding/"+w)}else{var t=a(n,"steps",P,"skipped");o(t),k("onboarding/"+w)}}};"completed"!==n.steps[P]&&(e={text:r("Skip","elementor")});E||(S.className="e-onboarding__button--disabled");return(0,l.useEffect)((function(){var e;if("initial"!==m.status)if("success"===m.status&&null!==(e=m.response)&&void 0!==e&&e.siteNameUpdated){var t=a(n,"steps",P,"completed");t.siteName=j.current.value,o(t),k("onboarding/"+w)}else"error"===m.status&&(elementorCommon.events.dispatchEvent({event:"indication prompt",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:n.currentStep,action_state:"failure",action:"site name update"}}),h({type:"error",icon:"eicon-warning",message:r("Sorry, the name wasn't saved. Try again, or skip for now.","elementor")}))}),[m.status]),l.default.createElement(d.default,{pageId:P,nextStep:w},l.default.createElement(p.default,{image:elementorCommon.config.urls.assets+"images/app/onboarding/Illustration_Setup.svg",title:r("Now, let's give your site a name.","elementor"),actionButton:S,skipButton:e,noticeState:_},l.default.createElement("p",null,r("This is what your site is called on the WP dashboard, and can be changed later from the general settings - it's not your website's URL.","elementor")),l.default.createElement("input",{className:"e-onboarding__text-input e-onboarding__site-name-input",type:"text",placeholder:"e.g. Eric's Space Shuttles",defaultValue:n.siteName||"",ref:j,onChange:function onChange(e){return O(e.target.value)}})))};var l=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==a(e)&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var r={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var l in e)if("default"!==l&&Object.prototype.hasOwnProperty.call(e,l)){var i=o?Object.getOwnPropertyDescriptor(e,l):null;i&&(i.get||i.set)?Object.defineProperty(r,l,i):r[l]=e[l]}r.default=e,n&&n.set(e,r);return r}(n(87363)),i=o(n(40131)),c=n(33959),u=n(50927),s=o(n(33105)),d=o(n(8915)),p=o(n(61961));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?n:t})(e)}},3902:(e,t,n)=>{var r=n(38003).__,o=n(73203),a=n(7501);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function UploadAndInstallPro(){(0,u.default)({title:r("Upload and Install Elementor Pro","elementor")});var e=(0,l.useContext)(f.OnboardingContext).state,t=(0,c.default)(),n=t.ajaxState,o=t.setAjax,a=(0,l.useState)(null),g=(0,i.default)(a,2),v=g[0],b=g[1],_=(0,l.useState)(!1),h=(0,i.default)(_,2),y=h[0],C=h[1],E=(0,l.useState)(),O=(0,i.default)(E,2),P=O[0],w=O[1],k=(0,l.useCallback)((function(e){C(!0),o({data:{action:"elementor_upload_and_install_pro",fileToUpload:e}})}),[]),j=function setErrorNotice(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"upload",r=(null==t?void 0:t.message)||"That didn't work. Try uploading your file again.";elementorCommon.events.dispatchEvent({event:"indication prompt",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:e.currentStep,action_state:"failure",action:n+" pro",source:P}}),b({type:"error",icon:"eicon-warning",message:r})};(0,l.useEffect)((function(){var t;"initial"!==n.status&&(C(!1),"success"===n.status&&null!==(t=n.response)&&void 0!==t&&t.elementorProInstalled?(elementorCommon.events.dispatchEvent({event:"pro uploaded",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:e.currentStep,source:P}}),opener&&opener!==window&&(opener.jQuery("body").trigger("elementor/upload-and-install-pro/success"),window.close(),opener.focus())):"error"===n.status&&j("install"))}),[n.status]);if(y)return l.default.createElement(m.default,{loadingText:r("Uploading","elementor")});return l.default.createElement("div",{className:"eps-app e-onboarding__upload-pro"},l.default.createElement(s.default,null,l.default.createElement(d.default,{className:"e-onboarding__upload-pro-drop-zone",onFileSelect:function onFileSelect(e,t,n){w(n),k(e)},onError:function onError(e){return j(e,"upload")},filetypes:["zip"],buttonColor:"cta",buttonVariant:"contained",heading:r("Import your Elementor Pro plugin file","elementor"),text:r("Drag & Drop your .zip file here","elementor"),secondaryText:r("or","elementor"),buttonText:r("Browse","elementor")}),v&&l.default.createElement(p.default,{noticeState:v}),l.default.createElement("div",{className:"e-onboarding__upload-pro-get-file"},r("Don't know where to get the file from?","elementor")+" ",l.default.createElement("a",{onClick:function onClick(){return function onProUploadHelpLinkClick(){elementorCommon.events.dispatchEvent({event:"pro plugin upload help",version:"",details:{placement:elementorAppConfig.onboarding.eventPlacement,step:e.currentStep}})}()},href:"https://my.elementor.com/subscriptions/"+elementorAppConfig.onboarding.utms.downloadPro,target:"_blank"},r("Click here","elementor")))))};var l=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==a(e)&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var r={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var l in e)if("default"!==l&&Object.prototype.hasOwnProperty.call(e,l)){var i=o?Object.getOwnPropertyDescriptor(e,l):null;i&&(i.get||i.set)?Object.defineProperty(r,l,i):r[l]=e[l]}r.default=e,n&&n.set(e,r);return r}(n(87363)),i=o(n(40131)),c=o(n(33105)),u=o(n(78845)),s=o(n(88138)),d=o(n(46218)),p=o(n(54041)),f=n(33959),m=o(n(87206));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?n:t})(e)}},94170:(e,t,n)=>{var r=n(23615);Object.defineProperty(t,"__esModule",{value:!0}),t.default=Connect;var o=n(87363),a=n(33959);function Connect(e){var t=(0,o.useContext)(a.OnboardingContext),n=t.state,r=t.updateState,l=t.getStateObjectToUpdate;return(0,o.useEffect)((function(){jQuery(e.buttonRef.current).elementorConnect({success:function success(t){return e.successCallback?e.successCallback(t):function connectSuccessCallback(e){var t=l(n,"steps","account","completed");elementorCommon.config.library_connect.is_connected=!0,elementorCommon.config.library_connect.current_access_level=e.kits_access_level||e.access_level||0,elementorCommon.config.library_connect.current_access_tier=e.access_tier,t.isLibraryConnected=!0,r(t)}(t)},error:function error(){e.errorCallback&&e.errorCallback()},popup:{width:726,height:534}})}),[]),null}Connect.propTypes={buttonRef:r.object.isRequired,successCallback:r.func,errorCallback:r.func}},7043:(e,t,n)=>{var r=n(38003).__,o=n(73203);Object.defineProperty(t,"__esModule",{value:!0}),t.setSelectedFeatureList=t.options=void 0;var a=o(n(9833)),l=o(n(93231));function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach((function(t){(0,l.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var i=[{plan:"essential",text:r("Templates & Theme Builder","elementor")},{plan:"advanced",text:r("WooCommerce Builder","elementor")},{plan:"essential",text:r("Lead Collection & Form Builder","elementor")},{plan:"essential",text:r("Dynamic Content","elementor")},{plan:"advanced",text:r("Popup Builder","elementor")},{plan:"advanced",text:r("Custom Code & CSS","elementor")},{plan:"essential",text:r("Motion Effects & Animations","elementor")},{plan:"advanced",text:r("Notes & Collaboration","elementor")}];t.options=i;t.setSelectedFeatureList=function setSelectedFeatureList(e){var t=e.checked,n=e.id,r=e.text,o=e.selectedFeatures,i=e.setSelectedFeatures,c=n.split("-")[0];i(_objectSpread(_objectSpread({},o),{},t?(0,l.default)({},c,[].concat((0,a.default)(o[c]),[r])):(0,l.default)({},c,o[c].filter((function(e){return e!==r})))))}}}]);