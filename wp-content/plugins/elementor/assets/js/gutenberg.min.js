/*! elementor - v3.23.0 - 05-08-2024 */
(()=>{"use strict";var e,t,n,o={38003:e=>{e.exports=wp.i18n}},i={};function __webpack_require__(e){var t=i[e];if(void 0!==t)return t.exports;var n=i[e]={exports:{}};return o[e](n,n.exports,__webpack_require__),n.exports}n=__webpack_require__(38003).__,e=jQuery,t={cacheElements:function cacheElements(){var t=this;t.isElementorMode=ElementorGutenbergSettings.isElementorMode,t.cache={},t.cache.$gutenberg=e("#editor"),t.cache.$switchMode=e(e("#elementor-gutenberg-button-switch-mode").html()),t.cache.$switchModeButton=t.cache.$switchMode.find("#elementor-switch-mode-button"),t.bindEvents(),t.toggleStatus(),wp.data.subscribe((function(){setTimeout((function(){t.buildPanel()}),1)}))},buildPanel:function buildPanel(){var t=this;if(t.cache.$gutenberg.find("#elementor-switch-mode").length||t.cache.$gutenberg.find(".edit-post-header-toolbar").append(t.cache.$switchMode),this.hasIframe()&&this.handleIframe(),!e("#elementor-editor").length){t.cache.$editorPanel=e(e("#elementor-gutenberg-panel").html());var n=t.cache.$gutenberg.find(".block-editor-writing-flow");n.length||(n=t.cache.$gutenberg.find(".is-desktop-preview")),t.cache.$gurenbergBlockList=n,t.cache.$gurenbergBlockList.append(t.cache.$editorPanel),t.cache.$editorPanelButton=t.cache.$editorPanel.find("#elementor-go-to-edit-page-link"),t.cache.$editorPanelButton.on("click",(function(e){e.preventDefault(),t.handleEditButtonClick()}))}},handleIframe:function handleIframe(){this.hideIframeContent(),this.buildPanelTopBar()},hasIframe:function hasIframe(){return!!this.cache.$gutenberg.find('iframe[name="editor-canvas"]').length},hideIframeContent:function hideIframeContent(){this.isElementorMode&&this.cache.$gutenberg.find('iframe[name="editor-canvas"]').contents().find("body").append("<style>\n\t\t\t\t.editor-post-text-editor,\n\t\t\t\t.block-editor-block-list__layout {\n\t\t\t\t\tdisplay: none;\n\t\t\t\t}\n\n\t\t\t\tbody {\n\t\t\t\t\tpadding: 0 !important;\n\t\t\t\t}\n\t\t\t</style>")},buildPanelTopBar:function buildPanelTopBar(){var t=this;!e("#elementor-edit-mode-button").length&&this.isElementorMode&&(t.cache.$editorBtnTop=e(e("#elementor-gutenberg-button-tmpl").html()),t.cache.$gutenberg.find(".edit-post-header-toolbar").append(t.cache.$editorBtnTop),e("#elementor-edit-mode-button").on("click",(function(e){e.preventDefault(),t.handleEditButtonClick(!1)})))},handleEditButtonClick:function handleEditButtonClick(){(!(arguments.length>0&&void 0!==arguments[0])||arguments[0])&&this.animateLoader(),"auto-draft"===wp.data.select("core/editor").getCurrentPost().status&&(wp.data.select("core/editor").getEditedPostAttribute("title")||wp.data.dispatch("core/editor").editPost({title:"Elementor #"+e("#post_ID").val()}),wp.data.dispatch("core/editor").savePost()),this.redirectWhenSave()},bindEvents:function bindEvents(){var e=this;e.cache.$switchModeButton.on("click",(function(){e.isElementorMode?elementorCommon.dialogsManager.createWidget("confirm",{message:n("Please note that you are switching to WordPress default editor. Your current layout, design and content might break.","elementor"),headerMessage:n("Back to WordPress Editor","elementor"),strings:{confirm:n("Continue","elementor"),cancel:n("Cancel","elementor")},defaultOption:"confirm",onConfirm:function onConfirm(){var t=wp.data.dispatch("core/editor");t.editPost({gutenberg_elementor_mode:!1}),t.savePost(),e.isElementorMode=!e.isElementorMode,e.toggleStatus()}}).show():(e.isElementorMode=!e.isElementorMode,e.toggleStatus(),e.cache.$editorPanelButton.trigger("click"))}))},redirectWhenSave:function redirectWhenSave(){var e=this;setTimeout((function(){wp.data.select("core/editor").isSavingPost()?e.redirectWhenSave():location.href=ElementorGutenbergSettings.editLink}),300)},animateLoader:function animateLoader(){this.cache.$editorPanelButton.addClass("elementor-animate")},toggleStatus:function toggleStatus(){jQuery("body").toggleClass("elementor-editor-active",this.isElementorMode).toggleClass("elementor-editor-inactive",!this.isElementorMode)},init:function init(){this.cacheElements()}},e((function(){t.init()}))})();