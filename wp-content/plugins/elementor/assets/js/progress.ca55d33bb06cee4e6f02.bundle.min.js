/*! elementor - v3.14.0 - 26-06-2023 */
"use strict";(self.webpackChunkelementor=self.webpackChunkelementor||[]).push([[192],{1351:(e,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.default=void 0;class Progress extends elementorModules.frontend.handlers.Base{getDefaultSettings(){return{selectors:{progressNumber:".elementor-progress-bar"}}}getDefaultElements(){const e=this.getSettings("selectors");return{$progressNumber:this.$element.find(e.progressNumber)}}onInit(){super.onInit(),elementorFrontend.waypoint(this.elements.$progressNumber,(()=>{const e=this.elements.$progressNumber;e.css("width",e.data("max")+"%")}))}}s.default=Progress}}]);