{"translation-revision-date": "2023-12-04 13:18:54+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=6; plural=(n == 0) ? 0 : ((n == 1) ? 1 : ((n == 2) ? 2 : ((n % 100 >= 3 && n % 100 <= 10) ? 3 : ((n % 100 >= 11 && n % 100 <= 99) ? 4 : 5))));", "lang": "ar"}, "Archive": ["الأرشيف"], "Single Post": ["مقال فردي"], "Single Page": ["صفحة فردية"], "Footer": ["التذييل"], "Header": ["الترويسة"], "Error:": ["خطأ:"], "What is a 404 Page Template?": ["ماهو قالب صفحة الخطأ 404 ؟"], "Products Archive": ["أر<PERSON>ي<PERSON> المنتجات"], "What is a Products Archive Template?": ["ماهو قالب أرشيف المنتجات ؟"], "What is a Single Product Template?": ["ماهو قالب المنتج الفردي ؟"], "What is an Archive Template?": ["ماهو قالب الارشيف ؟"], "What is a Footer Template?": ["ماهو قالب التذييل ؟"], "What is a Single Page Template?": ["ماهو قالب الصفحة الفردية ؟"], "What is a Single Post Template?": ["ماهو قالب التدوينة الفردية ؟"], "What is a Header Template?": ["ماهو قالب الترويسة ؟"], "App could not be loaded": ["لم يتم التمكن من تحميل التطبيق"], "A 404 page template allows you to easily design the layout and style of the page that is displayed when a visitor arrives at a page that does not exist.": ["قالب صفحة 404 يسمح لك بسهولة بتصميم تخطيط وشكل الصفحة التي يتم عرضها حينما يصل الزائر إلى صفحة غير موجودة."], "What is a Search Results Template?": ["ماهو قالب نتائج البحث؟"], "Product": ["منتج"], "Keep your site's visitors happy when they get lost by displaying your recent posts, a search bar, or any information that might help the user find what they were looking for.": ["قم بالحفاظ على زوار موقعك سعداء عندما يتيهون عند عرض أحدث مقالاتك, شريط بحث, أو أية معلومات يمكن أن تساعد المستخدمين على العثور عما كانوا يبحثون عنه."], "You can create multiple archive product templates, and assign each to different categories of products. This gives you the freedom to customize the appearance for each type of product being shown.": ["يمكنك إنشاء قوالب منتج لأرشيفات متعددة, وتعيين كل منها لتصنيفات مختلفة من المنتجات. وهذا يمنحك الحرية لتخصيص المظهر لنمط كل منتج يتم عرضه."], "A products archive template allows you to easily design the layout and style of your WooCommerce shop page or other product archive pages - those pages that show a list of products, which may be filtered by terms such as categories, tags, etc.": ["قالب أرشيف المنتجات يسمح لك بسهولة بتصميم تخطيط وشكل صفحة متجر ووكومرس الخاص بك أو صفحات أرشيف المنتج الأُخرى -تلك الصفحات التي تعرض قائمة من المنتجات, والتي يمكن فرزها عبر شروط مثل التصنيفات, الوسوم, إلخ..."], "You can create multiple single product templates, and assign each to different types of products, enabling a custom design for each group of similar products.": ["يمكنك إنشاء قوالب منتج فردي متعددة, وتعيين كل واحد منها لأنماط مختلفة من المنتجات, القيام بتمكين تصميم مخصص لكل مجموعة من المنتجات المتشابهة."], "A single product template allows you to easily design the layout and style of WooCommerce single product pages, and apply that template to various conditions that you assign.": ["قالب مُنتج فردي يسمح لك بسهولة بتصميم تخطيط وشكل صفحات المنتج الفردي لووكومرس, وتطبيق ذلك القالب إلى شروط متنوّعة والتي تقوم أنت بتعيينها."], "You can customize the message if there are no results for the search term.": ["يمكنك تخصيص الرسالة في حال عدم وجود نتائج لشرط البحث."], "You can easily control the layout and design of the Search Results page with the Search Results template, which is simply a special archive template just for displaying search results.": ["يمكنك التحكم بسهولة بتخطيط وتصميم صفحة نتائج البحث عن طريق قالب نتائج البحث, حيث أنه ببساطة عبارة عن قالب أرشيف خاص فقط من أجل عرض نتائج البحث."], "If you’d like a different style for a specific category, it’s easy to create a separate archive template whose condition is to only display when users are viewing that category’s list of posts.": ["إذا كنت ترغب بمظهر مختلف من أجل تصنيف محدد, فإنه من السهل إنشاء قالب أرشيف منفصل والذي شرطه هو فقط أن يتم عرضه عندما يقوم المستخدمون بعرض قائمة مقالات ذلك التصنيف ."], "Tip": ["تلميح"], "Site Parts": ["أجزاء الموقع"], "We’re sorry, but something went wrong. Click on ‘Learn more’ and follow each of the steps to quickly solve it.": ["نحن آسفون, لكن حدثت مشكلة. أنقر على ‘معرفة المزيد’ واتّبع كافة الخطوات لحلها بشكل سريع."], "Theme Builder could not be loaded": ["لا يمكن تحميل مصمم القوالب"], "All Parts": ["كافة الأجزاء"], "Watch Video": ["مشاهدة الفيديو"], "Not Found": ["لا يوجد"], "Theme Builder": ["مصمم القوالب"], "Continue": ["المتابعة"], "Skip": ["تخطي"], "Something went wrong.": ["لقد حدث شئ خاطئ"], "Add New": ["اضافة جديد"], "Select File": ["اختيار ملف"], "Enable": ["تفعيل"], "Close": ["إغلاق"], "Learn More": ["معرفة المزيد"], "Elementor": ["<PERSON><PERSON><PERSON>"], "Loading": ["جاري التحميل"], "Go Back": ["الرجوع للخلف"], "Info": ["معلومات"]}}, "comment": {"reference": "assets/js/app-packages.js"}}