# Translation of Plugins - Elementor Website Builder &#8211; More than Just a Page Builder - Stable (latest release) in Arabic
# This file is distributed under the same license as the Plugins - Elementor Website Builder &#8211; More than Just a Page Builder - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2023-12-04 13:18:54+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=6; plural=(n == 0) ? 0 : ((n == 1) ? 1 : ((n == 2) ? 2 : ((n % 100 >= 3 && n % 100 <= 10) ? 3 : ((n % 100 >= 11 && n % 100 <= 99) ? 4 : 5))));\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: ar\n"
"Project-Id-Version: Plugins - Elementor Website Builder &#8211; More than Just a Page Builder - Stable (latest release)\n"

#: modules/generator-tag/module.php:84
msgid "A generator tag is a meta element that indicates the attributes used to create a webpage. It is used for analytical purposes."
msgstr "مولدبطاقة شعارهو عنصر تعريف يشير إلى السمات المستخدمة لإنشاء صفحة ويب. يتم استخدامه لأغراض تحليلية."

#: core/experiments/manager.php:642
msgid "Activate All"
msgstr "تنشيط الكل"

#: core/experiments/manager.php:643
msgid "Deactivate All"
msgstr "قم بإلغاء تنشيط الكل"

#: core/experiments/manager.php:433
msgid "Prepare your website for future improvements to carousel features by upgrading the Swiper library integrated into your site from v5.36 to v8.45. This experiment includes markup changes so it might require updating custom code and cause compatibility issues with third party plugins."
msgstr "قم بإعداد موقع الويب الخاص بك للتحسينات المستقبلية لميزات الرف الدائري عن طريق ترقية مكتبة Swiper المدمجة في موقعك من الإصدار 5.36 إلى الإصدار 8.45. تتضمن هذه التجربة تغييرات في الترميز ، لذا فقد تتطلب تحديث رمز مخصص وتتسبب في حدوث مشكلات في التوافق مع المكونات الإضافية التابعة لجهات خارجية."

#: modules/nested-tabs/widgets/nested-tabs.php:323
msgid "Align Title"
msgstr "محاذاة العنوان"

#: modules/nested-accordion/widgets/nested-accordion.php:411
#: modules/nested-tabs/widgets/nested-tabs.php:450
msgid "Distance from content"
msgstr "المسافة من المحتوى"

#: includes/settings/settings.php:351
msgid "Google Fonts"
msgstr "خطوط جوجل"

#: modules/lazyload/module.php:25
msgid "Lazy loading images that are not in the viewport improves initial page load performance and user experience. By activating this experiment all background images except the first one on your page will be lazy loaded to improve your LCP score"
msgstr "الصور البطيئة التي يتم تحميلها غير الموجودة في منفذ العرض تعمل على تحسين أداء تحميل الصفحة الأولي ومستخدمخبرة. من خلال تنشيط هذه التجربة ، سيتم تحميل جميع صور الخلفية باستثناء الصورة الأولى على صفحتك بشكل كسول لتحسين درجة LCP الخاصة بك"

#: core/admin/admin.php:968 modules/apps/admin-apps-page.php:111
#: modules/apps/admin-apps-page.php:144
#: modules/home/<USER>/filter-plugins.php:82 assets/js/admin.js:805
msgid "Activate"
msgstr "تفعيل"

#: core/settings/editor-preferences/model.php:208
msgid "All Posts"
msgstr "جميع المقالات"

#: includes/editor-templates/hotkeys.php:191 assets/js/notes.js:140
#: assets/js/notes.js:144 assets/js/notes.js:234
msgid "Notes"
msgstr "ملاحظات"

#: modules/library/documents/page.php:65
msgid "Add New Page Template"
msgstr "أضف قالب صفحة جديد"

#: includes/base/element-base.php:974
msgid "Perspective"
msgstr "منظور"

#: includes/base/element-base.php:848
msgid "Transform"
msgstr "تحويل"

#: includes/base/element-base.php:1333
msgid "Y Anchor Point"
msgstr "نقطة الإرساء Y"

#: includes/base/element-base.php:1305
msgid "X Anchor Point"
msgstr "نقطة الإرساء X"

#: includes/base/element-base.php:1188
msgid "Skew Y"
msgstr "إمالة Y"

#: includes/base/element-base.php:1166
msgid "Skew X"
msgstr "إمالة X"

#: includes/base/element-base.php:1154
msgid "Skew"
msgstr "إمالة"

#: includes/base/element-base.php:1130
msgid "Scale Y"
msgstr "مُواءَمة Y"

#: includes/base/element-base.php:1108
msgid "Scale X"
msgstr "مُواءَمة X"

#: includes/base/element-base.php:1075
msgid "Keep Proportions"
msgstr "الحفاظ على التناسُبات"

#: includes/base/element-base.php:1035
msgid "Offset Y"
msgstr "مُوازِن Y"

#: includes/base/element-base.php:1009
msgid "Offset X"
msgstr "مُوازِن X"

#: includes/base/element-base.php:951
msgid "Rotate Y"
msgstr "تدوير Y"

#: includes/base/element-base.php:928
msgid "Rotate X"
msgstr "تدوير X"

#: includes/base/element-base.php:912
msgid "3D Rotate"
msgstr "تدوير ثلاثي الأبعاد"

#: modules/promotions/admin-menu-items/custom-code-promotion-item.php:15
#: modules/promotions/admin-menu-items/custom-code-promotion-item.php:19
msgid "Custom Code"
msgstr "الشيفرة المخصصة"

#: includes/base/element-base.php:1231 includes/base/element-base.php:1235
msgid "Flip Vertical"
msgstr "القَلْب عمودياً"

#: includes/base/element-base.php:1212 includes/base/element-base.php:1216
msgid "Flip Horizontal"
msgstr "القَلْب أفقياً"

#: core/editor/promotion.php:31 assets/js/editor.js:6885 assets/js/notes.js:153
#: assets/js/styleguide.js:196
msgid "Connect & Activate"
msgstr "قم بالتوصيل & التنشيط"

#: includes/settings/tools.php:140
msgid "Not allowed to rollback versions"
msgstr "غير مسموح الرجوع إلى إصدارات أقدم"

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: modules/safe-mode/module.php:277 modules/safe-mode/module.php:290
msgid "%1$sClick here%2$s to troubleshoot"
msgstr "%1$sانقر هنا%2$s للتدقيق بحثاً عن الأخطاء"

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: modules/floating-buttons/module.php:362 modules/landing-pages/module.php:211
msgid "Or view %1$sTrashed Items%1$s"
msgstr "أو قم بعرض %1$sالعناصر المحذوفة%1$s"

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: modules/history/views/revisions-panel-template.php:31
msgid "Learn more about %1$sWordPress revisions%2$s"
msgstr "معرفة المزيد حول %1$sWordPress revisions%2$s"

#: core/kits/documents/tabs/settings-page-transitions.php:19
#: includes/managers/controls.php:1117
msgid "Page Transitions"
msgstr "الحركات الانتقالية للصفحة"

#: core/experiments/manager.php:373
msgid "Additional Custom Breakpoints"
msgstr "نقاط قطع مخصصة إضافية"

#. translators: %s: Device name.
#: includes/base/element-base.php:1380
msgid "Hide On %s"
msgstr "إخفاء على %s"

#: includes/elements/column.php:412 includes/elements/container.php:848
#: includes/elements/section.php:686 includes/widgets/heading.php:322
msgid "Lighten"
msgstr "تفتيح"

#: includes/elements/column.php:411 includes/elements/container.php:847
#: includes/elements/section.php:685 includes/widgets/heading.php:321
msgid "Darken"
msgstr "تعتيم"

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: includes/maintenance-mode.php:375
msgid "Select one or go ahead and %1$screate one%2$s now."
msgstr "قم باختيار واحد أو قم استمر و %1$s قُم بإنشاء واحد %2$s الآن."

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: includes/editor-templates/panel.php:223
msgid "You can enable it from the %1$sElementor settings page%2$s."
msgstr "يمكنك تفعيله من %1$s صفحة إعدادات Elementor %2$s."

#. translators: %1$s Span open tag, %2$s: Span close tag.
#: includes/admin-templates/new-template.php:52
msgid "Templates Help You %1$sWork Efficiently%2$s"
msgstr "القوالب تساعدك على %1$s العمل بكفاءة %2$s"

#. translators: 1: Link open tag, 2: Link close tag.
#: core/kits/documents/tabs/tab-base.php:80
msgid "In order for Theme Style to affect all relevant Elementor elements, please disable Default Colors and Fonts from the %1$sSettings Page%2$s."
msgstr "من أجل جعل ستايل القالب يؤثر على كل عناصر Elementor ذات الصلة به,يُرجى تعطيل الألوان والخطوط الافتراضية من %1$sصفحة الإعدادات%2$s."

#: includes/managers/controls.php:1135
msgid "Meet Page Transitions"
msgstr "تعرف على الحركات الانتقالية للصفحات"

#: includes/elements/column.php:419 includes/elements/container.php:852
#: includes/elements/section.php:690 includes/widgets/heading.php:329
msgid "Luminosity"
msgstr "اللَمَعان"

#: includes/elements/column.php:414 includes/elements/container.php:850
#: includes/elements/section.php:688 includes/widgets/heading.php:324
msgid "Saturation"
msgstr "التشبُّع اللوني"

#: includes/elements/column.php:413 includes/elements/container.php:849
#: includes/elements/section.php:687 includes/widgets/heading.php:323
msgid "Color Dodge"
msgstr "إنقاص كثافة اللون"

#: includes/elements/column.php:410 includes/elements/container.php:846
#: includes/elements/section.php:684 includes/widgets/heading.php:320
msgid "Overlay"
msgstr "غِشاء"

#: includes/elements/column.php:409 includes/elements/container.php:845
#: includes/elements/section.php:683 includes/widgets/heading.php:319
msgid "Screen"
msgstr "ترشيح لوني"

#: includes/elements/column.php:408 includes/elements/container.php:844
#: includes/elements/section.php:682 includes/widgets/heading.php:318
msgid "Multiply"
msgstr "مضاعفة لونية"

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: modules/safe-mode/module.php:306
msgid "Please note! We couldn't deactivate all of your plugins on Safe Mode. Please %1$sread more%2$s about this issue"
msgstr "لاحظ رجاءاً! لم نكن قادرين على تعطيل كل إضافاتك في الوضع الآمن. يرجى %1$sقراءة المزيد%2$s حول هذه المشكلة"

#: includes/managers/elements.php:320
#: assets/js/kit-library.ac1a4cd5deae5526ce49.bundle.js:3615
msgid "Favorites"
msgstr "المفضلة"

#: core/common/modules/finder/categories/settings.php:64
#: core/experiments/manager.php:582
#: assets/js/kit-library.ac1a4cd5deae5526ce49.bundle.js:3236
msgid "Features"
msgstr "المزايا"

#: includes/template-library/sources/admin-menu-items/templates-categories-menu-item.php:23
#: assets/js/kit-library.ac1a4cd5deae5526ce49.bundle.js:3229
msgid "Categories"
msgstr "التصنيفات"

#: modules/nested-accordion/widgets/nested-accordion.php:539
#: assets/js/app-packages.js:5378
msgid "Header"
msgstr "الترويسة"

#: includes/editor-templates/panel.php:314
#: includes/editor-templates/panel.php:316 assets/js/editor.js:13457
msgid "Color Sampler"
msgstr "مُنـتقي الألوان"

#: includes/settings/tools.php:295
msgid "Regenerate CSS & Data"
msgstr "إعادة توليد CSS & البيانات"

#: includes/settings/tools.php:298
msgid "Regenerate Files & Data"
msgstr "إعادة توليد الملفات & البيانات"

#: includes/settings/tools.php:299
msgid "Styles set in Elementor are saved in CSS files in the uploads folder and in the site’s database. Recreate those files and settings, according to the most recent settings."
msgstr "مجموعة الأنماط (Styles) في Elementor محفوظة في ملفات CSS في مجلد uploads وفي قاعدة بيانات الموقع. قم بإعادة إنشاء تلك الملفات والإعدادات، استناداً إلى أحدث الإعدادات الحاليّة."

#: core/experiments/manager.php:343
msgid "Please Note! The “Improved CSS Loading” mode reduces the amount of CSS code that is loaded on the page by default. When activated, the CSS code will be loaded, rather inline or in a dedicated file, only when needed. Activating this experiment may cause conflicts with incompatible plugins."
msgstr "لاحظ رجاءاً! نمط \"تحميل CSS المٌحسَّن\" يقوم بتخفيض كمية شيفرة CSS التي يتم تحميلها على الصفحة بشكل افتراضي. حينما يتم تفعيله, شيفرة CSS سيتم تحميلها, سواءاً بتضمينها سطرياً أو في ملف مخصّص, فقط عند الحاجة. تفعيل هذه الميزة الاختبارية قد يسبب تعارضات مع إضافات غير متوافقة."

#: modules/library/documents/section.php:47
msgid "Sections"
msgstr "الأقسام"

#: includes/settings/tools.php:412 includes/settings/tools.php:415
#: assets/js/editor.js:28845
msgid "Recreate Kit"
msgstr "إعادة إنشاء الحزمة"

#: includes/settings/tools.php:94
msgid "New kit have been created successfully"
msgstr "تم إنشاء الحزمة الجديدة بنجاح"

#: includes/settings/tools.php:89
msgid "An error occurred while trying to create a kit."
msgstr "حدث خطأ أثناء محاولة إنشاء حزمة."

#: includes/settings/tools.php:83
msgid "There's already an active kit."
msgstr "هنالك حزمة مفعّلة مسبقاً."

#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:15
#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:19
msgid "Submissions"
msgstr "الإدخالات"

#: core/experiments/manager.php:339
msgid "Improved CSS Loading"
msgstr "تحميل CSS المُحسَّن"

#: app/modules/kit-library/data/repository.php:147
#: app/modules/kit-library/data/repository.php:167
msgid "Kit not found"
msgstr "لم يتم العثور على الحُزمة"

#: app/modules/kit-library/data/kits/controller.php:29
msgid "Kit not exists."
msgstr "الحُزمة غير موجودة."

#: app/modules/kit-library/connect/kit-library.php:16
#: app/modules/kit-library/kit-library-menu-item.php:22
#: app/modules/kit-library/module.php:34 app/modules/kit-library/module.php:35
#: core/common/modules/finder/categories/general.php:78
#: assets/js/import-export-admin.js:315
#: assets/js/kit-library.ac1a4cd5deae5526ce49.bundle.js:1550
#: assets/js/kit-library.ac1a4cd5deae5526ce49.bundle.js:3668
#: assets/js/kit-library.ac1a4cd5deae5526ce49.bundle.js:4106
#: assets/js/kit-library.ac1a4cd5deae5526ce49.bundle.js:4407
msgid "Kit Library"
msgstr "مكتبة الحُزم"

#: includes/settings/tools.php:322 assets/js/app.js:8333 assets/js/app.js:9054
msgid "Important:"
msgstr "هام:"

#: modules/compatibility-tag/compatibility-tag-report.php:172
msgid "Compatibility unknown"
msgstr "التوافقية غير معروفة"

#: modules/compatibility-tag/compatibility-tag-report.php:171
msgid "Compatibility not specified"
msgstr "التوافقية غير محددة"

#: modules/compatibility-tag/compatibility-tag-report.php:170
msgid "Incompatible"
msgstr "غير متوافق"

#: modules/compatibility-tag/compatibility-tag-report.php:169
msgid "Compatible"
msgstr "متوافق"

#. translators: 1: Link open tag, 2: Link close tag
#: includes/settings/settings.php:304
msgid "Google Maps Embed API is a free service by Google that allows embedding Google Maps in your site. For more details, visit Google Maps' %1$sUsing API Keys%2$s page."
msgstr "API تضمين خرائط جوجل هي خدمة مجانية من جوجل تسمح بتضمين خرائط جوجل في موقعك. من اجل تفاصيل أكثر, قم بزيارة صفحة ' %1$sاستخدام مفاتيح API%2$s الخاصة بخرائط جوجل."

#: includes/settings/settings.php:300
msgid "Google Maps Embed API"
msgstr " تضمين خرائط جوجل API"

#: includes/settings/settings.php:311
msgid "API Key"
msgstr "مفتاح API"

#: includes/widgets/common.php:1090
#: modules/floating-buttons/base/widget-contact-button-base.php:2026
#: modules/floating-buttons/base/widget-contact-button-base.php:2117
#: modules/floating-buttons/base/widget-contact-button-base.php:2810
#: modules/link-in-bio/base/widget-link-in-bio-base.php:106
msgid "Round"
msgstr "تدوير"

#: includes/controls/groups/background.php:462 includes/widgets/common.php:1086
msgid "No-repeat"
msgstr "بدون تكرار"

#: includes/controls/groups/background.php:463 includes/widgets/common.php:1083
#: includes/widgets/common.php:1087
msgid "Repeat"
msgstr "تكرار"

#: includes/controls/groups/background.php:384 includes/widgets/common.php:1047
msgid "Y Position"
msgstr "الموضع Y"

#: includes/controls/groups/background.php:341 includes/widgets/common.php:1011
msgid "X Position"
msgstr "الموضع X"

#: includes/controls/groups/background.php:275
#: includes/controls/groups/background.php:327
#: includes/controls/groups/background.php:715 includes/widgets/common.php:997
#: includes/widgets/image.php:391
#: modules/link-in-bio/base/widget-link-in-bio-base.php:176
msgid "Bottom Right"
msgstr "أسفل اليمين"

#: includes/controls/groups/background.php:274
#: includes/controls/groups/background.php:326
#: includes/controls/groups/background.php:714 includes/widgets/common.php:996
#: includes/widgets/image.php:390
#: modules/link-in-bio/base/widget-link-in-bio-base.php:175
msgid "Bottom Left"
msgstr "أسفل اليسار"

#: includes/controls/groups/background.php:273
#: includes/controls/groups/background.php:325
#: includes/controls/groups/background.php:713 includes/widgets/common.php:995
#: includes/widgets/image.php:389
#: modules/link-in-bio/base/widget-link-in-bio-base.php:174
msgid "Bottom Center"
msgstr "أسفل الوسط"

#: includes/controls/groups/background.php:272
#: includes/controls/groups/background.php:324
#: includes/controls/groups/background.php:712 includes/widgets/common.php:994
#: includes/widgets/image.php:388
#: modules/link-in-bio/base/widget-link-in-bio-base.php:173
msgid "Top Right"
msgstr "أعلى اليمين"

#: includes/controls/groups/background.php:271
#: includes/controls/groups/background.php:323
#: includes/controls/groups/background.php:711 includes/widgets/common.php:993
#: includes/widgets/image.php:387
#: modules/link-in-bio/base/widget-link-in-bio-base.php:172
msgid "Top Left"
msgstr "أعلى اليسار"

#: includes/controls/groups/background.php:270
#: includes/controls/groups/background.php:322
#: includes/controls/groups/background.php:710 includes/widgets/common.php:992
#: includes/widgets/image.php:386
#: modules/link-in-bio/base/widget-link-in-bio-base.php:171
msgid "Top Center"
msgstr "أعلى الوسط"

#: includes/controls/groups/background.php:269
#: includes/controls/groups/background.php:321
#: includes/controls/groups/background.php:709 includes/widgets/common.php:991
#: includes/widgets/image.php:385
#: modules/link-in-bio/base/widget-link-in-bio-base.php:170
msgid "Center Right"
msgstr "أوسط اليمين"

#: includes/controls/groups/background.php:268
#: includes/controls/groups/background.php:320
#: includes/controls/groups/background.php:708 includes/widgets/common.php:990
#: includes/widgets/image.php:384
#: modules/link-in-bio/base/widget-link-in-bio-base.php:169
msgid "Center Left"
msgstr "أوسط اليسار"

#: includes/controls/groups/background.php:267
#: includes/controls/groups/background.php:319
#: includes/controls/groups/background.php:707 includes/widgets/common.php:989
#: includes/widgets/image.php:383
#: modules/link-in-bio/base/widget-link-in-bio-base.php:168
msgid "Center Center"
msgstr "منتصف الوسط"

#: includes/widgets/common.php:939
msgid "Fit"
msgstr "مُلائم"

#: includes/widgets/common.php:919
msgid "Need More Shapes?"
msgstr "هل أنت بحاجة للمزيد من الأشكال؟"

#: includes/widgets/common.php:866 includes/widgets/common.php:874
msgid "Mask"
msgstr "قناع"

#: includes/widgets/common.php:133
msgid "Hexagon"
msgstr "شكل سُداسي"

#: includes/widgets/common.php:132
msgid "Blob"
msgstr "فُقاعة"

#: includes/widgets/common.php:131
msgid "Triangle"
msgstr "مثلث"

#: includes/widgets/common.php:130
msgid "Sketch"
msgstr "رسم"

#: includes/widgets/common.php:129
msgid "Flower"
msgstr "زهرة"

#: includes/widgets/accordion.php:244 includes/widgets/toggle.php:247
#: modules/nested-accordion/widgets/nested-accordion.php:291
msgid "FAQ Schema"
msgstr "مخطط الأسئلة المتداولة"

#: includes/settings/settings.php:378
msgid "Set the way Google Fonts are being loaded by selecting the font-display property (Default: Auto)."
msgstr "قم بإعداد الطريقة التي يتم بها تحميل خطوط جوجل باختيار خاصية \"font-display\" (الافتراضي: تلقائي)."

#: includes/settings/settings.php:378
msgid "Font-display property defines how font files are loaded and displayed by the browser."
msgstr "خاصية \"font-display\" تقوم بتحديد كيفية تحميل وعرض ملفات الخط عبر المتصفح."

#: includes/settings/settings.php:376
msgid "Optional"
msgstr "إختياري"

#: includes/settings/settings.php:374
msgid "Swap"
msgstr "Swap"

#: includes/settings/settings.php:367
msgid "Google Fonts Load"
msgstr "تحميل خطوط جوجل"

#: includes/editor-templates/responsive-bar.php:63
msgid "Manage Breakpoints"
msgstr "إدارة نقاط القطع"

#: core/base/db-upgrades-manager.php:114
msgid "Database update process is running in the background. Taking a while?"
msgstr "عملية تحديث قاعدة البيانات جارية في الخلفية. هل تستغرق زمناً طويلاً ؟"

#: core/admin/admin-notices.php:326
msgid "With Elementor Pro, you can control user access and make sure no one messes up your design."
msgstr "مع Elementor Pro , تستطيع التحكم بوصول المستخدم والتأكد من أن لايقوم أحدهم بتخريب تصميمك."

#: core/admin/admin-notices.php:325
msgid "Managing a multi-user site?"
msgstr "إدارة موقع متعدد المستخدمين؟"

#: core/breakpoints/manager.php:339
msgid "Widescreen"
msgstr "الشاشة العريضة"

#: includes/settings/settings.php:373
msgid "Blocking"
msgstr "تجميع"

#: includes/editor-templates/responsive-bar.php:22
msgid "Desktop <br> Settings added for the base device will apply to all breakpoints unless edited"
msgstr "سطح المكتب <br> الاعدادات التي تمت إضافتها إلى الجهاز الأساسي سوف تسري على كل نقاط القطع إلا إذا تم تعديلها"

#. translators: %1$s: Device name, %2$s: Breakpoint screen size.
#: includes/editor-templates/responsive-bar.php:32
msgid "%1$s <br> Settings added for the %1$s device will apply to %2$spx screens and down"
msgstr "%1$s <br> الإعدادات التي تمّت إضافتها إلى الجهاز %1$s سوف تسري على شاشات %2$s بيكسل ومادون"

#: includes/base/element-base.php:1063 includes/base/element-base.php:1086
#: includes/widgets/common.php:954
msgid "Scale"
msgstr "قياس (نسبة)"

#: core/admin/admin-notices.php:236
msgid "Love using Elementor?"
msgstr "هل تحب استخدام Elementor ?"

#: app/modules/import-export/module.php:161
msgid "Apply the design and settings of another site to this one."
msgstr "تطبيق التصميم والإعدادات من موقع آخر على هذا الموقع."

#: app/modules/import-export/module.php:159
msgid "Start Import"
msgstr "بدء الاستيراد"

#: app/modules/import-export/module.php:149
msgid "Bundle your whole site - or just some of its elements - to be used for another website."
msgstr "قُم بحزم كامل موقعك - أو فقط بعض عناصره - لكي يتم استخدامها من أجل موقع آخر."

#: app/modules/import-export/module.php:147
msgid "Start Export"
msgstr "بدء التصدير"

#: app/modules/import-export/module.php:116
msgid "Import / Export Kit"
msgstr "استيراد / تصدير الحُزمة"

#: app/modules/import-export/module.php:156
msgid "Import a Template Kit"
msgstr "استيراد حُزمة نماذج"

#: app/modules/import-export/module.php:144
msgid "Export a Template Kit"
msgstr "تصدير حُزمة نماذج"

#. translators: 1: New line break, 2: Learn more link.
#: app/modules/import-export/module.php:137
msgid "Design sites faster with a template kit that contains some or all components of a complete site, like templates, content & site settings.%1$sYou can import a kit and apply it to your site, or export the elements from this site to be used anywhere else. %2$s"
msgstr "تصميم المواقع بشكل أسرع مع حُزمة نماذج تحتوي على بعض المحتويات أو كل محتويات الموقع كاملاً, مثل النماذج, محتوى&إعدادات الموقع. %1$s يمكنك استيراد حُزمة وتطبيقها على موقعك, أو تصدير العناصر من من هذا الموقع لكي يتم استخدامها في أي مكان آخر. %2$s"

#: app/modules/import-export/module.php:119
msgid "Template Kits"
msgstr "حُزم النماذج"

#: app/modules/import-export/module.php:133 core/admin/admin-notices.php:372
#: core/editor/editor.php:614 core/experiments/manager.php:344
#: core/experiments/manager.php:361 core/experiments/manager.php:377
#: core/experiments/manager.php:409 core/experiments/manager.php:421
#: core/experiments/manager.php:636 includes/controls/url.php:78
#: includes/elements/section.php:473 includes/settings/settings-page.php:404
#: includes/widgets/common.php:924 includes/widgets/video.php:587
#: modules/nested-elements/module.php:21
#: modules/shapes/widgets/text-path.php:133 assets/js/app.js:8323
#: assets/js/editor.js:27948
#: assets/js/kit-library.ac1a4cd5deae5526ce49.bundle.js:3450
msgid "Learn more"
msgstr "معرفة المزيد"

#: core/utils/import-export/wp-import.php:1136
msgid "The uploaded file could not be moved"
msgstr "لم يتم التمكن من نقل الملف المرفوع"

#: core/utils/import-export/wp-import.php:246
msgid "The file does not exist, please try again."
msgstr "الملف غير موجود, يُرجى المحاولة من جديد."

#: core/kits/documents/tabs/settings-layout.php:220
msgid "Mobile and Tablet options cannot be deleted."
msgstr "خيارات الهاتف المحمول والجهاز اللوحي لايمكن حذفها."

#: core/kits/documents/tabs/settings-layout.php:218
msgid "Active Breakpoints"
msgstr "نقاط القطع النشطة"

#: modules/shapes/widgets/text-path.php:468
msgid "Path"
msgstr "مسار"

#: modules/shapes/widgets/text-path.php:365
msgid "Starting Point"
msgstr "نقطة البدء"

#: includes/controls/groups/typography.php:243
#: modules/shapes/widgets/text-path.php:330
msgid "Word Spacing"
msgstr "تباعد الكلمات"

#: modules/shapes/widgets/text-path.php:200
msgid "Show Path"
msgstr "عرض المسار"

#: core/utils/import-export/wp-import.php:871
msgid "Menu item skipped due to missing menu slug"
msgstr "تم تجاوز عنصر القائمة نتيجةً لرابط لطيف مفقود."

#. translators: 1: Term taxonomy, 2: Term name.
#. translators: 1: Post type singular label, 2: Post title.
#. translators: 1: Taxonomy name, 2: Term name.
#: core/utils/import-export/wp-import.php:478
#: core/utils/import-export/wp-import.php:669
#: core/utils/import-export/wp-import.php:719
msgid "Failed to import %1$s %2$s"
msgstr "فشل استيراد %1$s %2$s"

#. translators: %s: Author display name.
#: core/utils/import-export/wp-import.php:378
msgid "Failed to create new user for %s. Their posts will be attributed to the current user."
msgstr "فشل إنشاء مستخدم جديد لـ %s . مقالاتهم سوف يتم نسبها للمستخدم الحالي."

#. translators: %s: Post author.
#: core/utils/import-export/wp-import.php:313
msgid "Failed to import author %s. Their posts will be attributed to the current user."
msgstr "فشل استيراد الكاتب %s . مقالاتهم سوف يتم نسبها للمستخدم الحالي."

#: core/utils/import-export/parsers/wxr-parser-regex.php:146
#: core/utils/import-export/parsers/wxr-parser-simple-xml.php:70
#: core/utils/import-export/parsers/wxr-parser-simple-xml.php:76
#: core/utils/import-export/parsers/wxr-parser-xml.php:190
msgid "This does not appear to be a WXR file, missing/invalid WXR version number"
msgstr "لايبدو أن هذا ملف WXR , رقم إصدار WXR مفقود/خاطئ"

#: modules/shapes/widgets/text-path.php:182
msgid "Text Direction"
msgstr "إتجاه النص"

#: modules/shapes/widgets/text-path.php:119
msgid "SVG"
msgstr "SVG"

#: modules/shapes/widgets/text-path.php:188
msgid "LTR"
msgstr "اليسار لليمين"

#: modules/shapes/widgets/text-path.php:187
msgid "RTL"
msgstr "اليمين لليسار"

#: core/utils/import-export/wp-import.php:1120
msgid "Sorry, this file type is not permitted for security reasons."
msgstr "عذراً, هذا النوع من الملفات غير مسموح به لأسباب امنية."

#. translators: %s: Max file size.
#: core/utils/import-export/wp-import.php:1088
msgid "Remote file is too large, limit is %s"
msgstr "الملف البعيد كبير جداً, الحد الأقصى هو %s"

#: core/utils/import-export/wp-import.php:1080
msgid "Downloaded file has incorrect size"
msgstr "الملف الذي تم تنزيله حجمه غير صحيح"

#: core/utils/import-export/wp-import.php:1074
msgid "Zero size file downloaded"
msgstr "ملف حجمه صفر تم تنزيله"

#: core/utils/import-export/wp-import.php:1066
msgid "Remote server did not respond"
msgstr "الخادم البعيد لم يستجب"

#. translators: 1: HTTP error message, 2: HTTP error code.
#: core/utils/import-export/wp-import.php:1057
msgid "Remote server returned the following unexpected result: %1$s (%2$s)"
msgstr "قام الخادم البعيد بإعادة النتيجة غير المتوقعة التالية: %1$s (%2$s)"

#. translators: 1: WordPress error message, 2: WordPress error code.
#: core/utils/import-export/wp-import.php:1048
msgid "Request failed due to an error: %1$s (%2$s)"
msgstr "فشل الطلب بسبب خطأ: %1$s (%2$s)"

#: core/utils/import-export/wp-import.php:1032
msgid "Could not create temporary file."
msgstr "غير قادر على إنشاء ملف مؤقت."

#: core/utils/import-export/wp-import.php:988
msgid "Invalid file type"
msgstr "نوع ملف غير صالح"

#: core/utils/import-export/wp-import.php:971
msgid "Fetching attachments is not enabled"
msgstr "جلب المرفقات غير مُمكّن"

#. translators: %s: Menu slug.
#: core/utils/import-export/wp-import.php:884
msgid "Menu item skipped due to invalid menu slug: %s"
msgstr "عنصر القائمة تم تجاوزه نتيجة للخطأ في الاسم اللطيف للقائمة :%s"

#: modules/shapes/widgets/text-path.php:97
msgid "Add Your Curvy Text Here"
msgstr "أضف نصّك المقوس هنا"

#: modules/shapes/widgets/text-path.php:50
#: modules/shapes/widgets/text-path.php:86
#: modules/shapes/widgets/text-path.php:226
msgid "Text Path"
msgstr "مسار النص"

#: modules/shapes/module.php:25
msgid "Spiral"
msgstr "دوّامة"

#: modules/shapes/module.php:24
msgid "Oval"
msgstr "بيضوي"

#: modules/shapes/module.php:21
msgid "Arc"
msgstr "قوس"

#: modules/shapes/module.php:20
msgid "Wave"
msgstr "موجة"

#: modules/nested-tabs/widgets/nested-tabs.php:756
#: modules/nested-tabs/widgets/nested-tabs.php:803
#: modules/nested-tabs/widgets/nested-tabs.php:849
#: modules/shapes/widgets/text-path.php:503
#: modules/shapes/widgets/text-path.php:574
msgid "Stroke"
msgstr "حد خارجي"

#: modules/shapes/widgets/text-path.php:109
msgid "Path Type"
msgstr "نمط المسار"

#. translators: 1: Post title, 2: Post type.
#: core/utils/import-export/wp-import.php:577
msgid "Failed to import %1$s: Invalid post type %2$s"
msgstr "فشل استيراد %1$s : نمط post خاطئ %2$s"

#: core/utils/import-export/parsers/wxr-parser-simple-xml.php:57
#: core/utils/import-export/parsers/wxr-parser-simple-xml.php:65
msgid "There was an error when reading this WXR file"
msgstr "كان هنالك خطأ عند قراءة ملف WXR هذا"

#: core/common/modules/finder/categories/settings.php:59
msgid "Experiments"
msgstr "إختبارات"

#: core/experiments/manager.php:570
msgid "The current version of Elementor doesn't have any experimental features . if you're feeling curious make sure to come back in future versions."
msgstr "الإصدارالحالي من Elementor لايحتوي على أية مزايا قيد الاختبار. إذا كنت تشعر بالفضول تأكّد من العودة في إصدارات مستقبلية."

#: core/experiments/manager.php:567
msgid "No available experiments"
msgstr "لاتوجد اختبارات متوفّرة"

#: core/experiments/manager.php:508
msgid "Stable"
msgstr "مستقر"

#: core/experiments/manager.php:507
msgid "Release Candidate"
msgstr "إصدار مُرشّح"

#: core/experiments/manager.php:504
msgid "Development"
msgstr "قيد التطوير"

#: core/experiments/manager.php:778
msgid "Inactive by default"
msgstr "غير مفعّل افتراضياً"

#: core/experiments/manager.php:777
msgid "Active by default"
msgstr "مفعّل افتراضياً"

#. translators: %s Release status.
#: core/experiments/manager.php:667
msgid "Status: %s"
msgstr "الحالة: %s"

#: core/experiments/manager.php:506 assets/js/ai-admin.js:1247
#: assets/js/ai-admin.js:7780 assets/js/ai-gutenberg.js:3007
#: assets/js/ai-gutenberg.js:9622 assets/js/ai-layout.js:1175
#: assets/js/ai-layout.js:4022 assets/js/ai-media-library.js:2876
#: assets/js/ai-media-library.js:9409 assets/js/ai.js:3639
#: assets/js/ai.js:10682
msgid "Beta"
msgstr "بيتا تجريبي"

#: core/experiments/manager.php:505
msgid "Alpha"
msgstr "ألفا تجريبي"

#: core/admin/notices/elementor-dev-notice.php:81
msgid "Install & Activate"
msgstr "التنصيب والتفعيل"

#: core/admin/notices/elementor-dev-notice.php:76
msgid "Get a sneak peek at our in progress development versions, and help us improve Elementor to perfection. Developer Edition releases contain experimental functionality for testing purposes."
msgstr "اختلس النظر إلى الإصدارات قيد التطوير, وساعدنا للارتقاء بـ Elementor نحو الكمال. إصدارات نسخة المطورين تحتوي على أداء غير مستقر لأغراض اختبارية."

#: core/admin/notices/elementor-dev-notice.php:75
msgid "Elementor Developer Edition"
msgstr "Elementor نسخة المطورين"

#: modules/compatibility-tag/compatibility-tag-report.php:123
#: modules/compatibility-tag/views/plugin-update-message-compatibility.php:57
#: modules/element-manager/ajax.php:138
msgid "Unknown"
msgstr "غير معروف"

#. translators: %s: Elementor plugin name.
#: modules/compatibility-tag/views/plugin-update-message-compatibility.php:45
msgid "Tested up to %s version"
msgstr "تم الاختبار حتى إصدار %s"

#: modules/compatibility-tag/views/plugin-update-message-compatibility.php:42
#: assets/js/element-manager-admin.js:2313
#: assets/js/element-manager-admin.js:2374
msgid "Plugin"
msgstr "الإضافة"

#: modules/compatibility-tag/views/plugin-update-message-compatibility.php:28
msgid "Compatibility Alert"
msgstr "تحذير توافقية"

#: includes/elements/section.php:301
msgid "Custom Columns Gap"
msgstr "فجوة مخصصة بين الأعمدة"

#: modules/landing-pages/module.php:279
msgid "No landing pages found in trash"
msgstr "لم يتم العثور على صفحات منسدلة في سلة المهملات"

#: modules/landing-pages/module.php:278
msgid "No landing pages found"
msgstr "لم يتم العثور على صفحات منسدلة"

#: modules/landing-pages/module.php:277
msgid "Search Landing Pages"
msgstr "البحث عن صفحات منسدلة"

#: modules/landing-pages/module.php:276
msgid "View Landing Page"
msgstr "عرض الصفحة المنسدلة"

#: modules/landing-pages/module.php:275
msgid "All Landing Pages"
msgstr "كل الصفحات المنسدلة"

#: modules/landing-pages/module.php:274
msgid "New Landing Page"
msgstr "صفحة منسدلة جديدة"

#: modules/landing-pages/module.php:273
msgid "Edit Landing Page"
msgstr "تحرير الصفحة المنسدلة"

#: modules/landing-pages/module.php:272
msgid "Add New Landing Page"
msgstr "إضافة صفحة منسدلة جديدة"

#: modules/landing-pages/module.php:204
msgid "Build Effective Landing Pages for your business' marketing campaigns."
msgstr "إبنِ صفحات منسدلة مؤثرة من اجل عملك , حملاتك التسويقية."

#: modules/landing-pages/module.php:48
msgid "Adds a new Elementor content type that allows creating beautiful landing pages instantly in a streamlined workflow."
msgstr "إضافة نمط محتوى Elementor جديد يجعل من الممكن إنشاء صفحات منسدلة جميلة عن بعد في في فضاء عمل انسيابي."

#: modules/landing-pages/admin-menu-items/landing-pages-menu-item.php:22
#: modules/landing-pages/admin-menu-items/landing-pages-menu-item.php:26
#: modules/landing-pages/documents/landing-page.php:54
#: modules/landing-pages/module.php:47 modules/landing-pages/module.php:138
#: modules/landing-pages/module.php:269 modules/landing-pages/module.php:281
#: assets/js/app.js:11044 assets/js/editor.js:52226
msgid "Landing Pages"
msgstr "صفحات منسدلة"

#: modules/landing-pages/documents/landing-page.php:46
#: modules/landing-pages/module.php:204 modules/landing-pages/module.php:270
msgid "Landing Page"
msgstr "صفحة منسدلة"

#. translators: 1: Plugin name, 2: Plugin version.
#: modules/compatibility-tag/views/plugin-update-message-compatibility.php:33
msgid "Some of the plugins you’re using have not been tested with the latest version of %1$s (%2$s). To avoid issues, make sure they are all up to date and compatible before updating %1$s."
msgstr "بعض الإضافات التي تستخدمها لم يتم اختبارها على الإصدار الأخير من %1$s (%2$s) .لكي تتجنب المشاكل, تأكد أنها جميعاً محدثة لآخر إصدار ومتوافقة قبل القيام بتحديث %1$s ."

#: core/kits/views/trash-kit-confirmation.php:33
msgid "Keep my settings"
msgstr "إحتفظ بإعداداتي"

#: core/kits/views/trash-kit-confirmation.php:21
msgid "By removing this template you will delete your entire Site Settings. If this template is deleted, all associated settings: Global Colors & Fonts, Theme Style, Layout, Background, and Lightbox settings will be removed from your existing site. This action can not be undone."
msgstr "بإزالة هذا النموذج سوف تقوم بحذف كافة إعدادات موقعك. إذا تم حذف هذا النموذج, كل الإعدادات المرتبطة:إعدادات الألوان والخطوط العامّة, تصميم الثيمة, شكل التصميم, الخلفية, والصندوق المضيء سيتم إزالتها من موقعك الحالي. هذا الأمر لايمكن التراجع عنه."

#: core/kits/views/trash-kit-confirmation.php:17
msgid "Are you sure you want to delete your Site Settings?"
msgstr "هل أنت متأكّد من أنك تريد حذف إعدادات موقعك؟"

#: core/editor/data/globals/endpoints/base.php:34
msgid "The Global value you are trying to use is not available."
msgstr "القيمة العامة التي تحاول استخدامها غير متوفِّرة."

#: includes/controls/media.php:196
msgid "Choose SVG"
msgstr "إختر SVG"

#. Description of the plugin
#: elementor.php
msgid "The Elementor Website Builder has it all: drag and drop page builder, pixel perfect design, mobile responsive editing, and more. Get started now!"
msgstr "منشئ المواقع Elementor يمتلك الكل: منشئ الصفحات بالسحب والإفلات, تصميم pixel perfect , تحرير هاتف محمول متجاوب, والكثير. إبدأ الآن!"

#: core/kits/documents/tabs/global-colors.php:24
#: core/kits/documents/tabs/global-colors.php:43
#: assets/js/152486453d0e39071cdb.bundle.js:175 assets/js/app.js:11052
#: assets/js/editor.js:47022
msgid "Global Colors"
msgstr "الالوان العامة "

#: core/kits/documents/tabs/settings-layout.php:341
#: modules/nested-tabs/widgets/nested-tabs.php:413
msgid "Breakpoint"
msgstr "نقطة التحول"

#: core/admin/admin.php:812
msgid "Heads up, Please backup before upgrade!"
msgstr "إنتبه , رجاءاً قم بالنسخ الاحتياطي قبل الترقية!"

#: core/settings/editor-preferences/model.php:40
#: includes/editor-templates/hotkeys.php:153 assets/js/editor.js:38145
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:3
msgid "User Preferences"
msgstr "تفضيلات المستخدم"

#: core/kits/manager.php:436 includes/editor-templates/hotkeys.php:115
#: assets/js/app.js:11050 assets/js/app.js:11531 assets/js/editor.js:46971
#: assets/js/editor.js:46975 assets/js/editor.js:46985
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:6
msgid "Site Settings"
msgstr "إعدادات الموقع"

#: core/kits/documents/tabs/settings-site-identity.php:93
#: assets/js/onboarding.001cbdc1993cabe048db.bundle.js:1048
msgid "Site Logo"
msgstr "شعار الموقع"

#: core/kits/documents/tabs/settings-site-identity.php:84
msgid "Choose description"
msgstr "إختر وصفاً"

#: core/kits/documents/tabs/settings-site-identity.php:82
msgid "Site Description"
msgstr "وصف الموقع"

#: core/kits/documents/tabs/settings-site-identity.php:73
msgid "Choose name"
msgstr "إختر الاسم"

#: core/kits/documents/tabs/settings-site-identity.php:71
#: assets/js/onboarding.001cbdc1993cabe048db.bundle.js:1044
msgid "Site Name"
msgstr "إسم الموقع"

#: core/kits/documents/tabs/settings-site-identity.php:20
msgid "Site Identity"
msgstr "هويّة الموقع"

#: core/kits/documents/tabs/settings-layout.php:197
msgid "Breakpoints"
msgstr "نقاط القطع"

#: core/kits/documents/tabs/settings-layout.php:183
msgid "Default Page Layout"
msgstr "التخطيط الافتراضي للصفحة"

#: core/kits/documents/tabs/settings-layout.php:47 assets/js/app.js:11052
msgid "Layout Settings"
msgstr "إعدادات التخطيط"

#: modules/page-templates/module.php:363
msgid "Changes will be reflected in the preview only after the page reloads."
msgstr "التغييرات ستنعكس على المعاينة فقط بعد إعادة تحميل الصفحة."

#: core/kits/documents/tabs/settings-site-identity.php:113
msgid "Site Favicon"
msgstr "أيقونة المتصفح للموقع"

#: includes/widgets/social-icons.php:463
msgid "Rows Gap"
msgstr "فجوة بين الصفوف"

#: includes/widgets/icon-list.php:195
msgid "Apply Link On"
msgstr "تطبيق الرابط على"

#: includes/controls/groups/flex-container.php:24
#: includes/controls/groups/grid-container.php:26
#: includes/widgets/icon-list.php:162
#: modules/nested-accordion/widgets/nested-accordion.php:130
msgid "Items"
msgstr "عناصر"

#: includes/widgets/image.php:358
msgid "Object Fit"
msgstr "ملائم للإطار"

#: modules/nested-tabs/widgets/nested-tabs.php:352 assets/js/editor.js:47394
msgid "Additional Settings"
msgstr "إعدادات إضافية"

#: core/kits/documents/tabs/settings-background.php:67
msgid "The `theme-color` meta tag will only be available in supported browsers and devices."
msgstr "وسم الميتا `theme-color` سيكون متوفراً فقط في الأجهزة والمتصفحات المدعومة."

#: core/kits/documents/tabs/settings-site-identity.php:120
msgid "Suggested favicon dimensions: 512 × 512 pixels."
msgstr "الأبعاد المقترحة لأيقونة المتصفح للموقع: 512 × 512 بيكسل."

#: core/kits/documents/tabs/global-typography.php:197
msgid "Fallback Font Family"
msgstr "عائلة الخط البديل عند فشل التحميل"

#: includes/frontend.php:1366
msgid "Download"
msgstr "تحميل"

#: core/admin/admin-notices.php:220 includes/settings/settings-page.php:403
msgid "Become a super contributor by opting in to share non-sensitive plugin data and to receive periodic email updates from us."
msgstr "كُن مساهماً رائعاً عبر مشاركة بيانات غير حساسة من الإضافة وتلقي التحديثات عبر رسائل بريد إليكتروني على فترات متقطعة."

#: modules/compatibility-tag/compatibility-tag-report.php:173
msgid "Error"
msgstr "خطأ"

#: core/kits/documents/tabs/settings-background.php:65
msgid "Mobile Browser Background"
msgstr "خلفية متصفح الجوال"

#: core/settings/editor-preferences/model.php:173 assets/js/editor.js:47402
msgid "Design System"
msgstr "تصميم النظام"

#: includes/widgets/common.php:940 includes/widgets/image.php:365
msgid "Fill"
msgstr "تعبئة"

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: core/admin/admin.php:818
msgid "The latest update includes some substantial changes across different areas of the plugin. We highly recommend you %1$sbackup your site before upgrading%2$s, and make sure you first update in a staging environment"
msgstr "التحديث الأخير يحتوي على بعض التغييرات الجوهرية في عدة مناطق من الإضافة. ننصحك بشدّة %1$sبإجراء نسخ احتياطي لموقعك قبل الترقية%2$s, وأن تتاكد أوّلاً بأن تقوم بالتحديث في بيئة Staging."

#: core/kits/documents/tabs/settings-layout.php:186
#: modules/page-templates/module.php:159
msgid "Theme"
msgstr "قالب (Theme)"

#: core/common/modules/connect/apps/base-app.php:109
msgid "Reset Data"
msgstr "إعادة ضبط البيانات"

#: includes/settings/admin-menu-items/getting-started-menu-item.php:74
msgid "Watch the Full Guide"
msgstr "شاهد الدليل الكامل"

#: includes/settings/admin-menu-items/getting-started-menu-item.php:62
msgid "Get introduced to Elementor by watching our \"Getting Started\" video series. It will guide you through the steps needed to create your website. Then click to create your first page."
msgstr "تعرف على Elementor بمشاهدة سلسة فيديوهاتنا \"كيف تبدء\". سوف ترشدك للخطوات اللازمه لعمل موقعك الإلكتروني. وبعد ذلك اضغط على عمل اول صفحة لك."

#: includes/settings/settings.php:339 assets/js/admin.js:298
#: assets/js/admin.js:308 assets/js/common.js:2146 assets/js/common.js:2156
#: assets/js/editor.js:39930 assets/js/editor.js:39940
msgid "Enable Unfiltered File Uploads"
msgstr "تمكين تحميل الملفات التي لم يتم تصفيتها. "

#: includes/controls/media.php:286
msgid "Click the media icon to upload file"
msgstr "اضغط على أيقونة الوسائط لتحميل الملف"

#: modules/safe-mode/module.php:383
msgid "If you are experiencing a loading issue, contact your site administrator to troubleshoot the problem using Safe Mode."
msgstr "إذا كنت تواجه مشكلة في التحميل ، يُرجى الاتصال بمسؤول الموقع لاستكشاف المشكلة وإصلاحها باستخدام الوضع الآمن."

#: includes/frontend.php:1367
msgid "Download image"
msgstr "تنزيل الصورة"

#: includes/controls/url.php:120
msgid "Custom Attributes"
msgstr "السمات المخصصة"

#: includes/managers/icons.php:497
msgid "We highly recommend backing up your database before performing this upgrade."
msgstr "نوصي بشدة بعمل نسخة احتياطية من قاعدة البيانات الخاصة بك قبل إجراء هذه الترقية."

#: includes/managers/icons.php:496
msgid "The upgrade process includes a database update"
msgstr "تتضمن عملية الترقية تحديث قاعدة البيانات"

#: core/kits/documents/tabs/theme-style-form-fields.php:21
#: core/kits/documents/tabs/theme-style-form-fields.php:60
msgid "Form Fields"
msgstr "حقول النموذج"

#: core/kits/documents/tabs/theme-style-buttons.php:23
#: core/kits/documents/tabs/theme-style-buttons.php:63
#: modules/floating-buttons/base/widget-contact-button-base.php:200
msgid "Buttons"
msgstr "الأزرار"

#: core/kits/documents/kit.php:155
msgid "Draft"
msgstr "مسودة"

#: includes/managers/controls.php:1201
msgid "Attributes"
msgstr "الخصائص"

#: core/kits/documents/tabs/settings-lightbox.php:187
msgid "Navigation Icons Size"
msgstr "حجم أيقونات التنقل"

#: core/kits/documents/tabs/settings-lightbox.php:86 includes/frontend.php:1370
msgid "Share"
msgstr "مشاركة"

#: core/kits/documents/tabs/theme-style-form-fields.php:99
msgid "Field"
msgstr "حقل"

#: core/kits/documents/tabs/theme-style-form-fields.php:128
msgid "Focus"
msgstr "تركيز"

#: core/kits/documents/tabs/theme-style-typography.php:49
msgid "Body"
msgstr "هيكل"

#: core/base/db-upgrades-manager.php:118
msgid "Click here to run it now"
msgstr "انقر هنا لتشغيله الآن"

#: includes/frontend.php:1365
msgid "Pin it"
msgstr "ثبتها "

#: includes/frontend.php:1364
msgid "Share on Twitter"
msgstr "شارك على تويتر"

#: includes/frontend.php:1363
msgid "Share on Facebook"
msgstr "شارك على فيسبوك"

#: includes/controls/url.php:77
msgid "Set custom attributes for the link element. Separate attribute keys from values using the | (pipe) character. Separate key-value pairs with a comma."
msgstr "قم بتعيين السمات المخصصة لعنصر الارتباط. افصل مفاتيح السمات عن القيم باستخدام | محرف قضيب عمودي (pipe) character . افصل بين أزواج القيمة الرئيسية بفاصلة."

#: includes/editor-templates/panel.php:339
msgid "Get more dynamic capabilities by incorporating dozens of Elementor's native dynamic tags."
msgstr "احصل على إمكانيات أكثر ديناميكية من خلال دمج العشرات من العلامات الديناميكية الأصلية لـ Elementor."

#: includes/editor-templates/panel.php:338
msgid "You’re missing out!"
msgstr "لقد فقدت!"

#: includes/editor-templates/panel.php:335
msgid "Elementor Dynamic Content"
msgstr "محتوى Elementor ديناميكي"

#: includes/editor-templates/panel.php:307
#: includes/editor-templates/panel.php:309
msgid "Dynamic Tags"
msgstr "وسوم ديناميكية"

#: includes/managers/controls.php:1213
msgid "Attributes lets you add custom HTML attributes to any element."
msgstr "تتيح لك الميزات إضافة سمات HTML مخصصة لأي عنصر."

#: includes/managers/controls.php:1211
msgid "Meet Our Attributes"
msgstr "تعرف على ميزاتنا"

#. translators: %s: Widget title.
#: core/editor/promotion.php:54
msgid "%s Widget"
msgstr "%s الأدوات"

#: core/experiments/manager.php:145 includes/editor-templates/global.php:30
#: assets/js/ai-admin.js:9626 assets/js/ai-gutenberg.js:11468
#: assets/js/ai-media-library.js:11255 assets/js/ai.js:12528
#: assets/js/app.js:7894 assets/js/editor.js:47283
msgid "Back"
msgstr "الرجوع"

#: core/kits/documents/tabs/theme-style-typography.php:76
msgid "Paragraph Spacing"
msgstr "تباعد الفقرات"

#: core/kits/documents/tabs/settings-lightbox.php:174
msgid "Toolbar Icons Size"
msgstr "حجم أيقونات شريط الأدوات"

#: core/kits/documents/tabs/settings-lightbox.php:102
#: core/kits/documents/tabs/settings-lightbox.php:119
msgid "Alt"
msgstr "بديل"

#: core/kits/documents/tabs/settings-lightbox.php:66 includes/frontend.php:1368
msgid "Fullscreen"
msgstr "عرض شاشة كاملة"

#: core/kits/documents/tabs/theme-style-form-fields.php:71
msgid "Label"
msgstr "مسمّى الحقل"

#: core/kits/manager.php:156 core/kits/manager.php:174
msgid "Default Kit"
msgstr "الحُزمة الافتراضية"

#: core/kits/documents/kit.php:44
msgid "Kit"
msgstr "الحُزمة"

#. translators: %s: Widget title.
#: core/editor/promotion.php:56
msgid "Use %s widget and dozens more pro features to extend your toolbox and build sites faster and better."
msgstr "استخدم %s الأدوات وعشرات الميزات الاحترافية الأخرى لتوسيع مربع الأدوات وإنشاء مواقع بشكل أسرع وأفضل."

#: core/common/modules/connect/apps/base-app.php:161
msgid "Already connected."
msgstr "تم الاتصال بالفعل"

#: core/kits/documents/tabs/settings-background.php:80
#: includes/controls/groups/background.php:483
#: includes/controls/groups/background.php:688
#: includes/elements/container.php:585 includes/widgets/social-icons.php:281
#: includes/widgets/video.php:581
msgid "Auto"
msgstr "تلقائي"

#: includes/controls/groups/background.php:682
msgid "Background Size"
msgstr "حجم الخلفية"

#: core/common/modules/connect/apps/library.php:29
#: core/common/modules/connect/apps/library.php:56
msgid "Connecting to the Library failed. Please try reloading the page and try again"
msgstr "فشل الاتصال بالمكتبة. يرجى محاولة إعادة تحميل الصفحة وحاول مرة أخرى"

#: includes/controls/groups/background.php:701
msgid "Background Position"
msgstr "موضع الخلفية"

#: core/kits/documents/tabs/settings-background.php:81
#: includes/controls/groups/background.php:485
#: includes/controls/groups/background.php:690 includes/widgets/image.php:367
msgid "Contain"
msgstr "يحتوي"

#: includes/controls/groups/background.php:484
#: includes/controls/groups/background.php:689 includes/widgets/image.php:366
#: modules/link-in-bio/base/widget-link-in-bio-base.php:912
#: modules/link-in-bio/base/widget-link-in-bio-base.php:967
msgid "Cover"
msgstr "غلاف"

#: includes/settings/settings-page.php:396
msgid "Usage Data Sharing"
msgstr "مشاركة بيانات الاستخدام"

#: core/settings/editor-preferences/model.php:53
msgid "Preferences"
msgstr "التفضيلات"

#: includes/widgets/image-carousel.php:431
msgid "Pause on Interaction"
msgstr "اوقف التفاعل"

#. translators: %s: Video provider
#: includes/embed.php:185
msgid "%s Video Player"
msgstr "%s مشغل الفيديو"

#. translators: %s: Remote user.
#: core/common/modules/connect/apps/base-app.php:84
msgid "Connected as %s"
msgstr "متصل باسم %s"

#: modules/promotions/admin-menu-items/custom-icons-promotion-item.php:15
#: modules/promotions/admin-menu-items/custom-icons-promotion-item.php:19
msgid "Custom Icons"
msgstr "أيقونات مخصصة"

#: includes/controls/groups/background.php:654
msgid "Transition"
msgstr "انتقال"

#: includes/controls/groups/background.php:644
msgid "Duration"
msgstr "المدة الزمنية"

#: core/logger/log-reporter.php:44
msgid "Clear Log"
msgstr "مسح السجل"

#: includes/controls/groups/background.php:753
msgid "Out"
msgstr "خارج"

#: includes/controls/groups/background.php:737
msgid "Ken Burns Effect"
msgstr "تأثير كين بيرنز"

#: includes/controls/groups/background.php:607
msgid "This cover image will replace the background video in case that the video could not be loaded."
msgstr "ستحل صورة الغلاف هذه محل فيديو الخلفية في حالة تعذر تحميل الفيديو."

#: includes/frontend.php:1373 assets/js/app.js:7833 assets/js/app.js:9515
#: assets/js/app.js:10431
#: assets/js/onboarding.001cbdc1993cabe048db.bundle.js:1653
#: assets/js/onboarding.001cbdc1993cabe048db.bundle.js:1704
#: assets/js/onboarding.001cbdc1993cabe048db.bundle.js:1983
#: assets/js/onboarding.001cbdc1993cabe048db.bundle.js:2260
msgid "Next"
msgstr "التالي"

#: includes/frontend.php:1372 assets/js/app.js:8707 assets/js/app.js:9502
#: assets/js/app.js:10424
msgid "Previous"
msgstr "السابق"

#: includes/widgets/divider.php:649
msgid "Amount"
msgstr "الكمية"

#: includes/controls/groups/background.php:582 includes/widgets/video.php:357
msgid "Play On Mobile"
msgstr "التشغيل على الجوال"

#: includes/widgets/divider.php:322 modules/shapes/module.php:23
msgid "Line"
msgstr "سطر"

#: includes/widgets/image-gallery.php:44 includes/widgets/image-gallery.php:112
msgid "Basic Gallery"
msgstr "المعرض الأساسي"

#: includes/widgets/divider.php:478
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:13
msgid "Add Element"
msgstr "إضافة العنصر"

#: includes/controls/groups/background.php:752
msgid "In"
msgstr "داخل"

#: includes/controls/groups/background.php:527
msgid "YouTube/Vimeo link, or link to video file (mp4 is recommended)."
msgstr "رابط يوتيوب/فيمو، أو رابط لملف فيديو (ينصح بـ mp4)"

#: includes/settings/tools.php:367
msgid "Reinstall"
msgstr "إعادة تنصيب"

#: core/document-types/post.php:51
msgid "Post"
msgstr "مقال"

#. translators: %s: Path to .htaccess file.
#: core/debug/classes/htaccess.php:31
msgid "File Path: %s"
msgstr "مسار الملف: %s"

#: core/editor/editor.php:204
msgid "Document not found."
msgstr "المستند غير موجود."

#: core/debug/classes/htaccess.php:12
msgid "Your site's .htaccess file appears to be missing."
msgstr "يبدو أن ملف .htaccess الخاص بالموقع مفقود."

#: core/debug/classes/theme-missing.php:22
msgid "Some of your theme files are missing."
msgstr "بعض ملفات القوالب الخاصة بك مفقودة."

#: includes/managers/icons.php:491
msgid "Access 1,500+ amazing Font Awesome 5 icons and enjoy faster performance and design flexibility."
msgstr "يمكنك الوصول إلى أكثر من 1500 أيقونة من Font Awesome 5 والتمتع بأداء أسرع ومرونة في التصميم."

#: includes/managers/icons.php:485 includes/managers/icons.php:489
#: includes/managers/icons.php:504
msgid "Font Awesome Upgrade"
msgstr "ترقية Font Awesome"

#: includes/managers/icons.php:470
msgid "Load Font Awesome 4 Support"
msgstr "تحميل دعم Font Awesome 4"

#: includes/managers/icons.php:247
msgid "All Icons"
msgstr "جميع الأيقونات"

#: includes/settings/settings.php:347
msgid "We recommend you only enable this feature if you understand the security risks involved."
msgstr "نوصيك بتمكين هذه الميزة فقط إذا فهمت مخاطر الأمان التي تصاحبها."

#: includes/admin-templates/beta-tester.php:32
msgid "Your Email"
msgstr "بريدك الإلكتروني"

#: includes/admin-templates/beta-tester.php:37 assets/js/beta-tester.js:65
msgid "Sign Up"
msgstr "التسجيل"

#: includes/controls/media.php:299 includes/controls/media.php:301
#: assets/js/editor.js:8226
msgid "Upload"
msgstr "رفع"

#: includes/controls/icons.php:91 includes/controls/icons.php:112
#: includes/controls/icons.php:198
msgid "Upload SVG"
msgstr "رفع SVG"

#: includes/managers/icons.php:132
msgid "Font Awesome - Regular"
msgstr "Font Awesome - عادي"

#: includes/admin-templates/beta-tester.php:29
msgid "Get Beta Updates"
msgstr "احصل على تحديثات بيتا"

#: includes/template-library/sources/local.php:615
msgid "Template not exist."
msgstr "القالب غير موجود."

#: includes/managers/icons.php:511
msgid "Upgrade To Font Awesome 5"
msgstr "الترقية إلى Font Awesome 5"

#: includes/managers/icons.php:499
msgid "This action is not reversible and cannot be undone by rolling back to previous versions."
msgstr "هذا الإجراء غير قابل للتغيير ولا يمكن التراجع عنه بالرجوع إلى الإصدارات السابقة."

#: includes/controls/icons.php:90 includes/controls/icons.php:116
#: includes/controls/icons.php:202 assets/js/editor.js:8826
msgid "Icon Library"
msgstr "مكتبة الأيقونات"

#: includes/managers/icons.php:144
msgid "Font Awesome - Solid"
msgstr "Font Awesome - صلب"

#: includes/settings/settings.php:347
msgid "Please note! Allowing uploads of any files (SVG & JSON included) is a potential security risk."
msgstr "يرجى الملاحظة ! يعد السماح بتحميل أي ملفات (بما في ذلك الايقونات و جافا سكريبت ) مخاطرة أمنية محتملة."

#: includes/managers/icons.php:156
msgid "Font Awesome - Brands"
msgstr "ماركات Font Awesome "

#: includes/controls/media.php:193
msgid "Choose Video"
msgstr "قم باختيار فيديو"

#: includes/controls/groups/background.php:572
msgid "Play Once"
msgstr "التشغيل مرة واحده"

#: includes/managers/icons.php:557
msgid "Hurray! The upgrade process to Font Awesome 5 was completed successfully."
msgstr "مرحى! اكتملت عملية الترقية إلى Font Awesome 5 بنجاح."

#: includes/managers/icons.php:492
msgid "By upgrading, whenever you edit a page containing a Font Awesome 4 icon, Elementor will convert it to the new Font Awesome 5 icon."
msgstr "من خلال الترقية ، كلما قمت بتحرير صفحة تحتوي على أيقونة Font Awesome 4 ، سيقوم اليمنتور بتحويلها إلى أيقونة Font Awesome 5 الجديدة."

#: includes/managers/icons.php:478
msgid "Font Awesome 4 support script (shim.js) is a script that makes sure all previously selected Font Awesome 4 icons are displayed correctly while using Font Awesome 5 library."
msgstr " يدعم Font Awesome 4 سكريبت (shim.js) وهو برنامج نصي يتأكد من عرض جميع رموز Font Awesome 4 المحددة مسبقًا بشكل صحيح أثناء استخدام مكتبة Font Awesome 5."

#: includes/admin-templates/beta-tester.php:30
msgid "As a beta tester, you’ll receive an update that includes a testing version of Elementor and its content directly to your Email"
msgstr "بصفتك مستخدماً لإختبارات بيتا ، ستتلقى تحديثًا يتضمن إصدارًا تجريبيًا من اليمنتور ومحتواه مباشرة إلى بريدك الإلكتروني"

#: includes/settings/settings.php:347
msgid "Elementor will try to sanitize the unfiltered files, removing potential malicious code and scripts."
msgstr "سيحاول اليمنتور تنظيف ملفات الأيقونات ، وإزالة الأكواد والبرامج الضارة المحتملة."

#: core/kits/views/panel.php:12 includes/editor-templates/panel.php:30
#: includes/editor-templates/panel.php:193
msgid "Need Help"
msgstr "هل تحتاج لمساعدة"

#: includes/base/widget-base.php:1035
msgid "Deprecated"
msgstr "تم إهماله"

#: includes/managers/icons.php:494
msgid "Please note that the upgrade process may cause some of the previously used Font Awesome 4 icons to look a bit different due to minor design changes made by Font Awesome."
msgstr "يرجى ملاحظة أن عملية الترقية قد تتسبب في ظهور بعض رموز Font Awesome للإصدار الرابع المستخدمة سابقًا بشكل مختلف قليلاً بسبب التغييرات الطفيفة في التصميم التي أجرتها Font Awesome."

#: core/files/file-types/svg.php:73 core/files/uploads-manager.php:571
msgid "This file is not allowed for security reasons."
msgstr "هذا الملف غير مسموح به لأسباب أمنية."

#: includes/elements/container.php:1375 includes/widgets/common.php:327
msgid "Please note!"
msgstr "يرجى الملاحظة!"

#: includes/elements/container.php:1394 includes/widgets/common.php:345
msgid "Fixed"
msgstr "ثابت"

#: includes/elements/container.php:1393 includes/widgets/common.php:344
msgid "Absolute"
msgstr "مطلق"

#: includes/settings/controls.php:236
msgid "Super Admin"
msgstr "المدير العام"

#: includes/controls/groups/flex-item.php:30 includes/widgets/common.php:242
msgid "Custom Width"
msgstr "عرض مخصص"

#: includes/elements/container.php:584 includes/elements/section.php:453
msgid "Hidden"
msgstr "مخفي"

#: includes/elements/container.php:1413 includes/widgets/common.php:359
msgid "Horizontal Orientation"
msgstr "الاتجاه الأفقي"

#: includes/settings/admin-menu-items/get-help-menu-item.php:23
msgid "Get Help"
msgstr "الحصول على مساعدة "

#: includes/controls/groups/flex-container.php:117
#: includes/controls/groups/flex-container.php:214
#: includes/controls/groups/grid-container.php:212
#: includes/controls/groups/grid-container.php:252
#: includes/elements/column.php:192 includes/elements/column.php:220
#: includes/elements/section.php:430
msgid "Space Evenly"
msgstr "ضع مسافة بالتساوي"

#: includes/elements/container.php:1514 includes/widgets/common.php:460
msgid "Vertical Orientation"
msgstr "الاتجاه العمودي"

#: includes/elements/section.php:420 includes/widgets/common.php:299
#: includes/widgets/image-carousel.php:681
msgid "Vertical Align"
msgstr "محاذاة عمودية"

#: includes/elements/column.php:826 includes/elements/container.php:1671
#: includes/elements/section.php:1263 includes/widgets/common.php:615
msgid "Motion Effects"
msgstr "تأثيرات الحركة"

#: includes/elements/container.php:579 includes/elements/section.php:448
msgid "Overflow"
msgstr "تدفق جانبي"

#: includes/elements/container.php:1376 includes/widgets/common.php:328
msgid "Custom positioning is not considered best practice for responsive web design and should not be used too frequently."
msgstr "لا يُعتبر تحديد المواقع المخصصة أفضل ممارسة لتصميم الويب المتجاوب ويجب عدم استخدامه كثيرًا."

#: includes/controls/groups/flex-container.php:113
#: includes/controls/groups/flex-container.php:213
#: includes/controls/groups/grid-container.php:208
#: includes/controls/groups/grid-container.php:248
#: includes/elements/column.php:191 includes/elements/column.php:219
#: includes/elements/section.php:429
msgid "Space Around"
msgstr "مساحة بين"

#: includes/base/element-base.php:997 includes/elements/container.php:1438
#: includes/elements/container.php:1476 includes/elements/container.php:1538
#: includes/elements/container.php:1575 includes/widgets/common.php:384
#: includes/widgets/common.php:422 includes/widgets/common.php:484
#: includes/widgets/common.php:521
#: modules/floating-buttons/base/widget-contact-button-base.php:2902
#: modules/floating-buttons/base/widget-contact-button-base.php:2956
msgid "Offset"
msgstr "إزاحة"

#: modules/safe-mode/module.php:374
msgid "Having problems loading Elementor? Please enable Safe Mode to troubleshoot."
msgstr "هل تواجه مشاكل في تحميل Elementor؟ يرجى تمكين الوضع الآمن لاستكشاف الأخطاء وإصلاحها."

#: modules/safe-mode/module.php:272
msgid "The issue was probably caused by one of your plugins or theme."
msgstr "ربما كان سبب المشكلة أحد الإضافات أو القالب الخاص بك."

#. translators: %s: Accepted chars.
#: includes/widgets/menu-anchor.php:118
msgid "Note: The ID link ONLY accepts these chars: %s"
msgstr "ملاحظة: يقبل رابط المعرف هذه الأحرف فقط: %s"

#: includes/template-library/sources/local.php:320
msgctxt "Template Library"
msgid "All Categories"
msgstr "كل التصنيفات"

#: includes/template-library/sources/local.php:319
msgctxt "Template Library"
msgid "Category"
msgstr "تصنيف"

#: includes/template-library/sources/local.php:318
msgctxt "Template Library"
msgid "Categories"
msgstr "تصنيفات"

#: modules/promotions/admin-menu-items/popups-promotion-item.php:18
msgid "Get Popup Builder"
msgstr "الحصول على مصمم النوافذ المنبثقة"

#: includes/template-library/sources/local.php:1720
#: modules/promotions/admin-menu-items/popups-promotion-item.php:42
#: modules/promotions/admin-menu-items/popups-promotion-item.php:46
#: assets/js/app.js:11035
#: assets/js/kit-library.ac1a4cd5deae5526ce49.bundle.js:2250
msgid "Popups"
msgstr "النوافذ المنبثقة"

#: app/admin-menu-items/theme-builder-menu-item.php:22
#: app/modules/site-editor/module.php:31
#: core/common/modules/finder/categories/general.php:72
#: includes/template-library/sources/local.php:1719
#: assets/js/app-packages.js:5925 assets/js/editor.js:46996
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:4
msgid "Theme Builder"
msgstr "مصمم القوالب"

#: core/admin/menu/main.php:34 core/admin/menu/main.php:35
#: includes/template-library/sources/local.php:239
msgctxt "Template Library"
msgid "Templates"
msgstr "قوالب"

#: includes/widgets/google-maps.php:136
#: modules/floating-buttons/base/widget-contact-button-base.php:375
#: modules/floating-buttons/base/widget-contact-button-base.php:907
#: modules/link-in-bio/base/widget-link-in-bio-base.php:492
#: modules/link-in-bio/base/widget-link-in-bio-base.php:742
msgid "Location"
msgstr "الموقع الجغرافي"

#: includes/widgets/read-more.php:121
msgid "Read More Text"
msgstr "قراءة المزيد"

#. translators: %s: Current post name.
#: includes/frontend.php:1507
msgid "Continue reading %s"
msgstr "متابعة قراءة %s"

#: modules/safe-mode/module.php:42
msgid "Safe Mode"
msgstr "الوضع الآمن"

#: modules/safe-mode/module.php:370
msgid "Enable Safe Mode"
msgstr "تمكين الوضع الآمن"

#: modules/safe-mode/module.php:368 modules/safe-mode/module.php:380
msgid "Can't Edit?"
msgstr "لا تستطيع التعديل؟"

#: modules/safe-mode/module.php:285
msgid "Still experiencing issues?"
msgstr "لا تزال تواجه مشاكل؟"

#: modules/safe-mode/module.php:269
msgid "Editor successfully loaded?"
msgstr "تم تحميل المحرر بنجاح؟"

#: includes/controls/media.php:199
#: modules/link-in-bio/base/widget-link-in-bio-base.php:379
msgid "Choose File"
msgstr "اختيار ملف"

#: includes/template-library/sources/local.php:1415
msgctxt "Template Library"
msgid "Filter by category"
msgstr "تصفية حسب التصنيف"

#: modules/promotions/admin-menu-items/popups-promotion-item.php:19
msgid "The Popup Builder lets you take advantage of all the amazing features in Elementor, so you can build beautiful & highly converting popups. Get Elementor Pro and start designing your popups today."
msgstr "يتيح لك مصمم النوافذ المنبثقة الاستفادة من جميع الميزات المدهشة في Elementor، حتى تتمكن من إنشاء نوافذ منبثقة جميلة للغاية. اذهب لباقة المحترفين وابدأ في تصميم النوافذ المنبثقة الخاصة بك اليوم."

#: modules/safe-mode/module.php:51
msgid "Safe Mode allows you to troubleshoot issues by only loading the editor, without loading the theme or any other plugin."
msgstr "يسمح لك الوضع الآمن باستكشاف المشكلات وإصلاحها عن طريق تحميل المحرر فقط، دون تحميل السمة أو أي مكون إضافي آخر."

#: includes/widgets/read-more.php:92
msgid "Continue reading"
msgstr "متابعة القراءة"

#: includes/widgets/read-more.php:41 includes/widgets/read-more.php:88
msgid "Read More"
msgstr "قراءة المزيد"

#: includes/widgets/video.php:226
msgid "External URL"
msgstr "رابط خارجي"

#: core/upgrade/manager.php:47
msgid "Elementor Data Updater"
msgstr "محدث بيانات Elementor"

#. translators: %d: Interval in minutes.
#: core/base/background-process/wp-background-process.php:439
#: core/base/background-task.php:312
msgid "Every %d minutes"
msgstr "كل %d دقيقة"

#: modules/safe-mode/module.php:262 modules/safe-mode/module.php:492
msgid "Disable Safe Mode"
msgstr "تعطيل الوضع الآمن"

#: modules/safe-mode/module.php:102
msgid "Cannot enable Safe Mode"
msgstr "لا يمكن تشغيل الوضع الآمن"

#: modules/safe-mode/module.php:260
msgid "Safe Mode ON"
msgstr "تشغيل الوضع الآمن "

#: includes/frontend.php:1500
msgid "(more&hellip;)"
msgstr "(المزيد&hellip;)"

#: includes/widgets/image-gallery.php:205
msgid "Order By"
msgstr "الترتيب حسب"

#: core/base/db-upgrades-manager.php:130
msgid "The database update process is now complete. Thank you for updating to the latest version!"
msgstr "عملية تحديث قاعدة البيانات اكتملت الآن. شكراً لك على التحديث لآخر إصدار!"

#: core/base/db-upgrades-manager.php:93
msgid "Your site database needs to be updated to the latest version."
msgstr "قاعدة بيانات موقعك بحاجة للتحديث لآخر إصدار."

#. translators: %s: The `the_content` function.
#: includes/widgets/read-more.php:112
msgid "Note: This widget only affects themes that use `%s` in archive pages."
msgstr "ملاحظة: تؤثر هذه الأداة فقط على القوالب التي تستخدم `%s` في صفحات الأرشيف."

#: modules/library/documents/not-supported.php:57
msgid "Not Supported"
msgstr "غير مدعوم"

#: core/common/modules/finder/categories/site.php:76 assets/js/app.js:11539
msgid "Plugins"
msgstr "إضافات"

#: core/common/modules/finder/categories/site.php:82
msgid "Users"
msgstr "المستخدمون"

#: includes/widgets/star-rating.php:301
msgid "Stars"
msgstr "نجوم"

#: includes/widgets/video.php:497
msgid "Current Video Channel"
msgstr "قناة الفيديو الحالية"

#: core/common/modules/finder/categories/site.php:58
msgid "Menus"
msgstr "قوائم"

#: includes/widgets/star-rating.php:45 includes/widgets/star-rating.php:104
msgid "Star Rating"
msgstr "التقييم بالنجوم"

#: includes/widgets/rating.php:22 includes/widgets/rating.php:132
#: includes/widgets/rating.php:156 includes/widgets/star-rating.php:135
msgid "Rating"
msgstr "التقييم"

#: core/common/modules/finder/categories/site.php:52
msgid "Dashboard"
msgstr "لوحة التحكم"

#: core/common/modules/finder/categories/site.php:46
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:12
msgid "Homepage"
msgstr "الصفحة الرئيسية"

#: includes/widgets/video.php:498
msgid "Any Video"
msgstr "أي فيديو"

#: includes/widgets/rating.php:139 includes/widgets/star-rating.php:122
msgid "Rating Scale"
msgstr "مقياس التصنيف"

#: core/common/modules/connect/apps/base-app.php:87
msgid "Disconnect"
msgstr "قطع الاتصال"

#: core/common/modules/connect/apps/base-app.php:233
msgid "Disconnected successfully."
msgstr "تم قطع الاتصال بنجاح."

#: core/common/modules/connect/apps/connect.php:11
#: core/common/modules/connect/connect-menu-item.php:24
#: core/common/modules/connect/connect-menu-item.php:28
#: assets/js/ai-admin.js:1668 assets/js/ai-admin.js:6654
#: assets/js/ai-gutenberg.js:3428 assets/js/ai-gutenberg.js:8496
#: assets/js/ai-layout.js:1459 assets/js/ai-layout.js:3183
#: assets/js/ai-media-library.js:3297 assets/js/ai-media-library.js:8283
#: assets/js/ai.js:4060 assets/js/ai.js:9463 assets/js/ai.js:9556
msgid "Connect"
msgstr "اتصال"

#: includes/widgets/image.php:145 includes/widgets/image.php:157
msgid "Custom Caption"
msgstr "تسمية توضيحية مخصصة"

#: includes/editor-templates/hotkeys.php:107
msgid "Show / Hide Panel"
msgstr "إظهار / إخفاء اللوحة"

#: core/base/document.php:1961
msgid "Future"
msgstr "المستقبل"

#: includes/editor-templates/hotkeys.php:32 assets/js/ai-admin.js:12912
#: assets/js/ai-gutenberg.js:14754 assets/js/ai-media-library.js:14541
#: assets/js/ai.js:15814
msgid "Redo"
msgstr "إلغاء التراجع"

#: includes/editor-templates/hotkeys.php:24 assets/js/ai-admin.js:12901
#: assets/js/ai-gutenberg.js:14743 assets/js/ai-media-library.js:14530
#: assets/js/ai.js:15803 assets/js/kit-elements-defaults-editor.js:236
msgid "Undo"
msgstr "تراجع"

#: core/common/modules/finder/template.php:13
msgid "Type to find anything in Elementor"
msgstr "اكتب للبحث على أي شيء في Elementor"

#: includes/editor-templates/hotkeys.php:99 assets/js/admin-top-bar.js:191
#: assets/js/common.js:4759 assets/js/editor.js:38157
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:10
msgid "Finder"
msgstr "Finder"

#: core/common/modules/finder/categories/site.php:70
msgid "Customizer"
msgstr "أداة التخصيص"

#: core/common/modules/finder/categories/create.php:27
#: assets/js/editor.js:45533
msgid "Create"
msgstr "إنشاء"

#: includes/editor-templates/hotkeys.php:208
msgid "Quit"
msgstr "إنهاء"

#: includes/editor-templates/hotkeys.php:200 assets/js/editor.js:7524
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:8
msgid "Keyboard Shortcuts"
msgstr "اختصارات لوحة المفاتيح"

#: includes/editor-templates/hotkeys.php:167
msgid "Go To"
msgstr "اذهب إلى"

#: includes/widgets/image-gallery.php:157 includes/widgets/image.php:144
msgid "Attachment Caption"
msgstr "تسمية توضيحية للمرفق"

#: includes/widgets/star-rating.php:166
msgid "Unmarked Style"
msgstr "نمط غير مميز"

#: includes/widgets/rating.php:114 includes/widgets/star-rating.php:372
msgid "Unmarked Color"
msgstr "لون غير مميز"

#: core/common/modules/connect/apps/base-app.php:221 assets/js/editor.js:10900
msgid "Connected successfully."
msgstr "تم الربط بنجاح."

#: includes/widgets/star-rating.php:174
msgid "Outline"
msgstr "مُحدد"

#: includes/widgets/video.php:600
msgid "Poster"
msgstr "ملصق"

#: includes/widgets/inner-section.php:35 assets/js/editor.js:27962
msgid "Inner Section"
msgstr "قسم داخلي"

#: includes/editor-templates/navigator.php:107
msgid "Empty"
msgstr "فارغ"

#: includes/editor-templates/navigator.php:113
msgid "Once you fill your page with content, this window will give you an overview display of all the page elements. This way, you can easily move around any section, column, or widget."
msgstr "بمجرد تعبئة صفحتك بالمحتوى، ستمنحك هذه النافذة عرضًا عامًا لجميع عناصر الصفحة. بهذه الطريقة، يمكنك التنقل بسهولة في أي قسم أو عمود أو ودجت."

#: core/admin/admin-notices.php:287
msgid "Hide Notification"
msgstr "إخفاء الإشعارات"

#: core/admin/admin-notices.php:281
msgid "Happy To Help"
msgstr "مسرور بالمساعدة"

#: core/admin/admin-notices.php:277
msgid "Congrats!"
msgstr "تهانينا!"

#: includes/widgets/video.php:162 includes/widgets/video.php:187
#: includes/widgets/video.php:211 includes/widgets/video.php:271
msgid "Enter your URL"
msgstr "أدخل عنوان ويب URL الخاص بك"

#: includes/editor-templates/library-layout.php:13
#: includes/settings/admin-menu-items/getting-started-menu-item.php:55
#: includes/settings/admin-menu-items/getting-started-menu-item.php:56
#: modules/announcements/module.php:111 assets/js/app-packages.js:2843
#: assets/js/app.js:3964
#: assets/js/onboarding.001cbdc1993cabe048db.bundle.js:1269
#: assets/js/onboarding.001cbdc1993cabe048db.bundle.js:1475
#: assets/js/onboarding.001cbdc1993cabe048db.bundle.js:1556
#: assets/js/onboarding.001cbdc1993cabe048db.bundle.js:1799
#: assets/js/onboarding.001cbdc1993cabe048db.bundle.js:1974
#: assets/js/onboarding.001cbdc1993cabe048db.bundle.js:2295
msgid "Skip"
msgstr "تخطي"

#: includes/controls/url.php:68
#: modules/floating-buttons/base/widget-contact-button-base.php:900
#: modules/link-in-bio/base/widget-link-in-bio-base.php:262
#: modules/shapes/widgets/text-path.php:147
msgid "Paste URL or type"
msgstr "لصق عنوان ويب URL أو النوع"

#: includes/controls/groups/css-filter.php:129
msgctxt "Filter Control"
msgid "Hue"
msgstr "Hue"

#: core/debug/inspector.php:49
msgid "Debug Bar"
msgstr "شريط التصحيح"

#: includes/settings/admin-menu-items/getting-started-menu-item.php:61
msgid "Welcome to Elementor"
msgstr "مرحبًا بكم في Elementor"

#: includes/settings/admin-menu-items/getting-started-menu-item.php:39
msgid "Create Your First Post"
msgstr "إنشاء أول مقالة"

#: includes/settings/admin-menu-items/getting-started-menu-item.php:36
msgid "Create Your First Page"
msgstr "أنشئ صفحتك الأولى "

#: includes/settings/admin-menu-items/getting-started-menu-item.php:23
#: includes/settings/admin-menu-items/getting-started-menu-item.php:27
#: includes/settings/admin-menu-items/getting-started-menu-item.php:52
#: assets/js/onboarding.001cbdc1993cabe048db.bundle.js:792
msgid "Getting Started"
msgstr "البدء"

#: includes/editor-templates/navigator.php:112
msgid "Easy Navigation is Here!"
msgstr "سهولة التنقل هنا!"

#: includes/widgets/accordion.php:153 includes/widgets/accordion.php:157
#: includes/widgets/icon-box.php:167 includes/widgets/image-box.php:143
#: includes/widgets/tabs.php:152 includes/widgets/tabs.php:156
#: includes/widgets/testimonial.php:126 includes/widgets/text-editor.php:118
#: includes/widgets/toggle.php:156 includes/widgets/toggle.php:160
msgid "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut elit tellus, luctus nec ullamcorper mattis, pulvinar dapibus leo."
msgstr "أبجد هوز حطي كلمن سعفص قرشت ثخذ ضظغ"

#: includes/editor-templates/hotkeys.php:126
#: includes/editor-templates/navigator.php:42
#: includes/editor-templates/panel.php:90
#: includes/editor-templates/panel.php:96 assets/js/editor.js:31633
msgid "Navigator"
msgstr "مسار التنقل"

#: includes/widgets/video.php:460
msgid "Lazy Load"
msgstr "تحميل بطيء"

#: core/kits/documents/tabs/settings-lightbox.php:76 includes/frontend.php:1369
#: includes/widgets/google-maps.php:156 assets/js/ai-admin.js:10047
#: assets/js/ai-admin.js:10050 assets/js/ai-gutenberg.js:11889
#: assets/js/ai-gutenberg.js:11892 assets/js/ai-media-library.js:11676
#: assets/js/ai-media-library.js:11679 assets/js/ai.js:12949
#: assets/js/ai.js:12952
msgid "Zoom"
msgstr "تكبير"

#: core/debug/inspector.php:57
msgid "Debug Bar adds an admin bar menu that lists all the templates that are used on a page that is being displayed."
msgstr "يضيف شريط التصحيح قائمة في شريط يسرد جميع القوالب المستخدمة في الصفحة التي يتم عرضها."

#: core/document-types/page-base.php:47
msgid "Single"
msgstr "أحادي"

#: includes/widgets/video.php:432
msgid "Logo"
msgstr "الشعار"

#: includes/widgets/video.php:405
msgid "Video Info"
msgstr "معلومات الفيديو"

#: includes/widgets/video.php:419
msgid "Modest Branding"
msgstr "علامة تجارية متواضعة"

#: app/modules/site-editor/module.php:32
#: core/common/modules/finder/categories/site.php:26 core/kits/manager.php:437
#: includes/managers/elements.php:302
msgid "Site"
msgstr "الموقع"

#: includes/editor-templates/hotkeys.php:41 assets/js/editor.js:30632
msgid "Copy"
msgstr "نسخ"

#: includes/editor-templates/global.php:51
msgid "Drag widget here"
msgstr "اسحب ودجت هنا"

#: includes/controls/groups/css-filter.php:113
msgctxt "Filter Control"
msgid "Saturation"
msgstr "التشبع"

#: includes/controls/groups/css-filter.php:97
msgctxt "Filter Control"
msgid "Contrast"
msgstr "التباين"

#: includes/controls/groups/css-filter.php:81
msgctxt "Filter Control"
msgid "Brightness"
msgstr "السطوع"

#: includes/controls/groups/background.php:562 includes/widgets/video.php:322
msgid "Specify an end time (in seconds)"
msgstr "تحديد وقت الانتهاء (بالثواني)"

#: includes/controls/groups/background.php:560 includes/widgets/video.php:320
msgid "End Time"
msgstr "وقت الانتهاء"

#: includes/controls/groups/background.php:550 includes/widgets/video.php:311
msgid "Specify a start time (in seconds)"
msgstr "تحديد وقت البدء (بالثواني)"

#: core/base/providers/social-network-provider.php:207
#: includes/widgets/video.php:258 includes/widgets/video.php:282
msgid "URL"
msgstr "عنوان ويب URL"

#: includes/widgets/video.php:144
msgid "Self Hosted"
msgstr "مستضاف ذاتيا"

#: includes/widgets/video.php:142
msgid "Dailymotion"
msgstr "Dailymotion"

#: includes/widgets/video.php:136
msgid "Source"
msgstr "المصدر"

#: includes/widgets/traits/button-trait.php:204
msgid "Button ID"
msgstr "معرف الزر"

#: core/debug/inspector.php:115
msgid "Elementor Debugger"
msgstr "مصحح Elementor"

#: includes/controls/groups/background.php:548 includes/widgets/video.php:309
msgid "Start Time"
msgstr "وقت البدء"

#: core/admin/feedback.php:117
msgid "Wait! Don't deactivate Elementor. You have to activate both Elementor and Elementor Pro in order for the plugin to work."
msgstr "انتظر! لا تقم بتعطيل Elementor. يجب عليك تفعيل كل من Elementor و Elementor Pro حتى تعمل الإضافة."

#: core/admin/feedback.php:115
msgid "I have Elementor Pro"
msgstr "لدي Elementor Pro"

#: includes/managers/elements.php:309
msgid "WooCommerce"
msgstr "ووكومرس"

#: includes/managers/elements.php:288
#: modules/promotions/widgets/pro-widget-promotion.php:53
#: assets/js/ai-admin.js:7998 assets/js/ai-gutenberg.js:9840
#: assets/js/ai-layout.js:4240 assets/js/ai-media-library.js:9627
#: assets/js/ai.js:10900
msgid "Pro"
msgstr "Pro"

#: includes/controls/groups/css-filter.php:62
msgctxt "Filter Control"
msgid "Blur"
msgstr "ضبابي"

#: includes/widgets/audio.php:189
msgid "Artwork"
msgstr "عمل فني"

#: includes/elements/column.php:404 includes/elements/container.php:840
#: includes/elements/section.php:678 includes/widgets/heading.php:314
msgid "Blend Mode"
msgstr "وضع المزج"

#: core/admin/admin.php:220 assets/js/admin.js:2124 assets/js/gutenberg.js:148
msgid "Back to WordPress Editor"
msgstr "الرجوع الى محرر وردبرس"

#. translators: %s: Document title.
#: core/documents-manager.php:389
msgid "Elementor %s"
msgstr "Elementor %s"

#. translators: %s: Document title.
#. translators: %s: Template type label.
#: core/base/document.php:267
#: core/common/modules/finder/categories/create.php:86
#: core/document-types/page-base.php:183
#: includes/template-library/sources/local.php:1376
msgid "Add New %s"
msgstr "أضف %s"

#: core/kits/documents/tabs/theme-style-images.php:95
#: core/kits/documents/tabs/theme-style-images.php:166
#: includes/elements/column.php:373 includes/elements/column.php:447
#: includes/elements/container.php:794 includes/elements/container.php:908
#: includes/elements/section.php:632 includes/elements/section.php:736
#: includes/widgets/image-box.php:411 includes/widgets/image-box.php:446
#: includes/widgets/image.php:423 includes/widgets/image.php:457
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1491
msgid "Opacity"
msgstr "الشفافية"

#: includes/widgets/image.php:301
msgid "Max Width"
msgstr "الحد الأقصى للعرض"

#: includes/template-library/sources/local.php:232
#: assets/js/new-template.js:149
msgid "New Template"
msgstr "قالب جديد"

#: includes/controls/groups/background.php:264
#: includes/controls/groups/background.php:312
#: includes/controls/groups/box-shadow.php:69
#: includes/elements/container.php:1388 includes/widgets/common.php:339
#: includes/widgets/common.php:986 includes/widgets/divider.php:748
#: includes/widgets/divider.php:914 includes/widgets/image-carousel.php:541
#: includes/widgets/image-carousel.php:605 includes/widgets/tabs.php:166
#: includes/widgets/traits/button-trait.php:250
#: modules/link-in-bio/base/widget-link-in-bio-base.php:932
#: modules/link-in-bio/base/widget-link-in-bio-base.php:987
#: modules/nested-accordion/widgets/nested-accordion.php:197
#: modules/nested-tabs/widgets/nested-tabs.php:872
msgid "Position"
msgstr "موضع"

#. translators: 1: Elementor, 2: Link to plugin review
#: core/admin/admin.php:409
msgid "Enjoyed %1$s? Please leave us a %2$s rating. We really appreciate your support!"
msgstr "استمتعت %1$s? المرجو أن تترك لنا %2$s تقييما. نحن نقدر دعمكم!"

#: core/common/modules/finder/categories/general.php:67
msgid "Knowledge Base"
msgstr "قاعدة المعرفة"

#: modules/page-templates/module.php:295
msgid "Page Layout"
msgstr "تخطيط الصفحة"

#: modules/page-templates/module.php:350
msgid "This template includes the header, full-width content and footer"
msgstr "يتضمن هذا القالب الرأس والمحتوى ذي العرض الكامل والتذييل"

#: modules/page-templates/module.php:338
msgid "No header, no footer, just Elementor"
msgstr "لا رأس ، لا تذييل ، فقط Elementor."

#: modules/page-templates/module.php:326
msgid "Default Page Template from your theme."
msgstr "نموذج الصفحة الافتراضي من قالبك."

#: includes/frontend.php:1371 includes/widgets/video.php:980
msgid "Play Video"
msgstr "تشغيل الفيديو"

#: includes/template-library/sources/local.php:1241
msgid "All"
msgstr "الكل"

#: includes/template-library/sources/local.php:227
msgctxt "Template Library"
msgid "My Templates"
msgstr "قوالبي"

#: includes/admin-templates/new-template.php:112
msgid "Enter template name (optional)"
msgstr "أدخل أسم القالب ( إختياري )"

#: includes/admin-templates/new-template.php:109
msgid "Name your template"
msgstr "تسمية القالب"

#: includes/admin-templates/new-template.php:66
msgid "Select the type of template you want to work on"
msgstr "أختر القالب الذي تريد العمل به"

#: includes/admin-templates/new-template.php:64
msgid "Choose Template Type"
msgstr "أختر نوع القالب"

#: core/document-types/page.php:65 modules/library/documents/page.php:61
#: assets/js/app.js:11821 assets/js/editor.js:10151
#: assets/js/kit-library.ac1a4cd5deae5526ce49.bundle.js:2222
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:1
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:3
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:7
msgid "Pages"
msgstr "صفحات"

#: core/dynamic-tags/tag.php:115 includes/settings/settings.php:375
msgid "Fallback"
msgstr "تراجع"

#: core/common/modules/ajax/module.php:131
msgid "Token Expired."
msgstr "انتهت صلاحية الرمز المميز"

#: includes/widgets/counter.php:192
msgid "Separator"
msgstr "فاصل"

#: core/common/modules/ajax/module.php:165
msgid "Action not found."
msgstr "الإجراء غير موجود"

#: core/document-types/page-base.php:182
#: includes/template-library/sources/admin-menu-items/add-new-template-menu-item.php:23
#: modules/landing-pages/module.php:271 assets/js/app-packages.js:4610
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:8
msgid "Add New"
msgstr "اضافة جديد"

#. translators: %s: Template type label.
#: includes/template-library/sources/local.php:1369
msgid "Create Your First %s"
msgstr "اضافة %s الأول الخاص بك"

#: core/kits/documents/tabs/global-typography.php:182
#: modules/promotions/admin-menu-items/custom-fonts-promotion-item.php:15
#: modules/promotions/admin-menu-items/custom-fonts-promotion-item.php:19
#: assets/js/152486453d0e39071cdb.bundle.js:237
msgid "Custom Fonts"
msgstr "الخطوط المخصصة"

#: core/common/modules/finder/categories/general.php:61
#: core/role-manager/role-manager-menu-item.php:28
#: core/role-manager/role-manager-menu-item.php:32
#: core/role-manager/role-manager.php:50
msgid "Role Manager"
msgstr "مدير الصلاحيات"

#: core/role-manager/role-manager.php:157
msgid "No access to editor"
msgstr "لا يمكن الوصول إلى المحرر"

#: core/role-manager/role-manager.php:239
msgid "Want to give access only to content?"
msgstr "تريد منح حق الوصول إلى المحتوى فقط؟"

#. translators: %s: Document title.
#. translators: %s: Post type label.
#: core/base/document.php:1222 core/settings/page/model.php:127
#: includes/editor-templates/panel.php:84
#: assets/js/packages/editor-app-bar/editor-app-bar.js:2
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:24
msgid "%s Settings"
msgstr "%s الإعدادات"

#: core/role-manager/role-manager.php:150
msgid "Role Excluded"
msgstr "دور مستبعد"

#: core/role-manager/role-manager.php:114
msgid "Manage What Your Users Can Edit In Elementor"
msgstr "إدارة ما يمكن للمستخدمين تحريره في Elementor"

#: includes/editor-templates/templates.php:102
msgid "Search Templates:"
msgstr "البحث في القوالب"

#: includes/admin-templates/new-template.php:57
msgid "Use templates to create the different pieces of your site, and reuse them with one click whenever needed."
msgstr "استخدم النماذج لإنشاء أجزاء مختلفة من موقعك ، وإعادة استخدامها بنقرة واحدة كلما دعت الحاجة."

#: includes/editor-templates/templates.php:182
msgid "More actions"
msgstr "مزيد من الإجراءات"

#: includes/editor-templates/global.php:122
msgid "This tag has no settings."
msgstr "هذه الوسم لا يوجد لديه إعدادات."

#: core/document-types/page-base.php:124
msgid "Body Style"
msgstr "نمط الجسم"

#: core/base/document.php:257
msgid "Document"
msgstr "وثيقة"

#: includes/template-library/sources/local.php:1336
msgid "Add templates and reuse them across your website. Easily export and import them to any other project, for an optimized workflow."
msgstr "أضف القوالب وأعد استخدامها على موقعك الإلكتروني. بسهولة يمكنك تصدير واستيراد القوالب إلى أي مشروع آخر، لسير عمل محسن."

#: core/document-types/page-base.php:230
msgid "Featured Image"
msgstr "الصورة البارزة"

#: includes/widgets/common.php:226 includes/widgets/icon-list.php:108
#: includes/widgets/icon-list.php:199
msgid "Inline"
msgstr "مضمنة"

#: includes/admin-templates/new-template.php:115
msgid "Create Template"
msgstr "إنشاء قالب"

#: includes/widgets/image-carousel.php:162
msgid "Set how many slides are scrolled per swipe."
msgstr "حدد عدد الشرائح لكل حركة سحب"

#: core/admin/admin.php:478
msgid "Create New Post"
msgstr "إنشاء مقالة جديدة"

#: includes/controls/groups/background.php:447
msgid "Note: Attachment Fixed works only on desktop."
msgstr "ملاحظة: مرفق ثابت يعمل فقط على سطح المكتب."

#: includes/controls/groups/background.php:596 includes/widgets/video.php:447
msgid "Privacy Mode"
msgstr "وضع الخصوصية"

#: includes/widgets/shortcode.php:106
msgid "Enter your shortcode"
msgstr "أدخل الرمز الخاص بك"

#: includes/widgets/image.php:160
msgid "Enter your image caption"
msgstr "أدخل عنونا او شرحا موجزا للصورة"

#: includes/widgets/html.php:103
msgid "Enter your code"
msgstr "أدخل الكود الخاص بك"

#: includes/widgets/heading.php:172
msgid "Add Your Heading Text Here"
msgstr "أضف النص الخاص بالعنوان هنا"

#: includes/widgets/alert.php:132 includes/widgets/icon-box.php:168
#: includes/widgets/image-box.php:144
msgid "Enter your description"
msgstr "أدخل الوصف الخاص بك"

#. translators: 1: Saving date, 2: Author display name.
#: core/base/document.php:1540
msgid "Draft saved on %1$s by %2$s"
msgstr "تم حفظ المسودة %1$s بواسطة %2$s"

#: core/base/document.php:1534
msgctxt "revision date format"
msgid "M j, H:i"
msgstr "M j, H:i"

#: core/common/modules/finder/template.php:19 assets/js/editor.js:11247
#: assets/js/kit-library.ac1a4cd5deae5526ce49.bundle.js:1891
msgid "No Results Found"
msgstr "لا توجد نتائج"

#: includes/editor-templates/templates.php:264
#: assets/js/onboarding.001cbdc1993cabe048db.bundle.js:2146
#: assets/js/onboarding.001cbdc1993cabe048db.bundle.js:2490
msgid "or"
msgstr "أو"

#: includes/editor-templates/templates.php:160
msgid "Favorite"
msgstr "المفضلة"

#: includes/editor-templates/templates.php:123
msgid "Creation Date"
msgstr "تاريخ الإنشاء"

#: includes/editor-templates/templates.php:119
msgid "Created By"
msgstr "أنشئت بواسطة"

#: includes/editor-templates/templates.php:103
msgid "Search"
msgstr "البحث"

#: includes/editor-templates/templates.php:72
#: assets/js/kit-library.ac1a4cd5deae5526ce49.bundle.js:3764
msgid "Popular"
msgstr "شائع"

#: includes/editor-templates/templates.php:68
#: assets/js/kit-library.ac1a4cd5deae5526ce49.bundle.js:3760
msgid "New"
msgstr "جديد"

#: includes/editor-templates/templates.php:11
#: includes/editor-templates/templates.php:12
msgid "Import Template"
msgstr "إستيراد القالب"

#: core/kits/views/panel.php:44 includes/controls/icons.php:83
#: includes/controls/icons.php:85 includes/controls/media.php:216
#: includes/controls/media.php:218 includes/controls/media.php:295
#: includes/controls/media.php:297 includes/editor-templates/repeater.php:27
#: modules/promotions/widgets/pro-widget-promotion.php:64
#: assets/js/ai-admin.js:2927 assets/js/ai-admin.js:7425
#: assets/js/ai-gutenberg.js:4687 assets/js/ai-gutenberg.js:9267
#: assets/js/ai-layout.js:3667 assets/js/ai-media-library.js:4556
#: assets/js/ai-media-library.js:9054 assets/js/ai.js:5319
#: assets/js/ai.js:10327
#: assets/js/kit-library.ac1a4cd5deae5526ce49.bundle.js:919
msgid "Remove"
msgstr "إزالة"

#: includes/editor-templates/hotkeys.php:73
#: includes/editor-templates/repeater.php:21 assets/js/editor.js:30617
#: assets/js/editor.js:50897
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:20
msgid "Duplicate"
msgstr "تكرار"

#: includes/editor-templates/panel.php:161
#: includes/editor-templates/panel.php:163 assets/js/editor.js:36480
msgid "Hide Panel"
msgstr "إخفاء اللوحة"

#: includes/editor-templates/panel.php:143
#: assets/js/packages/editor-app-bar/editor-app-bar.js:2
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:16
msgid "Save Draft"
msgstr "حفظ المسودة"

#: includes/editor-templates/panel.php:128
#: includes/editor-templates/panel.php:130
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:18
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:19
msgid "Save Options"
msgstr "حفظ الخصائص"

#: core/base/document.php:171 includes/editor-templates/panel.php:123
#: assets/js/editor.js:25564
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:20
msgid "Publish"
msgstr "نشر"

#: includes/controls/groups/typography.php:190
msgctxt "Typography Control"
msgid "Line Through"
msgstr "خلالها خط"

#: includes/controls/groups/typography.php:183
msgctxt "Typography Control"
msgid "Decoration"
msgstr "زخرفة"

#: includes/controls/dimensions.php:148
msgid "Unlinked values"
msgstr "قيم غير مرتبطة"

#: core/admin/admin.php:618
msgid "Blog"
msgstr "مدونة"

#: core/admin/admin.php:566
msgid "News & Updates"
msgstr "الأخبار والتحديثات"

#: core/admin/admin.php:533
msgctxt "Dashboard Overview Widget Recently Date"
msgid "M jS"
msgstr "M jS"

#: core/admin/admin.php:525
msgid "Recently Edited"
msgstr "تم تعديلها مؤخرا"

#: core/document-types/page-base.php:215
msgid "Excerpt"
msgstr "مقتطف"

#: core/admin/admin.php:429
msgid "Elementor Overview"
msgstr "نظرة عامة Elementor"

#: core/kits/documents/kit.php:156
#: modules/history/views/revisions-panel-template.php:64
msgid "Published"
msgstr "منشور"

#: modules/history/revisions-manager.php:157
msgid "Current Version"
msgstr "النسخة الحالية"

#: includes/editor-templates/templates.php:253
#: includes/editor-templates/templates.php:269
#: includes/editor-templates/templates.php:282
#: includes/widgets/traits/button-trait.php:56 assets/js/app.js:7574
#: assets/js/app.js:8580
#: assets/js/onboarding.001cbdc1993cabe048db.bundle.js:2502
msgid "Click here"
msgstr "أُنقر هنا"

#: includes/widgets/alert.php:119
msgid "This is an Alert"
msgstr "هذا تنبيه"

#: includes/editor-templates/templates.php:252
#: includes/editor-templates/templates.php:268
#: includes/editor-templates/templates.php:281
msgid "Want to learn more about the Elementor library?"
msgstr "هل تريد معرفة المزيد عن مكتبة Elementor؟"

#: includes/editor-templates/templates.php:94
msgid "My Favorites"
msgstr "مفضلتي"

#: includes/editor-templates/panel.php:147 assets/js/editor.js:33195
#: assets/js/editor.js:33694
#: assets/js/packages/editor-app-bar/editor-app-bar.js:2
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:15
msgid "Save as Template"
msgstr "حفظ كقالب"

#: includes/editor-templates/panel.php:110
#: includes/editor-templates/panel.php:113
#: assets/js/packages/editor-app-bar/editor-app-bar.js:2
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:22
msgid "Preview Changes"
msgstr "معاينة التغييرات"

#: includes/controls/groups/typography.php:188
msgctxt "Typography Control"
msgid "Underline"
msgstr "تحته خط"

#: core/admin/admin.php:475
msgid "Create New Page"
msgstr "إنشاء صفحة جديدة"

#: includes/editor-templates/templates.php:265 assets/js/app-packages.js:2531
#: assets/js/app.js:3652
msgid "Select File"
msgstr "اختيار ملف"

#: core/experiments/manager.php:644 includes/controls/popover-toggle.php:71
#: includes/controls/popover-toggle.php:73
msgid "Back to default"
msgstr "العودة إلى الإفتراضي"

#. translators: %s: Document title.
#: core/base/document.php:198
msgid "Hurray! Your %s is live."
msgstr "أهلا بك. صفحتك أصبحت منشورة أونلاين"

#: includes/editor-templates/repeater.php:14
msgid "Drag & Drop"
msgstr "السحب & الإفلات"

#: core/admin/admin.php:598
msgid "(opens in a new window)"
msgstr "(الفتح في نافذة جديدة)"

#: includes/editor-templates/templates.php:262
msgid "Import Template to Your Library"
msgstr "استيراد القالب إلى مكتبتك"

#: includes/editor-templates/panel-elements.php:78
msgid "Search Widget:"
msgstr "البحث عن ودجت:"

#: includes/widgets/accordion.php:195 includes/widgets/toggle.php:198
#: modules/nested-tabs/widgets/nested-tabs.php:118
msgid "Active Icon"
msgstr "أيقونة النشِط"

#: includes/controls/groups/typography.php:189
msgctxt "Typography Control"
msgid "Overline"
msgstr "فوقه خط"

#: includes/editor-templates/templates.php:70
msgid "Trend"
msgstr "رائج"

#. translators: 1: Editing date, 2: Author display name.
#: core/base/document.php:1547
msgid "Last edited on %1$s by %2$s"
msgstr "أخر تعديل تم %1$s بواسطة %2$s"

#: includes/editor-templates/templates.php:263
msgid "Drag & drop your .JSON or .zip template file"
msgstr "قم بسحب وإفلات ملف قالبك من نوع JSON. أو zip."

#: includes/template-library/sources/local.php:493
#: includes/template-library/sources/local.php:609
#: includes/template-library/sources/local.php:751
msgid "Access denied."
msgstr "غير مصرح بالدخول"

#: includes/settings/settings.php:281
msgid "Disable Default Fonts"
msgstr "تعطيل الخطوط الإفتراضية"

#: includes/controls/groups/flex-container.php:105
#: includes/controls/groups/flex-container.php:141
#: includes/controls/groups/flex-container.php:211
#: includes/controls/groups/flex-item.php:63
#: includes/controls/groups/flex-item.php:89
#: includes/controls/groups/grid-container.php:143
#: includes/controls/groups/grid-container.php:171
#: includes/controls/groups/grid-container.php:200
#: includes/controls/groups/grid-container.php:240
#: includes/elements/column.php:217 includes/widgets/accordion.php:409
#: includes/widgets/common.php:311 includes/widgets/counter.php:274
#: includes/widgets/counter.php:308 includes/widgets/counter.php:382
#: includes/widgets/counter.php:418 includes/widgets/icon-list.php:567
#: includes/widgets/image-carousel.php:693 includes/widgets/rating.php:203
#: includes/widgets/tabs.php:199 includes/widgets/tabs.php:229
#: includes/widgets/toggle.php:433 includes/widgets/traits/button-trait.php:150
#: includes/widgets/traits/button-trait.php:294
#: modules/nested-accordion/widgets/nested-accordion.php:165
#: modules/nested-accordion/widgets/nested-accordion.php:205
#: modules/nested-tabs/widgets/nested-tabs.php:223
#: modules/nested-tabs/widgets/nested-tabs.php:265
#: modules/nested-tabs/widgets/nested-tabs.php:335
msgid "End"
msgstr "النهاية"

#: includes/controls/groups/flex-container.php:97
#: includes/controls/groups/flex-container.php:133
#: includes/controls/groups/flex-container.php:210
#: includes/controls/groups/flex-item.php:55
#: includes/controls/groups/flex-item.php:85
#: includes/controls/groups/grid-container.php:135
#: includes/controls/groups/grid-container.php:163
#: includes/controls/groups/grid-container.php:192
#: includes/controls/groups/grid-container.php:232
#: includes/elements/column.php:215 includes/widgets/accordion.php:405
#: includes/widgets/common.php:303 includes/widgets/counter.php:270
#: includes/widgets/counter.php:300 includes/widgets/counter.php:374
#: includes/widgets/counter.php:410 includes/widgets/icon-list.php:559
#: includes/widgets/image-carousel.php:685 includes/widgets/rating.php:195
#: includes/widgets/tabs.php:191 includes/widgets/tabs.php:221
#: includes/widgets/toggle.php:429 includes/widgets/traits/button-trait.php:146
#: includes/widgets/traits/button-trait.php:286
#: modules/nested-accordion/widgets/nested-accordion.php:157
#: modules/nested-accordion/widgets/nested-accordion.php:201
#: modules/nested-tabs/widgets/nested-tabs.php:215
#: modules/nested-tabs/widgets/nested-tabs.php:257
#: modules/nested-tabs/widgets/nested-tabs.php:327
msgid "Start"
msgstr "البداية"

#: core/debug/classes/inspection-base.php:25
#: core/debug/loading-inspection-manager.php:37
msgid "The preview could not be loaded"
msgstr "لا يمكن تحميل المعاينة"

#: core/admin/admin-notices.php:149 core/admin/admin-notices.php:184
msgid "Update Notification"
msgstr "تحديث الاشعارات"

#. Author URI of the plugin
#: elementor.php
msgid "https://elementor.com/?utm_source=wp-plugins&utm_campaign=author-uri&utm_medium=wp-dash"
msgstr "https://elementor.com/?utm_source=wp-plugins&utm_campaign=author-uri&utm_medium=wp-dash"

#. Plugin URI of the plugin
#: elementor.php
msgid "https://elementor.com/?utm_source=wp-plugins&utm_campaign=plugin-uri&utm_medium=wp-dash"
msgstr "https://elementor.com/?utm_source=wp-plugins&utm_campaign=plugin-uri&utm_medium=wp-dash"

#: modules/history/views/history-panel-template.php:25
msgid "Once you start working, you'll be able to redo / undo any action you make in the editor."
msgstr "بعد بدء العمل، ستتمكن من إعادة / التراجع عن أي إجراء تقوم به في المحرر."

#: modules/history/views/history-panel-template.php:17
msgid "Switch to Revisions tab for older versions"
msgstr "التبديل إلى علامة التبويب المراجعات للإصدارات الأقدم"

#: modules/history/views/history-panel-template.php:10
#: assets/js/editor.js:50619
msgid "Revisions"
msgstr "المراجعات"

#: includes/editor-templates/hotkeys.php:144
#: includes/editor-templates/panel.php:100
#: includes/editor-templates/panel.php:102 assets/js/ai-admin.js:2672
#: assets/js/ai-gutenberg.js:4432 assets/js/ai-media-library.js:4301
#: assets/js/ai.js:5064 assets/js/editor.js:51221
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:9
msgid "History"
msgstr "السّجل"

#: includes/editor-templates/hotkeys.php:19
#: includes/editor-templates/templates.php:126
#: modules/history/views/history-panel-template.php:9 assets/js/editor.js:50616
msgid "Actions"
msgstr "إجراءات"

#: modules/history/views/history-panel-template.php:24
msgid "No History Yet"
msgstr "لا يوجد سجل محفوظات بعد"

#: core/kits/documents/tabs/settings-lightbox.php:141
#: includes/widgets/video.php:886
msgid "UI Color"
msgstr "لون واجهة المستخدم"

#: core/kits/documents/tabs/settings-lightbox.php:152
#: includes/widgets/video.php:898
msgid "UI Hover Color"
msgstr "لون واجهة المستخدم عند التمرير"

#: includes/widgets/video.php:369
msgid "Mute"
msgstr "كتم الصوت"

#: core/kits/documents/tabs/settings-lightbox.php:48
msgid "Open all image links in a lightbox popup window. The lightbox will automatically work on any link that leads to an image file."
msgstr "أفتح كل روابط الصور عبر نافذة بوب أب ، سيعمل البرنامج تلقائياً على اي رابط يؤدي الى الصورة"

#: includes/template-library/sources/local.php:978
msgid "Choose an Elementor template JSON file or a .zip archive of Elementor templates, and add them to the list of templates available in your library."
msgstr "أختر قالب عنصر بصيغة JSON او ZIP واضف إلى قائمة العناصر المتاحة فى المكتبة"

#: core/kits/documents/tabs/settings-lightbox.php:45
msgid "Image Lightbox"
msgstr "الصندوق الضوئي للصورة"

#: includes/settings/tools.php:401
msgid "Please Note: We do not recommend updating to a beta version on production sites."
msgstr "يرجى الملاحظة : أننا لا ننصح بالتحديث لبرنامج البيتا على المواقع المنتجة للمحتوى ."

#: includes/settings/tools.php:393
msgid "Beta Tester"
msgstr "مجرب البيتا"

#: includes/settings/tools.php:377
msgid "Become a Beta Tester"
msgstr "كُن مجرب بيتا"

#: includes/rollback.php:165 includes/settings/tools.php:175
#: includes/settings/tools.php:349 assets/js/admin.js:2309
msgid "Rollback to Previous Version"
msgstr "الرجوع إلى النسخة السابقة"

#: core/common/modules/finder/categories/tools.php:77
#: includes/settings/tools.php:346
msgid "Version Control"
msgstr "التحكم في النسخة"

#. translators: %s: Elementor version.
#: includes/settings/tools.php:353
msgid "Experiencing an issue with Elementor version %s? Rollback to a previous version before the issue appeared."
msgstr "Experiencing an issue with Elementor version %s? Rollback to a previous version before the issue appeared."

#: includes/settings/settings.php:327
msgid "Switch Editor Loader Method"
msgstr "Switch Editor Loader Method"

#: includes/settings/settings.php:297
#: assets/js/packages/editor-app-bar-ui/editor-app-bar-ui.js:2
#: assets/js/packages/editor-app-bar-ui/editor-app-bar-ui.strings.js:3
msgid "Integrations"
msgstr "التكامل"

#: includes/elements/column.php:754 includes/elements/container.php:1612
#: includes/elements/section.php:1211 includes/widgets/common.php:558
msgid "Z-Index"
msgstr "Z-الفهرس"

#: core/kits/documents/tabs/theme-style-form-fields.php:137
#: core/kits/documents/tabs/theme-style-images.php:203
#: includes/base/element-base.php:1251
#: includes/controls/groups/background.php:672 includes/elements/column.php:318
#: includes/elements/column.php:478 includes/elements/column.php:586
#: includes/elements/container.php:720 includes/elements/container.php:939
#: includes/elements/container.php:1088 includes/elements/section.php:580
#: includes/elements/section.php:767 includes/elements/section.php:874
#: includes/widgets/alert.php:428 includes/widgets/common.php:718
#: includes/widgets/common.php:833 includes/widgets/google-maps.php:234
#: includes/widgets/icon-list.php:453 includes/widgets/icon-list.php:680
#: includes/widgets/image-box.php:464 includes/widgets/image.php:483
#: includes/widgets/traits/button-trait.php:434
#: modules/floating-buttons/base/widget-contact-button-base.php:1435
#: modules/floating-buttons/base/widget-contact-button-base.php:2174
#: modules/nested-tabs/widgets/nested-tabs.php:592
#: modules/shapes/widgets/text-path.php:443
#: modules/shapes/widgets/text-path.php:621
msgid "Transition Duration"
msgstr "مدة الانتقال"

#. Translators: %s: Element name.
#. Translators: %s: Element Name.
#: core/document-types/page-base.php:184 assets/js/editor.js:30414
#: assets/js/editor.js:30600 assets/js/editor.js:32779
#: assets/js/editor.js:33243 assets/js/editor.js:33344
#: assets/js/editor.js:33673 assets/js/editor.js:37078
msgid "Edit %s"
msgstr "تحرير %s"

#: includes/controls/url.php:103 includes/controls/url.php:105
msgid "Link Options"
msgstr "خيارات الرابط"

#: includes/controls/url.php:113
msgid "Open in new window"
msgstr "فتح في نافذة جديدة"

#: core/kits/documents/tabs/settings-layout.php:101
#: includes/elements/column.php:231
msgid "Widgets Space"
msgstr "الفراغ بين الودجات"

#: includes/controls/groups/box-shadow.php:72
msgctxt "Box Shadow Control"
msgid "Outline"
msgstr "حد خارجي"

#: includes/settings/tools.php:380
msgid "Turn-on Beta Tester, to get notified when a new beta version of Elementor or Elementor Pro is available. The Beta version will not install automatically. You always have the option to ignore it."
msgstr "قم بتشغيل اختبار بيتا  ، ليتم إعلامك عند توفر إصدار تجريبي جديد من اليمنتور أو المينتور المطوّر . لن يتم تثبيت إصدار بيتا تلقائيًا. لديك دائمًا خيار تجاهل ذلك."

#: includes/elements/column.php:863 includes/elements/container.php:1708
#: includes/elements/section.php:1300 includes/widgets/common.php:652
#: modules/floating-buttons/base/widget-contact-button-base.php:1358
msgid "Animation Delay"
msgstr "مُهلة الحركة"

#: includes/controls/url.php:117
msgid "Add nofollow"
msgstr "أضف لا اتّباع"

#: includes/settings/tools.php:370
msgid "Warning: Please backup your database before making the rollback."
msgstr "تحذير : يرجى إجراء نسخ احتياطي لقاعدة بياناتك قبل تنفيذ الرجوع إلى الإصدار الأقدم."

#: includes/settings/tools.php:362
msgid "Rollback Version"
msgstr "الرجوع إلى إصدار أقدم"

#: core/kits/documents/tabs/settings-layout.php:118
msgid "Sets the default space between widgets (Default: 20px)"
msgstr "ضع المسافة الإفتراضية بين القطع (القيمة الإفتراضية:20px)"

#: includes/settings/settings.php:405
msgid "Internal Embedding"
msgstr "التضمين الداخلي"

#: includes/settings/settings.php:404
msgid "External File"
msgstr "ملف خارجي"

#: includes/settings/settings.php:398
msgid "CSS Print Method"
msgstr "طريقة طباعة css"

#: includes/settings/settings.php:335
msgid "For troubleshooting server configuration conflicts."
msgstr "لحل مشكلات تعارض تكوين الخادم."

#: core/debug/inspector.php:55 includes/settings/settings.php:333
#: includes/settings/settings.php:345 includes/settings/settings.php:356
#: includes/settings/settings.php:420 includes/settings/settings.php:437
#: includes/settings/tools.php:399 modules/generator-tag/module.php:81
#: modules/nested-tabs/widgets/nested-tabs.php:363
#: modules/safe-mode/module.php:48 assets/js/admin.js:298
#: assets/js/app-packages.js:2839 assets/js/app.js:3960
#: assets/js/common.js:2146 assets/js/editor.js:39930
msgid "Enable"
msgstr "تفعيل"

#: core/debug/inspector.php:54 includes/settings/settings.php:332
#: includes/settings/settings.php:344 includes/settings/settings.php:357
#: includes/settings/settings.php:421 includes/settings/settings.php:438
#: includes/settings/tools.php:398 modules/generator-tag/module.php:82
#: modules/nested-tabs/widgets/nested-tabs.php:362
#: modules/safe-mode/module.php:47
msgid "Disable"
msgstr "تعطيل"

#: core/base/document.php:1967 assets/js/element-manager-admin.js:2297
#: assets/js/element-manager-admin.js:2374
msgid "Status"
msgstr "الحالة"

#: includes/maintenance-mode.php:251
msgid "Choose Template"
msgstr "اختيار القالب"

#: includes/maintenance-mode.php:370
msgid "To enable maintenance mode you have to set a template for the maintenance mode page."
msgstr "لتمكين وضع الصيانة لديك يجب تعيين قالب لصفحة وضع الصيانة."

#: includes/maintenance-mode.php:292 includes/maintenance-mode.php:369
#: includes/template-library/sources/local.php:231 assets/js/app.js:10296
msgid "Edit Template"
msgstr "تحرير القالب"

#: includes/maintenance-mode.php:237
msgid "Logged In"
msgstr "تم تسجيل الدخول"

#: includes/maintenance-mode.php:231
msgid "Who Can Access"
msgstr "من لديه صلاحية الوصول"

#: includes/maintenance-mode.php:223
msgid "Maintenance Mode returns HTTP 503 code, so search engines know to come back a short time later. It is not recommended to use this mode for more than a couple of days."
msgstr "يقوم وضع الصيانة بإرجاع HTTP 503 code، لذلك تعرف محركات البحث أنك ستعود بعد وقت قصير. لا ينصح باستخدام هذا الوضع لأكثر من بضعة أيام."

#: includes/maintenance-mode.php:217
msgid "Maintenance"
msgstr "صيانة"

#: includes/maintenance-mode.php:216
msgid "Coming Soon"
msgstr "قريبا"

#: includes/maintenance-mode.php:210
msgid "Choose Mode"
msgstr "اختر وضع"

#: includes/maintenance-mode.php:206
msgid "Set your entire website as MAINTENANCE MODE, meaning the site is offline temporarily for maintenance, or set it as COMING SOON mode, meaning the site is offline until it is ready to be launched."
msgstr "ضع موقعك بالكامل في وضع الصيانة، مما يعني أن الموقع غير متصل مؤقتا للصيانة، أو تعيينه على قريبا، مما يعني أن الموقع غير متصل حتى يصبح جاهزا للإطلاق."

#: includes/widgets/common.php:1091 includes/widgets/spacer.php:112
#: includes/widgets/text-editor.php:374
msgid "Space"
msgstr "مسافة"

#: core/kits/documents/tabs/settings-layout.php:146
msgid "Elementor lets you hide the page title. This works for themes that have \"h1.entry-title\" selector. If your theme's selector is different, please enter it above."
msgstr "إليمنتور يتيح لك إخفاء عنوان الصفحة. هذا يعمل للمواضيع التي لديها \"h1.entry-title\" محدد. إذا كان المحدد مختلفا، فالرجاء إدخاله أعلاه."

#: core/kits/documents/tabs/settings-layout.php:142
msgid "Page Title Selector"
msgstr "محدد عنوان الصفحة"

#: includes/admin-templates/new-template.php:75
#: includes/settings/controls.php:155
msgid "Select"
msgstr "تحديد"

#: core/common/modules/finder/categories/edit.php:118 assets/js/editor.js:18963
msgid "Template"
msgstr "قالب"

#: core/common/modules/finder/categories/tools.php:62
#: includes/maintenance-mode.php:201 includes/maintenance-mode.php:205
msgid "Maintenance Mode"
msgstr "وضع الصيانة"

#: core/document-types/page-base.php:96
msgid "Hide Title"
msgstr "اخفاء العنوان"

#: includes/maintenance-mode.php:283
msgid "Maintenance Mode ON"
msgstr "وضع الصيانة يعمل"

#: includes/maintenance-mode.php:226
msgid "Coming Soon returns HTTP 200 code, meaning the site is ready to be indexed."
msgstr "وضع Coming Soon يرد بكود HTTP 200 والذي يعني ان الموقع جاهز للفهرسة"

#: includes/widgets/text-editor.php:124 includes/widgets/text-editor.php:281
msgid "Drop Cap"
msgstr "الحرف الكبير"

#: core/kits/documents/kit.php:155 includes/maintenance-mode.php:215
#: assets/js/editor.js:50896
msgid "Disabled"
msgstr "معطّل"

#: includes/editor-templates/hotkeys.php:57
msgid "Paste Style"
msgstr "لصق التنسيق"

#: includes/maintenance-mode.php:220
msgid "Choose between Coming Soon mode (returning HTTP 200 code) or Maintenance Mode (returning HTTP 503 code)."
msgstr "الاختيار بين وضع قريباً (يعيد حالة HTTP200) أو وضع الصيانة (يعيد حالة HTTP503)."

#: includes/maintenance-mode.php:243
msgid "Roles"
msgstr "الأدوار"

#: includes/elements/container.php:1282 includes/elements/section.php:1062
msgid "Bring to Front"
msgstr "اجلب للمقدمة"

#: includes/controls/groups/flex-container.php:109
#: includes/controls/groups/flex-container.php:212
#: includes/controls/groups/grid-container.php:204
#: includes/controls/groups/grid-container.php:244
#: includes/elements/column.php:190 includes/elements/column.php:218
#: includes/elements/section.php:428 includes/widgets/icon-list.php:220
#: includes/widgets/toggle.php:299
msgid "Space Between"
msgstr "المسافة بين"

#: includes/widgets/icon-list.php:212
msgid "List"
msgstr "قائمة"

#: includes/shapes.php:212
msgctxt "Shapes"
msgid "Book"
msgstr "كتاب"

#: includes/shapes.php:208
msgctxt "Shapes"
msgid "Split"
msgstr "تقسيم"

#: includes/shapes.php:204
msgctxt "Shapes"
msgid "Arrow"
msgstr "سهم"

#: includes/shapes.php:196
msgctxt "Shapes"
msgid "Waves Brush"
msgstr "فرشاة مموجه"

#: includes/shapes.php:191
msgctxt "Shapes"
msgid "Waves"
msgstr "مموج"

#: includes/shapes.php:186
msgctxt "Shapes"
msgid "Curve Asymmetrical"
msgstr "منحنى غير متناظر"

#: includes/shapes.php:182
msgctxt "Shapes"
msgid "Curve"
msgstr "منحنى"

#: includes/shapes.php:165
msgctxt "Shapes"
msgid "Triangle Asymmetrical"
msgstr "مثلث غير متناظر"

#: includes/shapes.php:161
msgctxt "Shapes"
msgid "Triangle"
msgstr "مثلث"

#: includes/shapes.php:153 includes/widgets/divider.php:171
#: includes/widgets/divider.php:301
msgctxt "Shapes"
msgid "Zigzag"
msgstr "متعرج"

#: includes/shapes.php:147
msgctxt "Shapes"
msgid "Clouds"
msgstr "سحاب"

#: includes/shapes.php:141
msgctxt "Shapes"
msgid "Drops"
msgstr "قطرات"

#: includes/shapes.php:137
msgctxt "Shapes"
msgid "Mountains"
msgstr "جبال"

#: includes/elements/container.php:1269 includes/elements/section.php:1049
msgid "Invert"
msgstr "عكس"

#: includes/elements/container.php:1255 includes/elements/section.php:1035
msgid "Flip"
msgstr "قلب"

#: includes/elements/container.php:1139 includes/elements/section.php:919
msgid "Shape Divider"
msgstr "مقسم الشكل"

#: includes/shapes.php:200
msgctxt "Shapes"
msgid "Waves Pattern"
msgstr "نمط الموجات"

#: includes/shapes.php:175
msgctxt "Shapes"
msgid "Tilt Opacity"
msgstr "وضوح الامالة"

#: includes/shapes.php:179
msgctxt "Shapes"
msgid "Fan Opacity"
msgstr "وضوح المروحة"

#: includes/shapes.php:170
msgctxt "Shapes"
msgid "Tilt"
msgstr "امالة"

#: includes/shapes.php:156
msgctxt "Shapes"
msgid "Pyramids"
msgstr "أهرامات"

#: core/kits/documents/tabs/settings-lightbox.php:18
#: includes/widgets/image-carousel.php:345
#: includes/widgets/image-gallery.php:182 includes/widgets/image.php:207
#: includes/widgets/video.php:714 includes/widgets/video.php:862
msgid "Lightbox"
msgstr "نافذة منبثقة (Lightbox)"

#: includes/widgets/tabs.php:257
msgid "Navigation Width"
msgstr "عرض المستكشف"

#: core/kits/documents/tabs/theme-style-buttons.php:166
#: core/kits/documents/tabs/theme-style-images.php:134
#: core/kits/documents/tabs/theme-style-typography.php:149
#: includes/base/element-base.php:871 includes/elements/column.php:303
#: includes/elements/column.php:432 includes/elements/column.php:551
#: includes/elements/container.php:705 includes/elements/container.php:883
#: includes/elements/container.php:1041 includes/elements/section.php:565
#: includes/elements/section.php:721 includes/elements/section.php:839
#: includes/widgets/alert.php:411 includes/widgets/common.php:703
#: includes/widgets/common.php:798 includes/widgets/google-maps.php:219
#: includes/widgets/icon-box.php:414 includes/widgets/icon-list.php:433
#: includes/widgets/icon-list.php:661 includes/widgets/icon.php:251
#: includes/widgets/image-box.php:431 includes/widgets/image.php:450
#: includes/widgets/traits/button-trait.php:382
#: modules/floating-buttons/base/widget-contact-button-base.php:1194
#: modules/floating-buttons/base/widget-contact-button-base.php:1953
#: modules/floating-buttons/base/widget-contact-button-base.php:2435
#: modules/floating-buttons/base/widget-contact-button-base.php:2615
#: modules/nested-accordion/widgets/nested-accordion.php:648
#: modules/nested-accordion/widgets/nested-accordion.php:715
#: modules/nested-tabs/widgets/nested-tabs.php:527
#: modules/nested-tabs/widgets/nested-tabs.php:767
#: modules/nested-tabs/widgets/nested-tabs.php:969
#: modules/shapes/widgets/text-path.php:416
#: modules/shapes/widgets/text-path.php:555
msgid "Hover"
msgstr "مرور فوق العنصر"

#: includes/elements/column.php:775 includes/elements/container.php:1633
#: includes/elements/section.php:1232 includes/widgets/common.php:578
#: includes/widgets/traits/button-trait.php:213
#: modules/floating-buttons/base/widget-contact-button-base.php:3040
#: modules/nested-accordion/widgets/nested-accordion.php:122
#: modules/nested-tabs/widgets/nested-tabs.php:141
msgid "Add your custom id WITHOUT the Pound key. e.g: my-id"
msgstr "اضف المعرف الخاص بدون المفتاح . مثال : my-id"

#: includes/elements/column.php:766 includes/elements/container.php:1624
#: includes/elements/section.php:1223 includes/widgets/common.php:569
#: modules/floating-buttons/base/widget-contact-button-base.php:3031
#: modules/nested-accordion/widgets/nested-accordion.php:113
#: modules/nested-tabs/widgets/nested-tabs.php:132
msgid "CSS ID"
msgstr "معرف الـ ID"

#: includes/controls/groups/background.php:183
#: includes/controls/groups/background.php:212
msgctxt "Background Control"
msgid "Location"
msgstr "موقع"

#: includes/controls/groups/background.php:229
msgctxt "Background Control"
msgid "Type"
msgstr "نوع"

#: includes/settings/settings.php:285
msgid "Checking this box will disable Elementor's Default Fonts, and make Elementor inherit the fonts from your theme."
msgstr "سيؤدي تحديد هذا المربع إلى تعطيل خطوط إليمنتور الافتراضية، وجعل إليمنتور يستقبل الخطوط من الثيم."

#: core/admin/admin.php:379
msgid "Video Tutorials"
msgstr "دروس الفيديو"

#: core/admin/admin.php:379
msgid "View Elementor Video Tutorials"
msgstr "عرض الفيديو التعليمي لإليمنتور"

#: core/admin/admin.php:378
msgid "Docs & FAQs"
msgstr "المستندات والأسئلة الشائعة"

#: core/admin/admin.php:378
msgid "View Elementor Documentation"
msgstr "مشاهدة مستندات اليمنتور"

#: includes/settings/settings.php:277
msgid "Checking this box will disable Elementor's Default Colors, and make Elementor inherit the colors from your theme."
msgstr "هذا الخيار يمنع المحرر من استخدام الالوان الافتراضية وسيجعله يأخذ الالوان من الثيم الخاص بك."

#: includes/settings/tools.php:333
msgid "Update Site Address (URL)"
msgstr "تحديث عنوان الموقع (URL)"

#: core/common/modules/finder/categories/tools.php:56
#: includes/settings/tools.php:315 includes/settings/tools.php:319
#: includes/settings/tools.php:336
msgid "Replace URL"
msgstr "استبدال رابط الموقع"

#: modules/history/revisions-manager.php:160
msgid "Autosave"
msgstr "حفظ تلقائي"

#: modules/history/revisions-manager.php:163
msgid "Revision"
msgstr "مراجعة"

#: modules/history/views/revisions-panel-template.php:28
msgid "It looks like the post revision feature is unavailable in your website."
msgstr "يبدو ان ميزة مراجعة المقالات غير متوفرة في موقعك"

#: modules/history/views/revisions-panel-template.php:26
msgid "Revision history lets you save your previous versions of your work, and restore them any time."
msgstr "تاريخ المراجعات يمكنك من حفظ النسخ السابقة من عملك ، واعادتها في اي وقت"

#: modules/apps/admin-apps-page.php:174
#: modules/history/views/revisions-panel-template.php:55
msgid "By"
msgstr "بواسطة"

#: modules/history/views/revisions-panel-template.php:39
msgid "No Revisions Saved Yet"
msgstr "لا توجد مراجعات محفوظة حتى الان"

#: includes/settings/tools.php:337
msgid "Enter your old and new URLs for your WordPress installation, to update all Elementor data (Relevant for domain transfers or move to 'HTTPS')."
msgstr "ضع الروابط القديمة والجديدة الخاصة بتثبيت وردبريس . لتحديث كافة بيانات Elementor ( مناسب في حالة تغيير الدومين او الانتقال الى HTTPS )"

#. translators: 1: Human readable time difference, 2: Date.
#: modules/history/revisions-manager.php:179
msgid "%1$s ago (%2$s)"
msgstr "%1$s منذ (%2$s)"

#: modules/history/revisions-manager.php:151
msgctxt "revision date format"
msgid "M j @ H:i"
msgstr "M j @ H:i"

#: modules/history/views/revisions-panel-template.php:27
msgid "Start designing your page and you will be able to see the entire revision history here."
msgstr "إبدأ بتصميم صفحتك وسوف تكون قادراً على رؤية كامل تاريخ السجل هنا."

#: includes/widgets/counter.php:181
msgid "Thousand Separator"
msgstr "فاصلة الالاف"

#: includes/editor-templates/panel-elements.php:101
msgid "With this feature, you can save a widget as global, then add it to multiple areas. All areas will be editable from one single place."
msgstr "مع هذه الميزة، يمكنك حفظ ودجت كنموذج عام، ثم إضافتها إلى مناطق متعددة. وجميع المناطق ستكون قابلة للتحرير من مكان واحد."

#: includes/base/element-base.php:914 includes/base/element-base.php:1077
#: includes/widgets/common.php:876 includes/widgets/icon-list.php:268
#: includes/widgets/icon.php:325 includes/widgets/text-editor.php:127
#: includes/widgets/video.php:718 modules/shapes/widgets/text-path.php:202
msgid "On"
msgstr "تشغيل"

#: includes/managers/controls.php:1090
msgid "Meet Our Custom CSS"
msgstr "قابل منسق الـ CSS الخاص"

#: modules/promotions/widgets/pro-widget-promotion.php:65
#: assets/js/ai-admin.js:7929 assets/js/ai-gutenberg.js:9771
#: assets/js/ai-layout.js:4171 assets/js/ai-media-library.js:9558
#: assets/js/ai.js:10831
msgid "Go Pro"
msgstr "الذهاب الى النسخة المدفوعة"

#: includes/editor-templates/panel-elements.php:28
msgid "Get more with Elementor Pro"
msgstr "احصل على المزيد بالنسخة المدفوعة من Elementor"

#: includes/editor-templates/panel-elements.php:100
msgid "Meet Our Global Widget"
msgstr "قابل اضافتنا الشاملة"

#: core/kits/documents/tabs/settings-custom-css.php:17
#: includes/managers/controls.php:1072
msgid "Custom CSS"
msgstr "CSS مخصص"

#: includes/base/element-base.php:915 includes/base/element-base.php:1078
#: includes/widgets/common.php:877 includes/widgets/icon-list.php:267
#: includes/widgets/icon.php:324 includes/widgets/text-editor.php:126
#: includes/widgets/video.php:717 modules/shapes/widgets/text-path.php:203
msgid "Off"
msgstr "إيقاف"

#: includes/managers/controls.php:1078
msgid "Custom CSS lets you add CSS code to any widget, and see it render live right in the editor."
msgstr "أكواد CSS المخصصة تتيح لك إضافة أكواد CSS لأي ودجت، ورؤيتها تعمل مباشرة داخل المحرر."

#: includes/widgets/traits/button-trait.php:33
msgid "Extra Small"
msgstr "صغير جدا"

#: includes/settings/settings.php:291
msgid "Improve Elementor"
msgstr "تطوير Elementor"

#: includes/frontend.php:1214
msgid "Invalid Data: The Template ID cannot be the same as the currently edited template. Please choose a different one."
msgstr "بيانات غير صالحة: معرف القالب (ID) لا يمكن أن يكون مثل الخاص بالقالب المحرر حاليا. الرجاء اختيار واحد مختلف."

#: includes/base/widget-base.php:311 includes/base/widget-base.php:320
msgid "Skin"
msgstr "النمط"

#: includes/widgets/traits/button-trait.php:37
msgid "Extra Large"
msgstr "كبير جدًا"

#: includes/editor-templates/panel.php:183
msgid "Update changes to page"
msgstr "تحديث التغييرات للصفحة"

#: includes/editor-templates/panel.php:219
msgid "%s are disabled"
msgstr "%s غير ممكنة"

#: core/admin/admin-notices.php:244
msgid "No thanks"
msgstr "لا شكراً."

#: includes/elements/section.php:464
msgid "Stretch Section"
msgstr "تمديد القسم"

#: core/kits/documents/tabs/settings-layout.php:164
msgid "Enter parent element selector to which stretched sections will fit to (e.g. #primary / .wrapper / main etc). Leave blank to fit to page width."
msgstr "ضع محدد العنصر الرئيسي الذي تود تمديد المحتوى اليه ؟ (مثال: #primary / .wrapper / main ... الخ). اتركه فارغاً حتى يتناسق مع عرض الصفحة."

#: core/kits/documents/tabs/settings-layout.php:161
msgid "Stretched Section Fit To"
msgstr "المحتوى المتمدد يتناسق مع"

#: includes/elements/section.php:472
msgid "Stretch the section to the full width of the page using JS."
msgstr "تمديد الجزء الى العرض الكامل للصفحة باستخدام الجافاسكربت"

#: core/kits/documents/tabs/settings-layout.php:74
msgid "Sets the default width of the content area (Default: 1140px)"
msgstr "حدد العرض الافتراضي من منطقة المحتوى ( الافتراضي 1140px)"

#: includes/elements/section.php:1331
msgid "Reverse Columns"
msgstr "عكس الأعمدة"

#: core/settings/editor-preferences/model.php:125
#: assets/js/kit-library.ac1a4cd5deae5526ce49.bundle.js:4307
msgid "Mobile"
msgstr "الهاتف المحمول"

#: core/admin/admin-notices.php:233
msgid "Learn more."
msgstr "معرفة المزيد."

#: includes/controls/dimensions.php:141 includes/controls/dimensions.php:144
msgid "Link values together"
msgstr "ربط القيم مع بعضها"

#: includes/widgets/shortcode.php:42 includes/widgets/shortcode.php:99
msgid "Shortcode"
msgstr "الكود المختصر"

#: includes/editor-templates/templates.php:192
#: includes/template-library/sources/local.php:1163 assets/js/app.js:12017
msgid "Export"
msgstr "تصدير"

#: includes/editor-templates/templates.php:16
#: includes/editor-templates/templates.php:17 includes/settings/tools.php:303
#: includes/settings/tools.php:306
msgid "Sync Library"
msgstr "مزامنة المكتبة"

#: includes/settings/tools.php:307
msgid "Elementor Library automatically updates on a daily basis. You can also manually update it by clicking on the sync button."
msgstr "مكتبة Elementor يتم تحديثها بشكل يومي، يمكنك التحديث يدوياً بالنقر على زر المزامنة."

#: includes/template-library/sources/local.php:209
msgid "Local"
msgstr "محلي"

#: includes/template-library/sources/local.php:497
msgid "(no title)"
msgstr "(غير معنون)"

#: includes/template-library/sources/local.php:948
msgid "Export Template"
msgstr "تصدير النموذج"

#: includes/template-library/sources/local.php:985
msgid "Import Now"
msgstr "الاستيراد الآن"

#: includes/template-library/sources/remote.php:61
msgid "Remote"
msgstr "Remote"

#: includes/editor-templates/global.php:47
msgid "Add Template"
msgstr "اضافة نموذج"

#: includes/editor-templates/hotkeys.php:181
msgid "Template Library"
msgstr "مكتبة النماذج"

#: core/common/modules/connect/apps/library.php:16 assets/js/editor.js:10788
msgid "Library"
msgstr "مكتبة"

#: includes/editor-templates/templates.php:178
#: includes/editor-templates/templates.php:202
#: includes/editor-templates/templates.php:216 assets/js/ai-admin.js:6725
#: assets/js/ai-gutenberg.js:8567 assets/js/ai-media-library.js:8354
#: assets/js/ai.js:9627 assets/js/editor.js:8644
msgid "Insert"
msgstr "ادراج"

#: includes/editor-templates/templates.php:136
msgid "Stay tuned! More awesome templates coming real soon."
msgstr "كن بالقرب! المزيد من النماذج الرائعة ستتوفر قريباً."

#: includes/editor-templates/templates.php:243
msgid "Enter Template Name"
msgstr "أدخل اسم النموذج"

#: core/document-types/page.php:51 modules/library/documents/page.php:57
#: assets/js/editor.js:10433
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:2
msgid "Page"
msgstr "صفحة"

#: core/common/modules/finder/categories/tools.php:28
#: core/common/modules/finder/categories/tools.php:50
#: includes/settings/admin-menu-items/tools-menu-item.php:29
#: includes/settings/admin-menu-items/tools-menu-item.php:33
#: includes/settings/tools.php:32 includes/settings/tools.php:33
#: includes/settings/tools.php:434
msgid "Tools"
msgstr "أدوات"

#: includes/template-library/sources/local.php:277
msgctxt "Template Library"
msgid "Type"
msgstr "النوع"

#: includes/template-library/sources/local.php:976
msgid "Import Templates"
msgstr "إستيراد النماذج"

#: includes/editor-templates/templates.php:39
#: assets/js/kit-library.ac1a4cd5deae5526ce49.bundle.js:1485
msgid "Back to Library"
msgstr "العودة للمكتبة"

#: includes/template-library/sources/local.php:228
msgctxt "Template Library"
msgid "Template"
msgstr "النموذج"

#: core/base/document.php:170 includes/editor-templates/global.php:23
#: includes/editor-templates/responsive-bar.php:68
#: includes/editor-templates/responsive-bar.php:69 includes/frontend.php:1374
#: assets/js/ai-admin.js:1177 assets/js/ai-gutenberg.js:2937
#: assets/js/ai-layout.js:1105 assets/js/ai-media-library.js:2806
#: assets/js/ai.js:3569 assets/js/app-packages.js:2029
#: assets/js/app-packages.js:4006 assets/js/app-packages.js:4527
#: assets/js/app.js:2905 assets/js/app.js:5023 assets/js/app.js:5426
#: assets/js/app.js:7558 assets/js/app.js:8390 assets/js/app.js:11918
#: assets/js/e8a7573e654d921656ab.bundle.js:221 assets/js/editor.js:47286
#: assets/js/import-export-admin.js:328
msgid "Close"
msgstr "إغلاق"

#: core/common/modules/finder/categories/general.php:49
#: includes/template-library/sources/admin-menu-items/saved-templates-menu-item.php:23
#: includes/template-library/sources/local.php:1718 assets/js/app.js:11034
msgid "Saved Templates"
msgstr "القوالب المحفوطة"

#: core/kits/documents/tabs/global-typography.php:28
#: core/kits/documents/tabs/global-typography.php:47
#: assets/js/152486453d0e39071cdb.bundle.js:224 assets/js/app.js:11052
#: assets/js/editor.js:47031
msgid "Global Fonts"
msgstr "الخطوط العامة"

#: app/modules/import-export/module.php:152
#: app/modules/import-export/module.php:164 core/admin/admin-notices.php:330
#: modules/apps/admin-apps-page.php:184 modules/safe-mode/module.php:375
#: modules/safe-mode/module.php:384
#: modules/safe-mode/mu-plugin/elementor-safe-mode.php:106
#: assets/js/app-packages.js:2710 assets/js/app-packages.js:5696
#: assets/js/app-packages.js:5805 assets/js/app.js:3831 assets/js/app.js:7842
#: assets/js/app.js:8984 assets/js/app.js:10452 assets/js/app.js:10762
#: assets/js/app.js:10808 assets/js/app.js:11917 assets/js/editor.js:14655
#: assets/js/editor.js:28492 assets/js/editor.js:28523
#: assets/js/editor.js:40485 assets/js/element-manager-admin.js:2184
#: assets/js/kit-library.ac1a4cd5deae5526ce49.bundle.js:3795
msgid "Learn More"
msgstr "معرفة المزيد"

#: modules/floating-buttons/base/widget-contact-button-base.php:2157
msgid "Animation"
msgstr "الحركة"

#: core/base/traits/shared-widget-controls-trait.php:288
#: includes/widgets/icon-box.php:450 includes/widgets/icon.php:289
#: includes/widgets/image-box.php:485 includes/widgets/image.php:501
#: includes/widgets/social-icons.php:555
#: includes/widgets/traits/button-trait.php:449
#: modules/floating-buttons/base/widget-contact-button-base.php:1417
#: modules/floating-buttons/base/widget-contact-button-base.php:2453
#: modules/nested-tabs/widgets/nested-tabs.php:584
#: modules/shapes/widgets/text-path.php:435
msgid "Hover Animation"
msgstr "الحركة أثناء التمرير"

#: includes/elements/column.php:849 includes/elements/container.php:1694
#: includes/elements/section.php:1286 includes/widgets/common.php:638
#: modules/floating-buttons/base/widget-contact-button-base.php:1347
#: modules/floating-buttons/base/widget-contact-button-base.php:2730
msgid "Slow"
msgstr "بطئ"

#: includes/elements/column.php:851 includes/elements/container.php:1696
#: includes/elements/section.php:1288 includes/widgets/common.php:640
#: modules/floating-buttons/base/widget-contact-button-base.php:1349
#: modules/floating-buttons/base/widget-contact-button-base.php:2732
msgid "Fast"
msgstr "سريع"

#: includes/controls/box-shadow.php:78 includes/controls/text-shadow.php:66
msgid "Blur"
msgstr "الضبابية"

#: includes/controls/box-shadow.php:83
msgid "Spread"
msgstr "الانتشار"

#: includes/controls/box-shadow.php:68 includes/controls/text-shadow.php:71
#: includes/widgets/tabs.php:175
msgid "Horizontal"
msgstr "أفقي"

#: includes/controls/box-shadow.php:73 includes/controls/text-shadow.php:76
#: includes/widgets/tabs.php:171
msgid "Vertical"
msgstr "رأسي"

#: includes/elements/column.php:836 includes/elements/container.php:1681
#: includes/elements/section.php:1273 includes/widgets/common.php:625
#: includes/widgets/video.php:910
#: modules/floating-buttons/base/widget-contact-button-base.php:1333
msgid "Entrance Animation"
msgstr "حركة الدخول"

#: includes/controls/groups/box-shadow.php:73
msgctxt "Box Shadow Control"
msgid "Inset"
msgstr "إدراج"

#: includes/settings/settings.php:273
msgid "Disable Default Colors"
msgstr "تعطيل الألوان الإفتراضية"

#: includes/widgets/progress.php:105
msgid "My Skill"
msgstr "مهارتي"

#: includes/widgets/social-icons.php:194 includes/widgets/social-icons.php:343
msgid "Official Color"
msgstr "اللون الرسمي"

#: includes/widgets/testimonial.php:45 includes/widgets/testimonial.php:113
msgid "Testimonial"
msgstr "تزكية"

#: includes/widgets/testimonial.php:203
msgid "Aside"
msgstr "بجانب"

#: includes/widgets/social-icons.php:42 includes/widgets/social-icons.php:89
#: includes/widgets/social-icons.php:232
msgid "Social Icons"
msgstr "الأيقونات الاجتماعية"

#: includes/widgets/social-icons.php:266
#: modules/floating-buttons/base/widget-contact-button-base.php:2027
#: modules/floating-buttons/base/widget-contact-button-base.php:2118
#: modules/floating-buttons/base/widget-contact-button-base.php:2811
#: modules/link-in-bio/base/widget-link-in-bio-base.php:107
msgid "Rounded"
msgstr "دائرية"

#: core/kits/documents/tabs/global-colors.php:123
#: assets/js/152486453d0e39071cdb.bundle.js:187
msgid "Custom Colors"
msgstr "ألوان مخصصة"

#. translators: %s: Elementor version.
#: core/admin/admin-notices.php:140
msgid "View Elementor version %s details"
msgstr "مشاهدة تفاصيل النسخة %s من Elementor"

#: core/admin/admin-notices.php:145 core/admin/admin-notices.php:153
#: core/base/db-upgrades-manager.php:97
msgid "Update Now"
msgstr "التحديث الآن"

#: core/admin/feedback.php:99
msgid "I no longer need the plugin"
msgstr "لم أعد في حاجة إلى هذه الإضافة"

#: core/admin/feedback.php:103
msgid "I found a better plugin"
msgstr "عثرت على إضافة أفضل"

#: core/admin/feedback.php:104
msgid "Please share which plugin"
msgstr "من فضلك، شاركنا باسم الإضافة"

#: core/admin/feedback.php:107
msgid "I couldn't get the plugin to work"
msgstr "لم أستطع جعل الإضافة تعمل"

#: core/admin/feedback.php:111
msgid "It's a temporary deactivation"
msgstr "إنه إلغاء مؤقت"

#: core/admin/feedback.php:120
msgid "Other"
msgstr "سبب آخر"

#: core/admin/feedback.php:121
msgid "Please share the reason"
msgstr "من فضلك، شاركنا السبب"

#: core/admin/feedback.php:137
msgid "If you have a moment, please share why you are deactivating Elementor:"
msgstr "إذا كان لديك بعض الوقت، فالمرجو مشاركتنا بسبب إلغائك تفعيل Elementor:"

#: includes/elements/section.php:290
msgid "Extended"
msgstr "ممتد"

#. translators: 1: Details URL, 2: Accessibility text, 3: Version number, 4:
#. Update URL, 5: Accessibility text.
#: core/admin/admin-notices.php:136
msgid "There is a new version of Elementor Page Builder available. <a href=\"%1$s\" class=\"thickbox open-plugin-details-modal\" aria-label=\"%2$s\">View version %3$s details</a> or <a href=\"%4$s\" class=\"update-link\" aria-label=\"%5$s\">update now</a>."
msgstr "هناك نسخة جديدة متاحة من مصمم الصفحات Elementor. <a href=\"%1$s\" class=\"thickbox open-plugin-details-modal\" aria-label=\"%2$s\">مشاهدة تفاصيل النسخة %3$s </a> أو <a href=\"%4$s\" class=\"update-link\" aria-label=\"%5$s\">قم بالتحديث الآن</a>."

#: includes/widgets/audio.php:236
#: modules/floating-buttons/base/widget-contact-button-base.php:337
#: modules/floating-buttons/base/widget-contact-button-base.php:860
#: modules/link-in-bio/base/widget-link-in-bio-base.php:513
#: modules/link-in-bio/base/widget-link-in-bio-base.php:766
msgid "Username"
msgstr "اسم المستخدم"

#: includes/widgets/audio.php:225
msgid "Play Counts"
msgstr "مرات التشغيل"

#: includes/widgets/audio.php:214
msgid "Comments"
msgstr "تعليقات"

#: core/base/providers/social-network-provider.php:159
#: includes/widgets/audio.php:53 includes/widgets/audio.php:100
msgid "SoundCloud"
msgstr "SoundCloud"

#: includes/widgets/audio.php:126
msgid "Visual Player"
msgstr "المشغل المرئي"

#: includes/widgets/audio.php:156
msgid "Buy Button"
msgstr "زر الشراء"

#: includes/widgets/audio.php:167
msgid "Like Button"
msgstr "زر الاعجاب"

#: includes/widgets/audio.php:178 includes/widgets/video.php:564
msgid "Download Button"
msgstr "زر التنزيل"

#: includes/widgets/audio.php:203
msgid "Share Button"
msgstr "زر المشاركة"

#: includes/elements/column.php:345 includes/elements/container.php:756
#: includes/elements/section.php:607
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1472
msgid "Background Overlay"
msgstr "غطاء الخلفية"

#: core/admin/feedback.php:129
msgid "Quick Feedback"
msgstr "ملاحظات سريعة"

#: core/kits/documents/tabs/settings-background.php:18
#: includes/elements/column.php:270 includes/elements/container.php:667
#: includes/elements/section.php:533 includes/widgets/accordion.php:308
#: includes/widgets/accordion.php:479 includes/widgets/common.php:676
#: includes/widgets/toggle.php:340 includes/widgets/toggle.php:503
#: modules/floating-buttons/base/widget-contact-button-base.php:1619
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1453
#: assets/js/ai-admin.js:11039 assets/js/ai-gutenberg.js:12881
#: assets/js/ai-media-library.js:12668 assets/js/ai.js:13941
msgid "Background"
msgstr "خلفية"

#: includes/elements/section.php:292
msgid "Wider"
msgstr "أعرض"

#: core/common/modules/finder/categories/general.php:29
#: core/role-manager/role-manager.php:69 includes/managers/elements.php:294
#: includes/settings/settings.php:252 includes/settings/tools.php:290
msgid "General"
msgstr "عام"

#: includes/editor-templates/hotkeys.php:49 assets/js/editor.js:30644
#: assets/js/editor.js:32708 assets/js/editor.js:41863
#: assets/js/editor.js:42905
msgid "Paste"
msgstr "لصق"

#: includes/widgets/image-carousel.php:322 includes/widgets/image.php:180
msgid "Custom URL"
msgstr "رابط (URL) مخصّص"

#: includes/controls/groups/background.php:748
#: includes/controls/groups/flex-container.php:29
#: includes/widgets/image-carousel.php:503
#: modules/nested-tabs/widgets/nested-tabs.php:172
msgid "Direction"
msgstr "الاتجاه"

#: includes/widgets/image-carousel.php:492
msgid "Animation Speed"
msgstr "سرعة الحركة"

#: includes/widgets/alert.php:222
msgid "Left Border Width"
msgstr "عرض الاطار الأيسر"

#: includes/elements/column.php:845 includes/elements/container.php:1690
#: includes/elements/section.php:1282 includes/widgets/common.php:634
#: includes/widgets/counter.php:170
#: modules/floating-buttons/base/widget-contact-button-base.php:1343
#: modules/floating-buttons/base/widget-contact-button-base.php:2726
#: modules/nested-accordion/widgets/nested-accordion.php:351
msgid "Animation Duration"
msgstr "مدة التأثير"

#: includes/widgets/image-carousel.php:45
#: includes/widgets/image-carousel.php:113
msgid "Image Carousel"
msgstr "عارض الصور"

#: includes/elements/container.php:571 includes/widgets/audio.php:139
#: includes/widgets/image-carousel.php:385
msgid "Additional Options"
msgstr "خيارات اضافية"

#: includes/widgets/image-carousel.php:545
#: includes/widgets/image-carousel.php:610
msgid "Inside"
msgstr "بالداخل"

#: includes/widgets/image-carousel.php:546
#: includes/widgets/image-carousel.php:609
msgid "Outside"
msgstr "بالخارج"

#: includes/widgets/image-carousel.php:194
msgid "Arrows and Dots"
msgstr "الأسهم والنقاط"

#: includes/widgets/image-carousel.php:177
msgid "Image Stretch"
msgstr "تمديد الصورة"

#: includes/controls/groups/image-size.php:303
msgid "You can crop the original image size to any custom size. You can also set a single value for height or width in order to keep the original size ratio."
msgstr "يمكنك اقتصاص حجم الصورة الأصلي إلى أي حجم مخصص. يمكنك أيضا تعيين قيمة واحدة للارتفاع أو عرض من اجل الحفاظ على نسبة الحجم الأصلي."

#: includes/widgets/video.php:538
msgid "Intro Byline"
msgstr "مقدمة"

#: includes/widgets/accordion.php:445 includes/widgets/divider.php:772
#: includes/widgets/divider.php:939 includes/widgets/image-carousel.php:709
#: includes/widgets/image-carousel.php:843
#: includes/widgets/image-gallery.php:228
#: includes/widgets/image-gallery.php:382 includes/widgets/image.php:634
#: includes/widgets/rating.php:77 includes/widgets/social-icons.php:435
#: includes/widgets/star-rating.php:334 includes/widgets/toggle.php:469
#: modules/nested-accordion/widgets/nested-accordion.php:612
#: modules/nested-tabs/widgets/nested-tabs.php:930
msgid "Spacing"
msgstr "مسافة التباعد"

#: core/base/providers/social-network-provider.php:177
#: includes/widgets/video.php:141
msgid "Vimeo"
msgstr "Vimeo"

#: includes/widgets/video.php:333
msgid "Video Options"
msgstr "خيارات الفيديو"

#: includes/widgets/video.php:378
msgid "Loop"
msgstr "تكرار"

#: includes/widgets/video.php:510
msgid "Intro Title"
msgstr "عنوان المقدمة"

#: includes/widgets/video.php:524
msgid "Intro Portrait"
msgstr "صورة المقدمة"

#: includes/controls/groups/background.php:103 includes/widgets/video.php:44
#: includes/widgets/video.php:129 includes/widgets/video.php:732
msgid "Video"
msgstr "فيديو"

#: includes/widgets/audio.php:247 includes/widgets/video.php:552
msgid "Controls Color"
msgstr "التحكم باللون"

#: includes/controls/image-dimensions.php:81
msgid "The server does not have ImageMagick or GD installed and/or enabled! Any of these libraries are required for WordPress to be able to resize images. Please contact your server administrator to enable this before continuing."
msgstr "يبدو ان  ImageMagick or GD غير مثبتين او مفعلين على الخادم ، اي من هذه المكتبات ضروري لوردبريس حتى يتمكن من تغير حجم الصور . من فضلك اتصل بمسئول الخادم لتمكين الخدمة قبل الاستمرار."

#: includes/widgets/image-carousel.php:475
msgid "Effect"
msgstr "تأثير"

#: includes/widgets/image-carousel.php:480
msgid "Fade"
msgstr "تلاشي"

#: includes/controls/media.php:344
msgctxt "Image Size Control"
msgid "Full"
msgstr "الكامل"

#: includes/widgets/image-carousel.php:196
msgid "Dots"
msgstr "نقاط"

#: includes/elements/column.php:182 includes/widgets/icon-box.php:246
#: includes/widgets/icon-list.php:555 includes/widgets/image-box.php:222
msgid "Vertical Alignment"
msgstr "تعيين رأسي"

#: includes/controls/groups/background.php:633
#: includes/widgets/image-carousel.php:462
msgid "Infinite Loop"
msgstr "التكرار اللا نهائي"

#: includes/elements/column.php:794 includes/elements/container.php:1652
#: includes/elements/section.php:1251 includes/widgets/common.php:596
#: modules/floating-buttons/base/widget-contact-button-base.php:3057
msgid "Add your custom class WITHOUT the dot. e.g: my-class"
msgstr "اضف ال class الخاص بك بدون النقط. مثال my-class"

#: includes/widgets/accordion.php:225 includes/widgets/counter.php:224
#: includes/widgets/icon-box.php:188 includes/widgets/image-box.php:164
#: includes/widgets/progress.php:113 includes/widgets/toggle.php:228
#: modules/nested-accordion/widgets/nested-accordion.php:254
msgid "Title HTML Tag"
msgstr "وسم عنوان HTML"

#: includes/widgets/icon-box.php:153 includes/widgets/image-box.php:129
msgid "This is the heading"
msgstr "هذه هي الترويسة"

#: core/kits/documents/tabs/global-typography.php:200
msgid "The list of fonts used if the chosen font is not available."
msgstr "قائمة الخطوط تستخدم في حالة عدم اتاحة الخط المحدد"

#: includes/widgets/wordpress.php:214
msgid "Form"
msgstr "نموذج"

#: includes/editor-templates/panel-elements.php:14 assets/js/editor.js:19615
#: assets/js/editor.js:21589 assets/js/editor.js:22020
#: assets/js/editor.js:37243
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:11
msgid "Elements"
msgstr "العناصر"

#: core/admin/admin.php:344 core/admin/menu/main.php:75
#: core/common/modules/finder/categories/settings.php:29
#: core/dynamic-tags/base-tag.php:171 includes/editor-templates/panel.php:82
#: includes/editor-templates/responsive-bar.php:64
#: includes/managers/controls.php:334
#: includes/settings/admin-menu-items/admin-menu-item.php:28
#: includes/settings/settings.php:216 modules/usage/settings-reporter.php:13
#: assets/js/editor.js:9681 assets/js/editor.js:38141 assets/js/editor.js:47410
msgid "Settings"
msgstr "الإعدادات"

#: includes/controls/groups/typography.php:177
msgctxt "Typography Control"
msgid "Italic"
msgstr "مائل"

#: includes/controls/groups/typography.php:164
msgctxt "Typography Control"
msgid "Lowercase"
msgstr "أحرف صغيرة"

#: includes/controls/groups/typography.php:163
msgctxt "Typography Control"
msgid "Uppercase"
msgstr "أحرف كبيرة"

#: includes/controls/groups/typography.php:178
msgctxt "Typography Control"
msgid "Oblique"
msgstr "مائل"

#: includes/controls/groups/typography.php:165
msgctxt "Typography Control"
msgid "Capitalize"
msgstr "الكتابة بأحرف كبيرة"

#: core/admin/admin-notices.php:239
msgid "Sure! I'd love to help"
msgstr "طبعاً! أحب تقديم المساعدة"

#: includes/fonts.php:76
msgid "Google"
msgstr "Google"

#: core/kits/views/trash-kit-confirmation.php:30
#: includes/editor-templates/hotkeys.php:66
#: includes/editor-templates/templates.php:187 assets/js/editor.js:10569
#: assets/js/editor.js:28210 assets/js/editor.js:30709
#: assets/js/editor.js:47601 assets/js/import-export-admin.js:287
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:21
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:26
msgid "Delete"
msgstr "حذف"

#: includes/controls/icon.php:876 includes/controls/icon.php:877
msgid "Select Icon"
msgstr "اختر أيقونة"

#: includes/controls/repeater.php:178
#: modules/floating-buttons/base/widget-contact-button-base.php:956
msgid "Add Item"
msgstr "اضافة عنصر"

#. Plugin Name of the plugin
#: elementor.php app/view.php:23 core/admin/admin.php:293
#: core/admin/admin.php:410 core/admin/admin.php:488
#: core/admin/menu/main.php:17 core/admin/menu/main.php:18
#: core/documents-manager.php:385 core/upgrade/custom-tasks-manager.php:29
#: core/upgrade/manager.php:43 includes/editor-templates/navigator.php:111
#: includes/editor-templates/panel-elements.php:99
#: includes/editor-templates/panel.php:218
#: includes/editor-templates/templates.php:135 includes/plugin.php:855
#: includes/settings/admin-menu-items/admin-menu-item.php:29
#: includes/settings/settings.php:91 includes/settings/settings.php:92
#: includes/settings/settings.php:461 modules/compatibility-tag/module.php:36
#: modules/history/views/history-panel-template.php:23
#: modules/history/views/revisions-panel-template.php:38
#: assets/js/app-packages.js:1821 assets/js/app.js:2697
msgid "Elementor"
msgstr "Elementor"

#: core/admin/admin.php:243
#: core/editor/loader/v1/templates/editor-body-v1.view.php:23
#: core/editor/loader/v2/templates/editor-body-v2.view.php:23
#: includes/editor-templates/templates.php:52 modules/gutenberg/module.php:123
#: assets/js/ai-admin.js:2343 assets/js/ai-gutenberg.js:4103
#: assets/js/ai-media-library.js:3972 assets/js/ai.js:4735
#: assets/js/app-packages.js:5312
#: assets/js/kit-library.ac1a4cd5deae5526ce49.bundle.js:236
#: assets/js/onboarding.001cbdc1993cabe048db.bundle.js:46
msgid "Loading"
msgstr "جاري التحميل"

#: includes/editor-templates/global.php:36
msgid "Add New Section"
msgstr "اضافة قسم جديد"

#: includes/editor-templates/panel-elements.php:79
msgid "Search Widget..."
msgstr "البحث في الودجات..."

#: includes/editor-templates/panel.php:76
#: includes/editor-templates/panel.php:77
msgid "Widgets Panel"
msgstr "لوحة الودجات"

#: includes/editor-templates/hotkeys.php:172
#: includes/editor-templates/panel.php:104
#: includes/editor-templates/panel.php:107
msgid "Responsive Mode"
msgstr "الوضع المتجاوب"

#: core/admin/admin.php:622 core/admin/menu/main.php:41
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:2
msgid "Help"
msgstr "مساعدة"

#: includes/editor-templates/hotkeys.php:81
#: includes/editor-templates/templates.php:20
#: includes/editor-templates/templates.php:21
#: includes/editor-templates/templates.php:237
#: includes/editor-templates/templates.php:248 assets/js/e-home-screen.js:30606
#: assets/js/editor.js:5117 assets/js/editor.js:44789
#: assets/js/element-manager-admin.js:2427
#: assets/js/kit-elements-defaults-editor.js:615
msgid "Save"
msgstr "حفظ"

#: core/editor/loader/v1/templates/editor-body-v1.view.php:31
#: core/editor/loader/v2/templates/editor-body-v2.view.php:33
#: includes/editor-templates/templates.php:174 assets/js/ai-admin.js:7516
#: assets/js/ai-gutenberg.js:9358 assets/js/ai-layout.js:3758
#: assets/js/ai-media-library.js:9145 assets/js/ai.js:10418
#: assets/js/editor.js:28149
msgid "Preview"
msgstr "معاينة"

#: includes/controls/structure.php:65
msgid "Reset"
msgstr "إعادة تعيين"

#: includes/editor-templates/hotkeys.php:135
msgid "Page Settings"
msgstr "اعدادات الصفحة"

#: modules/floating-buttons/base/widget-contact-button-base.php:1150
#: modules/floating-buttons/base/widget-contact-button-base.php:1202
#: modules/floating-buttons/base/widget-contact-button-base.php:1289
#: modules/floating-buttons/base/widget-contact-button-base.php:1498
#: modules/floating-buttons/base/widget-contact-button-base.php:1664
#: modules/floating-buttons/base/widget-contact-button-base.php:2554
#: modules/floating-buttons/base/widget-contact-button-base.php:2623
#: assets/js/152486453d0e39071cdb.bundle.js:450
msgid "Colors"
msgstr "الألوان"

#: includes/controls/groups/grid-container.php:42
#: includes/widgets/image-gallery.php:142 includes/widgets/social-icons.php:277
#: includes/widgets/text-editor.php:136
msgid "Columns"
msgstr "أعمدة"

#: includes/managers/controls.php:329 includes/widgets/accordion.php:127
#: includes/widgets/accordion.php:471 includes/widgets/alert.php:130
#: includes/widgets/icon-box.php:577 includes/widgets/image-box.php:499
#: includes/widgets/tabs.php:123 includes/widgets/tabs.php:432
#: includes/widgets/testimonial.php:120 includes/widgets/testimonial.php:253
#: includes/widgets/toggle.php:127 includes/widgets/toggle.php:495
#: modules/nested-accordion/widgets/nested-accordion.php:477
#: modules/nested-tabs/widgets/nested-tabs.php:1005 assets/js/app.js:11042
#: assets/js/app.js:11535 assets/js/editor.js:37042
msgid "Content"
msgstr "محتوى"

#: includes/managers/controls.php:330 includes/widgets/divider.php:367
#: includes/widgets/icon-list.php:279 assets/js/ai-admin.js:10329
#: assets/js/ai-gutenberg.js:12171 assets/js/ai-media-library.js:11958
#: assets/js/ai.js:13231 assets/js/editor.js:9684 assets/js/editor.js:37045
msgid "Style"
msgstr "تنسيق"

#: core/common/modules/finder/categories/settings.php:54
#: core/dynamic-tags/tag.php:88 includes/elements/column.php:720
#: includes/elements/section.php:1172 includes/managers/controls.php:331
#: includes/settings/settings.php:322
#: modules/floating-buttons/base/widget-contact-button-base.php:2862
#: modules/floating-buttons/module.php:90 assets/js/editor.js:9687
#: assets/js/editor.js:37048
#: assets/js/onboarding.001cbdc1993cabe048db.bundle.js:1441
msgid "Advanced"
msgstr "متقدم"

#: includes/elements/column.php:881 includes/elements/container.php:1733
#: includes/elements/section.php:1319 includes/managers/controls.php:332
#: includes/widgets/common.php:1115
#: modules/floating-buttons/base/widget-contact-button-base.php:3002
msgid "Responsive"
msgstr "متجاوب"

#: includes/elements/section.php:78 modules/library/documents/section.php:43
#: assets/js/container-converter.js:99 assets/js/editor.js:10434
msgid "Section"
msgstr "قسم"

#: includes/controls/gaps.php:58
#: includes/controls/groups/grid-container.php:119
#: includes/elements/column.php:60
msgid "Column"
msgstr "عمود"

#: includes/controls/icons.php:80 includes/controls/media.php:239
msgid "Add"
msgstr "اضافة"

#: includes/elements/column.php:688 includes/elements/section.php:1140
msgid "Text Align"
msgstr "محاذاة النص"

#: includes/elements/column.php:160
msgid "Column Width"
msgstr "عرض العمود"

#: core/kits/documents/tabs/settings-layout.php:55
#: includes/elements/container.php:430 includes/elements/section.php:249
#: includes/widgets/video.php:935
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1579
msgid "Content Width"
msgstr "عرض المحتوى"

#: includes/elements/section.php:283 includes/widgets/text-editor.php:161
msgid "Columns Gap"
msgstr "الفجوة بين الأعمدة"

#: includes/elements/section.php:288
msgid "No Gap"
msgstr "بدون فجوة"

#: includes/elements/section.php:289
msgid "Narrow"
msgstr "ضيق"

#: core/kits/documents/tabs/settings-layout.php:24
#: includes/elements/column.php:117 includes/elements/container.php:1324
#: includes/elements/section.php:231 includes/managers/controls.php:333
#: includes/managers/elements.php:280 includes/widgets/common.php:175
#: includes/widgets/icon-list.php:99
#: modules/floating-buttons/base/widget-contact-button-base.php:2869
#: modules/nested-accordion/widgets/nested-accordion.php:91
#: assets/js/editor.js:37051
msgid "Layout"
msgstr "النسق"

#: core/settings/editor-preferences/model.php:92
#: includes/controls/groups/background.php:498
#: includes/controls/image-dimensions.php:95
#: includes/elements/container.php:444 includes/elements/container.php:1195
#: includes/elements/section.php:263 includes/elements/section.php:975
#: includes/widgets/common.php:220 includes/widgets/divider.php:425
#: includes/widgets/icon-list.php:326 includes/widgets/image-box.php:344
#: includes/widgets/image.php:266
#: modules/floating-buttons/base/widget-contact-button-base.php:2783
#: modules/nested-tabs/widgets/nested-tabs.php:293
#: modules/shapes/widgets/text-path.php:523
#: modules/shapes/widgets/text-path.php:594
msgid "Width"
msgstr "العرض"

#: includes/controls/image-dimensions.php:100
#: includes/elements/container.php:1229 includes/elements/section.php:321
#: includes/elements/section.php:361 includes/elements/section.php:1009
#: includes/widgets/google-maps.php:174 includes/widgets/icon-list.php:345
#: includes/widgets/image.php:336 includes/widgets/progress.php:232
msgid "Height"
msgstr "ارتفاع"

#: includes/elements/section.php:326 includes/elements/section.php:366
msgid "Fit To Screen"
msgstr "متناسب مع مقاس الشاشة"

#: includes/elements/section.php:401
msgid "Column Position"
msgstr "موضع العمود"

#: includes/elements/container.php:523 includes/elements/section.php:327
#: includes/elements/section.php:367
msgid "Min Height"
msgstr "أقل ارتفاع"

#: includes/controls/groups/grid-container.php:196
#: includes/controls/groups/grid-container.php:236
#: includes/elements/column.php:188 includes/elements/section.php:407
#: includes/elements/section.php:426 includes/widgets/counter.php:333
#: includes/widgets/icon-box.php:254 includes/widgets/image-box.php:230
#: modules/floating-buttons/base/widget-contact-button-base.php:2939
msgid "Middle"
msgstr "وسط"

#: includes/widgets/video.php:955
msgid "Content Position"
msgstr "موضع المحتوى"

#: includes/managers/elements.php:343
msgid "WordPress"
msgstr "ووردبريس"

#: modules/system-info/module.php:166
msgid "You can copy the below info as simple text with Ctrl+C / Ctrl+V:"
msgstr "يمكنك نسخ المعلومات أدناه كنص بسيط باستخدام Ctrl+C / Ctrl+V:"

#: modules/system-info/module.php:164
msgid "Copy & Paste Info"
msgstr "نسخ ولصق المعلومات"

#: core/common/modules/finder/categories/general.php:55
#: modules/system-info/module.php:157
#: modules/system-info/system-info-menu-item.php:29
#: modules/system-info/system-info-menu-item.php:33
msgid "System Info"
msgstr "معلومات النظام"

#: includes/widgets/alert.php:46 includes/widgets/alert.php:93
#: includes/widgets/alert.php:192
msgid "Alert"
msgstr "تنبيه"

#: includes/editor-templates/templates.php:115
#: includes/elements/container.php:1170 includes/elements/section.php:950
#: includes/template-library/sources/local.php:1689
#: includes/widgets/alert.php:100 includes/widgets/progress.php:136
#: includes/widgets/traits/button-trait.php:66
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1083
msgid "Type"
msgstr "نوع"

#: core/kits/documents/tabs/settings-lightbox.php:130
#: core/kits/documents/tabs/theme-style-form-fields.php:200
#: includes/controls/groups/background.php:173 includes/widgets/alert.php:200
#: includes/widgets/image.php:604 includes/widgets/progress.php:221
#: includes/widgets/tabs.php:327 includes/widgets/video.php:875
#: modules/floating-buttons/base/widget-contact-button-base.php:1178
#: modules/floating-buttons/base/widget-contact-button-base.php:1230
#: modules/floating-buttons/base/widget-contact-button-base.php:1271
#: modules/floating-buttons/base/widget-contact-button-base.php:1317
#: modules/floating-buttons/base/widget-contact-button-base.php:1633
#: modules/floating-buttons/base/widget-contact-button-base.php:1903
#: modules/floating-buttons/base/widget-contact-button-base.php:1939
#: modules/floating-buttons/base/widget-contact-button-base.php:1972
#: modules/floating-buttons/base/widget-contact-button-base.php:2076
#: modules/floating-buttons/base/widget-contact-button-base.php:2102
#: modules/floating-buttons/base/widget-contact-button-base.php:2311
#: modules/floating-buttons/base/widget-contact-button-base.php:2598
#: modules/floating-buttons/base/widget-contact-button-base.php:2667
#: modules/floating-buttons/base/widget-contact-button-base.php:2754
#: modules/floating-buttons/base/widget-contact-button-base.php:2768
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1117
#: modules/nested-tabs/widgets/nested-tabs.php:487
#: modules/nested-tabs/widgets/nested-tabs.php:546
#: modules/nested-tabs/widgets/nested-tabs.php:631
#: modules/nested-tabs/widgets/nested-tabs.php:1018
msgid "Background Color"
msgstr "لون الخلفية"

#: core/common/modules/finder/categories/edit.php:30
#: includes/controls/popover-toggle.php:68 assets/js/ai-admin.js:2950
#: assets/js/ai-admin.js:10020 assets/js/ai-admin.js:10027
#: assets/js/ai-gutenberg.js:4710 assets/js/ai-gutenberg.js:11862
#: assets/js/ai-gutenberg.js:11869 assets/js/ai-media-library.js:4579
#: assets/js/ai-media-library.js:11649 assets/js/ai-media-library.js:11656
#: assets/js/ai.js:5342 assets/js/ai.js:12922 assets/js/ai.js:12929
#: assets/js/element-manager-admin.js:2516
#: assets/js/element-manager-admin.js:2573
msgid "Edit"
msgstr "تحرير"

#: includes/widgets/menu-anchor.php:102
msgid "For Example: About"
msgstr "كمثال: من نحن"

#: includes/widgets/progress.php:188
msgid "e.g. Web Designer"
msgstr "مثلاً \"مصمم مواقع\""

#: includes/widgets/progress.php:189
msgid "Web Designer"
msgstr "مصمم مواقع"

#: modules/gutenberg/module.php:98
msgid "&#8592; Back to WordPress Editor"
msgstr "&#8592; العودة لمحرر ووردبريس"

#: core/admin/admin.php:224 core/admin/admin.php:232 core/base/document.php:651
#: modules/admin-bar/module.php:123 modules/gutenberg/module.php:101
#: modules/gutenberg/module.php:112 modules/gutenberg/module.php:134
msgid "Edit with Elementor"
msgstr "التحرير بواسطة Elementor"

#: core/experiments/manager.php:490
#: core/kits/documents/tabs/settings-background.php:78
#: core/settings/editor-preferences/model.php:124
#: includes/base/widget-base.php:296 includes/controls/animation.php:152
#: includes/controls/font.php:66 includes/controls/groups/background.php:318
#: includes/controls/groups/background.php:431
#: includes/controls/groups/background.php:461
#: includes/controls/groups/background.php:482
#: includes/controls/groups/background.php:687
#: includes/controls/groups/background.php:706
#: includes/controls/groups/border.php:63
#: includes/controls/groups/flex-container.php:208
#: includes/controls/groups/flex-item.php:23
#: includes/controls/groups/typography.php:151
#: includes/controls/groups/typography.php:162
#: includes/controls/groups/typography.php:175
#: includes/controls/groups/typography.php:187
#: includes/editor-templates/panel.php:264 includes/elements/column.php:186
#: includes/elements/column.php:214 includes/elements/column.php:252
#: includes/elements/container.php:583 includes/elements/container.php:606
#: includes/elements/container.php:1392 includes/elements/section.php:287
#: includes/elements/section.php:325 includes/elements/section.php:365
#: includes/elements/section.php:424 includes/elements/section.php:452
#: includes/elements/section.php:492 includes/settings/settings.php:372
#: includes/widgets/common.php:224 includes/widgets/common.php:343
#: includes/widgets/divider.php:817 includes/widgets/heading.php:196
#: includes/widgets/icon-box.php:115 includes/widgets/icon-list.php:104
#: includes/widgets/icon.php:130 includes/widgets/image-carousel.php:146
#: includes/widgets/image-carousel.php:164
#: includes/widgets/image-carousel.php:355
#: includes/widgets/image-carousel.php:712
#: includes/widgets/image-gallery.php:192
#: includes/widgets/image-gallery.php:208
#: includes/widgets/image-gallery.php:231 includes/widgets/image.php:217
#: includes/widgets/image.php:364 includes/widgets/progress.php:139
#: includes/widgets/text-editor.php:140 includes/widgets/text-editor.php:295
#: includes/widgets/traits/button-trait.php:70
#: modules/element-cache/module.php:109
#: modules/floating-buttons/base/widget-contact-button-base.php:1154
#: modules/floating-buttons/base/widget-contact-button-base.php:1206
#: modules/floating-buttons/base/widget-contact-button-base.php:1293
#: modules/floating-buttons/base/widget-contact-button-base.php:1502
#: modules/floating-buttons/base/widget-contact-button-base.php:1668
#: modules/floating-buttons/base/widget-contact-button-base.php:2232
#: modules/floating-buttons/base/widget-contact-button-base.php:2558
#: modules/floating-buttons/base/widget-contact-button-base.php:2627
#: modules/floating-buttons/base/widget-contact-button-base.php:2758
#: modules/link-in-bio/base/widget-link-in-bio-base.php:167
#: modules/page-templates/module.php:299
#: modules/shapes/widgets/text-path.php:186 assets/js/editor.js:45351
#: assets/js/editor.js:45362
msgid "Default"
msgstr "افتراضي"

#: includes/widgets/image-carousel.php:143
msgid "Slides to Show"
msgstr "الشرائح المراد عرضها"

#: core/kits/documents/tabs/theme-style-buttons.php:91
#: core/kits/documents/tabs/theme-style-form-fields.php:117
#: core/kits/documents/tabs/theme-style-images.php:63
#: core/kits/documents/tabs/theme-style-typography.php:120
#: includes/base/element-base.php:871
#: includes/controls/groups/typography.php:152
#: includes/controls/groups/typography.php:166
#: includes/controls/groups/typography.php:176 includes/elements/column.php:280
#: includes/elements/column.php:358 includes/elements/column.php:407
#: includes/elements/column.php:514 includes/elements/column.php:850
#: includes/elements/container.php:680 includes/elements/container.php:769
#: includes/elements/container.php:843 includes/elements/container.php:985
#: includes/elements/container.php:1695 includes/elements/section.php:543
#: includes/elements/section.php:617 includes/elements/section.php:681
#: includes/elements/section.php:804 includes/elements/section.php:1287
#: includes/widgets/alert.php:394 includes/widgets/common.php:639
#: includes/widgets/common.php:686 includes/widgets/common.php:761
#: includes/widgets/google-maps.php:203 includes/widgets/heading.php:317
#: includes/widgets/icon-box.php:373 includes/widgets/icon-list.php:408
#: includes/widgets/icon-list.php:637 includes/widgets/icon.php:208
#: includes/widgets/image-box.php:396 includes/widgets/image.php:416
#: includes/widgets/traits/button-trait.php:338
#: modules/floating-buttons/base/widget-contact-button-base.php:1142
#: modules/floating-buttons/base/widget-contact-button-base.php:1348
#: modules/floating-buttons/base/widget-contact-button-base.php:1920
#: modules/floating-buttons/base/widget-contact-button-base.php:2415
#: modules/floating-buttons/base/widget-contact-button-base.php:2546
#: modules/floating-buttons/base/widget-contact-button-base.php:2731
#: modules/nested-accordion/widgets/nested-accordion.php:656
#: modules/nested-accordion/widgets/nested-accordion.php:710
#: modules/nested-tabs/widgets/nested-tabs.php:474
#: modules/nested-tabs/widgets/nested-tabs.php:721
#: modules/nested-tabs/widgets/nested-tabs.php:952
#: modules/shapes/widgets/text-path.php:392
#: modules/shapes/widgets/text-path.php:484
msgid "Normal"
msgstr "عادي"

#: core/kits/documents/tabs/theme-style-typography.php:110
#: includes/elements/container.php:633 includes/widgets/audio.php:107
#: includes/widgets/heading.php:179 includes/widgets/icon-box.php:176
#: includes/widgets/icon-list.php:151 includes/widgets/icon.php:159
#: includes/widgets/image-box.php:152 includes/widgets/image-carousel.php:316
#: includes/widgets/image-carousel.php:330
#: includes/widgets/image-gallery.php:168 includes/widgets/image.php:174
#: includes/widgets/image.php:191 includes/widgets/social-icons.php:176
#: includes/widgets/testimonial.php:185
#: includes/widgets/traits/button-trait.php:98 includes/widgets/video.php:153
#: includes/widgets/video.php:178 includes/widgets/video.php:202
#: modules/floating-buttons/base/widget-contact-button-base.php:397
#: modules/floating-buttons/base/widget-contact-button-base.php:882
#: modules/floating-buttons/base/widget-contact-button-base.php:1010
#: modules/link-in-bio/base/widget-link-in-bio-base.php:255
#: modules/link-in-bio/base/widget-link-in-bio-base.php:397
#: modules/link-in-bio/base/widget-link-in-bio-base.php:632
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1088
#: modules/shapes/widgets/text-path.php:141
msgid "Link"
msgstr "رابط"

#: includes/controls/groups/background.php:456
msgctxt "Background Control"
msgid "Repeat"
msgstr "تكرار"

#: core/base/traits/shared-widget-controls-trait.php:269
#: core/settings/editor-preferences/model.php:127
#: includes/base/element-base.php:1373 includes/editor-templates/panel.php:293
#: assets/js/kit-library.ac1a4cd5deae5526ce49.bundle.js:4291
#: assets/js/packages/editor-responsive/editor-responsive.js:2
#: assets/js/packages/editor-responsive/editor-responsive.strings.js:1
msgid "Desktop"
msgstr "سطح المكتب"

#: includes/controls/image-dimensions.php:102
#: includes/editor-templates/panel.php:185
#: includes/editor-templates/templates.php:209
#: modules/history/views/revisions-panel-template.php:14
#: assets/js/editor.js:10340 assets/js/editor.js:38222
msgid "Apply"
msgstr "تطبيق"

#: core/kits/manager.php:139 includes/controls/groups/background.php:328
#: includes/controls/groups/background.php:486
#: includes/controls/groups/flex-item.php:24
#: includes/controls/groups/flex-item.php:93
#: includes/controls/groups/flex-item.php:142
#: includes/controls/groups/image-size.php:383
#: includes/elements/section.php:293 includes/maintenance-mode.php:238
#: includes/widgets/common.php:137 includes/widgets/common.php:227
#: includes/widgets/common.php:941 includes/widgets/common.php:998
#: includes/widgets/image-carousel.php:713
#: includes/widgets/image-gallery.php:232 includes/widgets/social-icons.php:195
#: includes/widgets/social-icons.php:344
#: modules/floating-buttons/base/widget-contact-button-base.php:1155
#: modules/floating-buttons/base/widget-contact-button-base.php:1207
#: modules/floating-buttons/base/widget-contact-button-base.php:1294
#: modules/floating-buttons/base/widget-contact-button-base.php:1503
#: modules/floating-buttons/base/widget-contact-button-base.php:1669
#: modules/floating-buttons/base/widget-contact-button-base.php:2233
#: modules/floating-buttons/base/widget-contact-button-base.php:2559
#: modules/floating-buttons/base/widget-contact-button-base.php:2628
#: modules/floating-buttons/base/widget-contact-button-base.php:2759
#: modules/shapes/module.php:29 assets/js/editor.js:45359
msgid "Custom"
msgstr "مخصص"

#: includes/editor-templates/hotkeys.php:125
#: includes/editor-templates/navigator.php:41
#: includes/editor-templates/panel.php:89
#: includes/editor-templates/panel.php:95 includes/elements/section.php:511
#: includes/elements/section.php:519 assets/js/editor.js:31633
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:5
msgid "Structure"
msgstr "هيكلة"

#: core/kits/documents/tabs/settings-lightbox.php:163
#: core/kits/documents/tabs/theme-style-buttons.php:98
#: core/kits/documents/tabs/theme-style-buttons.php:173
#: core/kits/documents/tabs/theme-style-form-fields.php:176
#: core/kits/documents/tabs/theme-style-typography.php:56
#: includes/elements/column.php:652 includes/elements/section.php:1104
#: includes/widgets/alert.php:252 includes/widgets/alert.php:292
#: includes/widgets/counter.php:481 includes/widgets/counter.php:535
#: includes/widgets/heading.php:273 includes/widgets/image-carousel.php:812
#: includes/widgets/image-gallery.php:342 includes/widgets/image.php:589
#: includes/widgets/progress.php:305 includes/widgets/star-rating.php:240
#: includes/widgets/testimonial.php:261 includes/widgets/testimonial.php:359
#: includes/widgets/testimonial.php:404 includes/widgets/text-editor.php:246
#: includes/widgets/traits/button-trait.php:346
#: includes/widgets/traits/button-trait.php:390
#: modules/floating-buttons/base/widget-contact-button-base.php:1524
#: modules/floating-buttons/base/widget-contact-button-base.php:1556
#: modules/floating-buttons/base/widget-contact-button-base.php:1687
#: modules/floating-buttons/base/widget-contact-button-base.php:1718
#: modules/floating-buttons/base/widget-contact-button-base.php:1749
#: modules/floating-buttons/base/widget-contact-button-base.php:2057
#: modules/floating-buttons/base/widget-contact-button-base.php:2264
#: modules/floating-buttons/base/widget-contact-button-base.php:2292
#: modules/floating-buttons/base/widget-contact-button-base.php:2585
#: modules/floating-buttons/base/widget-contact-button-base.php:2654
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1097
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1293
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1321
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1349
msgid "Text Color"
msgstr "لون النص"

#: includes/elements/column.php:640 includes/elements/section.php:1092
msgid "Heading Color"
msgstr "لون الترويسة"

#: includes/elements/column.php:664 includes/elements/section.php:1116
msgid "Link Color"
msgstr "لون الرابط"

#: includes/elements/column.php:676 includes/elements/section.php:1128
msgid "Link Hover Color"
msgstr "لون الرابط عند التمرير"

#: core/base/providers/social-network-provider.php:129
#: includes/widgets/video.php:140
msgid "YouTube"
msgstr "يوتيوب"

#: includes/widgets/progress.php:156
msgid "Percentage"
msgstr "نسبة مئوية"

#: includes/widgets/video.php:663 includes/widgets/video.php:790
msgid "Play Icon"
msgstr "أيقونة التشغيل"

#: includes/widgets/video.php:494
msgid "Suggested Videos"
msgstr "الفيديوهات المقترحة"

#: includes/widgets/divider.php:880 includes/widgets/icon-box.php:380
#: includes/widgets/icon-box.php:421 includes/widgets/icon.php:215
#: includes/widgets/icon.php:258 includes/widgets/social-icons.php:203
#: includes/widgets/social-icons.php:352 includes/widgets/social-icons.php:509
#: includes/widgets/text-editor.php:307
msgid "Primary Color"
msgstr "اللون الأساسي"

#: includes/widgets/divider.php:897 includes/widgets/icon-box.php:396
#: includes/widgets/icon-box.php:434 includes/widgets/icon.php:232
#: includes/widgets/icon.php:272 includes/widgets/social-icons.php:217
#: includes/widgets/social-icons.php:366 includes/widgets/social-icons.php:524
#: includes/widgets/text-editor.php:322
msgid "Secondary Color"
msgstr "اللون الثانوي"

#: includes/base/element-base.php:1313 includes/base/element-base.php:1341
#: includes/controls/groups/flex-container.php:101
#: includes/controls/groups/flex-container.php:137
#: includes/controls/groups/flex-container.php:209
#: includes/controls/groups/flex-item.php:59
#: includes/controls/groups/grid-container.php:139
#: includes/controls/groups/grid-container.php:167
#: includes/elements/column.php:216 includes/elements/column.php:696
#: includes/elements/section.php:1148 includes/widgets/common.php:307
#: includes/widgets/counter.php:304 includes/widgets/counter.php:378
#: includes/widgets/counter.php:414 includes/widgets/divider.php:460
#: includes/widgets/divider.php:756 includes/widgets/divider.php:922
#: includes/widgets/heading.php:251 includes/widgets/icon-box.php:282
#: includes/widgets/icon-list.php:250 includes/widgets/icon-list.php:532
#: includes/widgets/icon-list.php:563 includes/widgets/icon.php:188
#: includes/widgets/image-box.php:258 includes/widgets/image-carousel.php:689
#: includes/widgets/image-carousel.php:790
#: includes/widgets/image-gallery.php:317 includes/widgets/image.php:249
#: includes/widgets/image.php:567 includes/widgets/rating.php:199
#: includes/widgets/social-icons.php:310 includes/widgets/star-rating.php:205
#: includes/widgets/tabs.php:195 includes/widgets/tabs.php:225
#: includes/widgets/tabs.php:412 includes/widgets/testimonial.php:232
#: includes/widgets/text-editor.php:225
#: includes/widgets/traits/button-trait.php:258
#: includes/widgets/traits/button-trait.php:290 includes/widgets/video.php:959
#: modules/floating-buttons/base/widget-contact-button-base.php:2885
#: modules/nested-accordion/widgets/nested-accordion.php:161
#: modules/nested-tabs/widgets/nested-tabs.php:219
#: modules/nested-tabs/widgets/nested-tabs.php:261
#: modules/nested-tabs/widgets/nested-tabs.php:331
#: modules/shapes/widgets/text-path.php:164
msgid "Center"
msgstr "توسيط"

#: core/kits/documents/tabs/theme-style-form-fields.php:78
#: core/kits/documents/tabs/theme-style-typography.php:127
#: core/kits/documents/tabs/theme-style-typography.php:156
#: core/kits/documents/tabs/theme-style-typography.php:201
#: includes/controls/box-shadow.php:104
#: includes/controls/groups/background.php:170
#: includes/controls/text-shadow.php:97 includes/elements/column.php:415
#: includes/elements/container.php:851 includes/elements/container.php:1181
#: includes/elements/section.php:689 includes/elements/section.php:961
#: includes/widgets/accordion.php:319 includes/widgets/accordion.php:421
#: includes/widgets/accordion.php:490 includes/widgets/alert.php:400
#: includes/widgets/alert.php:417 includes/widgets/divider.php:571
#: includes/widgets/divider.php:715 includes/widgets/heading.php:325
#: includes/widgets/icon-box.php:594 includes/widgets/icon-box.php:645
#: includes/widgets/icon-list.php:378 includes/widgets/icon-list.php:415
#: includes/widgets/icon-list.php:440 includes/widgets/icon-list.php:644
#: includes/widgets/icon-list.php:668 includes/widgets/image-box.php:516
#: includes/widgets/image-box.php:567 includes/widgets/image-carousel.php:578
#: includes/widgets/image-carousel.php:642 includes/widgets/progress.php:207
#: includes/widgets/progress.php:265 includes/widgets/rating.php:102
#: includes/widgets/social-icons.php:190 includes/widgets/social-icons.php:339
#: includes/widgets/star-rating.php:360 includes/widgets/tabs.php:348
#: includes/widgets/tabs.php:441 includes/widgets/toggle.php:352
#: includes/widgets/toggle.php:445 includes/widgets/toggle.php:514
#: includes/widgets/video.php:802
#: modules/floating-buttons/base/widget-contact-button-base.php:1829
#: modules/floating-buttons/base/widget-contact-button-base.php:2228
#: modules/floating-buttons/base/widget-contact-button-base.php:2241
#: modules/floating-buttons/base/widget-contact-button-base.php:2479
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1196
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1381
#: modules/nested-accordion/widgets/nested-accordion.php:671
#: modules/nested-accordion/widgets/nested-accordion.php:738
#: modules/nested-tabs/widgets/nested-tabs.php:728
#: modules/nested-tabs/widgets/nested-tabs.php:774
#: modules/nested-tabs/widgets/nested-tabs.php:821
#: modules/nested-tabs/widgets/nested-tabs.php:957
#: modules/nested-tabs/widgets/nested-tabs.php:974
#: modules/nested-tabs/widgets/nested-tabs.php:991
#: modules/shapes/widgets/text-path.php:399
#: modules/shapes/widgets/text-path.php:423
#: modules/shapes/widgets/text-path.php:491
#: modules/shapes/widgets/text-path.php:511
#: modules/shapes/widgets/text-path.php:562
#: modules/shapes/widgets/text-path.php:582 assets/js/editor.js:47548
#: assets/js/editor.js:47591
msgid "Color"
msgstr "لون"

#: core/kits/documents/tabs/settings-lightbox.php:56
#: includes/widgets/counter.php:45 includes/widgets/counter.php:109
#: includes/widgets/counter.php:249
msgid "Counter"
msgstr "عداد"

#: includes/editor-templates/templates.php:111
#: includes/widgets/testimonial.php:155 includes/widgets/testimonial.php:351
#: modules/floating-buttons/base/widget-contact-button-base.php:101
#: modules/floating-buttons/base/widget-contact-button-base.php:188
#: modules/floating-buttons/base/widget-contact-button-base.php:620
#: modules/floating-buttons/base/widget-contact-button-base.php:1678
msgid "Name"
msgstr "اسم"

#: includes/settings/settings.php:264
msgid "Post Types"
msgstr "أنواع المقالات"

#. Author of the plugin
#: elementor.php
msgid "Elementor.com"
msgstr "Elementor.com"

#: includes/base/element-base.php:1337 includes/controls/dimensions.php:82
#: includes/elements/column.php:187 includes/elements/container.php:1155
#: includes/elements/container.php:1520 includes/elements/section.php:406
#: includes/elements/section.php:425 includes/elements/section.php:935
#: includes/widgets/common.php:466 includes/widgets/counter.php:329
#: includes/widgets/icon-box.php:228 includes/widgets/icon-box.php:250
#: includes/widgets/image-box.php:203 includes/widgets/image-box.php:226
#: includes/widgets/testimonial.php:207 includes/widgets/video.php:960
#: modules/floating-buttons/base/widget-contact-button-base.php:2935
msgid "Top"
msgstr "أعلى"

#: includes/base/element-base.php:1345 includes/controls/dimensions.php:84
#: includes/elements/column.php:189 includes/elements/container.php:1156
#: includes/elements/container.php:1524 includes/elements/section.php:408
#: includes/elements/section.php:427 includes/elements/section.php:936
#: includes/widgets/common.php:470 includes/widgets/counter.php:337
#: includes/widgets/icon-box.php:258 includes/widgets/image-box.php:234
#: modules/floating-buttons/base/widget-contact-button-base.php:2943
msgid "Bottom"
msgstr "أسفل"

#: includes/controls/groups/flex-container.php:145
#: includes/controls/groups/flex-item.php:67
#: includes/controls/groups/grid-container.php:147
#: includes/controls/groups/grid-container.php:175
#: includes/elements/section.php:405 includes/widgets/counter.php:386
#: includes/widgets/tabs.php:203 includes/widgets/tabs.php:233
#: includes/widgets/traits/button-trait.php:266
#: modules/nested-accordion/widgets/nested-accordion.php:169
#: modules/nested-tabs/widgets/nested-tabs.php:227
#: modules/nested-tabs/widgets/nested-tabs.php:269
msgid "Stretch"
msgstr "متمدد"

#: core/base/traits/shared-widget-controls-trait.php:158
#: core/settings/editor-preferences/model.php:140
#: core/settings/editor-preferences/model.php:152
#: core/settings/editor-preferences/model.php:163
#: core/settings/editor-preferences/model.php:185
#: includes/controls/switcher.php:74 includes/managers/icons.php:476
#: includes/widgets/audio.php:130 includes/widgets/image-carousel.php:182
#: includes/widgets/image-carousel.php:356
#: includes/widgets/image-carousel.php:403
#: includes/widgets/image-carousel.php:416
#: includes/widgets/image-carousel.php:433
#: includes/widgets/image-carousel.php:464
#: includes/widgets/image-gallery.php:193 includes/widgets/image.php:218
#: modules/floating-buttons/base/widget-contact-button-base.php:2988
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1545
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1603
#: modules/nested-accordion/widgets/nested-accordion.php:293
#: modules/styleguide/module.php:128 assets/js/app.js:9671
msgid "Yes"
msgstr "نعم"

#: core/base/traits/shared-widget-controls-trait.php:159
#: core/settings/editor-preferences/model.php:141
#: core/settings/editor-preferences/model.php:153
#: core/settings/editor-preferences/model.php:164
#: core/settings/editor-preferences/model.php:186
#: includes/controls/switcher.php:73 includes/managers/icons.php:475
#: includes/widgets/audio.php:131 includes/widgets/image-carousel.php:181
#: includes/widgets/image-carousel.php:357
#: includes/widgets/image-carousel.php:404
#: includes/widgets/image-carousel.php:417
#: includes/widgets/image-carousel.php:434
#: includes/widgets/image-carousel.php:465
#: includes/widgets/image-gallery.php:194 includes/widgets/image.php:219
#: modules/floating-buttons/base/widget-contact-button-base.php:2989
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1546
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1604
#: modules/nested-accordion/widgets/nested-accordion.php:294
#: modules/styleguide/module.php:127
msgid "No"
msgstr "لا"

#: includes/base/element-base.php:1317 includes/controls/dimensions.php:83
#: includes/elements/column.php:700 includes/elements/container.php:1405
#: includes/elements/section.php:1152 includes/widgets/common.php:353
#: includes/widgets/common.php:354 includes/widgets/divider.php:464
#: includes/widgets/divider.php:760 includes/widgets/divider.php:926
#: includes/widgets/heading.php:255 includes/widgets/icon-box.php:232
#: includes/widgets/icon-box.php:286 includes/widgets/icon-list.php:254
#: includes/widgets/icon-list.php:536 includes/widgets/icon.php:192
#: includes/widgets/image-box.php:207 includes/widgets/image-box.php:262
#: includes/widgets/image-carousel.php:508
#: includes/widgets/image-carousel.php:794
#: includes/widgets/image-gallery.php:321 includes/widgets/image.php:253
#: includes/widgets/image.php:571 includes/widgets/social-icons.php:314
#: includes/widgets/star-rating.php:209 includes/widgets/tabs.php:416
#: includes/widgets/testimonial.php:236 includes/widgets/text-editor.php:229
#: includes/widgets/traits/button-trait.php:262
#: modules/floating-buttons/base/widget-contact-button-base.php:1089
#: modules/floating-buttons/base/widget-contact-button-base.php:2347
#: modules/floating-buttons/base/widget-contact-button-base.php:2889
#: modules/shapes/widgets/text-path.php:168
msgid "Right"
msgstr "يمين"

#: includes/base/element-base.php:1309 includes/controls/dimensions.php:85
#: includes/elements/column.php:692 includes/elements/container.php:1404
#: includes/elements/section.php:1144 includes/widgets/common.php:353
#: includes/widgets/common.php:354 includes/widgets/divider.php:456
#: includes/widgets/divider.php:752 includes/widgets/divider.php:918
#: includes/widgets/heading.php:247 includes/widgets/icon-box.php:224
#: includes/widgets/icon-box.php:278 includes/widgets/icon-list.php:246
#: includes/widgets/icon-list.php:528 includes/widgets/icon.php:184
#: includes/widgets/image-box.php:199 includes/widgets/image-box.php:254
#: includes/widgets/image-carousel.php:507
#: includes/widgets/image-carousel.php:786
#: includes/widgets/image-gallery.php:313 includes/widgets/image.php:245
#: includes/widgets/image.php:563 includes/widgets/social-icons.php:306
#: includes/widgets/star-rating.php:201 includes/widgets/tabs.php:408
#: includes/widgets/testimonial.php:228 includes/widgets/text-editor.php:221
#: includes/widgets/traits/button-trait.php:254
#: modules/floating-buttons/base/widget-contact-button-base.php:1085
#: modules/floating-buttons/base/widget-contact-button-base.php:2343
#: modules/floating-buttons/base/widget-contact-button-base.php:2881
#: modules/shapes/widgets/text-path.php:160
msgid "Left"
msgstr "يسار"

#: core/kits/documents/tabs/global-colors.php:77
#: core/kits/documents/tabs/global-typography.php:128
msgid "Primary"
msgstr "أساسي"

#: core/kits/documents/tabs/global-colors.php:82
#: core/kits/documents/tabs/global-typography.php:135
msgid "Secondary"
msgstr "فرعي"

#: modules/system-info/module.php:160 modules/system-info/module.php:184
msgid "Download System Info"
msgstr "نزل معلومات النظام"

#: includes/controls/media.php:190 includes/widgets/image-box.php:99
#: includes/widgets/image.php:115 includes/widgets/testimonial.php:133
#: includes/widgets/video.php:634
#: modules/link-in-bio/base/widget-link-in-bio-base.php:243
#: modules/link-in-bio/base/widget-link-in-bio-base.php:333
#: modules/link-in-bio/base/widget-link-in-bio-base.php:921
#: modules/link-in-bio/base/widget-link-in-bio-base.php:976
msgid "Choose Image"
msgstr "اختر صورة"

#: includes/controls/gallery.php:95 includes/controls/gallery.php:97
#: includes/widgets/image-carousel.php:120
#: includes/widgets/image-gallery.php:119
msgid "Add Images"
msgstr "اضافة صور"

#: includes/widgets/image-gallery.php:209
msgid "Random"
msgstr "عشوائي"

#: core/base/traits/shared-widget-controls-trait.php:23
#: includes/elements/column.php:258 includes/elements/container.php:612
#: includes/elements/section.php:498 includes/widgets/divider.php:520
#: includes/widgets/heading.php:213
msgid "HTML Tag"
msgstr "وسم HTML"

#: includes/widgets/html.php:42
msgid "HTML"
msgstr "HTML"

#: includes/widgets/html.php:93 includes/widgets/html.php:100
msgid "HTML Code"
msgstr "كود HTML"

#: includes/widgets/sidebar.php:42 includes/widgets/sidebar.php:102
msgid "Sidebar"
msgstr "الشريط الجانبي"

#: includes/widgets/text-editor.php:46 includes/widgets/text-editor.php:109
#: includes/widgets/text-editor.php:209
msgid "Text Editor"
msgstr "محرر النصوص"

#: includes/fonts.php:71
msgid "System"
msgstr "نظام"

#: includes/controls/groups/background.php:427
msgctxt "Background Control"
msgid "Attachment"
msgstr "المرفق"

#: includes/controls/groups/background.php:432
msgctxt "Background Control"
msgid "Scroll"
msgstr "تمرير"

#: includes/controls/groups/background.php:433
msgctxt "Background Control"
msgid "Fixed"
msgstr "ثابت"

#: includes/elements/container.php:435 includes/elements/section.php:254
#: includes/widgets/common.php:225 includes/widgets/icon-list.php:198
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1543
msgid "Full Width"
msgstr "عرض كامل"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:1640
msgid "Image Size"
msgstr "حجم الصورة"

#: includes/widgets/progress.php:297
msgid "Title Style"
msgstr "نسق العنوان"

#: includes/widgets/audio.php:148 includes/widgets/image-carousel.php:401
#: includes/widgets/video.php:342
msgid "Autoplay"
msgstr "تشغيل تلقائي"

#: includes/controls/groups/border.php:68 includes/widgets/divider.php:327
#: includes/widgets/icon-list.php:285
msgid "Dashed"
msgstr "متقطع"

#: core/breakpoints/manager.php:334
msgid "Laptop"
msgstr "حاسوب محمول"

#: includes/widgets/divider.php:814 includes/widgets/icon-box.php:112
#: includes/widgets/icon.php:127 includes/widgets/text-editor.php:292
msgid "View"
msgstr "عرض"

#: includes/controls/groups/border.php:67 includes/widgets/divider.php:326
#: includes/widgets/icon-list.php:284
msgid "Dotted"
msgstr "منقط"

#: includes/controls/groups/typography.php:113
msgctxt "Typography Control"
msgid "Size"
msgstr "الحجم"

#: core/base/traits/shared-widget-controls-trait.php:207
#: includes/controls/groups/border.php:90 includes/widgets/accordion.php:285
#: includes/widgets/alert.php:211 includes/widgets/social-icons.php:540
#: includes/widgets/tabs.php:316 includes/widgets/toggle.php:287
#: includes/widgets/traits/button-trait.php:419
#: modules/nested-accordion/widgets/nested-accordion.php:499
#: modules/nested-tabs/widgets/nested-tabs.php:503
#: modules/nested-tabs/widgets/nested-tabs.php:562
#: modules/nested-tabs/widgets/nested-tabs.php:647
#: modules/nested-tabs/widgets/nested-tabs.php:1031
msgid "Border Color"
msgstr "لون الاطار"

#: includes/widgets/icon-box.php:218
#: includes/widgets/traits/button-trait.php:141
#: modules/floating-buttons/base/widget-contact-button-base.php:1081
#: modules/floating-buttons/base/widget-contact-button-base.php:2339
msgid "Icon Position"
msgstr "موضع الأيقونة"

#: includes/widgets/image-gallery.php:173
msgid "Attachment Page"
msgstr "صفحة المرفق"

#: core/kits/documents/tabs/theme-style-images.php:21
#: core/kits/documents/tabs/theme-style-images.php:51
#: includes/controls/groups/background.php:622
#: includes/widgets/image-gallery.php:220
msgid "Images"
msgstr "الصور"

#: includes/widgets/google-maps.php:44 includes/widgets/google-maps.php:107
#: includes/widgets/google-maps.php:194
msgid "Google Maps"
msgstr "خرائظ قوقل"

#: includes/widgets/google-maps.php:132
msgid "London Eye, London, United Kingdom"
msgstr "لندن، الممكلة المتحدة"

#: includes/widgets/image-box.php:194 includes/widgets/testimonial.php:198
msgid "Image Position"
msgstr "موضع الصورة"

#: includes/widgets/sidebar.php:89 includes/widgets/sidebar.php:109
msgid "Choose Sidebar"
msgstr "اختر شريط جانبي"

#: includes/widgets/sidebar.php:87
msgid "No sidebars were found"
msgstr "لا توجد أشرطة جانبية"

#: includes/widgets/heading.php:199 includes/widgets/traits/button-trait.php:36
#: modules/floating-buttons/base/widget-contact-button-base.php:1071
#: modules/floating-buttons/base/widget-contact-button-base.php:1488
#: modules/floating-buttons/base/widget-contact-button-base.php:1870
#: modules/floating-buttons/base/widget-contact-button-base.php:2220
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1398
msgid "Large"
msgstr "كبير"

#: includes/widgets/heading.php:198 includes/widgets/traits/button-trait.php:35
#: modules/floating-buttons/base/widget-contact-button-base.php:1070
#: modules/floating-buttons/base/widget-contact-button-base.php:1487
#: modules/floating-buttons/base/widget-contact-button-base.php:1869
#: modules/floating-buttons/base/widget-contact-button-base.php:2219
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1397
msgid "Medium"
msgstr "متوسط"

#: includes/controls/groups/flex-item.php:125 includes/widgets/alert.php:335
#: includes/widgets/common.php:936 includes/widgets/divider.php:621
#: includes/widgets/divider.php:829 includes/widgets/heading.php:193
#: includes/widgets/icon-box.php:462 includes/widgets/icon-list.php:474
#: includes/widgets/icon.php:301 includes/widgets/image-carousel.php:558
#: includes/widgets/image-carousel.php:622 includes/widgets/rating.php:52
#: includes/widgets/social-icons.php:381 includes/widgets/star-rating.php:309
#: includes/widgets/text-editor.php:345
#: includes/widgets/traits/button-trait.php:113 includes/widgets/video.php:818
#: modules/floating-buttons/base/widget-contact-button-base.php:1065
#: modules/floating-buttons/base/widget-contact-button-base.php:1482
#: modules/floating-buttons/base/widget-contact-button-base.php:1864
#: modules/floating-buttons/base/widget-contact-button-base.php:2214
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1392
#: modules/nested-accordion/widgets/nested-accordion.php:588
#: modules/nested-tabs/widgets/nested-tabs.php:910
#: modules/shapes/widgets/text-path.php:234
msgid "Size"
msgstr "حجم"

#: core/kits/documents/tabs/settings-background.php:79
#: core/kits/documents/tabs/settings-lightbox.php:99
#: core/kits/documents/tabs/settings-lightbox.php:116
#: includes/controls/animation.php:153 includes/controls/groups/border.php:64
#: includes/controls/groups/flex-item.php:130
#: includes/controls/groups/typography.php:191
#: includes/controls/hover-animation.php:125 includes/controls/icons.php:108
#: includes/controls/icons.php:194 includes/elements/container.php:1147
#: includes/elements/section.php:927 includes/widgets/divider.php:483
#: includes/widgets/image-carousel.php:197
#: includes/widgets/image-carousel.php:320
#: includes/widgets/image-carousel.php:372
#: includes/widgets/image-gallery.php:156
#: includes/widgets/image-gallery.php:174 includes/widgets/image.php:143
#: includes/widgets/image.php:178 includes/widgets/video.php:582
#: modules/nested-tabs/widgets/nested-tabs.php:387 assets/js/ai-admin.js:11023
#: assets/js/ai-admin.js:11029 assets/js/ai-admin.js:11041
#: assets/js/ai-admin.js:11052 assets/js/ai-admin.js:11063
#: assets/js/ai-admin.js:11079 assets/js/ai-gutenberg.js:12865
#: assets/js/ai-gutenberg.js:12871 assets/js/ai-gutenberg.js:12883
#: assets/js/ai-gutenberg.js:12894 assets/js/ai-gutenberg.js:12905
#: assets/js/ai-gutenberg.js:12921 assets/js/ai-media-library.js:12652
#: assets/js/ai-media-library.js:12658 assets/js/ai-media-library.js:12670
#: assets/js/ai-media-library.js:12681 assets/js/ai-media-library.js:12692
#: assets/js/ai-media-library.js:12708 assets/js/ai.js:13925
#: assets/js/ai.js:13931 assets/js/ai.js:13943 assets/js/ai.js:13954
#: assets/js/ai.js:13965 assets/js/ai.js:13981
msgid "None"
msgstr "بدون"

#: includes/widgets/heading.php:197 includes/widgets/traits/button-trait.php:34
#: modules/floating-buttons/base/widget-contact-button-base.php:1069
#: modules/floating-buttons/base/widget-contact-button-base.php:1486
#: modules/floating-buttons/base/widget-contact-button-base.php:1868
#: modules/floating-buttons/base/widget-contact-button-base.php:2218
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1396
msgid "Small"
msgstr "صغير"

#: includes/widgets/button.php:47 includes/widgets/button.php:110
#: includes/widgets/button.php:121
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1087
msgid "Button"
msgstr "زر"

#: includes/controls/groups/typography.php:158
msgctxt "Typography Control"
msgid "Transform"
msgstr "تحويل"

#: includes/controls/groups/border.php:66 includes/widgets/divider.php:325
#: includes/widgets/icon-list.php:283
msgid "Double"
msgstr "مزدوج"

#: includes/elements/container.php:1741 includes/elements/section.php:1343
msgid "Visibility"
msgstr "الظهور"

#: includes/widgets/tabs.php:124 includes/widgets/tabs.php:125
msgid "Tab Content"
msgstr "محتوى علامة التبويب"

#: includes/widgets/tabs.php:111 includes/widgets/tabs.php:112
#: modules/nested-tabs/widgets/nested-tabs.php:96
#: modules/nested-tabs/widgets/nested-tabs.php:97
msgid "Tab Title"
msgstr "عنوان علامة التبويب"

#: includes/widgets/tabs.php:155
#: modules/nested-tabs/widgets/nested-tabs.php:156
msgid "Tab #2"
msgstr "علامة التبويب #2"

#: includes/widgets/progress.php:183 includes/widgets/progress.php:256
msgid "Inner Text"
msgstr "النص الداخلى"

#: includes/widgets/progress.php:171
msgid "Display Percentage"
msgstr "عرض النسبة المئوية"

#: includes/widgets/progress.php:45 includes/widgets/progress.php:92
#: includes/widgets/progress.php:199
msgid "Progress Bar"
msgstr "شريط التقدم"

#: includes/widgets/image-box.php:280 includes/widgets/image-carousel.php:725
msgid "Image Spacing"
msgstr "تباعد الصورة"

#: includes/widgets/image-box.php:45 includes/widgets/image-box.php:92
msgid "Image Box"
msgstr "صندوق الصورة"

#: includes/widgets/icon-list.php:127 includes/widgets/icon-list.php:128
msgid "List Item"
msgstr "بند القائمة"

#: includes/widgets/icon-list.php:181
msgid "List Item #3"
msgstr "بند القائمة #3"

#: includes/widgets/icon-list.php:174
msgid "List Item #2"
msgstr "بند القائمة #2"

#: includes/widgets/icon-list.php:167
msgid "List Item #1"
msgstr "بند القائمة #1"

#: includes/widgets/alert.php:118 includes/widgets/heading.php:171
#: includes/widgets/icon-box.php:154 includes/widgets/image-box.php:130
#: includes/widgets/progress.php:104
msgid "Enter your title"
msgstr "أدخل العنوان الخاص بك"

#: includes/controls/groups/typography.php:171
msgctxt "Typography Control"
msgid "Style"
msgstr "نمط"

#: includes/controls/groups/typography.php:106
msgctxt "Typography Control"
msgid "Family"
msgstr "عائلة الخط"

#: includes/elements/column.php:784 includes/elements/container.php:1642
#: includes/elements/section.php:1241 includes/widgets/common.php:587
#: modules/floating-buttons/base/widget-contact-button-base.php:3048
msgid "CSS Classes"
msgstr "كلاس css"

#: includes/managers/elements.php:284
msgid "Basic"
msgstr "أساسي"

#: includes/widgets/accordion.php:116
msgid "Accordion Title"
msgstr "عنوان الأكورديون"

#: includes/widgets/accordion.php:147
msgid "Accordion Items"
msgstr "أكورديون #1"

#: includes/widgets/accordion.php:46 includes/widgets/accordion.php:105
#: includes/widgets/accordion.php:255
#: modules/nested-accordion/widgets/nested-accordion.php:36
#: modules/nested-accordion/widgets/nested-accordion.php:377
msgid "Accordion"
msgstr "أكورديون"

#: includes/widgets/alert.php:107 includes/widgets/progress.php:143
#: includes/widgets/traits/button-trait.php:74
msgid "Danger"
msgstr "خطر"

#: includes/widgets/alert.php:106 includes/widgets/progress.php:142
#: includes/widgets/traits/button-trait.php:73
msgid "Warning"
msgstr "تحذير"

#: includes/widgets/accordion.php:334 includes/widgets/accordion.php:433
#: includes/widgets/image-carousel.php:657 includes/widgets/tabs.php:362
#: includes/widgets/toggle.php:367 includes/widgets/toggle.php:457
msgid "Active Color"
msgstr "اللون النشط"

#: includes/controls/groups/background.php:291 includes/widgets/common.php:899
#: includes/widgets/image-box.php:333 includes/widgets/image-carousel.php:673
#: includes/widgets/image.php:45 includes/widgets/image.php:108
#: includes/widgets/image.php:233 includes/widgets/testimonial.php:298
msgid "Image"
msgstr "صورة"

#: includes/widgets/accordion.php:167 includes/widgets/accordion.php:390
#: includes/widgets/alert.php:155 includes/widgets/divider.php:491
#: includes/widgets/divider.php:543 includes/widgets/divider.php:803
#: includes/widgets/icon-box.php:99 includes/widgets/icon-box.php:360
#: includes/widgets/icon-list.php:138 includes/widgets/icon-list.php:398
#: includes/widgets/icon.php:44 includes/widgets/icon.php:107
#: includes/widgets/icon.php:114 includes/widgets/icon.php:172
#: includes/widgets/rating.php:44 includes/widgets/rating.php:169
#: includes/widgets/social-icons.php:98 includes/widgets/social-icons.php:331
#: includes/widgets/star-rating.php:150 includes/widgets/toggle.php:170
#: includes/widgets/toggle.php:414 includes/widgets/traits/button-trait.php:125
#: includes/widgets/video.php:679
#: modules/floating-buttons/base/widget-contact-button-base.php:462
#: modules/nested-accordion/widgets/nested-accordion.php:189
#: modules/nested-accordion/widgets/nested-accordion.php:580
#: modules/nested-tabs/widgets/nested-tabs.php:107
#: modules/nested-tabs/widgets/nested-tabs.php:862
msgid "Icon"
msgstr "ايقونة"

#: includes/widgets/accordion.php:401 includes/widgets/divider.php:452
#: includes/widgets/heading.php:243 includes/widgets/icon-box.php:274
#: includes/widgets/icon-list.php:242 includes/widgets/icon.php:180
#: includes/widgets/image-box.php:250 includes/widgets/image-carousel.php:782
#: includes/widgets/image-gallery.php:309 includes/widgets/image.php:241
#: includes/widgets/image.php:559 includes/widgets/rating.php:191
#: includes/widgets/social-icons.php:302 includes/widgets/star-rating.php:197
#: includes/widgets/tabs.php:187 includes/widgets/tabs.php:217
#: includes/widgets/tabs.php:404 includes/widgets/testimonial.php:223
#: includes/widgets/text-editor.php:217 includes/widgets/toggle.php:425
#: includes/widgets/traits/button-trait.php:282
#: modules/shapes/widgets/text-path.php:155
msgid "Alignment"
msgstr "تنظيم"

#: includes/widgets/image-carousel.php:160
msgid "Slides to Scroll"
msgstr "سلايدر عند عمل سكرول"

#: includes/widgets/divider.php:681 includes/widgets/icon-list.php:501
#: includes/widgets/star-rating.php:273
msgid "Gap"
msgstr "فراغ"

#: includes/widgets/divider.php:819 includes/widgets/icon-box.php:117
#: includes/widgets/icon.php:132 includes/widgets/text-editor.php:297
msgid "Framed"
msgstr "مؤطر"

#: includes/widgets/divider.php:818 includes/widgets/icon-box.php:116
#: includes/widgets/icon.php:131 includes/widgets/text-editor.php:296
msgid "Stacked"
msgstr "مرصوص"

#: includes/widgets/icon-box.php:304
#: includes/widgets/traits/button-trait.php:174
#: modules/floating-buttons/base/widget-contact-button-base.php:1103
#: modules/floating-buttons/base/widget-contact-button-base.php:2359
msgid "Icon Spacing"
msgstr "مسافة الأيقونة"

#: includes/widgets/icon-list.php:45 includes/widgets/icon-list.php:92
msgid "Icon List"
msgstr "قائمة الايقونة"

#: includes/widgets/social-icons.php:501
msgid "Icon Hover"
msgstr "تأثير مؤشر الماوس على الايقونة"

#: includes/base/element-base.php:878 includes/base/element-base.php:890
#: includes/widgets/divider.php:963 includes/widgets/icon-box.php:522
#: includes/widgets/icon.php:366 modules/shapes/widgets/text-path.php:266
msgid "Rotate"
msgstr "تدوير"

#: includes/widgets/icon-box.php:134 includes/widgets/icon.php:146
#: includes/widgets/social-icons.php:267
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1668
#: assets/js/ai-admin.js:11093 assets/js/ai-gutenberg.js:12935
#: assets/js/ai-media-library.js:12722 assets/js/ai.js:13995
msgid "Square"
msgstr "مربع"

#: includes/widgets/common.php:128 includes/widgets/icon-box.php:133
#: includes/widgets/icon.php:145 includes/widgets/social-icons.php:268
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1667
#: modules/shapes/module.php:22
msgid "Circle"
msgstr "دائرة"

#: includes/widgets/menu-anchor.php:97
msgid "The ID of Menu Anchor."
msgstr "رقم ID للمرساة"

#: includes/widgets/alert.php:133
msgid "I am a description. Click the edit button to change this text."
msgstr "هنا الوصف. اضغط على زر التعديل لتغير هذا النص"

#: includes/widgets/toggle.php:129
msgid "Toggle Content"
msgstr "تبديل المحتوى"

#: includes/widgets/toggle.php:150
msgid "Toggle Items"
msgstr "تبديل العناصر"

#: includes/widgets/toggle.php:46 includes/widgets/toggle.php:105
#: includes/widgets/toggle.php:258
msgid "Toggle"
msgstr "تبديل"

#: includes/widgets/tabs.php:151
#: modules/nested-tabs/widgets/nested-tabs.php:153
msgid "Tab #1"
msgstr "تبويب #1"

#: includes/widgets/tabs.php:146
#: modules/nested-tabs/widgets/nested-tabs.php:148
msgid "Tabs Items"
msgstr "عناصر التبويبات"

#: includes/widgets/tabs.php:46 includes/widgets/tabs.php:100
#: includes/widgets/tabs.php:249 modules/nested-tabs/widgets/nested-tabs.php:32
#: modules/nested-tabs/widgets/nested-tabs.php:88
#: modules/nested-tabs/widgets/nested-tabs.php:425
msgid "Tabs"
msgstr "تبويبات"

#: includes/widgets/image-carousel.php:447
msgid "Autoplay Speed"
msgstr "سرعة التشغيل التلقائي"

#: includes/widgets/image-carousel.php:195
#: includes/widgets/image-carousel.php:529
msgid "Arrows"
msgstr "الأسهم"

#: includes/widgets/image-carousel.php:479
msgid "Slide"
msgstr "شريحة"

#: includes/widgets/menu-anchor.php:43 includes/widgets/menu-anchor.php:90
msgid "Menu Anchor"
msgstr "تركيز القائمة"

#: includes/widgets/counter.php:473
#: modules/floating-buttons/base/widget-contact-button-base.php:312
#: modules/floating-buttons/base/widget-contact-button-base.php:836
#: modules/link-in-bio/base/widget-link-in-bio-base.php:473
#: modules/link-in-bio/base/widget-link-in-bio-base.php:720
msgid "Number"
msgstr "رقم"

#: includes/widgets/counter.php:155
msgid "Number Suffix"
msgstr "لاحقة الرقم"

#: includes/widgets/counter.php:140
msgid "Number Prefix"
msgstr "سابقة الرقم"

#: includes/widgets/accordion.php:129
msgid "Accordion Content"
msgstr "محتوى أكورديون"

#: includes/widgets/accordion.php:156
msgid "Accordion #2"
msgstr "أكورديون #2"

#: includes/widgets/accordion.php:152
msgid "Accordion #1"
msgstr "أكورديون #1"

#: core/role-manager/role-manager.php:74
msgid "Exclude Roles"
msgstr "استثناء الادوار"

#: includes/widgets/toggle.php:116
msgid "Toggle Title"
msgstr "تبديل العنوان"

#: includes/widgets/toggle.php:159
msgid "Toggle #2"
msgstr "تبديل #2"

#: includes/widgets/toggle.php:155
msgid "Toggle #1"
msgstr "تبديل #1"

#: includes/widgets/counter.php:128
msgid "Ending Number"
msgstr "رقم النهاية"

#: core/dynamic-tags/tag.php:105 includes/widgets/counter.php:265
#: modules/nested-tabs/widgets/nested-tabs.php:184
#: modules/nested-tabs/widgets/nested-tabs.php:880
msgid "After"
msgstr "بعد"

#: core/dynamic-tags/tag.php:95 includes/widgets/counter.php:261
#: modules/nested-tabs/widgets/nested-tabs.php:188
#: modules/nested-tabs/widgets/nested-tabs.php:888
msgid "Before"
msgstr "قبل"

#: includes/widgets/counter.php:116
msgid "Starting Number"
msgstr "رقم البداية"

#: includes/widgets/counter.php:217
msgid "Cool Number"
msgstr "الرقم"

#: includes/widgets/video.php:616 includes/widgets/video.php:623
#: includes/widgets/video.php:778
msgid "Image Overlay"
msgstr "غطاء الصورة"

#: includes/widgets/menu-anchor.php:103
msgid "This ID will be the CSS ID you will have to use in your own page, Without #."
msgstr "هذا المعرف سيكون معرف الـ CSS الذي ستحصل عليه في صفحتك بدون الـ #"

#: includes/widgets/video.php:390
msgid "Player Controls"
msgstr "أدوات التحكم بالمشغّل"

#: modules/history/views/revisions-panel-template.php:11
#: assets/js/editor.js:5118 assets/js/editor.js:44790
msgid "Discard"
msgstr "تجاهل"

#: includes/widgets/alert.php:104 includes/widgets/progress.php:140
#: includes/widgets/traits/button-trait.php:71 assets/js/app-packages.js:5685
#: assets/js/kit-library.ac1a4cd5deae5526ce49.bundle.js:3390
msgid "Info"
msgstr "معلومات"

#: core/common/modules/finder/categories/site.php:64
msgid "Themes"
msgstr "قوالب"

#: includes/editor-templates/panel.php:70
#: includes/editor-templates/panel.php:71
msgid "Menu"
msgstr "القائمة"

#: includes/widgets/image-carousel.php:321
#: includes/widgets/image-gallery.php:172 includes/widgets/image.php:179
msgid "Media File"
msgstr "ملف الوسائط"

#: includes/controls/groups/border.php:65 includes/widgets/divider.php:324
#: includes/widgets/icon-list.php:282 includes/widgets/star-rating.php:170
msgid "Solid"
msgstr "لون كامل"

#: core/settings/editor-preferences/model.php:194
#: includes/widgets/image-carousel.php:190
#: includes/widgets/image-carousel.php:518
msgid "Navigation"
msgstr "قائمة التنقّل"

#: includes/base/element-base.php:1387
#: includes/controls/groups/grid-container.php:33
#: includes/widgets/alert.php:145 includes/widgets/audio.php:159
#: includes/widgets/audio.php:170 includes/widgets/audio.php:181
#: includes/widgets/audio.php:192 includes/widgets/audio.php:206
#: includes/widgets/audio.php:217 includes/widgets/audio.php:228
#: includes/widgets/audio.php:239 includes/widgets/counter.php:184
#: includes/widgets/progress.php:173 includes/widgets/video.php:393
#: includes/widgets/video.php:408 includes/widgets/video.php:435
#: includes/widgets/video.php:513 includes/widgets/video.php:527
#: includes/widgets/video.php:541 includes/widgets/video.php:567
#: includes/widgets/video.php:626 includes/widgets/video.php:667
#: modules/floating-buttons/base/widget-contact-button-base.php:485
#: modules/floating-buttons/base/widget-contact-button-base.php:595
#: modules/floating-buttons/base/widget-contact-button-base.php:660
#: modules/floating-buttons/base/widget-contact-button-base.php:2468
#: assets/js/element-manager-admin.js:2091
msgid "Show"
msgstr "إظهار"

#: includes/widgets/video.php:740
msgid "Aspect Ratio"
msgstr "نسبة العرض إلى الارتفاع"

#: includes/widgets/alert.php:105 includes/widgets/progress.php:141
#: includes/widgets/traits/button-trait.php:72
msgid "Success"
msgstr "تمّ بنجاح"

#: core/kits/documents/tabs/global-colors.php:87
#: core/kits/documents/tabs/global-typography.php:142
#: includes/widgets/divider.php:487 includes/widgets/divider.php:505
#: includes/widgets/divider.php:704 includes/widgets/icon-list.php:124
#: includes/widgets/icon-list.php:608
#: includes/widgets/traits/button-trait.php:57
#: modules/floating-buttons/base/widget-contact-button-base.php:125
#: modules/floating-buttons/base/widget-contact-button-base.php:993
#: modules/link-in-bio/base/widget-link-in-bio-base.php:318
#: modules/link-in-bio/base/widget-link-in-bio-base.php:574
#: modules/shapes/widgets/text-path.php:94
#: modules/shapes/widgets/text-path.php:287
msgid "Text"
msgstr "النص"

#: core/document-types/page-base.php:144
#: core/kits/documents/tabs/theme-style-buttons.php:243
#: core/kits/documents/tabs/theme-style-form-fields.php:159
#: includes/elements/column.php:742 includes/elements/container.php:1344
#: includes/elements/section.php:1199 includes/widgets/accordion.php:376
#: includes/widgets/accordion.php:523 includes/widgets/common.php:205
#: includes/widgets/divider.php:852 includes/widgets/icon-box.php:480
#: includes/widgets/icon.php:338 includes/widgets/social-icons.php:400
#: includes/widgets/toggle.php:400 includes/widgets/toggle.php:547
#: includes/widgets/traits/button-trait.php:494
#: modules/floating-buttons/base/widget-contact-button-base.php:1402
#: modules/floating-buttons/base/widget-contact-button-base.php:2127
#: modules/floating-buttons/base/widget-contact-button-base.php:2142
#: modules/floating-buttons/base/widget-contact-button-base.php:2690
#: modules/floating-buttons/base/widget-contact-button-base.php:2830
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1168
#: modules/nested-accordion/widgets/nested-accordion.php:458
#: modules/nested-accordion/widgets/nested-accordion.php:523
#: modules/nested-tabs/widgets/nested-tabs.php:685
#: modules/nested-tabs/widgets/nested-tabs.php:1066
msgid "Padding"
msgstr "الهوامش الداخلية"

#: includes/widgets/divider.php:587 includes/widgets/icon-list.php:301
#: modules/floating-buttons/base/widget-contact-button-base.php:2493
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1207
msgid "Weight"
msgstr "الوزن"

#: core/kits/documents/tabs/settings-lightbox.php:101
#: core/kits/documents/tabs/settings-lightbox.php:118
#: includes/widgets/image-carousel.php:368
#: includes/widgets/image-carousel.php:374
#: includes/widgets/image-carousel.php:771
#: includes/widgets/image-gallery.php:152
#: includes/widgets/image-gallery.php:298 includes/widgets/image.php:140
#: includes/widgets/image.php:547
msgid "Caption"
msgstr "كلمات توضيحية"

#: includes/base/element-base.php:1386
#: includes/controls/groups/grid-container.php:34
#: includes/widgets/alert.php:146 includes/widgets/audio.php:158
#: includes/widgets/audio.php:169 includes/widgets/audio.php:180
#: includes/widgets/audio.php:191 includes/widgets/audio.php:205
#: includes/widgets/audio.php:216 includes/widgets/audio.php:227
#: includes/widgets/audio.php:238 includes/widgets/counter.php:185
#: includes/widgets/progress.php:174 includes/widgets/video.php:392
#: includes/widgets/video.php:407 includes/widgets/video.php:434
#: includes/widgets/video.php:512 includes/widgets/video.php:526
#: includes/widgets/video.php:540 includes/widgets/video.php:566
#: includes/widgets/video.php:625 includes/widgets/video.php:666
#: modules/floating-buttons/base/widget-contact-button-base.php:486
#: modules/floating-buttons/base/widget-contact-button-base.php:596
#: modules/floating-buttons/base/widget-contact-button-base.php:661
#: modules/floating-buttons/base/widget-contact-button-base.php:2469
msgid "Hide"
msgstr "إخفاء"

#: core/base/traits/shared-widget-controls-trait.php:156
#: includes/elements/column.php:504 includes/elements/container.php:972
#: includes/elements/section.php:794 includes/widgets/common.php:751
msgid "Border"
msgstr "إطار"

#: core/kits/documents/tabs/theme-style-buttons.php:152
#: core/kits/documents/tabs/theme-style-buttons.php:227
#: core/kits/documents/tabs/theme-style-form-fields.php:233
#: core/kits/documents/tabs/theme-style-images.php:83
#: core/kits/documents/tabs/theme-style-images.php:154
#: includes/elements/column.php:529 includes/elements/column.php:566
#: includes/elements/container.php:1017 includes/elements/container.php:1068
#: includes/elements/section.php:818 includes/elements/section.php:854
#: includes/widgets/common.php:776 includes/widgets/common.php:813
#: includes/widgets/divider.php:1007 includes/widgets/icon-box.php:559
#: includes/widgets/icon.php:402 includes/widgets/image-box.php:381
#: includes/widgets/image-carousel.php:757
#: includes/widgets/image-gallery.php:284 includes/widgets/image.php:522
#: includes/widgets/progress.php:244 includes/widgets/social-icons.php:487
#: includes/widgets/testimonial.php:336 includes/widgets/text-editor.php:401
#: includes/widgets/traits/button-trait.php:472
#: modules/nested-accordion/widgets/nested-accordion.php:445
#: modules/nested-accordion/widgets/nested-accordion.php:511
#: modules/nested-tabs/widgets/nested-tabs.php:672
#: modules/nested-tabs/widgets/nested-tabs.php:1043
msgid "Border Radius"
msgstr "زوايا الإطار"

#: core/kits/documents/tabs/theme-style-typography.php:19
#: core/kits/documents/tabs/theme-style-typography.php:38
#: includes/controls/groups/typography.php:353 includes/elements/column.php:631
#: includes/elements/section.php:1084 assets/js/editor-modules.js:1438
#: assets/js/editor.js:42574
msgid "Typography"
msgstr "الخطوط"

#: core/base/document.php:1937
#: core/common/modules/finder/categories/settings.php:49
msgid "General Settings"
msgstr "الإعدادات العامة"

#: core/settings/editor-preferences/model.php:126
#: assets/js/kit-library.ac1a4cd5deae5526ce49.bundle.js:4298
msgid "Tablet"
msgstr "الأجهزة اللوحية"

#: includes/widgets/spacer.php:42 includes/widgets/spacer.php:105
msgid "Spacer"
msgstr "فراغ فاصل"

#: core/document-types/page-base.php:132 includes/elements/column.php:729
#: includes/elements/container.php:1332 includes/elements/section.php:1180
#: includes/widgets/common.php:193
msgid "Margin"
msgstr "الهامش الخارجي"

#: includes/elements/column.php:704 includes/elements/section.php:1156
#: includes/widgets/heading.php:259 includes/widgets/icon-box.php:290
#: includes/widgets/image-box.php:266 includes/widgets/image-carousel.php:798
#: includes/widgets/image-gallery.php:325 includes/widgets/image.php:575
#: includes/widgets/star-rating.php:213 includes/widgets/text-editor.php:233
msgid "Justified"
msgstr "مضبوط"

#: core/experiments/manager.php:492 core/experiments/manager.php:781
#: modules/element-cache/module.php:110 assets/js/editor.js:27942
#: assets/js/element-manager-admin.js:2239
msgid "Inactive"
msgstr "غير مُفعّل"

#: core/experiments/manager.php:491 core/experiments/manager.php:780
#: modules/element-cache/module.php:111
#: modules/floating-buttons/base/widget-contact-button-base.php:1253
#: modules/nested-accordion/widgets/nested-accordion.php:652
#: modules/nested-accordion/widgets/nested-accordion.php:719
#: modules/nested-tabs/widgets/nested-tabs.php:612
#: modules/nested-tabs/widgets/nested-tabs.php:814
#: modules/nested-tabs/widgets/nested-tabs.php:986 assets/js/editor.js:27944
#: assets/js/element-manager-admin.js:2236
msgid "Active"
msgstr "مُفعَّل"

#: includes/widgets/divider.php:46 includes/widgets/divider.php:360
#: includes/widgets/divider.php:510 includes/widgets/divider.php:560
#: includes/widgets/icon-list.php:265
msgid "Divider"
msgstr "مُقسّم"

#: includes/widgets/icon-box.php:45 includes/widgets/icon-box.php:92
msgid "Icon Box"
msgstr "صندوق الأيقونة"

#: core/base/traits/shared-widget-controls-trait.php:179
#: includes/controls/groups/border.php:77 includes/widgets/accordion.php:263
#: includes/widgets/divider.php:984 includes/widgets/icon-box.php:544
#: includes/widgets/icon.php:387 includes/widgets/tabs.php:293
#: includes/widgets/text-editor.php:423 includes/widgets/toggle.php:266
#: modules/nested-accordion/widgets/nested-accordion.php:502
#: modules/nested-tabs/widgets/nested-tabs.php:506
#: modules/nested-tabs/widgets/nested-tabs.php:565
#: modules/nested-tabs/widgets/nested-tabs.php:650
#: modules/nested-tabs/widgets/nested-tabs.php:1034
msgid "Border Width"
msgstr "عرض الإطار"

#: core/document-types/post.php:65
msgid "Posts"
msgstr "المقالات"

#: core/kits/documents/tabs/settings-lightbox.php:103
#: core/kits/documents/tabs/settings-lightbox.php:113
#: core/kits/documents/tabs/settings-lightbox.php:120
#: includes/compatibility.php:154 includes/widgets/alert.php:284
#: includes/widgets/icon-box.php:162 includes/widgets/icon-box.php:636
#: includes/widgets/image-box.php:138 includes/widgets/image-box.php:558
#: includes/widgets/image-carousel.php:375
#: modules/floating-buttons/base/widget-contact-button-base.php:2283
#: modules/link-in-bio/base/widget-link-in-bio-base.php:884
#: modules/link-in-bio/base/widget-link-in-bio-base.php:889
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1340
msgid "Description"
msgstr "الوصف"

#: core/base/document.php:1945
#: core/kits/documents/tabs/settings-lightbox.php:96
#: core/kits/documents/tabs/settings-lightbox.php:100
#: core/kits/documents/tabs/settings-lightbox.php:117
#: includes/elements/column.php:126 includes/elements/section.php:240
#: includes/widgets/accordion.php:114 includes/widgets/accordion.php:300
#: includes/widgets/alert.php:116 includes/widgets/alert.php:244
#: includes/widgets/common.php:184 includes/widgets/counter.php:210
#: includes/widgets/counter.php:524 includes/widgets/heading.php:163
#: includes/widgets/icon-box.php:148 includes/widgets/icon-box.php:585
#: includes/widgets/image-box.php:124 includes/widgets/image-box.php:507
#: includes/widgets/image-carousel.php:373 includes/widgets/progress.php:99
#: includes/widgets/star-rating.php:185 includes/widgets/star-rating.php:229
#: includes/widgets/tabs.php:109 includes/widgets/tabs.php:339
#: includes/widgets/testimonial.php:170 includes/widgets/testimonial.php:396
#: includes/widgets/toggle.php:114 includes/widgets/toggle.php:332
#: modules/floating-buttons/base/widget-contact-button-base.php:109
#: modules/floating-buttons/base/widget-contact-button-base.php:189
#: modules/floating-buttons/base/widget-contact-button-base.php:2255
#: modules/link-in-bio/base/widget-link-in-bio-base.php:858
#: modules/nested-accordion/widgets/nested-accordion.php:99
#: modules/nested-accordion/widgets/nested-accordion.php:548
#: modules/nested-tabs/widgets/nested-tabs.php:94
msgid "Title"
msgstr "العنوان"

#: includes/elements/section.php:291
msgid "Wide"
msgstr "واسع"

#: includes/widgets/heading.php:200
msgid "XL"
msgstr "كبير جداً"

#: includes/widgets/heading.php:201
msgid "XXL"
msgstr "ضخم"

#: includes/elements/container.php:434 includes/elements/section.php:253
msgid "Boxed"
msgstr "داخل صندوق"

#: includes/controls/groups/typography.php:138
msgctxt "Typography Control"
msgid "Weight"
msgstr "سُمك الخط"

#: core/kits/documents/tabs/global-colors.php:92
#: core/kits/documents/tabs/global-typography.php:149
msgid "Accent"
msgstr "التمييز"

#: includes/widgets/image-carousel.php:414
msgid "Pause on Hover"
msgstr "إيقاف عند التمرير"

#: includes/widgets/heading.php:46 includes/widgets/heading.php:156
#: includes/widgets/heading.php:235
#: modules/link-in-bio/base/widget-link-in-bio-base.php:838
#: modules/link-in-bio/base/widget-link-in-bio-base.php:843
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1284
msgid "Heading"
msgstr "عنوان"

#: includes/widgets/common.php:885 includes/widgets/icon-box.php:130
#: includes/widgets/icon.php:142 includes/widgets/social-icons.php:262
msgid "Shape"
msgstr "الشكل"

#: includes/elements/section.php:337 includes/elements/section.php:377
msgid "Minimum Height"
msgstr "أدنى ارتفاع"