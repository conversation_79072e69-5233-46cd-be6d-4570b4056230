{"translation-revision-date": "2025-07-03 08:54:11+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=6; plural=(n == 0) ? 0 : ((n == 1) ? 1 : ((n == 2) ? 2 : ((n % 100 >= 3 && n % 100 <= 10) ? 3 : ((n % 100 >= 11 && n % 100 <= 99) ? 4 : 5))));", "lang": "ar"}, "Attribution": ["تخصيص"], "Analytics date settings": ["إعدادات تاريخ التحليلات"], "We now collect orders in this table based on when the payment went through, rather than when they were placed. You can change this in <link>settings</link>.": ["نجمع الآن الطلبات في هذا الجدول حسب موعد تنفيذ الدفع، وليس وقت تقديمها.  يمكنك تغيير هذا ضمن <link>الإعدادات</link>."], "Orders are now reported based on the payment dates ✅": ["يتم الآن الإبلاغ بالطلبات حسب تواريخ الدفع ✅"], "Previous year:": ["العام السابق:"], "Item sold": ["العنصر المبيع", "العناصر المباعة", "العناصر المباعة", "العناصر المباعة", "العناصر المباعة", "العناصر المباعة"], "Previous period:": ["الفترة السابقة:"], "Got it": ["فهمت"], " Customer": [" زبون", " العملاء", " العملاء", " العملاء", " العملاء", " العملاء"], "net sales": ["صافي المبيعات"], "Customer type": ["نوع العميل"], "Product(s)": ["المنتج (المنتجات)"], "%1$s× %2$s": ["%1$s× %2$s"], "Order #": ["رق<PERSON> الطلب"], "Net sales": ["صافي المبيعات"], "Items sold": ["العناصر المباعة"], "Order Number": ["رق<PERSON> الطلب"], "Coupon(s)": ["القسائم"], "Coupon": ["قسيمة"], "Customer": ["الزبون"], "Products": ["المنتجات"], "Orders": ["الطلبات"], "Order": ["الطلب"], "Status": ["الحالة"], "Date": ["التاريخ"], "Product": ["المنتج", "المنتج", "المنتجات", "المنتجات", "المنتجات", "المنتجات"], "Coupons": ["القسائم"]}}, "comment": {"reference": "assets/client/admin/chunks/analytics-report-orders.js"}}