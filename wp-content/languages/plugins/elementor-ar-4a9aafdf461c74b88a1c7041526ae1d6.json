{"translation-revision-date": "2023-12-04 13:18:54+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=6; plural=(n == 0) ? 0 : ((n == 1) ? 1 : ((n == 2) ? 2 : ((n % 100 >= 3 && n % 100 <= 10) ? 3 : ((n % 100 >= 11 && n % 100 <= 99) ? 4 : 5))));", "lang": "ar"}, "The file exceeds the maximum upload size for this site.": ["الملف يتج<PERSON><PERSON><PERSON> الحد الأقصى لحجم رفع الملفات لهذا الموقع."], "Got it": ["لقد فهمتُ ذلك"], "Enable Unfiltered File Uploads": ["تمكين تحميل الملفات التي لم يتم تصفيتها. "], "Unable to connect": ["<PERSON>ير قادر على الاتصال"], "Before you enable unfiltered files upload, note that such files include a security risk. Elementor does run a process to remove possible malicious code, but there is still risk involved when using such files.": ["قبل تمكين تحميل الملفات التي لم تتم تصفيتها ، لاحظ أن هذا النوع من الملفات يتضمن مخاطر أمنية. يقوم اليمنتور بتشغيل عملية لإزالة تعليمات برمجية ضارة محتملة ، ولكن لا يزال هناك خطر ينطوي على استخدام مثل هذه الملفات."], "Finder": ["Finder"], "Enable": ["تفعيل"], "Cancel": ["الغاء"]}}, "comment": {"reference": "assets/js/common.js"}}